﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E85 RID: 3717
	public class CE1179 : BasePetEffect
	{
		// Token: 0x060080B5 RID: 32949 RVA: 0x002AA068 File Offset: 0x002A8268
		public CE1179(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1179, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060080B6 RID: 32950 RVA: 0x002AA0E8 File Offset: 0x002A82E8
		public override bool Start(Living living)
		{
			CE1179 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1179) as CE1179;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080B7 RID: 32951 RVA: 0x002AA148 File Offset: 0x002A8348
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				player.Attack += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060080B8 RID: 32952 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060080B9 RID: 32953 RVA: 0x002AA1AC File Offset: 0x002A83AC
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060080BA RID: 32954 RVA: 0x002AA1E0 File Offset: 0x002A83E0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Attack -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004FE0 RID: 20448
		private int m_type = 0;

		// Token: 0x04004FE1 RID: 20449
		private int m_count = 0;

		// Token: 0x04004FE2 RID: 20450
		private int m_probability = 0;

		// Token: 0x04004FE3 RID: 20451
		private int m_delay = 0;

		// Token: 0x04004FE4 RID: 20452
		private int m_coldDown = 0;

		// Token: 0x04004FE5 RID: 20453
		private int m_currentId;

		// Token: 0x04004FE6 RID: 20454
		private int m_added = 0;
	}
}
