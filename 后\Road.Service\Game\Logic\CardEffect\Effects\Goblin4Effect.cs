﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F28 RID: 3880
	public class Goblin4Effect : BaseCardEffect
	{
		// Token: 0x170014A8 RID: 5288
		// (get) Token: 0x06008427 RID: 33831 RVA: 0x00034BF0 File Offset: 0x00032DF0
		public int ReduceValue
		{
			get
			{
				return this.m_added;
			}
		}

		// Token: 0x06008428 RID: 33832 RVA: 0x002B6E0C File Offset: 0x002B500C
		public Goblin4Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.Goblin4, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008429 RID: 33833 RVA: 0x002B6E7C File Offset: 0x002B507C
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.Goblin4) is Goblin4Effect;
			return flag || base.Start(living);
		}

		// Token: 0x0600842A RID: 33834 RVA: 0x00034BF8 File Offset: 0x00032DF8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x0600842B RID: 33835 RVA: 0x00034C0E File Offset: 0x00032E0E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x0600842C RID: 33836 RVA: 0x002B6EB4 File Offset: 0x002B50B4
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 5;
			if (flag2)
			{
				this.m_added = this.m_value;
				player.Game.SendMessage(player.PlayerDetail, "您激活了龙巢之战4件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活龙巢之战4件套卡.", 3);
			}
		}

		// Token: 0x0400526D RID: 21101
		private int m_indexValue = 0;

		// Token: 0x0400526E RID: 21102
		private int m_value = 0;

		// Token: 0x0400526F RID: 21103
		private int m_added = 0;
	}
}
