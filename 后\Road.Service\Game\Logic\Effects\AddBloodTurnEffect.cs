﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000ED1 RID: 3793
	public class AddBloodTurnEffect : BasePlayerEffect
	{
		// Token: 0x06008296 RID: 33430 RVA: 0x000336E1 File Offset: 0x000318E1
		public AddBloodTurnEffect(int count, int probability)
			: base(eEffectType.AddBloodTurnEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x06008297 RID: 33431 RVA: 0x002B1250 File Offset: 0x002AF450
		public override bool Start(Living living)
		{
			AddBloodTurnEffect addBloodTurnEffect = living.EffectList.GetOfType(eEffectType.AddBloodTurnEffect) as AddBloodTurnEffect;
			bool flag = addBloodTurnEffect != null;
			bool flag2;
			if (flag)
			{
				addBloodTurnEffect.m_probability = this.m_probability;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008298 RID: 33432 RVA: 0x00033709 File Offset: 0x00031909
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.method_0;
		}

		// Token: 0x06008299 RID: 33433 RVA: 0x0003371F File Offset: 0x0003191F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.method_0;
		}

		// Token: 0x0600829A RID: 33434 RVA: 0x002B1298 File Offset: 0x002AF498
		private void method_0(Living living_0)
		{
			this.m_probability--;
			bool flag = this.m_probability < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				living_0.Blood += this.m_count;
			}
		}

		// Token: 0x040051C7 RID: 20935
		private int m_count = 0;

		// Token: 0x040051C8 RID: 20936
		private int m_probability = 0;
	}
}
