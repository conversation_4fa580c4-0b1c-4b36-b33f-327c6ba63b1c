﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E90 RID: 3728
	public class CE1196 : BasePetEffect
	{
		// Token: 0x060080FA RID: 33018 RVA: 0x002AB1FC File Offset: 0x002A93FC
		public CE1196(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1196, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060080FB RID: 33019 RVA: 0x002AB27C File Offset: 0x002A947C
		public override bool Start(Living living)
		{
			CE1196 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1196) as CE1196;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080FC RID: 33020 RVA: 0x002AB2DC File Offset: 0x002A94DC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.BaseDamage += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060080FD RID: 33021 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060080FE RID: 33022 RVA: 0x002AB33C File Offset: 0x002A953C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060080FF RID: 33023 RVA: 0x00032774 File Offset: 0x00030974
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BaseDamage -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x0400502D RID: 20525
		private int m_type = 0;

		// Token: 0x0400502E RID: 20526
		private int m_count = 0;

		// Token: 0x0400502F RID: 20527
		private int m_probability = 0;

		// Token: 0x04005030 RID: 20528
		private int m_delay = 0;

		// Token: 0x04005031 RID: 20529
		private int m_coldDown = 0;

		// Token: 0x04005032 RID: 20530
		private int m_currentId;

		// Token: 0x04005033 RID: 20531
		private int m_added = 0;
	}
}
