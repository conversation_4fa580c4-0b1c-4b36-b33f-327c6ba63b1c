﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FC6 RID: 4038
	public class AmuletMgr
	{
		// Token: 0x06008A68 RID: 35432 RVA: 0x002F6C08 File Offset: 0x002F4E08
		public static bool Init()
		{
			return AmuletMgr.ReLoad();
		}

		// Token: 0x06008A69 RID: 35433 RVA: 0x002F6C20 File Offset: 0x002F4E20
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, AmuletGradeItemInfo> dictionary = AmuletMgr.LoadGradeFromDatabase();
				Dictionary<int, AmuletItemInfo> dictionary2 = AmuletMgr.LoadItemFromDatabase();
				Dictionary<int, AmuletPhaseItemInfo> dictionary3 = AmuletMgr.LoadPhaseFromDatabase();
				bool flag = dictionary.Values.Count > 0 && dictionary2.Values.Count > 0 && dictionary3.Values.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, AmuletGradeItemInfo>>(ref AmuletMgr.m_amuletGradeItems, dictionary);
					Interlocked.Exchange<Dictionary<int, AmuletItemInfo>>(ref AmuletMgr.m_amuletItems, dictionary2);
					Interlocked.Exchange<Dictionary<int, AmuletPhaseItemInfo>>(ref AmuletMgr.m_amuletPhaseItems, dictionary3);
					return true;
				}
			}
			catch (Exception ex)
			{
				AmuletMgr.log.Error("AmuletGradeItemMgr init error:", ex);
			}
			return false;
		}

		// Token: 0x06008A6A RID: 35434 RVA: 0x002F6CD0 File Offset: 0x002F4ED0
		private static Dictionary<int, AmuletItemInfo> LoadItemFromDatabase()
		{
			Dictionary<int, AmuletItemInfo> dictionary = new Dictionary<int, AmuletItemInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				AmuletItemInfo[] allAmuletItem = produceBussiness.GetAllAmuletItem();
				AmuletItemInfo[] array = allAmuletItem;
				AmuletItemInfo[] array2 = array;
				AmuletItemInfo[] array3 = array2;
				foreach (AmuletItemInfo amuletItemInfo in array3)
				{
					bool flag = amuletItemInfo.Level > AmuletMgr.maxLevel;
					if (flag)
					{
						AmuletMgr.maxLevel = amuletItemInfo.Level;
					}
					bool flag2 = !dictionary.ContainsKey(amuletItemInfo.Level);
					if (flag2)
					{
						dictionary.Add(amuletItemInfo.Level, amuletItemInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008A6B RID: 35435 RVA: 0x002F6D8C File Offset: 0x002F4F8C
		private static Dictionary<int, AmuletGradeItemInfo> LoadGradeFromDatabase()
		{
			Dictionary<int, AmuletGradeItemInfo> dictionary = new Dictionary<int, AmuletGradeItemInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				AmuletGradeItemInfo[] allAmuletGradeItem = produceBussiness.GetAllAmuletGradeItem();
				AmuletGradeItemInfo[] array = allAmuletGradeItem;
				AmuletGradeItemInfo[] array2 = array;
				AmuletGradeItemInfo[] array3 = array2;
				foreach (AmuletGradeItemInfo amuletGradeItemInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(amuletGradeItemInfo.WahsLevel);
					if (flag)
					{
						dictionary.Add(amuletGradeItemInfo.WahsLevel, amuletGradeItemInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008A6C RID: 35436 RVA: 0x002F6E28 File Offset: 0x002F5028
		private static Dictionary<int, AmuletPhaseItemInfo> LoadPhaseFromDatabase()
		{
			Dictionary<int, AmuletPhaseItemInfo> dictionary = new Dictionary<int, AmuletPhaseItemInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				AmuletPhaseItemInfo[] allAmuletPhaseItem = produceBussiness.GetAllAmuletPhaseItem();
				AmuletPhaseItemInfo[] array = allAmuletPhaseItem;
				AmuletPhaseItemInfo[] array2 = array;
				AmuletPhaseItemInfo[] array3 = array2;
				foreach (AmuletPhaseItemInfo amuletPhaseItemInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(amuletPhaseItemInfo.Phase);
					if (flag)
					{
						dictionary.Add(amuletPhaseItemInfo.Phase, amuletPhaseItemInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008A6D RID: 35437 RVA: 0x002F6EC4 File Offset: 0x002F50C4
		public static List<AmuletGradeItemInfo> GetAllAmuletGradeItem()
		{
			return AmuletMgr.m_amuletGradeItems.Values.ToList<AmuletGradeItemInfo>();
		}

		// Token: 0x06008A6E RID: 35438 RVA: 0x002F6EE8 File Offset: 0x002F50E8
		public static List<AmuletItemInfo> GetAllAmuletItem()
		{
			return AmuletMgr.m_amuletItems.Values.ToList<AmuletItemInfo>();
		}

		// Token: 0x06008A6F RID: 35439 RVA: 0x002F6F0C File Offset: 0x002F510C
		public static List<AmuletPhaseItemInfo> GetAllAmuletPhaseItem()
		{
			return AmuletMgr.m_amuletPhaseItems.Values.ToList<AmuletPhaseItemInfo>();
		}

		// Token: 0x06008A70 RID: 35440 RVA: 0x002F6F30 File Offset: 0x002F5130
		public static AmuletGradeItemInfo FindAmuletGradeItem(int id)
		{
			bool flag = AmuletMgr.m_amuletGradeItems.ContainsKey(id);
			AmuletGradeItemInfo amuletGradeItemInfo;
			if (flag)
			{
				amuletGradeItemInfo = AmuletMgr.m_amuletGradeItems[id];
			}
			else
			{
				amuletGradeItemInfo = null;
			}
			return amuletGradeItemInfo;
		}

		// Token: 0x06008A71 RID: 35441 RVA: 0x002F6F64 File Offset: 0x002F5164
		public static AmuletGradeItemInfo FindAmuletGradeByGp(int exp)
		{
			AmuletGradeItemInfo amuletGradeItemInfo = AmuletMgr.FindAmuletGradeItem(10);
			bool flag = amuletGradeItemInfo != null && exp >= amuletGradeItemInfo.WahsTimes;
			AmuletGradeItemInfo amuletGradeItemInfo2;
			if (flag)
			{
				amuletGradeItemInfo2 = amuletGradeItemInfo;
			}
			else
			{
				for (int i = 1; i <= AmuletMgr.m_amuletGradeItems.Count; i++)
				{
					bool flag2 = AmuletMgr.m_amuletGradeItems.ContainsKey(i) && exp < AmuletMgr.m_amuletGradeItems[i].WahsTimes;
					if (flag2)
					{
						return (i == 1) ? null : AmuletMgr.m_amuletGradeItems[i - 1];
					}
				}
				amuletGradeItemInfo2 = null;
			}
			return amuletGradeItemInfo2;
		}

		// Token: 0x06008A72 RID: 35442 RVA: 0x002F6FFC File Offset: 0x002F51FC
		public static AmuletItemInfo FindAmuletItem(int id)
		{
			bool flag = AmuletMgr.m_amuletItems.ContainsKey(id);
			AmuletItemInfo amuletItemInfo;
			if (flag)
			{
				amuletItemInfo = AmuletMgr.m_amuletItems[id];
			}
			else
			{
				amuletItemInfo = null;
			}
			return amuletItemInfo;
		}

		// Token: 0x06008A73 RID: 35443 RVA: 0x002F7030 File Offset: 0x002F5230
		public static AmuletPhaseItemInfo FindAmuletPhaseItem(int id)
		{
			bool flag = AmuletMgr.m_amuletPhaseItems.ContainsKey(id);
			AmuletPhaseItemInfo amuletPhaseItemInfo;
			if (flag)
			{
				amuletPhaseItemInfo = AmuletMgr.m_amuletPhaseItems[id];
			}
			else
			{
				amuletPhaseItemInfo = null;
			}
			return amuletPhaseItemInfo;
		}

		// Token: 0x040054D9 RID: 21721
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040054DA RID: 21722
		private static Dictionary<int, AmuletItemInfo> m_amuletItems = new Dictionary<int, AmuletItemInfo>();

		// Token: 0x040054DB RID: 21723
		private static Dictionary<int, AmuletGradeItemInfo> m_amuletGradeItems = new Dictionary<int, AmuletGradeItemInfo>();

		// Token: 0x040054DC RID: 21724
		private static Dictionary<int, AmuletPhaseItemInfo> m_amuletPhaseItems = new Dictionary<int, AmuletPhaseItemInfo>();

		// Token: 0x040054DD RID: 21725
		private static ThreadSafeRandom random = new ThreadSafeRandom();

		// Token: 0x040054DE RID: 21726
		public static int maxLevel = 0;
	}
}
