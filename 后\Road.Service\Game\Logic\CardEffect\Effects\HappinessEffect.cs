﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F2F RID: 3887
	public class HappinessEffect : BaseCardEffect
	{
		// Token: 0x0600844F RID: 33871 RVA: 0x002B7940 File Offset: 0x002B5B40
		public HappinessEffect(int index, CardBuffInfo info)
			: base(eCardEffectType.HappinessDeck, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008450 RID: 33872 RVA: 0x002B79B0 File Offset: 0x002B5BB0
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.HappinessDeck) is HappinessEffect;
			return flag || base.Start(living);
		}

		// Token: 0x06008451 RID: 33873 RVA: 0x00034DA6 File Offset: 0x00032FA6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x06008452 RID: 33874 RVA: 0x00034DBC File Offset: 0x00032FBC
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x06008453 RID: 33875 RVA: 0x002B79E8 File Offset: 0x002B5BE8
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.AddMaxBlood(-this.m_value);
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVPGame && (player.Game as PVPGame).IsMatchOrFreedom();
			if (flag2)
			{
				player.AddMaxBlood(this.m_value);
				this.m_added = this.m_value;
			}
		}

		// Token: 0x04005281 RID: 21121
		private int m_indexValue = 0;

		// Token: 0x04005282 RID: 21122
		private int m_value = 0;

		// Token: 0x04005283 RID: 21123
		private int m_added = 0;
	}
}
