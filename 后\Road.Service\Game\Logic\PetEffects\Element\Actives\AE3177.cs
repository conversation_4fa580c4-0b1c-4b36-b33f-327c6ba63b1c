﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E39 RID: 3641
	public class AE3177 : BasePetEffect
	{
		// Token: 0x06007EE6 RID: 32486 RVA: 0x002A2D4C File Offset: 0x002A0F4C
		public AE3177(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE3177, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007EE7 RID: 32487 RVA: 0x002A2DC8 File Offset: 0x002A0FC8
		public override bool Start(Living living)
		{
			AE3177 ae = living.PetEffectList.GetOfType(ePetEffectType.AE3177) as AE3177;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007EE8 RID: 32488 RVA: 0x00031458 File Offset: 0x0002F658
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EE9 RID: 32489 RVA: 0x0003146E File Offset: 0x0002F66E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EEA RID: 32490 RVA: 0x002A2E24 File Offset: 0x002A1024
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					bool flag2 = player2.PetEffects.SkipCritical == 0;
					if (flag2)
					{
						player2.Game.SendPetBuff(player2, base.Info, true);
					}
					player2.AddPetEffect(new CE3177(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004DCA RID: 19914
		private int m_type = 0;

		// Token: 0x04004DCB RID: 19915
		private int m_count = 0;

		// Token: 0x04004DCC RID: 19916
		private int m_probability = 0;

		// Token: 0x04004DCD RID: 19917
		private int m_delay = 0;

		// Token: 0x04004DCE RID: 19918
		private int m_coldDown = 0;

		// Token: 0x04004DCF RID: 19919
		private int m_currentId;

		// Token: 0x04004DD0 RID: 19920
		private int m_added = 0;
	}
}
