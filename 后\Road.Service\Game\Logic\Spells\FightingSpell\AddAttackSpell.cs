﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CBF RID: 3263
	[SpellAttibute(14)]
	public class AddAttackSpell : ISpellHandler
	{
		// Token: 0x06007500 RID: 29952 RVA: 0x0026E2B0 File Offset: 0x0026C4B0
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				bool flag = (player.CurrentBall.ID == 3 || player.CurrentBall.ID == 5 || player.CurrentBall.ID == 1) && (item.TemplateID == 10001 || item.TemplateID == 10002);
				if (flag)
				{
					player.ShootCount = 1;
				}
				else
				{
					player.ShootCount += item.Property2;
					bool flag2 = item.Property2 == 2;
					if (flag2)
					{
						player.CurrentShootMinus *= 0.6f;
					}
					else
					{
						player.CurrentShootMinus *= 0.9f;
					}
				}
			}
			else
			{
				bool flag3 = game.CurrentLiving == null || !(game.CurrentLiving is Player) || game.CurrentLiving.Team != player.Team;
				if (!flag3)
				{
					bool flag4 = (player.CurrentBall.ID == 3 || player.CurrentBall.ID == 5 || player.CurrentBall.ID == 1) && (item.TemplateID == 10001 || item.TemplateID == 10002);
					if (flag4)
					{
						(game.CurrentLiving as Player).ShootCount = 1;
					}
					else
					{
						(game.CurrentLiving as Player).ShootCount += item.Property2;
						bool flag5 = item.Property2 == 2;
						if (flag5)
						{
							game.CurrentLiving.CurrentShootMinus *= 0.6f;
						}
						else
						{
							game.CurrentLiving.CurrentShootMinus *= 0.9f;
						}
					}
				}
			}
		}
	}
}
