﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D47 RID: 3399
	public class PetReduceTargetMaxBlood : BasePetEffect
	{
		// Token: 0x060079DA RID: 31194 RVA: 0x0002E02E File Offset: 0x0002C22E
		public PetReduceTargetMaxBlood(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetReduceTargetMaxBlood, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x060079DB RID: 31195 RVA: 0x0028CBB8 File Offset: 0x0028ADB8
		public override bool Start(Living living)
		{
			PetReduceTargetMaxBlood petReduceTargetMaxBlood = living.PetEffectList.GetOfType(ePetEffectType.PetReduceTargetMaxBlood) as PetReduceTargetMaxBlood;
			bool flag = petReduceTargetMaxBlood != null;
			bool flag2;
			if (flag)
			{
				petReduceTargetMaxBlood.m_probability = ((this.m_probability > petReduceTargetMaxBlood.m_probability) ? this.m_probability : petReduceTargetMaxBlood.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079DC RID: 31196 RVA: 0x0002E05F File Offset: 0x0002C25F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
			player.AfterKillingLiving += this.player_AfterKillingLiving;
		}

		// Token: 0x060079DD RID: 31197 RVA: 0x0002E088 File Offset: 0x0002C288
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
			player.AfterKillingLiving -= this.player_AfterKillingLiving;
		}

		// Token: 0x060079DE RID: 31198 RVA: 0x0028CC18 File Offset: 0x0028AE18
		private void player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = this.IsTrigger && living != target && living.BlackTiger;
			if (flag)
			{
				this.IsTrigger = false;
				target.AddPetEffect(new PetReduceMaxBloodEquip(this.m_count, base.Info.ID.ToString()), 0);
			}
			else
			{
				bool flag2 = this.IsTrigger && living != target && living.WhiteTiger;
				if (flag2)
				{
					this.IsTrigger = false;
					target.AddPetEffect(new PetAddAgilityEquip(3, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x060079DF RID: 31199 RVA: 0x0028CCB4 File Offset: 0x0028AEB4
		private void player_PlayerBuffSkillPet(Player player)
		{
			this.IsTrigger = false;
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
				player.PetEffectTrigger = true;
			}
		}

		// Token: 0x04004765 RID: 18277
		private int m_count;

		// Token: 0x04004766 RID: 18278
		private int m_probability;

		// Token: 0x04004767 RID: 18279
		private int m_currentId;
	}
}
