﻿using System;
using System.Data;
using System.Data.SqlClient;
using Bussiness.CenterService;
using SqlDataProvider.Data;

namespace Bussiness
{
	// Token: 0x02000FB1 RID: 4017
	public class ManageBussiness : BaseBussiness
	{
		// Token: 0x060088A9 RID: 34985 RVA: 0x002D2A04 File Offset: 0x002D0C04
		public int KitoffUserByUserName(string name, int areaid, string msg)
		{
			int num = 1;
			using (PlayerBussiness playerBussiness = new PlayerBussiness())
			{
				PlayerInfo userSingleByUserName = playerBussiness.GetUserSingleByUserName(name);
				bool flag = userSingleByUserName == null;
				if (flag)
				{
					return 2;
				}
				num = this.KitoffUser(userSingleByUserName.ID, areaid, msg);
			}
			return num;
		}

		// Token: 0x060088AA RID: 34986 RVA: 0x002D2A68 File Offset: 0x002D0C68
		public int KitoffUserByNickName(string name, int areaid, string msg)
		{
			int num = 1;
			using (PlayerBussiness playerBussiness = new PlayerBussiness())
			{
				PlayerInfo userSingleByNickName = playerBussiness.GetUserSingleByNickName(name);
				bool flag = userSingleByNickName == null;
				if (flag)
				{
					return 2;
				}
				num = this.KitoffUser(userSingleByNickName.ID, areaid, msg);
			}
			return num;
		}

		// Token: 0x060088AB RID: 34987 RVA: 0x002D2ACC File Offset: 0x002D0CCC
		public int KitoffUser(int id, int areaid, string msg)
		{
			int num;
			try
			{
				using (CenterServiceClient centerServiceClient = new CenterServiceClient())
				{
					bool flag = centerServiceClient.KitoffUser(id, areaid, msg);
					if (flag)
					{
						num = 0;
					}
					else
					{
						num = 3;
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("KitoffUser", ex);
				}
				num = 1;
			}
			return num;
		}

		// Token: 0x060088AC RID: 34988 RVA: 0x002D2B48 File Offset: 0x002D0D48
		public bool SystemNotice(string msg)
		{
			bool flag = false;
			try
			{
				bool flag2 = !string.IsNullOrEmpty(msg);
				if (flag2)
				{
					using (CenterServiceClient centerServiceClient = new CenterServiceClient())
					{
						bool flag3 = centerServiceClient.SystemNotice(msg);
						if (flag3)
						{
							flag = true;
						}
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SystemNotice", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088AD RID: 34989 RVA: 0x002D2BDC File Offset: 0x002D0DDC
		private bool ForbidPlayer(string userName, string nickName, int userID, int areaid, DateTime forbidDate, bool isExist)
		{
			return this.ForbidPlayer(userName, nickName, userID, areaid, forbidDate, isExist, "");
		}

		// Token: 0x060088AE RID: 34990 RVA: 0x002D2C04 File Offset: 0x002D0E04
		private bool ForbidPlayer(string userName, string nickName, int userID, int areaid, DateTime forbidDate, bool isExist, string ForbidReason)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[6];
				array[0] = new SqlParameter("@UserName", userName);
				array[1] = new SqlParameter("@NickName", nickName);
				array[2] = new SqlParameter("@UserID", userID);
				SqlParameter[] array2 = array;
				array2[2].Direction = ParameterDirection.InputOutput;
				array2[3] = new SqlParameter("@ForbidDate", forbidDate);
				array2[4] = new SqlParameter("@IsExist", isExist);
				array2[5] = new SqlParameter("@ForbidReason", ForbidReason);
				this.db.RunProcedure("SP_Admin_ForbidUser", array2);
				userID = (int)array2[2].Value;
				bool flag2 = userID > 0;
				if (flag2)
				{
					flag = true;
					bool flag3 = !isExist;
					if (flag3)
					{
						this.KitoffUser(userID, areaid, "You are kicking out by GM!!");
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088AF RID: 34991 RVA: 0x002D2D18 File Offset: 0x002D0F18
		public bool ForbidPlayerByUserName(string userName, int areaid, DateTime date, bool isExist)
		{
			return this.ForbidPlayer(userName, "", 0, areaid, date, isExist);
		}

		// Token: 0x060088B0 RID: 34992 RVA: 0x002D2D3C File Offset: 0x002D0F3C
		public bool ForbidPlayerByNickName(string nickName, int areaid, DateTime date, bool isExist)
		{
			return this.ForbidPlayer("", nickName, 0, areaid, date, isExist);
		}

		// Token: 0x060088B1 RID: 34993 RVA: 0x002D2D60 File Offset: 0x002D0F60
		public bool ForbidPlayerByUserID(int userID, int areaid, DateTime date, bool isExist)
		{
			return this.ForbidPlayer("", "", userID, areaid, date, isExist);
		}

		// Token: 0x060088B2 RID: 34994 RVA: 0x002D2D88 File Offset: 0x002D0F88
		public bool ForbidPlayerByUserName(string userName, int areaid, DateTime date, bool isExist, string ForbidReason)
		{
			return this.ForbidPlayer(userName, "", 0, areaid, date, isExist, ForbidReason);
		}

		// Token: 0x060088B3 RID: 34995 RVA: 0x002D2DB0 File Offset: 0x002D0FB0
		public bool ForbidPlayerByNickName(string nickName, int areaid, DateTime date, bool isExist, string ForbidReason)
		{
			return this.ForbidPlayer("", nickName, 0, areaid, date, isExist, ForbidReason);
		}

		// Token: 0x060088B4 RID: 34996 RVA: 0x002D2DD8 File Offset: 0x002D0FD8
		public bool ForbidPlayerByUserID(int userID, int areaid, DateTime date, bool isExist, string ForbidReason)
		{
			return this.ForbidPlayer("", "", userID, areaid, date, isExist, ForbidReason);
		}

		// Token: 0x060088B5 RID: 34997 RVA: 0x002D2E04 File Offset: 0x002D1004
		public bool ReLoadServerList()
		{
			bool flag = false;
			try
			{
				using (CenterServiceClient centerServiceClient = new CenterServiceClient())
				{
					bool flag2 = centerServiceClient.ReLoadServerList();
					if (flag2)
					{
						flag = true;
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("ReLoadServerList", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088B6 RID: 34998 RVA: 0x002D2E84 File Offset: 0x002D1084
		public int GetConfigState(int type)
		{
			int num = 2;
			try
			{
				using (CenterServiceClient centerServiceClient = new CenterServiceClient())
				{
					return centerServiceClient.GetConfigState(type);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetConfigState", ex);
				}
			}
			return num;
		}

		// Token: 0x060088B7 RID: 34999 RVA: 0x002D2EFC File Offset: 0x002D10FC
		public bool UpdateConfigState(int type, bool state)
		{
			bool flag = false;
			try
			{
				using (CenterServiceClient centerServiceClient = new CenterServiceClient())
				{
					return centerServiceClient.UpdateConfigState(type, state);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateConfigState", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088B8 RID: 35000 RVA: 0x002D2F74 File Offset: 0x002D1174
		public bool Reload(string type)
		{
			bool flag = false;
			try
			{
				using (CenterServiceClient centerServiceClient = new CenterServiceClient())
				{
					return centerServiceClient.Reload(type);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Reload", ex);
				}
			}
			return flag;
		}
	}
}
