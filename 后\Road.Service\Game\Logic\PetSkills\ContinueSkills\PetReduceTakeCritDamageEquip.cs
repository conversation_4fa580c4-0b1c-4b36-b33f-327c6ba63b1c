﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D17 RID: 3351
	public class PetReduceTakeCritDamageEquip : AbstractPetEffect
	{
		// Token: 0x060078D4 RID: 30932 RVA: 0x0028808C File Offset: 0x0028628C
		public PetReduceTakeCritDamageEquip(int count, string elementID)
			: base(ePetEffectType.PetReduceTakeCritDamageEquip, elementID)
		{
			this.m_count = count;
			if (!(elementID == "3180"))
			{
				if (!(elementID == "3181"))
				{
					if (!(elementID == "3224"))
					{
						if (elementID == "3491")
						{
							this.m_value = 60;
							this.m_percent = true;
						}
					}
					else
					{
						this.m_value = 50;
						this.m_percent = true;
					}
				}
				else
				{
					this.m_value = 30;
					this.m_percent = true;
				}
			}
			else
			{
				this.m_value = 15;
				this.m_percent = true;
			}
		}

		// Token: 0x060078D5 RID: 30933 RVA: 0x0028812C File Offset: 0x0028632C
		public override bool Start(Living living)
		{
			PetReduceTakeCritDamageEquip petReduceTakeCritDamageEquip = living.PetEffectList.GetOfType(ePetEffectType.PetReduceTakeCritDamageEquip) as PetReduceTakeCritDamageEquip;
			bool flag = petReduceTakeCritDamageEquip != null;
			bool flag2;
			if (flag)
			{
				petReduceTakeCritDamageEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078D6 RID: 30934 RVA: 0x0002CF0E File Offset: 0x0002B10E
		public override void OnAttached(Living player)
		{
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
			player.BeginSelfTurn += this.player_SelfTurn;
		}

		// Token: 0x060078D7 RID: 30935 RVA: 0x0002CF37 File Offset: 0x0002B137
		public override void OnRemoved(Living player)
		{
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
			player.BeginSelfTurn += this.player_SelfTurn;
		}

		// Token: 0x060078D8 RID: 30936 RVA: 0x00288174 File Offset: 0x00286374
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool percent = this.m_percent;
			if (percent)
			{
				criticalAmount -= criticalAmount * this.m_value / 100;
			}
			else
			{
				criticalAmount -= this.m_value;
			}
			living.Game.sendShowPicSkil(living, base.Info, true);
		}

		// Token: 0x060078D9 RID: 30937 RVA: 0x002881C8 File Offset: 0x002863C8
		private void player_SelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.Info, false);
				this.Stop();
			}
		}

		// Token: 0x040046D3 RID: 18131
		private int m_count;

		// Token: 0x040046D4 RID: 18132
		private int m_value;

		// Token: 0x040046D5 RID: 18133
		private bool m_percent;
	}
}
