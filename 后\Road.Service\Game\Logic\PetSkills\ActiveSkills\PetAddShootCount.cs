﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D2A RID: 3370
	public class PetAddShootCount : BasePetEffect
	{
		// Token: 0x0600793A RID: 31034 RVA: 0x0002D619 File Offset: 0x0002B819
		public PetAddShootCount(int skillId, string elementID)
			: base(ePetEffectType.PetAddShootCount, elementID)
		{
			this.m_skillId = skillId;
		}

		// Token: 0x0600793B RID: 31035 RVA: 0x0028980C File Offset: 0x00287A0C
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetAddShootCount) is PetAddShootCount;
			return flag || base.Start(living);
		}

		// Token: 0x0600793C RID: 31036 RVA: 0x0002D630 File Offset: 0x0002B830
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
		}

		// Token: 0x0600793D RID: 31037 RVA: 0x0002D646 File Offset: 0x0002B846
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
		}

		// Token: 0x0600793E RID: 31038 RVA: 0x00289848 File Offset: 0x00287A48
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_skillId;
			if (flag)
			{
				player.Game.sendShowPicSkil(player, base.Info, true);
				player.PlayerShoot -= this.player_PlayerShoot;
				player.PlayerShoot += this.player_PlayerShoot;
			}
		}

		// Token: 0x0600793F RID: 31039 RVA: 0x002898AC File Offset: 0x00287AAC
		private void player_PlayerShoot(Player player)
		{
			player.PlayerShoot -= this.player_PlayerShoot;
			int num = player.ShootCount + 1;
			player.ShootCount = num;
		}

		// Token: 0x04004708 RID: 18184
		private int m_skillId;
	}
}
