﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using Bussiness.Managers;
using Game.Base;
using Game.Base.Packets;
using Game.Logic;
using Game.Logic.Phy.Object;
using Game.Server.Buffer;
using Game.Server.GameObjects;
using Game.Server.Managers;
using Game.Server.RingStation;
using Game.Server.Rooms;
using log4net;
using SqlDataProvider.Data;

namespace Game.Server.Battle
{
	// Token: 0x02000C44 RID: 3140
	public class FightServerConnector : BaseConnector
	{
		// Token: 0x06006FDA RID: 28634 RVA: 0x00024B2F File Offset: 0x00022D2F
		protected override void OnDisconnect()
		{
			base.OnDisconnect();
		}

		// Token: 0x06006FDB RID: 28635 RVA: 0x00029FB1 File Offset: 0x000281B1
		public override void OnRecvPacket(GSPacketIn pkg)
		{
			ThreadPool.QueueUserWorkItem(new WaitCallback(this.AsynProcessPacket), pkg);
		}

		// Token: 0x06006FDC RID: 28636 RVA: 0x0024C0A8 File Offset: 0x0024A2A8
		protected void AsynProcessPacket(object state)
		{
			try
			{
				GSPacketIn gspacketIn = state as GSPacketIn;
				short code = gspacketIn.Code;
				short num = code;
				if (num <= 75)
				{
					if (num != 0)
					{
						switch (num)
						{
						case 32:
							this.HandleSendToPlayer(gspacketIn);
							break;
						case 33:
							this.HandleUpdatePlayerGameId(gspacketIn);
							break;
						case 34:
							this.HandleDisconnectPlayer(gspacketIn);
							break;
						case 35:
							this.HandlePlayerOnGameOver(gspacketIn);
							break;
						case 36:
							this.HandlePlayerOnUsingItem(gspacketIn);
							break;
						case 38:
							this.HandlePlayerAddGold(gspacketIn);
							break;
						case 39:
							this.HandlePlayerAddGP(gspacketIn);
							break;
						case 40:
							this.HandlePlayerOnKillingLiving(gspacketIn);
							break;
						case 41:
							this.HandlePlayerOnMissionOver(gspacketIn);
							break;
						case 42:
							this.HandlePlayerConsortiaFight(gspacketIn);
							break;
						case 43:
							this.HandlePlayerSendConsortiaFight(gspacketIn);
							break;
						case 44:
							this.HandlePlayerRemoveGold(gspacketIn);
							break;
						case 45:
							this.HandlePlayerRemoveMoney(gspacketIn);
							break;
						case 48:
							this.HandlePlayerAddTemplate1(gspacketIn);
							break;
						case 49:
							this.HandlePlayerRemoveGP(gspacketIn);
							break;
						case 50:
							this.HandlePlayerRemoveOffer(gspacketIn);
							break;
						case 51:
							this.HandlePlayerAddOffer(gspacketIn);
							break;
						case 52:
							this.HandPlayerAddRobRiches(gspacketIn);
							break;
						case 65:
							this.HandleRoomRemove(gspacketIn);
							break;
						case 66:
							this.HandleStartGame(gspacketIn);
							break;
						case 67:
							this.HandleSendToRoom(gspacketIn);
							break;
						case 68:
							this.HandleStopGame(gspacketIn);
							break;
						case 74:
							this.HandlePlayerAddMoney(gspacketIn);
							break;
						case 75:
							this.HandlePlayerAddGiftToken(gspacketIn);
							break;
						}
					}
					else
					{
						this.HandleRSAKey(gspacketIn);
					}
				}
				else if (num != 82)
				{
					if (num != 88)
					{
						switch (num)
						{
						case 204:
							this.HandleAddEliteScore(gspacketIn);
							break;
						case 205:
							this.HandleRemoveEliteScore(gspacketIn);
							break;
						case 206:
							this.HandleEliteScoreWinUpdate(gspacketIn);
							break;
						}
					}
					else
					{
						this.HandleFightNPC(gspacketIn);
					}
				}
				else
				{
					this.HandlePlayerAddleageScore(gspacketIn);
				}
			}
			catch (Exception ex)
			{
				GameServer.log.Error("AsynProcessPacket", ex);
			}
		}

		// Token: 0x06006FDD RID: 28637 RVA: 0x0024C35C File Offset: 0x0024A55C
		private void HandlePlayerOnKillingLiving(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			AbstractGame game = playerById.CurrentRoom.Game;
			if (playerById != null)
			{
				playerById.OnKillingLiving(game, pkg.ReadInt(), pkg.ClientID, pkg.ReadBoolean(), pkg.ReadInt());
			}
		}

		// Token: 0x06006FDE RID: 28638 RVA: 0x0024C3A8 File Offset: 0x0024A5A8
		private void HandlePlayerOnMissionOver(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			AbstractGame game = playerById.CurrentRoom.Game;
			if (playerById != null)
			{
				playerById.OnMissionOver(game, pkg.ReadBoolean(), pkg.ReadInt(), pkg.ReadInt());
			}
		}

		// Token: 0x06006FDF RID: 28639 RVA: 0x0024C3F0 File Offset: 0x0024A5F0
		private void HandlePlayerConsortiaFight(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			Dictionary<int, Player> dictionary = new Dictionary<int, Player>();
			int num = pkg.ReadInt();
			int num2 = pkg.ReadInt();
			int num3 = pkg.ReadInt();
			int num4 = 0;
			for (int i = 0; i < num3; i++)
			{
				GamePlayer playerById2 = WorldMgr.GetPlayerById(pkg.ReadInt());
				bool flag = playerById2 != null;
				if (flag)
				{
					Player player = new Player(playerById2, 0, null, 0, playerById2.PlayerCharacter.Blood);
					dictionary.Add(i, player);
				}
			}
			eRoomType eRoomType = (eRoomType)pkg.ReadByte();
			eGameType eGameType = (eGameType)pkg.ReadByte();
			int num5 = pkg.ReadInt();
			bool flag2 = playerById != null;
			if (flag2)
			{
				num4 = playerById.ConsortiaFight(num, num2, dictionary, eRoomType, eGameType, num5, num3);
			}
			bool flag3 = num4 == 0;
			if (flag3)
			{
			}
		}

		// Token: 0x06006FE0 RID: 28640 RVA: 0x00029FC7 File Offset: 0x000281C7
		private void HandlePlayerSendConsortiaFight(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.SendConsortiaFight(pkg.ReadInt(), pkg.ReadInt(), pkg.ReadString());
			}
		}

		// Token: 0x06006FE1 RID: 28641 RVA: 0x00029FF3 File Offset: 0x000281F3
		private void HandlePlayerRemoveGold(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.RemoveGold(pkg.ReadInt());
			}
		}

		// Token: 0x06006FE2 RID: 28642 RVA: 0x0002A013 File Offset: 0x00028213
		private void HandlePlayerRemoveMoney(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.RemoveMoney(pkg.ReadInt());
			}
		}

		// Token: 0x06006FE3 RID: 28643 RVA: 0x0002A033 File Offset: 0x00028233
		private void HandlePlayerRemoveOffer(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.RemoveOffer(pkg.ReadInt());
			}
		}

		// Token: 0x06006FE4 RID: 28644 RVA: 0x0024C4C4 File Offset: 0x0024A6C4
		private void HandlePlayerAddOffer(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			bool flag = playerById != null;
			if (flag)
			{
				playerById.RemoveUserOffer(pkg.ReadInt());
				playerById.OnFightAddOffer(pkg.Parameter1);
			}
		}

		// Token: 0x06006FE5 RID: 28645 RVA: 0x001D3FBC File Offset: 0x001D21BC
		private void HandPlayerAddRobRiches(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			int num = pkg.ReadInt();
			bool flag = playerById != null && num == pkg.Parameter1;
			if (flag)
			{
				playerById.AddRobRiches(pkg.Parameter1);
			}
		}

		// Token: 0x06006FE6 RID: 28646 RVA: 0x0002A053 File Offset: 0x00028253
		private void HandlePlayerAddMoney(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.AddMoney(pkg.Parameter1);
			}
		}

		// Token: 0x06006FE7 RID: 28647 RVA: 0x0002A073 File Offset: 0x00028273
		private void HandlePlayerAddGiftToken(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.AddGiftToken(pkg.Parameter1);
			}
		}

		// Token: 0x06006FE8 RID: 28648 RVA: 0x0002A093 File Offset: 0x00028293
		private void HandlePlayerAddleageScore(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.AddLeageScore(pkg.ReadBoolean(), pkg.Parameter1);
			}
		}

		// Token: 0x06006FE9 RID: 28649 RVA: 0x0024C504 File Offset: 0x0024A704
		private void HandlePlayerAddTemplate1(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			bool flag = playerById == null;
			if (!flag)
			{
				ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(pkg.ReadInt());
				eBageType eBageType = (eBageType)pkg.ReadByte();
				bool flag2 = itemTemplateInfo == null;
				if (!flag2)
				{
					int num = pkg.ReadInt();
					ItemInfo itemInfo = ItemInfo.CreateFromTemplate(itemTemplateInfo, num, 118);
					itemInfo.Count = num;
					itemInfo.ValidDate = pkg.ReadInt();
					itemInfo.IsBinds = pkg.ReadBoolean();
					itemInfo.IsUsed = pkg.ReadBoolean();
					itemInfo.StrengthenLevel = pkg.ReadInt();
					itemInfo.AttackCompose = pkg.ReadInt();
					itemInfo.DefendCompose = pkg.ReadInt();
					itemInfo.AgilityCompose = pkg.ReadInt();
					itemInfo.LuckCompose = pkg.ReadInt();
					bool flag3 = pkg.ReadBoolean();
					if (flag3)
					{
						GoldEquipTemplateInfo goldEquipTemplateInfo = GoldEquipMgr.FindGoldEquipByTemplate(itemTemplateInfo.TemplateID);
						bool flag4 = goldEquipTemplateInfo != null;
						if (flag4)
						{
							ItemTemplateInfo itemTemplateInfo2 = ItemMgr.FindItemTemplate(goldEquipTemplateInfo.NewTemplateId);
							bool flag5 = itemTemplateInfo2 != null;
							if (flag5)
							{
								itemInfo.GoldEquip = itemTemplateInfo2;
								itemInfo.goldBeginTime = pkg.ReadDateTime();
								itemInfo.goldValidDate = pkg.ReadInt();
							}
						}
					}
					playerById.AddTemplate(itemInfo, eBageType, itemInfo.Count, itemInfo.IsTips);
				}
			}
		}

		// Token: 0x06006FEA RID: 28650 RVA: 0x0002A0B9 File Offset: 0x000282B9
		private void HandlePlayerAddGP(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.AddGpDirect(pkg.Parameter1);
			}
		}

		// Token: 0x06006FEB RID: 28651 RVA: 0x0002A0D9 File Offset: 0x000282D9
		private void HandlePlayerRemoveGP(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.RemoveGP(pkg.Parameter1);
			}
		}

		// Token: 0x06006FEC RID: 28652 RVA: 0x0002A0F9 File Offset: 0x000282F9
		private void HandlePlayerAddGold(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.AddGold(pkg.Parameter1);
			}
		}

		// Token: 0x06006FED RID: 28653 RVA: 0x0024C660 File Offset: 0x0024A860
		private void HandlePlayerOnUsingItem(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			bool flag = playerById != null;
			if (flag)
			{
				int num = pkg.ReadInt();
				bool flag2 = playerById.UsePropItem(null, pkg.Parameter1, pkg.Parameter2, num, pkg.ReadBoolean());
				this.SendUsingPropInGame(playerById.CurrentRoom.Game.Id, playerById.GamePlayerId, num, flag2);
			}
		}

		// Token: 0x06006FEE RID: 28654 RVA: 0x0024C6C8 File Offset: 0x0024A8C8
		private void SendUsingPropInGame(int gameId, int playerId, int templateId, bool result)
		{
			GSPacketIn gspacketIn = new GSPacketIn(36, gameId);
			gspacketIn.Parameter1 = playerId;
			gspacketIn.Parameter2 = templateId;
			gspacketIn.WriteBoolean(result);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06006FEF RID: 28655 RVA: 0x0024C700 File Offset: 0x0024A900
		public void SendPlayerDisconnet(int gameId, int playerId, int roomid)
		{
			this.SendTCP(new GSPacketIn(83, gameId)
			{
				Parameter1 = playerId
			});
		}

		// Token: 0x06006FF0 RID: 28656 RVA: 0x0024C728 File Offset: 0x0024A928
		private void HandlePlayerOnGameOver(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			bool flag = playerById != null && playerById.CurrentRoom != null && playerById.CurrentRoom.Game != null;
			if (flag)
			{
				playerById.OnGameOver(playerById.CurrentRoom.Game, pkg.ReadBoolean(), pkg.ReadInt(), pkg.ReadInt(), pkg.ReadInt(), pkg.ReadInt(), pkg.ReadBoolean(), pkg.ReadBoolean(), pkg.ReadBoolean());
			}
		}

		// Token: 0x06006FF1 RID: 28657 RVA: 0x0002A119 File Offset: 0x00028319
		private void HandleDisconnectPlayer(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.Disconnect();
			}
		}

		// Token: 0x06006FF2 RID: 28658 RVA: 0x001C67D0 File Offset: 0x001C49D0
		public void SendRSALogin(RSACryptoServiceProvider rsa, string key)
		{
			GSPacketIn gspacketIn = new GSPacketIn(1);
			gspacketIn.Write(rsa.Encrypt(Encoding.UTF8.GetBytes(key), false));
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06006FF3 RID: 28659 RVA: 0x0024C7A8 File Offset: 0x0024A9A8
		protected void HandleRSAKey(GSPacketIn packet)
		{
			RSAParameters rsaparameters = default(RSAParameters);
			rsaparameters.Modulus = packet.ReadBytes(128);
			rsaparameters.Exponent = packet.ReadBytes();
			RSACryptoServiceProvider rsacryptoServiceProvider = new RSACryptoServiceProvider();
			rsacryptoServiceProvider.ImportParameters(rsaparameters);
			this.SendRSALogin(rsacryptoServiceProvider, this.m_key);
		}

		// Token: 0x06006FF4 RID: 28660 RVA: 0x0002A133 File Offset: 0x00028333
		public FightServerConnector(BattleServer server, string ip, int port, string key)
			: base(ip, port, true, new byte[2048], new byte[2048])
		{
			this.m_server = server;
			this.m_key = key;
			base.Strict = true;
		}

		// Token: 0x06006FF5 RID: 28661 RVA: 0x0024C7FC File Offset: 0x0024A9FC
		public void SendAddRoom(BaseRoom room)
		{
			GSPacketIn gspacketIn = new GSPacketIn(64);
			gspacketIn.WriteInt(room.RoomId);
			gspacketIn.WriteInt(room.PickUpNpcId);
			gspacketIn.WriteBoolean(false);
			gspacketIn.WriteBoolean(room.StartWithNpc);
			gspacketIn.WriteBoolean(false);
			gspacketIn.WriteBoolean(room.isCrosszone);
			gspacketIn.WriteInt((int)room.RoomType);
			gspacketIn.WriteInt((int)room.GameType);
			gspacketIn.WriteInt(room.GuildId);
			List<GamePlayer> players = room.GetPlayers();
			gspacketIn.WriteInt(players.Count);
			foreach (GamePlayer gamePlayer in players)
			{
				gspacketIn.WriteInt(gamePlayer.AreaID);
				gspacketIn.WriteString(gamePlayer.AreaName);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.ID);
				gspacketIn.WriteInt(gamePlayer.CurrentEnemyId);
				gspacketIn.WriteString(gamePlayer.PlayerCharacter.NickName);
				gspacketIn.WriteBoolean(gamePlayer.PlayerCharacter.Sex);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.Hide);
				gspacketIn.WriteString(gamePlayer.PlayerCharacter.Style);
				gspacketIn.WriteString(gamePlayer.PlayerCharacter.Colors);
				gspacketIn.WriteString(gamePlayer.PlayerCharacter.Skin);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.Offer);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.GP);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.Grade);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.Repute);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.AchievementPoint);
				gspacketIn.WriteString(gamePlayer.PlayerCharacter.Honor);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.HonorID);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.FightPower);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.Nimbus);
				gspacketIn.WriteBoolean(gamePlayer.PlayerCharacter.IsMarried);
				bool isMarried = gamePlayer.PlayerCharacter.IsMarried;
				if (isMarried)
				{
					gspacketIn.WriteInt(gamePlayer.PlayerCharacter.SpouseID);
					gspacketIn.WriteString(gamePlayer.PlayerCharacter.SpouseName);
				}
				gspacketIn.WriteString(gamePlayer.PlayerCharacter.WeaklessGuildProgressStr);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.ConsortiaID);
				gspacketIn.WriteString(gamePlayer.PlayerCharacter.ConsortiaName);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.ConsortiaLevel);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.ConsortiaRepute);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.badgeID);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.Attack);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.Defence);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.Agility);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.Luck);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.Blood);
				gspacketIn.WriteByte(gamePlayer.PlayerCharacter.TypeVIP);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.VIPLevel);
				gspacketIn.WriteDateTime(gamePlayer.PlayerCharacter.VIPExpireDay);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.ApprenticeshipState);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.MasterID);
				gspacketIn.WriteString(gamePlayer.PlayerCharacter.MasterOrApprentices);
				gspacketIn.WriteBoolean(gamePlayer.PlayerCharacter.DailyLeagueFirst);
				gspacketIn.WriteInt(gamePlayer.PlayerCharacter.DailyLeagueLastScore);
				gspacketIn.WriteDouble(gamePlayer.GetBaseAttack());
				gspacketIn.WriteDouble(gamePlayer.GetBaseDefence());
				gspacketIn.WriteDouble(gamePlayer.GetBaseAgility());
				gspacketIn.WriteDouble(gamePlayer.GetBaseBlood());
				gspacketIn.WriteInt(gamePlayer.MainWeapon.TemplateID);
				gspacketIn.WriteInt(gamePlayer.MainWeapon.StrengthenLevel);
				bool flag = gamePlayer.MainWeapon.GoldEquip == null;
				if (flag)
				{
					gspacketIn.WriteInt(0);
				}
				else
				{
					gspacketIn.WriteInt(gamePlayer.MainWeapon.GoldEquip.TemplateID);
					gspacketIn.WriteDateTime(gamePlayer.MainWeapon.goldBeginTime);
					gspacketIn.WriteInt(gamePlayer.MainWeapon.goldValidDate);
				}
				gspacketIn.WriteBoolean(gamePlayer.CanUseProp);
				bool flag2 = gamePlayer.SecondWeapon != null;
				if (flag2)
				{
					gspacketIn.WriteInt(gamePlayer.SecondWeapon.TemplateID);
					gspacketIn.WriteInt(gamePlayer.SecondWeapon.StrengthenLevel);
				}
				else
				{
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(0);
				}
				gspacketIn.WriteDouble(gamePlayer.GPAddPlus);
				gspacketIn.WriteDouble(gamePlayer.OfferAddPlus);
				gspacketIn.WriteDouble(gamePlayer.GPApprenticeOnline);
				gspacketIn.WriteDouble(gamePlayer.GPApprenticeTeam);
				gspacketIn.WriteDouble(gamePlayer.GPSpouseTeam);
				gspacketIn.WriteFloat(gamePlayer.GMExperienceRate);
				gspacketIn.WriteFloat(gamePlayer.GMOfferRate);
				gspacketIn.WriteFloat(gamePlayer.GMRichesRate);
				gspacketIn.WriteDouble(gamePlayer.AntiAddictionRate);
				gspacketIn.WriteInt(GameServer.Instance.Configuration.ServerID);
				List<AbstractBuffer> allBuffer = gamePlayer.BufferList.GetAllBuffer();
				gspacketIn.WriteInt(allBuffer.Count);
				foreach (AbstractBuffer abstractBuffer in allBuffer)
				{
					UserBufferInfo info = abstractBuffer.Info;
					gspacketIn.WriteInt(info.Type);
					gspacketIn.WriteBoolean(info.IsExist);
					gspacketIn.WriteDateTime(info.BeginDate);
					gspacketIn.WriteInt(info.ValidDate);
					gspacketIn.WriteInt(info.Value);
					gspacketIn.WriteInt(info.ValidCount);
				}
				gspacketIn.WriteInt(gamePlayer.EquipEffect.Count);
				foreach (int num in gamePlayer.EquipEffect)
				{
					gspacketIn.WriteInt(num);
				}
				gspacketIn.WriteInt(gamePlayer.FightBuffs.Count);
				foreach (UserBufferInfo userBufferInfo in gamePlayer.FightBuffs)
				{
					gspacketIn.WriteInt(userBufferInfo.Type);
					gspacketIn.WriteInt(userBufferInfo.Value);
				}
				gspacketIn.WriteInt(gamePlayer.CardEffect.Count);
				foreach (int num2 in gamePlayer.CardEffect)
				{
					gspacketIn.WriteInt(num2);
				}
				bool flag3 = gamePlayer.UserPet != null;
				gspacketIn.WriteBoolean(flag3);
				bool flag4 = flag3;
				if (flag4)
				{
					gspacketIn.WriteInt(gamePlayer.UserPet.Place);
					gspacketIn.WriteInt(gamePlayer.UserPet.TemplateID);
					gspacketIn.WriteInt(gamePlayer.UserPet.ID);
					gspacketIn.WriteString(gamePlayer.UserPet.Name);
					gspacketIn.WriteInt(gamePlayer.UserPet.UserID);
					gspacketIn.WriteInt(gamePlayer.UserPet.Level);
					gspacketIn.WriteString(gamePlayer.UserPet.Skill);
					gspacketIn.WriteString(gamePlayer.UserPet.SkillEquip);
				}
				gspacketIn.WriteString(gamePlayer.PlayerCharacter.GhostEquipList);
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06006FF6 RID: 28662 RVA: 0x0024D034 File Offset: 0x0024B234
		public void SendRemoveRoom(BaseRoom room)
		{
			this.SendTCP(new GSPacketIn(65)
			{
				Parameter1 = room.RoomId
			});
		}

		// Token: 0x06006FF7 RID: 28663 RVA: 0x001D4450 File Offset: 0x001D2650
		public void SendToGame(int gameId, GSPacketIn pkg)
		{
			GSPacketIn gspacketIn = new GSPacketIn(2, gameId);
			gspacketIn.WritePacket(pkg);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06006FF8 RID: 28664 RVA: 0x0002A16B File Offset: 0x0002836B
		protected void HandleRoomRemove(GSPacketIn packet)
		{
			this.m_server.RemoveRoomImp(packet.ClientID);
		}

		// Token: 0x06006FF9 RID: 28665 RVA: 0x0024D060 File Offset: 0x0024B260
		protected void HandleStartGame(GSPacketIn pkg)
		{
			ProxyGame proxyGame = new ProxyGame(pkg.Parameter2, this, (eRoomType)pkg.ReadInt(), (eGameType)pkg.ReadInt(), pkg.ReadInt());
			this.m_server.StartGame(pkg.Parameter1, proxyGame);
		}

		// Token: 0x06006FFA RID: 28666 RVA: 0x0024D0A0 File Offset: 0x0024B2A0
		protected void HandleStopGame(GSPacketIn pkg)
		{
			int parameter = pkg.Parameter1;
			int parameter2 = pkg.Parameter2;
			this.m_server.StopGame(parameter, parameter2);
		}

		// Token: 0x06006FFB RID: 28667 RVA: 0x0024D0CC File Offset: 0x0024B2CC
		protected void HandleSendToRoom(GSPacketIn pkg)
		{
			int clientID = pkg.ClientID;
			GSPacketIn gspacketIn = pkg.ReadPacket();
			this.m_server.SendToRoom(clientID, gspacketIn, pkg.Parameter1, pkg.Parameter2);
		}

		// Token: 0x06006FFC RID: 28668 RVA: 0x0024D104 File Offset: 0x0024B304
		protected void HandleSendToPlayer(GSPacketIn pkg)
		{
			int clientID = pkg.ClientID;
			try
			{
				GSPacketIn gspacketIn = pkg.ReadPacket();
				this.m_server.SendToUser(clientID, gspacketIn);
			}
			catch (Exception ex)
			{
				FightServerConnector.log.Error(string.Format("pkg len:{0}", pkg.Length), ex);
				FightServerConnector.log.Error(Marshal.ToHexDump("pkg content:", pkg.Buffer, 0, pkg.Length));
			}
		}

		// Token: 0x06006FFD RID: 28669 RVA: 0x0002A180 File Offset: 0x00028380
		private void HandleUpdatePlayerGameId(GSPacketIn pkg)
		{
			this.m_server.UpdatePlayerGameId(pkg.Parameter1, pkg.Parameter2);
		}

		// Token: 0x06006FFE RID: 28670 RVA: 0x0024D18C File Offset: 0x0024B38C
		public void SendChatMessage(string msg, GamePlayer player, bool team)
		{
			GSPacketIn gspacketIn = new GSPacketIn(19, player.CurrentRoom.Game.Id);
			gspacketIn.WriteInt(player.GamePlayerId);
			gspacketIn.WriteBoolean(team);
			gspacketIn.WriteString(msg);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06006FFF RID: 28671 RVA: 0x0024D1D8 File Offset: 0x0024B3D8
		public void SendFightNotice(GamePlayer player, int GameId)
		{
			this.SendTCP(new GSPacketIn(3, GameId)
			{
				Parameter1 = player.GamePlayerId
			});
		}

		// Token: 0x06007000 RID: 28672 RVA: 0x0024D204 File Offset: 0x0024B404
		private void HandleFightNPC(GSPacketIn packet)
		{
			int num = packet.ReadInt();
			int num2 = packet.ReadInt();
			int num3 = packet.ReadInt();
			GamePlayer playerById = WorldMgr.GetPlayerById(packet.Parameter1);
			bool flag = playerById != null;
			if (flag)
			{
				RingStationMgr.CreateAutoBot(playerById, num, num2, num3);
			}
		}

		// Token: 0x06007001 RID: 28673 RVA: 0x0002A19B File Offset: 0x0002839B
		private void HandleAddEliteScore(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.AddEliteScore(pkg.Parameter1);
			}
		}

		// Token: 0x06007002 RID: 28674 RVA: 0x0002A1BB File Offset: 0x000283BB
		private void HandleRemoveEliteScore(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.RemoveEliteScore(pkg.Parameter1);
			}
		}

		// Token: 0x06007003 RID: 28675 RVA: 0x0002A1DB File Offset: 0x000283DB
		private void HandleEliteScoreWinUpdate(GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(pkg.ClientID);
			if (playerById != null)
			{
				playerById.SendWinEliteChampion(pkg.ReadInt(), pkg.ReadInt());
			}
		}

		// Token: 0x04003C41 RID: 15425
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04003C42 RID: 15426
		private BattleServer m_server;

		// Token: 0x04003C43 RID: 15427
		private string m_key;
	}
}
