﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EA7 RID: 3751
	public class CE1231 : BasePetEffect
	{
		// Token: 0x0600818F RID: 33167 RVA: 0x002AD5F4 File Offset: 0x002AB7F4
		public CE1231(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1231, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008190 RID: 33168 RVA: 0x002AD674 File Offset: 0x002AB874
		public override bool Start(Living living)
		{
			CE1231 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1231) as CE1231;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008191 RID: 33169 RVA: 0x00032CC7 File Offset: 0x00030EC7
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008192 RID: 33170 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008193 RID: 33171 RVA: 0x002AD6D4 File Offset: 0x002AB8D4
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			living.Game.SendPetBuff(living, base.ElementInfo, true);
			this.m_added = 500;
			damageAmount -= this.m_added;
			bool flag = damageAmount < 0;
			if (flag)
			{
				damageAmount = 1;
			}
		}

		// Token: 0x06008194 RID: 33172 RVA: 0x002AD71C File Offset: 0x002AB91C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008195 RID: 33173 RVA: 0x00032D03 File Offset: 0x00030F03
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
		}

		// Token: 0x040050CE RID: 20686
		private int m_type = 0;

		// Token: 0x040050CF RID: 20687
		private int m_count = 0;

		// Token: 0x040050D0 RID: 20688
		private int m_probability = 0;

		// Token: 0x040050D1 RID: 20689
		private int m_delay = 0;

		// Token: 0x040050D2 RID: 20690
		private int m_coldDown = 0;

		// Token: 0x040050D3 RID: 20691
		private int m_currentId;

		// Token: 0x040050D4 RID: 20692
		private int m_added = 0;
	}
}
