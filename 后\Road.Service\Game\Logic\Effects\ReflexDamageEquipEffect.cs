﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EF8 RID: 3832
	public class ReflexDamageEquipEffect : BasePlayerEffect
	{
		// Token: 0x06008377 RID: 33655 RVA: 0x00034602 File Offset: 0x00032802
		public ReflexDamageEquipEffect(int count, int probability)
			: base(eEffectType.ReflexDamageEquipEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x06008378 RID: 33656 RVA: 0x002B3CD4 File Offset: 0x002B1ED4
		public override bool Start(Living living)
		{
			ReflexDamageEquipEffect reflexDamageEquipEffect = living.EffectList.GetOfType(eEffectType.ReflexDamageEquipEffect) as ReflexDamageEquipEffect;
			bool flag = reflexDamageEquipEffect != null;
			bool flag2;
			if (flag)
			{
				reflexDamageEquipEffect.m_probability = ((this.m_probability > reflexDamageEquipEffect.m_probability) ? this.m_probability : reflexDamageEquipEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008379 RID: 33657 RVA: 0x00005683 File Offset: 0x00003883
		protected override void OnAttachedToPlayer(Player player)
		{
		}

		// Token: 0x0600837A RID: 33658 RVA: 0x002B3D30 File Offset: 0x002B1F30
		private void player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				target.SyncAtTime = true;
				target.AddBlood(-this.m_count, 1);
				target.SyncAtTime = false;
			}
		}

		// Token: 0x0600837B RID: 33659 RVA: 0x00005683 File Offset: 0x00003883
		protected override void OnRemovedFromPlayer(Player player)
		{
		}

		// Token: 0x0600837C RID: 33660 RVA: 0x002B3D6C File Offset: 0x002B1F6C
		public void ChangeProperty(Living living)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				this.IsTrigger = true;
				living.DefenceEffectTrigger = true;
				living.Game.AddAction(new LivingSayAction(living, LanguageMgr.GetTranslation("ReflexDamageEquipEffect.msg", Array.Empty<object>()), 9, 1000, 1000));
			}
		}

		// Token: 0x0400521A RID: 21018
		private int m_count;

		// Token: 0x0400521B RID: 21019
		private int m_probability;
	}
}
