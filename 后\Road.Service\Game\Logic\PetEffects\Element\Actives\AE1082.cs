﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DB7 RID: 3511
	public class AE1082 : BasePetEffect
	{
		// Token: 0x06007C34 RID: 31796 RVA: 0x00296560 File Offset: 0x00294760
		public AE1082(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1082, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C35 RID: 31797 RVA: 0x002965E0 File Offset: 0x002947E0
		public override bool Start(Living living)
		{
			AE1082 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1082) as AE1082;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C36 RID: 31798 RVA: 0x0002F8FE File Offset: 0x0002DAFE
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C37 RID: 31799 RVA: 0x0002F914 File Offset: 0x0002DB14
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C38 RID: 31800 RVA: 0x00296640 File Offset: 0x00294840
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPlayerPicture(player, 5, true);
				player.IsNoHole = true;
			}
		}

		// Token: 0x04004A3B RID: 19003
		private int m_type = 0;

		// Token: 0x04004A3C RID: 19004
		private int m_count = 0;

		// Token: 0x04004A3D RID: 19005
		private int m_probability = 0;

		// Token: 0x04004A3E RID: 19006
		private int m_delay = 0;

		// Token: 0x04004A3F RID: 19007
		private int m_coldDown = 0;

		// Token: 0x04004A40 RID: 19008
		private int m_currentId;

		// Token: 0x04004A41 RID: 19009
		private int m_added = 0;
	}
}
