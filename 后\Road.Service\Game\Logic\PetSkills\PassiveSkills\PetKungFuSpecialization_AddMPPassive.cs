﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CF6 RID: 3318
	public class PetKungFuSpecialization_AddMPPassive : BasePetEffect
	{
		// Token: 0x06007829 RID: 30761 RVA: 0x002844E8 File Offset: 0x002826E8
		public PetKungFuSpecialization_AddMPPassive(int probability, string elementID)
			: base(ePetEffectType.PetKungFuSpecialization_AddMPPassive, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			if (!(elementID == "1575"))
			{
				if (!(elementID == "1611"))
				{
					if (elementID == "1624")
					{
						this.m_value = 10;
					}
				}
				else
				{
					this.m_value = 2;
				}
			}
			else
			{
				this.m_value = 4;
			}
		}

		// Token: 0x0600782A RID: 30762 RVA: 0x00284574 File Offset: 0x00282774
		public override bool Start(Living living)
		{
			PetKungFuSpecialization_AddMPPassive petKungFuSpecialization_AddMPPassive = living.PetEffectList.GetOfType(ePetEffectType.PetKungFuSpecialization_AddMPPassive) as PetKungFuSpecialization_AddMPPassive;
			bool flag = petKungFuSpecialization_AddMPPassive != null;
			bool flag2;
			if (flag)
			{
				petKungFuSpecialization_AddMPPassive.m_probability = ((this.m_probability > petKungFuSpecialization_AddMPPassive.m_probability) ? this.m_probability : petKungFuSpecialization_AddMPPassive.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600782B RID: 30763 RVA: 0x0002C705 File Offset: 0x0002A905
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerSkip += new PlayerEventHandle(this.player_PlayerSkip);
			player.BeginNextTurn += this.player_BeginNextTurn;
			player.PlayerAfterBuffSkillPet += new PlayerEventHandle(this.player_PlayerAfterBuffSkillPet);
		}

		// Token: 0x0600782C RID: 30764 RVA: 0x0002C741 File Offset: 0x0002A941
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerSkip -= new PlayerEventHandle(this.player_PlayerSkip);
			player.BeginNextTurn -= this.player_BeginNextTurn;
			player.PlayerAfterBuffSkillPet -= new PlayerEventHandle(this.player_PlayerAfterBuffSkillPet);
		}

		// Token: 0x0600782D RID: 30765 RVA: 0x002845D4 File Offset: 0x002827D4
		private void player_BeginNextTurn(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetStealthPostureEquip) is PetStealthPostureEquip;
			if (flag)
			{
				((Player)living).AddPetMP(this.m_value);
				bool flag2 = this.m_count < 6 && this.m_value == 10;
				if (flag2)
				{
					living.MaxBlood += living.MaxBlood * 10 / 100;
					living.Game.UpdateMaxBlood(living);
					this.m_count++;
				}
			}
		}

		// Token: 0x0600782E RID: 30766 RVA: 0x00284664 File Offset: 0x00282864
		private void player_PlayerSkip(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetDefensivePosture_DamageEquip) is PetDefensivePosture_DamageEquip;
			if (flag)
			{
				((Player)living).AddPetMP(this.m_value);
				bool flag2 = this.m_count < 6 && this.m_value == 10;
				if (flag2)
				{
					living.MaxBlood += living.MaxBlood * 10 / 100;
					living.Game.UpdateMaxBlood(living);
					this.m_count++;
					living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				}
			}
		}

		// Token: 0x0600782F RID: 30767 RVA: 0x00284708 File Offset: 0x00282908
		private void player_PlayerAfterBuffSkillPet(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetKungFuSpecializationEquip) is PetKungFuSpecializationEquip;
			if (flag)
			{
				((Player)living).AddPetMP(this.m_value);
				bool flag2 = this.m_count < 6 && this.m_value == 10;
				if (flag2)
				{
					living.MaxBlood += living.MaxBlood * 10 / 100;
					living.Game.UpdateMaxBlood(living);
					this.m_count++;
					living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				}
			}
		}

		// Token: 0x04004670 RID: 18032
		private int m_probability = 0;

		// Token: 0x04004671 RID: 18033
		private int m_value = 0;

		// Token: 0x04004672 RID: 18034
		private int m_count = 0;
	}
}
