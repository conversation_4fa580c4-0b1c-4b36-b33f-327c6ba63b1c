﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DF7 RID: 3575
	public class AE1197 : BasePetEffect
	{
		// Token: 0x06007D83 RID: 32131 RVA: 0x0029C524 File Offset: 0x0029A724
		public AE1197(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1197, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D84 RID: 32132 RVA: 0x0029C5A4 File Offset: 0x0029A7A4
		public override bool Start(Living living)
		{
			AE1197 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1197) as AE1197;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D85 RID: 32133 RVA: 0x000305B5 File Offset: 0x0002E7B5
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D86 RID: 32134 RVA: 0x000305CB File Offset: 0x0002E7CB
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D87 RID: 32135 RVA: 0x0029C604 File Offset: 0x0029A804
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1197(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004BF9 RID: 19449
		private int m_type = 0;

		// Token: 0x04004BFA RID: 19450
		private int m_count = 0;

		// Token: 0x04004BFB RID: 19451
		private int m_probability = 0;

		// Token: 0x04004BFC RID: 19452
		private int m_delay = 0;

		// Token: 0x04004BFD RID: 19453
		private int m_coldDown = 0;

		// Token: 0x04004BFE RID: 19454
		private int m_currentId;

		// Token: 0x04004BFF RID: 19455
		private int m_added = 0;
	}
}
