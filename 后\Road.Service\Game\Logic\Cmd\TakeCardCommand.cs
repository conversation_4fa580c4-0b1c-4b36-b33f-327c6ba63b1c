﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F16 RID: 3862
	[GameCommand(98, "翻牌")]
	public class TakeCardCommand : ICommandHandler
	{
		// Token: 0x060083C5 RID: 33733 RVA: 0x002B5828 File Offset: 0x002B3A28
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			if (!player.FinishTakeCard && player.CanTakeOut > 0)
			{
				int num = (int)packet.ReadByte();
				if (num == 100)
				{
					int canTakeOut = player.CanTakeOut;
					for (int i = 0; i < canTakeOut; i++)
					{
						game.TakeCard(player, true);
					}
					return;
				}
				if (num < 0 || num > game.Cards.Length)
				{
					game.TakeCard(player, false);
					return;
				}
				game.TakeCard(player, num, false);
			}
		}
	}
}
