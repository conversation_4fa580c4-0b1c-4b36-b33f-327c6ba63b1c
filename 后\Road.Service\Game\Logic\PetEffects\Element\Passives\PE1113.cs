﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D64 RID: 3428
	public class PE1113 : BasePetEffect
	{
		// Token: 0x06007A8B RID: 31371 RVA: 0x0028F404 File Offset: 0x0028D604
		public PE1113(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1113, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A8C RID: 31372 RVA: 0x0028F484 File Offset: 0x0028D684
		public override bool Start(Living living)
		{
			PE1113 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1113) as PE1113;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A8D RID: 31373 RVA: 0x0002E868 File Offset: 0x0002CA68
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
		}

		// Token: 0x06007A8E RID: 31374 RVA: 0x0028F4E4 File Offset: 0x0028D6E4
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = this.rand.Next(100) > 20;
			if (!flag)
			{
				this.m_added = 300;
				bool flag2 = this.m_added <= 0;
				if (!flag2)
				{
					target.SyncAtTime = true;
					target.AddBlood(-this.m_added, 1);
					target.SyncAtTime = false;
					bool flag3 = target.Blood <= 0;
					if (flag3)
					{
						target.Die();
						bool flag4 = living != null && living is Player;
						if (flag4)
						{
							(living as Player).PlayerDetail.OnKillingLiving(living.Game, 2, living.Id, living.IsLiving, this.m_added);
						}
					}
					else
					{
						target.AddPetEffect(new CE1113(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
					}
					living.PetEffects.ActiveEffect = false;
				}
			}
		}

		// Token: 0x06007A8F RID: 31375 RVA: 0x0002E87E File Offset: 0x0002CA7E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x040047FC RID: 18428
		private int m_type = 0;

		// Token: 0x040047FD RID: 18429
		private int m_count = 0;

		// Token: 0x040047FE RID: 18430
		private int m_probability = 0;

		// Token: 0x040047FF RID: 18431
		private int m_delay = 0;

		// Token: 0x04004800 RID: 18432
		private int m_coldDown = 0;

		// Token: 0x04004801 RID: 18433
		private int m_currentId;

		// Token: 0x04004802 RID: 18434
		private int m_added = 0;
	}
}
