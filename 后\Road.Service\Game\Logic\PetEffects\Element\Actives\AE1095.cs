﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DC3 RID: 3523
	public class AE1095 : BasePetEffect
	{
		// Token: 0x06007C72 RID: 31858 RVA: 0x002978C4 File Offset: 0x00295AC4
		public AE1095(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1095, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C73 RID: 31859 RVA: 0x00297944 File Offset: 0x00295B44
		public override bool Start(Living living)
		{
			AE1095 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1095) as AE1095;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C74 RID: 31860 RVA: 0x0002FAB0 File Offset: 0x0002DCB0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C75 RID: 31861 RVA: 0x0002FAC6 File Offset: 0x0002DCC6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C76 RID: 31862 RVA: 0x002979A4 File Offset: 0x00295BA4
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.AddPetEffect(new CE1095(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004A8F RID: 19087
		private int m_type = 0;

		// Token: 0x04004A90 RID: 19088
		private int m_count = 0;

		// Token: 0x04004A91 RID: 19089
		private int m_probability = 0;

		// Token: 0x04004A92 RID: 19090
		private int m_delay = 0;

		// Token: 0x04004A93 RID: 19091
		private int m_coldDown = 0;

		// Token: 0x04004A94 RID: 19092
		private int m_currentId;

		// Token: 0x04004A95 RID: 19093
		private int m_added = 0;
	}
}
