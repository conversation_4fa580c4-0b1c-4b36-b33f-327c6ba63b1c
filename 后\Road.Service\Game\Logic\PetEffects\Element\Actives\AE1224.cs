﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E0A RID: 3594
	public class AE1224 : BasePetEffect
	{
		// Token: 0x06007DE7 RID: 32231 RVA: 0x0029E290 File Offset: 0x0029C490
		public AE1224(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1224, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DE8 RID: 32232 RVA: 0x0029E310 File Offset: 0x0029C510
		public override bool Start(Living living)
		{
			AE1224 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1224) as AE1224;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DE9 RID: 32233 RVA: 0x000309C1 File Offset: 0x0002EBC1
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DEA RID: 32234 RVA: 0x000309D7 File Offset: 0x0002EBD7
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DEB RID: 32235 RVA: 0x0029E370 File Offset: 0x0029C570
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && !this.IsTrigger;
			if (flag)
			{
				this.m_added = 500;
				player.AddBlood(-this.m_added, 1);
				bool flag2 = player.Blood <= 0;
				if (flag2)
				{
					player.Die();
				}
				this.IsTrigger = true;
			}
		}

		// Token: 0x04004C7E RID: 19582
		private int m_type = 0;

		// Token: 0x04004C7F RID: 19583
		private int m_count = 0;

		// Token: 0x04004C80 RID: 19584
		private int m_probability = 0;

		// Token: 0x04004C81 RID: 19585
		private int m_delay = 0;

		// Token: 0x04004C82 RID: 19586
		private int m_coldDown = 0;

		// Token: 0x04004C83 RID: 19587
		private int m_currentId;

		// Token: 0x04004C84 RID: 19588
		private int m_added = 0;
	}
}
