﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D20 RID: 3360
	public class AE4848 : BasePetEffect
	{
		// Token: 0x06007905 RID: 30981 RVA: 0x0002D227 File Offset: 0x0002B427
		public AE4848(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE4848, elementID)
		{
			this.m_count = count;
			this.m_currentId = skillId;
			this.m_probability = ((probability == -1) ? 10000 : probability);
		}

		// Token: 0x06007906 RID: 30982 RVA: 0x00288EF4 File Offset: 0x002870F4
		public override bool Start(Living living)
		{
			AE4848 ae = living.PetEffectList.GetOfType(ePetEffectType.AE4848) as AE4848;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007907 RID: 30983 RVA: 0x0002D260 File Offset: 0x0002B460
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
			player.AfterKillingLiving += this.player_AfterKillingLiving;
		}

		// Token: 0x06007908 RID: 30984 RVA: 0x0002D289 File Offset: 0x0002B489
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
			player.AfterKillingLiving -= this.player_AfterKillingLiving;
		}

		// Token: 0x06007909 RID: 30985 RVA: 0x00288F54 File Offset: 0x00287154
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x0600790A RID: 30986 RVA: 0x00288F84 File Offset: 0x00287184
		private void player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = this.IsTrigger && target is Player;
			if (flag)
			{
				target.Game.sendShowPicSkil(target, base.ElementInfo, true);
				target.AddPetEffect(new CE4848(this.m_count, base.ElementInfo.ID.ToString()), 0);
				this.IsTrigger = false;
			}
		}

		// Token: 0x040046E7 RID: 18151
		private int m_probability = 0;

		// Token: 0x040046E8 RID: 18152
		private int m_currentId;

		// Token: 0x040046E9 RID: 18153
		private int m_count;
	}
}
