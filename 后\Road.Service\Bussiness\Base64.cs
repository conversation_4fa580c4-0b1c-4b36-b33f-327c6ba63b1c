﻿using System;

namespace Bussiness
{
	// Token: 0x02000FA1 RID: 4001
	public class Base64
	{
		// Token: 0x0600880F RID: 34831 RVA: 0x002C9DD8 File Offset: 0x002C7FD8
		public static string encodeByteArray(byte[] data)
		{
			string text = "";
			byte[] array = new byte[4];
			for (int i = 0; i < data.Length; i += 4)
			{
				byte[] array2 = new byte[3];
				for (int j = 0; j < data.Length; j++)
				{
					bool flag = j < 3;
					if (flag)
					{
						bool flag2 = j + i > data.Length;
						if (flag2)
						{
							break;
						}
						array2[j] = data[j + i];
					}
				}
				array[0] = (byte)((array2[0] & 252) >> 2);
				array[1] = (byte)(((int)(array2[0] & 3) << 4) | (array2[1] >> 4));
				array[2] = (byte)(((int)(array2[1] & 15) << 2) | (array2[2] >> 6));
				array[3] = array2[2] & 63;
				for (int k = array2.Length; k < 3; k++)
				{
					array[k + 1] = 64;
				}
				for (int l = 0; l < array.Length; l++)
				{
					text += Base64.BASE64_CHARS.Substring((int)array[l], 1);
				}
			}
			text = text.Substring(0, data.Length - 1);
			return text + "=";
		}

		// Token: 0x06008810 RID: 34832 RVA: 0x002C9F08 File Offset: 0x002C8108
		public static byte[] decodeToByteArray(string data)
		{
			byte[] array = new byte[data.Length];
			byte[] array2 = new byte[4];
			byte[] array3 = new byte[3];
			for (int i = 0; i < data.Length; i += 4)
			{
				int num = 0;
				while (num < 4 && i + num < data.Length)
				{
					array2[num] = (byte)Base64.BASE64_CHARS.IndexOf(data.Substring(i + num, 1));
					num++;
				}
				array3[0] = (byte)(((int)array2[0] << 2) + ((array2[1] & 48) >> 4));
				array3[1] = (byte)(((int)(array2[1] & 15) << 4) + ((array2[2] & 60) >> 2));
				array3[2] = (byte)(((int)(array2[2] & 3) << 6) + (int)array2[3]);
				int num2 = 0;
				while (num2 < array3.Length && array2[num2 + 1] != 64)
				{
					array[i + num2] = array3[num2];
					num2++;
				}
			}
			return array;
		}

		// Token: 0x040053C6 RID: 21446
		public static string version = "1.0.0";

		// Token: 0x040053C7 RID: 21447
		private static string BASE64_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
	}
}
