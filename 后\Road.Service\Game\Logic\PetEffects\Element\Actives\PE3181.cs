﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E40 RID: 3648
	public class PE3181 : BasePetEffect
	{
		// Token: 0x06007F0C RID: 32524 RVA: 0x002A3794 File Offset: 0x002A1994
		public PE3181(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE3181, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F0D RID: 32525 RVA: 0x002A3810 File Offset: 0x002A1A10
		public override bool Start(Living living)
		{
			PE3181 pe = living.PetEffectList.GetOfType(ePetEffectType.PE3181) as PE3181;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F0E RID: 32526 RVA: 0x000315FE File Offset: 0x0002F7FE
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
		}

		// Token: 0x06007F0F RID: 32527 RVA: 0x00031627 File Offset: 0x0002F827
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
		}

		// Token: 0x06007F10 RID: 32528 RVA: 0x002A386C File Offset: 0x002A1A6C
		private void Player_BeginSelfTurn(Living player)
		{
			bool flag = player.PetEffects.ReduceCritical == 0;
			if (flag)
			{
				player.PetEffects.ReduceCritical = 30;
				player.Game.SendPetBuff(player, base.Info, true);
			}
		}

		// Token: 0x06007F11 RID: 32529 RVA: 0x002A38B0 File Offset: 0x002A1AB0
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = criticalAmount > 0;
			if (flag)
			{
				criticalAmount -= criticalAmount * 30 / 100;
			}
		}

		// Token: 0x04004DFB RID: 19963
		private int m_type = 0;

		// Token: 0x04004DFC RID: 19964
		private int m_count = 0;

		// Token: 0x04004DFD RID: 19965
		private int m_probability = 0;

		// Token: 0x04004DFE RID: 19966
		private int m_delay = 0;

		// Token: 0x04004DFF RID: 19967
		private int m_coldDown = 0;

		// Token: 0x04004E00 RID: 19968
		private int m_currentId;

		// Token: 0x04004E01 RID: 19969
		private int m_added = 0;
	}
}
