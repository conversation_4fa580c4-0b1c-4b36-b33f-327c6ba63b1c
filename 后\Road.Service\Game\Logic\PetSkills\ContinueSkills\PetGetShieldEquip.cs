﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D0C RID: 3340
	public class PetGetShieldEquip : AbstractPetEffect
	{
		// Token: 0x0600789A RID: 30874 RVA: 0x0002CC7D File Offset: 0x0002AE7D
		public PetGetShieldEquip(int count, int value, int value2, string elementID)
			: base(ePetEffectType.PetGetShieldEquip, elementID)
		{
			this.m_count = count;
			this.m_value = value;
			this.m_value2 = value2;
		}

		// Token: 0x0600789B RID: 30875 RVA: 0x00286C98 File Offset: 0x00284E98
		public override bool Start(Living living)
		{
			PetGetShieldEquip petGetShieldEquip = living.PetEffectList.GetOfType(ePetEffectType.PetGetShieldEquip) as PetGetShieldEquip;
			bool flag = petGetShieldEquip != null;
			bool flag2;
			if (flag)
			{
				petGetShieldEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600789C RID: 30876 RVA: 0x00286CE0 File Offset: 0x00284EE0
		public override void OnAttached(Living player)
		{
			player.Game.sendShowPicSkil(player, base.Info, false);
			player.Game.sendShowPicSkil(player, base.Info, true);
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
			player.BeginSelfTurn += this.player_SelfTurn;
			player.AfterBeingCrited += this.player_BeforeTakeDamage;
		}

		// Token: 0x0600789D RID: 30877 RVA: 0x0002CCA3 File Offset: 0x0002AEA3
		public override void OnRemoved(Living player)
		{
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
			player.BeginSelfTurn += this.player_SelfTurn;
			player.AfterBeingCrited -= this.player_BeforeTakeDamage;
		}

		// Token: 0x0600789E RID: 30878 RVA: 0x00286D50 File Offset: 0x00284F50
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool beingCrited = living.BeingCrited;
			if (beingCrited)
			{
				damageAmount -= this.m_value2 / 2;
				criticalAmount -= this.m_value2 / 2;
				bool flag = damageAmount < 0;
				if (flag)
				{
					damageAmount = 0;
				}
				living.Game.sendShowPicSkil(living, base.Info, true);
			}
			bool flag2 = this.m_value > 0;
			if (flag2)
			{
				living.Game.sendShowPicSkil(living, base.Info, true);
				int num = damageAmount;
				int num2 = criticalAmount;
				damageAmount -= this.m_value;
				criticalAmount -= this.m_value;
				bool flag3 = damageAmount <= 0;
				if (flag3)
				{
					damageAmount = 0;
				}
				bool flag4 = criticalAmount <= 0;
				if (flag4)
				{
					criticalAmount = 0;
				}
				this.m_value -= num;
				this.m_value -= num2;
				living.Game.SendEquipEffect(living, string.Format("触发<星之盾>,抵挡{0}伤害,{1}暴击伤害.", num, num2));
				bool flag5 = this.m_value <= 0;
				if (flag5)
				{
					living.Game.SendEquipEffect(living, "屏障消除");
					this.m_value = 0;
					this.m_count = -1;
				}
			}
		}

		// Token: 0x0600789F RID: 30879 RVA: 0x00286E8C File Offset: 0x0028508C
		private void player_BeforeTakeDamage2(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.m_value > 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.Info, true);
				int num = damageAmount;
				int num2 = criticalAmount;
				damageAmount -= this.m_value;
				criticalAmount -= this.m_value;
				bool flag2 = damageAmount <= 0;
				if (flag2)
				{
					damageAmount = 0;
				}
				bool flag3 = criticalAmount <= 0;
				if (flag3)
				{
					criticalAmount = 0;
				}
				this.m_value -= num;
				this.m_value -= num2;
				living.Game.SendEquipEffect(living, string.Format("触发<星之盾>,抵挡{0}伤害,{1}暴击伤害.", num, num2));
				bool flag4 = this.m_value <= 0;
				if (flag4)
				{
					living.Game.SendEquipEffect(living, "屏障消除");
					this.m_value = 0;
					this.m_count = -1;
				}
			}
			else
			{
				Console.WriteLine("载入到了这里");
			}
		}

		// Token: 0x060078A0 RID: 30880 RVA: 0x00286F88 File Offset: 0x00285188
		private void player_SelfTurn(Living living)
		{
			living.Game.sendShowPicSkil(living, base.Info, false);
			living.Game.sendShowPicSkil(living, base.Info, true);
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.Info, false);
				this.Stop();
			}
		}

		// Token: 0x040046B0 RID: 18096
		private int m_count;

		// Token: 0x040046B1 RID: 18097
		private int m_value;

		// Token: 0x040046B2 RID: 18098
		private int m_value2;
	}
}
