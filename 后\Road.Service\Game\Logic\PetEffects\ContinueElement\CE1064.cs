﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E5F RID: 3679
	public class CE1064 : BasePetEffect
	{
		// Token: 0x06007FC4 RID: 32708 RVA: 0x002A674C File Offset: 0x002A494C
		public CE1064(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1064, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FC5 RID: 32709 RVA: 0x002A67CC File Offset: 0x002A49CC
		public override bool Start(Living living)
		{
			CE1064 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1064) as CE1064;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FC6 RID: 32710 RVA: 0x00031BC6 File Offset: 0x0002FDC6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007FC7 RID: 32711 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FC8 RID: 32712 RVA: 0x002A682C File Offset: 0x002A4A2C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 800;
				living.SyncAtTime = true;
				living.AddBlood(this.m_added);
				living.SyncAtTime = false;
				foreach (Living living2 in living.Game.Map.FindAllNearestSameTeam(living.X, living.Y, 250.0, living))
				{
					bool flag2 = living2 != living;
					if (flag2)
					{
						living2.SyncAtTime = true;
						living2.AddBlood(this.m_added);
						living2.SyncAtTime = false;
					}
				}
			}
		}

		// Token: 0x06007FC9 RID: 32713 RVA: 0x00031BEF File Offset: 0x0002FDEF
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004ED4 RID: 20180
		private int m_type = 0;

		// Token: 0x04004ED5 RID: 20181
		private int m_count = 0;

		// Token: 0x04004ED6 RID: 20182
		private int m_probability = 0;

		// Token: 0x04004ED7 RID: 20183
		private int m_delay = 0;

		// Token: 0x04004ED8 RID: 20184
		private int m_coldDown = 0;

		// Token: 0x04004ED9 RID: 20185
		private int m_currentId;

		// Token: 0x04004EDA RID: 20186
		private int m_added = 0;
	}
}
