﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DDB RID: 3547
	public class AE1153 : BasePetEffect
	{
		// Token: 0x06007CF6 RID: 31990 RVA: 0x00299AA4 File Offset: 0x00297CA4
		public AE1153(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1153, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CF7 RID: 31991 RVA: 0x00299B24 File Offset: 0x00297D24
		public override bool Start(Living living)
		{
			AE1153 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1153) as AE1153;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CF8 RID: 31992 RVA: 0x000300D2 File Offset: 0x0002E2D2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007CF9 RID: 31993 RVA: 0x000300E8 File Offset: 0x0002E2E8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007CFA RID: 31994 RVA: 0x00299B84 File Offset: 0x00297D84
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1153(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004B35 RID: 19253
		private int m_type = 0;

		// Token: 0x04004B36 RID: 19254
		private int m_count = 0;

		// Token: 0x04004B37 RID: 19255
		private int m_probability = 0;

		// Token: 0x04004B38 RID: 19256
		private int m_delay = 0;

		// Token: 0x04004B39 RID: 19257
		private int m_coldDown = 0;

		// Token: 0x04004B3A RID: 19258
		private int m_currentId;

		// Token: 0x04004B3B RID: 19259
		private int m_added = 0;
	}
}
