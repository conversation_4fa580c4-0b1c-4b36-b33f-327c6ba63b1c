﻿using System;

namespace Game.Logic.Actions
{
	// Token: 0x02000F64 RID: 3940
	public class PlayBackgroundSoundAction : BaseAction
	{
		// Token: 0x06008522 RID: 34082 RVA: 0x000355FC File Offset: 0x000337FC
		public PlayBackgroundSoundAction(bool isPlay, int delay)
			: base(delay, 1000)
		{
			this.m_isPlay = isPlay;
		}

		// Token: 0x06008523 RID: 34083 RVA: 0x00035613 File Offset: 0x00033813
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			((PVEGame)game).SendPlayBackgroundSound(this.m_isPlay);
			base.Finish(tick);
		}

		// Token: 0x04005331 RID: 21297
		private bool m_isPlay;
	}
}
