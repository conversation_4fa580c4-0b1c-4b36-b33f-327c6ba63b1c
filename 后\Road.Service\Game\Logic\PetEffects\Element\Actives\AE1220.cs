﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E07 RID: 3591
	public class AE1220 : BasePetEffect
	{
		// Token: 0x06007DD6 RID: 32214 RVA: 0x0029DCD8 File Offset: 0x0029BED8
		public AE1220(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1220, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DD7 RID: 32215 RVA: 0x0029DD58 File Offset: 0x0029BF58
		public override bool Start(Living living)
		{
			AE1220 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1220) as AE1220;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DD8 RID: 32216 RVA: 0x000308E7 File Offset: 0x0002EAE7
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DD9 RID: 32217 RVA: 0x000308FD File Offset: 0x0002EAFD
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DDA RID: 32218 RVA: 0x0029DDB8 File Offset: 0x0029BFB8
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddPetEffect(new CE1220(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004C69 RID: 19561
		private int m_type = 0;

		// Token: 0x04004C6A RID: 19562
		private int m_count = 0;

		// Token: 0x04004C6B RID: 19563
		private int m_probability = 0;

		// Token: 0x04004C6C RID: 19564
		private int m_delay = 0;

		// Token: 0x04004C6D RID: 19565
		private int m_coldDown = 0;

		// Token: 0x04004C6E RID: 19566
		private int m_currentId;

		// Token: 0x04004C6F RID: 19567
		private int m_added = 0;
	}
}
