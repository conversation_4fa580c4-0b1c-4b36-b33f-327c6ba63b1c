﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Xml.Linq;
using Game.Server.Rooms;
using log4net;

namespace Game.Server.Battle
{
	// Token: 0x02000C42 RID: 3138
	public class BattleMgr
	{
		// Token: 0x06006FAC RID: 28588 RVA: 0x0024B224 File Offset: 0x00249424
		public static bool Setup()
		{
			bool flag = File.Exists("battle.xml");
			if (flag)
			{
				try
				{
					XDocument xdocument = XDocument.Load("battle.xml");
					bool flag2 = xdocument.Root != null;
					if (flag2)
					{
						foreach (XNode xnode in xdocument.Root.Nodes())
						{
							XElement xelement = (XElement)xnode;
							try
							{
								int num = int.Parse(xelement.Attribute("id").Value);
								string value = xelement.Attribute("ip").Value;
								int num2 = int.Parse(xelement.Attribute("port").Value);
								string value2 = xelement.Attribute("key").Value;
								BattleMgr.AddBattleServer(new BattleServer(num, value, num2, value2));
								BattleMgr.log.InfoFormat("Battle server {0}:{1} loaded...", value, num2);
							}
							catch (Exception ex)
							{
								BattleMgr.log.Error("BattleMgr setup error:", ex);
							}
						}
					}
				}
				catch (Exception ex2)
				{
					BattleMgr.log.Error("BattleMgr setup error:", ex2);
				}
			}
			BattleMgr.log.InfoFormat("Total {0} battle server loaded.", BattleMgr.m_list.Count);
			return true;
		}

		// Token: 0x06006FAD RID: 28589 RVA: 0x0024B3E4 File Offset: 0x002495E4
		public static void AddBattleServer(BattleServer battle)
		{
			bool flag = battle != null;
			if (flag)
			{
				BattleMgr.m_list.Add(battle);
				battle.Disconnected += BattleMgr.battle_Disconnected;
			}
		}

		// Token: 0x06006FAE RID: 28590 RVA: 0x0024B41C File Offset: 0x0024961C
		private static void battle_Disconnected(object sender, EventArgs e)
		{
			BattleServer battleServer = sender as BattleServer;
			bool flag = battleServer == null;
			if (!flag)
			{
				BattleMgr.log.WarnFormat("Disconnect from battle server {0}:{1}", battleServer.Ip, battleServer.Port);
				bool flag2 = !BattleMgr.AutoReconnect || !BattleMgr.m_list.Contains(battleServer);
				if (!flag2)
				{
					BattleMgr.RemoveServer(battleServer);
					bool flag3 = (DateTime.Now - battleServer.LastRetryTime).TotalMinutes > 3.0;
					if (flag3)
					{
						battleServer.RetryCount = 0;
					}
					bool flag4 = battleServer.RetryCount >= 3;
					if (!flag4)
					{
						BattleServer battleServer2 = battleServer.Clone();
						BattleMgr.AddBattleServer(battleServer2);
						BattleServer battleServer3 = battleServer2;
						int retryCount = battleServer3.RetryCount;
						battleServer3.RetryCount = retryCount + 1;
						battleServer2.LastRetryTime = DateTime.Now;
						try
						{
							battleServer2.Start();
						}
						catch (Exception ex)
						{
							BattleMgr.log.ErrorFormat("Batter server {0}:{1} can't connected!", battleServer.Ip, battleServer.Port);
							BattleMgr.log.Error(ex.Message);
							battleServer.RetryCount = 0;
						}
					}
				}
			}
		}

		// Token: 0x06006FAF RID: 28591 RVA: 0x0024B560 File Offset: 0x00249760
		public static void ConnectTo(int id, string ip, int port, string key)
		{
			BattleServer battleServer = new BattleServer(id, ip, port, key);
			BattleMgr.AddBattleServer(battleServer);
			try
			{
				battleServer.Start();
			}
			catch (Exception ex)
			{
				BattleMgr.log.ErrorFormat("Batter server {0}:{1} can't connected!", battleServer.Ip, battleServer.Port);
				BattleMgr.log.Error(ex.Message);
			}
		}

		// Token: 0x06006FB0 RID: 28592 RVA: 0x0024B5D4 File Offset: 0x002497D4
		public static void Disconnet(int id)
		{
			BattleServer server = BattleMgr.GetServer(id);
			bool flag = server != null && server.IsActive;
			if (flag)
			{
				server.LastRetryTime = DateTime.Now;
				server.RetryCount = 4;
				server.Connector.Disconnect();
			}
		}

		// Token: 0x06006FB1 RID: 28593 RVA: 0x0024B61C File Offset: 0x0024981C
		public static void RemoveServer(BattleServer server)
		{
			bool flag = server != null;
			if (flag)
			{
				BattleMgr.m_list.Remove(server);
				server.Disconnected += BattleMgr.battle_Disconnected;
			}
		}

		// Token: 0x06006FB2 RID: 28594 RVA: 0x0024B654 File Offset: 0x00249854
		public static BattleServer GetServer(int id)
		{
			foreach (BattleServer battleServer in BattleMgr.m_list)
			{
				bool flag = battleServer.ServerId == id;
				if (flag)
				{
					return battleServer;
				}
			}
			return null;
		}

		// Token: 0x06006FB3 RID: 28595 RVA: 0x0024B6BC File Offset: 0x002498BC
		public static void Start()
		{
			foreach (BattleServer battleServer in BattleMgr.m_list)
			{
				try
				{
					battleServer.Start();
				}
				catch (Exception ex)
				{
					BattleMgr.log.ErrorFormat("Batter server {0}:{1} can't connected!", battleServer.Ip, battleServer.Port);
					BattleMgr.log.Error(ex.Message);
				}
			}
		}

		// Token: 0x06006FB4 RID: 28596 RVA: 0x0024B75C File Offset: 0x0024995C
		public static BattleServer FindActiveServer()
		{
			List<BattleServer> list = BattleMgr.m_list;
			lock (list)
			{
				foreach (BattleServer battleServer in BattleMgr.m_list)
				{
					bool isActive = battleServer.IsActive;
					if (isActive)
					{
						return battleServer;
					}
				}
			}
			return null;
		}

		// Token: 0x06006FB5 RID: 28597 RVA: 0x0024B7F4 File Offset: 0x002499F4
		public static BattleServer FindActiveServer(bool isCrosszone)
		{
			List<BattleServer> list = BattleMgr.m_list;
			lock (list)
			{
				foreach (BattleServer battleServer in BattleMgr.m_list)
				{
					bool flag2 = (isCrosszone && battleServer.ServerId == 2 && battleServer.IsActive) || (!isCrosszone && battleServer.IsActive);
					if (flag2)
					{
						return battleServer;
					}
				}
			}
			return null;
		}

		// Token: 0x06006FB6 RID: 28598 RVA: 0x0024B8A8 File Offset: 0x00249AA8
		public static BattleServer AddRoom(BaseRoom room)
		{
			BattleServer battleServer = BattleMgr.FindActiveServer(room.isCrosszone);
			bool flag = battleServer != null && battleServer.AddRoom(room);
			BattleServer battleServer2;
			if (flag)
			{
				battleServer2 = battleServer;
			}
			else
			{
				battleServer2 = null;
			}
			return battleServer2;
		}

		// Token: 0x06006FB7 RID: 28599 RVA: 0x0024B8E0 File Offset: 0x00249AE0
		public static BattleServer AddRoom1(BaseRoom room)
		{
			BattleServer battleServer = BattleMgr.FindActiveServer();
			bool flag = battleServer != null && battleServer.AddRoom(room);
			bool flag2 = flag;
			bool flag3 = flag2;
			BattleServer battleServer2;
			if (flag3)
			{
				battleServer2 = battleServer;
			}
			else
			{
				battleServer2 = null;
			}
			return battleServer2;
		}

		// Token: 0x06006FB8 RID: 28600 RVA: 0x0024B91C File Offset: 0x00249B1C
		public static BattleServer FindActiveServer1()
		{
			List<BattleServer> list = BattleMgr.m_list;
			List<BattleServer> list2 = list;
			List<BattleServer> list3 = list2;
			lock (list3)
			{
				foreach (BattleServer battleServer in BattleMgr.m_list)
				{
					bool isActive = battleServer.IsActive;
					bool flag2 = isActive;
					bool flag3 = flag2;
					if (flag3)
					{
						return battleServer;
					}
				}
			}
			return null;
		}

		// Token: 0x06006FB9 RID: 28601 RVA: 0x0024B9C4 File Offset: 0x00249BC4
		public static List<BattleServer> GetAllBattles()
		{
			return BattleMgr.m_list;
		}

		// Token: 0x04003C35 RID: 15413
		public static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04003C36 RID: 15414
		public static List<BattleServer> m_list = new List<BattleServer>();

		// Token: 0x04003C37 RID: 15415
		public static bool AutoReconnect = true;
	}
}
