﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CEB RID: 3307
	public class AE4858 : BasePetEffect
	{
		// Token: 0x060077EF RID: 30703 RVA: 0x0002C34C File Offset: 0x0002A54C
		public AE4858(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE4858, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_value = count;
			this.m_elementID = elementID;
		}

		// Token: 0x060077F0 RID: 30704 RVA: 0x00283480 File Offset: 0x00281680
		public override bool Start(Living living)
		{
			AE4858 ae = living.PetEffectList.GetOfType(ePetEffectType.AE4858) as AE4858;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060077F1 RID: 30705 RVA: 0x0002C385 File Offset: 0x0002A585
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKilledByLiving += this.player_AfterKilledByLiving;
		}

		// Token: 0x060077F2 RID: 30706 RVA: 0x0002C39B File Offset: 0x0002A59B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.player_AfterKilledByLiving;
		}

		// Token: 0x060077F3 RID: 30707 RVA: 0x002834E0 File Offset: 0x002816E0
		private void player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = living == target || living.Blood > 0 || this.IsTrigger;
			if (!flag)
			{
				living.Blood = 1;
				bool flag2 = living.KeepLifeCount < this.m_value;
				if (flag2)
				{
					bool flag3 = this.m_elementID == "4858";
					if (flag3)
					{
						int petMP = (living as Player).PetMP;
						(living as Player).RemovePetMP(petMP);
						int num = petMP / 2 * living.MaxBlood / 100;
						this.IsTrigger = true;
						living.EffectTrigger = true;
						bool isReduceHealingAmount = living.IsReduceHealingAmount;
						if (isReduceHealingAmount)
						{
							num -= (int)((double)num * living.PetReduceHealingAmountValue);
						}
						living.SyncAtTime = true;
						living.AddBlood(num);
						living.Game.SendGameUpdateHealth(living, 0, living.Blood);
						living.SyncAtTime = false;
						living.Game.SendEquipEffect(living, "触发<不灭冥魂>免疫本次致命伤害,扣除全部魔法值并恢复生命值");
						int keepLifeCount = living.KeepLifeCount;
						living.KeepLifeCount = keepLifeCount + 1;
						living.Game.sendShowPicSkil(living, base.ElementInfo, true);
					}
				}
				else
				{
					living.KeepLife = false;
					living.PetKeepLife = false;
					this.Stop();
				}
			}
		}

		// Token: 0x04004642 RID: 17986
		private int m_probability = 0;

		// Token: 0x04004643 RID: 17987
		private int m_value;

		// Token: 0x04004644 RID: 17988
		private string m_elementID;
	}
}
