﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D31 RID: 3377
	public class PetChangeSuit : BasePetEffect
	{
		// Token: 0x06007961 RID: 31073 RVA: 0x00289FC4 File Offset: 0x002881C4
		public PetChangeSuit(int count, int skillId, string elementID)
			: base(ePetEffectType.PetChangeSuit, elementID)
		{
			this.m_skillId = skillId;
			if (skillId <= 600)
			{
				if (skillId != 528)
				{
					if (skillId == 576 || skillId == 600)
					{
						this.suit = "13831|suits628";
						this.m_count = 3;
					}
				}
				else
				{
					this.suit = "13937|suits700";
					this.m_count = 3;
				}
			}
			else if (skillId <= 2357)
			{
				if (skillId != 2346)
				{
					if (skillId == 2357)
					{
						this.suit = "13933|suits729";
						this.m_count = 4;
						this.damageadd = 75;
						this.boold = 40;
						this.energyCost = 10;
					}
				}
				else
				{
					this.suit = "13933|suits729";
					this.m_count = 4;
					this.damageadd = 50;
					this.boold = 0;
					this.energyCost = 70;
				}
			}
			else if (skillId != 2362)
			{
				if (skillId == 2367)
				{
					this.suit = "13933|suits729";
					this.m_count = 4;
					this.damageadd = 100;
					this.boold = 80;
					this.energyCost = 10;
				}
			}
			else
			{
				this.suit = "13933|suits729";
				this.m_count = 4;
				this.damageadd = 75;
				this.boold = 60;
				this.energyCost = 10;
			}
		}

		// Token: 0x06007962 RID: 31074 RVA: 0x0028A134 File Offset: 0x00288334
		public override bool Start(Living living)
		{
			PetChangeSuit petChangeSuit = living.PetEffectList.GetOfType(ePetEffectType.PetChangeSuit) as PetChangeSuit;
			bool flag = petChangeSuit != null;
			bool flag2;
			if (flag)
			{
				petChangeSuit.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007963 RID: 31075 RVA: 0x0002D8A1 File Offset: 0x0002BAA1
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
			player.OnMakeDamageEvent += this.OnMakeDamage;
		}

		// Token: 0x06007964 RID: 31076 RVA: 0x0002D8CA File Offset: 0x0002BACA
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
			player.OnMakeDamageEvent -= this.OnMakeDamage;
		}

		// Token: 0x06007965 RID: 31077 RVA: 0x0028A17C File Offset: 0x0028837C
		private void OnMakeDamage(Living living, Living source, ref int damageamount, ref int criticalamount)
		{
			bool flag = source.BlackTiger && living.Game.RoomType != eRoomType.Element_PVE;
			if (flag)
			{
				living.MaxBlood += living.MaxBlood * this.boold / 100;
				damageamount += damageamount * this.damageadd / 100;
			}
		}

		// Token: 0x06007966 RID: 31078 RVA: 0x0028A1DC File Offset: 0x002883DC
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_skillId;
			if (flag)
			{
				bool flag2 = this.m_skillId == 2346;
				if (flag2)
				{
					living.BlackTiger = true;
				}
				bool flag3 = this.m_skillId == 2357 || this.m_skillId == 2362 || this.m_skillId == 2367;
				if (flag3)
				{
					living.BlackTiger = true;
				}
				bool flag4 = this.m_skillId == 528;
				if (flag4)
				{
					living.Blood = 1;
					living.Game.UpdateBlood(living, 1, 1, 0);
					living.Devil = true;
					living.KeepLife = true;
				}
				bool flag5 = living.PetEffects.CurrentUseSkill == 600 && living.Game.IsPVE();
				if (flag5)
				{
					living.Game.SetWind(5);
					living.Game.UpdateWind2((float)(5 * living.m_direction), true);
				}
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				living.AddPetEffect(new PetChangeSuitEquip(this.m_count, this.suit, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x0400471B RID: 18203
		private int m_count;

		// Token: 0x0400471C RID: 18204
		private int m_skillId;

		// Token: 0x0400471D RID: 18205
		private string suit;

		// Token: 0x0400471E RID: 18206
		private int damageadd;

		// Token: 0x0400471F RID: 18207
		private int energyCost;

		// Token: 0x04004720 RID: 18208
		private int boold;
	}
}
