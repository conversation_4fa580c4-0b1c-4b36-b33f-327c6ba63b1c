﻿using System;
using System.Collections.Generic;
using System.Reflection;
using Game.Base.Events;
using Game.Logic.Phy.Object;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic.Spells
{
	// Token: 0x02000CB3 RID: 3251
	public class SpellMgr
	{
		// Token: 0x060074E3 RID: 29923 RVA: 0x0026DCFC File Offset: 0x0026BEFC
		public static ISpellHandler LoadSpellHandler(int code)
		{
			return SpellMgr.handles[code];
		}

		// Token: 0x060074E4 RID: 29924 RVA: 0x0026DD1C File Offset: 0x0026BF1C
		[ScriptLoadedEvent]
		public static void OnScriptCompiled(RoadEvent ev, object sender, EventArgs args)
		{
			SpellMgr.handles.Clear();
			int num = SpellMgr.SearchSpellHandlers(Assembly.GetAssembly(typeof(BaseGame)));
			bool isInfoEnabled = SpellMgr.log.IsInfoEnabled;
			if (isInfoEnabled)
			{
				SpellMgr.log.Info("SpellMgr: Loaded " + num.ToString() + " spell handlers from GameServer Assembly!");
			}
		}

		// Token: 0x060074E5 RID: 29925 RVA: 0x0026DD7C File Offset: 0x0026BF7C
		protected static int SearchSpellHandlers(Assembly assembly)
		{
			int num = 0;
			Type[] types = assembly.GetTypes();
			Type[] array = types;
			Type[] array2 = array;
			foreach (Type type in array2)
			{
				bool flag = type.IsClass && !(type.GetInterface("Game.Logic.Spells.ISpellHandler") == null);
				if (flag)
				{
					SpellAttibute[] array4 = (SpellAttibute[])type.GetCustomAttributes(typeof(SpellAttibute), true);
					bool flag2 = array4.Length != 0;
					if (flag2)
					{
						num++;
						SpellMgr.RegisterSpellHandler(array4[0].Type, Activator.CreateInstance(type) as ISpellHandler);
					}
				}
			}
			return num;
		}

		// Token: 0x060074E6 RID: 29926 RVA: 0x0002B56B File Offset: 0x0002976B
		protected static void RegisterSpellHandler(int type, ISpellHandler handle)
		{
			SpellMgr.handles.Add(type, handle);
		}

		// Token: 0x060074E7 RID: 29927 RVA: 0x0026DE30 File Offset: 0x0026C030
		public static void ExecuteSpell(BaseGame game, Player player, ItemTemplateInfo item)
		{
			try
			{
				ISpellHandler spellHandler = SpellMgr.LoadSpellHandler(item.Property1);
				spellHandler.Execute(game, player, item);
			}
			catch (Exception ex)
			{
				SpellMgr.log.Error("Execute Spell Error:", ex);
			}
		}

		// Token: 0x0400447B RID: 17531
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x0400447C RID: 17532
		private static Dictionary<int, ISpellHandler> handles = new Dictionary<int, ISpellHandler>();
	}
}
