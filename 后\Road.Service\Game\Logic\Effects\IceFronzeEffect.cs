﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EEB RID: 3819
	public class IceFronzeEffect : AbstractEffect
	{
		// Token: 0x06008332 RID: 33586 RVA: 0x0003416E File Offset: 0x0003236E
		public IceFronzeEffect(int count)
			: base(eEffectType.IceFronzeEffect)
		{
			this.m_count = count;
		}

		// Token: 0x06008333 RID: 33587 RVA: 0x002B320C File Offset: 0x002B140C
		public override bool Start(Living living)
		{
			IceFronzeEffect iceFronzeEffect = living.EffectList.GetOfType(eEffectType.IceFronzeEffect) as IceFronzeEffect;
			bool flag = iceFronzeEffect != null;
			bool flag2;
			if (flag)
			{
				iceFronzeEffect.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008334 RID: 33588 RVA: 0x002B3254 File Offset: 0x002B1454
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginFitting;
			bool flag = !living.SyncAtTime;
			if (flag)
			{
				living.SyncAtTime = true;
				living.IsFrost = true;
				living.SyncAtTime = false;
			}
			else
			{
				living.IsFrost = true;
			}
		}

		// Token: 0x06008335 RID: 33589 RVA: 0x002B32A8 File Offset: 0x002B14A8
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginFitting;
			bool flag = !living.SyncAtTime;
			if (flag)
			{
				living.SyncAtTime = true;
				living.IsFrost = false;
				living.SyncAtTime = false;
			}
			else
			{
				living.IsFrost = false;
			}
		}

		// Token: 0x06008336 RID: 33590 RVA: 0x002B32FC File Offset: 0x002B14FC
		private void player_BeginFitting(Living player)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x04005204 RID: 20996
		private int m_count;
	}
}
