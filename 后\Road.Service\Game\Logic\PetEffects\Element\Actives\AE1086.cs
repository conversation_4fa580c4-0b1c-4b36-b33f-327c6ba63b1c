﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DBB RID: 3515
	public class AE1086 : BasePetEffect
	{
		// Token: 0x06007C48 RID: 31816 RVA: 0x00296B60 File Offset: 0x00294D60
		public AE1086(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1086, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C49 RID: 31817 RVA: 0x00296BE0 File Offset: 0x00294DE0
		public override bool Start(Living living)
		{
			AE1086 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1086) as AE1086;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C4A RID: 31818 RVA: 0x0002F96C File Offset: 0x0002DB6C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C4B RID: 31819 RVA: 0x00296C40 File Offset: 0x00294E40
		protected override void OnPausedOnPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Lucky -= (double)this.m_added;
			this.IsTrigger = false;
		}

		// Token: 0x06007C4C RID: 31820 RVA: 0x00296C90 File Offset: 0x00294E90
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && !this.IsTrigger;
			if (flag)
			{
				this.IsTrigger = true;
				this.m_added = 800;
				player.Lucky += (double)this.m_added;
				player.Game.SendPetBuff(player, base.ElementInfo, true);
			}
		}

		// Token: 0x04004A57 RID: 19031
		private int m_type = 0;

		// Token: 0x04004A58 RID: 19032
		private int m_count = 0;

		// Token: 0x04004A59 RID: 19033
		private int m_probability = 0;

		// Token: 0x04004A5A RID: 19034
		private int m_delay = 0;

		// Token: 0x04004A5B RID: 19035
		private int m_coldDown = 0;

		// Token: 0x04004A5C RID: 19036
		private int m_currentId;

		// Token: 0x04004A5D RID: 19037
		private int m_added = 0;
	}
}
