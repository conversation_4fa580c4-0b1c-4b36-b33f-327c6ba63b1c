﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E79 RID: 3705
	public class CE1156 : BasePetEffect
	{
		// Token: 0x06008067 RID: 32871 RVA: 0x002A8FBC File Offset: 0x002A71BC
		public CE1156(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1156, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008068 RID: 32872 RVA: 0x002A903C File Offset: 0x002A723C
		public override bool Start(Living living)
		{
			CE1156 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1156) as CE1156;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008069 RID: 32873 RVA: 0x002A909C File Offset: 0x002A729C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 120;
				bool flag2 = player.BaseGuard < (double)this.m_added;
				if (flag2)
				{
					this.m_added = (int)(player.BaseGuard - 1.0);
				}
				player.BaseGuard -= (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600806A RID: 32874 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600806B RID: 32875 RVA: 0x002A9128 File Offset: 0x002A7328
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600806C RID: 32876 RVA: 0x000321B1 File Offset: 0x000303B1
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BaseGuard += (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F88 RID: 20360
		private int m_type = 0;

		// Token: 0x04004F89 RID: 20361
		private int m_count = 0;

		// Token: 0x04004F8A RID: 20362
		private int m_probability = 0;

		// Token: 0x04004F8B RID: 20363
		private int m_delay = 0;

		// Token: 0x04004F8C RID: 20364
		private int m_coldDown = 0;

		// Token: 0x04004F8D RID: 20365
		private int m_currentId;

		// Token: 0x04004F8E RID: 20366
		private int m_added = 0;
	}
}
