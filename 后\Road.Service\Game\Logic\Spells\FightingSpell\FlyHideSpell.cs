﻿using System;
using Game.Logic.Effects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CC6 RID: 3270
	[SpellAttibute(80)]
	public class FlyHideSpell : ISpellHandler
	{
		// Token: 0x0600750E RID: 29966 RVA: 0x0026E850 File Offset: 0x0026CA50
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				new HideEffect(item.Property3).Start(player);
				player.SetBall(3);
			}
			else
			{
				bool flag = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
				if (flag)
				{
					new HideEffect(item.Property3).Start(game.CurrentLiving);
					(game.CurrentLiving as Player).SetBall(3);
				}
			}
		}
	}
}
