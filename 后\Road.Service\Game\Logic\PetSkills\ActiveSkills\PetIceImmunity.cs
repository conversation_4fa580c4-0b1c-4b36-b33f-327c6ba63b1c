﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D3C RID: 3388
	public class PetIceImmunity : BasePetEffect
	{
		// Token: 0x0600799E RID: 31134 RVA: 0x0002DC47 File Offset: 0x0002BE47
		public PetIceImmunity(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetIceImmunity, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_skillId = skillId;
		}

		// Token: 0x0600799F RID: 31135 RVA: 0x0028BB7C File Offset: 0x00289D7C
		public override bool Start(Living living)
		{
			PetIceImmunity petIceImmunity = living.PetEffectList.GetOfType(ePetEffectType.PetIceImmunity) as PetIceImmunity;
			bool flag = petIceImmunity != null;
			bool flag2;
			if (flag)
			{
				petIceImmunity.m_probability = ((this.m_probability > petIceImmunity.m_probability) ? this.m_probability : petIceImmunity.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079A0 RID: 31136 RVA: 0x0002DC78 File Offset: 0x0002BE78
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079A1 RID: 31137 RVA: 0x0002DC8E File Offset: 0x0002BE8E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079A2 RID: 31138 RVA: 0x0028BBDC File Offset: 0x00289DDC
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_skillId;
			if (flag)
			{
				for (int i = 0; i < this.m_count; i++)
				{
					living.Game.sendShowPicSkil(living, base.Info, true);
				}
				living.AddPetEffect(new PetIceImmunityEquip(this.m_count, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004743 RID: 18243
		private int m_count;

		// Token: 0x04004744 RID: 18244
		private int m_probability;

		// Token: 0x04004745 RID: 18245
		private int m_skillId;
	}
}
