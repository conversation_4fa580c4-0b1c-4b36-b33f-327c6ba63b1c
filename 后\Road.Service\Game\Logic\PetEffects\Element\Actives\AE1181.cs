﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DEC RID: 3564
	public class AE1181 : BasePetEffect
	{
		// Token: 0x06007D4B RID: 32075 RVA: 0x0029B3A0 File Offset: 0x002995A0
		public AE1181(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1181, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D4C RID: 32076 RVA: 0x0029B420 File Offset: 0x00299620
		public override bool Start(Living living)
		{
			AE1181 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1181) as AE1181;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D4D RID: 32077 RVA: 0x000303BE File Offset: 0x0002E5BE
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D4E RID: 32078 RVA: 0x000303D4 File Offset: 0x0002E5D4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D4F RID: 32079 RVA: 0x0029B480 File Offset: 0x00299680
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					int num = 2;
					CE1181 ce = player2.PetEffectList.GetOfType(ePetEffectType.CE1181) as CE1181;
					CE1182 ce2 = player2.PetEffectList.GetOfType(ePetEffectType.CE1182) as CE1182;
					CE1183 ce3 = player2.PetEffectList.GetOfType(ePetEffectType.CE1183) as CE1183;
					bool flag2 = ce != null;
					if (flag2)
					{
						num = ce.Count;
						ce.Stop();
					}
					bool flag3 = ce2 != null;
					if (flag3)
					{
						num = ce2.Count;
						ce2.Stop();
					}
					bool flag4 = ce3 != null;
					if (flag4)
					{
						num = ce3.Count;
						ce3.Stop();
					}
					player2.AddPetEffect(new CE1181(num, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004BAC RID: 19372
		private int m_type = 0;

		// Token: 0x04004BAD RID: 19373
		private int m_count = 0;

		// Token: 0x04004BAE RID: 19374
		private int m_probability = 0;

		// Token: 0x04004BAF RID: 19375
		private int m_delay = 0;

		// Token: 0x04004BB0 RID: 19376
		private int m_coldDown = 0;

		// Token: 0x04004BB1 RID: 19377
		private int m_currentId;

		// Token: 0x04004BB2 RID: 19378
		private int m_added = 0;
	}
}
