﻿using System;
using System.Reflection;
using Game.Base.Packets;
using Game.Server.ActiveSystem.Handle;
using Game.Server.GameObjects;
using Game.Server.Packets;
using log4net;

namespace Game.Server.ActiveSystem
{
	// Token: 0x02000C47 RID: 3143
	[ActiveSystemProcessorAtribute(255, "礼堂逻辑")]
	public class ActiveSystemLogicProcessor : AbstractActiveSystemProcessor
	{
		// Token: 0x0600700A RID: 28682 RVA: 0x0002A25C File Offset: 0x0002845C
		public ActiveSystemLogicProcessor()
		{
			this._commandMgr = new ActiveSystemHandleMgr();
		}

		// Token: 0x0600700B RID: 28683 RVA: 0x0024D24C File Offset: 0x0024B44C
		public override void OnGameData(GamePlayer player, GSPacketIn packet)
		{
			ActiveSystemPackageType activeSystemPackageType = (ActiveSystemPackageType)packet.ReadByte();
			try
			{
				IActiveSystemCommandHadler activeSystemCommandHadler = this._commandMgr.LoadCommandHandler((int)activeSystemPackageType);
				bool flag = activeSystemCommandHadler != null;
				if (flag)
				{
					activeSystemCommandHadler.CommandHandler(player, packet);
				}
				else
				{
					Console.WriteLine("______________ERROR______________");
					Console.WriteLine("ActiveSystemPackageType: {0} not found!", activeSystemPackageType);
					Console.WriteLine("_______________END_______________");
				}
			}
			catch (Exception ex)
			{
				ActiveSystemLogicProcessor.log.Error(string.Format("ActiveSystemPackageType: {1}, OnGameData is Error: {0}", ex, activeSystemPackageType));
			}
		}

		// Token: 0x04003C45 RID: 15429
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04003C46 RID: 15430
		private ActiveSystemHandleMgr _commandMgr;
	}
}
