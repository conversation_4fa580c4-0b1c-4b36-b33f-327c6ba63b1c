﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CF5 RID: 3317
	public class PetGetshield_Passive : BasePetEffect
	{
		// Token: 0x06007823 RID: 30755 RVA: 0x00284308 File Offset: 0x00282508
		public PetGetshield_Passive(int count, string elementID)
			: base(ePetEffectType.PetGetshield_Passive, elementID)
		{
			this.m_count = count;
			bool flag = !(elementID == "3219");
			if (flag)
			{
				bool flag2 = elementID == "3230";
				if (flag2)
				{
					bool flag3 = elementID == "70009";
					if (flag3)
					{
						this.m_added = 50;
						this.m_cd = 8;
					}
				}
				else
				{
					this.m_added = 30;
					this.m_cd = 12;
				}
			}
			else
			{
				this.m_added = 20;
				this.m_cd = 5;
			}
		}

		// Token: 0x06007824 RID: 30756 RVA: 0x002843BC File Offset: 0x002825BC
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetGetshield_Passive) is PetGetshield_Passive;
			return flag || base.Start(living);
		}

		// Token: 0x06007825 RID: 30757 RVA: 0x0002C6B3 File Offset: 0x0002A8B3
		protected override void OnAttachedToPlayer(Player player)
		{
			player.GameStarted += this.player_GameStarted;
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x06007826 RID: 30758 RVA: 0x0002C6DC File Offset: 0x0002A8DC
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.GameStarted -= this.player_GameStarted;
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x06007827 RID: 30759 RVA: 0x002843F8 File Offset: 0x002825F8
		private void player_BeginSelfTurn(Living living)
		{
			this.m_coldDown++;
			bool flag = this.m_coldDown >= this.m_cd;
			if (flag)
			{
				this.m_coldDown = 0;
				living.Game.sendShowPicSkil(living, base.Info, true);
				new PetGetShieldEquip(this.m_count, this.m_value, 0, base.Info.ID.ToString()).Start(living);
			}
		}

		// Token: 0x06007828 RID: 30760 RVA: 0x00284474 File Offset: 0x00282674
		private void player_GameStarted(Living living)
		{
			bool flag = this.m_value == 0;
			if (flag)
			{
				this.m_value = living.MaxBlood * this.m_added / 100;
				living.Game.sendShowPicSkil(living, base.Info, true);
				new PetGetShieldEquip(this.m_count, this.m_value, 0, base.Info.ID.ToString()).Start(living);
			}
		}

		// Token: 0x0400466B RID: 18027
		private int m_count = 0;

		// Token: 0x0400466C RID: 18028
		private int m_added = 0;

		// Token: 0x0400466D RID: 18029
		private int m_value = 0;

		// Token: 0x0400466E RID: 18030
		private int m_coldDown = 0;

		// Token: 0x0400466F RID: 18031
		private int m_cd = 0;
	}
}
