﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{4896D65B-2F8C-497C-A898-6C19AB0B2FB4}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>System.Data.Entity.SqlServer</RootNamespace>
    <AssemblyName>EntityFramework.SqlServer</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoStdLib>true</NoStdLib>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoStdLib>true</NoStdLib>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DbGeographyAdapter.cs" />
    <Compile Include="DbGeometryAdapter.cs" />
    <Compile Include="DefaultSqlExecutionStrategy.cs" />
    <Compile Include="Expressions.cs" />
    <Compile Include="IDbSpatialValue.cs" />
    <Compile Include="IDbSpatialValueExtensionMethods.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Resources\EntityRes.cs" />
    <Compile Include="Resources\Error.cs" />
    <Compile Include="Resources\Strings.cs" />
    <Compile Include="ServerType.cs" />
    <Compile Include="SqlAzureExecutionStrategy.cs" />
    <Compile Include="SqlAzureRetriableExceptionDetector.cs" />
    <Compile Include="SqlDdlBuilder.cs" />
    <Compile Include="SqlFunctions.cs" />
    <Compile Include="SqlGen\BoolWrapper.cs" />
    <Compile Include="SqlGen\DmlFunctionSqlGenerator.cs" />
    <Compile Include="SqlGen\DmlSqlGenerator.cs" />
    <Compile Include="SqlGen\ISqlFragment.cs" />
    <Compile Include="SqlGen\JoinSymbol.cs" />
    <Compile Include="SqlGen\OptionalColumn.cs" />
    <Compile Include="SqlGen\SkipClause.cs" />
    <Compile Include="SqlGen\Sql8ConformanceChecker.cs" />
    <Compile Include="SqlGen\Sql8ExpressionRewriter.cs" />
    <Compile Include="SqlGen\SqlBuilder.cs" />
    <Compile Include="SqlGen\SqlFunctionCallHandler.cs" />
    <Compile Include="SqlGen\SqlGenerator.cs" />
    <Compile Include="SqlGen\SqlSelectClauseBuilder.cs" />
    <Compile Include="SqlGen\SqlSelectStatement.cs" />
    <Compile Include="SqlGen\SqlStringBuilder.cs" />
    <Compile Include="SqlGen\SqlWriter.cs" />
    <Compile Include="SqlGen\Symbol.cs" />
    <Compile Include="SqlGen\SymbolPair.cs" />
    <Compile Include="SqlGen\SymbolTable.cs" />
    <Compile Include="SqlGen\SymbolUsageManager.cs" />
    <Compile Include="SqlGen\TopClause.cs" />
    <Compile Include="SqlHierarchyIdFunctions.cs" />
    <Compile Include="SqlProviderManifest.cs" />
    <Compile Include="SqlProviderServices.cs" />
    <Compile Include="SqlProviderUtilities.cs" />
    <Compile Include="SqlServerMigrationSqlGenerator.cs" />
    <Compile Include="SqlSpatialDataReader.cs" />
    <Compile Include="SqlSpatialFunctions.cs" />
    <Compile Include="SqlSpatialServices.cs" />
    <Compile Include="SqlTableExistenceChecker.cs" />
    <Compile Include="SqlTypesAssembly.cs" />
    <Compile Include="SqlTypesAssemblyLoader.cs" />
    <Compile Include="SqlVersion.cs" />
    <Compile Include="SqlVersionUtils.cs" />
    <Compile Include="Utilities\ByteExtensions.cs" />
    <Compile Include="Utilities\Check.cs" />
    <Compile Include="Utilities\DatabaseName.cs" />
    <Compile Include="Utilities\DbExpressionExtensions.cs" />
    <Compile Include="Utilities\DebugCheck.cs" />
    <Compile Include="Utilities\EdmFunctionExtensions.cs" />
    <Compile Include="Utilities\FuncExtensions.cs" />
    <Compile Include="Utilities\IDictionaryExtensions.cs" />
    <Compile Include="Utilities\IEnumerableExtensions.cs" />
    <Compile Include="Utilities\MetdataItemExtensions.cs" />
    <Compile Include="Utilities\PrimitiveTypeExtensions.cs" />
    <Compile Include="Utilities\PropertyInfoExtensions.cs" />
    <Compile Include="Utilities\SqlDataReaderWrapper.cs" />
    <Compile Include="Utilities\StringExtensions.cs" />
    <Compile Include="Utilities\TaskExtensions.cs" />
    <Compile Include="Utilities\TypeExtensions.cs" />
    <Compile Include="Utilities\TypeUsageExtensions.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources\SqlServer.resources" />
    <EmbeddedResource Include="System.Data.Resources.SqlClient.SqlProviderServices.Provider.xml" />
    <EmbeddedResource Include="System.Data.Resources.SqlClient.SqlProviderServices.StoreSch.2.msl" />
    <EmbeddedResource Include="System.Data.Resources.SqlClient.SqlProviderServices.StoreSch.2.ssdl" />
    <EmbeddedResource Include="System.Data.Resources.SqlClient.SqlProviderServices.StoreSch.3.ssdl" />
    <EmbeddedResource Include="System.Data.Resources.SqlClient.SqlProviderServices.StoreSch.4.ssdl" />
    <EmbeddedResource Include="System.Data.Resources.SqlClient.SqlProviderServices.StoreSch.msl" />
    <EmbeddedResource Include="System.Data.Resources.SqlClient.SqlProviderServices.StoreSch.ssdl" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\EntityFramework\EntityFramework.csproj">
      <Project>{4896D65B-2F8C-497C-A898-6C19AB0B2FB3}</Project>
      <Name>EntityFramework</Name>
    </ProjectReference>
    <ProjectReference Include="..\mscorlib\mscorlib.csproj">
      <Project>{4896D65B-2F8C-497C-A898-6C19AB0B2FB7}</Project>
      <Name>mscorlib</Name>
    </ProjectReference>
    <ProjectReference Include="..\System\System.csproj">
      <Project>{4896D65B-2F8C-497C-A898-6C19AB0B2FBB}</Project>
      <Name>System</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>