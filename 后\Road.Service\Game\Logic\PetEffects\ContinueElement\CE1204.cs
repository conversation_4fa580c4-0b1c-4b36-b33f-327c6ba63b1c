﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E97 RID: 3735
	public class CE1204 : BasePetEffect
	{
		// Token: 0x06008127 RID: 33063 RVA: 0x002ABDE4 File Offset: 0x002A9FE4
		public CE1204(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1204, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008128 RID: 33064 RVA: 0x002ABE64 File Offset: 0x002AA064
		public override bool Start(Living living)
		{
			CE1204 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1204) as CE1204;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008129 RID: 33065 RVA: 0x0003288A File Offset: 0x00030A8A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600812A RID: 33066 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600812B RID: 33067 RVA: 0x002ABEC4 File Offset: 0x002AA0C4
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				bool flag2 = living.Attack < (double)this.m_added;
				if (flag2)
				{
					this.m_added = (int)living.Attack - 1;
				}
				living.Attack -= (double)this.m_added;
			}
		}

		// Token: 0x0600812C RID: 33068 RVA: 0x002ABF24 File Offset: 0x002AA124
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600812D RID: 33069 RVA: 0x002ABF58 File Offset: 0x002AA158
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Attack += (double)this.m_added;
			this.m_added = 0;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x0400505E RID: 20574
		private int m_type = 0;

		// Token: 0x0400505F RID: 20575
		private int m_count = 0;

		// Token: 0x04005060 RID: 20576
		private int m_probability = 0;

		// Token: 0x04005061 RID: 20577
		private int m_delay = 0;

		// Token: 0x04005062 RID: 20578
		private int m_coldDown = 0;

		// Token: 0x04005063 RID: 20579
		private int m_currentId;

		// Token: 0x04005064 RID: 20580
		private int m_added = 0;
	}
}
