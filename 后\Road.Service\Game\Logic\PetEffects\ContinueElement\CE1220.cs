﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EA1 RID: 3745
	public class CE1220 : BasePetEffect
	{
		// Token: 0x06008169 RID: 33129 RVA: 0x002ACC4C File Offset: 0x002AAE4C
		public CE1220(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1220, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600816A RID: 33130 RVA: 0x002ACCCC File Offset: 0x002AAECC
		public override bool Start(Living living)
		{
			CE1220 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1220) as CE1220;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600816B RID: 33131 RVA: 0x002ACD2C File Offset: 0x002AAF2C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.BaseGuard += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600816C RID: 33132 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600816D RID: 33133 RVA: 0x002ACD8C File Offset: 0x002AAF8C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600816E RID: 33134 RVA: 0x002ACDC0 File Offset: 0x002AAFC0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BaseGuard -= (double)this.m_added;
			this.m_added = 0;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x040050A4 RID: 20644
		private int m_type = 0;

		// Token: 0x040050A5 RID: 20645
		private int m_count = 0;

		// Token: 0x040050A6 RID: 20646
		private int m_probability = 0;

		// Token: 0x040050A7 RID: 20647
		private int m_delay = 0;

		// Token: 0x040050A8 RID: 20648
		private int m_coldDown = 0;

		// Token: 0x040050A9 RID: 20649
		private int m_currentId;

		// Token: 0x040050AA RID: 20650
		private int m_added = 0;
	}
}
