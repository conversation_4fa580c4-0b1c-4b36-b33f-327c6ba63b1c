﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F38 RID: 3896
	public class TimeVortex5Effect : BaseCardEffect
	{
		// Token: 0x170014AB RID: 5291
		// (get) Token: 0x0600847E RID: 33918 RVA: 0x00034F60 File Offset: 0x00033160
		public int CureValue
		{
			get
			{
				return this.m_added;
			}
		}

		// Token: 0x0600847F RID: 33919 RVA: 0x002B8664 File Offset: 0x002B6864
		public TimeVortex5Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.TimeVortex5, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008480 RID: 33920 RVA: 0x002B86D4 File Offset: 0x002B68D4
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.TimeVortex5) is TimeVortex5Effect;
			return flag || base.Start(living);
		}

		// Token: 0x06008481 RID: 33921 RVA: 0x00034F68 File Offset: 0x00033168
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerCure += this.ChangeProperty;
		}

		// Token: 0x06008482 RID: 33922 RVA: 0x00034F7E File Offset: 0x0003317E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerCure -= this.ChangeProperty;
		}

		// Token: 0x06008483 RID: 33923 RVA: 0x002B870C File Offset: 0x002B690C
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 12;
			if (flag2)
			{
				this.m_added = this.m_value;
			}
		}

		// Token: 0x0400529C RID: 21148
		private int m_indexValue = 0;

		// Token: 0x0400529D RID: 21149
		private int m_value = 0;

		// Token: 0x0400529E RID: 21150
		private int m_added = 0;
	}
}
