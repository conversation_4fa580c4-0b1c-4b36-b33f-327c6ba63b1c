﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EE7 RID: 3815
	public class ContinueReduceGreenBloodEffect : AbstractEffect
	{
		// Token: 0x06008313 RID: 33555 RVA: 0x00033FAB File Offset: 0x000321AB
		public ContinueReduceGreenBloodEffect(int count, int blood, Living liv)
			: base(eEffectType.ContinueReduceGreenBloodEffect)
		{
			this.m_count = count;
			this.m_blood = blood;
			this.m_liv = liv;
		}

		// Token: 0x06008314 RID: 33556 RVA: 0x002B2AB8 File Offset: 0x002B0CB8
		public override bool Start(Living living)
		{
			ContinueReduceGreenBloodEffect continueReduceGreenBloodEffect = living.EffectList.GetOfType(eEffectType.ContinueReduceGreenBloodEffect) as ContinueReduceGreenBloodEffect;
			bool flag = continueReduceGreenBloodEffect != null;
			bool flag2;
			if (flag)
			{
				continueReduceGreenBloodEffect.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008315 RID: 33557 RVA: 0x00033FCC File Offset: 0x000321CC
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginFitting;
			living.Game.SendPlayerPicture(living, 28, true);
		}

		// Token: 0x06008316 RID: 33558 RVA: 0x00033FF2 File Offset: 0x000321F2
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginFitting;
			living.Game.SendPlayerPicture(living, 28, false);
		}

		// Token: 0x06008317 RID: 33559 RVA: 0x002B2B00 File Offset: 0x002B0D00
		private void player_BeginFitting(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				living.AddBlood(-this.m_blood, 1);
				bool flag2 = living.Blood > 0;
				if (!flag2)
				{
					living.Die();
					bool flag3 = this.m_liv != null && this.m_liv is Player;
					if (flag3)
					{
						int num = 2;
						bool flag4 = living is Player;
						if (flag4)
						{
							num = 1;
						}
						(this.m_liv as Player).PlayerDetail.OnKillingLiving(this.m_liv.Game, num, living.Id, living.IsLiving, this.m_blood);
					}
				}
			}
		}

		// Token: 0x040051F9 RID: 20985
		private int m_count;

		// Token: 0x040051FA RID: 20986
		private int m_blood;

		// Token: 0x040051FB RID: 20987
		private Living m_liv;
	}
}
