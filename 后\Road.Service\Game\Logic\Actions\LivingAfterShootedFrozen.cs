﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F4E RID: 3918
	public class LivingAfterShootedFrozen : BaseAction
	{
		// Token: 0x060084F3 RID: 34035 RVA: 0x000352A7 File Offset: 0x000334A7
		public LivingAfterShootedFrozen(Living living, int delay)
			: base(delay, 0)
		{
			this.m_liv = living;
		}

		// Token: 0x060084F4 RID: 34036 RVA: 0x000352BA File Offset: 0x000334BA
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			this.m_liv.OnAfterTakedFrozen();
			base.Finish(tick);
		}

		// Token: 0x040052D6 RID: 21206
		private Living m_liv;
	}
}
