﻿using System;
using Game.Logic;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C63 RID: 3171
	public class FightCompleteCondition : BaseCondition
	{
		// Token: 0x0600707C RID: 28796 RVA: 0x0002A421 File Offset: 0x00028621
		public FightCompleteCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x0600707D RID: 28797 RVA: 0x0002A498 File Offset: 0x00028698
		public override void AddTrigger(GamePlayer player)
		{
			player.GameOver += this.player_PlayerGameOverEventHandle;
		}

		// Token: 0x0600707E RID: 28798 RVA: 0x0025008C File Offset: 0x0024E28C
		private void player_PlayerGameOverEventHandle(AbstractGame game, bool isWin, int gainXp, bool isSpanArea)
		{
			bool flag = game.RoomType == eRoomType.Match;
			if (flag)
			{
				int value = base.Value;
				base.Value = value + 1;
			}
		}

		// Token: 0x0600707F RID: 28799 RVA: 0x0002A4AE File Offset: 0x000286AE
		public override void RemoveTrigger(GamePlayer player)
		{
			player.GameOver -= this.player_PlayerGameOverEventHandle;
		}

		// Token: 0x06007080 RID: 28800 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
