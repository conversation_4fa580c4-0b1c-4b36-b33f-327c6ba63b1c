﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EAC RID: 3756
	public class CE1243 : BasePetEffect
	{
		// Token: 0x060081AE RID: 33198 RVA: 0x002ADDDC File Offset: 0x002ABFDC
		public CE1243(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1243, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081AF RID: 33199 RVA: 0x002ADE5C File Offset: 0x002AC05C
		public override bool Start(Living living)
		{
			CE1243 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1243) as CE1243;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081B0 RID: 33200 RVA: 0x00032D93 File Offset: 0x00030F93
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081B1 RID: 33201 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081B2 RID: 33202 RVA: 0x002ADEBC File Offset: 0x002AC0BC
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			this.m_added = 1500;
			bool isLiving = living.IsLiving;
			if (isLiving)
			{
				living.SyncAtTime = true;
				living.AddBlood(this.m_added);
				living.SyncAtTime = false;
			}
		}

		// Token: 0x060081B3 RID: 33203 RVA: 0x002ADF00 File Offset: 0x002AC100
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.m_added = 0;
				this.Stop();
			}
		}

		// Token: 0x060081B4 RID: 33204 RVA: 0x00032DCF File Offset: 0x00030FCF
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x040050F1 RID: 20721
		private int m_type = 0;

		// Token: 0x040050F2 RID: 20722
		private int m_count = 0;

		// Token: 0x040050F3 RID: 20723
		private int m_probability = 0;

		// Token: 0x040050F4 RID: 20724
		private int m_delay = 0;

		// Token: 0x040050F5 RID: 20725
		private int m_coldDown = 0;

		// Token: 0x040050F6 RID: 20726
		private int m_currentId;

		// Token: 0x040050F7 RID: 20727
		private int m_added = 0;
	}
}
