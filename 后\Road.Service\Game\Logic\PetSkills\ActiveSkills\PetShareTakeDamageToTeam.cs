﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D48 RID: 3400
	public class PetShareTakeDamageToTeam : BasePetEffect
	{
		// Token: 0x060079E0 RID: 31200 RVA: 0x0002E0B1 File Offset: 0x0002C2B1
		public PetShareTakeDamageToTeam(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetShareTakeDamageToTeam, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_skillId = skillId;
		}

		// Token: 0x060079E1 RID: 31201 RVA: 0x0028CCF0 File Offset: 0x0028AEF0
		public override bool Start(Living living)
		{
			PetShareTakeDamageToTeam petShareTakeDamageToTeam = living.PetEffectList.GetOfType(ePetEffectType.PetShareTakeDamageToTeam) as PetShareTakeDamageToTeam;
			bool flag = petShareTakeDamageToTeam != null;
			bool flag2;
			if (flag)
			{
				petShareTakeDamageToTeam.m_probability = ((this.m_probability > petShareTakeDamageToTeam.m_probability) ? this.m_probability : petShareTakeDamageToTeam.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079E2 RID: 31202 RVA: 0x0028CD50 File Offset: 0x0028AF50
		private void petUseSkill(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_skillId || player.Game.GetAllTeamPlayers(player).Count <= 1;
			if (!flag)
			{
				foreach (Player player2 in player.Game.GetAllTeamPlayers(player))
				{
					player2.AddPetEffect(new PetShareTakeDamageToTeamEquip(this.m_count, base.Info.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x060079E3 RID: 31203 RVA: 0x0002E0E2 File Offset: 0x0002C2E2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.petUseSkill;
		}

		// Token: 0x060079E4 RID: 31204 RVA: 0x0002E0F8 File Offset: 0x0002C2F8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.petUseSkill;
		}

		// Token: 0x04004768 RID: 18280
		private int m_count;

		// Token: 0x04004769 RID: 18281
		private int m_probability;

		// Token: 0x0400476A RID: 18282
		private int m_skillId;
	}
}
