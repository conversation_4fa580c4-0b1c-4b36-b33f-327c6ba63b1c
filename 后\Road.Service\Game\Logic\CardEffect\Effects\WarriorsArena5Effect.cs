﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F3A RID: 3898
	public class WarriorsArena5Effect : BaseCardEffect
	{
		// Token: 0x06008489 RID: 33929 RVA: 0x002B8890 File Offset: 0x002B6A90
		public WarriorsArena5Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.WarriorsArena5, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x0600848A RID: 33930 RVA: 0x002B8900 File Offset: 0x002B6B00
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.WarriorsArena5) is WarriorsArena5Effect;
			return flag || base.Start(living);
		}

		// Token: 0x0600848B RID: 33931 RVA: 0x00034FC0 File Offset: 0x000331C0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.ChangeProperty;
		}

		// Token: 0x0600848C RID: 33932 RVA: 0x00034FD6 File Offset: 0x000331D6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage -= this.ChangeProperty;
		}

		// Token: 0x0600848D RID: 33933 RVA: 0x002B8938 File Offset: 0x002B6B38
		private void ChangeProperty(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				this.m_added = 0;
			}
			bool flag2 = living.Game is PVEGame && (living.Game as PVEGame).Info.ID == 13 && living is SimpleBoss;
			if (flag2)
			{
				int id = (living as SimpleBoss).NpcInfo.ID;
				int num = id;
				if (num <= 13107)
				{
					if (num != 13007 && num != 13107)
					{
						goto IL_00A8;
					}
				}
				else if (num != 13207 && num != 13307 && num != 13407)
				{
					goto IL_00A8;
				}
				damageAmount -= this.m_value;
				IL_00A8:;
			}
		}

		// Token: 0x040052A2 RID: 21154
		private int m_indexValue = 0;

		// Token: 0x040052A3 RID: 21155
		private int m_value = 0;

		// Token: 0x040052A4 RID: 21156
		private int m_added = 0;
	}
}
