﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F24 RID: 3876
	public class FiveGodSoldier5Effect : BaseCardEffect
	{
		// Token: 0x0600840F RID: 33807 RVA: 0x002B6848 File Offset: 0x002B4A48
		public FiveGodSoldier5Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.FiveGodSoldier5, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008410 RID: 33808 RVA: 0x002B68B8 File Offset: 0x002B4AB8
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.FiveGodSoldier5) is FiveGodSoldier5Effect;
			return flag || base.Start(living);
		}

		// Token: 0x06008411 RID: 33809 RVA: 0x00034A4D File Offset: 0x00032C4D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
			player.PlayerAfterReset += this.ChangeProperty1;
		}

		// Token: 0x06008412 RID: 33810 RVA: 0x002B68F0 File Offset: 0x002B4AF0
		private void Player_AfterKilledByLiving(Living player, Living target, int damageAmount, int criticalAmount)
		{
			this.m_added = (damageAmount + criticalAmount) * this.m_value / 100;
			bool flag = this.m_added <= 0;
			if (!flag)
			{
				target.SyncAtTime = true;
				target.AddBlood(-this.m_added, 1);
				target.SyncAtTime = false;
				bool flag2 = target.Blood <= 0;
				if (flag2)
				{
					target.Die();
					bool flag3 = player != null && player is Player;
					if (flag3)
					{
						(player as Player).PlayerDetail.OnKillingLiving(player.Game, 2, target.Id, target.IsLiving, this.m_added);
					}
				}
			}
		}

		// Token: 0x06008413 RID: 33811 RVA: 0x00034A76 File Offset: 0x00032C76
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
			player.PlayerAfterReset -= this.ChangeProperty1;
		}

		// Token: 0x06008414 RID: 33812 RVA: 0x00034A9F File Offset: 0x00032C9F
		private void ChangeProperty1(Player player)
		{
			player.Game.SendMessage(player.PlayerDetail, "您激活了五神兵5件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活五神兵5件套卡.", 3);
		}

		// Token: 0x06008415 RID: 33813 RVA: 0x002B699C File Offset: 0x002B4B9C
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.BaseDamage -= (double)this.m_added;
				player.BaseGuard -= (double)this.m_added;
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVPGame && (player.Game as PVPGame).IsMatchOrFreedom();
			if (flag2)
			{
				this.m_added = this.m_value;
				player.BaseDamage += (double)this.m_added;
				player.BaseGuard += (double)this.m_added;
			}
		}

		// Token: 0x04005262 RID: 21090
		private int m_indexValue = 0;

		// Token: 0x04005263 RID: 21091
		private int m_value = 0;

		// Token: 0x04005264 RID: 21092
		private int m_added = 0;
	}
}
