﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using Bussiness;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000CA9 RID: 3241
	public class PropItemMgr
	{
		// Token: 0x0600742A RID: 29738 RVA: 0x0026606C File Offset: 0x0026426C
		public static bool Reload()
		{
			try
			{
				Dictionary<int, ItemTemplateInfo> dictionary = new Dictionary<int, ItemTemplateInfo>();
				bool flag = PropItemMgr.LoadProps(dictionary);
				if (flag)
				{
					PropItemMgr.m_lock.AcquireWriterLock(-1);
					try
					{
						PropItemMgr._allProp = dictionary;
						return true;
					}
					catch
					{
					}
					finally
					{
						PropItemMgr.m_lock.ReleaseWriterLock();
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = PropItemMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					PropItemMgr.log.Error("ReloadProps", ex);
				}
			}
			return false;
		}

		// Token: 0x0600742B RID: 29739 RVA: 0x00266114 File Offset: 0x00264314
		public static bool Init()
		{
			bool flag;
			try
			{
				PropItemMgr.m_lock = new ReaderWriterLock();
				PropItemMgr._allProp = new Dictionary<int, ItemTemplateInfo>();
				flag = PropItemMgr.LoadProps(PropItemMgr._allProp);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = PropItemMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					PropItemMgr.log.Error("InitProps", ex);
				}
				flag = false;
			}
			return flag;
		}

		// Token: 0x0600742C RID: 29740 RVA: 0x0026617C File Offset: 0x0026437C
		private static bool LoadProps(Dictionary<int, ItemTemplateInfo> allProp)
		{
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				ItemTemplateInfo[] singleCategory = produceBussiness.GetSingleCategory(10);
				ItemTemplateInfo[] array = singleCategory;
				ItemTemplateInfo[] array2 = array;
				ItemTemplateInfo[] array3 = array2;
				foreach (ItemTemplateInfo itemTemplateInfo in array3)
				{
					allProp.Add(itemTemplateInfo.TemplateID, itemTemplateInfo);
				}
			}
			return true;
		}

		// Token: 0x0600742D RID: 29741 RVA: 0x002661F8 File Offset: 0x002643F8
		public static ItemTemplateInfo FindFightingProp(int id)
		{
			PropItemMgr.m_lock.AcquireReaderLock(-1);
			try
			{
				bool flag = !PropItemMgr.PropBag.Contains(id);
				if (flag)
				{
					return null;
				}
				bool flag2 = PropItemMgr._allProp.ContainsKey(id);
				if (flag2)
				{
					return PropItemMgr._allProp[id];
				}
			}
			catch
			{
			}
			finally
			{
				PropItemMgr.m_lock.ReleaseReaderLock();
			}
			return null;
		}

		// Token: 0x0400440A RID: 17418
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x0400440B RID: 17419
		private static ThreadSafeRandom random = new ThreadSafeRandom();

		// Token: 0x0400440C RID: 17420
		private static ReaderWriterLock m_lock;

		// Token: 0x0400440D RID: 17421
		private static Dictionary<int, ItemTemplateInfo> _allProp;

		// Token: 0x0400440E RID: 17422
		private static int[] PropBag = new int[]
		{
			10001, 10002, 10003, 10004, 10005, 10006, 10007, 10008, 10009, 10010,
			10011, 10012, 10013, 10014, 10015, 10016, 10017, 10018, 10019, 10020,
			10021, 10022
		};
	}
}
