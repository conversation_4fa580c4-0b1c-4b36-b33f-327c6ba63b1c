﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EBE RID: 3774
	public class CE1432 : BasePetEffect
	{
		// Token: 0x06008225 RID: 33317 RVA: 0x002AF8E8 File Offset: 0x002ADAE8
		public CE1432(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1432, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008226 RID: 33318 RVA: 0x002AF968 File Offset: 0x002ADB68
		public override bool Start(Living living)
		{
			CE1432 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1432) as CE1432;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008227 RID: 33319 RVA: 0x000332BD File Offset: 0x000314BD
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008228 RID: 33320 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008229 RID: 33321 RVA: 0x002AF9C8 File Offset: 0x002ADBC8
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600822A RID: 33322 RVA: 0x002AF9FC File Offset: 0x002ADBFC
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PetEffects.CritRate = 0;
			player.BaseDamage -= player.BaseDamage * 10.0 / 100.0;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005171 RID: 20849
		private int m_type = 0;

		// Token: 0x04005172 RID: 20850
		private int m_count = 0;

		// Token: 0x04005173 RID: 20851
		private int m_probability = 0;

		// Token: 0x04005174 RID: 20852
		private int m_delay = 0;

		// Token: 0x04005175 RID: 20853
		private int m_coldDown = 0;

		// Token: 0x04005176 RID: 20854
		private int m_currentId;

		// Token: 0x04005177 RID: 20855
		private int m_added = 0;
	}
}
