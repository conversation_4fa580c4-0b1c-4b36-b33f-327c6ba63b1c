﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DE0 RID: 3552
	public class AE1162 : BasePetEffect
	{
		// Token: 0x06007D0F RID: 32015 RVA: 0x0029A1C4 File Offset: 0x002983C4
		public AE1162(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1162, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D10 RID: 32016 RVA: 0x0029A244 File Offset: 0x00298444
		public override bool Start(Living living)
		{
			AE1162 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1162) as AE1162;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D11 RID: 32017 RVA: 0x000301AE File Offset: 0x0002E3AE
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007D12 RID: 32018 RVA: 0x0029A2A4 File Offset: 0x002984A4
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				this.m_added = 4000;
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddBlood(-this.m_added, 1);
					bool flag2 = player2.Blood <= 0;
					if (flag2)
					{
						player2.Die();
						if (player != null)
						{
							player.PlayerDetail.OnKillingLiving(player.Game, 2, player2.Id, player2.IsLiving, this.m_added);
						}
					}
					player2.AddPetEffect(new CE1162(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x06007D13 RID: 32019 RVA: 0x000301C4 File Offset: 0x0002E3C4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004B58 RID: 19288
		private int m_type = 0;

		// Token: 0x04004B59 RID: 19289
		private int m_count = 0;

		// Token: 0x04004B5A RID: 19290
		private int m_probability = 0;

		// Token: 0x04004B5B RID: 19291
		private int m_delay = 0;

		// Token: 0x04004B5C RID: 19292
		private int m_coldDown = 0;

		// Token: 0x04004B5D RID: 19293
		private int m_currentId;

		// Token: 0x04004B5E RID: 19294
		private int m_added = 0;
	}
}
