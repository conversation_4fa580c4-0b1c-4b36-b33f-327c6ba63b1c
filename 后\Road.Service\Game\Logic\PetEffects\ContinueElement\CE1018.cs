﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E44 RID: 3652
	public class CE1018 : BasePetEffect
	{
		// Token: 0x06007F23 RID: 32547 RVA: 0x002A3D30 File Offset: 0x002A1F30
		public CE1018(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1018, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F24 RID: 32548 RVA: 0x002A3DB0 File Offset: 0x002A1FB0
		public override bool Start(Living living)
		{
			CE1018 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1018) as CE1018;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F25 RID: 32549 RVA: 0x002A3E10 File Offset: 0x002A2010
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				player.Defence += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F26 RID: 32550 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F27 RID: 32551 RVA: 0x002A3E74 File Offset: 0x002A2074
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F28 RID: 32552 RVA: 0x002A3EA8 File Offset: 0x002A20A8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Defence -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E17 RID: 19991
		private int m_type = 0;

		// Token: 0x04004E18 RID: 19992
		private int m_count = 0;

		// Token: 0x04004E19 RID: 19993
		private int m_probability = 0;

		// Token: 0x04004E1A RID: 19994
		private int m_delay = 0;

		// Token: 0x04004E1B RID: 19995
		private int m_coldDown = 0;

		// Token: 0x04004E1C RID: 19996
		private int m_currentId;

		// Token: 0x04004E1D RID: 19997
		private int m_added = 0;
	}
}
