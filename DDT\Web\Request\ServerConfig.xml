<Result value="true" message="Success!">
  <Item Name="MustStrengthenGold" Value="1000" />
  <Item Name="MustComposeGold" Value="1000" />
  <Item Name="MustFusionGold" Value="400" />
  <Item Name="Edition" Value="36000" />
  <Item Name="CheckRewardItem" Value="11001" />
  <Item Name="CheckCount" Value="10" />
  <Item Name="HymenealMoney" Value="500" />
  <Item Name="DivorcedMoney" Value="5214" />
  <Item Name="MarryRoomCreateMoney" Value="2000,2700,3400" />
  <Item Name="BoxAppearCondition" Value="4" />
  <Item Name="DisableCommands" Value="   " />
  <Item Name="AssState" Value="False" />
  <Item Name="DailyAwardState" Value="True" />
  <Item Name="Cess" Value="0" />
  <Item Name="BeginAuction" Value="45" />
  <Item Name="EndAuction" Value="75" />
  <Item Name="Equip" Value="3158,,5160,7008|3244,,5276,7008" />
  <Item Name="CopyInviteLevelLimit" Value="9" />
  <Item Name="VIPExpNeededForEachLv" Value="0|100|250|550|1250|2200|3100|4100|5650|7650|10250|14650" />
  <Item Name="HotSpringExp" Value="400,480,576,692,830,996,1194,1434,1720,1892,2082,2290,2518,2770,3046,3352,3686,4056,4462,4684,4918,5164,5422,5694,5978,6278,6590,6920,7266,7630,7858,8094,8338,8588,8846,9110,9384,9666,9956,10254,10562,10878,11204,11540,11888,12244,12612,12990,13380,13780,14194,14620,15058,15510,15976,16454,16948,17456,17980,18520" />
  <Item Name="SpaPubRoomLoginPay" Value="10000,2000" />
  <Item Name="AcademyApprenticeFreezeHours" Value="24" />
  <Item Name="AcademyMasterFreezeHours" Value="48" />
  <Item Name="AcademyApprenticeAward" Value="10|112085,15|112086,18|112087,20|112125" />
  <Item Name="AcademyMasterAward" Value="10|112088,15|112089,18|112090,20|112124" />
  <Item Name="AcademyAppAwardComplete" Value="1401|5293,1301|5192" />
  <Item Name="AcademyMasAwardComplete" Value="1414|5409,1314|5306" />
  <Item Name="WishBeadLimitLv" Value="12" />
  <Item Name="IsWishBeadLimit" Value="False" />
  <Item Name="HoleLevelUpExpList" Value="400|600|700|800|800" />
  <Item Name="Equip" Value="3158,,5160,7008|3244,,5276,7008" />
  <Item Name="MustTransferGold" Value="10000" />
  <Item Name="GiftExpForEachLv" Value="0|10|60|180|390|710|1160|1760|2530|3490|4660|6070|7750|9730|12040|14710|17770|21250|25180|29590|34510|39980|46040|52730|60090|68160|76980|86590|97030|108340|120560|133750|147970|163280|179740|197410|216350|236620|258280|281390|306500|334160|364920|399330|437940|481300|529960|584470|645380|713240|788600|872060|964220|1065680|1177040|1298900|1431860|1576520|1733480|1903340|2086700|2284160|2496320|2723780|2967140|3227000|3503960|3798620|4111580|4443440|4794800|5166260|5558420|5971880|6407240|6865100|7346060|7850720|8379680|8933540|9512900|10118360|10750520|11409980|12097340|12813200|13558160|14332820|15137780|15973640|16841000|17740460|18672620|19638080|20637440|21671300|22740260|23844920|24985880|26163740" />
  <Item Name="LittleGameStartHourse" Value="1" />
  <Item Name="LittleGameTimeSpending" Value="22" />
  <Item Name="LittleGameBoguConfig" Value="200,1,1|100,4,1|5000,80,3|10000,125,5" />
  <Item Name="LittleGameMaxBoguCount" Value="60" />
  <Item Name="LeftRouterRateData" Value="0.8|90|100|110|120|" />
  <Item Name="LeftRoutteMaxDay" Value="5" />
  <Item Name="LeftRouterEndDate" Value="2020/11/13 0:00:00" />
  <Item Name="MissionRiches" Value="3000|3000|5000|5000|8000|8000|10000|10000|12000|12000" />
  <Item Name="MissionAwardRiches" Value="4500|4500|7500|7500|12000|12000|15000|15000|18000|18000" />
  <Item Name="MissionAwardGP" Value="100000|100000|200000|200000|300000|300000|400000|400000|500000|500000" />
  <Item Name="MissionAwardOffer" Value="500|500|1000|1000|1500|1500|2000|2000|2500|2500" />
  <Item Name="MissionMinute" Value="360" />
  <Item Name="MissionBuffDay" Value="1" />
  <Item Name="PetQualityConfig" Value="200|300|400|500|600" />
  <Item Name="VirtualName" Value="XiaoJian" />
  <Item Name="SpaPubRoomServerID" Value="1|2|3|4|5|6|7|8|9|10" />
  <Item Name="SpaPubRoomLoginPay" Value="10000,200" />
  <Item Name="SpaPriRoomInitTime" Value="60" />
  <Item Name="SpaPriRoomContinueTime" Value="60" />
  <Item Name="SpaPubRoomTimeLimit" Value="60,60" />
  <Item Name="SpaPubRoomPlayerMaxCount" Value="10,10" />
  <Item Name="SpaRoomCreateMoney" Value="800,1600" />
  <Item Name="SpaPubRoomCount" Value="8,0" />
  <Item Name="SpaPriRoomCount" Value="2000" />
  <Item Name="OpenCloseTreasure" Value="True" />
  <Item Name="TreasureBeginTime" Value="2024/01/28 23:59" />
  <Item Name="TreasureEndTime" Value="2050/02/04 23:59" />
  <Item Name="ShowCloseTreasure" Value="True" />
  <Item Name="TreasureNeedMoney" Value="3000" />
  <Item Name="VIPGpBonusRatePerLevel" Value="0.1" />
  <Item Name="VIPOfferDecreaseRate" Value="0.5" />
  <Item Name="ConsortiaFightRate" Value="0" />
  <Item Name="CoupleFightGPRate" Value="0.5" />
  <Item Name="EliteGameDayOpening" Value="6" />
  <Item Name="CheckFailCount" Value="5" />
  <Item Name="LuckStoneRankReward" Value="1-1,11660058|2-2,116600589|3-3,11660058|4-4,11660058|5-5,11660058|6-10,11660058|11-20,11660058|21-50,11660058|51-100,11660058|101-200,11660058" />
  <Item Name="EmblemComposeRandom" Value="500,600" />
  <Item Name="BombEnterLevel" Value="10" />
  <Item Name="BombItemNum" Value="10" />
  <Item Name="BombStartTime" Value="2024/09/14 23:59" />
  <Item Name="BombEndTime" Value="2050/10/15 23:59" />
  <Item Name="BombTimes" Value="2" />
  <Item Name="BombFreeAutoMarkTimes" Value="2" />
  <Item Name="AutoMarkPrice" Value="300" />
  <Item Name="AutoMarkFactor" Value="300" />
  <Item Name="RecoverPrice" Value="500" />
  <Item Name="RecoverPriceFactor" Value="500" />
  <Item Name="BombFreeAutoResetTimes" Value="2" />
  <Item Name="BombResetPrice" Value="5000" />
  <Item Name="BombResetPriceFactor" Value="0" />
  <Item Name="BombAreaAward" Value="1-1,10|2-2,11|3-3,12|4-4,13|5-5,14|6-6,15|7-7,16|8-8,17|9-9,18|10-10,19" />
  <Item Name="PairBoxBeginTime" Value="2023/9/13 0:00:00" />
  <Item Name="PairBoxEndTime" Value="2050/11/20 23:59:59" />
  <Item Name="PairBoxDayBoxCount" Value="1" />
  <Item Name="PairBoxExchangeHour" Value="24" />
  <Item Name="PairBoxDayActiveConfig" Value="30,50,90,120,150" />
  <Item Name="PairBoxLimitBuy" Value="0" />
  <Item Name="PairBoxPriceConfig" Value="10000,10000,10000" />
  <Item Name="MysteryShopOpenTime" Value="0|0" />
  <Item Name="MysteryShopFreshTime" Value="18" />
  <Item Name="VIP11BagVIPLevelToBagCount" Value="2,40|4,60|6,80|8,100" />
  <Item Name="MysteryShopMoneyRefreshValue" Value="1000" />
  <Item Name="EliteGameBlockWeapon" Value="0" />
  <Item Name="MoonLightBoxOpenTime" Value="2024/01/28 23:59" />
  <Item Name="MoonLightBoxEndExchangeTime" Value="2024/02/05 23:59" />
  <Item Name="MoonLightBoxEndTime" Value="2050/11/20 23:59:59" />
  <Item Name="MoonlightBoxOpenTimes" Value="1,100,500" />
  <Item Name="MoonlightBoxPrice" Value="1,2000|100,200000|500,1000000" />
  <Item Name="MoonlightBoxCrossReward" Value="1,1124925|2,1124926|3,1124927|4,1124928|5,1124929|6,1124929|7,1124929|8,1124929|9,1124929|10,1124929" />
  <Item Name="MoonlightBoxLimitBuy" Value="100" />
  <Item Name="MoonlightBoxRewardsTemplate" Value="20,1124933,1,1|100,1124934,2,1|500,1124935,2,1|3000,1124947,2,1|5000,1124942,2,1|10000,1124946,2,1" />
  <Item Name="NoviceUpGradeStartTime" Value="2014/2/13 0:00:00" />
  <Item Name="NoviceUpGradeEndTime" Value="2014/2/13 0:00:00" />
  <Item Name="NoviceUseMoneyStartTime" Value="2014/2/13 0:00:00" />
  <Item Name="NoviceUseMoneyEndTime" Value="2014/2/13 0:00:00" />
  <Item Name="NoviceReChangeMoneyStartTime" Value="2014/2/13 0:00:00" />
  <Item Name="NoviceReChangeMoneyEndTime" Value="2014/2/13 0:00:00" />
  <Item Name="NoviceStrengthenWeaponStartTime" Value="2014/2/13 0:00:00" />
  <Item Name="NoviceStrengthenWeaponEndTime" Value="2014/2/13 0:00:00" />
  <Item Name="FastGrowNeedMoney" Value="30" />
  <Item Name="FastGrowSubTime" Value="30" />
  <Item Name="SignRateBeginTime" Value="2014/2/13 0:00:00" />
  <Item Name="SignRateEndTime" Value="2023/8/31 23:59:59" />
  <Item Name="IsOpenPetScore" Value="false" />
  <Item Name="ChargeActivityBeginTime" Value="2023/12/19 0:00:00" />
  <Item Name="ChargeActivityEndTime" Value="2050/12/19 0:00:00" />
  <Item Name="EngraveLimitLevel" Value="60" />
  <Item Name="AddEngraveEquipSchemeCostMoney" Value="2000,2000,2000,5000,8000,10000,12000,15000" />
  <Item Name="EngraveTemperMoney" Value="1000" />
  <Item Name="SigilRanProUseSoulCrystalConfig" Value="1,100|2,200|3,300|4,400|5,500|6,600" />
  <Item Name="SpellsUpgradeUseSoulCrystalConfig" Value="1,100|2,200|3,300|4,400|5,500|6,600" />
  <Item Name="Sigil3" Value="0.2,0.4,0.6,0.8,0.9,1" />
  <Item Name="EngraveVaultsConfig" Value="1,2000,1|10,18000,10" />
  <Item Name="EngraveVaultsFreeTimes" Value="5" />
  <Item Name="EngraveSaleStarConfig" Value="10" />
  <Item Name="EngraveSaleTemperConsumeConfig" Value="60" />
  <Item Name="WarPassPriceConfig" Value="1,8,500,800|2,99,2500,9900|3,498,2500,49800|4,8,6,800|5,99,6,9900|6,498,6,49800" />
  <Item Name="WarPassPetConfig" Value="1,240203_12270_46494,290203_12271_46495,270203_12272_46496,230203_12273_46497,310203_12274_46498,320203_12275_46499,350203_12299_46538,340203_12301_46540,360203_12318_46593,370203_12319_46595|2,240203_12270_46478,290203_12271_46479,270203_12272_46480,230203_12273_46481,310203_12274_46482,320203_12275_46483,350203_12299_46537,340203_12301_46539,360203_12318_46592,370203_12319_46594|3,140403_12276_46484,160103_12277_46485,200203_12278_46486,150103_12279_46487,190103_12280_46488|4,1124047_1124023_1124027,1124048_1124024_1124028,1124049_1124025_1124029,1124050_1124026_1124030|5,1124047_1124031_1124035,1124048_1124032_1124036,1124049_1124033_1124037,1124050_1124034_1124038|6,1124047_1124039_1124043,1124048_1124040_1124044,1124049_1124041_1124045,1124050_1124042_1124046" />
  <Item Name="WarPassPetShowConfig" Value="550,500,1|2750,2500,1|11000,2500,1|275,6,1|1375,6,1|5500,6,1" />
  <Item Name="WarPassSeasonNeedCharge" Value="58800" />
  <Item Name="WarPassSeasonPrice" Value="58800" />
  <Item Name="WarPassSeasonBuyLevelPrice" Value="20000" />
  <Item Name="WarPassSeasonID" Value="2" />
  <Item Name="WarPassSeasonEndID" Value="0" />
  <Item Name="WarPassSeasonEndTime" Value="2050/8/31 23:59:59" />
  <Item Name="WarPassSeasonValidDays" Value="45" />
  <Item Name="WarPassQuitConfig" Value="0-0,66|1-14,50|15-30,0" />
  <Item Name="WarPassLevelLimit&#xD;&#xA;" Value="20" />
  <Item Name="WarPassRefreshConfig&#xD;&#xA;" Value="2,1,6,4|2,1,6,4" />
  <Item Name="MatchWIn" Value="100" />
  <Item Name="MatchLose" Value="200" />
  <Item Name="CarnivalConvertItem" Value="1,1000000034-1,1|2,1000000035-1,1|3,1000000036-1,1|4,1000000037-1,1|5,7225-1,10|6,46827-1,5|7,1000000038-1,5" />
  <Item Name="CarnivalBeginTime" Value="2013/12/17 0:00:00" />
  <Item Name="CarnivalEndTime" Value="2050/12/25 0:00:00" />
  <Item Name="YearMonsterBeginDate" Value="2015/8/20 0:00:00" />
  <Item Name="YearMonsterEndDate" Value="2015/9/30 23:59:00" />
  <Item Name="YearMonsterHP" Value="6000000" />
  <Item Name="YearMonsterBoxInfo" Value="11408,10|11901,60|11902,180|11903,300|11904,600" />
  <Item Name="YearMonsterBuffMoney" Value="300" />
  <Item Name="YearMonsterFightNum" Value="1" />
  <Item Name="YearMonsterOpenLevel" Value="15" />
  <Item Name="QXGameLimitCount" Value="3" />
  <Item Name="ExchangeFold" Value="100" />
  <Item Name="DDPlayMoney" Value="10000" />
  <Item Name="FightSpiritLevelAddDamage" Value="6,2" />
  <Item Name="FightSpiritMaxLevel" Value="12" />
  <Item Name="FirstKillBeginDate" Value="2022/4/20 0:00:00" />
  <Item Name="FirstKillEndDate" Value="2025/4/27 0:00:00" />
  <Item Name="DevilTreasureOneCost" Value="0" />
  <Item Name="DevilTreasureTenCost" Value="0" />
  <Item Name="DevilTreasureFreeLotteryCount" Value="10" />
  <Item Name="DevilTreasureFreeLotteryCD" Value="600" />
  <Item Name="DevilTreasurePrizePool" Value="50000" />
  <Item Name="DevilTreasurePrizePoolMax" Value="100000" />
  <Item Name="DevilTreasureRankCount" Value="2000" />
  <Item Name="DevilTreasureBoxExpriyMinutes" Value="240" />
  <Item Name="DevilTreasureTemplateID" Value="11169" />
  <Item Name="AmuletBuyDustMax" Value="50" />
  <Item Name="AmuletActiveRandom" Value="1" />
  <Item Name="AmuletActiveMoney" Value="1000" />
  <Item Name="AmuletBuyDustCountAndNeedMoney" Value="100|200|500" />
  <Item Name="DevilTreasureBeginDate" Value="2020/4/26 10:00:00" />
  <Item Name="DevilTreasureEndDate" Value="2050/4/26 23:59:59" />
  <Item Name="DevilTreasureCfgBox" Value="12711|12712|12713" />
  <Item Name="ChristmasBeginDate" Value="2013/12/17 0:00:00" />
  <Item Name="ChristmasEndDate" Value="2050/12/25 0:00:00" />
  <Item Name="ChristmasGifts" Value="201148,10|201149,35|201150,70|201151,120|201152,220|201153,370|201154,650|201155,1000|201156,100" />
  <Item Name="ChristmasGiftsMaxNum" Value="1000" />
  <Item Name="ChristmasBuildSnowmanDoubleMoney" Value="10" />
  <Item Name="ChristmasBuyTimeMoney" Value="150" />
  <Item Name="ChristmasMinute" Value="60" />
  <Item Name="ChristmasBuyMinute" Value="10" />
  <Item Name="IsDDTMoneyActive" Value="False" />
  <Item Name="PetWashCost" Value="0,40|1,80|2,120|3,160|4,200" />
  <Item Name="PetQualityConfig" Value="60|70|80|95|100" />
  <Item Name="GodCardDailyFreeCount" Value="3" />
  <Item Name="GodCardOpenOneTimeMoney" Value="500000" />
  <Item Name="GodCardOpenFiveTimeMoney" Value="24688" />
  <Item Name="RelicAdvanceToUpgrade" Value="0,5|1,8|2,10|3,10" />
  <Item Name="RelicDegreeScore" Value="1,2,3,5,7|2,3,4,6,8|3,4,5,7,9|4,5,6,8,10|5,6,7,9,11" />
  <Item Name="RelicGroupSkill" Value="1,3,20,1,1,2,2,3,70392|2,3,30,1,2,3,4,5,70393" />
  <Item Name="RelicUpgradeItem" Value="11024,50" />
  <Item Name="RelicOpenEquipPos" Value="1,10,20,30,40,50" />
  <Item Name="RelicBuffSubstatPrice" Value="1,1,10,1,0|1,2,20,2,0|1,3,30,3,0|2,1,20,2,0|2,2,30,3,0|2,3,40,4,0|3,1,30,3,20|3,2,40,4,30|3,3,50,5,40|4,1,40,4,20|4,2,50,5,30|4,3,60,6,40|5,1,50,5,20|5,2,60,6,30|5,3,70,7,40" />
  <Item Name="RelicUpgradeItem" Value="50" />
</Result>