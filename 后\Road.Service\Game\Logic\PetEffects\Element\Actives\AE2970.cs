﻿using System;
using Game.Logic.Effects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E31 RID: 3633
	public class AE2970 : BasePetEffect
	{
		// Token: 0x06007EB8 RID: 32440 RVA: 0x002A215C File Offset: 0x002A035C
		public AE2970(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE2970, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_currentId = skillId;
			bool flag = !(elementID == "2970");
			if (flag)
			{
				bool flag2 = elementID == "2973";
				if (flag2)
				{
					this.m_guard = 15;
				}
			}
			else
			{
				this.m_value = 50;
			}
		}

		// Token: 0x06007EB9 RID: 32441 RVA: 0x002A21F8 File Offset: 0x002A03F8
		public override bool Start(Living living)
		{
			AE2970 ae = living.PetEffectList.GetOfType(ePetEffectType.AE2970) as AE2970;
			bool flag = ae == null;
			bool flag2;
			if (flag)
			{
				flag2 = base.Start(living);
			}
			else
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x06007EBA RID: 32442 RVA: 0x0003123D File Offset: 0x0002F43D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
			player.BeforeMakeDamage += this.player_BeforeMakeDamage;
		}

		// Token: 0x06007EBB RID: 32443 RVA: 0x002A225C File Offset: 0x002A045C
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.ControlBall = true;
			}
			bool flag2 = this.m_guard != 0;
			if (flag2)
			{
				player.IgnoreGuard += this.m_guard;
			}
		}

		// Token: 0x06007EBC RID: 32444 RVA: 0x002A22AC File Offset: 0x002A04AC
		private void player_BeforeMakeDamage(Living living, Living target)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				target.IsNoHole = true;
				target.AddEffect(new NoHoleEffect(0), 1000);
				target.Game.SendGameUpdateNoHoleState(target);
				this.m_blood = (int)(living.BaseDamage * 2.0);
				bool flag2 = target != living;
				if (flag2)
				{
					target.AddPetEffect(new ContinueReduceBloodEffect(3, this.m_blood, living), 1000);
				}
			}
		}

		// Token: 0x06007EBD RID: 32445 RVA: 0x002A2338 File Offset: 0x002A0538
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
			player.BeforeMakeDamage -= this.player_BeforeMakeDamage;
			player.ControlBall = false;
			player.IgnoreGuard -= this.m_guard;
		}

		// Token: 0x04004D92 RID: 19858
		private int m_type = 0;

		// Token: 0x04004D93 RID: 19859
		private int m_count = 0;

		// Token: 0x04004D94 RID: 19860
		private int m_probability = 0;

		// Token: 0x04004D95 RID: 19861
		private int m_guard = 0;

		// Token: 0x04004D96 RID: 19862
		private int m_currentId;

		// Token: 0x04004D97 RID: 19863
		private int m_value;

		// Token: 0x04004D98 RID: 19864
		private int m_blood;
	}
}
