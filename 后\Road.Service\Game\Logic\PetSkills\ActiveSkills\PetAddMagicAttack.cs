﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D27 RID: 3367
	public class PetAddMagicAttack : BasePetEffect
	{
		// Token: 0x0600792B RID: 31019 RVA: 0x0002D502 File Offset: 0x0002B702
		public PetAddMagicAttack(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetAddMagicAttack, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x0600792C RID: 31020 RVA: 0x002895A4 File Offset: 0x002877A4
		public override bool Start(Living living)
		{
			PetAddMagicAttack petAddMagicAttack = living.PetEffectList.GetOfType(ePetEffectType.PetAddMagicAttack) as PetAddMagicAttack;
			bool flag = petAddMagicAttack != null;
			bool flag2;
			if (flag)
			{
				petAddMagicAttack.m_probability = ((this.m_probability > petAddMagicAttack.m_probability) ? this.m_probability : petAddMagicAttack.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600792D RID: 31021 RVA: 0x0002D533 File Offset: 0x0002B733
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x0600792E RID: 31022 RVA: 0x0002D549 File Offset: 0x0002B749
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x0600792F RID: 31023 RVA: 0x00289604 File Offset: 0x00287804
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				living.PetEffectTrigger = true;
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				new PetAddMagicAttackEquip(this.m_count, base.Info.ID.ToString()).Start(living);
			}
		}

		// Token: 0x040046FF RID: 18175
		private int m_count;

		// Token: 0x04004700 RID: 18176
		private int m_probability;

		// Token: 0x04004701 RID: 18177
		private int m_currentId;
	}
}
