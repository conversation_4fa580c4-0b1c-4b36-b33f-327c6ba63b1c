﻿using System;
using System.Collections;
using System.Configuration;
using System.IO;
using System.Reflection;
using System.Text;
using System.Threading;
using log4net;

namespace Bussiness
{
	// Token: 0x02000FB0 RID: 4016
	public class LanguageMgr
	{
		// Token: 0x170014E2 RID: 5346
		// (get) Token: 0x060088A2 RID: 34978 RVA: 0x0003631B File Offset: 0x0003451B
		private static string LanguageFile
		{
			get
			{
				return ConfigurationManager.AppSettings["LanguagePath"];
			}
		}

		// Token: 0x060088A3 RID: 34979 RVA: 0x002D27A4 File Offset: 0x002D09A4
		public static bool Setup(string path)
		{
			return LanguageMgr.Reload(path);
		}

		// Token: 0x060088A4 RID: 34980 RVA: 0x002D27BC File Offset: 0x002D09BC
		public static bool Reload(string path)
		{
			try
			{
				Hashtable hashtable = LanguageMgr.LoadLanguage(path);
				bool flag = hashtable.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Hashtable>(ref LanguageMgr.LangsSentences, hashtable);
					return true;
				}
			}
			catch (Exception ex)
			{
				LanguageMgr.log.Error("Load language file error:", ex);
			}
			return false;
		}

		// Token: 0x060088A5 RID: 34981 RVA: 0x002D2820 File Offset: 0x002D0A20
		private static Hashtable LoadLanguage(string path)
		{
			Hashtable hashtable = new Hashtable();
			string text = path + LanguageMgr.LanguageFile;
			bool flag = !File.Exists(text);
			if (flag)
			{
				LanguageMgr.log.Error("Language file : " + text + " not found !");
			}
			else
			{
				string[] array = File.ReadAllLines(text, Encoding.UTF8);
				IList list = new ArrayList(array);
				foreach (object obj in list)
				{
					string text2 = (string)obj;
					bool flag2 = !text2.StartsWith("#") && text2.IndexOf(':') != -1;
					if (flag2)
					{
						string[] array2 = new string[]
						{
							text2.Substring(0, text2.IndexOf(':')),
							text2.Substring(text2.IndexOf(':') + 1)
						};
						array2[1] = array2[1].Replace("\t", "");
						hashtable[array2[0]] = array2[1];
					}
				}
			}
			return hashtable;
		}

		// Token: 0x060088A6 RID: 34982 RVA: 0x002D2960 File Offset: 0x002D0B60
		public static string GetTranslation(string translateId, params object[] args)
		{
			bool flag = LanguageMgr.LangsSentences.ContainsKey(translateId);
			string text2;
			if (flag)
			{
				string text = (string)LanguageMgr.LangsSentences[translateId];
				try
				{
					text = string.Format(text, args);
				}
				catch (Exception ex)
				{
					LanguageMgr.log.Error(string.Concat(new string[]
					{
						"Parameters number error, ID: ",
						translateId,
						" (Arg count=",
						args.Length.ToString(),
						")"
					}), ex);
				}
				text2 = ((text == null) ? translateId : text);
			}
			else
			{
				text2 = translateId;
			}
			return text2;
		}

		// Token: 0x04005497 RID: 21655
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005498 RID: 21656
		private static Hashtable LangsSentences = new Hashtable();
	}
}
