﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D3A RID: 3386
	public class PetFatalEffect : BasePetEffect
	{
		// Token: 0x06007992 RID: 31122 RVA: 0x0002DB97 File Offset: 0x0002BD97
		public PetFatalEffect(int count, int probability, int skillId, int delay, string elementID)
			: base(ePetEffectType.PetFatalEffect, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007993 RID: 31123 RVA: 0x0028B490 File Offset: 0x00289690
		public override bool Start(Living living)
		{
			PetFatalEffect petFatalEffect = living.PetEffectList.GetOfType(ePetEffectType.PetFatalEffect) as PetFatalEffect;
			bool flag = petFatalEffect != null;
			bool flag2;
			if (flag)
			{
				petFatalEffect.m_probability = ((this.m_probability > petFatalEffect.m_probability) ? this.m_probability : petFatalEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007994 RID: 31124 RVA: 0x0002DBC9 File Offset: 0x0002BDC9
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerShoot += new PlayerEventHandle(this.player_PlayerShoot);
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x06007995 RID: 31125 RVA: 0x0002DBF2 File Offset: 0x0002BDF2
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerShoot -= new PlayerEventHandle(this.player_PlayerShoot);
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x06007996 RID: 31126 RVA: 0x0028B4F0 File Offset: 0x002896F0
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0 && this.IsTrigger;
			if (flag)
			{
				this.IsTrigger = false;
				living.ControlBall = false;
				living.EffectTrigger = false;
				living.Game.sendShowPicSkil(living, base.Info, false);
				this.Stop();
			}
		}

		// Token: 0x06007997 RID: 31127 RVA: 0x0028B554 File Offset: 0x00289754
		public void player_PlayerShoot(Living living)
		{
			this.IsTrigger = false;
			bool flag = living.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.Info, true);
				this.IsTrigger = true;
				living.PetEffectTrigger = true;
				living.ControlBall = true;
				living.PetEffects.PetDelay = this.m_delay;
				bool isPetUseSkill = living.PetEffects.IsPetUseSkill;
				if (isPetUseSkill)
				{
					living.PetEffects.IsPetUseSkill = false;
					(living as Player).ControlBall = true;
				}
			}
		}

		// Token: 0x04004739 RID: 18233
		private int m_delay;

		// Token: 0x0400473A RID: 18234
		private int m_probability;

		// Token: 0x0400473B RID: 18235
		private int m_currentId;

		// Token: 0x0400473C RID: 18236
		private int m_count;
	}
}
