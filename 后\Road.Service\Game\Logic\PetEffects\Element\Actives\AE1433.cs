﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E2F RID: 3631
	public class AE1433 : BasePetEffect
	{
		// Token: 0x06007EAC RID: 32428 RVA: 0x002A178C File Offset: 0x0029F98C
		public AE1433(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1433, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007EAD RID: 32429 RVA: 0x002A180C File Offset: 0x0029FA0C
		public override bool Start(Living living)
		{
			AE1433 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1433) as AE1433;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007EAE RID: 32430 RVA: 0x000311E5 File Offset: 0x0002F3E5
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EAF RID: 32431 RVA: 0x000311FB File Offset: 0x0002F3FB
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EB0 RID: 32432 RVA: 0x002A186C File Offset: 0x0029FA6C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.PetEffects.CritRate = 25;
				player.BaseDamage += player.BaseDamage * 20.0 / 100.0;
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1433(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004D81 RID: 19841
		private int m_type = 0;

		// Token: 0x04004D82 RID: 19842
		private int m_count = 0;

		// Token: 0x04004D83 RID: 19843
		private int m_probability = 0;

		// Token: 0x04004D84 RID: 19844
		private int m_delay = 0;

		// Token: 0x04004D85 RID: 19845
		private int m_coldDown = 0;

		// Token: 0x04004D86 RID: 19846
		private int m_currentId;

		// Token: 0x04004D87 RID: 19847
		private int m_added = 0;
	}
}
