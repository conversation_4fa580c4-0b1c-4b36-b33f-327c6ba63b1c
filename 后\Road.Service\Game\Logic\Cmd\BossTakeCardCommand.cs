﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000EFB RID: 3835
	[GameCommand(130, "战胜关卡中Boss翻牌")]
	public class BossTakeCardCommand : ICommandHandler
	{
		// Token: 0x06008388 RID: 33672 RVA: 0x002B3F54 File Offset: 0x002B2154
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			int num = (int)packet.ReadByte();
			bool flag = game is PVEGame;
			if (flag)
			{
				PVEGame pvegame = game as PVEGame;
				bool flag2 = pvegame.BossCardCount > 0;
				if (flag2)
				{
					bool flag3 = num < 0 || num > pvegame.BossCards.Length;
					if (flag3)
					{
						pvegame.TakeBossCard(player);
					}
					else
					{
						pvegame.TakeBossCard(player, num);
					}
				}
			}
			else
			{
				bool flag4 = game is PVPGame && num == 100;
				if (flag4)
				{
					game.Stop();
				}
			}
		}
	}
}
