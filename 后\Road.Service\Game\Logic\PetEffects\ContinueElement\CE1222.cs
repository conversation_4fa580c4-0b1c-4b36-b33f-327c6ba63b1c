﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EA3 RID: 3747
	public class CE1222 : BasePetEffect
	{
		// Token: 0x06008175 RID: 33141 RVA: 0x002ACFD8 File Offset: 0x002AB1D8
		public CE1222(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1222, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008176 RID: 33142 RVA: 0x002AD058 File Offset: 0x002AB258
		public override bool Start(Living living)
		{
			CE1222 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1222) as CE1222;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008177 RID: 33143 RVA: 0x002AD0B8 File Offset: 0x002AB2B8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.SpeedMultX(0);
			player.Game.SendPlayerPicture(player, 23, false);
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008178 RID: 33144 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008179 RID: 33145 RVA: 0x002AD104 File Offset: 0x002AB304
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.SpeedMultX(3);
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.Game.SendPlayerPicture(player, 23, false);
		}

		// Token: 0x0600817A RID: 33146 RVA: 0x002AD154 File Offset: 0x002AB354
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x040050B2 RID: 20658
		private int m_type = 0;

		// Token: 0x040050B3 RID: 20659
		private int m_count = 0;

		// Token: 0x040050B4 RID: 20660
		private int m_probability = 0;

		// Token: 0x040050B5 RID: 20661
		private int m_delay = 0;

		// Token: 0x040050B6 RID: 20662
		private int m_coldDown = 0;

		// Token: 0x040050B7 RID: 20663
		private int m_currentId;

		// Token: 0x040050B8 RID: 20664
		private int m_added = 0;
	}
}
