﻿using System;
using Game.Logic.Actions;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CEE RID: 3310
	public class PE4853 : BasePetEffect
	{
		// Token: 0x060077FE RID: 30718 RVA: 0x0002C455 File Offset: 0x0002A655
		public PE4853(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE4853, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_value = 15;
			this.m_count = count;
		}

		// Token: 0x060077FF RID: 30719 RVA: 0x0028391C File Offset: 0x00281B1C
		public override bool Start(Living living)
		{
			PE4853 pe = living.PetEffectList.GetOfType(ePetEffectType.PE4853) as PE4853;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007800 RID: 30720 RVA: 0x0002C48E File Offset: 0x0002A68E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerUsePetMP += this.player_PlayerUsePetMP;
		}

		// Token: 0x06007801 RID: 30721 RVA: 0x0002C4A4 File Offset: 0x0002A6A4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerUsePetMP -= this.player_PlayerUsePetMP;
		}

		// Token: 0x06007802 RID: 30722 RVA: 0x0028397C File Offset: 0x00281B7C
		private bool canAdd(Living living)
		{
			PE4853 pe = living.PetEffectList.GetOfType(ePetEffectType.PE4853) as PE4853;
			PE4853 pe2 = living.PetEffectList.GetOfType(ePetEffectType.PE4853) as PE4853;
			bool flag = pe != null || pe2 != null;
			bool flag2;
			if (flag)
			{
				if (pe != null)
				{
					pe.Stop();
				}
				if (pe2 != null)
				{
					pe2.Stop();
				}
				flag2 = true;
			}
			else
			{
				flag2 = false;
			}
			return flag2;
		}

		// Token: 0x06007803 RID: 30723 RVA: 0x002839E8 File Offset: 0x00281BE8
		private void player_PlayerUsePetMP(Player player, int value)
		{
			int num = 0;
			num += value;
			bool flag = num >= 30;
			if (flag)
			{
				int num2 = player.MaxBlood * this.m_value / 100;
				this.m_addedBlood += num2;
				player.MaxBlood += num2;
				player.Game.SendGameUpdateHealth(player, num2, 0);
				player.Blood += num2;
				player.Game.SendGameUpdateHealth(player, num2, 0);
				bool flag2 = this.canAdd(player);
				if (flag2)
				{
					player.Blood += num2 * 2;
					player.Game.AddAction(new LivingSayAction(player, "增加血量上限", 9, 0, 300));
					player.Game.SendGameUpdateHealth(player, num2 * 2, 0);
				}
				player.Game.sendShowPicSkil(player, base.ElementInfo, true);
			}
		}

		// Token: 0x04004653 RID: 18003
		private int m_probability = 0;

		// Token: 0x04004654 RID: 18004
		private int m_value;

		// Token: 0x04004655 RID: 18005
		private int m_addedBlood;

		// Token: 0x04004656 RID: 18006
		private int m_count;
	}
}
