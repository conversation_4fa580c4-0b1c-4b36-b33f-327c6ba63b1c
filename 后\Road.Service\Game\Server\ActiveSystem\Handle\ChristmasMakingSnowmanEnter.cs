﻿using System;
using Game.Base.Packets;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C55 RID: 3157
	[ActiveSystemHandleAttbute(24)]
	public class ChristmasMakingSnowmanEnter : IActiveSystemCommandHadler
	{
		// Token: 0x0600702E RID: 28718 RVA: 0x0024DC80 File Offset: 0x0024BE80
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			UserChristmasInfo christmas = Player.Actives.Christmas;
			gspacketIn.WriteByte(24);
			gspacketIn.WriteInt(christmas.Count);
			gspacketIn.WriteInt(christmas.Exp);
			gspacketIn.WriteInt(christmas.AwardState);
			gspacketIn.WriteInt(christmas.PacksNumber);
			Player.Out.SendTCP(gspacketIn);
			return true;
		}
	}
}
