﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EF9 RID: 3833
	public class SealEffect : AbstractEffect
	{
		// Token: 0x0600837D RID: 33661 RVA: 0x0003461C File Offset: 0x0003281C
		public SealEffect(int count, int type)
			: base(eEffectType.SealEffect)
		{
			this.m_count = count;
			this.m_type = type;
		}

		// Token: 0x0600837E RID: 33662 RVA: 0x002B3DE0 File Offset: 0x002B1FE0
		public override bool Start(Living living)
		{
			SealEffect sealEffect = living.EffectList.GetOfType(eEffectType.SealEffect) as SealEffect;
			bool flag = sealEffect != null;
			bool flag2;
			if (flag)
			{
				sealEffect.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600837F RID: 33663 RVA: 0x00034636 File Offset: 0x00032836
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginFitting;
			living.SetSeal(true, this.m_type);
			this.m_living.Game.SendGameUpdateSealState(living, this.m_type);
		}

		// Token: 0x06008380 RID: 33664 RVA: 0x00034672 File Offset: 0x00032872
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginFitting;
			living.SetSeal(false, this.m_type);
			this.m_living.Game.SendGameUpdateSealState(living, this.m_type);
		}

		// Token: 0x06008381 RID: 33665 RVA: 0x002B3E28 File Offset: 0x002B2028
		private void player_BeginFitting(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0400521C RID: 21020
		private int m_count;

		// Token: 0x0400521D RID: 21021
		private int m_type;
	}
}
