﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E60 RID: 3680
	public class CE1067 : BasePetEffect
	{
		// Token: 0x06007FCA RID: 32714 RVA: 0x002A6918 File Offset: 0x002A4B18
		public CE1067(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1067, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FCB RID: 32715 RVA: 0x002A6998 File Offset: 0x002A4B98
		public override bool Start(Living living)
		{
			CE1067 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1067) as CE1067;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FCC RID: 32716 RVA: 0x002A69F8 File Offset: 0x002A4BF8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
			this.IsTrigger = true;
		}

		// Token: 0x06007FCD RID: 32717 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FCE RID: 32718 RVA: 0x002A6A48 File Offset: 0x002A4C48
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = !this.IsTrigger;
			if (!flag)
			{
				this.m_added = 400;
				target.SyncAtTime = true;
				target.AddBlood(-this.m_added, 1);
				target.SyncAtTime = false;
				bool flag2 = target.Blood <= 0;
				if (flag2)
				{
					target.Die();
					bool flag3 = living != null && living is Player;
					if (flag3)
					{
						(living as Player).PlayerDetail.OnKillingLiving(living.Game, 2, target.Id, target.IsLiving, this.m_added);
					}
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007FCF RID: 32719 RVA: 0x002A6AF4 File Offset: 0x002A4CF4
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			this.IsTrigger = true;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.m_added = 0;
				this.Stop();
			}
		}

		// Token: 0x06007FD0 RID: 32720 RVA: 0x00031C19 File Offset: 0x0002FE19
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x04004EDB RID: 20187
		private int m_type = 0;

		// Token: 0x04004EDC RID: 20188
		private int m_count = 0;

		// Token: 0x04004EDD RID: 20189
		private int m_probability = 0;

		// Token: 0x04004EDE RID: 20190
		private int m_delay = 0;

		// Token: 0x04004EDF RID: 20191
		private int m_coldDown = 0;

		// Token: 0x04004EE0 RID: 20192
		private int m_currentId;

		// Token: 0x04004EE1 RID: 20193
		private int m_added = 0;
	}
}
