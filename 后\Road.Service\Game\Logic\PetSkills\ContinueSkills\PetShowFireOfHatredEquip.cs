﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D1A RID: 3354
	public class PetShowFireOfHatredEquip : BasePetEffect
	{
		// Token: 0x060078E6 RID: 30950 RVA: 0x0002D02F File Offset: 0x0002B22F
		public PetShowFireOfHatredEquip(int count, string elementID)
			: base(ePetEffectType.PetShowFireOfHatredEquip, elementID)
		{
			this.m_count = count;
		}

		// Token: 0x060078E7 RID: 30951 RVA: 0x00288A04 File Offset: 0x00286C04
		public override bool Start(Living living)
		{
			PetShowFireOfHatredEquip petShowFireOfHatredEquip = living.PetEffectList.GetOfType(ePetEffectType.PetShowFireOfHatredEquip) as PetShowFireOfHatredEquip;
			bool flag = petShowFireOfHatredEquip != null;
			bool flag2;
			if (flag)
			{
				petShowFireOfHatredEquip.m_count += this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078E8 RID: 30952 RVA: 0x0002D046 File Offset: 0x0002B246
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x060078E9 RID: 30953 RVA: 0x0002D05C File Offset: 0x0002B25C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.sendShowPicSkil(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x060078EA RID: 30954 RVA: 0x00288A54 File Offset: 0x00286C54
		private void player_BeginSelfTurn(Living living)
		{
			living.Game.sendShowPicSkil(living, base.ElementInfo, false);
			this.m_count--;
			bool flag = base.ElementInfo.Pic != -1;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
			}
			bool flag2 = this.m_count < 0;
			if (flag2)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
			else
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
			}
		}

		// Token: 0x040046DB RID: 18139
		private int m_count;
	}
}
