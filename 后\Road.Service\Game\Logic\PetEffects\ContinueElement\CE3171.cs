﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EC1 RID: 3777
	public class CE3171 : BasePetEffect
	{
		// Token: 0x06008239 RID: 33337 RVA: 0x002AFFA8 File Offset: 0x002AE1A8
		public CE3171(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE3171, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600823A RID: 33338 RVA: 0x002B0024 File Offset: 0x002AE224
		public override bool Start(Living living)
		{
			CE3171 ce = living.PetEffectList.GetOfType(ePetEffectType.CE3171) as CE3171;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600823B RID: 33339 RVA: 0x0003330F File Offset: 0x0003150F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600823C RID: 33340 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600823D RID: 33341 RVA: 0x002B0080 File Offset: 0x002AE280
		private void Player_BeginNextTurn(Living living)
		{
			Player player = living as Player;
			bool flag = living is Player;
			if (flag)
			{
				player.Dander = 0;
				player.Game.SendGameUpdateDander(player);
			}
		}

		// Token: 0x0600823E RID: 33342 RVA: 0x002B00BC File Offset: 0x002AE2BC
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600823F RID: 33343 RVA: 0x0003334B File Offset: 0x0003154B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005185 RID: 20869
		private int m_type = 0;

		// Token: 0x04005186 RID: 20870
		private int m_count = 0;

		// Token: 0x04005187 RID: 20871
		private int m_probability = 0;

		// Token: 0x04005188 RID: 20872
		private int m_delay = 0;

		// Token: 0x04005189 RID: 20873
		private int m_coldDown = 0;

		// Token: 0x0400518A RID: 20874
		private int m_currentId;

		// Token: 0x0400518B RID: 20875
		private int m_added = 0;
	}
}
