﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D95 RID: 3477
	public class AE1022 : BasePetEffect
	{
		// Token: 0x06007B83 RID: 31619 RVA: 0x002933F0 File Offset: 0x002915F0
		public AE1022(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1022, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B84 RID: 31620 RVA: 0x00293470 File Offset: 0x00291670
		public override bool Start(Living living)
		{
			AE1022 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1022) as AE1022;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B85 RID: 31621 RVA: 0x0002F22F File Offset: 0x0002D42F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.PlayerAnyShellThrow += this.Player_PlayerAnyShellThrow;
		}

		// Token: 0x06007B86 RID: 31622 RVA: 0x002934D0 File Offset: 0x002916D0
		private void Player_PlayerAnyShellThrow(Player player)
		{
			bool flag = !this.IsTrigger;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					bool flag2 = player2.PlayerDetail != player.PlayerDetail;
					if (flag2)
					{
						player2.Game.SendPetBuff(player2, base.ElementInfo, true);
						player2.Game.SendPlayerPicture(player2, 30, true);
						player2.AddPetEffect(new CE1022(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
					}
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007B87 RID: 31623 RVA: 0x0002F258 File Offset: 0x0002D458
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.PlayerAnyShellThrow -= this.Player_PlayerAnyShellThrow;
		}

		// Token: 0x06007B88 RID: 31624 RVA: 0x002935C0 File Offset: 0x002917C0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x0400494D RID: 18765
		private int m_type = 0;

		// Token: 0x0400494E RID: 18766
		private int m_count = 0;

		// Token: 0x0400494F RID: 18767
		private int m_probability = 0;

		// Token: 0x04004950 RID: 18768
		private int m_delay = 0;

		// Token: 0x04004951 RID: 18769
		private int m_coldDown = 0;

		// Token: 0x04004952 RID: 18770
		private int m_currentId;

		// Token: 0x04004953 RID: 18771
		private int m_added = 0;
	}
}
