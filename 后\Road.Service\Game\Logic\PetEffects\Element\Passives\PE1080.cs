﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D5E RID: 3422
	public class PE1080 : BasePetEffect
	{
		// Token: 0x06007A6C RID: 31340 RVA: 0x0028ECD4 File Offset: 0x0028CED4
		public PE1080(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1080, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A6D RID: 31341 RVA: 0x0028ED54 File Offset: 0x0028CF54
		public override bool Start(Living living)
		{
			PE1080 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1080) as PE1080;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A6E RID: 31342 RVA: 0x0002E72A File Offset: 0x0002C92A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerShootCure += this.Player_AfterPlayerShootCure;
		}

		// Token: 0x06007A6F RID: 31343 RVA: 0x0028EDB4 File Offset: 0x0028CFB4
		private void Player_AfterPlayerShootCure(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.SyncAtTime = true;
					player2.AddBlood(300);
					player2.SyncAtTime = false;
				}
			}
		}

		// Token: 0x06007A70 RID: 31344 RVA: 0x0002E740 File Offset: 0x0002C940
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerShootCure -= this.Player_AfterPlayerShootCure;
		}

		// Token: 0x040047D2 RID: 18386
		private int m_type = 0;

		// Token: 0x040047D3 RID: 18387
		private int m_count = 0;

		// Token: 0x040047D4 RID: 18388
		private int m_probability = 0;

		// Token: 0x040047D5 RID: 18389
		private int m_delay = 0;

		// Token: 0x040047D6 RID: 18390
		private int m_coldDown = 0;

		// Token: 0x040047D7 RID: 18391
		private int m_currentId;

		// Token: 0x040047D8 RID: 18392
		private int m_added = 0;
	}
}
