﻿using System;
using Game.Logic.Effects;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F5B RID: 3931
	public class LivingOffSealAction : BaseAction
	{
		// Token: 0x0600850E RID: 34062 RVA: 0x0003545A File Offset: 0x0003365A
		public LivingOffSealAction(Living Living, Living target, int delay)
			: base(delay, 1000)
		{
			this.m_Living = Living;
			this.m_Target = target;
		}

		// Token: 0x0600850F RID: 34063 RVA: 0x00035478 File Offset: 0x00033678
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			SealEffect sealEffect = (SealEffect)this.m_Target.EffectList.GetOfType(eEffectType.SealEffect);
			if (sealEffect != null)
			{
				sealEffect.Stop();
			}
			base.Finish(tick);
		}

		// Token: 0x0400530C RID: 21260
		private Living m_Living;

		// Token: 0x0400530D RID: 21261
		private Living m_Target;
	}
}
