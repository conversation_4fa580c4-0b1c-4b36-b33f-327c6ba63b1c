﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EAF RID: 3759
	public class CE1247 : BasePetEffect
	{
		// Token: 0x060081C3 RID: 33219 RVA: 0x002AE1F8 File Offset: 0x002AC3F8
		public CE1247(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1247, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081C4 RID: 33220 RVA: 0x002AE278 File Offset: 0x002AC478
		public override bool Start(Living living)
		{
			CE1247 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1247) as CE1247;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081C5 RID: 33221 RVA: 0x00032ED6 File Offset: 0x000310D6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081C6 RID: 33222 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081C7 RID: 33223 RVA: 0x002AE2D8 File Offset: 0x002AC4D8
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			living.Game.SendPetBuff(living, base.ElementInfo, true);
			this.m_added = 750;
			damageAmount -= this.m_added;
			bool flag = damageAmount < 0;
			if (flag)
			{
				damageAmount = 1;
			}
		}

		// Token: 0x060081C8 RID: 33224 RVA: 0x002AE320 File Offset: 0x002AC520
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060081C9 RID: 33225 RVA: 0x00032F12 File Offset: 0x00031112
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
		}

		// Token: 0x04005106 RID: 20742
		private int m_type = 0;

		// Token: 0x04005107 RID: 20743
		private int m_count = 0;

		// Token: 0x04005108 RID: 20744
		private int m_probability = 0;

		// Token: 0x04005109 RID: 20745
		private int m_delay = 0;

		// Token: 0x0400510A RID: 20746
		private int m_coldDown = 0;

		// Token: 0x0400510B RID: 20747
		private int m_currentId;

		// Token: 0x0400510C RID: 20748
		private int m_added = 0;
	}
}
