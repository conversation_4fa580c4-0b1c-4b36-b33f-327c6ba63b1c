﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EED RID: 3821
	public class InvinciblyEffect : AbstractEffect
	{
		// Token: 0x0600833C RID: 33596 RVA: 0x000341D5 File Offset: 0x000323D5
		public InvinciblyEffect(int count)
			: base(eEffectType.InvinciblyEffect)
		{
			this.m_count = count;
		}

		// Token: 0x0600833D RID: 33597 RVA: 0x002B3424 File Offset: 0x002B1624
		public override bool Start(Living living)
		{
			InvinciblyEffect invinciblyEffect = living.EffectList.GetOfType(eEffectType.InvinciblyEffect) as InvinciblyEffect;
			bool flag = invinciblyEffect != null;
			bool flag2;
			if (flag)
			{
				invinciblyEffect.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600833E RID: 33598 RVA: 0x000341E8 File Offset: 0x000323E8
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginFitting;
		}

		// Token: 0x0600833F RID: 33599 RVA: 0x000341FE File Offset: 0x000323FE
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginFitting;
		}

		// Token: 0x06008340 RID: 33600 RVA: 0x002B346C File Offset: 0x002B166C
		private void player_BeginFitting(Living player)
		{
			this.m_count--;
			bool flag = this.m_count <= 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x04005207 RID: 20999
		private int m_count;
	}
}
