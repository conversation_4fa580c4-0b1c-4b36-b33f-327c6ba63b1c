﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F30 RID: 3888
	public class LegendaryWeaponEffect : BaseCardEffect
	{
		// Token: 0x06008454 RID: 33876 RVA: 0x002B7A5C File Offset: 0x002B5C5C
		public LegendaryWeaponEffect(int index, CardBuffInfo info)
			: base(eCardEffectType.LegendaryWeaponDeck, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008455 RID: 33877 RVA: 0x002B7ACC File Offset: 0x002B5CCC
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.LegendaryWeaponDeck) is LegendaryWeaponEffect;
			return flag || base.Start(living);
		}

		// Token: 0x06008456 RID: 33878 RVA: 0x00034DD2 File Offset: 0x00032FD2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x06008457 RID: 33879 RVA: 0x00034DE8 File Offset: 0x00032FE8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x06008458 RID: 33880 RVA: 0x002B7B04 File Offset: 0x002B5D04
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.Attack -= (double)this.m_added;
				player.Agility -= (double)this.m_added;
				player.Lucky -= (double)this.m_added;
				player.Defence -= (double)this.m_added;
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVPGame && (player.Game as PVPGame).IsMatchOrFreedom();
			if (flag2)
			{
				this.m_added = this.m_value;
				player.Attack += (double)this.m_added;
				player.Agility += (double)this.m_added;
				player.Lucky += (double)this.m_added;
				player.Defence += (double)this.m_added;
			}
		}

		// Token: 0x04005284 RID: 21124
		private int m_indexValue = 0;

		// Token: 0x04005285 RID: 21125
		private int m_value = 0;

		// Token: 0x04005286 RID: 21126
		private int m_added = 0;
	}
}
