﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E65 RID: 3685
	public class CE1095 : BasePetEffect
	{
		// Token: 0x06007FEA RID: 32746 RVA: 0x002A7250 File Offset: 0x002A5450
		public CE1095(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1095, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FEB RID: 32747 RVA: 0x002A72D0 File Offset: 0x002A54D0
		public override bool Start(Living living)
		{
			CE1095 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1095) as CE1095;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FEC RID: 32748 RVA: 0x002A7330 File Offset: 0x002A5530
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				player.Defence += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007FED RID: 32749 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FEE RID: 32750 RVA: 0x002A7394 File Offset: 0x002A5594
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007FEF RID: 32751 RVA: 0x00031CC4 File Offset: 0x0002FEC4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Defence -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004EFE RID: 20222
		private int m_type = 0;

		// Token: 0x04004EFF RID: 20223
		private int m_count = 0;

		// Token: 0x04004F00 RID: 20224
		private int m_probability = 0;

		// Token: 0x04004F01 RID: 20225
		private int m_delay = 0;

		// Token: 0x04004F02 RID: 20226
		private int m_coldDown = 0;

		// Token: 0x04004F03 RID: 20227
		private int m_currentId;

		// Token: 0x04004F04 RID: 20228
		private int m_added = 0;
	}
}
