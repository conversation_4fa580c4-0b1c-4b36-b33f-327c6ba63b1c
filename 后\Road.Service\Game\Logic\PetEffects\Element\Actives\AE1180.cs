﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DEB RID: 3563
	public class AE1180 : BasePetEffect
	{
		// Token: 0x06007D46 RID: 32070 RVA: 0x0029B24C File Offset: 0x0029944C
		public AE1180(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1180, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D47 RID: 32071 RVA: 0x0029B2CC File Offset: 0x002994CC
		public override bool Start(Living living)
		{
			AE1180 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1180) as AE1180;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D48 RID: 32072 RVA: 0x00030392 File Offset: 0x0002E592
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D49 RID: 32073 RVA: 0x000303A8 File Offset: 0x0002E5A8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D4A RID: 32074 RVA: 0x0029B32C File Offset: 0x0029952C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1180(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004BA5 RID: 19365
		private int m_type = 0;

		// Token: 0x04004BA6 RID: 19366
		private int m_count = 0;

		// Token: 0x04004BA7 RID: 19367
		private int m_probability = 0;

		// Token: 0x04004BA8 RID: 19368
		private int m_delay = 0;

		// Token: 0x04004BA9 RID: 19369
		private int m_coldDown = 0;

		// Token: 0x04004BAA RID: 19370
		private int m_currentId;

		// Token: 0x04004BAB RID: 19371
		private int m_added = 0;
	}
}
