﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E00 RID: 3584
	public class AE1207 : BasePetEffect
	{
		// Token: 0x06007DB3 RID: 32179 RVA: 0x0029D264 File Offset: 0x0029B464
		public AE1207(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1207, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DB4 RID: 32180 RVA: 0x0029D2E4 File Offset: 0x0029B4E4
		public override bool Start(Living living)
		{
			AE1207 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1207) as AE1207;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DB5 RID: 32181 RVA: 0x000307B3 File Offset: 0x0002E9B3
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DB6 RID: 32182 RVA: 0x000307C9 File Offset: 0x0002E9C9
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DB7 RID: 32183 RVA: 0x0029D344 File Offset: 0x0029B544
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.AddPetEffect(new CE1207(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004C38 RID: 19512
		private int m_type = 0;

		// Token: 0x04004C39 RID: 19513
		private int m_count = 0;

		// Token: 0x04004C3A RID: 19514
		private int m_probability = 0;

		// Token: 0x04004C3B RID: 19515
		private int m_delay = 0;

		// Token: 0x04004C3C RID: 19516
		private int m_coldDown = 0;

		// Token: 0x04004C3D RID: 19517
		private int m_currentId;

		// Token: 0x04004C3E RID: 19518
		private int m_added = 0;
	}
}
