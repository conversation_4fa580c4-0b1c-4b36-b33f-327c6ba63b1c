﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E51 RID: 3665
	public class CE1034 : BasePetEffect
	{
		// Token: 0x06007F73 RID: 32627 RVA: 0x002A52A8 File Offset: 0x002A34A8
		public CE1034(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1034, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F74 RID: 32628 RVA: 0x002A5324 File Offset: 0x002A3524
		public override bool Start(Living living)
		{
			CE1034 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1034) as CE1034;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F75 RID: 32629 RVA: 0x0003195E File Offset: 0x0002FB5E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F76 RID: 32630 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F77 RID: 32631 RVA: 0x002A5380 File Offset: 0x002A3580
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			this.m_added = 300;
			damageAmount -= this.m_added;
			bool flag = damageAmount < 0;
			if (flag)
			{
				damageAmount = 1;
			}
		}

		// Token: 0x06007F78 RID: 32632 RVA: 0x002A53B4 File Offset: 0x002A35B4
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F79 RID: 32633 RVA: 0x0003199A File Offset: 0x0002FB9A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
		}

		// Token: 0x04004E72 RID: 20082
		private int m_type = 0;

		// Token: 0x04004E73 RID: 20083
		private int m_count = 0;

		// Token: 0x04004E74 RID: 20084
		private int m_probability = 0;

		// Token: 0x04004E75 RID: 20085
		private int m_delay = 0;

		// Token: 0x04004E76 RID: 20086
		private int m_coldDown = 0;

		// Token: 0x04004E77 RID: 20087
		private int m_currentId;

		// Token: 0x04004E78 RID: 20088
		private int m_added = 0;
	}
}
