﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E15 RID: 3605
	public class AE1243 : BasePetEffect
	{
		// Token: 0x06007E1E RID: 32286 RVA: 0x0029F0E0 File Offset: 0x0029D2E0
		public AE1243(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1243, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E1F RID: 32287 RVA: 0x0029F160 File Offset: 0x0029D360
		public override bool Start(Living living)
		{
			AE1243 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1243) as AE1243;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E20 RID: 32288 RVA: 0x00030BA5 File Offset: 0x0002EDA5
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E21 RID: 32289 RVA: 0x00030BBB File Offset: 0x0002EDBB
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E22 RID: 32290 RVA: 0x0029F1C0 File Offset: 0x0029D3C0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && player.Game is PVPGame;
			if (flag)
			{
				player.AddPetEffect(new CE1243(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004CCB RID: 19659
		private int m_type = 0;

		// Token: 0x04004CCC RID: 19660
		private int m_count = 0;

		// Token: 0x04004CCD RID: 19661
		private int m_probability = 0;

		// Token: 0x04004CCE RID: 19662
		private int m_delay = 0;

		// Token: 0x04004CCF RID: 19663
		private int m_coldDown = 0;

		// Token: 0x04004CD0 RID: 19664
		private int m_currentId;

		// Token: 0x04004CD1 RID: 19665
		private int m_added = 0;
	}
}
