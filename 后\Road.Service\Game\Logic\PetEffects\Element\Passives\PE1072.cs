﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D56 RID: 3414
	public class PE1072 : BasePetEffect
	{
		// Token: 0x06007A3E RID: 31294 RVA: 0x0028E100 File Offset: 0x0028C300
		public PE1072(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1072, elementID)
		{
			this.m_count = count;
			this.m_coldDown = 0;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A3F RID: 31295 RVA: 0x0028E17C File Offset: 0x0028C37C
		public override bool Start(Living living)
		{
			PE1072 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1072) as PE1072;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A40 RID: 31296 RVA: 0x0002E505 File Offset: 0x0002C705
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
			player.AfterKilledByLiving += this.zplayer_AfterKilledByLiving;
		}

		// Token: 0x06007A41 RID: 31297 RVA: 0x0002E52E File Offset: 0x0002C72E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
			player.AfterKilledByLiving -= this.zplayer_AfterKilledByLiving;
		}

		// Token: 0x06007A42 RID: 31298 RVA: 0x0028E1D8 File Offset: 0x0028C3D8
		private void player_BeginSelfTurn(Living liv)
		{
			bool flag = this.m_coldDown < 5;
			if (flag)
			{
				bool flag2 = this.m_added == 0;
				if (flag2)
				{
					this.m_added = 15;
				}
				Player player = liv as Player;
				player.Game.SendPetBuff(liv, base.ElementInfo, true);
				player.BaseDamage += (double)this.m_added;
				this.m_count += this.m_added;
				this.m_coldDown++;
			}
		}

		// Token: 0x06007A43 RID: 31299 RVA: 0x0002E557 File Offset: 0x0002C757
		private void zplayer_AfterKilledByLiving(Living liv, Living target, int damageAmount, int criticalAmount)
		{
			this.ExitEffect(liv);
		}

		// Token: 0x06007A44 RID: 31300 RVA: 0x0028E25C File Offset: 0x0028C45C
		private void ExitEffect(Living liv)
		{
			bool flag = this.m_coldDown > 0;
			if (flag)
			{
				Player player = liv as Player;
				player.BaseDamage -= (double)this.m_added;
				player.Game.SendPetBuff(player, base.ElementInfo, false);
				this.m_coldDown = 0;
				this.m_added = 0;
			}
		}

		// Token: 0x0400479A RID: 18330
		private int m_type = 0;

		// Token: 0x0400479B RID: 18331
		private int m_count = 0;

		// Token: 0x0400479C RID: 18332
		private int m_probability = 0;

		// Token: 0x0400479D RID: 18333
		private int m_delay = 0;

		// Token: 0x0400479E RID: 18334
		private int m_coldDown = 0;

		// Token: 0x0400479F RID: 18335
		private int m_currentId;

		// Token: 0x040047A0 RID: 18336
		private int m_added = 0;
	}
}
