﻿using System;
using System.Collections.Generic;
using System.Reflection;
using log4net;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C4C RID: 3148
	public class ActiveSystemHandleMgr
	{
		// Token: 0x06007017 RID: 28695 RVA: 0x0024D32C File Offset: 0x0024B52C
		public IActiveSystemCommandHadler LoadCommandHandler(int code)
		{
			bool flag = this.handles.ContainsKey(code);
			IActiveSystemCommandHadler activeSystemCommandHadler;
			if (flag)
			{
				activeSystemCommandHadler = this.handles[code];
			}
			else
			{
				ActiveSystemHandleMgr.log.Error("LoadCommandHandler code002: " + code.ToString());
				activeSystemCommandHadler = null;
			}
			return activeSystemCommandHadler;
		}

		// Token: 0x06007018 RID: 28696 RVA: 0x0002A2EF File Offset: 0x000284EF
		public ActiveSystemHandleMgr()
		{
			this.handles.Clear();
			this.SearchCommandHandlers(Assembly.GetAssembly(typeof(GameServer)));
		}

		// Token: 0x06007019 RID: 28697 RVA: 0x0024D37C File Offset: 0x0024B57C
		protected int SearchCommandHandlers(Assembly assembly)
		{
			int num = 0;
			Type[] types = assembly.GetTypes();
			Type[] array = types;
			Type[] array2 = array;
			foreach (Type type in array2)
			{
				bool flag = type.IsClass && !(type.GetInterface("Game.Server.ActiveSystem.Handle.IActiveSystemCommandHadler") == null);
				if (flag)
				{
					ActiveSystemHandleAttbute[] array4 = (ActiveSystemHandleAttbute[])type.GetCustomAttributes(typeof(ActiveSystemHandleAttbute), true);
					bool flag2 = array4.Length != 0;
					if (flag2)
					{
						num++;
						this.RegisterCommandHandler((int)array4[0].Code, Activator.CreateInstance(type) as IActiveSystemCommandHadler);
					}
				}
			}
			return num;
		}

		// Token: 0x0600701A RID: 28698 RVA: 0x0002A326 File Offset: 0x00028526
		protected void RegisterCommandHandler(int code, IActiveSystemCommandHadler handle)
		{
			this.handles.Add(code, handle);
		}

		// Token: 0x04003C4C RID: 15436
		private Dictionary<int, IActiveSystemCommandHadler> handles = new Dictionary<int, IActiveSystemCommandHadler>();

		// Token: 0x04003C4D RID: 15437
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
	}
}
