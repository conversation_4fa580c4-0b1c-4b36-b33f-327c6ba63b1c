﻿using System;

namespace Game.Logic.Phy.Maths
{
	// Token: 0x02000CE4 RID: 3300
	public class EulerVector
	{
		// Token: 0x060077A6 RID: 30630 RVA: 0x0002C1E2 File Offset: 0x0002A3E2
		public EulerVector(int x0, int x1, float x2)
		{
			this.x0 = (float)x0;
			this.x1 = (float)x1;
			this.x2 = x2;
		}

		// Token: 0x060077A7 RID: 30631 RVA: 0x0002C203 File Offset: 0x0002A403
		public void clear()
		{
			this.x0 = 0f;
			this.x1 = 0f;
			this.x2 = 0f;
		}

		// Token: 0x060077A8 RID: 30632 RVA: 0x0002C227 File Offset: 0x0002A427
		public void clearMotion()
		{
			this.x1 = 0f;
			this.x2 = 0f;
		}

		// Token: 0x060077A9 RID: 30633 RVA: 0x002814D0 File Offset: 0x0027F6D0
		public void ComputeOneEulerStep(float m, float af, float f, float dt)
		{
			this.x2 = (f - af * this.x1) / m;
			this.x1 += this.x2 * dt;
			this.x0 += this.x1 * dt;
		}

		// Token: 0x060077AA RID: 30634 RVA: 0x0028151C File Offset: 0x0027F71C
		public string toString()
		{
			return string.Concat(new string[]
			{
				"x:",
				this.x0.ToString(),
				",v:",
				this.x1.ToString(),
				",a",
				this.x2.ToString()
			});
		}

		// Token: 0x04004611 RID: 17937
		public float x0;

		// Token: 0x04004612 RID: 17938
		public float x1;

		// Token: 0x04004613 RID: 17939
		public float x2;
	}
}
