﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E4B RID: 3659
	public class CE1025 : BasePetEffect
	{
		// Token: 0x06007F4D RID: 32589 RVA: 0x002A4914 File Offset: 0x002A2B14
		public CE1025(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1025, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F4E RID: 32590 RVA: 0x002A4994 File Offset: 0x002A2B94
		public override bool Start(Living living)
		{
			CE1025 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1025) as CE1025;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F4F RID: 32591 RVA: 0x002A49F4 File Offset: 0x002A2BF4
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 200;
				player.BaseDamage += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F50 RID: 32592 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F51 RID: 32593 RVA: 0x002A4A58 File Offset: 0x002A2C58
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F52 RID: 32594 RVA: 0x002A4A8C File Offset: 0x002A2C8C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Game.SendPlayerPicture(player, 29, false);
			player.BaseDamage -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E48 RID: 20040
		private int m_type = 0;

		// Token: 0x04004E49 RID: 20041
		private int m_count = 0;

		// Token: 0x04004E4A RID: 20042
		private int m_probability = 0;

		// Token: 0x04004E4B RID: 20043
		private int m_delay = 0;

		// Token: 0x04004E4C RID: 20044
		private int m_coldDown = 0;

		// Token: 0x04004E4D RID: 20045
		private int m_currentId;

		// Token: 0x04004E4E RID: 20046
		private int m_added = 0;
	}
}
