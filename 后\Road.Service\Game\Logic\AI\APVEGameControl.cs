﻿using System;

namespace Game.Logic.AI
{
	// Token: 0x02000F3E RID: 3902
	public abstract class APVEGameControl
	{
		// Token: 0x170014B0 RID: 5296
		// (get) Token: 0x060084C5 RID: 33989 RVA: 0x002B8BF0 File Offset: 0x002B6DF0
		// (set) Token: 0x060084C6 RID: 33990 RVA: 0x00035040 File Offset: 0x00033240
		public PVEGame Game
		{
			get
			{
				return this.m_game;
			}
			set
			{
				this.m_game = value;
			}
		}

		// Token: 0x060084C7 RID: 33991 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnCreated()
		{
		}

		// Token: 0x060084C8 RID: 33992 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnPrepated()
		{
		}

		// Token: 0x060084C9 RID: 33993 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnGameOverAllSession()
		{
		}

		// Token: 0x060084CA RID: 33994 RVA: 0x00052AE4 File Offset: 0x00050CE4
		public virtual int CalculateScoreGrade(int score)
		{
			return 0;
		}

		// Token: 0x060084CB RID: 33995 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void Dispose()
		{
		}

		// Token: 0x040052AC RID: 21164
		protected PVEGame m_game;
	}
}
