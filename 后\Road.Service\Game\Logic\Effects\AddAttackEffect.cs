﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000ECF RID: 3791
	public class AddAttackEffect : BasePlayerEffect
	{
		// Token: 0x0600828B RID: 33419 RVA: 0x0003360E File Offset: 0x0003180E
		public AddAttackEffect(int count, int probability)
			: base(eEffectType.AddAttackEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x0600828C RID: 33420 RVA: 0x002B1080 File Offset: 0x002AF280
		public override bool Start(Living living)
		{
			AddAttackEffect addAttackEffect = living.EffectList.GetOfType(eEffectType.AddAttackEffect) as AddAttackEffect;
			bool flag = addAttackEffect != null;
			bool flag2;
			if (flag)
			{
				this.m_probability = ((this.m_probability > addAttackEffect.m_probability) ? this.m_probability : addAttackEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600828D RID: 33421 RVA: 0x0003363C File Offset: 0x0003183C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.ChangeProperty;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x0600828E RID: 33422 RVA: 0x000335DB File Offset: 0x000317DB
		private void player_AfterPlayerShooted(Player player)
		{
			player.FlyingPartical = 0;
		}

		// Token: 0x0600828F RID: 33423 RVA: 0x00033665 File Offset: 0x00031865
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.ChangeProperty;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06008290 RID: 33424 RVA: 0x002B10DC File Offset: 0x002AF2DC
		private void ChangeProperty(Player player, int ball)
		{
			player.Attack -= (double)this.m_added;
			this.m_added = 0;
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				player.FlyingPartical = 65;
				this.IsTrigger = true;
				player.AttackEffectTrigger = true;
				player.Attack += (double)this.m_count;
				this.m_added = this.m_count;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("AddAttackEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051C2 RID: 20930
		private int m_count = 0;

		// Token: 0x040051C3 RID: 20931
		private int m_probability = 0;

		// Token: 0x040051C4 RID: 20932
		private int m_added = 0;
	}
}
