﻿using System;
using System.CodeDom.Compiler;
using System.Configuration;
using System.Runtime.CompilerServices;

namespace Game.Manager.Properties
{
	// Token: 0x02000C7A RID: 3194
	[CompilerGenerated]
	[GeneratedCode("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "11.0.0.0")]
	internal sealed partial class Settings : ApplicationSettingsBase
	{
		// Token: 0x1700132E RID: 4910
		// (get) Token: 0x060070EF RID: 28911 RVA: 0x0002A838 File Offset: 0x00028A38
		public static Settings Default
		{
			get
			{
				return Settings.defaultInstance;
			}
		}

		// Token: 0x04003C67 RID: 15463
		private static Settings defaultInstance = (Settings)SettingsBase.Synchronized(new Settings());
	}
}
