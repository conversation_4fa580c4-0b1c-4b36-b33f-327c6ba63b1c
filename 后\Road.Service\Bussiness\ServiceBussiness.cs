﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using SqlDataProvider.Data;

namespace Bussiness
{
	// Token: 0x02000FB8 RID: 4024
	public class ServiceBussiness : BaseBussiness
	{
		// Token: 0x06008A33 RID: 35379 RVA: 0x002F57C8 File Offset: 0x002F39C8
		public ServerInfo GetServiceSingle(int ID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", SqlDbType.Int, 4)
				};
				array[0].Value = ID;
				this.db.GetReader(ref sqlDataReader, "SP_Service_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return new ServerInfo
					{
						ID = (int)sqlDataReader["ID"],
						IP = sqlDataReader["IP"].ToString(),
						Name = sqlDataReader["Name"].ToString(),
						Online = (int)sqlDataReader["Online"],
						Port = (int)sqlDataReader["Port"],
						Remark = sqlDataReader["Remark"].ToString(),
						Room = (int)sqlDataReader["Room"],
						State = (int)sqlDataReader["State"],
						Total = (int)sqlDataReader["Total"],
						RSA = sqlDataReader["RSA"].ToString(),
						NewerServer = (bool)sqlDataReader["NewerServer"],
						AreaId = (int)sqlDataReader["AreaId"]
					};
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x06008A34 RID: 35380 RVA: 0x002F59CC File Offset: 0x002F3BCC
		public ServerInfo[] GetServerList()
		{
			List<ServerInfo> list = new List<ServerInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Service_List");
				while (sqlDataReader.Read())
				{
					list.Add(new ServerInfo
					{
						ID = (int)sqlDataReader["ID"],
						IP = sqlDataReader["IP"].ToString(),
						Name = sqlDataReader["Name"].ToString(),
						Online = (int)sqlDataReader["Online"],
						Port = (int)sqlDataReader["Port"],
						Remark = sqlDataReader["Remark"].ToString(),
						Room = (int)sqlDataReader["Room"],
						State = (int)sqlDataReader["State"],
						Total = (int)sqlDataReader["Total"],
						RSA = sqlDataReader["RSA"].ToString(),
						MustLevel = (int)sqlDataReader["MustLevel"],
						LowestLevel = (int)sqlDataReader["LowestLevel"],
						AreaId = (int)sqlDataReader["AreaId"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A35 RID: 35381 RVA: 0x002F5BD4 File Offset: 0x002F3DD4
		public bool UpdateService(ServerInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@Online", info.Online),
					new SqlParameter("@State", info.State)
				};
				flag = this.db.RunProcedure("SP_Service_Update", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008A36 RID: 35382 RVA: 0x002F5C88 File Offset: 0x002F3E88
		public ServerProperty[] GetAllServerConfig()
		{
			List<ServerProperty> list = new List<ServerProperty>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Server_Config");
				while (sqlDataReader.Read())
				{
					list.Add(new ServerProperty
					{
						Key = sqlDataReader["Name"].ToString(),
						Value = sqlDataReader["Value"].ToString()
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetServerConfig", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A37 RID: 35383 RVA: 0x002F5D74 File Offset: 0x002F3F74
		public ServerProperty GetServerPropertyByKey(string key)
		{
			ServerProperty serverProperty = null;
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@Key", key)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Server_Config_Single", array);
				while (sqlDataReader.Read())
				{
					serverProperty = new ServerProperty();
					serverProperty.Key = sqlDataReader["Name"].ToString();
					serverProperty.Value = sqlDataReader["Value"].ToString();
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetServerConfig", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return serverProperty;
		}

		// Token: 0x06008A38 RID: 35384 RVA: 0x002F5E68 File Offset: 0x002F4068
		public bool UpdateServerPropertyByKey(string key, string value)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@Key", key),
					new SqlParameter("@Value", value)
				};
				flag = this.db.RunProcedure("SP_Server_Config_Update", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008A39 RID: 35385 RVA: 0x002F5EF0 File Offset: 0x002F40F0
		public ServerEventInfo[] GetServerEvent()
		{
			List<ServerEventInfo> list = new List<ServerEventInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Get_ServerEvent");
				while (sqlDataReader.Read())
				{
					list.Add(new ServerEventInfo
					{
						ID = (int)sqlDataReader["ID"],
						Name = (string)sqlDataReader["Name"],
						Value = (string)sqlDataReader["Value"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_Get_ServerEvent", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A3A RID: 35386 RVA: 0x002F5FF4 File Offset: 0x002F41F4
		public ArrayList GetRate(int serverId)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				ArrayList arrayList = new ArrayList();
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ServerID", serverId)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Rate", array);
				while (sqlDataReader.Read())
				{
					arrayList.Add(new RateInfo
					{
						ServerID = (int)sqlDataReader["ServerID"],
						Rate = (float)((decimal)sqlDataReader["Rate"]),
						BeginDay = (DateTime)sqlDataReader["BeginDay"],
						EndDay = (DateTime)sqlDataReader["EndDay"],
						BeginTime = (DateTime)sqlDataReader["BeginTime"],
						EndTime = (DateTime)sqlDataReader["EndTime"],
						Type = (int)sqlDataReader["Type"]
					});
				}
				arrayList.TrimToSize();
				return arrayList;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetRates", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x06008A3B RID: 35387 RVA: 0x002F6198 File Offset: 0x002F4398
		public FightRateInfo[] GetFightRate(int serverId)
		{
			SqlDataReader sqlDataReader = null;
			List<FightRateInfo> list = new List<FightRateInfo>();
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ServerID", serverId)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Fight_Rate", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					list.Add(new FightRateInfo
					{
						ID = (int)sqlDataReader["ID"],
						ServerID = (int)sqlDataReader["ServerID"],
						Rate = (int)sqlDataReader["Rate"],
						BeginDay = (DateTime)sqlDataReader["BeginDay"],
						EndDay = (DateTime)sqlDataReader["EndDay"],
						BeginTime = (DateTime)sqlDataReader["BeginTime"],
						EndTime = (DateTime)sqlDataReader["EndTime"],
						SelfCue = ((sqlDataReader["SelfCue"] == null) ? "" : sqlDataReader["SelfCue"].ToString()),
						EnemyCue = ((sqlDataReader["EnemyCue"] == null) ? "" : sqlDataReader["EnemyCue"].ToString()),
						BoyTemplateID = (int)sqlDataReader["BoyTemplateID"],
						GirlTemplateID = (int)sqlDataReader["GirlTemplateID"],
						Name = ((sqlDataReader["Name"] == null) ? "" : sqlDataReader["Name"].ToString())
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetFightRate", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A3C RID: 35388 RVA: 0x002F63E8 File Offset: 0x002F45E8
		public string GetGameEquip()
		{
			string text = string.Empty;
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Server_Equip");
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					text = ((sqlDataReader["value"] == null) ? "" : sqlDataReader["value"].ToString());
					return text;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return text;
		}
	}
}
