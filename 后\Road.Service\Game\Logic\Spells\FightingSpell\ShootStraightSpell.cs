﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CCA RID: 3274
	[SpellAttibute(12)]
	public class ShootStraightSpell : ISpellHandler
	{
		// Token: 0x06007516 RID: 29974 RVA: 0x0026EA30 File Offset: 0x0026CC30
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.ControlBall = true;
				player.CurrentShootMinus *= 0.5f;
			}
			else
			{
				bool flag = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
				if (flag)
				{
					game.CurrentLiving.ControlBall = true;
					game.CurrentLiving.CurrentShootMinus *= 0.5f;
				}
			}
		}
	}
}
