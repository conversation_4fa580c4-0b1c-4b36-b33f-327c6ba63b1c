﻿namespace Game.Manager.Data
{
	// Token: 0x02000C7B RID: 3195
	public partial class EditBox : global::System.Windows.Forms.Form
	{
		// Token: 0x060070FB RID: 28923 RVA: 0x00251018 File Offset: 0x0024F218
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060070FC RID: 28924 RVA: 0x00251050 File Offset: 0x0024F250
		private void InitializeComponent()
		{
			this.components = new global::System.ComponentModel.Container();
			global::System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle = new global::System.Windows.Forms.DataGridViewCellStyle();
			global::System.ComponentModel.ComponentResourceManager componentResourceManager = new global::System.ComponentModel.ComponentResourceManager(typeof(global::Game.Manager.Data.EditBox));
			this.EditShopBox = new global::System.Windows.Forms.DataGridView();
			this.ID = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.DataID = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.BoxName = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.TemplateID = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.TemplateName = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Random = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.ItemValid = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.ItemCount = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.StrengthenLevel = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.AttackCompose = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.DefendCompose = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.AgilityCompose = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.LuckCompose = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.IsSelect = new global::System.Windows.Forms.DataGridViewCheckBoxColumn();
			this.IsBind = new global::System.Windows.Forms.DataGridViewCheckBoxColumn();
			this.IsTips = new global::System.Windows.Forms.DataGridViewCheckBoxColumn();
			this.IsLogs = new global::System.Windows.Forms.DataGridViewCheckBoxColumn();
			this.label1 = new global::System.Windows.Forms.Label();
			this.Name_Text = new global::System.Windows.Forms.TextBox();
			this.Name_Button = new global::System.Windows.Forms.Button();
			this.bdnInfo = new global::System.Windows.Forms.BindingNavigator(this.components);
			this.bindingNavigatorCountItem = new global::System.Windows.Forms.ToolStripLabel();
			this.bindingNavigatorMoveFirstItem = new global::System.Windows.Forms.ToolStripButton();
			this.bindingNavigatorMovePreviousItem = new global::System.Windows.Forms.ToolStripButton();
			this.bindingNavigatorSeparator = new global::System.Windows.Forms.ToolStripSeparator();
			this.bindingNavigatorPositionItem = new global::System.Windows.Forms.ToolStripTextBox();
			this.bindingNavigatorSeparator1 = new global::System.Windows.Forms.ToolStripSeparator();
			this.bindingNavigatorMoveNextItem = new global::System.Windows.Forms.ToolStripButton();
			this.bindingNavigatorMoveLastItem = new global::System.Windows.Forms.ToolStripButton();
			this.bindingNavigatorSeparator2 = new global::System.Windows.Forms.ToolStripSeparator();
			this.LastPage = new global::System.Windows.Forms.ToolStripButton();
			this.txtCurrentPage = new global::System.Windows.Forms.ToolStripLabel();
			this.toolStripLabel1 = new global::System.Windows.Forms.ToolStripLabel();
			this.lblPageCount = new global::System.Windows.Forms.ToolStripLabel();
			this.NextPage = new global::System.Windows.Forms.ToolStripButton();
			this.bdsInfo = new global::System.Windows.Forms.BindingSource(this.components);
			((global::System.ComponentModel.ISupportInitialize)this.EditShopBox).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.bdnInfo).BeginInit();
			this.bdnInfo.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.bdsInfo).BeginInit();
			base.SuspendLayout();
			this.EditShopBox.AllowUserToAddRows = false;
			this.EditShopBox.AllowUserToDeleteRows = false;
			this.EditShopBox.AllowUserToOrderColumns = true;
			this.EditShopBox.AllowUserToResizeColumns = false;
			this.EditShopBox.AllowUserToResizeRows = false;
			this.EditShopBox.ColumnHeadersBorderStyle = global::System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
			dataGridViewCellStyle.Alignment = global::System.Windows.Forms.DataGridViewContentAlignment.TopCenter;
			dataGridViewCellStyle.BackColor = global::System.Drawing.SystemColors.Control;
			dataGridViewCellStyle.Font = new global::System.Drawing.Font("宋体", 9f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 134);
			dataGridViewCellStyle.ForeColor = global::System.Drawing.SystemColors.WindowText;
			dataGridViewCellStyle.SelectionBackColor = global::System.Drawing.SystemColors.Highlight;
			dataGridViewCellStyle.SelectionForeColor = global::System.Drawing.SystemColors.HighlightText;
			dataGridViewCellStyle.WrapMode = global::System.Windows.Forms.DataGridViewTriState.True;
			this.EditShopBox.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle;
			this.EditShopBox.ColumnHeadersHeightSizeMode = global::System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.EditShopBox.Columns.AddRange(new global::System.Windows.Forms.DataGridViewColumn[]
			{
				this.ID, this.DataID, this.BoxName, this.TemplateID, this.TemplateName, this.Random, this.ItemValid, this.ItemCount, this.StrengthenLevel, this.AttackCompose,
				this.DefendCompose, this.AgilityCompose, this.LuckCompose, this.IsSelect, this.IsBind, this.IsTips, this.IsLogs
			});
			this.EditShopBox.EditMode = global::System.Windows.Forms.DataGridViewEditMode.EditOnEnter;
			this.EditShopBox.Location = new global::System.Drawing.Point(3, 41);
			this.EditShopBox.Name = "EditShopBox";
			this.EditShopBox.RowTemplate.Height = 23;
			this.EditShopBox.Size = new global::System.Drawing.Size(1743, 491);
			this.EditShopBox.TabIndex = 0;
			this.EditShopBox.CellEndEdit += new global::System.Windows.Forms.DataGridViewCellEventHandler(this.EditShopBox_CellEndEdit);
			this.ID.DataPropertyName = "Id";
			this.ID.HeaderText = "ID";
			this.ID.Name = "ID";
			this.ID.ReadOnly = true;
			this.ID.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.DataID.DataPropertyName = "DataId";
			this.DataID.HeaderText = "宝箱ID";
			this.DataID.Name = "DataID";
			this.DataID.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.BoxName.DataPropertyName = "BoxName";
			this.BoxName.HeaderText = "宝箱名称";
			this.BoxName.Name = "BoxName";
			this.BoxName.ReadOnly = true;
			this.BoxName.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.TemplateID.DataPropertyName = "TemplateId";
			this.TemplateID.HeaderText = "物品ID";
			this.TemplateID.Name = "TemplateID";
			this.TemplateID.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.TemplateName.DataPropertyName = "TemplateName";
			this.TemplateName.HeaderText = "物品名称";
			this.TemplateName.Name = "TemplateName";
			this.TemplateName.ReadOnly = true;
			this.TemplateName.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.Random.DataPropertyName = "Random";
			this.Random.HeaderText = "几率";
			this.Random.Name = "Random";
			this.Random.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.ItemValid.DataPropertyName = "ItemValid";
			this.ItemValid.HeaderText = "物品期限";
			this.ItemValid.Name = "ItemValid";
			this.ItemValid.Resizable = global::System.Windows.Forms.DataGridViewTriState.True;
			this.ItemValid.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.ItemCount.DataPropertyName = "ItemCount";
			this.ItemCount.HeaderText = "物品数量";
			this.ItemCount.Name = "ItemCount";
			this.ItemCount.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.StrengthenLevel.DataPropertyName = "StrengthenLevel";
			this.StrengthenLevel.HeaderText = "强化等级";
			this.StrengthenLevel.Name = "StrengthenLevel";
			this.StrengthenLevel.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.AttackCompose.DataPropertyName = "AttackCompose";
			this.AttackCompose.HeaderText = "攻击合成";
			this.AttackCompose.Name = "AttackCompose";
			this.AttackCompose.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.DefendCompose.DataPropertyName = "DefendCompose";
			this.DefendCompose.HeaderText = "防御合成";
			this.DefendCompose.Name = "DefendCompose";
			this.DefendCompose.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.AgilityCompose.DataPropertyName = "AgilityCompose";
			this.AgilityCompose.HeaderText = "敏捷合成";
			this.AgilityCompose.Name = "AgilityCompose";
			this.AgilityCompose.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.LuckCompose.DataPropertyName = "LuckCompose";
			this.LuckCompose.HeaderText = "幸运合成";
			this.LuckCompose.Name = "LuckCompose";
			this.LuckCompose.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.IsSelect.DataPropertyName = "IsSelect";
			this.IsSelect.HeaderText = "是否可选";
			this.IsSelect.Name = "IsSelect";
			this.IsBind.DataPropertyName = "IsBind";
			this.IsBind.HeaderText = "是否绑定";
			this.IsBind.Name = "IsBind";
			this.IsTips.DataPropertyName = "IsTips";
			this.IsTips.HeaderText = "是否提示";
			this.IsTips.Name = "IsTips";
			this.IsLogs.DataPropertyName = "IsLogs";
			this.IsLogs.HeaderText = "是否记录";
			this.IsLogs.Name = "IsLogs";
			this.label1.AutoSize = true;
			this.label1.Font = new global::System.Drawing.Font("宋体", 11f);
			this.label1.Location = new global::System.Drawing.Point(1413, 15);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(68, 15);
			this.label1.TabIndex = 7;
			this.label1.Text = "宝箱ID：";
			this.Name_Text.Location = new global::System.Drawing.Point(1487, 11);
			this.Name_Text.Name = "Name_Text";
			this.Name_Text.Size = new global::System.Drawing.Size(168, 21);
			this.Name_Text.TabIndex = 6;
			this.Name_Text.TabStop = false;
			this.Name_Button.Location = new global::System.Drawing.Point(1661, 9);
			this.Name_Button.Name = "Name_Button";
			this.Name_Button.Size = new global::System.Drawing.Size(75, 23);
			this.Name_Button.TabIndex = 5;
			this.Name_Button.TabStop = false;
			this.Name_Button.Text = "查询";
			this.Name_Button.UseVisualStyleBackColor = true;
			this.Name_Button.Click += new global::System.EventHandler(this.Name_Button_Click);
			this.bdnInfo.AddNewItem = null;
			this.bdnInfo.AutoSize = false;
			this.bdnInfo.BackColor = global::System.Drawing.SystemColors.ActiveBorder;
			this.bdnInfo.CountItem = this.bindingNavigatorCountItem;
			this.bdnInfo.CountItemFormat = "/ 共{0}行";
			this.bdnInfo.DeleteItem = null;
			this.bdnInfo.Dock = global::System.Windows.Forms.DockStyle.None;
			this.bdnInfo.Font = new global::System.Drawing.Font("Microsoft YaHei UI", 9f);
			this.bdnInfo.Items.AddRange(new global::System.Windows.Forms.ToolStripItem[]
			{
				this.bindingNavigatorMoveFirstItem, this.bindingNavigatorMovePreviousItem, this.bindingNavigatorSeparator, this.bindingNavigatorPositionItem, this.bindingNavigatorCountItem, this.bindingNavigatorSeparator1, this.bindingNavigatorMoveNextItem, this.bindingNavigatorMoveLastItem, this.bindingNavigatorSeparator2, this.LastPage,
				this.txtCurrentPage, this.toolStripLabel1, this.lblPageCount, this.NextPage
			});
			this.bdnInfo.LayoutStyle = global::System.Windows.Forms.ToolStripLayoutStyle.HorizontalStackWithOverflow;
			this.bdnInfo.Location = new global::System.Drawing.Point(3, 535);
			this.bdnInfo.MoveFirstItem = this.bindingNavigatorMoveFirstItem;
			this.bdnInfo.MoveLastItem = this.bindingNavigatorMoveLastItem;
			this.bdnInfo.MoveNextItem = this.bindingNavigatorMoveNextItem;
			this.bdnInfo.MovePreviousItem = this.bindingNavigatorMovePreviousItem;
			this.bdnInfo.Name = "bdnInfo";
			this.bdnInfo.PositionItem = this.bindingNavigatorPositionItem;
			this.bdnInfo.RenderMode = global::System.Windows.Forms.ToolStripRenderMode.System;
			this.bdnInfo.Size = new global::System.Drawing.Size(1743, 40);
			this.bdnInfo.TabIndex = 9;
			this.bdnInfo.Text = "bindingNavigator1";
			this.bindingNavigatorCountItem.Name = "bindingNavigatorCountItem";
			this.bindingNavigatorCountItem.Size = new global::System.Drawing.Size(56, 37);
			this.bindingNavigatorCountItem.Text = "/ 共{0}行";
			this.bindingNavigatorCountItem.ToolTipText = "总项数";
			this.bindingNavigatorMoveFirstItem.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.bindingNavigatorMoveFirstItem.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("bindingNavigatorMoveFirstItem.Image");
			this.bindingNavigatorMoveFirstItem.Name = "bindingNavigatorMoveFirstItem";
			this.bindingNavigatorMoveFirstItem.RightToLeftAutoMirrorImage = true;
			this.bindingNavigatorMoveFirstItem.Size = new global::System.Drawing.Size(23, 37);
			this.bindingNavigatorMoveFirstItem.Text = "移到第一条记录";
			this.bindingNavigatorMovePreviousItem.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.bindingNavigatorMovePreviousItem.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("bindingNavigatorMovePreviousItem.Image");
			this.bindingNavigatorMovePreviousItem.Name = "bindingNavigatorMovePreviousItem";
			this.bindingNavigatorMovePreviousItem.RightToLeftAutoMirrorImage = true;
			this.bindingNavigatorMovePreviousItem.Size = new global::System.Drawing.Size(23, 37);
			this.bindingNavigatorMovePreviousItem.Text = "移到上一条记录";
			this.bindingNavigatorSeparator.Name = "bindingNavigatorSeparator";
			this.bindingNavigatorSeparator.Size = new global::System.Drawing.Size(6, 40);
			this.bindingNavigatorPositionItem.AccessibleName = "位置";
			this.bindingNavigatorPositionItem.AutoSize = false;
			this.bindingNavigatorPositionItem.Font = new global::System.Drawing.Font("Microsoft YaHei UI", 9f);
			this.bindingNavigatorPositionItem.Name = "bindingNavigatorPositionItem";
			this.bindingNavigatorPositionItem.Size = new global::System.Drawing.Size(50, 23);
			this.bindingNavigatorPositionItem.Text = "0";
			this.bindingNavigatorPositionItem.ToolTipText = "当前位置";
			this.bindingNavigatorSeparator1.Name = "bindingNavigatorSeparator1";
			this.bindingNavigatorSeparator1.Size = new global::System.Drawing.Size(6, 40);
			this.bindingNavigatorMoveNextItem.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.bindingNavigatorMoveNextItem.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("bindingNavigatorMoveNextItem.Image");
			this.bindingNavigatorMoveNextItem.Name = "bindingNavigatorMoveNextItem";
			this.bindingNavigatorMoveNextItem.RightToLeftAutoMirrorImage = true;
			this.bindingNavigatorMoveNextItem.Size = new global::System.Drawing.Size(23, 37);
			this.bindingNavigatorMoveNextItem.Text = "移到下一条记录";
			this.bindingNavigatorMoveLastItem.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.bindingNavigatorMoveLastItem.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("bindingNavigatorMoveLastItem.Image");
			this.bindingNavigatorMoveLastItem.Name = "bindingNavigatorMoveLastItem";
			this.bindingNavigatorMoveLastItem.RightToLeftAutoMirrorImage = true;
			this.bindingNavigatorMoveLastItem.Size = new global::System.Drawing.Size(23, 37);
			this.bindingNavigatorMoveLastItem.Text = "移到最后一条记录";
			this.bindingNavigatorSeparator2.Name = "bindingNavigatorSeparator2";
			this.bindingNavigatorSeparator2.Size = new global::System.Drawing.Size(6, 40);
			this.LastPage.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Text;
			this.LastPage.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("LastPage.Image");
			this.LastPage.ImageTransparentColor = global::System.Drawing.Color.Magenta;
			this.LastPage.Name = "LastPage";
			this.LastPage.Size = new global::System.Drawing.Size(48, 37);
			this.LastPage.Text = "上一页";
			this.LastPage.Click += new global::System.EventHandler(this.LastPage_Click);
			this.txtCurrentPage.Name = "txtCurrentPage";
			this.txtCurrentPage.Size = new global::System.Drawing.Size(15, 37);
			this.txtCurrentPage.Text = "1";
			this.toolStripLabel1.Name = "toolStripLabel1";
			this.toolStripLabel1.Size = new global::System.Drawing.Size(13, 37);
			this.toolStripLabel1.Text = "/";
			this.lblPageCount.Name = "lblPageCount";
			this.lblPageCount.Size = new global::System.Drawing.Size(29, 37);
			this.lblPageCount.Text = "100";
			this.NextPage.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Text;
			this.NextPage.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("NextPage.Image");
			this.NextPage.ImageTransparentColor = global::System.Drawing.Color.Magenta;
			this.NextPage.Name = "NextPage";
			this.NextPage.Size = new global::System.Drawing.Size(48, 37);
			this.NextPage.Text = "下一页";
			this.NextPage.Click += new global::System.EventHandler(this.NextPage_Click);
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(6f, 12f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			this.AutoScroll = true;
			this.AutoSize = true;
			this.BackColor = global::System.Drawing.SystemColors.ActiveBorder;
			base.ClientSize = new global::System.Drawing.Size(1746, 578);
			base.Controls.Add(this.Name_Text);
			base.Controls.Add(this.Name_Button);
			base.Controls.Add(this.bdnInfo);
			base.Controls.Add(this.label1);
			base.Controls.Add(this.EditShopBox);
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.Fixed3D;
			base.Icon = (global::System.Drawing.Icon)componentResourceManager.GetObject("$this.Icon");
			base.MaximizeBox = false;
			base.Name = "EditBox";
			this.Text = "物品箱子编辑";
			base.Load += new global::System.EventHandler(this.EditBox_Load);
			((global::System.ComponentModel.ISupportInitialize)this.EditShopBox).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.bdnInfo).EndInit();
			this.bdnInfo.ResumeLayout(false);
			this.bdnInfo.PerformLayout();
			((global::System.ComponentModel.ISupportInitialize)this.bdsInfo).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04003C6E RID: 15470
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04003C6F RID: 15471
		private global::System.Windows.Forms.DataGridView EditShopBox;

		// Token: 0x04003C70 RID: 15472
		private global::System.Windows.Forms.DataGridViewTextBoxColumn ID;

		// Token: 0x04003C71 RID: 15473
		private global::System.Windows.Forms.DataGridViewTextBoxColumn DataID;

		// Token: 0x04003C72 RID: 15474
		private global::System.Windows.Forms.DataGridViewTextBoxColumn BoxName;

		// Token: 0x04003C73 RID: 15475
		private global::System.Windows.Forms.DataGridViewTextBoxColumn TemplateID;

		// Token: 0x04003C74 RID: 15476
		private global::System.Windows.Forms.DataGridViewTextBoxColumn TemplateName;

		// Token: 0x04003C75 RID: 15477
		private global::System.Windows.Forms.DataGridViewTextBoxColumn Random;

		// Token: 0x04003C76 RID: 15478
		private global::System.Windows.Forms.DataGridViewTextBoxColumn ItemValid;

		// Token: 0x04003C77 RID: 15479
		private global::System.Windows.Forms.DataGridViewTextBoxColumn ItemCount;

		// Token: 0x04003C78 RID: 15480
		private global::System.Windows.Forms.DataGridViewTextBoxColumn StrengthenLevel;

		// Token: 0x04003C79 RID: 15481
		private global::System.Windows.Forms.DataGridViewTextBoxColumn AttackCompose;

		// Token: 0x04003C7A RID: 15482
		private global::System.Windows.Forms.DataGridViewTextBoxColumn DefendCompose;

		// Token: 0x04003C7B RID: 15483
		private global::System.Windows.Forms.DataGridViewTextBoxColumn AgilityCompose;

		// Token: 0x04003C7C RID: 15484
		private global::System.Windows.Forms.DataGridViewTextBoxColumn LuckCompose;

		// Token: 0x04003C7D RID: 15485
		private global::System.Windows.Forms.DataGridViewCheckBoxColumn IsSelect;

		// Token: 0x04003C7E RID: 15486
		private global::System.Windows.Forms.DataGridViewCheckBoxColumn IsBind;

		// Token: 0x04003C7F RID: 15487
		private global::System.Windows.Forms.DataGridViewCheckBoxColumn IsTips;

		// Token: 0x04003C80 RID: 15488
		private global::System.Windows.Forms.DataGridViewCheckBoxColumn IsLogs;

		// Token: 0x04003C81 RID: 15489
		private global::System.Windows.Forms.Label label1;

		// Token: 0x04003C82 RID: 15490
		private global::System.Windows.Forms.TextBox Name_Text;

		// Token: 0x04003C83 RID: 15491
		private global::System.Windows.Forms.Button Name_Button;

		// Token: 0x04003C84 RID: 15492
		private global::System.Windows.Forms.BindingNavigator bdnInfo;

		// Token: 0x04003C85 RID: 15493
		private global::System.Windows.Forms.ToolStripLabel bindingNavigatorCountItem;

		// Token: 0x04003C86 RID: 15494
		private global::System.Windows.Forms.ToolStripButton bindingNavigatorMoveFirstItem;

		// Token: 0x04003C87 RID: 15495
		private global::System.Windows.Forms.ToolStripButton bindingNavigatorMovePreviousItem;

		// Token: 0x04003C88 RID: 15496
		private global::System.Windows.Forms.ToolStripSeparator bindingNavigatorSeparator;

		// Token: 0x04003C89 RID: 15497
		private global::System.Windows.Forms.ToolStripTextBox bindingNavigatorPositionItem;

		// Token: 0x04003C8A RID: 15498
		private global::System.Windows.Forms.ToolStripSeparator bindingNavigatorSeparator1;

		// Token: 0x04003C8B RID: 15499
		private global::System.Windows.Forms.ToolStripButton bindingNavigatorMoveNextItem;

		// Token: 0x04003C8C RID: 15500
		private global::System.Windows.Forms.ToolStripButton bindingNavigatorMoveLastItem;

		// Token: 0x04003C8D RID: 15501
		private global::System.Windows.Forms.ToolStripSeparator bindingNavigatorSeparator2;

		// Token: 0x04003C8E RID: 15502
		private global::System.Windows.Forms.BindingSource bdsInfo;

		// Token: 0x04003C8F RID: 15503
		private global::System.Windows.Forms.ToolStripButton LastPage;

		// Token: 0x04003C90 RID: 15504
		private global::System.Windows.Forms.ToolStripLabel txtCurrentPage;

		// Token: 0x04003C91 RID: 15505
		private global::System.Windows.Forms.ToolStripLabel toolStripLabel1;

		// Token: 0x04003C92 RID: 15506
		private global::System.Windows.Forms.ToolStripLabel lblPageCount;

		// Token: 0x04003C93 RID: 15507
		private global::System.Windows.Forms.ToolStripButton NextPage;
	}
}
