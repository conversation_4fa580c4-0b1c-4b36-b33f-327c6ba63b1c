﻿using System;
using System.Collections.Generic;
using Game.Logic.Actions;

namespace Game.Logic.Phy.Object
{
	// Token: 0x02000CD9 RID: 3289
	public class PhysicalObj : Physics
	{
		// Token: 0x1700143A RID: 5178
		// (get) Token: 0x0600763D RID: 30269 RVA: 0x0002BC21 File Offset: 0x00029E21
		public virtual int IsCollide
		{
			get
			{
				return this.m_isCollide;
			}
		}

		// Token: 0x1700143B RID: 5179
		// (get) Token: 0x0600763E RID: 30270 RVA: 0x0002BC29 File Offset: 0x00029E29
		public virtual int phyBringToFront
		{
			get
			{
				return this.m_phyBringToFront;
			}
		}

		// Token: 0x1700143C RID: 5180
		// (get) Token: 0x0600763F RID: 30271 RVA: 0x0002BC31 File Offset: 0x00029E31
		public virtual int Type
		{
			get
			{
				return this.m_type;
			}
		}

		// Token: 0x1700143D RID: 5181
		// (get) Token: 0x06007640 RID: 30272 RVA: 0x0002BC39 File Offset: 0x00029E39
		public int typeEffect
		{
			get
			{
				return this.m_typeEffect;
			}
		}

		// Token: 0x1700143E RID: 5182
		// (get) Token: 0x06007641 RID: 30273 RVA: 0x0002BC41 File Offset: 0x00029E41
		public string Model
		{
			get
			{
				return this.m_model;
			}
		}

		// Token: 0x1700143F RID: 5183
		// (get) Token: 0x06007642 RID: 30274 RVA: 0x0002BC49 File Offset: 0x00029E49
		public Dictionary<string, string> ActionMapping
		{
			get
			{
				return this.m_actionMapping;
			}
		}

		// Token: 0x17001440 RID: 5184
		// (get) Token: 0x06007643 RID: 30275 RVA: 0x002729BC File Offset: 0x00270BBC
		// (set) Token: 0x06007644 RID: 30276 RVA: 0x0002BC51 File Offset: 0x00029E51
		public string CurrentAction
		{
			get
			{
				return this.m_currentAction;
			}
			set
			{
				this.m_currentAction = value;
			}
		}

		// Token: 0x17001441 RID: 5185
		// (get) Token: 0x06007645 RID: 30277 RVA: 0x0002BC5B File Offset: 0x00029E5B
		public int Scale
		{
			get
			{
				return this.m_scale;
			}
		}

		// Token: 0x17001442 RID: 5186
		// (get) Token: 0x06007646 RID: 30278 RVA: 0x0002BC63 File Offset: 0x00029E63
		public int Rotation
		{
			get
			{
				return this.m_rotation;
			}
		}

		// Token: 0x17001443 RID: 5187
		// (get) Token: 0x06007647 RID: 30279 RVA: 0x002729D4 File Offset: 0x00270BD4
		// (set) Token: 0x06007648 RID: 30280 RVA: 0x0002BC6B File Offset: 0x00029E6B
		public bool CanPenetrate
		{
			get
			{
				return this.m_canPenetrate;
			}
			set
			{
				this.m_canPenetrate = value;
			}
		}

		// Token: 0x17001444 RID: 5188
		// (get) Token: 0x06007649 RID: 30281 RVA: 0x0002BC75 File Offset: 0x00029E75
		public string Name
		{
			get
			{
				return this.m_name;
			}
		}

		// Token: 0x0600764A RID: 30282 RVA: 0x002729EC File Offset: 0x00270BEC
		public PhysicalObj(int id, string name, string model, string defaultAction, int scale, int rotation, int typeEffect)
			: base(id)
		{
			this.m_name = name;
			this.m_model = model;
			this.m_currentAction = defaultAction;
			this.m_scale = scale;
			this.m_rotation = rotation;
			this.m_canPenetrate = false;
			this.m_typeEffect = typeEffect;
			bool flag = !(name == "hide");
			if (flag)
			{
				bool flag2 = name == "top";
				if (flag2)
				{
					this.m_phyBringToFront = 1;
				}
				else
				{
					this.m_phyBringToFront = -1;
				}
			}
			else
			{
				this.m_phyBringToFront = 6;
			}
			this.m_isCollide = 0;
			this.m_actionMapping = new Dictionary<string, string>();
			bool flag3 = model == "asset.game.transmitted";
			if (flag3)
			{
				this.m_type = 3;
			}
			else
			{
				bool flag4 = model == "asset.game.six.ball" && !this.m_actionMapping.ContainsKey(defaultAction);
				if (flag4)
				{
					this.m_actionMapping.Add(defaultAction, this.getActionMap(defaultAction));
				}
			}
		}

		// Token: 0x0600764B RID: 30283 RVA: 0x00272AE4 File Offset: 0x00270CE4
		private string getActionMap(string act)
		{
			if (!true)
			{
			}
			uint num = <PrivateImplementationDetails>.ComputeStringHash(act);
			string text;
			if (num <= 206683925U)
			{
				if (num <= 139573449U)
				{
					if (num != 89240592U)
					{
						if (num != 106018211U)
						{
							if (num == 139573449U)
							{
								if (act == "s1")
								{
									text = "shield1";
									goto IL_027F;
								}
							}
						}
						else if (act == "s3")
						{
							text = "shield3";
							goto IL_027F;
						}
					}
					else if (act == "s2")
					{
						text = "shield2";
						goto IL_027F;
					}
				}
				else if (num != 156351068U)
				{
					if (num != 189906306U)
					{
						if (num == 206683925U)
						{
							if (act == "s5")
							{
								text = "shield5";
								goto IL_027F;
							}
						}
					}
					else if (act == "s4")
					{
						text = "shield4";
						goto IL_027F;
					}
				}
				else if (act == "s6")
				{
					text = "shield6";
					goto IL_027F;
				}
			}
			else if (num <= 396078097U)
			{
				if (num != 345745240U)
				{
					if (num != 362522859U)
					{
						if (num == 396078097U)
						{
							if (act == "s-6")
							{
								text = "shield-6";
								goto IL_027F;
							}
						}
					}
					else if (act == "s-4")
					{
						text = "shield-4";
						goto IL_027F;
					}
				}
				else if (act == "s-5")
				{
					text = "shield-5";
					goto IL_027F;
				}
			}
			else if (num <= 446410954U)
			{
				if (num != 412855716U)
				{
					if (num == 446410954U)
					{
						if (act == "s-3")
						{
							text = "shield-3";
							goto IL_027F;
						}
					}
				}
				else if (act == "s-1")
				{
					text = "shield-1";
					goto IL_027F;
				}
			}
			else if (num != 463188573U)
			{
				if (num == 2699759368U)
				{
					if (act == "double")
					{
						text = "shield-double";
						goto IL_027F;
					}
				}
			}
			else if (act == "s-2")
			{
				text = "shield-2";
				goto IL_027F;
			}
			text = act;
			IL_027F:
			if (!true)
			{
			}
			return text;
		}

		// Token: 0x0600764C RID: 30284 RVA: 0x0002BC7D File Offset: 0x00029E7D
		public void SetGame(BaseGame game)
		{
			this.m_game = game;
		}

		// Token: 0x0600764D RID: 30285 RVA: 0x00272D88 File Offset: 0x00270F88
		public void PlayMovie(string action, int delay, int movieTime)
		{
			bool flag = this.m_game != null;
			if (flag)
			{
				this.m_game.AddAction(new PhysicalObjDoAction(this, action, delay, movieTime));
			}
		}

		// Token: 0x0600764E RID: 30286 RVA: 0x00272DBC File Offset: 0x00270FBC
		public override void CollidedByObject(Physics phy)
		{
			bool flag = !this.m_canPenetrate && phy is SimpleBomb;
			if (flag)
			{
				((SimpleBomb)phy).Bomb();
			}
		}

		// Token: 0x0400456F RID: 17775
		private string m_model;

		// Token: 0x04004570 RID: 17776
		private string m_currentAction;

		// Token: 0x04004571 RID: 17777
		private int m_scale;

		// Token: 0x04004572 RID: 17778
		private int m_rotation;

		// Token: 0x04004573 RID: 17779
		private BaseGame m_game;

		// Token: 0x04004574 RID: 17780
		private bool m_canPenetrate;

		// Token: 0x04004575 RID: 17781
		private int m_type;

		// Token: 0x04004576 RID: 17782
		private string m_name;

		// Token: 0x04004577 RID: 17783
		private int m_phyBringToFront;

		// Token: 0x04004578 RID: 17784
		private int m_typeEffect;

		// Token: 0x04004579 RID: 17785
		private int m_isCollide;

		// Token: 0x0400457A RID: 17786
		private Dictionary<string, string> m_actionMapping;
	}
}
