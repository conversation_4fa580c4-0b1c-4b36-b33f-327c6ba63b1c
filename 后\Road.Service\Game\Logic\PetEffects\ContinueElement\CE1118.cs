﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E71 RID: 3697
	public class CE1118 : AbstractPetEffect
	{
		// Token: 0x06008035 RID: 32821 RVA: 0x002A8344 File Offset: 0x002A6544
		public CE1118(int count, int skilId, string elementID)
			: base(ePetEffectType.CE1118, elementID)
		{
			this.m_count = count;
			if (skilId != 74)
			{
				if (skilId == 75)
				{
					this.m_added = 500;
				}
			}
			else
			{
				this.m_added = 300;
			}
		}

		// Token: 0x06008036 RID: 32822 RVA: 0x002A8390 File Offset: 0x002A6590
		public override bool Start(Living living)
		{
			CE1118 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1118) as CE1118;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008037 RID: 32823 RVA: 0x00031F68 File Offset: 0x00030168
		public override void OnAttached(Living living)
		{
			living.Defence += (double)this.m_added;
			living.Game.SendPetBuff(living, base.Info, true);
			living.BeginSelfTurn += this.player_BeginFitting;
		}

		// Token: 0x06008038 RID: 32824 RVA: 0x00031FA6 File Offset: 0x000301A6
		public override void OnRemoved(Living living)
		{
			living.Defence -= (double)this.m_added;
			living.Game.SendPetBuff(living, base.Info, false);
			living.BeginSelfTurn -= this.player_BeginFitting;
		}

		// Token: 0x06008039 RID: 32825 RVA: 0x002A83D8 File Offset: 0x002A65D8
		private void player_BeginFitting(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x04004F52 RID: 20306
		private int m_count;

		// Token: 0x04004F53 RID: 20307
		private int m_added;
	}
}
