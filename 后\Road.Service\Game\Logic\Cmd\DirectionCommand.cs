﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000EFF RID: 3839
	[GameCommand(7, "改变方向")]
	public class DirectionCommand : ICommandHandler
	{
		// Token: 0x06008395 RID: 33685 RVA: 0x0003477F File Offset: 0x0003297F
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			player.Direction = packet.ReadInt();
			game.SendLivingUpdateDirection(player);
		}
	}
}
