﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E18 RID: 3608
	public class AE1247 : BasePetEffect
	{
		// Token: 0x06007E2D RID: 32301 RVA: 0x0029F4C8 File Offset: 0x0029D6C8
		public AE1247(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1247, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E2E RID: 32302 RVA: 0x0029F548 File Offset: 0x0029D748
		public override bool Start(Living living)
		{
			AE1247 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1247) as AE1247;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E2F RID: 32303 RVA: 0x00030C29 File Offset: 0x0002EE29
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E30 RID: 32304 RVA: 0x00030C3F File Offset: 0x0002EE3F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E31 RID: 32305 RVA: 0x0029F5A8 File Offset: 0x0029D7A8
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1247(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004CE0 RID: 19680
		private int m_type = 0;

		// Token: 0x04004CE1 RID: 19681
		private int m_count = 0;

		// Token: 0x04004CE2 RID: 19682
		private int m_probability = 0;

		// Token: 0x04004CE3 RID: 19683
		private int m_delay = 0;

		// Token: 0x04004CE4 RID: 19684
		private int m_coldDown = 0;

		// Token: 0x04004CE5 RID: 19685
		private int m_currentId;

		// Token: 0x04004CE6 RID: 19686
		private int m_added = 0;
	}
}
