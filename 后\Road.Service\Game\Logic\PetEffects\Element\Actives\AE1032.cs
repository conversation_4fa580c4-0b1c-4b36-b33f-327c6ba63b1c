﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D9C RID: 3484
	public class AE1032 : BasePetEffect
	{
		// Token: 0x06007BAA RID: 31658 RVA: 0x00293FE8 File Offset: 0x002921E8
		public AE1032(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1032, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BAB RID: 31659 RVA: 0x00294064 File Offset: 0x00292264
		public override bool Start(Living living)
		{
			AE1032 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1032) as AE1032;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BAC RID: 31660 RVA: 0x0002F3FB File Offset: 0x0002D5FB
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BAD RID: 31661 RVA: 0x0002F411 File Offset: 0x0002D611
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BAE RID: 31662 RVA: 0x002940C0 File Offset: 0x002922C0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1032(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x0400497E RID: 18814
		private int m_type = 0;

		// Token: 0x0400497F RID: 18815
		private int m_count = 0;

		// Token: 0x04004980 RID: 18816
		private int m_probability = 0;

		// Token: 0x04004981 RID: 18817
		private int m_delay = 0;

		// Token: 0x04004982 RID: 18818
		private int m_coldDown = 0;

		// Token: 0x04004983 RID: 18819
		private int m_currentId;

		// Token: 0x04004984 RID: 18820
		private int m_added = 0;
	}
}
