﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D6D RID: 3437
	public class PE1191 : BasePetEffect
	{
		// Token: 0x06007AB8 RID: 31416 RVA: 0x00290044 File Offset: 0x0028E244
		public PE1191(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1191, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AB9 RID: 31417 RVA: 0x002900C4 File Offset: 0x0028E2C4
		public override bool Start(Living living)
		{
			PE1191 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1191) as PE1191;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007ABA RID: 31418 RVA: 0x0002E9F4 File Offset: 0x0002CBF4
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007ABB RID: 31419 RVA: 0x00290124 File Offset: 0x0028E324
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = living.PetEffects.BonusAgility == 0;
			if (flag)
			{
				this.m_added = 500;
				living.Agility += (double)this.m_added;
				living.PetEffects.BonusAgility += this.m_added;
				living.Game.SendPetBuff(living, base.Info, true);
			}
		}

		// Token: 0x06007ABC RID: 31420 RVA: 0x0002EA0A File Offset: 0x0002CC0A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x0400483B RID: 18491
		private int m_type = 0;

		// Token: 0x0400483C RID: 18492
		private int m_count = 0;

		// Token: 0x0400483D RID: 18493
		private int m_probability = 0;

		// Token: 0x0400483E RID: 18494
		private int m_delay = 0;

		// Token: 0x0400483F RID: 18495
		private int m_coldDown = 0;

		// Token: 0x04004840 RID: 18496
		private int m_currentId;

		// Token: 0x04004841 RID: 18497
		private int m_added = 0;
	}
}
