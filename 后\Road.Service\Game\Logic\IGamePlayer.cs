﻿using System;
using System.Collections.Generic;
using Game.Base.Packets;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000C9F RID: 3231
	public interface IGamePlayer
	{
		// Token: 0x170013A3 RID: 5027
		// (get) Token: 0x06007336 RID: 29494
		PlayerInfo PlayerCharacter { get; }

		// Token: 0x170013A4 RID: 5028
		// (get) Token: 0x06007337 RID: 29495
		ItemInfo MainWeapon { get; }

		// Token: 0x170013A5 RID: 5029
		// (get) Token: 0x06007338 RID: 29496
		ItemInfo SecondWeapon { get; }

		// Token: 0x170013A6 RID: 5030
		// (get) Token: 0x06007339 RID: 29497
		// (set) Token: 0x0600733A RID: 29498
		bool CanUseProp { get; set; }

		// Token: 0x170013A7 RID: 5031
		// (get) Token: 0x0600733B RID: 29499
		// (set) Token: 0x0600733C RID: 29500
		bool IsCrossZone { get; set; }

		// Token: 0x170013A8 RID: 5032
		// (get) Token: 0x0600733D RID: 29501
		// (set) Token: 0x0600733E RID: 29502
		int GamePlayerId { get; set; }

		// Token: 0x170013A9 RID: 5033
		// (get) Token: 0x0600733F RID: 29503
		// (set) Token: 0x06007340 RID: 29504
		int CurrentEnemyId { get; set; }

		// Token: 0x170013AA RID: 5034
		// (get) Token: 0x06007341 RID: 29505
		// (set) Token: 0x06007342 RID: 29506
		int ServerID { get; set; }

		// Token: 0x170013AB RID: 5035
		// (get) Token: 0x06007343 RID: 29507
		int AreaID { get; }

		// Token: 0x170013AC RID: 5036
		// (get) Token: 0x06007344 RID: 29508
		string AreaName { get; }

		// Token: 0x170013AD RID: 5037
		// (get) Token: 0x06007345 RID: 29509
		// (set) Token: 0x06007346 RID: 29510
		List<int> EquipEffect { get; set; }

		// Token: 0x170013AE RID: 5038
		// (get) Token: 0x06007347 RID: 29511
		// (set) Token: 0x06007348 RID: 29512
		List<int> CardEffect { get; set; }

		// Token: 0x170013AF RID: 5039
		// (get) Token: 0x06007349 RID: 29513
		List<UserBufferInfo> FightBuffs { get; }

		// Token: 0x170013B0 RID: 5040
		// (get) Token: 0x0600734A RID: 29514
		UserPetInfo UserPet { get; }

		// Token: 0x170013B1 RID: 5041
		// (get) Token: 0x0600734B RID: 29515
		double OfferPlusRate { get; }

		// Token: 0x170013B2 RID: 5042
		// (get) Token: 0x0600734C RID: 29516
		double GPPlusRate { get; }

		// Token: 0x170013B3 RID: 5043
		// (get) Token: 0x0600734D RID: 29517
		float GMExperienceRate { get; }

		// Token: 0x170013B4 RID: 5044
		// (get) Token: 0x0600734E RID: 29518
		float GMOfferRate { get; }

		// Token: 0x170013B5 RID: 5045
		// (get) Token: 0x0600734F RID: 29519
		float GMRichesRate { get; }

		// Token: 0x170013B6 RID: 5046
		// (get) Token: 0x06007350 RID: 29520
		double GPApprenticeOnline { get; }

		// Token: 0x170013B7 RID: 5047
		// (get) Token: 0x06007351 RID: 29521
		double GPApprenticeTeam { get; }

		// Token: 0x170013B8 RID: 5048
		// (get) Token: 0x06007352 RID: 29522
		double GPSpouseTeam { get; }

		// Token: 0x170013B9 RID: 5049
		// (get) Token: 0x06007353 RID: 29523
		double AntiAddictionRate { get; }

		// Token: 0x170013BA RID: 5050
		// (get) Token: 0x06007354 RID: 29524
		long WorldbossBood { get; }

		// Token: 0x170013BB RID: 5051
		// (get) Token: 0x06007355 RID: 29525
		long AllWorldDameBoss { get; }

		// Token: 0x06007356 RID: 29526
		double GetBaseBlood();

		// Token: 0x06007357 RID: 29527
		double GetBaseAttack();

		// Token: 0x06007358 RID: 29528
		double GetBaseDefence();

		// Token: 0x06007359 RID: 29529
		int AddGP(int gp);

		// Token: 0x0600735A RID: 29530
		int AddGpDirect(int gp);

		// Token: 0x0600735B RID: 29531
		int RemoveGP(int gp);

		// Token: 0x0600735C RID: 29532
		int AddGold(int value);

		// Token: 0x0600735D RID: 29533
		int RemoveGold(int value);

		// Token: 0x0600735E RID: 29534
		int AddMoney(int value);

		// Token: 0x0600735F RID: 29535
		int RemoveMoney(int value);

		// Token: 0x06007360 RID: 29536
		int AddGiftToken(int value);

		// Token: 0x06007361 RID: 29537
		int RemoveGiftToken(int value);

		// Token: 0x06007362 RID: 29538
		int AddOffer(int value);

		// Token: 0x06007363 RID: 29539
		int AddOfferDirect(int value);

		// Token: 0x06007364 RID: 29540
		int AddRobRiches(int value);

		// Token: 0x06007365 RID: 29541
		int RemoveOffer(int value);

		// Token: 0x06007366 RID: 29542
		int AddLeageScore(bool isWin, int value);

		// Token: 0x06007367 RID: 29543
		int AddEliteScore(int value);

		// Token: 0x06007368 RID: 29544
		int RemoveEliteScore(int value);

		// Token: 0x06007369 RID: 29545
		void SendWinEliteChampion(int blood, int win);

		// Token: 0x0600736A RID: 29546
		bool AddTemplate(ItemInfo cloneItem, eBageType bagType, int count);

		// Token: 0x0600736B RID: 29547
		bool ClearTempBag();

		// Token: 0x0600736C RID: 29548
		bool ClearFightBag();

		// Token: 0x0600736D RID: 29549
		bool UsePropItem(AbstractGame game, int bag, int place, int templateId, bool isLiving);

		// Token: 0x0600736E RID: 29550
		void OnKillingLiving(AbstractGame game, int type, int id, bool isLiving, int demage);

		// Token: 0x0600736F RID: 29551
		void OnGameOver(AbstractGame game, bool isWin, int gainXp, int blood, int countTeamPlayers, int playerCount, bool isAcademyTeam, bool isMarryTeam, bool isSpanArea);

		// Token: 0x06007370 RID: 29552
		void OnUsingItem(int templateID);

		// Token: 0x06007371 RID: 29553
		void OnMissionOver(AbstractGame game, bool isWin, int MissionID, int TurnNum);

		// Token: 0x06007372 RID: 29554
		int ConsortiaFight(int consortiaWin, int consortiaLose, Dictionary<int, Player> players, eRoomType roomType, eGameType gameClass, int totalKillHealth, int count);

		// Token: 0x06007373 RID: 29555
		void SendConsortiaFight(int consortiaID, int riches, string msg);

		// Token: 0x06007374 RID: 29556
		bool SetPvePermission(int missionId, eHardLevel hardLevel);

		// Token: 0x06007375 RID: 29557
		bool IsPvePermission(int missionId, eHardLevel hardLevel);

		// Token: 0x06007376 RID: 29558
		bool SetFightLabPermission(int copyId, eHardLevel hardLevel, int missionId);

		// Token: 0x06007377 RID: 29559
		void Disconnect();

		// Token: 0x06007378 RID: 29560
		void SendInsufficientMoney(int type);

		// Token: 0x06007379 RID: 29561
		void SendMessage(string msg);

		// Token: 0x0600737A RID: 29562
		void SendTCP(GSPacketIn pkg);

		// Token: 0x0600737B RID: 29563
		void LogAddMoney(AddMoneyType masterType, AddMoneyType sonType, int userId, int moneys, int SpareMoney);

		// Token: 0x0600737C RID: 29564
		void UpdateBarrier(int barrier, string pic);

		// Token: 0x0600737D RID: 29565
		void ResetRoom(bool isWin, string parram);

		// Token: 0x0600737E RID: 29566
		bool ClearPropItem();

		// Token: 0x0600737F RID: 29567
		bool UsePayBuff(eBuffType type);

		// Token: 0x06007380 RID: 29568
		void UpdatePveResult(string type, int value, bool isWin);
	}
}
