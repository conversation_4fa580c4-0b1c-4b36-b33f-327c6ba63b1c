﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E3C RID: 3644
	public class AE4856 : BasePetEffect
	{
		// Token: 0x06007EF6 RID: 32502 RVA: 0x002A3238 File Offset: 0x002A1438
		public AE4856(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE4856, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007EF7 RID: 32503 RVA: 0x002A32B8 File Offset: 0x002A14B8
		public override bool Start(Living living)
		{
			AE4856 ae = living.PetEffectList.GetOfType(ePetEffectType.AE4856) as AE4856;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007EF8 RID: 32504 RVA: 0x00031502 File Offset: 0x0002F702
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007EF9 RID: 32505 RVA: 0x002A3318 File Offset: 0x002A1518
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.SyncAtTime = true;
					player2.AddBlood(-(player2.MaxBlood * 30 / 100), 1);
					player2.SyncAtTime = false;
					player.Game.sendShowPicSkil(player, base.ElementInfo, true);
				}
			}
		}

		// Token: 0x06007EFA RID: 32506 RVA: 0x00031518 File Offset: 0x0002F718
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004DDF RID: 19935
		private int m_type = 0;

		// Token: 0x04004DE0 RID: 19936
		private int m_count = 0;

		// Token: 0x04004DE1 RID: 19937
		private int m_probability = 0;

		// Token: 0x04004DE2 RID: 19938
		private int m_delay = 0;

		// Token: 0x04004DE3 RID: 19939
		private int m_coldDown = 0;

		// Token: 0x04004DE4 RID: 19940
		private int m_currentId;

		// Token: 0x04004DE5 RID: 19941
		private int m_added = 0;
	}
}
