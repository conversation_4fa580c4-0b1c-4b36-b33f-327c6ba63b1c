﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DB3 RID: 3507
	public class AE1064 : BasePetEffect
	{
		// Token: 0x06007C20 RID: 31776 RVA: 0x00295FE4 File Offset: 0x002941E4
		public AE1064(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1064, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C21 RID: 31777 RVA: 0x00296064 File Offset: 0x00294264
		public override bool Start(Living living)
		{
			AE1064 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1064) as AE1064;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C22 RID: 31778 RVA: 0x0002F84E File Offset: 0x0002DA4E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007C23 RID: 31779 RVA: 0x002960C4 File Offset: 0x002942C4
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1064(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007C24 RID: 31780 RVA: 0x0002F864 File Offset: 0x0002DA64
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004A1F RID: 18975
		private int m_type = 0;

		// Token: 0x04004A20 RID: 18976
		private int m_count = 0;

		// Token: 0x04004A21 RID: 18977
		private int m_probability = 0;

		// Token: 0x04004A22 RID: 18978
		private int m_delay = 0;

		// Token: 0x04004A23 RID: 18979
		private int m_coldDown = 0;

		// Token: 0x04004A24 RID: 18980
		private int m_currentId;

		// Token: 0x04004A25 RID: 18981
		private int m_added = 0;
	}
}
