﻿using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace Game.Manager.Data
{
	// Token: 0x02000C7B RID: 3195
	public partial class EditBox : Form
	{
		// Token: 0x060070F2 RID: 28914 RVA: 0x00250A34 File Offset: 0x0024EC34
		public EditBox()
		{
			this.InitializeComponent();
		}

		// Token: 0x060070F3 RID: 28915 RVA: 0x00250A88 File Offset: 0x0024EC88
		private void EditBox_Load(object sender, EventArgs e)
		{
			string text = string.Empty;
			text = "SELECT * FROM Shop_Goods_Box";
			this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
			this.InitDataSet();
		}

		// Token: 0x060070F4 RID: 28916 RVA: 0x00250AC0 File Offset: 0x0024ECC0
		private void InitDataSet()
		{
			this.pageSize = 20;
			this.nMax = this.dtInfo.Rows.Count;
			this.pageCount = this.nMax / this.pageSize;
			bool flag = this.nMax % this.pageSize > 0;
			if (flag)
			{
				this.pageCount++;
			}
			this.pageCurrent = 1;
			this.nCurrent = 0;
			this.LoadData();
		}

		// Token: 0x060070F5 RID: 28917 RVA: 0x00250B38 File Offset: 0x0024ED38
		private void LoadData()
		{
			DataTable dataTable = this.dtInfo.Clone();
			int num = ((this.pageCurrent != this.pageCount) ? (this.pageSize * this.pageCurrent) : this.nMax);
			int num2 = this.nCurrent;
			this.lblPageCount.Text = this.pageCount.ToString();
			this.txtCurrentPage.Text = Convert.ToString(this.pageCurrent);
			for (int i = num2; i < num; i++)
			{
				dataTable.ImportRow(this.dtInfo.Rows[i]);
				this.nCurrent++;
			}
			this.bdsInfo.DataSource = dataTable;
			this.bdnInfo.BindingSource = this.bdsInfo;
			this.EditShopBox.DataSource = this.bdsInfo;
			string text = string.Empty;
			string text2 = string.Empty;
			DataTable boxList = ConnectDataBase.GetBoxList();
			for (int j = 0; j < dataTable.Rows.Count; j++)
			{
				text = string.Format("SELECT TemplateID, Name FROM Shop_Goods where TemplateID = {0}", dataTable.Rows[j]["DataId"]);
				text2 = string.Format("SELECT TemplateID, Name FROM Shop_Goods where TemplateID = {0}", dataTable.Rows[j]["TemplateId"]);
				this.EditShopBox.Rows[j].Cells["BoxName"].Value = ((ConnectDataBase.GetDataSet(text).Tables[0].Rows.Count == 0) ? "未知" : ConnectDataBase.GetDataSet(text).Tables[0].Rows[0]["Name"].ToString());
				this.EditShopBox.Rows[j].Cells["TemplateName"].Value = ((ConnectDataBase.GetDataSet(text2).Tables[0].Rows.Count == 0) ? "未知" : ConnectDataBase.GetDataSet(text2).Tables[0].Rows[0]["Name"].ToString());
			}
		}

		// Token: 0x060070F6 RID: 28918 RVA: 0x00250D90 File Offset: 0x0024EF90
		private void Name_Button_Click(object sender, EventArgs e)
		{
			string text = string.Empty;
			bool flag = this.Name_Text.Text != "";
			if (flag)
			{
				text = "SELECT * FROM Shop_Goods_Box where DataId = " + this.Name_Text.Text;
				this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
				this.InitDataSet();
			}
			else
			{
				text = "SELECT * FROM Shop_Goods_Box";
				this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
				this.InitDataSet();
			}
		}

		// Token: 0x060070F7 RID: 28919 RVA: 0x00250E1C File Offset: 0x0024F01C
		private void LastPage_Click(object sender, EventArgs e)
		{
			this.pageCurrent--;
			bool flag = this.pageCurrent <= 0;
			if (flag)
			{
				MessageBox.Show("已经是第一页，请点击“下一页”查看！");
			}
			else
			{
				this.nCurrent = this.pageSize * (this.pageCurrent - 1);
				this.LoadData();
			}
		}

		// Token: 0x060070F8 RID: 28920 RVA: 0x00250E74 File Offset: 0x0024F074
		private void NextPage_Click(object sender, EventArgs e)
		{
			this.pageCurrent++;
			bool flag = this.pageCurrent > this.pageCount;
			if (flag)
			{
				MessageBox.Show("已经是最后一页，请点击“上一页”查看！");
			}
			else
			{
				this.nCurrent = this.pageSize * (this.pageCurrent - 1);
				this.LoadData();
			}
		}

		// Token: 0x060070F9 RID: 28921 RVA: 0x00250ECC File Offset: 0x0024F0CC
		private void EditShopBox_CellValueChanged(object sender, DataGridViewCellEventArgs e)
		{
			string headerText = this.EditShopBox.Columns[e.ColumnIndex].HeaderText;
			string text = this.EditShopBox.Rows[e.RowIndex].Cells[0].Value.ToString();
			string text2 = this.EditShopBox.CurrentCell.Value.ToString();
			string text3 = string.Concat(new string[] { "Update [dbo].[Shop_Goods_Box] set ", headerText, "='", text2, "'Where Id = ", text });
			ConnectDataBase.GetNonQueryEffectedRow(text3);
		}

		// Token: 0x060070FA RID: 28922 RVA: 0x00250F70 File Offset: 0x0024F170
		private void EditShopBox_CellEndEdit(object sender, DataGridViewCellEventArgs e)
		{
			string dataPropertyName = this.EditShopBox.Columns[e.ColumnIndex].DataPropertyName;
			string text = this.EditShopBox.Rows[e.RowIndex].Cells["Id"].Value.ToString();
			string text2 = this.EditShopBox.CurrentCell.Value.ToString();
			string text3 = string.Concat(new string[] { "Update [dbo].[Shop_Goods_Box] set ", dataPropertyName, "='", text2, "'Where Id = ", text });
			ConnectDataBase.GetNonQueryEffectedRow(text3);
		}

		// Token: 0x04003C68 RID: 15464
		private int pageSize = 0;

		// Token: 0x04003C69 RID: 15465
		private int nMax = 0;

		// Token: 0x04003C6A RID: 15466
		private int pageCount = 0;

		// Token: 0x04003C6B RID: 15467
		private int pageCurrent = 0;

		// Token: 0x04003C6C RID: 15468
		private int nCurrent = 0;

		// Token: 0x04003C6D RID: 15469
		private DataTable dtInfo = new DataTable();
	}
}
