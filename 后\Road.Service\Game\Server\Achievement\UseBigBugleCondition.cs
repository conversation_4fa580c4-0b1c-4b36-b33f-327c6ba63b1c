﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C73 RID: 3187
	public class UseBigBugleCondition : BaseCondition
	{
		// Token: 0x060070C8 RID: 28872 RVA: 0x0002A421 File Offset: 0x00028621
		public UseBigBugleCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x060070C9 RID: 28873 RVA: 0x0002A780 File Offset: 0x00028980
		public override void AddTrigger(GamePlayer player)
		{
			player.AfterUsingItem += this.player_AfterUsingItem;
		}

		// Token: 0x060070CA RID: 28874 RVA: 0x002505BC File Offset: 0x0024E7BC
		private void player_AfterUsingItem(int templateID)
		{
			bool flag = templateID == 11102;
			if (flag)
			{
				int value = base.Value;
				base.Value = value + 1;
			}
		}

		// Token: 0x060070CB RID: 28875 RVA: 0x0002A796 File Offset: 0x00028996
		public override void RemoveTrigger(GamePlayer player)
		{
			player.AfterUsingItem -= this.player_AfterUsingItem;
		}

		// Token: 0x060070CC RID: 28876 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
