﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E7A RID: 3706
	public class CE1161 : BasePetEffect
	{
		// Token: 0x0600806D RID: 32877 RVA: 0x002A915C File Offset: 0x002A735C
		public CE1161(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1161, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600806E RID: 32878 RVA: 0x002A91DC File Offset: 0x002A73DC
		public override bool Start(Living living)
		{
			CE1161 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1161) as CE1161;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600806F RID: 32879 RVA: 0x000321E2 File Offset: 0x000303E2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008070 RID: 32880 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008071 RID: 32881 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_BeginSelfTurn(Living living)
		{
			this.Stop();
		}

		// Token: 0x06008072 RID: 32882 RVA: 0x0003220B File Offset: 0x0003040B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F8F RID: 20367
		private int m_type = 0;

		// Token: 0x04004F90 RID: 20368
		private int m_count = 0;

		// Token: 0x04004F91 RID: 20369
		private int m_probability = 0;

		// Token: 0x04004F92 RID: 20370
		private int m_delay = 0;

		// Token: 0x04004F93 RID: 20371
		private int m_coldDown = 0;

		// Token: 0x04004F94 RID: 20372
		private int m_currentId;

		// Token: 0x04004F95 RID: 20373
		private int m_added = 0;
	}
}
