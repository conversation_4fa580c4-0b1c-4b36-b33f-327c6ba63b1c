﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F32 RID: 3890
	public class PioneerEffect : BaseCardEffect
	{
		// Token: 0x0600845E RID: 33886 RVA: 0x002B7D9C File Offset: 0x002B5F9C
		public PioneerEffect(int index, CardBuffInfo info)
			: base(eCardEffectType.PioneerDeck, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x0600845F RID: 33887 RVA: 0x002B7E0C File Offset: 0x002B600C
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.PioneerDeck) is PioneerEffect;
			return flag || base.Start(living);
		}

		// Token: 0x06008460 RID: 33888 RVA: 0x00034E2A File Offset: 0x0003302A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x06008461 RID: 33889 RVA: 0x00034E40 File Offset: 0x00033040
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x06008462 RID: 33890 RVA: 0x002B7E44 File Offset: 0x002B6044
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.AddMaxBlood(-this.m_value);
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVPGame && (player.Game as PVPGame).IsMatchOrFreedom();
			if (flag2)
			{
				player.AddMaxBlood(this.m_value);
				this.m_added = this.m_value;
			}
		}

		// Token: 0x0400528A RID: 21130
		private int m_indexValue = 0;

		// Token: 0x0400528B RID: 21131
		private int m_value = 0;

		// Token: 0x0400528C RID: 21132
		private int m_added = 0;
	}
}
