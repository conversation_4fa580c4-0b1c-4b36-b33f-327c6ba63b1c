﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FC3 RID: 4035
	public static class AchievementMgr
	{
		// Token: 0x06008A5A RID: 35418 RVA: 0x002F687C File Offset: 0x002F4A7C
		public static bool Init()
		{
			return AchievementMgr.Reload();
		}

		// Token: 0x06008A5B RID: 35419 RVA: 0x002F6894 File Offset: 0x002F4A94
		public static bool Reload()
		{
			try
			{
				Dictionary<int, AchievementInfo> dictionary = AchievementMgr.LoadAchievementInfoDb();
				Dictionary<int, List<AchievementConditionInfo>> dictionary2 = AchievementMgr.LoadAchievementCondictionDb(dictionary);
				Dictionary<int, List<AchievementGoodsInfo>> dictionary3 = AchievementMgr.LoadAchievementGoodDb(dictionary);
				bool flag = dictionary.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, AchievementInfo>>(ref AchievementMgr.m_achievement, dictionary);
					Interlocked.Exchange<Dictionary<int, List<AchievementConditionInfo>>>(ref AchievementMgr.m_achievementCondition, dictionary2);
					Interlocked.Exchange<Dictionary<int, List<AchievementGoodsInfo>>>(ref AchievementMgr.m_achievementReward, dictionary3);
				}
				return true;
			}
			catch (Exception ex)
			{
				AchievementMgr.log.Error("AchievementMgr", ex);
			}
			return false;
		}

		// Token: 0x06008A5C RID: 35420 RVA: 0x002F6920 File Offset: 0x002F4B20
		public static Dictionary<int, AchievementInfo> LoadAchievementInfoDb()
		{
			Dictionary<int, AchievementInfo> dictionary = new Dictionary<int, AchievementInfo>();
			Dictionary<int, AchievementInfo> dictionary2;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				AchievementInfo[] allAchievement = produceBussiness.GetAllAchievement();
				AchievementInfo[] array = allAchievement;
				AchievementInfo[] array2 = array;
				AchievementInfo[] array3 = array2;
				foreach (AchievementInfo achievementInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(achievementInfo.ID);
					if (flag)
					{
						dictionary.Add(achievementInfo.ID, achievementInfo);
					}
				}
				dictionary2 = dictionary;
			}
			return dictionary2;
		}

		// Token: 0x06008A5D RID: 35421 RVA: 0x002F69B8 File Offset: 0x002F4BB8
		public static Dictionary<int, List<AchievementConditionInfo>> LoadAchievementCondictionDb(Dictionary<int, AchievementInfo> infos)
		{
			Dictionary<int, List<AchievementConditionInfo>> dictionary = new Dictionary<int, List<AchievementConditionInfo>>();
			Dictionary<int, List<AchievementConditionInfo>> dictionary2;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				AchievementConditionInfo[] allAchievementCondiction = produceBussiness.GetAllAchievementCondiction();
				using (Dictionary<int, AchievementInfo>.ValueCollection.Enumerator enumerator = infos.Values.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						AchievementInfo info = enumerator.Current;
						IEnumerable<AchievementConditionInfo> enumerable = allAchievementCondiction.Where((AchievementConditionInfo s) => s.AchievementID == info.ID);
						dictionary.Add(info.ID, enumerable.ToList<AchievementConditionInfo>());
					}
				}
				dictionary2 = dictionary;
			}
			return dictionary2;
		}

		// Token: 0x06008A5E RID: 35422 RVA: 0x002F6A78 File Offset: 0x002F4C78
		public static Dictionary<int, List<AchievementGoodsInfo>> LoadAchievementGoodDb(Dictionary<int, AchievementInfo> infos)
		{
			Dictionary<int, List<AchievementGoodsInfo>> dictionary = new Dictionary<int, List<AchievementGoodsInfo>>();
			Dictionary<int, List<AchievementGoodsInfo>> dictionary2;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				AchievementGoodsInfo[] allAchievementGoods = produceBussiness.GetAllAchievementGoods();
				using (Dictionary<int, AchievementInfo>.ValueCollection.Enumerator enumerator = infos.Values.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						AchievementInfo info = enumerator.Current;
						IEnumerable<AchievementGoodsInfo> enumerable = allAchievementGoods.Where((AchievementGoodsInfo s) => s.AchievementID == info.ID);
						dictionary.Add(info.ID, enumerable.ToList<AchievementGoodsInfo>());
					}
				}
				dictionary2 = dictionary;
			}
			return dictionary2;
		}

		// Token: 0x06008A5F RID: 35423 RVA: 0x002F6B38 File Offset: 0x002F4D38
		public static AchievementInfo GetSingleAchievement(int id)
		{
			bool flag = AchievementMgr.m_achievement.ContainsKey(id);
			AchievementInfo achievementInfo;
			if (flag)
			{
				achievementInfo = AchievementMgr.m_achievement[id];
			}
			else
			{
				achievementInfo = null;
			}
			return achievementInfo;
		}

		// Token: 0x06008A60 RID: 35424 RVA: 0x002F6B6C File Offset: 0x002F4D6C
		public static List<AchievementInfo> GetAllAchievements()
		{
			return AchievementMgr.m_achievement.Values.ToList<AchievementInfo>();
		}

		// Token: 0x06008A61 RID: 35425 RVA: 0x002F6B90 File Offset: 0x002F4D90
		public static List<AchievementGoodsInfo> GetAchievementGoods(AchievementInfo info)
		{
			bool flag = AchievementMgr.m_achievementReward.ContainsKey(info.ID);
			List<AchievementGoodsInfo> list;
			if (flag)
			{
				list = AchievementMgr.m_achievementReward[info.ID];
			}
			else
			{
				list = null;
			}
			return list;
		}

		// Token: 0x06008A62 RID: 35426 RVA: 0x002F6BCC File Offset: 0x002F4DCC
		public static List<AchievementConditionInfo> GetAchievementCondiction(AchievementInfo info)
		{
			bool flag = AchievementMgr.m_achievementCondition.ContainsKey(info.ID);
			List<AchievementConditionInfo> list;
			if (flag)
			{
				list = AchievementMgr.m_achievementCondition[info.ID];
			}
			else
			{
				list = null;
			}
			return list;
		}

		// Token: 0x040054D3 RID: 21715
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040054D4 RID: 21716
		private static Dictionary<int, AchievementInfo> m_achievement = new Dictionary<int, AchievementInfo>();

		// Token: 0x040054D5 RID: 21717
		private static Dictionary<int, List<AchievementConditionInfo>> m_achievementCondition = new Dictionary<int, List<AchievementConditionInfo>>();

		// Token: 0x040054D6 RID: 21718
		private static Dictionary<int, List<AchievementGoodsInfo>> m_achievementReward = new Dictionary<int, List<AchievementGoodsInfo>>();
	}
}
