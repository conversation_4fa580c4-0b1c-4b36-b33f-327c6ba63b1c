﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000ED7 RID: 3799
	public class AddGuardEquipEffect : BasePlayerEffect
	{
		// Token: 0x060082B9 RID: 33465 RVA: 0x00033916 File Offset: 0x00031B16
		public AddGuardEquipEffect(int count, int probability, bool isArrmor)
			: base(eEffectType.AddGuardEquipEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
			this.m_isArrmor = isArrmor;
		}

		// Token: 0x060082BA RID: 33466 RVA: 0x00033937 File Offset: 0x00031B37
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AddArmor = true;
			player.BeginSelfTurn += this.player_SelfTurn;
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
			player.Game.SendPlayerPicture(player, 6, true);
		}

		// Token: 0x060082BB RID: 33467 RVA: 0x00033976 File Offset: 0x00031B76
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AddArmor = false;
			player.BeginSelfTurn -= this.player_SelfTurn;
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
			player.Game.SendPlayerPicture(player, 6, true);
		}

		// Token: 0x060082BC RID: 33468 RVA: 0x002B1894 File Offset: 0x002AFA94
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			damageAmount -= this.m_count;
			bool flag = (damageAmount -= this.m_count) <= 0;
			if (flag)
			{
				damageAmount = 1;
			}
		}

		// Token: 0x060082BD RID: 33469 RVA: 0x002B18CC File Offset: 0x002AFACC
		private void player_SelfTurn(Living living)
		{
			this.m_probability--;
			bool flag = this.m_probability < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060082BE RID: 33470 RVA: 0x002B1900 File Offset: 0x002AFB00
		public override bool Start(Living living)
		{
			AddGuardEquipEffect addGuardEquipEffect = living.EffectList.GetOfType(eEffectType.AddGuardEquipEffect) as AddGuardEquipEffect;
			bool flag = addGuardEquipEffect != null;
			bool flag2;
			if (flag)
			{
				addGuardEquipEffect.m_probability = this.m_probability;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x040051D4 RID: 20948
		private int m_count;

		// Token: 0x040051D5 RID: 20949
		private int m_probability;

		// Token: 0x040051D6 RID: 20950
		private bool m_isArrmor;
	}
}
