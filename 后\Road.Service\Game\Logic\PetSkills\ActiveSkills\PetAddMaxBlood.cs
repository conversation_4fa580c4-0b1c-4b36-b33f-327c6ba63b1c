﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D28 RID: 3368
	public class PetAddMaxBlood : BasePetEffect
	{
		// Token: 0x06007930 RID: 31024 RVA: 0x0002D55F File Offset: 0x0002B75F
		public PetAddMaxBlood(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetAddMaxBlood, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x06007931 RID: 31025 RVA: 0x0028966C File Offset: 0x0028786C
		public override bool Start(Living living)
		{
			PetAddMaxBlood petAddMaxBlood = living.PetEffectList.GetOfType(ePetEffectType.PetAddMaxBlood) as PetAddMaxBlood;
			bool flag = petAddMaxBlood != null;
			bool flag2;
			if (flag)
			{
				petAddMaxBlood.m_probability = ((this.m_probability > petAddMaxBlood.m_probability) ? this.m_probability : petAddMaxBlood.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007932 RID: 31026 RVA: 0x0002D590 File Offset: 0x0002B790
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x06007933 RID: 31027 RVA: 0x0002D5A6 File Offset: 0x0002B7A6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x06007934 RID: 31028 RVA: 0x002896CC File Offset: 0x002878CC
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				living.PetEffectTrigger = true;
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				new PetAddMaxBloodEquip(this.m_count, base.Info.ID.ToString()).Start(living);
			}
		}

		// Token: 0x04004702 RID: 18178
		private int m_count;

		// Token: 0x04004703 RID: 18179
		private int m_probability;

		// Token: 0x04004704 RID: 18180
		private int m_currentId;
	}
}
