﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E4D RID: 3661
	public class CE1027 : BasePetEffect
	{
		// Token: 0x06007F59 RID: 32601 RVA: 0x002A4CAC File Offset: 0x002A2EAC
		public CE1027(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1027, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F5A RID: 32602 RVA: 0x002A4D2C File Offset: 0x002A2F2C
		public override bool Start(Living living)
		{
			CE1027 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1027) as CE1027;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F5B RID: 32603 RVA: 0x000317C6 File Offset: 0x0002F9C6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F5C RID: 32604 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F5D RID: 32605 RVA: 0x002A4D8C File Offset: 0x002A2F8C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 500;
				living.SyncAtTime = true;
				living.AddBlood(this.m_added);
				living.SyncAtTime = false;
				List<Living> list = living.Game.Map.FindAllNearestSameTeam(living.X, living.Y, 250.0, living);
				foreach (Living living2 in list)
				{
					living2.SyncAtTime = true;
					living2.AddBlood(this.m_added);
					living2.SyncAtTime = false;
				}
			}
		}

		// Token: 0x06007F5E RID: 32606 RVA: 0x000317EF File Offset: 0x0002F9EF
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E56 RID: 20054
		private int m_type = 0;

		// Token: 0x04004E57 RID: 20055
		private int m_count = 0;

		// Token: 0x04004E58 RID: 20056
		private int m_probability = 0;

		// Token: 0x04004E59 RID: 20057
		private int m_delay = 0;

		// Token: 0x04004E5A RID: 20058
		private int m_coldDown = 0;

		// Token: 0x04004E5B RID: 20059
		private int m_currentId;

		// Token: 0x04004E5C RID: 20060
		private int m_added = 0;
	}
}
