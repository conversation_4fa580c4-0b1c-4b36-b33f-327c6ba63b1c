﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E06 RID: 3590
	public class AE1213 : BasePetEffect
	{
		// Token: 0x06007DD1 RID: 32209 RVA: 0x0029DB74 File Offset: 0x0029BD74
		public AE1213(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1213, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DD2 RID: 32210 RVA: 0x0029DBF4 File Offset: 0x0029BDF4
		public override bool Start(Living living)
		{
			AE1213 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1213) as AE1213;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DD3 RID: 32211 RVA: 0x000308BB File Offset: 0x0002EABB
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DD4 RID: 32212 RVA: 0x000308D1 File Offset: 0x0002EAD1
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DD5 RID: 32213 RVA: 0x0029DC54 File Offset: 0x0029BE54
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.PetEffects.CritRate = 50;
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1213(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004C62 RID: 19554
		private int m_type = 0;

		// Token: 0x04004C63 RID: 19555
		private int m_count = 0;

		// Token: 0x04004C64 RID: 19556
		private int m_probability = 0;

		// Token: 0x04004C65 RID: 19557
		private int m_delay = 0;

		// Token: 0x04004C66 RID: 19558
		private int m_coldDown = 0;

		// Token: 0x04004C67 RID: 19559
		private int m_currentId;

		// Token: 0x04004C68 RID: 19560
		private int m_added = 0;
	}
}
