﻿using System;
using System.Collections.Generic;
using System.Reflection;
using DAL;
using log4net;

namespace Bussiness
{
	// Token: 0x02000FA6 RID: 4006
	public class CountBussiness
	{
		// Token: 0x170014D9 RID: 5337
		// (get) Token: 0x06008854 RID: 34900 RVA: 0x000360DD File Offset: 0x000342DD
		public static string ConnectionString
		{
			get
			{
				return CountBussiness._connectionString;
			}
		}

		// Token: 0x170014DA RID: 5338
		// (get) Token: 0x06008855 RID: 34901 RVA: 0x000360E4 File Offset: 0x000342E4
		public static int AppID
		{
			get
			{
				return CountBussiness._appID;
			}
		}

		// Token: 0x170014DB RID: 5339
		// (get) Token: 0x06008856 RID: 34902 RVA: 0x000360EB File Offset: 0x000342EB
		public static int SubID
		{
			get
			{
				return CountBussiness._subID;
			}
		}

		// Token: 0x170014DC RID: 5340
		// (get) Token: 0x06008857 RID: 34903 RVA: 0x000360F2 File Offset: 0x000342F2
		public static int ServerID
		{
			get
			{
				return CountBussiness._serverID;
			}
		}

		// Token: 0x170014DD RID: 5341
		// (get) Token: 0x06008858 RID: 34904 RVA: 0x000360F9 File Offset: 0x000342F9
		public static bool CountRecord
		{
			get
			{
				return CountBussiness._conutRecord;
			}
		}

		// Token: 0x06008859 RID: 34905 RVA: 0x00036100 File Offset: 0x00034300
		public static void SetConfig(string connectionString, int appID, int subID, int serverID, bool countRecord)
		{
			CountBussiness._connectionString = connectionString;
			CountBussiness._appID = appID;
			CountBussiness._subID = subID;
			CountBussiness._serverID = serverID;
			CountBussiness._conutRecord = countRecord;
		}

		// Token: 0x0600885A RID: 34906 RVA: 0x002D0E28 File Offset: 0x002CF028
		public static void InsertGameInfo(DateTime begin, int mapID, int money, int gold, string users)
		{
			CountBussiness.InsertGameInfo(CountBussiness.AppID, CountBussiness.SubID, CountBussiness.ServerID, begin, DateTime.Now, users.Split(new char[] { ',' }).Length, mapID, money, gold, users);
		}

		// Token: 0x0600885B RID: 34907 RVA: 0x002D0E6C File Offset: 0x002CF06C
		public static void InsertGameInfo(int appid, int subid, int serverid, DateTime begin, DateTime end, int usercount, int mapID, int money, int gold, string users)
		{
			try
			{
				bool countRecord = CountBussiness.CountRecord;
				if (countRecord)
				{
					SqlHelper.BeginExecuteNonQuery(CountBussiness.ConnectionString, "SP_Insert_Count_FightInfo", new object[] { appid, subid, serverid, begin, end, usercount, mapID, money, gold, users });
				}
			}
			catch (Exception ex)
			{
				CountBussiness.log.Error("Insert Log Error!", ex);
			}
		}

		// Token: 0x0600885C RID: 34908 RVA: 0x002D0F24 File Offset: 0x002CF124
		public static void InsertServerInfo(int appid, int subid, int serverid, int usercount, int gamecount, DateTime time)
		{
			try
			{
				bool countRecord = CountBussiness.CountRecord;
				if (countRecord)
				{
					SqlHelper.BeginExecuteNonQuery(CountBussiness.ConnectionString, "SP_Insert_Count_Server", new object[] { appid, subid, serverid, usercount, gamecount, time });
				}
			}
			catch (Exception ex)
			{
				CountBussiness.log.Error("Insert Log Error!!", ex);
			}
		}

		// Token: 0x0600885D RID: 34909 RVA: 0x00036122 File Offset: 0x00034322
		public static void InsertSystemPayCount(int consumerid, int money, int gold, int consumertype, int subconsumertype)
		{
			CountBussiness.InsertSystemPayCount(CountBussiness.AppID, CountBussiness.SubID, consumerid, money, gold, consumertype, subconsumertype, DateTime.Now);
		}

		// Token: 0x0600885E RID: 34910 RVA: 0x002D0FB8 File Offset: 0x002CF1B8
		public static void InsertSystemPayCount(int appid, int subid, int consumerid, int money, int gold, int consumertype, int subconsumertype, DateTime datime)
		{
			try
			{
				bool countRecord = CountBussiness.CountRecord;
				if (countRecord)
				{
					SqlHelper.BeginExecuteNonQuery(CountBussiness.ConnectionString, "SP_Insert_Count_SystemPay", new object[] { appid, subid, consumerid, money, gold, consumertype, subconsumertype, datime });
				}
			}
			catch (Exception ex)
			{
				CountBussiness.log.Error("InsertSystemPayCount Log Error!!!", ex);
			}
		}

		// Token: 0x0600885F RID: 34911 RVA: 0x002D1060 File Offset: 0x002CF260
		public static void InsertContentCount(Dictionary<string, string> clientInfos)
		{
			try
			{
				bool flag = !CountBussiness.CountRecord;
				if (flag)
				{
					SqlHelper.BeginExecuteNonQuery(CountBussiness.ConnectionString, "Modify_Count_Content", new object[]
					{
						clientInfos["Application_Id"],
						clientInfos["Cpu"],
						clientInfos["OperSystem"],
						clientInfos["IP"],
						clientInfos["IPAddress"],
						clientInfos["NETCLR"],
						clientInfos["Browser"],
						clientInfos["ActiveX"],
						clientInfos["Cookies"],
						clientInfos["CSS"],
						clientInfos["Language"],
						clientInfos["Computer"],
						clientInfos["Platform"],
						clientInfos["Win16"],
						clientInfos["Win32"],
						clientInfos["Referry"],
						clientInfos["Redirect"],
						clientInfos["TimeSpan"],
						clientInfos["ScreenWidth"] + clientInfos["ScreenHeight"],
						clientInfos["Color"],
						clientInfos["Flash"],
						"Insert"
					});
				}
			}
			catch (Exception ex)
			{
				CountBussiness.log.Error("Insert Log Error!!!!", ex);
			}
		}

		// Token: 0x040053DA RID: 21466
		protected static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040053DB RID: 21467
		private static string _connectionString;

		// Token: 0x040053DC RID: 21468
		private static int _appID;

		// Token: 0x040053DD RID: 21469
		private static int _subID;

		// Token: 0x040053DE RID: 21470
		private static int _serverID;

		// Token: 0x040053DF RID: 21471
		private static bool _conutRecord;
	}
}
