﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E83 RID: 3715
	public class CE1177 : BasePetEffect
	{
		// Token: 0x060080A8 RID: 32936 RVA: 0x002A9D00 File Offset: 0x002A7F00
		public CE1177(int count, int probability, int type, int skillId, int delay, string elementID, Living source)
			: base(ePetEffectType.CE1177, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
			this.m_source = source;
		}

		// Token: 0x060080A9 RID: 32937 RVA: 0x002A9D88 File Offset: 0x002A7F88
		public override bool Start(Living living)
		{
			CE1177 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1177) as CE1177;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080AA RID: 32938 RVA: 0x00032563 File Offset: 0x00030763
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060080AB RID: 32939 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060080AC RID: 32940 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginNextTurn(Living living)
		{
		}

		// Token: 0x060080AD RID: 32941 RVA: 0x002A9DE8 File Offset: 0x002A7FE8
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 800;
				living.AddBlood(-this.m_added, 1);
				bool flag2 = living.Blood <= 0;
				if (flag2)
				{
					living.Die();
					bool flag3 = this.m_source != null && this.m_source is Player;
					if (flag3)
					{
						(this.m_source as Player).PlayerDetail.OnKillingLiving(this.m_source.Game, 2, living.Id, living.IsLiving, this.m_added);
					}
				}
			}
		}

		// Token: 0x060080AE RID: 32942 RVA: 0x0003259F File Offset: 0x0003079F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004FD1 RID: 20433
		private int m_type = 0;

		// Token: 0x04004FD2 RID: 20434
		private int m_count = 0;

		// Token: 0x04004FD3 RID: 20435
		private int m_probability = 0;

		// Token: 0x04004FD4 RID: 20436
		private int m_delay = 0;

		// Token: 0x04004FD5 RID: 20437
		private int m_coldDown = 0;

		// Token: 0x04004FD6 RID: 20438
		private int m_currentId;

		// Token: 0x04004FD7 RID: 20439
		private int m_added = 0;

		// Token: 0x04004FD8 RID: 20440
		private Living m_source;
	}
}
