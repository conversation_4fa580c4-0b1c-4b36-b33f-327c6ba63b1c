﻿using System;
using System.Collections;
using System.Data;
using System.Data.SqlClient;
using System.Xml;

namespace DAL
{
	// Token: 0x02000F9C RID: 3996
	public sealed class SqlHelper
	{
		// Token: 0x060087CD RID: 34765 RVA: 0x0000586E File Offset: 0x00003A6E
		private SqlHelper()
		{
		}

		// Token: 0x060087CE RID: 34766 RVA: 0x002C88B0 File Offset: 0x002C6AB0
		private static void AttachParameters(SqlCommand command, SqlParameter[] commandParameters)
		{
			foreach (SqlParameter sqlParameter in commandParameters)
			{
				bool flag = sqlParameter.Direction == ParameterDirection.InputOutput && sqlParameter.Value == null;
				if (flag)
				{
					sqlParameter.Value = DBNull.Value;
				}
				command.Parameters.Add(sqlParameter);
			}
		}

		// Token: 0x060087CF RID: 34767 RVA: 0x002C890C File Offset: 0x002C6B0C
		public static void AssignParameterValues(SqlParameter[] commandParameters, params object[] parameterValues)
		{
			bool flag = commandParameters == null || parameterValues == null;
			if (!flag)
			{
				bool flag2 = commandParameters.Length != parameterValues.Length;
				if (flag2)
				{
					throw new ArgumentException("Parameter count does not match Parameter Value count.");
				}
				int i = 0;
				int num = commandParameters.Length;
				while (i < num)
				{
					bool flag3 = parameterValues[i] != null && (commandParameters[i].Direction == ParameterDirection.Input || commandParameters[i].Direction == ParameterDirection.InputOutput);
					if (flag3)
					{
						commandParameters[i].Value = parameterValues[i];
					}
					i++;
				}
			}
		}

		// Token: 0x060087D0 RID: 34768 RVA: 0x002C8994 File Offset: 0x002C6B94
		public static void AssignParameterValues(SqlParameter[] commandParameters, Hashtable parameterValues)
		{
			bool flag = commandParameters == null || parameterValues == null;
			if (!flag)
			{
				bool flag2 = commandParameters.Length != parameterValues.Count;
				if (flag2)
				{
					throw new ArgumentException("Parameter count does not match Parameter Value count.");
				}
				int i = 0;
				int num = commandParameters.Length;
				while (i < num)
				{
					bool flag3 = parameterValues[commandParameters[i].ParameterName] != null && (commandParameters[i].Direction == ParameterDirection.Input || commandParameters[i].Direction == ParameterDirection.InputOutput);
					if (flag3)
					{
						commandParameters[i].Value = parameterValues[commandParameters[i].ParameterName];
					}
					i++;
				}
			}
		}

		// Token: 0x060087D1 RID: 34769 RVA: 0x002C8A38 File Offset: 0x002C6C38
		private static void PrepareCommand(SqlCommand command, SqlConnection connection, SqlTransaction transaction, CommandType commandType, string commandText, SqlParameter[] commandParameters)
		{
			bool flag = connection.State != ConnectionState.Open;
			if (flag)
			{
				connection.Open();
			}
			command.Connection = connection;
			command.CommandText = commandText;
			bool flag2 = transaction != null;
			if (flag2)
			{
				command.Transaction = transaction;
			}
			command.CommandType = commandType;
			bool flag3 = commandParameters != null;
			if (flag3)
			{
				SqlHelper.AttachParameters(command, commandParameters);
			}
		}

		// Token: 0x060087D2 RID: 34770 RVA: 0x002C8AA0 File Offset: 0x002C6CA0
		public static int ExecuteNonQuery(string connectionString, CommandType commandType, string commandText)
		{
			return SqlHelper.ExecuteNonQuery(connectionString, commandType, commandText, null);
		}

		// Token: 0x060087D3 RID: 34771 RVA: 0x002C8ABC File Offset: 0x002C6CBC
		public static int ExecuteNonQuery(string connectionString, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			int num;
			using (SqlConnection sqlConnection = new SqlConnection(connectionString))
			{
				sqlConnection.Open();
				num = SqlHelper.ExecuteNonQuery(sqlConnection, commandType, commandText, commandParameters);
			}
			return num;
		}

		// Token: 0x060087D4 RID: 34772 RVA: 0x002C8B00 File Offset: 0x002C6D00
		public static int ExecuteNonQuery(string connectionString, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			int num;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(connectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				num = SqlHelper.ExecuteNonQuery(connectionString, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				num = SqlHelper.ExecuteNonQuery(connectionString, CommandType.StoredProcedure, spName);
			}
			return num;
		}

		// Token: 0x060087D5 RID: 34773 RVA: 0x002C8B48 File Offset: 0x002C6D48
		public static int ExecuteNonQuery(string connectionString, string spName, Hashtable parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Count > 0;
			int num;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(connectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				num = SqlHelper.ExecuteNonQuery(connectionString, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				num = SqlHelper.ExecuteNonQuery(connectionString, CommandType.StoredProcedure, spName);
			}
			return num;
		}

		// Token: 0x060087D6 RID: 34774 RVA: 0x002C8B94 File Offset: 0x002C6D94
		public static int ExecuteNonQuery(SqlConnection connection, CommandType commandType, string commandText)
		{
			return SqlHelper.ExecuteNonQuery(connection, commandType, commandText, null);
		}

		// Token: 0x060087D7 RID: 34775 RVA: 0x002C8BB0 File Offset: 0x002C6DB0
		public static int ExecuteNonQuery(SqlConnection connection, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			SqlCommand sqlCommand = new SqlCommand();
			SqlHelper.PrepareCommand(sqlCommand, connection, null, commandType, commandText, commandParameters);
			int num = sqlCommand.ExecuteNonQuery();
			sqlCommand.Parameters.Clear();
			return num;
		}

		// Token: 0x060087D8 RID: 34776 RVA: 0x002C8BE8 File Offset: 0x002C6DE8
		public static int ExecuteNonQuery(SqlConnection connection, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			int num;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(connection.ConnectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				num = SqlHelper.ExecuteNonQuery(connection, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				num = SqlHelper.ExecuteNonQuery(connection, CommandType.StoredProcedure, spName);
			}
			return num;
		}

		// Token: 0x060087D9 RID: 34777 RVA: 0x002C8C34 File Offset: 0x002C6E34
		public static int ExecuteNonQuery(SqlTransaction transaction, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			SqlCommand sqlCommand = new SqlCommand();
			SqlHelper.PrepareCommand(sqlCommand, transaction.Connection, transaction, commandType, commandText, commandParameters);
			int num = sqlCommand.ExecuteNonQuery();
			sqlCommand.Parameters.Clear();
			return num;
		}

		// Token: 0x060087DA RID: 34778 RVA: 0x002C8C74 File Offset: 0x002C6E74
		public static int ExecuteNonQuery(SqlTransaction transaction, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			int num;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(transaction.Connection.ConnectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				num = SqlHelper.ExecuteNonQuery(transaction, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				num = SqlHelper.ExecuteNonQuery(transaction, CommandType.StoredProcedure, spName, Array.Empty<SqlParameter>());
			}
			return num;
		}

		// Token: 0x060087DB RID: 34779 RVA: 0x002C8CCC File Offset: 0x002C6ECC
		public static DataSet ExecuteDataset(string connectionString, CommandType commandType, string commandText)
		{
			return SqlHelper.ExecuteDataset(connectionString, commandType, commandText, null);
		}

		// Token: 0x060087DC RID: 34780 RVA: 0x002C8CE8 File Offset: 0x002C6EE8
		public static DataSet ExecuteDataset(string connectionString, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			DataSet dataSet;
			using (SqlConnection sqlConnection = new SqlConnection(connectionString))
			{
				sqlConnection.Open();
				dataSet = SqlHelper.ExecuteDataset(sqlConnection, commandType, commandText, commandParameters);
			}
			return dataSet;
		}

		// Token: 0x060087DD RID: 34781 RVA: 0x002C8D2C File Offset: 0x002C6F2C
		public static DataSet ExecuteDataset(string connectionString, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			DataSet dataSet;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(connectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				dataSet = SqlHelper.ExecuteDataset(connectionString, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				dataSet = SqlHelper.ExecuteDataset(connectionString, CommandType.StoredProcedure, spName);
			}
			return dataSet;
		}

		// Token: 0x060087DE RID: 34782 RVA: 0x002C8D74 File Offset: 0x002C6F74
		public static DataSet ExecuteDataset(SqlConnection connection, CommandType commandType, string commandText)
		{
			return SqlHelper.ExecuteDataset(connection, commandType, commandText, null);
		}

		// Token: 0x060087DF RID: 34783 RVA: 0x002C8D90 File Offset: 0x002C6F90
		public static DataSet ExecuteDataset(SqlConnection connection, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			SqlCommand sqlCommand = new SqlCommand();
			SqlHelper.PrepareCommand(sqlCommand, connection, null, commandType, commandText, commandParameters);
			SqlDataAdapter sqlDataAdapter = new SqlDataAdapter(sqlCommand);
			DataSet dataSet = new DataSet();
			sqlDataAdapter.Fill(dataSet);
			sqlCommand.Parameters.Clear();
			return dataSet;
		}

		// Token: 0x060087E0 RID: 34784 RVA: 0x002C8DD8 File Offset: 0x002C6FD8
		public static DataSet ExecuteDataset(SqlConnection connection, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			DataSet dataSet;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(connection.ConnectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				dataSet = SqlHelper.ExecuteDataset(connection, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				dataSet = SqlHelper.ExecuteDataset(connection, CommandType.StoredProcedure, spName);
			}
			return dataSet;
		}

		// Token: 0x060087E1 RID: 34785 RVA: 0x002C8E24 File Offset: 0x002C7024
		public static DataSet ExecuteDataset(SqlTransaction transaction, CommandType commandType, string commandText)
		{
			return SqlHelper.ExecuteDataset(transaction, commandType, commandText, null);
		}

		// Token: 0x060087E2 RID: 34786 RVA: 0x002C8E40 File Offset: 0x002C7040
		public static DataSet ExecuteDataset(SqlTransaction transaction, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			SqlCommand sqlCommand = new SqlCommand();
			SqlHelper.PrepareCommand(sqlCommand, transaction.Connection, transaction, commandType, commandText, commandParameters);
			SqlDataAdapter sqlDataAdapter = new SqlDataAdapter(sqlCommand);
			DataSet dataSet = new DataSet();
			sqlDataAdapter.Fill(dataSet);
			sqlCommand.Parameters.Clear();
			return dataSet;
		}

		// Token: 0x060087E3 RID: 34787 RVA: 0x002C8E8C File Offset: 0x002C708C
		public static DataSet ExecuteDataset(SqlTransaction transaction, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			DataSet dataSet;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(transaction.Connection.ConnectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				dataSet = SqlHelper.ExecuteDataset(transaction, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				dataSet = SqlHelper.ExecuteDataset(transaction, CommandType.StoredProcedure, spName);
			}
			return dataSet;
		}

		// Token: 0x060087E4 RID: 34788 RVA: 0x002C8EDC File Offset: 0x002C70DC
		private static SqlDataReader ExecuteReader(SqlConnection connection, SqlTransaction transaction, CommandType commandType, string commandText, SqlParameter[] commandParameters, SqlHelper.SqlConnectionOwnership connectionOwnership)
		{
			SqlCommand sqlCommand = new SqlCommand();
			SqlHelper.PrepareCommand(sqlCommand, connection, transaction, commandType, commandText, commandParameters);
			SqlDataReader sqlDataReader = ((connectionOwnership != SqlHelper.SqlConnectionOwnership.External) ? sqlCommand.ExecuteReader(CommandBehavior.CloseConnection) : sqlCommand.ExecuteReader());
			sqlCommand.Parameters.Clear();
			return sqlDataReader;
		}

		// Token: 0x060087E5 RID: 34789 RVA: 0x002C8F24 File Offset: 0x002C7124
		public static SqlDataReader ExecuteReader(string connectionString, CommandType commandType, string commandText)
		{
			return SqlHelper.ExecuteReader(connectionString, commandType, commandText, null);
		}

		// Token: 0x060087E6 RID: 34790 RVA: 0x002C8F40 File Offset: 0x002C7140
		public static SqlDataReader ExecuteReader(string connectionString, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			SqlConnection sqlConnection = new SqlConnection(connectionString);
			sqlConnection.Open();
			SqlDataReader sqlDataReader;
			try
			{
				sqlDataReader = SqlHelper.ExecuteReader(sqlConnection, null, commandType, commandText, commandParameters, SqlHelper.SqlConnectionOwnership.Internal);
			}
			catch
			{
				sqlConnection.Close();
				throw;
			}
			return sqlDataReader;
		}

		// Token: 0x060087E7 RID: 34791 RVA: 0x002C8F88 File Offset: 0x002C7188
		public static SqlDataReader ExecuteReader(string connectionString, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			SqlDataReader sqlDataReader;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(connectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				sqlDataReader = SqlHelper.ExecuteReader(connectionString, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				sqlDataReader = SqlHelper.ExecuteReader(connectionString, CommandType.StoredProcedure, spName);
			}
			return sqlDataReader;
		}

		// Token: 0x060087E8 RID: 34792 RVA: 0x002C8FD0 File Offset: 0x002C71D0
		public static SqlDataReader ExecuteReader(SqlConnection connection, CommandType commandType, string commandText)
		{
			return SqlHelper.ExecuteReader(connection, commandType, commandText, null);
		}

		// Token: 0x060087E9 RID: 34793 RVA: 0x002C8FEC File Offset: 0x002C71EC
		public static SqlDataReader ExecuteReader(SqlConnection connection, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			return SqlHelper.ExecuteReader(connection, null, commandType, commandText, commandParameters, SqlHelper.SqlConnectionOwnership.External);
		}

		// Token: 0x060087EA RID: 34794 RVA: 0x002C900C File Offset: 0x002C720C
		public static SqlDataReader ExecuteReader(SqlConnection connection, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			SqlDataReader sqlDataReader;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(connection.ConnectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				sqlDataReader = SqlHelper.ExecuteReader(connection, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				sqlDataReader = SqlHelper.ExecuteReader(connection, CommandType.StoredProcedure, spName);
			}
			return sqlDataReader;
		}

		// Token: 0x060087EB RID: 34795 RVA: 0x002C9058 File Offset: 0x002C7258
		public static SqlDataReader ExecuteReader(SqlTransaction transaction, CommandType commandType, string commandText)
		{
			return SqlHelper.ExecuteReader(transaction, commandType, commandText, null);
		}

		// Token: 0x060087EC RID: 34796 RVA: 0x002C9074 File Offset: 0x002C7274
		public static SqlDataReader ExecuteReader(SqlTransaction transaction, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			return SqlHelper.ExecuteReader(transaction.Connection, transaction, commandType, commandText, commandParameters, SqlHelper.SqlConnectionOwnership.External);
		}

		// Token: 0x060087ED RID: 34797 RVA: 0x002C9098 File Offset: 0x002C7298
		public static SqlDataReader ExecuteReader(SqlTransaction transaction, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			SqlDataReader sqlDataReader;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(transaction.Connection.ConnectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				sqlDataReader = SqlHelper.ExecuteReader(transaction, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				sqlDataReader = SqlHelper.ExecuteReader(transaction, CommandType.StoredProcedure, spName);
			}
			return sqlDataReader;
		}

		// Token: 0x060087EE RID: 34798 RVA: 0x002C90E8 File Offset: 0x002C72E8
		public static object ExecuteScalar(string connectionString, CommandType commandType, string commandText)
		{
			return SqlHelper.ExecuteScalar(connectionString, commandType, commandText, null);
		}

		// Token: 0x060087EF RID: 34799 RVA: 0x002C9104 File Offset: 0x002C7304
		public static object ExecuteScalar(string connectionString, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			object obj;
			using (SqlConnection sqlConnection = new SqlConnection(connectionString))
			{
				sqlConnection.Open();
				obj = SqlHelper.ExecuteScalar(sqlConnection, commandType, commandText, commandParameters);
			}
			return obj;
		}

		// Token: 0x060087F0 RID: 34800 RVA: 0x002C9148 File Offset: 0x002C7348
		public static object ExecuteScalar(string connectionString, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			object obj;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(connectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				obj = SqlHelper.ExecuteScalar(connectionString, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				obj = SqlHelper.ExecuteScalar(connectionString, CommandType.StoredProcedure, spName);
			}
			return obj;
		}

		// Token: 0x060087F1 RID: 34801 RVA: 0x002C9190 File Offset: 0x002C7390
		public static object ExecuteScalar(SqlConnection connection, CommandType commandType, string commandText)
		{
			return SqlHelper.ExecuteScalar(connection, commandType, commandText, null);
		}

		// Token: 0x060087F2 RID: 34802 RVA: 0x002C91AC File Offset: 0x002C73AC
		public static object ExecuteScalar(SqlConnection connection, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			SqlCommand sqlCommand = new SqlCommand();
			SqlHelper.PrepareCommand(sqlCommand, connection, null, commandType, commandText, commandParameters);
			object obj = sqlCommand.ExecuteScalar();
			sqlCommand.Parameters.Clear();
			return obj;
		}

		// Token: 0x060087F3 RID: 34803 RVA: 0x002C91E4 File Offset: 0x002C73E4
		public static object ExecuteScalar(SqlConnection connection, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			object obj;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(connection.ConnectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				obj = SqlHelper.ExecuteScalar(connection, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				obj = SqlHelper.ExecuteScalar(connection, CommandType.StoredProcedure, spName);
			}
			return obj;
		}

		// Token: 0x060087F4 RID: 34804 RVA: 0x002C9230 File Offset: 0x002C7430
		public static object ExecuteScalar(SqlTransaction transaction, CommandType commandType, string commandText)
		{
			return SqlHelper.ExecuteScalar(transaction, commandType, commandText, null);
		}

		// Token: 0x060087F5 RID: 34805 RVA: 0x002C924C File Offset: 0x002C744C
		public static object ExecuteScalar(SqlTransaction transaction, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			SqlCommand sqlCommand = new SqlCommand();
			SqlHelper.PrepareCommand(sqlCommand, transaction.Connection, transaction, commandType, commandText, commandParameters);
			object obj = sqlCommand.ExecuteScalar();
			sqlCommand.Parameters.Clear();
			return obj;
		}

		// Token: 0x060087F6 RID: 34806 RVA: 0x002C928C File Offset: 0x002C748C
		public static object ExecuteScalar(SqlTransaction transaction, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			object obj;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(transaction.Connection.ConnectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				obj = SqlHelper.ExecuteScalar(transaction, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				obj = SqlHelper.ExecuteScalar(transaction, CommandType.StoredProcedure, spName);
			}
			return obj;
		}

		// Token: 0x060087F7 RID: 34807 RVA: 0x002C92DC File Offset: 0x002C74DC
		public static XmlReader ExecuteXmlReader(SqlConnection connection, CommandType commandType, string commandText)
		{
			return SqlHelper.ExecuteXmlReader(connection, commandType, commandText, null);
		}

		// Token: 0x060087F8 RID: 34808 RVA: 0x002C92F8 File Offset: 0x002C74F8
		public static XmlReader ExecuteXmlReader(SqlConnection connection, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			SqlCommand sqlCommand = new SqlCommand();
			SqlHelper.PrepareCommand(sqlCommand, connection, null, commandType, commandText, commandParameters);
			XmlReader xmlReader = sqlCommand.ExecuteXmlReader();
			sqlCommand.Parameters.Clear();
			return xmlReader;
		}

		// Token: 0x060087F9 RID: 34809 RVA: 0x002C9330 File Offset: 0x002C7530
		public static XmlReader ExecuteXmlReader(SqlConnection connection, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			XmlReader xmlReader;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(connection.ConnectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				xmlReader = SqlHelper.ExecuteXmlReader(connection, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				xmlReader = SqlHelper.ExecuteXmlReader(connection, CommandType.StoredProcedure, spName);
			}
			return xmlReader;
		}

		// Token: 0x060087FA RID: 34810 RVA: 0x002C937C File Offset: 0x002C757C
		public static XmlReader ExecuteXmlReader(SqlTransaction transaction, CommandType commandType, string commandText)
		{
			return SqlHelper.ExecuteXmlReader(transaction, commandType, commandText, null);
		}

		// Token: 0x060087FB RID: 34811 RVA: 0x002C9398 File Offset: 0x002C7598
		public static XmlReader ExecuteXmlReader(SqlTransaction transaction, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			SqlCommand sqlCommand = new SqlCommand();
			SqlHelper.PrepareCommand(sqlCommand, transaction.Connection, transaction, commandType, commandText, commandParameters);
			XmlReader xmlReader = sqlCommand.ExecuteXmlReader();
			sqlCommand.Parameters.Clear();
			return xmlReader;
		}

		// Token: 0x060087FC RID: 34812 RVA: 0x002C93D8 File Offset: 0x002C75D8
		public static XmlReader ExecuteXmlReader(SqlTransaction transaction, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			XmlReader xmlReader;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(transaction.Connection.ConnectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				xmlReader = SqlHelper.ExecuteXmlReader(transaction, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				xmlReader = SqlHelper.ExecuteXmlReader(transaction, CommandType.StoredProcedure, spName);
			}
			return xmlReader;
		}

		// Token: 0x060087FD RID: 34813 RVA: 0x002C9428 File Offset: 0x002C7628
		public static void BeginExecuteNonQuery(SqlConnection connection, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			SqlCommand sqlCommand = new SqlCommand();
			SqlHelper.PrepareCommand(sqlCommand, connection, null, commandType, commandText, commandParameters);
			sqlCommand.BeginExecuteNonQuery();
			sqlCommand.Parameters.Clear();
		}

		// Token: 0x060087FE RID: 34814 RVA: 0x002C945C File Offset: 0x002C765C
		public static void BeginExecuteNonQuery(string connectionString, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
		{
			using (SqlConnection sqlConnection = new SqlConnection(connectionString))
			{
				sqlConnection.Open();
				SqlHelper.ExecuteNonQuery(sqlConnection, commandType, commandText, commandParameters);
			}
		}

		// Token: 0x060087FF RID: 34815 RVA: 0x002C94A0 File Offset: 0x002C76A0
		public static void BeginExecuteNonQuery(string connectionString, string spName, params object[] parameterValues)
		{
			bool flag = parameterValues != null && parameterValues.Length != 0;
			if (flag)
			{
				SqlParameter[] spParameterSet = SqlHelperParameterCache.GetSpParameterSet(connectionString, spName);
				SqlHelper.AssignParameterValues(spParameterSet, parameterValues);
				SqlHelper.ExecuteNonQuery(connectionString, CommandType.StoredProcedure, spName, spParameterSet);
			}
			else
			{
				SqlHelper.ExecuteNonQuery(connectionString, CommandType.StoredProcedure, spName);
			}
		}

		// Token: 0x02000F9D RID: 3997
		private enum SqlConnectionOwnership
		{
			// Token: 0x040053C3 RID: 21443
			Internal,
			// Token: 0x040053C4 RID: 21444
			External
		}
	}
}
