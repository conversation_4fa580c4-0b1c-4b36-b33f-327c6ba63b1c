﻿using System;

namespace Game.Base.Packets
{
	// Token: 0x02000F87 RID: 3975
	[AttributeUsage(AttributeTargets.Class, AllowMultiple = true, Inherited = false)]
	public class PacketLibAttribute : Attribute
	{
		// Token: 0x170014D0 RID: 5328
		// (get) Token: 0x06008764 RID: 34660 RVA: 0x00035D52 File Offset: 0x00033F52
		public int RawVersion
		{
			get
			{
				return this.m_rawVersion;
			}
		}

		// Token: 0x06008765 RID: 34661 RVA: 0x00035D5A File Offset: 0x00033F5A
		public PacketLibAttribute(int rawVersion)
		{
			this.m_rawVersion = rawVersion;
		}

		// Token: 0x0400539B RID: 21403
		private int m_rawVersion;
	}
}
