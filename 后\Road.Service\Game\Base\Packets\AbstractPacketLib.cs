﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Bussiness;
using EntityDatabase.PlayerModels;
using Game.Server;
using Game.Server.Buffer;
using Game.Server.ConsortiaTask;
using Game.Server.GameObjects;
using Game.Server.GameUtils;
using Game.Server.GMActives;
using Game.Server.LittleGame;
using Game.Server.Managers;
using Game.Server.Packets;
using Game.Server.Quests;
using Game.Server.Rooms;
using Game.Server.SceneMarryRooms;
using Game.Server.SpaRooms;
using log4net;
using Newtonsoft.Json;
using SqlDataProvider.Data;

namespace Game.Base.Packets
{
	// Token: 0x02000F81 RID: 3969
	[PacketLib(1)]
	public class AbstractPacketLib : IPacketLib
	{
		// Token: 0x06008604 RID: 34308 RVA: 0x00035C09 File Offset: 0x00033E09
		public AbstractPacketLib(GameClient client)
		{
			this.m_gameClient = client;
		}

		// Token: 0x06008605 RID: 34309 RVA: 0x002BD700 File Offset: 0x002BB900
		public GSPacketIn SendUserRelicItem(PlayerInfo player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(768);
			gspacketIn.WriteByte(2);
			gspacketIn.WriteBoolean(true);
			gspacketIn.WriteInt(3);
			gspacketIn.WriteInt(3);
			gspacketIn.WriteInt(player.RelicItem.Count);
			foreach (Sys_User_RelicItemTemplate sys_User_RelicItemTemplate in player.RelicItem)
			{
				gspacketIn.WriteInt(sys_User_RelicItemTemplate.itemID);
				gspacketIn.WriteInt(sys_User_RelicItemTemplate.level);
				gspacketIn.WriteInt(sys_User_RelicItemTemplate.stage);
				gspacketIn.WriteInt(sys_User_RelicItemTemplate.curExp);
				gspacketIn.WriteInt(sys_User_RelicItemTemplate.ShardNum);
				gspacketIn.WriteInt(0);
				IEnumerable<int> enumerable = sys_User_RelicItemTemplate._exProArr.Split(new char[] { ',' }).Select(new Func<string, int>(int.Parse));
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008606 RID: 34310 RVA: 0x002BD814 File Offset: 0x002BBA14
		public void SendFirstKillOpen()
		{
			GSPacketIn gspacketIn = new GSPacketIn(668);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteLong(DateTime.Parse(GameProperties.FirstKillBeginDate).ToUniversalTime().Subtract(new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc))
				.Ticks / 10000L);
			gspacketIn.WriteLong(DateTime.Parse(GameProperties.FirstKillEndDate).ToUniversalTime().Subtract(new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc))
				.Ticks / 10000L);
			gspacketIn.WriteBoolean(true);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008607 RID: 34311 RVA: 0x002BD8C8 File Offset: 0x002BBAC8
		public static IPacketLib CreatePacketLibForVersion(int rawVersion, GameClient client)
		{
			Type[] derivedClasses = ScriptMgr.GetDerivedClasses(typeof(IPacketLib));
			Type[] array = derivedClasses;
			Type[] array2 = array;
			foreach (Type type in array2)
			{
				foreach (PacketLibAttribute packetLibAttribute in type.GetCustomAttributes(typeof(PacketLibAttribute), false))
				{
					bool flag = packetLibAttribute.RawVersion != rawVersion;
					if (!flag)
					{
						try
						{
							return (IPacketLib)Activator.CreateInstance(type, new object[] { client });
						}
						catch (Exception ex)
						{
							bool isErrorEnabled = AbstractPacketLib.log.IsErrorEnabled;
							if (isErrorEnabled)
							{
								AbstractPacketLib.log.Error("error creating packetlib (" + type.FullName + ") for raw version " + rawVersion.ToString(), ex);
							}
						}
					}
				}
			}
			return null;
		}

		// Token: 0x06008608 RID: 34312 RVA: 0x00035C1A File Offset: 0x00033E1A
		public void SendTCP(GSPacketIn pkg)
		{
			this.m_gameClient.SendTCP(pkg);
		}

		// Token: 0x06008609 RID: 34313 RVA: 0x002BD9DC File Offset: 0x002BBBDC
		public void SendLoginFailed(string msg)
		{
			GSPacketIn gspacketIn = new GSPacketIn(1);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteString(msg);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600860A RID: 34314 RVA: 0x002BDA0C File Offset: 0x002BBC0C
		public void SendUpdateCountInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(725);
			gspacketIn.WriteByte(3);
			gspacketIn.WriteInt(player.Actives.PairBox.FreeReelCount);
			gspacketIn.WriteInt(player.Actives.PairBox.ReelCount);
			gspacketIn.WriteInt(player.Actives.PairBox.MarkCount);
			gspacketIn.WriteInt(player.Actives.PairBox.TotalBuyCount);
			gspacketIn.WriteInt(player.Actives.PairBox.FreeReelCount);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600860B RID: 34315 RVA: 0x002BDAA8 File Offset: 0x002BBCA8
		public void SendLoginSuccess()
		{
			bool flag = this.m_gameClient.Player != null;
			if (flag)
			{
				GSPacketIn gspacketIn = new GSPacketIn(1, this.m_gameClient.Player.PlayerCharacter.ID);
				gspacketIn.WriteByte(0);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.AreaID);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Attack);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Defence);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Agility);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Luck);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.GP);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Repute);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Gold);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Money);
				gspacketIn.WriteInt(this.m_gameClient.Player.CountMedal);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Score);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Hide);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.FightPower);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.ApprenticeshipState);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.MasterID);
				gspacketIn.WriteString(this.m_gameClient.Player.PlayerCharacter.MasterOrApprentices);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.GraduatesCount);
				gspacketIn.WriteString(this.m_gameClient.Player.PlayerCharacter.HonourOfMaster);
				gspacketIn.WriteDateTime(this.m_gameClient.Player.PlayerCharacter.FreezesDate);
				gspacketIn.WriteByte(this.m_gameClient.Player.PlayerCharacter.TypeVIP);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.VIPLevel);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.VIPExp);
				gspacketIn.WriteDateTime(this.m_gameClient.Player.PlayerCharacter.VIPExpireDay);
				gspacketIn.WriteDateTime(this.m_gameClient.Player.PlayerCharacter.LastDate);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.VIPNextLevelDaysNeeded);
				gspacketIn.WriteDateTime(DateTime.Now);
				gspacketIn.WriteBoolean(this.m_gameClient.Player.PlayerCharacter.CanTakeVipReward);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.AchievementPoint);
				gspacketIn.WriteString(this.m_gameClient.Player.PlayerCharacter.Honor);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.HonorID);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.AntiAddiction);
				gspacketIn.WriteBoolean(this.m_gameClient.Player.PlayerCharacter.Sex);
				gspacketIn.WriteString(this.m_gameClient.Player.PlayerCharacter.Style + "&" + this.m_gameClient.Player.PlayerCharacter.Colors);
				gspacketIn.WriteString(this.m_gameClient.Player.PlayerCharacter.Skin);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.ConsortiaID);
				gspacketIn.WriteString(this.m_gameClient.Player.PlayerCharacter.ConsortiaName);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.badgeID);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.DutyLevel);
				gspacketIn.WriteString(this.m_gameClient.Player.PlayerCharacter.DutyName);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Right);
				gspacketIn.WriteString(this.m_gameClient.Player.PlayerCharacter.ChairmanName);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.ConsortiaHonor);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.ConsortiaRiches);
				gspacketIn.WriteBoolean(this.m_gameClient.Player.PlayerCharacter.HasBagPassword);
				gspacketIn.WriteString(this.m_gameClient.Player.PlayerCharacter.PasswordQuest1);
				gspacketIn.WriteString(this.m_gameClient.Player.PlayerCharacter.PasswordQuest2);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.FailedPasswordAttemptCount);
				gspacketIn.WriteString(this.m_gameClient.Player.PlayerCharacter.UserName);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Nimbus);
				gspacketIn.WriteString(this.m_gameClient.Player.PlayerCharacter.PvePermission);
				gspacketIn.WriteString(this.m_gameClient.Player.PlayerCharacter.FightLibMission);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.AnswerSite);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.BoxProgression);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.GetBoxLevel);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.AlreadyGetBox);
				gspacketIn.WriteDateTime(this.m_gameClient.Player.PlayerCharacter.LastSpaDate);
				gspacketIn.WriteDateTime(this.m_gameClient.Player.PlayerCharacter.ShopFinallyGottenTime);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.UseOffer);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.DailyScore);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.DailyWinCount);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.DailyGameCount);
				gspacketIn.WriteBoolean(this.m_gameClient.Player.PlayerCharacter.DailyLeagueFirst);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.DailyLeagueLastScore);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.WeeklyScore);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.WeeklyGameCount);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.WeeklyRanking);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Texp.spdTexpExp);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Texp.attTexpExp);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Texp.defTexpExp);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Texp.hpTexpExp);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Texp.lukTexpExp);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Texp.texpTaskCount);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Texp.texpCount);
				gspacketIn.WriteDateTime(this.m_gameClient.Player.PlayerCharacter.Texp.texpTaskDate);
				gspacketIn.WriteBoolean(this.m_gameClient.Player.PlayerCharacter.IsOldPlayer);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.LuckStone);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteDateTime(DateTime.Now);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.TotemId);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.TakeCardNum);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.NecklaceExp);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.NecklaceCastLevel);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.Stive);
				this.SendTCP(gspacketIn);
			}
		}

		// Token: 0x0600860C RID: 34316 RVA: 0x002BE42C File Offset: 0x002BC62C
		public void SendCheckCode()
		{
			bool flag = this.m_gameClient.Player != null && (this.m_gameClient.Player.PlayerCharacter.CheckCount >= GameProperties.CHECKCODE_PER_GAME_COUNT || this.m_gameClient.Player.PlayerCharacter.ChatCount >= 5);
			if (flag)
			{
				bool flag2 = this.m_gameClient.Player.PlayerCharacter.CheckError == 0 && this.m_gameClient.Player.PlayerCharacter.ChatCount == 0;
				if (flag2)
				{
					this.m_gameClient.Player.PlayerCharacter.CheckCount += 10000;
				}
				GSPacketIn gspacketIn = new GSPacketIn(200, this.m_gameClient.Player.PlayerCharacter.ID, 10240);
				bool flag3 = this.m_gameClient.Player.PlayerCharacter.CheckError < 1;
				if (flag3)
				{
					gspacketIn.WriteByte(1);
				}
				else
				{
					gspacketIn.WriteByte(2);
				}
				gspacketIn.WriteBoolean(true);
				gspacketIn.WriteByte(2);
				bool flag4 = this.m_gameClient.Player.PlayerCharacter.ChatCount == 5;
				if (flag4)
				{
					gspacketIn.WriteString(LanguageMgr.GetTranslation("CheckCode.Chat", Array.Empty<object>()));
				}
				else
				{
					gspacketIn.WriteString(LanguageMgr.GetTranslation("CheckCode.Fight", Array.Empty<object>()));
				}
				this.m_gameClient.Player.PlayerCharacter.CheckCode = CheckCode.GenerateCheckCode();
				gspacketIn.Write(CheckCode.CreateImage(this.m_gameClient.Player.PlayerCharacter.CheckCode));
				this.SendTCP(gspacketIn);
			}
		}

		// Token: 0x0600860D RID: 34317 RVA: 0x002BE5E4 File Offset: 0x002BC7E4
		public void SendKitoff(string msg)
		{
			GSPacketIn gspacketIn = new GSPacketIn(2);
			gspacketIn.WriteString(msg);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600860E RID: 34318 RVA: 0x002BE60C File Offset: 0x002BC80C
		public void SendEditionError(string msg)
		{
			GSPacketIn gspacketIn = new GSPacketIn(12);
			gspacketIn.WriteString(msg);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600860F RID: 34319 RVA: 0x002BE634 File Offset: 0x002BC834
		public void SendWaitingRoom(bool result)
		{
			GSPacketIn gspacketIn = new GSPacketIn(16);
			gspacketIn.WriteByte(result ? 1 : 0);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008610 RID: 34320 RVA: 0x002BE664 File Offset: 0x002BC864
		public virtual GSPacketIn SendMessage(eMessageType type, string message)
		{
			GSPacketIn gspacketIn = new GSPacketIn(3);
			gspacketIn.WriteInt((int)type);
			gspacketIn.WriteString(message);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008611 RID: 34321 RVA: 0x002BE698 File Offset: 0x002BC898
		public void SendUpdatePrivateInfo(PlayerInfo info, int Medal)
		{
			GSPacketIn gspacketIn = new GSPacketIn(38, info.ID);
			gspacketIn.WriteInt(info.Money);
			gspacketIn.WriteInt(Medal);
			gspacketIn.WriteInt(info.Score);
			gspacketIn.WriteInt(info.Gold);
			gspacketIn.WriteInt(info.GiftToken);
			gspacketIn.WriteInt(info.LuckStone);
			gspacketIn.WriteInt(info.MyHornor);
			gspacketIn.WriteInt(info.DamageScore);
			gspacketIn.WriteInt(info.zhanling);
			gspacketIn.WriteInt(info.zhanlingExp);
			gspacketIn.WriteInt(info.zhanlingLevel);
			gspacketIn.WriteInt(info.zhanlingVipType);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008612 RID: 34322 RVA: 0x002BE754 File Offset: 0x002BC954
		public GSPacketIn SendUpdatePublicPlayer(PlayerInfo info)
		{
			GSPacketIn gspacketIn = new GSPacketIn(67, info.ID);
			gspacketIn.WriteInt(info.GP);
			gspacketIn.WriteInt(info.Offer);
			gspacketIn.WriteInt(info.RichesOffer);
			gspacketIn.WriteInt(info.RichesRob);
			gspacketIn.WriteInt(info.Win);
			gspacketIn.WriteInt(info.Total);
			gspacketIn.WriteInt(info.Escape);
			gspacketIn.WriteInt(info.Attack);
			gspacketIn.WriteInt(info.Defence);
			gspacketIn.WriteInt(info.Agility);
			gspacketIn.WriteInt(info.Luck);
			gspacketIn.WriteInt(info.Blood);
			gspacketIn.WriteInt(info.Hide);
			gspacketIn.WriteString(info.Style);
			gspacketIn.WriteString(info.Colors);
			gspacketIn.WriteString(info.Skin);
			gspacketIn.WriteBoolean(false);
			gspacketIn.WriteInt(info.ConsortiaID);
			gspacketIn.WriteString(info.ConsortiaName);
			gspacketIn.WriteInt(info.badgeID);
			gspacketIn.WriteInt(info.ConsortiaLevel);
			gspacketIn.WriteInt(info.ConsortiaRepute);
			gspacketIn.WriteInt(info.Nimbus);
			gspacketIn.WriteString(info.PvePermission);
			gspacketIn.WriteString(info.FightLibMission);
			gspacketIn.WriteInt(info.FightPower);
			gspacketIn.WriteInt(info.ApprenticeshipState);
			gspacketIn.WriteInt(info.MasterID);
			gspacketIn.WriteString(info.MasterOrApprentices);
			gspacketIn.WriteInt(info.GraduatesCount);
			gspacketIn.WriteString(info.HonourOfMaster);
			gspacketIn.WriteInt(info.AchievementPoint);
			gspacketIn.WriteString(info.Honor);
			gspacketIn.WriteInt(info.HonorID);
			gspacketIn.WriteDateTime(info.LastSpaDate);
			gspacketIn.WriteInt(info.GiftGP);
			gspacketIn.WriteInt(info.GiftLevel);
			gspacketIn.WriteDateTime(info.ShopFinallyGottenTime);
			gspacketIn.WriteInt(info.UseOffer);
			gspacketIn.WriteInt(info.DailyScore);
			gspacketIn.WriteInt(info.DailyWinCount);
			gspacketIn.WriteInt(info.DailyGameCount);
			gspacketIn.WriteInt(info.WeeklyScore);
			gspacketIn.WriteInt(info.WeeklyGameCount);
			gspacketIn.WriteInt(info.Texp.spdTexpExp);
			gspacketIn.WriteInt(info.Texp.attTexpExp);
			gspacketIn.WriteInt(info.Texp.defTexpExp);
			gspacketIn.WriteInt(info.Texp.hpTexpExp);
			gspacketIn.WriteInt(info.Texp.lukTexpExp);
			gspacketIn.WriteInt(info.Texp.texpTaskCount);
			gspacketIn.WriteInt(info.Texp.texpCount);
			gspacketIn.WriteDateTime(info.Texp.texpTaskDate);
			gspacketIn.WriteInt(info.TakeCardNum);
			gspacketIn.WriteInt(10);
			for (int i = 1; i <= 10; i++)
			{
				gspacketIn.WriteInt(i);
				gspacketIn.WriteByte(10);
			}
			UserElementProInfo elementPro = info.GetElementPro();
			gspacketIn.WriteInt(elementPro.Fire);
			gspacketIn.WriteInt(elementPro.Wind);
			gspacketIn.WriteInt(elementPro.Land);
			gspacketIn.WriteInt(elementPro.Water);
			gspacketIn.WriteInt(elementPro.Light);
			gspacketIn.WriteInt(elementPro.Dark);
			gspacketIn.WriteInt(elementPro.FireDefence);
			gspacketIn.WriteInt(elementPro.WindDefence);
			gspacketIn.WriteInt(elementPro.LandDefence);
			gspacketIn.WriteInt(elementPro.WaterDefence);
			gspacketIn.WriteInt(elementPro.LightDefence);
			gspacketIn.WriteInt(elementPro.DarkDefence);
			for (int j = 0; j < 2; j++)
			{
				bool flag = j == 0;
				if (flag)
				{
					gspacketIn.WriteInt(info.EmblemInfoAttackSKill);
					bool flag2 = info.EmblemInfoAttackSKill != 0;
					if (flag2)
					{
						gspacketIn.WriteInt(info.EmblemInfoAttackSKill_Value);
						gspacketIn.WriteInt(info.EmblemInfoAttackSKill_Rate);
					}
				}
				else
				{
					gspacketIn.WriteInt(info.EmblemInfoDefendSKill);
					bool flag3 = info.EmblemInfoDefendSKill != 0;
					if (flag3)
					{
						gspacketIn.WriteInt(info.EmblemInfoDefendSKill_Value);
						gspacketIn.WriteInt(info.EmblemInfoDefendSKill_Rate);
					}
				}
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008613 RID: 34323 RVA: 0x002BEBC8 File Offset: 0x002BCDC8
		public void SendPingTime(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(4);
			player.PingStart = DateTime.Now.Ticks;
			gspacketIn.WriteInt(player.PlayerCharacter.AntiAddiction);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008614 RID: 34324 RVA: 0x002BEC0C File Offset: 0x002BCE0C
		public GSPacketIn SendNetWork(int id, long delay)
		{
			GSPacketIn gspacketIn = new GSPacketIn(6, id);
			gspacketIn.WriteInt((int)delay / 1000 / 10);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008615 RID: 34325 RVA: 0x002BEC44 File Offset: 0x002BCE44
		public GSPacketIn SendUserEquip(PlayerInfo player, List<ItemInfo> items)
		{
			GSPacketIn gspacketIn = new GSPacketIn(74, player.ID, 10240);
			gspacketIn.WriteInt(player.ID);
			gspacketIn.WriteString(player.NickName);
			gspacketIn.WriteInt(player.Agility);
			gspacketIn.WriteInt(player.Attack);
			gspacketIn.WriteString(player.Colors);
			gspacketIn.WriteString(player.Skin);
			gspacketIn.WriteInt(player.Defence);
			gspacketIn.WriteInt(player.GP);
			gspacketIn.WriteInt(player.Grade);
			gspacketIn.WriteInt(player.Luck);
			gspacketIn.WriteInt(player.Blood);
			gspacketIn.WriteInt(player.Hide);
			gspacketIn.WriteInt(player.Repute);
			gspacketIn.WriteBoolean(player.Sex);
			gspacketIn.WriteString(player.Style);
			gspacketIn.WriteInt(player.Offer);
			gspacketIn.WriteByte(player.TypeVIP);
			gspacketIn.WriteInt(player.VIPLevel);
			gspacketIn.WriteInt(player.Win);
			gspacketIn.WriteInt(player.Total);
			gspacketIn.WriteInt(player.Escape);
			gspacketIn.WriteInt(player.ConsortiaID);
			gspacketIn.WriteString(player.ConsortiaName);
			gspacketIn.WriteInt(player.badgeID);
			gspacketIn.WriteInt(player.RichesOffer);
			gspacketIn.WriteInt(player.RichesRob);
			gspacketIn.WriteBoolean(player.IsMarried);
			gspacketIn.WriteInt(player.SpouseID);
			gspacketIn.WriteString(player.SpouseName);
			gspacketIn.WriteString(player.DutyName);
			gspacketIn.WriteInt(player.Nimbus);
			gspacketIn.WriteInt(player.FightPower);
			gspacketIn.WriteInt(player.ApprenticeshipState);
			gspacketIn.WriteInt(player.MasterID);
			gspacketIn.WriteString(player.MasterOrApprentices);
			gspacketIn.WriteInt(player.GraduatesCount);
			gspacketIn.WriteString(player.HonourOfMaster);
			gspacketIn.WriteInt(player.AchievementPoint);
			gspacketIn.WriteString(player.Honor);
			gspacketIn.WriteDateTime(player.LastDate);
			gspacketIn.WriteInt(player.Texp.spdTexpExp);
			gspacketIn.WriteInt(player.Texp.attTexpExp);
			gspacketIn.WriteInt(player.Texp.defTexpExp);
			gspacketIn.WriteInt(player.Texp.hpTexpExp);
			gspacketIn.WriteInt(player.Texp.lukTexpExp);
			gspacketIn.WriteBoolean(player.DailyLeagueFirst);
			gspacketIn.WriteInt(player.DailyLeagueLastScore);
			gspacketIn.WriteInt(player.TotemId);
			gspacketIn.WriteInt(player.NecklaceExp);
			gspacketIn.WriteInt(player.NecklaceCastLevel);
			gspacketIn.WriteInt(items.Count);
			foreach (ItemInfo itemInfo in items)
			{
				gspacketIn.WriteByte((byte)itemInfo.BagType);
				gspacketIn.WriteInt(itemInfo.UserID);
				gspacketIn.WriteInt(itemInfo.ItemID);
				gspacketIn.WriteInt(itemInfo.Count);
				gspacketIn.WriteInt(itemInfo.Place);
				gspacketIn.WriteInt(itemInfo.TemplateID);
				gspacketIn.WriteInt(itemInfo.AttackCompose);
				gspacketIn.WriteInt(itemInfo.DefendCompose);
				gspacketIn.WriteInt(itemInfo.AgilityCompose);
				gspacketIn.WriteInt(itemInfo.LuckCompose);
				gspacketIn.WriteInt(itemInfo.StrengthenLevel);
				gspacketIn.WriteBoolean(itemInfo.IsBinds);
				gspacketIn.WriteBoolean(itemInfo.IsJudge);
				gspacketIn.WriteDateTime(itemInfo.BeginDate);
				gspacketIn.WriteInt(itemInfo.ValidDate);
				gspacketIn.WriteString(itemInfo.Color);
				gspacketIn.WriteString(itemInfo.Skin);
				gspacketIn.WriteBoolean(itemInfo.IsUsed);
				gspacketIn.WriteInt(itemInfo.Hole1);
				gspacketIn.WriteInt(itemInfo.Hole2);
				gspacketIn.WriteInt(itemInfo.Hole3);
				gspacketIn.WriteInt(itemInfo.Hole4);
				gspacketIn.WriteInt(itemInfo.Hole5);
				gspacketIn.WriteInt(itemInfo.Hole6);
				gspacketIn.WriteString(itemInfo.Pic);
				gspacketIn.WriteInt(itemInfo.RefineryLevel);
				gspacketIn.WriteDateTime(DateTime.Now.AddDays(-100.0));
				gspacketIn.WriteByte((byte)itemInfo.Hole5Level);
				gspacketIn.WriteInt(itemInfo.Hole5Exp);
				gspacketIn.WriteByte((byte)itemInfo.Hole6Level);
				gspacketIn.WriteInt(itemInfo.Hole6Exp);
				gspacketIn.WriteBoolean(itemInfo.GoldValidDate());
				bool flag = itemInfo.GoldValidDate();
				if (flag)
				{
					gspacketIn.WriteInt(itemInfo.goldValidDate);
					gspacketIn.WriteDateTime(itemInfo.goldBeginTime);
				}
				gspacketIn.WriteString(itemInfo.LatentEnergyCurStr);
				gspacketIn.WriteString(itemInfo.LatentEnergyNewStr);
				gspacketIn.WriteDateTime(itemInfo.LatentEnergyEndTime);
				gspacketIn.WriteInt(itemInfo.MagicLevel);
				gspacketIn.WriteInt(itemInfo.MagicSpiritLevel);
			}
			gspacketIn.WriteInt(player.GetTotemUpgrade().Count);
			foreach (KeyValuePair<int, int> keyValuePair in player.GetTotemUpgrade())
			{
				gspacketIn.WriteInt(keyValuePair.Key);
				gspacketIn.WriteInt(keyValuePair.Value);
			}
			bool flag2 = player.GhostEquipList != null;
			if (flag2)
			{
				Dictionary<string, UserEquipGhostInfo> dictionary = JsonConvert.DeserializeObject<Dictionary<string, UserEquipGhostInfo>>(player.GhostEquipList);
				bool flag3 = dictionary == null;
				if (flag3)
				{
					gspacketIn.WriteInt(0);
				}
				else
				{
					gspacketIn.WriteInt(dictionary.Count);
					foreach (UserEquipGhostInfo userEquipGhostInfo in dictionary.Values)
					{
						gspacketIn.WriteInt(userEquipGhostInfo.BagType);
						gspacketIn.WriteInt(userEquipGhostInfo.Place);
						gspacketIn.WriteInt(userEquipGhostInfo.Level);
						gspacketIn.WriteInt(userEquipGhostInfo.TotalGhost);
					}
				}
			}
			else
			{
				gspacketIn.WriteInt(0);
			}
			gspacketIn.Compress();
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008616 RID: 34326 RVA: 0x002BF2EC File Offset: 0x002BD4EC
		public void SendDateTime()
		{
			GSPacketIn gspacketIn = new GSPacketIn(5);
			gspacketIn.WriteDateTime(DateTime.Now);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008617 RID: 34327 RVA: 0x002BF318 File Offset: 0x002BD518
		public GSPacketIn SendDailyAward(GamePlayer player, int getWay)
		{
			bool flag = false;
			bool flag2 = getWay == 1 && DateTime.Now.Date != player.PlayerCharacter.LastAuncherAward.Date;
			if (flag2)
			{
				flag = true;
			}
			bool flag3 = DateTime.Now.Date != player.PlayerCharacter.LastAward.Date;
			if (flag3)
			{
				flag = true;
			}
			GSPacketIn gspacketIn = new GSPacketIn(13);
			gspacketIn.WriteBoolean(flag);
			gspacketIn.WriteInt(getWay);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008618 RID: 34328 RVA: 0x002BF3B4 File Offset: 0x002BD5B4
		public GSPacketIn SendOpenVIP(GamePlayer Player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(92, Player.PlayerCharacter.ID);
			gspacketIn.WriteByte(Player.PlayerCharacter.TypeVIP);
			gspacketIn.WriteInt(Player.PlayerCharacter.VIPLevel);
			gspacketIn.WriteInt(Player.PlayerCharacter.VIPExp);
			gspacketIn.WriteDateTime(Player.PlayerCharacter.VIPExpireDay);
			gspacketIn.WriteDateTime(Player.PlayerCharacter.VIPLastDate);
			gspacketIn.WriteInt(Player.PlayerCharacter.VIPNextLevelDaysNeeded);
			gspacketIn.WriteBoolean(Player.PlayerCharacter.CanTakeVipReward);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008619 RID: 34329 RVA: 0x002BF460 File Offset: 0x002BD660
		public void SendShopGoodsCountUpdate(List<ShopFreeCountInfo> list)
		{
			GSPacketIn gspacketIn = new GSPacketIn(168);
			gspacketIn.WriteInt(list.Count);
			foreach (ShopFreeCountInfo shopFreeCountInfo in list)
			{
				gspacketIn.WriteInt(shopFreeCountInfo.ShopID);
				gspacketIn.WriteInt(shopFreeCountInfo.Count);
			}
			gspacketIn.WriteInt(0);
			gspacketIn.WriteInt(0);
			gspacketIn.WriteInt(0);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600861A RID: 34330 RVA: 0x002BF500 File Offset: 0x002BD700
		public GSPacketIn SendUpdateRoomList(List<BaseRoom> roomlist)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94);
			gspacketIn.WriteByte(9);
			gspacketIn.WriteInt(roomlist.Count);
			int num = ((roomlist.Count < 8) ? roomlist.Count : 8);
			gspacketIn.WriteInt(num);
			for (int i = 0; i < num; i++)
			{
				BaseRoom baseRoom = roomlist[i];
				gspacketIn.WriteInt(baseRoom.RoomId);
				gspacketIn.WriteByte((byte)baseRoom.RoomType);
				gspacketIn.WriteByte(baseRoom.TimeMode);
				gspacketIn.WriteByte((byte)baseRoom.PlayerCount);
				gspacketIn.WriteByte(0);
				gspacketIn.WriteByte(0);
				gspacketIn.WriteByte((byte)baseRoom.PlacesCount);
				gspacketIn.WriteBoolean(!string.IsNullOrEmpty(baseRoom.Password));
				gspacketIn.WriteInt(baseRoom.MapId);
				gspacketIn.WriteBoolean(baseRoom.IsPlaying);
				gspacketIn.WriteString(baseRoom.Name);
				gspacketIn.WriteByte((byte)baseRoom.GameType);
				gspacketIn.WriteByte((byte)baseRoom.HardLevel);
				gspacketIn.WriteInt(baseRoom.LevelLimits);
				gspacketIn.WriteBoolean(baseRoom.IsOpenBoss);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600861B RID: 34331 RVA: 0x002BF640 File Offset: 0x002BD840
		public GSPacketIn SendSceneAddPlayer(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(18, player.PlayerCharacter.ID);
			gspacketIn.WriteInt(player.PlayerCharacter.Grade);
			gspacketIn.WriteBoolean(player.PlayerCharacter.Sex);
			gspacketIn.WriteString(player.PlayerCharacter.NickName);
			gspacketIn.WriteByte(player.PlayerCharacter.TypeVIP);
			gspacketIn.WriteInt(player.PlayerCharacter.VIPLevel);
			gspacketIn.WriteString(player.PlayerCharacter.ConsortiaName);
			gspacketIn.WriteInt(player.PlayerCharacter.Offer);
			gspacketIn.WriteInt(player.PlayerCharacter.Win);
			gspacketIn.WriteInt(player.PlayerCharacter.Total);
			gspacketIn.WriteInt(player.PlayerCharacter.Escape);
			gspacketIn.WriteInt(player.PlayerCharacter.ConsortiaID);
			gspacketIn.WriteInt(player.PlayerCharacter.Repute);
			gspacketIn.WriteBoolean(player.PlayerCharacter.IsMarried);
			bool isMarried = player.PlayerCharacter.IsMarried;
			if (isMarried)
			{
				gspacketIn.WriteInt(player.PlayerCharacter.SpouseID);
				gspacketIn.WriteString(player.PlayerCharacter.SpouseName);
			}
			gspacketIn.WriteString(player.PlayerCharacter.UserName);
			gspacketIn.WriteInt(player.PlayerCharacter.FightPower);
			gspacketIn.WriteInt(player.PlayerCharacter.ApprenticeshipState);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600861C RID: 34332 RVA: 0x002BF7C4 File Offset: 0x002BD9C4
		public GSPacketIn SendSceneRemovePlayer(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(21, player.PlayerCharacter.ID);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600861D RID: 34333 RVA: 0x002BF7F4 File Offset: 0x002BD9F4
		public GSPacketIn SendRoomPlayerAdd(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94, player.PlayerId);
			gspacketIn.WriteByte(4);
			bool flag = false;
			bool flag2 = player.CurrentRoom.Game != null;
			if (flag2)
			{
				flag = true;
			}
			gspacketIn.WriteBoolean(flag);
			gspacketIn.WriteByte((byte)player.CurrentRoomIndex);
			gspacketIn.WriteByte((byte)player.CurrentRoomTeam);
			gspacketIn.WriteBoolean(false);
			gspacketIn.WriteInt(player.PlayerCharacter.Grade);
			gspacketIn.WriteInt(player.PlayerCharacter.Offer);
			gspacketIn.WriteInt(player.PlayerCharacter.Hide);
			gspacketIn.WriteInt(player.PlayerCharacter.Repute);
			gspacketIn.WriteInt((int)player.PingTime / 1000 / 10);
			gspacketIn.WriteInt(player.AreaID);
			gspacketIn.WriteInt(player.PlayerCharacter.Info.ActivityTanabataNum);
			gspacketIn.WriteInt(player.PlayerCharacter.ID);
			gspacketIn.WriteString(player.PlayerCharacter.NickName);
			gspacketIn.WriteByte(player.PlayerCharacter.TypeVIP);
			gspacketIn.WriteInt(player.PlayerCharacter.VIPLevel);
			gspacketIn.WriteBoolean(player.PlayerCharacter.Sex);
			gspacketIn.WriteString(player.PlayerCharacter.Style);
			gspacketIn.WriteString(player.PlayerCharacter.Colors);
			gspacketIn.WriteString(player.PlayerCharacter.Skin);
			PacketIn packetIn = gspacketIn;
			ItemInfo itemAt = player.MainBag.GetItemAt(6);
			packetIn.WriteInt((itemAt != null) ? itemAt.TemplateID : (-1));
			bool flag3 = player.SecondWeapon == null;
			if (flag3)
			{
				gspacketIn.WriteInt(0);
			}
			else
			{
				gspacketIn.WriteInt(player.SecondWeapon.TemplateID);
			}
			gspacketIn.WriteInt(player.PlayerCharacter.ConsortiaID);
			gspacketIn.WriteString(player.PlayerCharacter.ConsortiaName);
			gspacketIn.WriteInt(player.PlayerCharacter.badgeID);
			gspacketIn.WriteInt(player.PlayerCharacter.Win);
			gspacketIn.WriteInt(player.PlayerCharacter.Total);
			gspacketIn.WriteInt(player.PlayerCharacter.Escape);
			gspacketIn.WriteInt(player.PlayerCharacter.ConsortiaLevel);
			gspacketIn.WriteInt(player.PlayerCharacter.ConsortiaRepute);
			gspacketIn.WriteBoolean(player.PlayerCharacter.IsMarried);
			bool isMarried = player.PlayerCharacter.IsMarried;
			if (isMarried)
			{
				gspacketIn.WriteInt(player.PlayerCharacter.SpouseID);
				gspacketIn.WriteString(player.PlayerCharacter.SpouseName);
			}
			gspacketIn.WriteString(player.PlayerCharacter.UserName);
			gspacketIn.WriteInt(player.PlayerCharacter.Nimbus);
			gspacketIn.WriteInt(player.PlayerCharacter.FightPower);
			gspacketIn.WriteInt(player.PlayerCharacter.ApprenticeshipState);
			gspacketIn.WriteInt(player.PlayerCharacter.MasterID);
			gspacketIn.WriteString(player.PlayerCharacter.MasterOrApprentices);
			gspacketIn.WriteInt(player.PlayerCharacter.GraduatesCount);
			gspacketIn.WriteString(player.PlayerCharacter.HonourOfMaster);
			gspacketIn.WriteBoolean(player.PlayerCharacter.DailyLeagueFirst);
			gspacketIn.WriteInt(player.PlayerCharacter.DailyLeagueLastScore);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600861E RID: 34334 RVA: 0x002BFB58 File Offset: 0x002BDD58
		public GSPacketIn SendRoomPlayerRemove(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94, player.PlayerId);
			gspacketIn.WriteByte(5);
			gspacketIn.Parameter1 = player.PlayerId;
			gspacketIn.WriteInt(player.AreaID);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600861F RID: 34335 RVA: 0x002BFBA4 File Offset: 0x002BDDA4
		public GSPacketIn SendRoomUpdatePlayerStates(byte[] states)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94);
			gspacketIn.WriteByte(15);
			for (int i = 0; i < states.Length; i++)
			{
				gspacketIn.WriteByte(states[i]);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008620 RID: 34336 RVA: 0x002BFBF0 File Offset: 0x002BDDF0
		public GSPacketIn SendRoomUpdatePlacesStates(int[] states)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94);
			gspacketIn.WriteByte(10);
			for (int i = 0; i < states.Length; i++)
			{
				gspacketIn.WriteInt(states[i]);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008621 RID: 34337 RVA: 0x002BFC3C File Offset: 0x002BDE3C
		public GSPacketIn SendRoomPlayerChangedTeam(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94, player.PlayerId);
			gspacketIn.WriteByte(6);
			gspacketIn.WriteByte((byte)player.CurrentRoomTeam);
			gspacketIn.WriteByte((byte)player.CurrentRoomIndex);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008622 RID: 34338 RVA: 0x002BFC8C File Offset: 0x002BDE8C
		public GSPacketIn SendRoomCreate(BaseRoom room)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94);
			gspacketIn.WriteByte(0);
			gspacketIn.WriteInt(room.RoomId);
			gspacketIn.WriteByte((byte)room.RoomType);
			gspacketIn.WriteByte((byte)room.HardLevel);
			gspacketIn.WriteByte(room.TimeMode);
			gspacketIn.WriteByte((byte)room.PlayerCount);
			gspacketIn.WriteByte(0);
			gspacketIn.WriteByte((byte)room.PlacesCount);
			gspacketIn.WriteBoolean(!string.IsNullOrEmpty(room.Password));
			gspacketIn.WriteInt(room.MapId);
			gspacketIn.WriteBoolean(room.IsPlaying);
			gspacketIn.WriteString(room.Name);
			gspacketIn.WriteByte((byte)room.GameType);
			gspacketIn.WriteInt(room.LevelLimits);
			gspacketIn.WriteBoolean(room.isCrosszone);
			gspacketIn.WriteBoolean(room.isWithinLeageTime);
			gspacketIn.WriteBoolean(room.IsOpenBoss);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008623 RID: 34339 RVA: 0x002BFD90 File Offset: 0x002BDF90
		public GSPacketIn SendRoomLoginResult(bool result)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteBoolean(result);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008624 RID: 34340 RVA: 0x002BFDC4 File Offset: 0x002BDFC4
		public GSPacketIn SendRoomPairUpStart(BaseRoom room)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94);
			gspacketIn.WriteByte(13);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008625 RID: 34341 RVA: 0x002BFDF0 File Offset: 0x002BDFF0
		public GSPacketIn SendRoomType(GamePlayer player, BaseRoom game)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94);
			gspacketIn.WriteByte(12);
			gspacketIn.WriteByte((byte)game.GameStyle);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008626 RID: 34342 RVA: 0x002BFE2C File Offset: 0x002BE02C
		public GSPacketIn SendRoomPairUpCancel(BaseRoom room)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94);
			gspacketIn.WriteByte(11);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008627 RID: 34343 RVA: 0x002BFE58 File Offset: 0x002BE058
		public GSPacketIn SendEquipChange(GamePlayer player, int place, int goodsID, string style)
		{
			GSPacketIn gspacketIn = new GSPacketIn(66, player.PlayerCharacter.ID);
			gspacketIn.WriteByte((byte)place);
			gspacketIn.WriteInt(goodsID);
			gspacketIn.WriteString(style);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008628 RID: 34344 RVA: 0x002BFEA0 File Offset: 0x002BE0A0
		public GSPacketIn SendGameRoomSetupChange(BaseRoom room)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94);
			gspacketIn.WriteByte(2);
			gspacketIn.WriteBoolean(room.IsOpenBoss);
			bool isOpenBoss = room.IsOpenBoss;
			if (isOpenBoss)
			{
				gspacketIn.WriteString(room.Pic);
			}
			gspacketIn.WriteInt(room.MapId);
			gspacketIn.WriteByte((byte)room.RoomType);
			gspacketIn.WriteString((room.Password == null) ? "" : room.Password);
			gspacketIn.WriteString((room.Name == null) ? "弹弹堂" : room.Name);
			gspacketIn.WriteByte(room.TimeMode);
			gspacketIn.WriteByte((byte)room.HardLevel);
			gspacketIn.WriteInt(room.LevelLimits);
			gspacketIn.WriteBoolean(room.isCrosszone);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008629 RID: 34345 RVA: 0x002BFF7C File Offset: 0x002BE17C
		public GSPacketIn SendFusionPreview(GamePlayer player, Dictionary<int, double> previewItemList, bool isbind, int MinValid)
		{
			GSPacketIn gspacketIn = new GSPacketIn(76, player.PlayerCharacter.ID);
			gspacketIn.WriteInt(previewItemList.Count);
			foreach (KeyValuePair<int, double> keyValuePair in previewItemList)
			{
				gspacketIn.WriteInt(keyValuePair.Key);
				gspacketIn.WriteInt(MinValid);
				int num = Convert.ToInt32(keyValuePair.Value);
				gspacketIn.WriteInt((num > 100) ? 100 : ((num >= 0) ? num : 0));
			}
			gspacketIn.WriteBoolean(isbind);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600862A RID: 34346 RVA: 0x002C003C File Offset: 0x002BE23C
		public GSPacketIn SendFusionResult(GamePlayer player, bool result)
		{
			GSPacketIn gspacketIn = new GSPacketIn(78, player.PlayerCharacter.ID);
			gspacketIn.WriteBoolean(result);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600862B RID: 34347 RVA: 0x002C0074 File Offset: 0x002BE274
		public GSPacketIn SendRefineryPreview(GamePlayer player, int templateid, bool isbind, ItemInfo item)
		{
			GSPacketIn gspacketIn = new GSPacketIn(111, player.PlayerCharacter.ID);
			gspacketIn.WriteInt(templateid);
			gspacketIn.WriteInt(item.ValidDate);
			gspacketIn.WriteBoolean(isbind);
			gspacketIn.WriteInt(item.AgilityCompose);
			gspacketIn.WriteInt(item.AttackCompose);
			gspacketIn.WriteInt(item.DefendCompose);
			gspacketIn.WriteInt(item.LuckCompose);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600862C RID: 34348 RVA: 0x002C00F8 File Offset: 0x002BE2F8
		public void SendUpdateInventorySlot(PlayerInventory bag, int[] updatedSlots)
		{
			bool flag = this.m_gameClient.Player == null;
			if (!flag)
			{
				GSPacketIn gspacketIn = new GSPacketIn(64, this.m_gameClient.Player.PlayerCharacter.ID, 10240);
				gspacketIn.WriteInt(bag.BagType);
				gspacketIn.WriteInt(updatedSlots.Length);
				foreach (int num in updatedSlots)
				{
					gspacketIn.WriteInt(num);
					ItemInfo itemAt = bag.GetItemAt(num);
					bool flag2 = itemAt == null;
					if (flag2)
					{
						gspacketIn.WriteBoolean(false);
					}
					else
					{
						gspacketIn.WriteBoolean(true);
						gspacketIn.WriteInt(itemAt.UserID);
						gspacketIn.WriteInt(itemAt.ItemID);
						gspacketIn.WriteInt(itemAt.Count);
						gspacketIn.WriteInt(itemAt.Place);
						gspacketIn.WriteInt(itemAt.TemplateID);
						gspacketIn.WriteInt(itemAt.AttackCompose);
						gspacketIn.WriteInt(itemAt.DefendCompose);
						gspacketIn.WriteInt(itemAt.AgilityCompose);
						gspacketIn.WriteInt(itemAt.LuckCompose);
						gspacketIn.WriteInt(itemAt.StrengthenLevel);
						gspacketIn.WriteInt(itemAt.StrengthenExp);
						gspacketIn.WriteBoolean(itemAt.IsBinds);
						gspacketIn.WriteBoolean(itemAt.IsJudge);
						gspacketIn.WriteDateTime(itemAt.BeginDate);
						gspacketIn.WriteInt(itemAt.ValidDate);
						gspacketIn.WriteString((itemAt.Color == null) ? "" : itemAt.Color);
						gspacketIn.WriteString((itemAt.Skin == null) ? "" : itemAt.Skin);
						gspacketIn.WriteBoolean(itemAt.IsUsed);
						gspacketIn.WriteInt(itemAt.Hole1);
						gspacketIn.WriteInt(itemAt.Hole2);
						gspacketIn.WriteInt(itemAt.Hole3);
						gspacketIn.WriteInt(itemAt.Hole4);
						gspacketIn.WriteInt(itemAt.Hole5);
						gspacketIn.WriteInt(itemAt.Hole6);
						gspacketIn.WriteString(itemAt.Pic);
						gspacketIn.WriteInt(itemAt.RefineryLevel);
						gspacketIn.WriteDateTime(DateTime.Now.AddDays(-100.0));
						gspacketIn.WriteInt(itemAt.StrengthenTimes);
						gspacketIn.WriteByte((byte)itemAt.Hole5Level);
						gspacketIn.WriteInt(itemAt.Hole5Exp);
						gspacketIn.WriteByte((byte)itemAt.Hole6Level);
						gspacketIn.WriteInt(itemAt.Hole6Exp);
						gspacketIn.WriteBoolean(itemAt.GoldValidDate());
						bool flag3 = itemAt.GoldValidDate();
						if (flag3)
						{
							gspacketIn.WriteInt(itemAt.goldValidDate);
							gspacketIn.WriteDateTime(itemAt.goldBeginTime);
						}
						gspacketIn.WriteString(itemAt.LatentEnergyCurStr);
						gspacketIn.WriteString(itemAt.LatentEnergyNewStr);
						gspacketIn.WriteDateTime(itemAt.LatentEnergyEndTime);
						gspacketIn.WriteInt(itemAt.MagicExp);
						gspacketIn.WriteInt(itemAt.MagicLevel);
						gspacketIn.WriteInt(itemAt.MagicSpiritLevel);
					}
				}
				this.SendTCP(gspacketIn);
			}
		}

		// Token: 0x0600862D RID: 34349 RVA: 0x002C0430 File Offset: 0x002BE630
		public GSPacketIn SendAddFriend(PlayerInfo user, int relation, bool state)
		{
			GSPacketIn gspacketIn = new GSPacketIn(160, user.ID);
			gspacketIn.WriteByte(160);
			gspacketIn.WriteBoolean(state);
			gspacketIn.WriteInt(user.ID);
			gspacketIn.WriteString(user.NickName);
			gspacketIn.WriteByte(user.TypeVIP);
			gspacketIn.WriteInt(user.VIPLevel);
			gspacketIn.WriteBoolean(user.Sex);
			gspacketIn.WriteString(user.Style);
			gspacketIn.WriteString(user.Colors);
			gspacketIn.WriteString(user.Skin);
			gspacketIn.WriteInt((user.State == 1) ? 1 : 0);
			gspacketIn.WriteInt(user.Grade);
			gspacketIn.WriteInt(user.Hide);
			gspacketIn.WriteString(user.ConsortiaName);
			gspacketIn.WriteInt(user.Total);
			gspacketIn.WriteInt(user.Escape);
			gspacketIn.WriteInt(user.Win);
			gspacketIn.WriteInt(user.Offer);
			gspacketIn.WriteInt(user.Repute);
			gspacketIn.WriteInt(relation);
			gspacketIn.WriteString(user.UserName);
			gspacketIn.WriteInt(user.Nimbus);
			gspacketIn.WriteInt(user.FightPower);
			gspacketIn.WriteInt(user.ApprenticeshipState);
			gspacketIn.WriteInt(user.MasterID);
			gspacketIn.WriteString(user.MasterOrApprentices);
			gspacketIn.WriteInt(user.GraduatesCount);
			gspacketIn.WriteString(user.HonourOfMaster);
			gspacketIn.WriteInt(user.AchievementPoint);
			gspacketIn.WriteString(user.Honor);
			gspacketIn.WriteBoolean(user.IsMarried);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600862E RID: 34350 RVA: 0x002C05EC File Offset: 0x002BE7EC
		public GSPacketIn SendFriendRemove(int FriendID)
		{
			GSPacketIn gspacketIn = new GSPacketIn(160, FriendID);
			gspacketIn.WriteByte(161);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600862F RID: 34351 RVA: 0x002C0620 File Offset: 0x002BE820
		public GSPacketIn SendFriendState(int playerID, int state, byte typeVip, int viplevel)
		{
			GSPacketIn gspacketIn = new GSPacketIn(160, playerID);
			gspacketIn.WriteByte(165);
			gspacketIn.WriteInt(state);
			gspacketIn.WriteByte(typeVip);
			gspacketIn.WriteInt(viplevel);
			gspacketIn.WriteBoolean(true);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008630 RID: 34352 RVA: 0x002C0674 File Offset: 0x002BE874
		public GSPacketIn SendOneOnOneTalk(int receiverID, bool isAutoReply, string SenderNickName, string msg, int playerid)
		{
			GSPacketIn gspacketIn = new GSPacketIn(160, playerid);
			gspacketIn.WriteByte(51);
			gspacketIn.WriteInt(receiverID);
			gspacketIn.WriteString(SenderNickName);
			gspacketIn.WriteDateTime(DateTime.Now);
			gspacketIn.WriteString(msg);
			gspacketIn.WriteBoolean(isAutoReply);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008631 RID: 34353 RVA: 0x002C06D4 File Offset: 0x002BE8D4
		public GSPacketIn SendUpdateQuests(GamePlayer player, byte[] states, BaseQuest[] infos)
		{
			bool flag = player == null || states == null || infos == null;
			GSPacketIn gspacketIn;
			if (flag)
			{
				gspacketIn = null;
			}
			else
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(178, player.PlayerCharacter.ID);
				gspacketIn2.WriteInt(infos.Length);
				foreach (BaseQuest baseQuest in infos)
				{
					gspacketIn2.WriteInt(baseQuest.Data.QuestID);
					gspacketIn2.WriteBoolean(baseQuest.Data.IsComplete);
					gspacketIn2.WriteInt(baseQuest.Data.Condition1);
					gspacketIn2.WriteInt(baseQuest.Data.Condition2);
					gspacketIn2.WriteInt(baseQuest.Data.Condition3);
					gspacketIn2.WriteInt(baseQuest.Data.Condition4);
					gspacketIn2.WriteDateTime(baseQuest.Data.CompletedDate);
					gspacketIn2.WriteInt((baseQuest.Data.RepeatFinish == 0) ? (-1) : baseQuest.Data.RepeatFinish);
					gspacketIn2.WriteInt(baseQuest.Data.RandDobule);
					gspacketIn2.WriteBoolean(baseQuest.Data.ExistInCurrent);
				}
				gspacketIn2.Write(states);
				this.SendTCP(gspacketIn2);
				gspacketIn = gspacketIn2;
			}
			return gspacketIn;
		}

		// Token: 0x06008632 RID: 34354 RVA: 0x002C0824 File Offset: 0x002BEA24
		public GSPacketIn SendUpdateBuffer(GamePlayer player, AbstractBuffer[] infos)
		{
			GSPacketIn gspacketIn = new GSPacketIn(185, player.PlayerId);
			gspacketIn.WriteInt(infos.Length);
			foreach (AbstractBuffer abstractBuffer in infos)
			{
				gspacketIn.WriteInt(abstractBuffer.Info.Type);
				gspacketIn.WriteBoolean(abstractBuffer.Info.IsExist);
				gspacketIn.WriteDateTime(abstractBuffer.Info.BeginDate);
				bool flag = abstractBuffer.IsPayBuff();
				if (flag)
				{
					gspacketIn.WriteInt(abstractBuffer.Info.ValidDate / 60 / 24);
				}
				else
				{
					gspacketIn.WriteInt(abstractBuffer.Info.ValidDate);
				}
				gspacketIn.WriteInt(abstractBuffer.Info.Value);
				gspacketIn.WriteInt(abstractBuffer.Info.ValidCount);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008633 RID: 34355 RVA: 0x002C0910 File Offset: 0x002BEB10
		public GSPacketIn SendBufferList(GamePlayer player, List<AbstractBuffer> infos)
		{
			GSPacketIn gspacketIn = new GSPacketIn(186, player.PlayerId);
			gspacketIn.WriteInt(infos.Count);
			foreach (AbstractBuffer abstractBuffer in infos)
			{
				UserBufferInfo info = abstractBuffer.Info;
				gspacketIn.WriteInt(info.Type);
				gspacketIn.WriteBoolean(info.IsExist);
				gspacketIn.WriteDateTime(info.BeginDate);
				bool flag = abstractBuffer.IsPayBuff();
				if (flag)
				{
					gspacketIn.WriteInt(abstractBuffer.Info.ValidDate / 60 / 24);
				}
				else
				{
					gspacketIn.WriteInt(abstractBuffer.Info.ValidDate);
				}
				gspacketIn.WriteInt(info.Value);
				gspacketIn.WriteInt(info.ValidCount);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008634 RID: 34356 RVA: 0x002C0A14 File Offset: 0x002BEC14
		public GSPacketIn SendMailResponse(int playerID, eMailRespose type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(117);
			gspacketIn.WriteInt(playerID);
			gspacketIn.WriteInt((int)type);
			GameServer.Instance.LoginServer.SendPacket(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008635 RID: 34357 RVA: 0x002C0A50 File Offset: 0x002BEC50
		public GSPacketIn SendAuctionRefresh(AuctionInfo info, int auctionID, bool isExist, ItemInfo item)
		{
			GSPacketIn gspacketIn = new GSPacketIn(195);
			gspacketIn.WriteInt(auctionID);
			gspacketIn.WriteBoolean(isExist);
			if (isExist)
			{
				gspacketIn.WriteInt(info.AuctioneerID);
				gspacketIn.WriteString(info.AuctioneerName);
				gspacketIn.WriteDateTime(info.BeginDate);
				gspacketIn.WriteInt(info.BuyerID);
				gspacketIn.WriteString(info.BuyerName);
				gspacketIn.WriteInt(info.ItemID);
				gspacketIn.WriteInt(info.Mouthful);
				gspacketIn.WriteInt(info.PayType);
				gspacketIn.WriteInt(info.Price);
				gspacketIn.WriteInt(info.Rise);
				gspacketIn.WriteInt(info.ValidDate);
				gspacketIn.WriteBoolean(item != null);
				bool flag = item != null;
				if (flag)
				{
					gspacketIn.WriteInt(item.Count);
					gspacketIn.WriteInt(item.TemplateID);
					gspacketIn.WriteInt(item.AttackCompose);
					gspacketIn.WriteInt(item.DefendCompose);
					gspacketIn.WriteInt(item.AgilityCompose);
					gspacketIn.WriteInt(item.LuckCompose);
					gspacketIn.WriteInt(item.StrengthenLevel);
					gspacketIn.WriteBoolean(item.IsBinds);
					gspacketIn.WriteBoolean(item.IsJudge);
					gspacketIn.WriteDateTime(item.BeginDate);
					gspacketIn.WriteInt(item.ValidDate);
					gspacketIn.WriteString(item.Color);
					gspacketIn.WriteString(item.Skin);
					gspacketIn.WriteBoolean(item.IsUsed);
					gspacketIn.WriteInt(item.Hole1);
					gspacketIn.WriteInt(item.Hole2);
					gspacketIn.WriteInt(item.Hole3);
					gspacketIn.WriteInt(item.Hole4);
					gspacketIn.WriteInt(item.Hole5);
					gspacketIn.WriteInt(item.Hole6);
					gspacketIn.WriteString(item.Pic);
					gspacketIn.WriteInt(item.RefineryLevel);
					gspacketIn.WriteDateTime(DateTime.Now.AddDays(-100.0));
					gspacketIn.WriteByte((byte)item.Hole5Level);
					gspacketIn.WriteInt(item.Hole5Exp);
					gspacketIn.WriteByte((byte)item.Hole6Level);
					gspacketIn.WriteInt(item.Hole6Exp);
					gspacketIn.WriteInt(item.MagicLevel);
					gspacketIn.WriteInt(item.MagicExp);
				}
			}
			gspacketIn.Compress();
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008636 RID: 34358 RVA: 0x002C0CEC File Offset: 0x002BEEEC
		public GSPacketIn SendAASState(bool result)
		{
			GSPacketIn gspacketIn = new GSPacketIn(224);
			gspacketIn.WriteBoolean(result);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008637 RID: 34359 RVA: 0x002C0D1C File Offset: 0x002BEF1C
		public GSPacketIn SendIDNumberCheck(bool result)
		{
			GSPacketIn gspacketIn = new GSPacketIn(226);
			gspacketIn.WriteBoolean(result);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008638 RID: 34360 RVA: 0x002C0CEC File Offset: 0x002BEEEC
		public GSPacketIn SendAASInfoSet(bool result)
		{
			GSPacketIn gspacketIn = new GSPacketIn(224);
			gspacketIn.WriteBoolean(result);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008639 RID: 34361 RVA: 0x002C0D4C File Offset: 0x002BEF4C
		public GSPacketIn SendAASControl(bool result, bool IsAASInfo, bool IsMinor)
		{
			GSPacketIn gspacketIn = new GSPacketIn(227);
			gspacketIn.WriteBoolean(result);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteBoolean(IsAASInfo);
			gspacketIn.WriteBoolean(IsMinor);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600863A RID: 34362 RVA: 0x002C0D94 File Offset: 0x002BEF94
		public GSPacketIn SendLookupEffort(PlayerInfo player, List<AchievementDataInfo> datas)
		{
			GSPacketIn gspacketIn = new GSPacketIn(203);
			gspacketIn.WriteInt(player.AchievementPoint);
			gspacketIn.WriteInt(datas.Count);
			foreach (AchievementDataInfo achievementDataInfo in datas)
			{
				gspacketIn.WriteInt(achievementDataInfo.AchievementID);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600863B RID: 34363 RVA: 0x002C0E20 File Offset: 0x002BF020
		public GSPacketIn SendAchievementDataInfo(List<AchievementDataInfo> data)
		{
			GSPacketIn gspacketIn = new GSPacketIn(231);
			gspacketIn.WriteInt(data.Count);
			foreach (AchievementDataInfo achievementDataInfo in data)
			{
				gspacketIn.WriteInt(achievementDataInfo.AchievementID);
				gspacketIn.WriteInt(achievementDataInfo.CompletedDate.Year);
				gspacketIn.WriteInt(achievementDataInfo.CompletedDate.Month);
				gspacketIn.WriteInt(achievementDataInfo.CompletedDate.Day);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600863C RID: 34364 RVA: 0x002C0EE0 File Offset: 0x002BF0E0
		public GSPacketIn SendAchievementSuccess(AchievementDataInfo d)
		{
			GSPacketIn gspacketIn = new GSPacketIn(230);
			gspacketIn.WriteInt(d.AchievementID);
			gspacketIn.WriteInt(d.CompletedDate.Year);
			gspacketIn.WriteInt(d.CompletedDate.Month);
			gspacketIn.WriteInt(d.CompletedDate.Day);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600863D RID: 34365 RVA: 0x002C0F54 File Offset: 0x002BF154
		public GSPacketIn SendUpdateAchievementInfo(List<AchievementProcessInfo> process)
		{
			GSPacketIn gspacketIn = new GSPacketIn(229);
			gspacketIn.WriteInt(process.Count);
			foreach (AchievementProcessInfo achievementProcessInfo in process)
			{
				gspacketIn.WriteInt(achievementProcessInfo.CondictionType);
				gspacketIn.WriteInt(achievementProcessInfo.Value);
				achievementProcessInfo.IsDirty = false;
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600863E RID: 34366 RVA: 0x002C0FE8 File Offset: 0x002BF1E8
		public GSPacketIn SendMarryRoomInfo(GamePlayer player, MarryRoom room)
		{
			GSPacketIn gspacketIn = new GSPacketIn(241, player.PlayerCharacter.ID);
			bool flag = room != null;
			gspacketIn.WriteBoolean(flag);
			bool flag2 = flag;
			if (flag2)
			{
				gspacketIn.WriteInt(room.Info.ID);
				gspacketIn.WriteBoolean(room.Info.IsHymeneal);
				gspacketIn.WriteString(room.Info.Name);
				gspacketIn.WriteBoolean(!(room.Info.Pwd == ""));
				gspacketIn.WriteInt(room.Info.MapIndex);
				gspacketIn.WriteInt(room.Info.AvailTime);
				gspacketIn.WriteInt(room.Count);
				gspacketIn.WriteInt(room.Info.PlayerID);
				gspacketIn.WriteString(room.Info.PlayerName);
				gspacketIn.WriteInt(room.Info.GroomID);
				gspacketIn.WriteString(room.Info.GroomName);
				gspacketIn.WriteInt(room.Info.BrideID);
				gspacketIn.WriteString(room.Info.BrideName);
				gspacketIn.WriteDateTime(room.Info.BeginTime);
				gspacketIn.WriteByte((byte)room.RoomState);
				gspacketIn.WriteString(room.Info.RoomIntroduction);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600863F RID: 34367 RVA: 0x002C1154 File Offset: 0x002BF354
		public GSPacketIn SendMarryRoomLogin(GamePlayer player, bool result)
		{
			GSPacketIn gspacketIn = new GSPacketIn(242, player.PlayerCharacter.ID);
			gspacketIn.WriteBoolean(result);
			if (result)
			{
				gspacketIn.WriteInt(player.CurrentMarryRoom.Info.ID);
				gspacketIn.WriteString(player.CurrentMarryRoom.Info.Name);
				gspacketIn.WriteInt(player.CurrentMarryRoom.Info.MapIndex);
				gspacketIn.WriteInt(player.CurrentMarryRoom.Info.AvailTime);
				gspacketIn.WriteInt(player.CurrentMarryRoom.Count);
				gspacketIn.WriteInt(player.CurrentMarryRoom.Info.PlayerID);
				gspacketIn.WriteString(player.CurrentMarryRoom.Info.PlayerName);
				gspacketIn.WriteInt(player.CurrentMarryRoom.Info.GroomID);
				gspacketIn.WriteString(player.CurrentMarryRoom.Info.GroomName);
				gspacketIn.WriteInt(player.CurrentMarryRoom.Info.BrideID);
				gspacketIn.WriteString(player.CurrentMarryRoom.Info.BrideName);
				gspacketIn.WriteDateTime(player.CurrentMarryRoom.Info.BeginTime);
				gspacketIn.WriteBoolean(player.CurrentMarryRoom.Info.IsHymeneal);
				gspacketIn.WriteByte((byte)player.CurrentMarryRoom.RoomState);
				gspacketIn.WriteString(player.CurrentMarryRoom.Info.RoomIntroduction);
				gspacketIn.WriteBoolean(player.CurrentMarryRoom.Info.GuestInvite);
				gspacketIn.WriteInt(player.MarryMap);
				gspacketIn.WriteBoolean(player.CurrentMarryRoom.Info.IsGunsaluteUsed);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008640 RID: 34368 RVA: 0x002C1324 File Offset: 0x002BF524
		public GSPacketIn SendPlayerEnterMarryRoom(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(243, player.PlayerCharacter.ID);
			gspacketIn.WriteInt(player.PlayerCharacter.Grade);
			gspacketIn.WriteInt(player.PlayerCharacter.Hide);
			gspacketIn.WriteInt(player.PlayerCharacter.Repute);
			gspacketIn.WriteInt(player.PlayerCharacter.ID);
			gspacketIn.WriteString(player.PlayerCharacter.NickName);
			gspacketIn.WriteByte(player.PlayerCharacter.TypeVIP);
			gspacketIn.WriteInt(player.PlayerCharacter.VIPLevel);
			gspacketIn.WriteBoolean(player.PlayerCharacter.Sex);
			gspacketIn.WriteString(player.PlayerCharacter.Style);
			gspacketIn.WriteString(player.PlayerCharacter.Colors);
			gspacketIn.WriteString(player.PlayerCharacter.Skin);
			gspacketIn.WriteInt(player.X);
			gspacketIn.WriteInt(player.Y);
			gspacketIn.WriteInt(player.PlayerCharacter.FightPower);
			gspacketIn.WriteInt(player.PlayerCharacter.Win);
			gspacketIn.WriteInt(player.PlayerCharacter.Total);
			gspacketIn.WriteInt(player.PlayerCharacter.Offer);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008641 RID: 34369 RVA: 0x002C1480 File Offset: 0x002BF680
		public GSPacketIn SendMarryInfoRefresh(MarryInfo info, int ID, bool isExist)
		{
			GSPacketIn gspacketIn = new GSPacketIn(239);
			gspacketIn.WriteInt(ID);
			gspacketIn.WriteBoolean(isExist);
			if (isExist)
			{
				gspacketIn.WriteInt(info.UserID);
				gspacketIn.WriteBoolean(info.IsPublishEquip);
				gspacketIn.WriteString(info.Introduction);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008642 RID: 34370 RVA: 0x002C14E4 File Offset: 0x002BF6E4
		public GSPacketIn SendPlayerMarryStatus(GamePlayer player, int userID, bool isMarried)
		{
			GSPacketIn gspacketIn = new GSPacketIn(246, player.PlayerCharacter.ID);
			gspacketIn.WriteInt(userID);
			gspacketIn.WriteBoolean(isMarried);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008643 RID: 34371 RVA: 0x002C1528 File Offset: 0x002BF728
		public GSPacketIn SendPlayerMarryApply(GamePlayer player, int userID, string userName, string loveProclamation, int id)
		{
			GSPacketIn gspacketIn = new GSPacketIn(247, player.PlayerCharacter.ID);
			gspacketIn.WriteInt(userID);
			gspacketIn.WriteString(userName);
			gspacketIn.WriteString(loveProclamation);
			gspacketIn.WriteInt(id);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008644 RID: 34372 RVA: 0x002C157C File Offset: 0x002BF77C
		public GSPacketIn SendPlayerDivorceApply(GamePlayer player, bool result, bool isProposer)
		{
			GSPacketIn gspacketIn = new GSPacketIn(248, player.PlayerCharacter.ID);
			gspacketIn.WriteBoolean(result);
			gspacketIn.WriteBoolean(isProposer);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008645 RID: 34373 RVA: 0x002C15C0 File Offset: 0x002BF7C0
		public GSPacketIn SendMarryApplyReply(GamePlayer player, int UserID, string UserName, bool result, bool isApplicant, int id)
		{
			GSPacketIn gspacketIn = new GSPacketIn(250, player.PlayerCharacter.ID);
			gspacketIn.WriteInt(UserID);
			gspacketIn.WriteBoolean(result);
			gspacketIn.WriteString(UserName);
			gspacketIn.WriteBoolean(isApplicant);
			gspacketIn.WriteInt(id);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008646 RID: 34374 RVA: 0x002C161C File Offset: 0x002BF81C
		public GSPacketIn SendBigSpeakerMsg(GamePlayer player, string msg)
		{
			GSPacketIn gspacketIn = new GSPacketIn(72, player.PlayerCharacter.ID);
			gspacketIn.WriteInt(player.PlayerCharacter.ID);
			gspacketIn.WriteString(player.PlayerCharacter.NickName);
			gspacketIn.WriteString(msg);
			GameServer.Instance.LoginServer.SendPacket(gspacketIn);
			GamePlayer[] allPlayers = WorldMgr.GetAllPlayers();
			GamePlayer[] array = allPlayers;
			GamePlayer[] array2 = array;
			GamePlayer[] array3 = array2;
			foreach (GamePlayer gamePlayer in array3)
			{
				gamePlayer.Out.SendTCP(gspacketIn);
			}
			return gspacketIn;
		}

		// Token: 0x06008647 RID: 34375 RVA: 0x002C16C0 File Offset: 0x002BF8C0
		public GSPacketIn SendPlayerLeaveMarryRoom(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(244, player.PlayerCharacter.ID);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008648 RID: 34376 RVA: 0x002C16F4 File Offset: 0x002BF8F4
		public GSPacketIn SendMarryRoomInfoToPlayer(GamePlayer player, bool state, MarryRoomInfo info)
		{
			GSPacketIn gspacketIn = new GSPacketIn(252, player.PlayerCharacter.ID);
			gspacketIn.WriteBoolean(state);
			if (state)
			{
				gspacketIn.WriteInt(info.ID);
				gspacketIn.WriteString(info.Name);
				gspacketIn.WriteInt(info.MapIndex);
				gspacketIn.WriteInt(info.AvailTime);
				gspacketIn.WriteInt(info.PlayerID);
				gspacketIn.WriteInt(info.GroomID);
				gspacketIn.WriteInt(info.BrideID);
				gspacketIn.WriteDateTime(info.BeginTime);
				gspacketIn.WriteBoolean(info.IsGunsaluteUsed);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008649 RID: 34377 RVA: 0x002C17AC File Offset: 0x002BF9AC
		public GSPacketIn SendMarryInfo(GamePlayer player, MarryInfo info)
		{
			GSPacketIn gspacketIn = new GSPacketIn(235, player.PlayerCharacter.ID);
			gspacketIn.WriteString(info.Introduction);
			gspacketIn.WriteBoolean(info.IsPublishEquip);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600864A RID: 34378 RVA: 0x002C17F8 File Offset: 0x002BF9F8
		public GSPacketIn SendContinuation(GamePlayer player, MarryRoomInfo info)
		{
			GSPacketIn gspacketIn = new GSPacketIn(249, player.PlayerCharacter.ID);
			gspacketIn.WriteByte(3);
			gspacketIn.WriteInt(info.AvailTime);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600864B RID: 34379 RVA: 0x002C1840 File Offset: 0x002BFA40
		public GSPacketIn SendMarryProp(GamePlayer player, MarryProp info)
		{
			GSPacketIn gspacketIn = new GSPacketIn(234, player.PlayerCharacter.ID);
			gspacketIn.WriteBoolean(info.IsMarried);
			gspacketIn.WriteInt(info.SpouseID);
			gspacketIn.WriteString(info.SpouseName);
			gspacketIn.WriteBoolean(info.IsCreatedMarryRoom);
			gspacketIn.WriteInt(info.SelfMarryRoomID);
			gspacketIn.WriteBoolean(info.IsGotRing);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600864C RID: 34380 RVA: 0x002C18C0 File Offset: 0x002BFAC0
		public void SendAcademyGradute(GamePlayer app, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(141);
			gspacketIn.WriteByte(11);
			gspacketIn.WriteInt(type);
			gspacketIn.WriteInt(app.PlayerId);
			gspacketIn.WriteString(app.PlayerCharacter.NickName);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600864D RID: 34381 RVA: 0x002C1914 File Offset: 0x002BFB14
		public GSPacketIn SendAcademySystemNotice(string text, bool isAlert)
		{
			GSPacketIn gspacketIn = new GSPacketIn(141);
			gspacketIn.WriteByte(17);
			gspacketIn.WriteString(text);
			gspacketIn.WriteBoolean(isAlert);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600864E RID: 34382 RVA: 0x002C1954 File Offset: 0x002BFB54
		public GSPacketIn SendAcademyAppState(PlayerInfo player, int removeUserId)
		{
			GSPacketIn gspacketIn = new GSPacketIn(141);
			gspacketIn.WriteByte(10);
			gspacketIn.WriteInt(player.ApprenticeshipState);
			gspacketIn.WriteInt(player.MasterID);
			gspacketIn.WriteString(player.MasterOrApprentices);
			gspacketIn.WriteInt(removeUserId);
			gspacketIn.WriteInt(player.GraduatesCount);
			gspacketIn.WriteString(player.HonourOfMaster);
			gspacketIn.WriteDateTime(player.FreezesDate);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600864F RID: 34383 RVA: 0x002C19DC File Offset: 0x002BFBDC
		public GSPacketIn SendUserRanks(int Id, List<UserRankInfo> ranks)
		{
			GSPacketIn gspacketIn = new GSPacketIn(34, Id);
			gspacketIn.WriteInt(ranks.Count);
			foreach (UserRankInfo userRankInfo in ranks)
			{
				gspacketIn.WriteInt(userRankInfo.NewTitleID);
				gspacketIn.WriteString(userRankInfo.Name);
				gspacketIn.WriteDateTime(userRankInfo.BeginDate);
				bool flag = userRankInfo.Validate == 0;
				if (flag)
				{
					gspacketIn.WriteDateTime(DateTime.Now.AddYears(1));
				}
				else
				{
					gspacketIn.WriteDateTime(userRankInfo.EndDate);
				}
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008650 RID: 34384 RVA: 0x002C1AAC File Offset: 0x002BFCAC
		public GSPacketIn SendOpenTimeBox(int condtion, bool isSuccess)
		{
			GSPacketIn gspacketIn = new GSPacketIn(53);
			gspacketIn.WriteBoolean(isSuccess);
			gspacketIn.WriteInt(condtion);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008651 RID: 34385 RVA: 0x002C1AE0 File Offset: 0x002BFCE0
		public GSPacketIn SendBuyBadge(int consortiaID, int BadgeID, int ValidDate, bool result, string BadgeBuyTime, int playerid)
		{
			GSPacketIn gspacketIn = new GSPacketIn(129, playerid);
			gspacketIn.WriteByte(28);
			gspacketIn.WriteInt(consortiaID);
			gspacketIn.WriteInt(BadgeID);
			gspacketIn.WriteInt(ValidDate);
			gspacketIn.WriteDateTime(Convert.ToDateTime(BadgeBuyTime));
			gspacketIn.WriteBoolean(result);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008652 RID: 34386 RVA: 0x002C1B40 File Offset: 0x002BFD40
		public GSPacketIn SendSystemConsortiaChat(string content, bool sendToSelf)
		{
			GSPacketIn gspacketIn = new GSPacketIn(129);
			gspacketIn.WriteByte(20);
			gspacketIn.WriteByte(0);
			gspacketIn.WriteString("");
			gspacketIn.WriteString(content);
			if (sendToSelf)
			{
				this.SendTCP(gspacketIn);
			}
			return gspacketIn;
		}

		// Token: 0x06008653 RID: 34387 RVA: 0x002C1B94 File Offset: 0x002BFD94
		public GSPacketIn SendConsortiaTaskInfo(BaseConsortiaTask baseTask)
		{
			GSPacketIn gspacketIn = new GSPacketIn(129);
			gspacketIn.WriteByte(22);
			gspacketIn.WriteByte(3);
			bool flag = baseTask != null;
			if (flag)
			{
				gspacketIn.WriteInt(baseTask.ConditionList.Count);
				foreach (KeyValuePair<int, ConsortiaTaskInfo> keyValuePair in baseTask.ConditionList)
				{
					gspacketIn.WriteInt(keyValuePair.Key);
					gspacketIn.WriteInt(3);
					gspacketIn.WriteString(keyValuePair.Value.CondictionTitle);
					gspacketIn.WriteInt(baseTask.GetTotalValueByConditionPlace(keyValuePair.Key));
					gspacketIn.WriteInt(keyValuePair.Value.Para2);
					gspacketIn.WriteInt(baseTask.GetValueByConditionPlace(this.m_gameClient.Player.PlayerCharacter.ID, keyValuePair.Key));
				}
				gspacketIn.WriteInt(baseTask.Info.TotalExp);
				gspacketIn.WriteInt(baseTask.Info.TotalOffer);
				gspacketIn.WriteInt(baseTask.Info.TotalRiches);
				gspacketIn.WriteInt(baseTask.Info.BuffID);
				gspacketIn.WriteDateTime(baseTask.Info.StartTime);
				gspacketIn.WriteInt(baseTask.Info.VaildDate);
			}
			else
			{
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteDateTime(DateTime.Now);
				gspacketIn.WriteInt(0);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008654 RID: 34388 RVA: 0x002C1D5C File Offset: 0x002BFF5C
		public GSPacketIn SendUpdateConsotiaBuffer(GamePlayer player, Dictionary<int, UserBufferInfo> bufflist)
		{
			List<ConsortiaBufferTempInfo> allConsortiaBuff = ConsortiaLevelMgr.GetAllConsortiaBuff();
			GSPacketIn gspacketIn = new GSPacketIn(129, player.PlayerId);
			gspacketIn.WriteByte(26);
			gspacketIn.WriteInt(allConsortiaBuff.Count);
			foreach (ConsortiaBufferTempInfo consortiaBufferTempInfo in allConsortiaBuff)
			{
				bool flag = bufflist.ContainsKey(consortiaBufferTempInfo.ID);
				if (flag)
				{
					UserBufferInfo userBufferInfo = bufflist[consortiaBufferTempInfo.ID];
					gspacketIn.WriteInt(consortiaBufferTempInfo.ID);
					gspacketIn.WriteBoolean(true);
					gspacketIn.WriteDateTime(userBufferInfo.BeginDate);
					gspacketIn.WriteInt(userBufferInfo.ValidDate / 24 / 60);
				}
				else
				{
					gspacketIn.WriteInt(consortiaBufferTempInfo.ID);
					gspacketIn.WriteBoolean(false);
					gspacketIn.WriteDateTime(DateTime.Now);
					gspacketIn.WriteInt(0);
				}
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008655 RID: 34389 RVA: 0x002C1E70 File Offset: 0x002C0070
		public void SendUpdateAreaInfo()
		{
			AreaConfigInfo[] allAreaConfig = WorldMgr.GetAllAreaConfig();
			GSPacketIn gspacketIn = new GSPacketIn(210);
			gspacketIn.WriteInt(allAreaConfig.Length);
			AreaConfigInfo[] array = allAreaConfig;
			AreaConfigInfo[] array2 = array;
			AreaConfigInfo[] array3 = array2;
			foreach (AreaConfigInfo areaConfigInfo in array3)
			{
				gspacketIn.WriteInt(areaConfigInfo.AreaID);
				gspacketIn.WriteString(areaConfigInfo.AreaName);
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008656 RID: 34390 RVA: 0x002C1EE8 File Offset: 0x002C00E8
		public void SendWeaklessGuildProgress(PlayerInfo player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(15, player.ID);
			gspacketIn.WriteInt(player.weaklessGuildProgress.Length);
			for (int i = 0; i < player.weaklessGuildProgress.Length; i++)
			{
				gspacketIn.WriteByte(player.weaklessGuildProgress[i]);
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008657 RID: 34391 RVA: 0x002C1F44 File Offset: 0x002C0144
		public GSPacketIn SendGetUserGift(PlayerInfo p, UserGiftInfo[] g)
		{
			GSPacketIn gspacketIn = new GSPacketIn(218);
			gspacketIn.WriteInt(p.ID);
			gspacketIn.WriteInt(p.GiftGP);
			gspacketIn.WriteInt(g.Length);
			foreach (UserGiftInfo userGiftInfo in g)
			{
				gspacketIn.WriteInt(userGiftInfo.TemplateID);
				gspacketIn.WriteInt(userGiftInfo.Count);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008658 RID: 34392 RVA: 0x002C1FC4 File Offset: 0x002C01C4
		public void SendLittleGameActived()
		{
			GSPacketIn gspacketIn = new GSPacketIn(80);
			gspacketIn.WriteBoolean(true);
			this.m_gameClient.SendTCP(gspacketIn);
			bool isOpen = LittleGameWorldMgr.IsOpen;
			if (isOpen)
			{
				this.m_gameClient.Out.SendMessage(eMessageType.Normal, "精彩的波谷的大作战有开始了噢~赶紧点开温泉参加吧！");
			}
		}

		// Token: 0x06008659 RID: 34393 RVA: 0x002C2014 File Offset: 0x002C0214
		public void SendUpdateCardData(CardInventory bag, int[] updatedSlots)
		{
			bool flag = this.m_gameClient.Player == null;
			if (!flag)
			{
				GSPacketIn gspacketIn = new GSPacketIn(216, this.m_gameClient.Player.PlayerCharacter.ID);
				gspacketIn.WriteInt(this.m_gameClient.Player.PlayerCharacter.ID);
				gspacketIn.WriteInt(updatedSlots.Length);
				foreach (int num in updatedSlots)
				{
					gspacketIn.WriteInt(num);
					UserCardInfo itemAt = bag.GetItemAt(num);
					bool flag2 = itemAt != null && itemAt.TemplateID != 0;
					if (flag2)
					{
						gspacketIn.WriteBoolean(true);
						gspacketIn.WriteInt(itemAt.CardID);
						gspacketIn.WriteInt(itemAt.UserID);
						gspacketIn.WriteInt(itemAt.Count);
						gspacketIn.WriteInt(itemAt.Place);
						gspacketIn.WriteInt(itemAt.TemplateID);
						gspacketIn.WriteInt(itemAt.TotalAttack);
						gspacketIn.WriteInt(itemAt.TotalDefence);
						gspacketIn.WriteInt(itemAt.TotalAgility);
						gspacketIn.WriteInt(itemAt.TotalLuck);
						gspacketIn.WriteInt(itemAt.Damage);
						gspacketIn.WriteInt(itemAt.Guard);
						gspacketIn.WriteInt(itemAt.Level);
						gspacketIn.WriteInt(itemAt.CardGP);
						gspacketIn.WriteBoolean(itemAt.isFirstGet);
					}
					else
					{
						gspacketIn.WriteBoolean(false);
					}
				}
				this.SendTCP(gspacketIn);
			}
		}

		// Token: 0x0600865A RID: 34394 RVA: 0x002C21B0 File Offset: 0x002C03B0
		public void SendUpdateCardData(PlayerInfo player, List<UserCardInfo> userCard)
		{
			bool flag = this.m_gameClient.Player == null;
			if (!flag)
			{
				GSPacketIn gspacketIn = new GSPacketIn(216, player.ID);
				gspacketIn.WriteInt(player.ID);
				gspacketIn.WriteInt(userCard.Count);
				foreach (UserCardInfo userCardInfo in userCard)
				{
					gspacketIn.WriteInt(userCardInfo.Place);
					gspacketIn.WriteBoolean(true);
					gspacketIn.WriteInt(userCardInfo.CardID);
					gspacketIn.WriteInt(userCardInfo.UserID);
					gspacketIn.WriteInt(userCardInfo.Count);
					gspacketIn.WriteInt(userCardInfo.Place);
					gspacketIn.WriteInt(userCardInfo.TemplateID);
					gspacketIn.WriteInt(userCardInfo.TotalAttack);
					gspacketIn.WriteInt(userCardInfo.TotalDefence);
					gspacketIn.WriteInt(userCardInfo.TotalAgility);
					gspacketIn.WriteInt(userCardInfo.TotalLuck);
					gspacketIn.WriteInt(userCardInfo.Damage);
					gspacketIn.WriteInt(userCardInfo.Guard);
					gspacketIn.WriteInt(userCardInfo.Level);
					gspacketIn.WriteInt(userCardInfo.CardGP);
					gspacketIn.WriteBoolean(userCardInfo.isFirstGet);
				}
				this.SendTCP(gspacketIn);
			}
		}

		// Token: 0x0600865B RID: 34395 RVA: 0x002C2318 File Offset: 0x002C0518
		public void SendQQTips(QQTipsInfo QQTips)
		{
			bool flag = QQTips != null;
			if (flag)
			{
				GSPacketIn gspacketIn = new GSPacketIn(99);
				gspacketIn.WriteString(QQTips.title);
				gspacketIn.WriteString(QQTips.content);
				gspacketIn.WriteInt(QQTips.maxLevel);
				gspacketIn.WriteInt(QQTips.minLevel);
				gspacketIn.WriteInt(QQTips.outInType);
				bool flag2 = QQTips.outInType == 0;
				if (flag2)
				{
					gspacketIn.WriteInt(QQTips.moduleType);
					gspacketIn.WriteInt(QQTips.inItemID);
				}
				else
				{
					gspacketIn.WriteString(QQTips.url);
				}
				this.SendTCP(gspacketIn);
			}
		}

		// Token: 0x0600865C RID: 34396 RVA: 0x002C23C0 File Offset: 0x002C05C0
		public void SendLeftRouleteOpen(UserExtraInfo info)
		{
			GSPacketIn gspacketIn = new GSPacketIn(137);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteBoolean(true);
			gspacketIn.WriteInt((info.LeftRoutteRate <= 0f) ? info.LeftRoutteCount : 0);
			gspacketIn.WriteString(string.Format("{0:N1}", info.LeftRoutteRate));
			foreach (char c in GameProperties.LeftRouterRateData)
			{
				bool flag = c != '.' && c != '|';
				if (flag)
				{
					gspacketIn.WriteInt(int.Parse(c.ToString()));
				}
				else
				{
					gspacketIn.WriteInt(0);
				}
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600865D RID: 34397 RVA: 0x00005683 File Offset: 0x00003883
		public void SendLeftRouleteResult()
		{
		}

		// Token: 0x0600865E RID: 34398 RVA: 0x002C2490 File Offset: 0x002C0690
		public GSPacketIn SendUpdateUserPet(PetInventory bag, int[] slots)
		{
			bool flag = this.m_gameClient.Player == null;
			GSPacketIn gspacketIn;
			if (flag)
			{
				gspacketIn = null;
			}
			else
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(68, this.m_gameClient.Player.PlayerId);
				gspacketIn2.WriteByte(1);
				gspacketIn2.WriteInt(this.m_gameClient.Player.PlayerId);
				gspacketIn2.WriteInt(slots.Length);
				foreach (int num in slots)
				{
					gspacketIn2.WriteInt(num);
					UserPetInfo petAt = bag.GetPetAt(num);
					bool flag2 = petAt == null;
					if (flag2)
					{
						gspacketIn2.WriteBoolean(false);
					}
					else
					{
						gspacketIn2.WriteBoolean(true);
						gspacketIn2.WriteInt(petAt.ID);
						gspacketIn2.WriteInt(petAt.TemplateID);
						gspacketIn2.WriteString(petAt.Name);
						gspacketIn2.WriteInt(petAt.UserID);
						gspacketIn2.WriteInt(petAt.TotalAttack);
						gspacketIn2.WriteInt(petAt.TotalDefence);
						gspacketIn2.WriteInt(petAt.TotalLuck);
						gspacketIn2.WriteInt(petAt.TotalAgility);
						gspacketIn2.WriteInt(petAt.TotalBlood);
						gspacketIn2.WriteInt(petAt.Damage);
						gspacketIn2.WriteInt(petAt.Guard);
						gspacketIn2.WriteInt(petAt.AttackGrow);
						gspacketIn2.WriteInt(petAt.DefenceGrow);
						gspacketIn2.WriteInt(petAt.LuckGrow);
						gspacketIn2.WriteInt(petAt.AgilityGrow);
						gspacketIn2.WriteInt(petAt.BloodGrow);
						gspacketIn2.WriteInt(petAt.DamageGrow);
						gspacketIn2.WriteInt(petAt.GuardGrow);
						gspacketIn2.WriteInt(petAt.Level);
						gspacketIn2.WriteInt(petAt.GP);
						gspacketIn2.WriteInt(petAt.MaxGP);
						gspacketIn2.WriteInt(petAt.Hunger);
						gspacketIn2.WriteInt(petAt.PetHappyStar);
						gspacketIn2.WriteInt(petAt.MP);
						string[] array = petAt.Skill.Split(new char[] { '|' });
						gspacketIn2.WriteInt(array.Length);
						string[] array2 = array;
						string[] array3 = array2;
						string[] array4 = array3;
						foreach (string text in array4)
						{
							gspacketIn2.WriteInt(int.Parse(text.Split(new char[] { ',' })[0]));
							gspacketIn2.WriteInt(int.Parse(text.Split(new char[] { ',' })[1]));
						}
						string[] array6 = petAt.SkillEquip.Split(new char[] { '|' });
						gspacketIn2.WriteInt(array6.Length);
						string[] array7 = array6;
						string[] array8 = array7;
						string[] array9 = array8;
						foreach (string text2 in array9)
						{
							gspacketIn2.WriteInt(int.Parse(text2.Split(new char[] { ',' })[1]));
							gspacketIn2.WriteInt(int.Parse(text2.Split(new char[] { ',' })[0]));
						}
						gspacketIn2.WriteBoolean(petAt.IsEquip);
						gspacketIn2.WriteInt(petAt.PetEquips.Count);
						foreach (PetEquipInfo petEquipInfo in petAt.PetEquips)
						{
							gspacketIn2.WriteInt(petEquipInfo.eqType);
							gspacketIn2.WriteInt(petEquipInfo.eqTemplateID);
							gspacketIn2.WriteDateTime(petEquipInfo.startTime);
							gspacketIn2.WriteInt(petEquipInfo.ValidDate);
						}
					}
				}
				this.SendTCP(gspacketIn2);
				gspacketIn = gspacketIn2;
			}
			return gspacketIn;
		}

		// Token: 0x0600865F RID: 34399 RVA: 0x002C2870 File Offset: 0x002C0A70
		public GSPacketIn SendPetInfo(int id, int zoneId, UserPetInfo[] pets)
		{
			GSPacketIn gspacketIn = new GSPacketIn(68, id);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteInt(id);
			gspacketIn.WriteInt(pets.Length);
			foreach (UserPetInfo userPetInfo in pets)
			{
				gspacketIn.WriteInt(userPetInfo.Place);
				gspacketIn.WriteBoolean(true);
				gspacketIn.WriteInt(userPetInfo.ID);
				gspacketIn.WriteInt(userPetInfo.TemplateID);
				gspacketIn.WriteString(userPetInfo.Name);
				gspacketIn.WriteInt(userPetInfo.UserID);
				gspacketIn.WriteInt(userPetInfo.TotalAttack);
				gspacketIn.WriteInt(userPetInfo.TotalDefence);
				gspacketIn.WriteInt(userPetInfo.TotalLuck);
				gspacketIn.WriteInt(userPetInfo.TotalAgility);
				gspacketIn.WriteInt(userPetInfo.TotalBlood);
				gspacketIn.WriteInt(userPetInfo.Damage);
				gspacketIn.WriteInt(userPetInfo.Guard);
				gspacketIn.WriteInt(userPetInfo.AttackGrow);
				gspacketIn.WriteInt(userPetInfo.DefenceGrow);
				gspacketIn.WriteInt(userPetInfo.LuckGrow);
				gspacketIn.WriteInt(userPetInfo.AgilityGrow);
				gspacketIn.WriteInt(userPetInfo.BloodGrow);
				gspacketIn.WriteInt(userPetInfo.DamageGrow);
				gspacketIn.WriteInt(userPetInfo.GuardGrow);
				gspacketIn.WriteInt(userPetInfo.Level);
				gspacketIn.WriteInt(userPetInfo.GP);
				gspacketIn.WriteInt(userPetInfo.MaxGP);
				gspacketIn.WriteInt(userPetInfo.Hunger);
				gspacketIn.WriteInt(userPetInfo.PetHappyStar);
				gspacketIn.WriteInt(userPetInfo.MP);
				string[] array = userPetInfo.Skill.Split(new char[] { '|' });
				string[] array2 = userPetInfo.SkillEquip.Split(new char[] { '|' });
				gspacketIn.WriteInt(array.Length);
				string[] array3 = array;
				string[] array4 = array3;
				string[] array5 = array4;
				foreach (string text in array5)
				{
					gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[0]));
					gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[1]));
				}
				gspacketIn.WriteInt(array2.Length);
				string[] array7 = array2;
				string[] array8 = array7;
				string[] array9 = array8;
				foreach (string text2 in array9)
				{
					gspacketIn.WriteInt(int.Parse(text2.Split(new char[] { ',' })[1]));
					gspacketIn.WriteInt(int.Parse(text2.Split(new char[] { ',' })[0]));
				}
				gspacketIn.WriteBoolean(userPetInfo.IsEquip);
				gspacketIn.WriteInt(userPetInfo.PetEquips.Count);
				foreach (PetEquipInfo petEquipInfo in userPetInfo.PetEquips)
				{
					gspacketIn.WriteInt(petEquipInfo.eqType);
					gspacketIn.WriteInt(petEquipInfo.eqTemplateID);
					gspacketIn.WriteDateTime(petEquipInfo.startTime);
					gspacketIn.WriteInt(petEquipInfo.ValidDate);
				}
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008660 RID: 34400 RVA: 0x002C2BD8 File Offset: 0x002C0DD8
		public GSPacketIn SendRefreshPet(GamePlayer player, UserPetInfo[] pets, ItemInfo[] items, bool refreshBtn)
		{
			bool flag = pets.Length == 0;
			GSPacketIn gspacketIn2;
			if (flag)
			{
				int num = 10;
				int num2 = 10;
				int num3 = 100;
				GSPacketIn gspacketIn = new GSPacketIn(68, player.PlayerCharacter.ID);
				gspacketIn.WriteByte(5);
				gspacketIn.WriteBoolean(refreshBtn);
				gspacketIn.WriteInt(pets.Length);
				foreach (UserPetInfo userPetInfo in pets)
				{
					gspacketIn.WriteInt(userPetInfo.Place);
					gspacketIn.WriteInt(userPetInfo.TemplateID);
					gspacketIn.WriteString(userPetInfo.Name);
					gspacketIn.WriteInt(userPetInfo.Attack);
					gspacketIn.WriteInt(userPetInfo.Defence);
					gspacketIn.WriteInt(userPetInfo.Luck);
					gspacketIn.WriteInt(userPetInfo.Agility);
					gspacketIn.WriteInt(userPetInfo.Blood);
					gspacketIn.WriteInt(userPetInfo.Damage);
					gspacketIn.WriteInt(userPetInfo.Guard);
					gspacketIn.WriteInt(userPetInfo.AttackGrow);
					gspacketIn.WriteInt(userPetInfo.DefenceGrow);
					gspacketIn.WriteInt(userPetInfo.LuckGrow);
					gspacketIn.WriteInt(userPetInfo.AgilityGrow);
					gspacketIn.WriteInt(userPetInfo.BloodGrow);
					gspacketIn.WriteInt(userPetInfo.DamageGrow);
					gspacketIn.WriteInt(userPetInfo.GuardGrow);
					gspacketIn.WriteInt(userPetInfo.Level);
					gspacketIn.WriteInt(userPetInfo.GP);
					gspacketIn.WriteInt(userPetInfo.MaxGP);
					gspacketIn.WriteInt(userPetInfo.Hunger);
					gspacketIn.WriteInt(userPetInfo.MP);
					string[] array = userPetInfo.Skill.Split(new char[] { '|' });
					gspacketIn.WriteInt(array.Length);
					string[] array2 = array;
					string[] array3 = array2;
					string[] array4 = array3;
					foreach (string text in array4)
					{
						gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[0]));
						gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[1]));
					}
					gspacketIn.WriteInt(num);
					gspacketIn.WriteInt(num2);
					gspacketIn.WriteInt(num3);
				}
				this.SendTCP(gspacketIn);
				gspacketIn2 = gspacketIn;
			}
			else
			{
				gspacketIn2 = null;
			}
			return gspacketIn2;
		}

		// Token: 0x06008661 RID: 34401 RVA: 0x002C2E64 File Offset: 0x002C1064
		public GSPacketIn SendPlayerLeaveSpaRoom(GamePlayer player, string msg)
		{
			bool flag = player == null;
			GSPacketIn gspacketIn;
			if (flag)
			{
				gspacketIn = null;
			}
			else
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(169, player.PlayerCharacter.ID);
				gspacketIn2.WriteString(msg);
				this.SendTCP(gspacketIn2);
				gspacketIn = gspacketIn2;
			}
			return gspacketIn;
		}

		// Token: 0x06008662 RID: 34402 RVA: 0x002C2EAC File Offset: 0x002C10AC
		public GSPacketIn SendPlayerLeaveSpaRoomForTimeOut(GamePlayer player)
		{
			bool flag = player == null;
			GSPacketIn gspacketIn;
			if (flag)
			{
				gspacketIn = null;
			}
			else
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(196, player.PlayerCharacter.ID);
				this.SendTCP(gspacketIn2);
				gspacketIn = gspacketIn2;
			}
			return gspacketIn;
		}

		// Token: 0x06008663 RID: 34403 RVA: 0x002C2EEC File Offset: 0x002C10EC
		public GSPacketIn SendSpaRoomInfo(GamePlayer player, SpaRoom room)
		{
			bool flag = player == null || room == null;
			GSPacketIn gspacketIn;
			if (flag)
			{
				gspacketIn = null;
			}
			else
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(175, player.PlayerCharacter.ID);
				bool flag2 = room != null;
				gspacketIn2.WriteBoolean(flag2);
				bool flag3 = flag2;
				if (flag3)
				{
					gspacketIn2.WriteInt(room.Spa_Room_Info.RoomID);
					gspacketIn2.WriteString(room.Spa_Room_Info.RoomName);
					gspacketIn2.WriteString(room.Spa_Room_Info.Pwd);
					gspacketIn2.WriteInt(room.Spa_Room_Info.AvailTime);
					gspacketIn2.WriteInt(room.Count);
					gspacketIn2.WriteInt(room.Spa_Room_Info.PlayerID);
					gspacketIn2.WriteString(room.Spa_Room_Info.PlayerName);
					gspacketIn2.WriteDateTime(room.Spa_Room_Info.BeginTime);
					gspacketIn2.WriteString(room.Spa_Room_Info.RoomIntroduction);
					gspacketIn2.WriteInt(room.Spa_Room_Info.RoomType);
					gspacketIn2.WriteInt(room.Spa_Room_Info.MaxCount);
				}
				this.SendTCP(gspacketIn2);
				gspacketIn = gspacketIn2;
			}
			return gspacketIn;
		}

		// Token: 0x06008664 RID: 34404 RVA: 0x002C3010 File Offset: 0x002C1210
		public GSPacketIn SendSpaRoomLogin(GamePlayer player)
		{
			bool flag = player == null || player.CurrentSpaRoom == null;
			GSPacketIn gspacketIn;
			if (flag)
			{
				gspacketIn = null;
			}
			else
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(202, player.PlayerCharacter.ID);
				gspacketIn2.WriteInt(player.CurrentSpaRoom.Spa_Room_Info.RoomID);
				gspacketIn2.WriteInt(player.CurrentSpaRoom.Spa_Room_Info.RoomNumber);
				gspacketIn2.WriteString(player.CurrentSpaRoom.Spa_Room_Info.RoomName);
				gspacketIn2.WriteString(player.CurrentSpaRoom.Spa_Room_Info.Pwd);
				gspacketIn2.WriteInt(player.CurrentSpaRoom.RoomLeftMin);
				gspacketIn2.WriteInt(player.CurrentSpaRoom.Count);
				gspacketIn2.WriteInt(player.CurrentSpaRoom.Spa_Room_Info.PlayerID);
				gspacketIn2.WriteString(player.CurrentSpaRoom.Spa_Room_Info.PlayerName);
				gspacketIn2.WriteDateTime(player.CurrentSpaRoom.Spa_Room_Info.BeginTime);
				gspacketIn2.WriteString(player.CurrentSpaRoom.Spa_Room_Info.RoomIntroduction);
				gspacketIn2.WriteInt(player.CurrentSpaRoom.Spa_Room_Info.RoomType);
				gspacketIn2.WriteInt(player.CurrentSpaRoom.Spa_Room_Info.MaxCount);
				gspacketIn2.WriteDateTime(player.DayOrNightInSpa);
				bool flag2 = player.CurrentSpaRoom.Spa_Room_Info.RoomType == 1;
				if (flag2)
				{
					gspacketIn2.WriteInt(player.PlayerCharacter.SpaPubGoldRoomLimit);
				}
				else
				{
					bool flag3 = player.CurrentSpaRoom.Spa_Room_Info.RoomType == 2;
					if (flag3)
					{
						gspacketIn2.WriteInt(player.PlayerCharacter.SpaPubMoneyRoomLimit);
					}
					else
					{
						gspacketIn2.WriteInt(0);
					}
				}
				this.SendTCP(gspacketIn2);
				gspacketIn = gspacketIn2;
			}
			return gspacketIn;
		}

		// Token: 0x06008665 RID: 34405 RVA: 0x002C31DC File Offset: 0x002C13DC
		public GSPacketIn SendSpaRoomList(GamePlayer player, SpaRoom[] rooms)
		{
			bool flag = player == null || rooms == null;
			GSPacketIn gspacketIn;
			if (flag)
			{
				gspacketIn = null;
			}
			else
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(197, player.PlayerCharacter.ID);
				gspacketIn2.WriteInt(rooms.Length);
				foreach (SpaRoom spaRoom in rooms)
				{
					gspacketIn2.WriteInt(spaRoom.Spa_Room_Info.RoomNumber);
					gspacketIn2.WriteInt(spaRoom.Spa_Room_Info.RoomID);
					gspacketIn2.WriteString(spaRoom.Spa_Room_Info.RoomName);
					gspacketIn2.WriteString(spaRoom.Spa_Room_Info.Pwd);
					gspacketIn2.WriteInt(spaRoom.Spa_Room_Info.AvailTime);
					gspacketIn2.WriteInt(spaRoom.Count);
					gspacketIn2.WriteInt(spaRoom.Spa_Room_Info.PlayerID);
					gspacketIn2.WriteString(spaRoom.Spa_Room_Info.PlayerName);
					gspacketIn2.WriteDateTime(spaRoom.Spa_Room_Info.BeginTime);
					gspacketIn2.WriteString(spaRoom.Spa_Room_Info.RoomIntroduction);
					gspacketIn2.WriteInt(spaRoom.Spa_Room_Info.RoomType);
					gspacketIn2.WriteInt(spaRoom.Spa_Room_Info.MaxCount);
				}
				this.SendTCP(gspacketIn2);
				gspacketIn = gspacketIn2;
			}
			return gspacketIn;
		}

		// Token: 0x06008666 RID: 34406 RVA: 0x002C3330 File Offset: 0x002C1530
		public GSPacketIn SendSpaRoomAddGuest(GamePlayer player)
		{
			bool flag = this.m_gameClient == null || this.m_gameClient.Player == null;
			GSPacketIn gspacketIn;
			if (flag)
			{
				gspacketIn = null;
			}
			else
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(198, this.m_gameClient.Player.PlayerCharacter.ID);
				gspacketIn2.WriteInt(player.PlayerCharacter.ID);
				gspacketIn2.WriteInt(player.PlayerCharacter.Grade);
				gspacketIn2.WriteInt(player.PlayerCharacter.Hide);
				gspacketIn2.WriteInt(player.PlayerCharacter.Repute);
				gspacketIn2.WriteString(player.PlayerCharacter.NickName);
				gspacketIn2.WriteByte(player.PlayerCharacter.TypeVIP);
				gspacketIn2.WriteInt(player.PlayerCharacter.VIPLevel);
				gspacketIn2.WriteBoolean(player.PlayerCharacter.Sex);
				gspacketIn2.WriteString(player.PlayerCharacter.Style);
				gspacketIn2.WriteString(player.PlayerCharacter.Colors);
				gspacketIn2.WriteString(player.PlayerCharacter.Skin);
				gspacketIn2.WriteInt(player.LastPosX);
				gspacketIn2.WriteInt(player.LastPosY);
				gspacketIn2.WriteInt(player.FightPower);
				gspacketIn2.WriteInt(player.PlayerCharacter.Win);
				gspacketIn2.WriteInt(player.PlayerCharacter.Total);
				gspacketIn2.WriteInt(player.Spa_Player_Direction);
				this.SendTCP(gspacketIn2);
				gspacketIn = gspacketIn2;
			}
			return gspacketIn;
		}

		// Token: 0x06008667 RID: 34407 RVA: 0x002C34B0 File Offset: 0x002C16B0
		public GSPacketIn SendSpaRoomRemoveGuest(GamePlayer removePlayer)
		{
			bool flag = this.m_gameClient == null || this.m_gameClient.Player == null || removePlayer == null;
			GSPacketIn gspacketIn;
			if (flag)
			{
				gspacketIn = null;
			}
			else
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(199, this.m_gameClient.Player.PlayerCharacter.ID);
				gspacketIn2.WriteInt(removePlayer.PlayerCharacter.ID);
				this.SendTCP(gspacketIn2);
				gspacketIn = gspacketIn2;
			}
			return gspacketIn;
		}

		// Token: 0x06008668 RID: 34408 RVA: 0x002C3524 File Offset: 0x002C1724
		public GSPacketIn SendSpaRoomInfoPerMin(GamePlayer player, int leftTime)
		{
			bool flag = player == null;
			GSPacketIn gspacketIn;
			if (flag)
			{
				gspacketIn = null;
			}
			else
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(191, player.PlayerCharacter.ID);
				gspacketIn2.WriteByte(7);
				gspacketIn2.WriteInt(leftTime);
				this.SendTCP(gspacketIn2);
				gspacketIn = gspacketIn2;
			}
			return gspacketIn;
		}

		// Token: 0x06008669 RID: 34409 RVA: 0x002C3574 File Offset: 0x002C1774
		public GSPacketIn SendSpaRoomLoginRemind(SpaRoom room)
		{
			bool flag = this.m_gameClient == null || this.m_gameClient.Player == null || room == null;
			GSPacketIn gspacketIn;
			if (flag)
			{
				gspacketIn = null;
			}
			else
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(212, this.m_gameClient.Player.PlayerCharacter.ID);
				gspacketIn2.WriteInt(room.Spa_Room_Info.RoomID);
				this.SendTCP(gspacketIn2);
				gspacketIn = gspacketIn2;
			}
			return gspacketIn;
		}

		// Token: 0x0600866A RID: 34410 RVA: 0x002C35E8 File Offset: 0x002C17E8
		public GSPacketIn SendIsContinueNextDay(GamePlayer player)
		{
			bool flag = player == null;
			GSPacketIn gspacketIn;
			if (flag)
			{
				gspacketIn = null;
			}
			else
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(191, player.PlayerCharacter.ID);
				gspacketIn2.WriteByte(10);
				this.SendTCP(gspacketIn2);
				gspacketIn = gspacketIn2;
			}
			return gspacketIn;
		}

		// Token: 0x0600866B RID: 34411 RVA: 0x002C3630 File Offset: 0x002C1830
		public GSPacketIn SendPlayerRefreshTotem(PlayerInfo player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(136, player.ID);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteInt(player.MyHornor);
			gspacketIn.WriteInt(player.TotemId);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600866C RID: 34412 RVA: 0x002C3680 File Offset: 0x002C1880
		public GSPacketIn SendUpdateUpCount(PlayerInfo player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(96, player.ID);
			gspacketIn.WriteInt(player.MaxBuyHonor);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600866D RID: 34413 RVA: 0x002C36B8 File Offset: 0x002C18B8
		public void SendTotemUpgradeUpdate(int templateId, int level)
		{
			GSPacketIn gspacketIn = new GSPacketIn(617);
			gspacketIn.WriteInt(templateId);
			gspacketIn.WriteInt(level);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600866E RID: 34414 RVA: 0x002C36EC File Offset: 0x002C18EC
		public void SendTotemUpgradeInfo(Dictionary<int, int> infos)
		{
			GSPacketIn gspacketIn = new GSPacketIn(618);
			gspacketIn.WriteInt(infos.Count);
			foreach (KeyValuePair<int, int> keyValuePair in infos)
			{
				gspacketIn.WriteInt(keyValuePair.Key);
				gspacketIn.WriteInt(keyValuePair.Value);
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600866F RID: 34415 RVA: 0x002C3774 File Offset: 0x002C1974
		public void SendCaddyGetAwards(PlayerInfo player, List<ItemInfo> items)
		{
			GSPacketIn gspacketIn = new GSPacketIn(245, player.ID);
			gspacketIn.WriteBoolean(true);
			gspacketIn.WriteInt(items.Count);
			foreach (ItemInfo itemInfo in items)
			{
				gspacketIn.WriteString(player.NickName);
				gspacketIn.WriteInt(itemInfo.TemplateID);
				gspacketIn.WriteInt(player.AreaID);
				gspacketIn.WriteBoolean(false);
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008670 RID: 34416 RVA: 0x002C3820 File Offset: 0x002C1A20
		public void SendTreasureHunting(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(151);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteBoolean(GameProperties.ShowCloseTreasure);
			gspacketIn.WriteBoolean(GameProperties.OpenCloseTreasure);
			gspacketIn.WriteDateTime(DateTime.Parse(GameProperties.TreasureBeginTime));
			gspacketIn.WriteDateTime(DateTime.Parse(GameProperties.TreasureEndTime));
			gspacketIn.WriteInt(GameProperties.TreasureNeedMoney);
			gspacketIn.WriteInt(player.PlayerCharacter.TreasureNormalTicket);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008671 RID: 34417 RVA: 0x002C38A4 File Offset: 0x002C1AA4
		public void SendLeagueNotice(int id)
		{
			GSPacketIn gspacketIn = new GSPacketIn(42, id);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008672 RID: 34418 RVA: 0x002C38C4 File Offset: 0x002C1AC4
		public void SendEliteGameStartRoom()
		{
			GSPacketIn gspacketIn = new GSPacketIn(162);
			gspacketIn.WriteByte(2);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008673 RID: 34419 RVA: 0x002C38F0 File Offset: 0x002C1AF0
		public void SendQQHall(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(164);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteBoolean(player.PlayerCharacter.BlueDrillType > 0);
			gspacketIn.WriteInt(player.PlayerCharacter.BlueDrillLevel);
			gspacketIn.WriteBoolean(false);
			gspacketIn.WriteInt(0);
			gspacketIn.WriteInt(player.PlayerCharacter.isGetQQHallRegisterGift);
			gspacketIn.WriteInt(player.PlayerCharacter.isGetQQHallActiveGift);
			gspacketIn.WriteString(player.PlayerCharacter.qqHallUpgradeGift);
			gspacketIn.WriteInt(player.PlayerCharacter.isGetBlueDrillRegisterGift);
			gspacketIn.WriteInt(player.PlayerCharacter.isGetBlueDrillDayGift);
			gspacketIn.WriteString(player.PlayerCharacter.blueDrillUpgradeGift);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteString("");
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008674 RID: 34420 RVA: 0x002C39D8 File Offset: 0x002C1BD8
		public void SendOpenWorldBoss(int pX, int pY)
		{
			BaseWorldBossRoom worldBossRoom = RoomMgr.WorldBossRoom;
			GSPacketIn gspacketIn = new GSPacketIn(102);
			gspacketIn.WriteByte(0);
			gspacketIn.WriteString(worldBossRoom.BossResourceID);
			gspacketIn.WriteInt(worldBossRoom.CurrentPVE);
			gspacketIn.WriteString("世界BOSS");
			gspacketIn.WriteString(worldBossRoom.Name);
			gspacketIn.WriteLong(worldBossRoom.Blood);
			gspacketIn.WriteInt(0);
			gspacketIn.WriteInt(0);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteInt((pX == 0) ? worldBossRoom.playerDefaultPosX : pX);
			gspacketIn.WriteInt((pY == 0) ? worldBossRoom.playerDefaultPosY : pY);
			gspacketIn.WriteDateTime(worldBossRoom.BeginTime);
			gspacketIn.WriteDateTime(worldBossRoom.EndTime);
			gspacketIn.WriteInt(worldBossRoom.FIghtTime);
			gspacketIn.WriteBoolean(worldBossRoom.FightOver);
			gspacketIn.WriteBoolean(worldBossRoom.RoomClose);
			gspacketIn.WriteInt(worldBossRoom.TicketID);
			gspacketIn.WriteInt(worldBossRoom.TicketCount);
			gspacketIn.WriteInt(worldBossRoom.TimeCD);
			gspacketIn.WriteInt(worldBossRoom.ReviveMoney);
			gspacketIn.WriteInt(worldBossRoom.ReFightMoney);
			gspacketIn.WriteInt(worldBossRoom.AddInjureBuffMoney);
			gspacketIn.WriteInt(worldBossRoom.AddInjureValue);
			gspacketIn.WriteInt(0);
			gspacketIn.WriteBoolean(true);
			gspacketIn.WriteBoolean(false);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008675 RID: 34421 RVA: 0x00035C2A File Offset: 0x00033E2A
		public void WonderfulSingleActivityInit(IGMActive gmActivity, GamePlayer player)
		{
			this.WonderfulActivityInit(new List<IGMActive> { gmActivity }, player, 2);
		}

		// Token: 0x06008676 RID: 34422 RVA: 0x002C3B38 File Offset: 0x002C1D38
		public void WonderfulActivityInit(List<IGMActive> allActions, GamePlayer player, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(405);
			gspacketIn.WriteInt(type);
			gspacketIn.WriteInt(allActions.Count);
			foreach (IGMActive igmactive in allActions)
			{
				gspacketIn.WriteString(igmactive.Info.activityId);
				igmactive.SetStatusPacket(player, gspacketIn);
				Dictionary<string, GmGiftInfo> giftsGroup = igmactive.Info.GiftsGroup;
				UserGmActivityInfo player2 = igmactive.GetPlayer(player);
				gspacketIn.WriteInt(giftsGroup.Count);
				using (Dictionary<string, GmGiftInfo>.ValueCollection.Enumerator enumerator2 = giftsGroup.Values.GetEnumerator())
				{
					while (enumerator2.MoveNext())
					{
						GmGiftInfo gmGiftInfo = enumerator2.Current;
						gspacketIn.WriteString(gmGiftInfo.giftbagId);
						GmGiftCurInfo gmGiftCurInfo = player2.GiftsReceivedList.SingleOrDefault((GmGiftCurInfo a) => a.giftID == gmGiftInfo.giftbagId);
						bool flag = gmGiftCurInfo != null;
						if (flag)
						{
							gspacketIn.WriteInt(gmGiftCurInfo.times);
							gspacketIn.WriteInt(gmGiftCurInfo.allGiftGetTimes);
						}
						else
						{
							gspacketIn.WriteInt(0);
							gspacketIn.WriteInt(0);
						}
					}
				}
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008677 RID: 34423 RVA: 0x002C3CC8 File Offset: 0x002C1EC8
		public GSPacketIn SendEmblemInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(655, player.PlayerCharacter.ID);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteInt(player.Emblem.EmblemBag.Count);
			foreach (UserEmblemInfo userEmblemInfo in player.Emblem.EmblemBag)
			{
				gspacketIn.WriteInt(userEmblemInfo.ID);
				gspacketIn.WriteInt(userEmblemInfo.TemplateID);
				gspacketIn.WriteBoolean(userEmblemInfo.IsExist);
				gspacketIn.WriteInt(userEmblemInfo.EquipPos);
				gspacketIn.WriteInt(userEmblemInfo.EquipIndex);
				gspacketIn.WriteInt(userEmblemInfo.MainType);
				gspacketIn.WriteInt(userEmblemInfo.MainValue);
				bool flag = !string.IsNullOrEmpty(userEmblemInfo.StrValue);
				if (flag)
				{
					string[] array = userEmblemInfo.StrValue.Split(new char[] { '|' });
					gspacketIn.WriteInt(array.Length);
					for (int i = 0; i < array.Length; i++)
					{
						gspacketIn.WriteInt(int.Parse(array[i].Split(new char[] { ',' })[0]));
						gspacketIn.WriteInt(int.Parse(array[i].Split(new char[] { ',' })[1]));
					}
				}
				else
				{
					gspacketIn.WriteInt(0);
				}
				gspacketIn.WriteBoolean(userEmblemInfo.IsLock);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008678 RID: 34424 RVA: 0x002C3E84 File Offset: 0x002C2084
		public GSPacketIn SendUpdateEmblemInfo(GamePlayer player, UserEmblemInfo info)
		{
			GSPacketIn gspacketIn = new GSPacketIn(655, player.PlayerCharacter.ID);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteInt(info.ID);
			gspacketIn.WriteInt(info.TemplateID);
			gspacketIn.WriteBoolean(info.IsExist);
			gspacketIn.WriteInt(info.EquipPos);
			gspacketIn.WriteInt(info.EquipIndex);
			gspacketIn.WriteInt(info.MainType);
			gspacketIn.WriteInt(info.MainValue);
			bool flag = !string.IsNullOrEmpty(info.StrValue);
			if (flag)
			{
				string[] array = info.StrValue.Split(new char[] { '|' });
				gspacketIn.WriteInt(array.Length);
				for (int i = 0; i < array.Length; i++)
				{
					gspacketIn.WriteInt(int.Parse(array[i].Split(new char[] { ',' })[0]));
					gspacketIn.WriteInt(int.Parse(array[i].Split(new char[] { ',' })[1]));
				}
			}
			else
			{
				gspacketIn.WriteInt(0);
			}
			gspacketIn.WriteBoolean(info.IsLock);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008679 RID: 34425 RVA: 0x002C3FC8 File Offset: 0x002C21C8
		public GSPacketIn SendScrollInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(655, player.PlayerCharacter.ID);
			gspacketIn.WriteByte(3);
			gspacketIn.WriteInt(player.Emblem.ScrollBag.Count);
			foreach (UserScrollInfo userScrollInfo in player.Emblem.ScrollBag)
			{
				gspacketIn.WriteInt(userScrollInfo.TemplateID);
				gspacketIn.WriteInt(userScrollInfo.IsBind ? 1 : 0);
				gspacketIn.WriteInt(userScrollInfo.Count);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600867A RID: 34426 RVA: 0x002C4090 File Offset: 0x002C2290
		public void SendBoguAdventureInfo(GamePlayer player, int state)
		{
			GSPacketIn gspacketIn = new GSPacketIn(140);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteInt(state);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600867B RID: 34427 RVA: 0x002C40C4 File Offset: 0x002C22C4
		public GSPacketIn SendUpdateCardPosInfo(GamePlayer player, PairBoxCeilInfo info)
		{
			GSPacketIn gspacketIn = new GSPacketIn(725, player.PlayerCharacter.ID);
			gspacketIn.WriteByte(5);
			gspacketIn.WriteInt(info.Pos);
			gspacketIn.WriteInt(info.ID);
			gspacketIn.WriteBoolean(info.IsGet);
			gspacketIn.WriteInt(info.OtherPos);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600867C RID: 34428 RVA: 0x002C4134 File Offset: 0x002C2334
		public GSPacketIn SendAvatarColectionAllInfo(int PlayerId, Dictionary<int, UserAvatarInfo> infos)
		{
			Dictionary<int, List<int>> dictionary = new Dictionary<int, List<int>>();
			List<int> list = new List<int>();
			List<int> list2 = new List<int>();
			foreach (int num in infos.Keys)
			{
				bool flag = !string.IsNullOrEmpty(infos[num].ActiveDress);
				if (flag)
				{
					bool flag2 = infos[num].Sex == 3;
					if (flag2)
					{
						list2.Add(num);
					}
					else
					{
						list.Add(num);
					}
				}
			}
			GSPacketIn gspacketIn = new GSPacketIn(142, PlayerId);
			gspacketIn.WriteByte(5);
			dictionary.Add(1, list);
			dictionary.Add(2, list2);
			gspacketIn.WriteInt(dictionary.Count);
			foreach (int num2 in dictionary.Keys)
			{
				gspacketIn.WriteInt(num2);
				gspacketIn.WriteInt(dictionary[num2].Count);
				foreach (int num3 in dictionary[num2])
				{
					gspacketIn.WriteInt(num3);
					gspacketIn.WriteInt(infos[num3].Sex);
					gspacketIn.WriteInt(infos[num3].CurrentGroup.Length);
					string[] currentGroup = infos[num3].CurrentGroup;
					string[] array = currentGroup;
					string[] array2 = array;
					foreach (string text in array2)
					{
						gspacketIn.WriteInt(int.Parse(text));
					}
					gspacketIn.WriteDateTime(infos[num3].endTime);
				}
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600867D RID: 34429 RVA: 0x002C4384 File Offset: 0x002C2584
		public void SendOpenMoonLight(GamePlayer player, int state)
		{
			GSPacketIn gspacketIn = new GSPacketIn(743);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteInt(state);
			gspacketIn.WriteDateTime(DateTime.Parse(GameProperties.MoonLightBoxOpenTime));
			gspacketIn.WriteDateTime(DateTime.Parse(GameProperties.MoonLightBoxEndTime));
			gspacketIn.WriteDateTime(DateTime.Parse(GameProperties.MoonLightBoxEndExchangeTime));
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600867E RID: 34430 RVA: 0x002C43E8 File Offset: 0x002C25E8
		public void SendMoonLightBagInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(743);
			gspacketIn.WriteByte(8);
			gspacketIn.WriteInt(player.Actives.Info.ChipCount);
			gspacketIn.WriteInt(player.Actives.MoonBag.Count);
			foreach (UserMoonLightBoxInfo userMoonLightBoxInfo in player.Actives.MoonBag)
			{
				gspacketIn.WriteInt(userMoonLightBoxInfo.TemplateID);
				gspacketIn.WriteInt(userMoonLightBoxInfo.Count);
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600867F RID: 34431 RVA: 0x002C44A4 File Offset: 0x002C26A4
		public void SendMoonLightPlayerInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(743);
			gspacketIn.WriteByte(2);
			gspacketIn.WriteInt(player.Actives.Info.KeyCount);
			gspacketIn.WriteInt(player.Actives.Info.ChipCount);
			gspacketIn.WriteInt(player.Actives.Info.LimitCount);
			gspacketIn.WriteInt(player.Actives.Info.OpenCount);
			gspacketIn.WriteInt(player.Actives.Info.RewardState.Split(new char[] { ',' }).Length);
			for (int i = 0; i < player.Actives.Info.RewardState.Split(new char[] { ',' }).Length; i++)
			{
				gspacketIn.WriteInt(Convert.ToInt32(player.Actives.Info.RewardState.Split(new char[] { ',' })[i]));
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008680 RID: 34432 RVA: 0x002C45B4 File Offset: 0x002C27B4
		public void SendOpenNoviceActive(int channel, int activeId, int condition, int awardGot, DateTime startTime, DateTime endTime)
		{
			GSPacketIn gspacketIn = new GSPacketIn(258);
			gspacketIn.WriteInt(channel);
			if (channel != 0)
			{
				if (channel == 1)
				{
					gspacketIn.WriteBoolean(false);
				}
			}
			else
			{
				gspacketIn.WriteInt(activeId);
				gspacketIn.WriteInt(condition);
				gspacketIn.WriteInt(awardGot);
				gspacketIn.WriteDateTime(startTime);
				gspacketIn.WriteDateTime(endTime);
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008681 RID: 34433 RVA: 0x002C4624 File Offset: 0x002C2824
		public void SendUpdateFirstRecharge(bool isRecharge, bool isGetAward)
		{
			GSPacketIn gspacketIn = new GSPacketIn(259);
			gspacketIn.WriteBoolean(isRecharge);
			gspacketIn.WriteBoolean(isGetAward);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008682 RID: 34434 RVA: 0x002C4658 File Offset: 0x002C2858
		public void SendChargeActivity(GamePlayer player, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(139);
			switch (type)
			{
			case 1:
			{
				gspacketIn.WriteByte(1);
				bool flag = DateTime.Now >= DateTime.Parse(GameProperties.ChargeActivityBeginTime) && DateTime.Now < DateTime.Parse(GameProperties.ChargeActivityEndTime);
				if (flag)
				{
					gspacketIn.WriteInt(1);
				}
				else
				{
					gspacketIn.WriteInt(0);
				}
				gspacketIn.WriteDateTime(DateTime.Parse(GameProperties.ChargeActivityBeginTime));
				gspacketIn.WriteDateTime(DateTime.Parse(GameProperties.ChargeActivityEndTime));
				break;
			}
			case 2:
				gspacketIn.WriteByte(2);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt(player.Actives.Info.DailyChargeNum);
				gspacketIn.WriteString(player.Actives.Info.DailyChargeAward);
				gspacketIn.WriteInt(player.Actives.Info.TotalChargeNum);
				gspacketIn.WriteString(player.Actives.Info.TotalChargeAward);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteString("");
				gspacketIn.WriteInt(player.Actives.Info.TotalXFMoney);
				gspacketIn.WriteString(player.Actives.Info.TotalXFAward);
				break;
			case 5:
				gspacketIn.WriteByte(5);
				gspacketIn.WriteString(player.Actives.Info.ChargeChangeAward);
				break;
			case 7:
				gspacketIn.WriteByte(7);
				gspacketIn.WriteInt(1);
				break;
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008683 RID: 34435 RVA: 0x002C4808 File Offset: 0x002C2A08
		public void SendSignInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(632);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteInt(player.Actives.Info.SignInCurrentID);
			gspacketIn.WriteDateTime(player.Actives.Info.SignInLastDate.Date);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008684 RID: 34436 RVA: 0x002C4868 File Offset: 0x002C2A68
		public GSPacketIn SendEnterFarm(PlayerInfo Player, UserFarmInfo farm, UserFieldInfo[] fields)
		{
			GSPacketIn gspacketIn = new GSPacketIn(81, Player.ID);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteInt(farm.FarmID);
			gspacketIn.WriteBoolean(farm.isFarmHelper);
			gspacketIn.WriteInt(farm.isAutoId);
			gspacketIn.WriteDateTime(farm.AutoPayTime);
			gspacketIn.WriteInt(farm.AutoValidDate);
			gspacketIn.WriteInt(farm.GainFieldId);
			gspacketIn.WriteInt(farm.KillCropId);
			gspacketIn.WriteInt(fields.Length);
			foreach (UserFieldInfo userFieldInfo in fields)
			{
				gspacketIn.WriteInt(userFieldInfo.FieldID);
				gspacketIn.WriteInt(userFieldInfo.SeedID);
				gspacketIn.WriteDateTime(userFieldInfo.PayTime);
				gspacketIn.WriteDateTime(userFieldInfo.PlantTime);
				gspacketIn.WriteInt(userFieldInfo.GainCount);
				gspacketIn.WriteInt(userFieldInfo.FieldValidDate);
				gspacketIn.WriteInt(userFieldInfo.AccelerateTime);
			}
			bool flag = farm.FarmID == Player.ID;
			if (flag)
			{
				gspacketIn.WriteInt(3000);
				gspacketIn.WriteString(farm.PayFieldMoney);
				gspacketIn.WriteString(farm.PayAutoMoney);
				gspacketIn.WriteDateTime(farm.AutoPayTime);
				gspacketIn.WriteInt(farm.AutoValidDate);
				gspacketIn.WriteInt(Player.VIPLevel);
				gspacketIn.WriteInt(farm.buyExpRemainNum);
			}
			else
			{
				gspacketIn.WriteBoolean(farm.isArrange);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008685 RID: 34437 RVA: 0x002C49F4 File Offset: 0x002C2BF4
		public GSPacketIn SendSeeding(PlayerInfo Player, UserFieldInfo field)
		{
			GSPacketIn gspacketIn = new GSPacketIn(81, Player.ID);
			gspacketIn.WriteByte(2);
			gspacketIn.WriteInt(field.FieldID);
			gspacketIn.WriteInt(field.SeedID);
			gspacketIn.WriteDateTime(field.PlantTime);
			gspacketIn.WriteDateTime(field.PayTime);
			gspacketIn.WriteInt(field.GainCount);
			gspacketIn.WriteInt(field.FieldValidDate);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008686 RID: 34438 RVA: 0x002C4A74 File Offset: 0x002C2C74
		public GSPacketIn SenddoMature(FarmInventory farm)
		{
			GSPacketIn gspacketIn = new GSPacketIn(81, farm.Player.PlayerCharacter.ID);
			gspacketIn.WriteByte(3);
			gspacketIn.WriteInt(farm.CurrentFields.Length);
			UserFieldInfo[] currentFields = farm.CurrentFields;
			UserFieldInfo[] array = currentFields;
			UserFieldInfo[] array2 = array;
			foreach (UserFieldInfo userFieldInfo in array2)
			{
				bool flag = userFieldInfo != null;
				if (flag)
				{
					gspacketIn.WriteBoolean(true);
					gspacketIn.WriteInt(userFieldInfo.FieldID);
					gspacketIn.WriteInt(userFieldInfo.GainCount);
					gspacketIn.WriteInt(userFieldInfo.AccelerateTime);
				}
				else
				{
					gspacketIn.WriteBoolean(false);
				}
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008687 RID: 34439 RVA: 0x002C4B38 File Offset: 0x002C2D38
		public GSPacketIn SendtoGather(PlayerInfo Player, UserFieldInfo field)
		{
			GSPacketIn gspacketIn = new GSPacketIn(81, Player.ID);
			gspacketIn.WriteByte(4);
			gspacketIn.WriteBoolean(true);
			gspacketIn.WriteInt(field.FieldID);
			gspacketIn.WriteInt(field.SeedID);
			gspacketIn.WriteDateTime(field.PlantTime);
			gspacketIn.WriteInt(field.GainCount);
			gspacketIn.WriteInt(field.AccelerateTime);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008688 RID: 34440 RVA: 0x002C4BB4 File Offset: 0x002C2DB4
		public GSPacketIn SendPayFields(GamePlayer Player, List<int> fieldIds)
		{
			GSPacketIn gspacketIn = new GSPacketIn(81, Player.PlayerCharacter.ID);
			gspacketIn.WriteByte(6);
			gspacketIn.WriteInt(Player.PlayerCharacter.ID);
			gspacketIn.WriteInt(fieldIds.Count);
			foreach (int num in fieldIds)
			{
				UserFieldInfo fieldAt = Player.Farm.GetFieldAt(num);
				gspacketIn.WriteInt(fieldAt.FieldID);
				gspacketIn.WriteInt(fieldAt.SeedID);
				gspacketIn.WriteDateTime(fieldAt.PayTime);
				gspacketIn.WriteDateTime(fieldAt.PlantTime);
				gspacketIn.WriteInt(fieldAt.GainCount);
				gspacketIn.WriteInt(fieldAt.FieldValidDate);
				gspacketIn.WriteInt(fieldAt.AccelerateTime);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008689 RID: 34441 RVA: 0x002C4CB4 File Offset: 0x002C2EB4
		public GSPacketIn SendKillCropField(PlayerInfo Player, UserFieldInfo field)
		{
			GSPacketIn gspacketIn = new GSPacketIn(81, Player.ID);
			gspacketIn.WriteByte(7);
			gspacketIn.WriteBoolean(true);
			gspacketIn.WriteInt(field.FieldID);
			gspacketIn.WriteInt(field.SeedID);
			gspacketIn.WriteInt(field.AccelerateTime);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600868A RID: 34442 RVA: 0x002C4D14 File Offset: 0x002C2F14
		public GSPacketIn SendHelperSwitchField(PlayerInfo Player, UserFarmInfo farm)
		{
			GSPacketIn gspacketIn = new GSPacketIn(81, Player.ID);
			gspacketIn.WriteByte(9);
			gspacketIn.WriteBoolean(farm.isFarmHelper);
			gspacketIn.WriteInt(farm.isAutoId);
			gspacketIn.WriteDateTime(farm.AutoPayTime);
			gspacketIn.WriteInt(farm.AutoValidDate);
			gspacketIn.WriteInt(farm.GainFieldId);
			gspacketIn.WriteInt(farm.KillCropId);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600868B RID: 34443 RVA: 0x002C4D94 File Offset: 0x002C2F94
		public GSPacketIn SendFarmLandInfo(FarmInventory farm)
		{
			UserFieldInfo[] fields = farm.GetFields();
			GSPacketIn gspacketIn = new GSPacketIn(81, farm.CurrentFarm.FarmID);
			gspacketIn.WriteByte(17);
			gspacketIn.WriteInt(fields.Length);
			UserFieldInfo[] array = fields;
			UserFieldInfo[] array2 = array;
			UserFieldInfo[] array3 = array2;
			foreach (UserFieldInfo userFieldInfo in array3)
			{
				gspacketIn.WriteInt(userFieldInfo.FieldID);
				gspacketIn.WriteInt(userFieldInfo.SeedID);
				gspacketIn.WriteDateTime(userFieldInfo.PlantTime);
				gspacketIn.WriteInt(userFieldInfo.GainCount);
				gspacketIn.WriteInt(userFieldInfo.AccelerateTime);
			}
			gspacketIn.WriteBoolean(farm.midAutumnFlag);
			gspacketIn.WriteBoolean(farm.CurrentFarm.isFarmHelper);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600868C RID: 34444 RVA: 0x002C4E6C File Offset: 0x002C306C
		public void SendSyncOrUpdateChips(GamePlayer player, UserMarkInfo mark)
		{
			GSPacketIn gspacketIn = new GSPacketIn(529);
			gspacketIn.WriteByte(2);
			gspacketIn.WriteInt(player.Mark.Chip.Count);
			gspacketIn.WriteBoolean(mark != null && mark.IsExist);
			bool flag = mark != null && mark.IsExist;
			if (flag)
			{
				gspacketIn.WriteInt(mark.ID);
			}
			foreach (UserMarkInfo userMarkInfo in player.Mark.Chip)
			{
				gspacketIn.WriteInt(userMarkInfo.ID);
				gspacketIn.WriteInt(userMarkInfo.TemplateID);
				gspacketIn.WriteInt(userMarkInfo.BornTime);
				gspacketIn.WriteInt(userMarkInfo.BornLv);
				gspacketIn.WriteInt(userMarkInfo.HammerLv);
				gspacketIn.WriteInt(userMarkInfo.HLv);
				gspacketIn.WriteInt(userMarkInfo.Equip);
				gspacketIn.WriteInt(userMarkInfo.MainProp.Type);
				gspacketIn.WriteInt(userMarkInfo.MainProp.Value);
				gspacketIn.WriteInt(userMarkInfo.MainProp.AttachValue);
				gspacketIn.WriteInt(userMarkInfo.SecondProps.Count);
				foreach (UserMarkProInfo userMarkProInfo in userMarkInfo.SecondProps)
				{
					gspacketIn.WriteInt(userMarkProInfo.Type);
					gspacketIn.WriteInt(userMarkProInfo.Value);
					gspacketIn.WriteInt(userMarkProInfo.AttachValue);
					gspacketIn.WriteInt(userMarkProInfo.AddValue);
					gspacketIn.WriteInt(userMarkProInfo.HummerCount);
				}
				gspacketIn.WriteBoolean(userMarkInfo.IsBind);
				gspacketIn.WriteBoolean(userMarkInfo.IsLock);
				gspacketIn.WriteString(userMarkInfo.SealProListStr);
				gspacketIn.WriteInt(userMarkInfo.SealSkillID);
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600868D RID: 34445 RVA: 0x002C50A4 File Offset: 0x002C32A4
		public void SendEquipScheme(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(529);
			gspacketIn.WriteByte(32);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteInt(player.Mark.Scheme.Count);
			foreach (UserMarkSchemeInfo userMarkSchemeInfo in player.Mark.Scheme)
			{
				gspacketIn.WriteInt(userMarkSchemeInfo.SchemeID);
				gspacketIn.WriteString(userMarkSchemeInfo.SchemeData);
				gspacketIn.WriteString(userMarkSchemeInfo.NickName);
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600868E RID: 34446 RVA: 0x002C515C File Offset: 0x002C335C
		public GSPacketIn SendEXPBottleInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(393, player.PlayerCharacter.ID);
			gspacketIn.WriteInt(player.PlayerCharacter.ID);
			gspacketIn.WriteInt(player.Actives.ExpBottle.TemplateID);
			gspacketIn.WriteInt(player.Actives.ExpBottle.Exp);
			gspacketIn.WriteInt(player.Actives.ExpBottle.Stage);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600868F RID: 34447 RVA: 0x002C51E4 File Offset: 0x002C33E4
		public void SendBoGuTurnInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(744);
			gspacketIn.WriteByte(1);
			DateTime startDate = NewActivityMgr.GetConfigByID(5).StartDate;
			DateTime endDate = NewActivityMgr.GetConfigByID(5).EndDate;
			bool flag = DateTime.Now >= startDate && DateTime.Now < endDate;
			if (flag)
			{
				gspacketIn.WriteInt(1);
			}
			else
			{
				gspacketIn.WriteInt(0);
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008690 RID: 34448 RVA: 0x002C525C File Offset: 0x002C345C
		public void SendBoGuTurnPlayerInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(744);
			gspacketIn.WriteByte(2);
			gspacketIn.WriteInt(player.Actives.BoguTurn.Type);
			gspacketIn.WriteInt(player.Actives.BoguTurn.Level);
			gspacketIn.WriteInt(player.Actives.BoguTurn.SubScript);
			gspacketIn.WriteInt(player.Actives.BoguTurn.PurrMoneyCount);
			gspacketIn.WriteInt(player.Actives.BoguTurn.FreeCoin);
			gspacketIn.WriteInt(player.Actives.BoguTurn.ChipCount);
			gspacketIn.WriteInt(player.Actives.BoguTurn.Score);
			gspacketIn.WriteString(player.Actives.BoguTurn.Claimed);
			gspacketIn.WriteInt(player.Actives.BoguTurn.NiuniumoneyRestrict);
			gspacketIn.WriteInt(player.Actives.BoguTurn.AddState);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008691 RID: 34449 RVA: 0x002C536C File Offset: 0x002C356C
		public void SendHomeTempleInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(297, player.PlayerId);
			gspacketIn.WriteByte(0);
			gspacketIn.WriteInt(player.PlayerCharacter.CurrentSelectIndex);
			gspacketIn.WriteInt(player.PlayerCharacter.CurrentLevel);
			gspacketIn.WriteInt(player.PlayerCharacter.HomeTempleExp);
			gspacketIn.WriteInt(player.PlayerCharacter.PromoteLevel);
			player.SendTCP(gspacketIn);
		}

		// Token: 0x06008692 RID: 34450 RVA: 0x002C53E4 File Offset: 0x002C35E4
		public void SendOpenOrCloseChristmas(int lastPacks, bool isOpen)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145);
			gspacketIn.WriteByte(16);
			gspacketIn.WriteBoolean(isOpen);
			if (isOpen)
			{
				Console.WriteLine("当前活动已经处于开启状态");
				DateTime dateTime = DateTime.Parse(GameProperties.ChristmasBeginDate);
				DateTime dateTime2 = DateTime.Parse(GameProperties.ChristmasEndDate);
				gspacketIn.WriteDateTime(dateTime);
				gspacketIn.WriteDateTime(dateTime2);
				string[] array = GameProperties.ChristmasGifts.Split(new char[] { '|' });
				gspacketIn.WriteInt(array.Length);
				for (int i = 0; i < array.Length; i++)
				{
					string[] array2 = array[i].Split(new char[] { ',' });
					gspacketIn.WriteInt(int.Parse(array2[0]));
					gspacketIn.WriteInt(int.Parse(array2[1]));
				}
				gspacketIn.WriteInt(lastPacks);
				gspacketIn.WriteInt(GameProperties.ChristmasBuildSnowmanDoubleMoney);
			}
			Console.WriteLine("GGGGGGGGGGGGGGGGGG");
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008693 RID: 34451 RVA: 0x002C54E4 File Offset: 0x002C36E4
		public void SendNecklaceInfo(GamePlayer player, int addExp, int needCount)
		{
			GSPacketIn gspacketIn = new GSPacketIn(95, player.PlayerId);
			gspacketIn.WriteInt(player.PlayerCharacter.NecklaceExp);
			gspacketIn.WriteInt(addExp);
			gspacketIn.WriteInt(needCount);
			player.SendTCP(gspacketIn);
		}

		// Token: 0x06008694 RID: 34452 RVA: 0x002C552C File Offset: 0x002C372C
		public void SendNecklaceCastInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(661, player.PlayerId);
			gspacketIn.WriteInt(player.PlayerCharacter.NecklaceCastLevel);
			player.SendTCP(gspacketIn);
		}

		// Token: 0x06008695 RID: 34453 RVA: 0x002C5568 File Offset: 0x002C3768
		public GSPacketIn SendUserSyncEquipGhost(GamePlayer p)
		{
			GSPacketIn gspacketIn = new GSPacketIn(392);
			List<UserEquipGhostInfo> allEquipGhost = p.GetAllEquipGhost();
			gspacketIn.WriteInt(allEquipGhost.Count);
			foreach (UserEquipGhostInfo userEquipGhostInfo in allEquipGhost)
			{
				gspacketIn.WriteInt(userEquipGhostInfo.BagType);
				gspacketIn.WriteInt(userEquipGhostInfo.Place);
				gspacketIn.WriteInt(userEquipGhostInfo.Level);
				gspacketIn.WriteInt(userEquipGhostInfo.TotalGhost);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008696 RID: 34454 RVA: 0x00241880 File Offset: 0x0023FA80
		public void SendDiceResult(GamePlayer player, byte from, byte to)
		{
			GSPacketIn gspacketIn = new GSPacketIn(608, player.PlayerId);
			gspacketIn.WriteByte(16);
			gspacketIn.WriteByte(from);
			gspacketIn.WriteByte(to);
			player.SendTCP(gspacketIn);
		}

		// Token: 0x06008697 RID: 34455 RVA: 0x002C5618 File Offset: 0x002C3818
		public GSPacketIn SendUpdatePlayerProperty(PlayerInfo info, PlayerProInventory PlayerProp)
		{
			string[] array = new string[] { "Attack", "Defence", "Agility", "Luck" };
			GSPacketIn gspacketIn = new GSPacketIn(165, info.ID);
			gspacketIn.WriteInt(info.ID);
			foreach (string text in array)
			{
				gspacketIn.WriteInt(PlayerProp.Current["Avatar"][text]);
			}
			gspacketIn.WriteInt(PlayerProp.Current["Avatar"]["HP"]);
			gspacketIn.WriteInt(PlayerProp.Current["Damage"]["Avatar"]);
			gspacketIn.WriteInt(PlayerProp.Current["Armor"]["Avatar"]);
			foreach (string text2 in array)
			{
				gspacketIn.WriteInt(PlayerProp.Current["Temple"][text2]);
			}
			gspacketIn.WriteInt(PlayerProp.Current["Temple"]["HP"]);
			gspacketIn.WriteInt(PlayerProp.Current["Damage"]["Temple"]);
			gspacketIn.WriteInt(PlayerProp.Current["Armor"]["Temple"]);
			foreach (string text3 in array)
			{
				gspacketIn.WriteInt(PlayerProp.Current["Necklace"][text3]);
			}
			gspacketIn.WriteInt(PlayerProp.Current["Necklace"]["HP"]);
			foreach (string text4 in array)
			{
				gspacketIn.WriteInt(PlayerProp.Current["Amulet"][text4]);
			}
			gspacketIn.WriteInt(PlayerProp.Current["Amulet"]["HP"]);
			gspacketIn.WriteInt(PlayerProp.Current["Damage"]["Amulet"]);
			gspacketIn.WriteInt(PlayerProp.Current["Armor"]["Amulet"]);
			gspacketIn.WriteInt(PlayerProp.Current["Damage"]["Enchant"]);
			gspacketIn.WriteInt(PlayerProp.Current["Armor"]["Enchant"]);
			int num = ((PlayerProp.Current.ContainsKey("Damage") && PlayerProp.Current["Damage"].ContainsKey("Relic")) ? PlayerProp.Current["Damage"]["Relic"] : 0);
			int num2 = ((PlayerProp.Current.ContainsKey("Armor") && PlayerProp.Current["Armor"].ContainsKey("Relic")) ? PlayerProp.Current["Armor"]["Relic"] : 0);
			gspacketIn.WriteInt(num);
			gspacketIn.WriteInt(num2);
			gspacketIn.WriteInt(info.FineSuitExp);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x06008698 RID: 34456 RVA: 0x002C598C File Offset: 0x002C3B8C
		public void SendActiveInfo(GamePlayer player)
		{
			DateTime startDate = NewActivityMgr.GetConfigByID(6).StartDate;
			DateTime endDate = NewActivityMgr.GetConfigByID(6).EndDate;
			GSPacketIn gspacketIn = new GSPacketIn(702);
			gspacketIn.WriteByte(1);
			bool flag = DateTime.Now >= startDate && DateTime.Now <= endDate;
			if (flag)
			{
				gspacketIn.WriteInt(1);
			}
			else
			{
				gspacketIn.WriteInt(0);
			}
			gspacketIn.WriteDateTime(startDate);
			gspacketIn.WriteDateTime(endDate);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x06008699 RID: 34457 RVA: 0x002C5A14 File Offset: 0x002C3C14
		public void SendCatchBeastOpen(int playerID)
		{
			DateTime startDate = NewActivityMgr.GetConfigByID(7).StartDate;
			DateTime endDate = NewActivityMgr.GetConfigByID(7).EndDate;
			GSPacketIn gspacketIn = new GSPacketIn(145, playerID);
			gspacketIn.WriteByte(32);
			bool flag = DateTime.Now >= startDate && DateTime.Now <= endDate;
			if (flag)
			{
				gspacketIn.WriteBoolean(true);
			}
			else
			{
				gspacketIn.WriteBoolean(false);
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600869A RID: 34458 RVA: 0x002C5A8C File Offset: 0x002C3C8C
		public void SendOpenDDPlay(PlayerInfo player)
		{
			DateTime startDate = NewActivityMgr.GetConfigByID(8).StartDate;
			DateTime endDate = NewActivityMgr.GetConfigByID(8).EndDate;
			int num = int.Parse(NewActivityMgr.GetConfigByID(8).Params1);
			GSPacketIn gspacketIn = new GSPacketIn(145);
			gspacketIn.WriteByte(74);
			bool flag = DateTime.Now >= startDate && DateTime.Now <= endDate;
			if (flag)
			{
				gspacketIn.WriteBoolean(true);
			}
			else
			{
				gspacketIn.WriteBoolean(false);
			}
			gspacketIn.WriteDateTime(startDate);
			gspacketIn.WriteDateTime(endDate);
			gspacketIn.WriteInt(num);
			gspacketIn.WriteInt(0);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600869B RID: 34459 RVA: 0x002C5B38 File Offset: 0x002C3D38
		public GSPacketIn SendPlayerFigSpiritinit(int ID, List<UserGemStone> gems)
		{
			GSPacketIn gspacketIn = new GSPacketIn(209, ID);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteBoolean(true);
			gspacketIn.WriteInt(gems.Count);
			foreach (UserGemStone userGemStone in gems)
			{
				gspacketIn.WriteInt(userGemStone.UserID);
				gspacketIn.WriteInt(userGemStone.FigSpiritId);
				gspacketIn.WriteString(userGemStone.FigSpiritIdValue);
				gspacketIn.WriteInt(userGemStone.EquipPlace);
			}
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600869C RID: 34460 RVA: 0x002C5BF0 File Offset: 0x002C3DF0
		public GSPacketIn SendPlayerFigSpiritUp(int ID, UserGemStone gem, bool isUp, bool isMaxLevel, bool isFall, int num, int dir)
		{
			GSPacketIn gspacketIn = new GSPacketIn(209, ID);
			gspacketIn.WriteByte(2);
			string[] array = gem.FigSpiritIdValue.Split(new char[] { '|' });
			gspacketIn.WriteBoolean(isUp);
			gspacketIn.WriteBoolean(isMaxLevel);
			gspacketIn.WriteBoolean(isFall);
			gspacketIn.WriteInt(num);
			gspacketIn.WriteInt(array.Length);
			string[] array2 = array;
			string[] array3 = array2;
			foreach (string text in array3)
			{
				gspacketIn.WriteInt(gem.FigSpiritId);
				gspacketIn.WriteInt(Convert.ToInt32(text.Split(new char[] { ',' })[0]));
				gspacketIn.WriteInt(Convert.ToInt32(text.Split(new char[] { ',' })[1]));
				gspacketIn.WriteInt(Convert.ToInt32(text.Split(new char[] { ',' })[2]));
			}
			gspacketIn.WriteInt(gem.EquipPlace);
			gspacketIn.WriteInt(dir);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x0600869D RID: 34461 RVA: 0x002C5D10 File Offset: 0x002C3F10
		public void SendPairBoxInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(725);
			gspacketIn.WriteByte(1);
			bool flag = DateTime.Now >= DateTime.Parse(GameProperties.PairBoxBeginTime) && DateTime.Now < DateTime.Parse(GameProperties.PairBoxEndTime);
			if (flag)
			{
				gspacketIn.WriteInt(1);
			}
			else
			{
				gspacketIn.WriteInt(0);
			}
			gspacketIn.WriteDateTime(DateTime.Parse(GameProperties.PairBoxBeginTime));
			gspacketIn.WriteDateTime(DateTime.Parse(GameProperties.PairBoxEndTime));
			gspacketIn.WriteDateTime(DateTime.Parse(GameProperties.PairBoxEndTime).AddHours((double)GameProperties.PairBoxExchangeHour));
			gspacketIn.WriteInt(player.Actives.PairBox.FreeReelCount);
			gspacketIn.WriteInt(player.Actives.PairBox.ReelCount);
			gspacketIn.WriteBoolean(true);
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600869E RID: 34462 RVA: 0x002C5DF8 File Offset: 0x002C3FF8
		public void SendPairBoxBaseInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(725);
			gspacketIn.WriteByte(2);
			gspacketIn.Parameter1 = 1;
			gspacketIn.WriteInt(player.Actives.PairBox.FreeReelCount);
			gspacketIn.WriteInt(player.Actives.PairBox.ReelCount);
			gspacketIn.WriteInt(player.Actives.PairBox.MarkCount);
			gspacketIn.WriteInt(player.Actives.PairBox.TotalBuyCount);
			gspacketIn.WriteInt(player.Actives.PairBox.FreeResetCount);
			gspacketIn.WriteInt(player.Actives.PairBoxCell.Length);
			PairBoxCeilInfo[] pairBoxCell = player.Actives.PairBoxCell;
			PairBoxCeilInfo[] array = pairBoxCell;
			PairBoxCeilInfo[] array2 = array;
			foreach (PairBoxCeilInfo pairBoxCeilInfo in array2)
			{
				gspacketIn.WriteInt(pairBoxCeilInfo.Pos);
				gspacketIn.WriteInt(pairBoxCeilInfo.ID);
				gspacketIn.WriteBoolean(pairBoxCeilInfo.IsGet);
			}
			this.SendTCP(gspacketIn);
		}

		// Token: 0x0600869F RID: 34463 RVA: 0x002C5F0C File Offset: 0x002C410C
		public void SendForthActiveOpen(GamePlayer player)
		{
			DateTime startDate = NewActivityMgr.GetConfigByID(10).StartDate;
			DateTime endDate = NewActivityMgr.GetConfigByID(10).EndDate;
			int openSwitch = NewActivityMgr.GetConfigByID(10).OpenSwitch;
			GSPacketIn gspacketIn = new GSPacketIn(752);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteInt(openSwitch);
			gspacketIn.WriteDateTime(Convert.ToDateTime(startDate));
			gspacketIn.WriteDateTime(Convert.ToDateTime(endDate));
			gspacketIn.WriteInt(openSwitch);
			gspacketIn.WriteDateTime(Convert.ToDateTime(startDate));
			gspacketIn.WriteDateTime(Convert.ToDateTime(endDate));
			gspacketIn.WriteInt(openSwitch);
			gspacketIn.WriteDateTime(Convert.ToDateTime(startDate));
			gspacketIn.WriteDateTime(Convert.ToDateTime(endDate));
			gspacketIn.WriteInt(openSwitch);
			gspacketIn.WriteDateTime(Convert.ToDateTime(startDate));
			gspacketIn.WriteDateTime(Convert.ToDateTime(endDate));
			gspacketIn.WriteDateTime(Convert.ToDateTime(startDate));
			gspacketIn.WriteDateTime(Convert.ToDateTime(startDate));
			player.SendTCP(gspacketIn);
		}

		// Token: 0x060086A0 RID: 34464 RVA: 0x002C6000 File Offset: 0x002C4200
		private void SendcavelootOpen(GamePlayer player, GameClient client, GSPacketIn packet)
		{
			TsActivityInfo configByID = NewActivityMgr.GetConfigByID(12);
			PlayerData playerData = new PlayerData();
			int num = 1;
			int num2 = playerData.Sys_Users_CaveLoot_PlayerInfo.Sum((Sys_Users_CaveLoot_PlayerInfo p) => p.OpenCount);
			int num3 = (int)((double)num2 * 0.01);
			bool flag = num3 >= 100;
			if (flag)
			{
				num = 0;
			}
			bool flag2 = num == 0;
			if (flag2)
			{
				client.Player.SendMessage("活动已经结束.");
			}
			else
			{
				GSPacketIn gspacketIn = new GSPacketIn(772, client.Player.PlayerId);
				gspacketIn.WriteByte(1);
				gspacketIn.WriteInt(1);
				client.SendTCP(gspacketIn);
				Console.WriteLine("矿洞寻宝初始化成功！");
			}
		}

		// Token: 0x060086A1 RID: 34465 RVA: 0x002C60E4 File Offset: 0x002C42E4
		public GSPacketIn SendZhanling(GamePlayer Player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(300, Player.PlayerCharacter.ID);
			int num = Player.m_userZhanlingInfos.Length;
			string canGetStringByTypeWithUserID = zhanlingMgr.GetCanGetStringByTypeWithUserID(1, Player.PlayerId);
			string canGetStringByTypeWithUserID2 = zhanlingMgr.GetCanGetStringByTypeWithUserID(2, Player.PlayerId);
			string canGetStringByTypeWithUserID3 = zhanlingMgr.GetCanGetStringByTypeWithUserID(3, Player.PlayerId);
			zhanling[] zhanlingInfoWithType = zhanlingMgr.GetZhanlingInfoWithType(1);
			gspacketIn.WriteString(canGetStringByTypeWithUserID);
			gspacketIn.WriteString(canGetStringByTypeWithUserID2);
			gspacketIn.WriteString(canGetStringByTypeWithUserID3);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x060086A2 RID: 34466 RVA: 0x002C6170 File Offset: 0x002C4370
		public GSPacketIn SendZhanlingDispaly(GamePlayer Player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(301, Player.PlayerCharacter.ID);
			int num = Player.m_userZhanlingInfos.Length;
			gspacketIn.WriteInt(Player.PlayerCharacter.zhanling);
			gspacketIn.WriteInt(Player.PlayerCharacter.zhanlingExp);
			gspacketIn.WriteInt(Player.PlayerCharacter.zhanlingLevel);
			gspacketIn.WriteInt(Player.PlayerCharacter.zhanlingVipType);
			this.SendTCP(gspacketIn);
			return gspacketIn;
		}

		// Token: 0x060086A3 RID: 34467 RVA: 0x0000F4C1 File Offset: 0x0000D6C1
		public void SendcavelootOpen(GamePlayer player)
		{
			throw new NotImplementedException();
		}

		// Token: 0x060086A4 RID: 34468 RVA: 0x0000F4C1 File Offset: 0x0000D6C1
		public void SendUpdateRankList(GamePlayer player)
		{
			throw new NotImplementedException();
		}

		// Token: 0x060086A5 RID: 34469 RVA: 0x0000F4C1 File Offset: 0x0000D6C1
		public void SendUpdateJackpot(GamePlayer player)
		{
			throw new NotImplementedException();
		}

		// Token: 0x060086A6 RID: 34470 RVA: 0x0000F4C1 File Offset: 0x0000D6C1
		public void SendUpdateInfo(GamePlayer player)
		{
			throw new NotImplementedException();
		}

		// Token: 0x060086A7 RID: 34471 RVA: 0x0000F4C1 File Offset: 0x0000D6C1
		public void SendUpdateBoxList(GamePlayer player)
		{
			throw new NotImplementedException();
		}

		// Token: 0x0400538E RID: 21390
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x0400538F RID: 21391
		protected readonly GameClient m_gameClient;
	}
}
