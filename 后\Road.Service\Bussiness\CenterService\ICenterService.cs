﻿using System;
using System.CodeDom.Compiler;
using System.ServiceModel;

namespace Bussiness.CenterService
{
	// Token: 0x02001005 RID: 4101
	[GeneratedCode("System.ServiceModel", "4.0.0.0")]
	[ServiceContract(ConfigurationName = "CenterService.ICenterService")]
	public interface ICenterService
	{
		// Token: 0x06008BFD RID: 35837
		[OperationContract(Action = "http://tempuri.org/ICenterService/GetServerList", ReplyAction = "http://tempuri.org/ICenterService/GetServerListResponse")]
		ServerData[] GetServerList(int areaid);

		// Token: 0x06008BFE RID: 35838
		[OperationContract(Action = "http://tempuri.org/ICenterService/ChargeMoney", ReplyAction = "http://tempuri.org/ICenterService/ChargeMoneyResponse")]
		bool ChargeMoney(int userID, int areaid, string chargeID);

		// Token: 0x06008BFF RID: 35839
		[OperationContract(Action = "http://tempuri.org/ICenterService/SystemNotice", ReplyAction = "http://tempuri.org/ICenterService/SystemNoticeResponse")]
		bool SystemNotice(string msg);

		// Token: 0x06008C00 RID: 35840
		[OperationContract(Action = "http://tempuri.org/ICenterService/KitoffUser", ReplyAction = "http://tempuri.org/ICenterService/KitoffUserResponse")]
		bool KitoffUser(int playerID, int areaid, string msg);

		// Token: 0x06008C01 RID: 35841
		[OperationContract(Action = "http://tempuri.org/ICenterService/ReLoadServerList", ReplyAction = "http://tempuri.org/ICenterService/ReLoadServerListResponse")]
		bool ReLoadServerList();

		// Token: 0x06008C02 RID: 35842
		[OperationContract(Action = "http://tempuri.org/ICenterService/MailNotice", ReplyAction = "http://tempuri.org/ICenterService/MailNoticeResponse")]
		bool MailNotice(int playerID, int areaid);

		// Token: 0x06008C03 RID: 35843
		[OperationContract(Action = "http://tempuri.org/ICenterService/ActivePlayer", ReplyAction = "http://tempuri.org/ICenterService/ActivePlayerResponse")]
		bool ActivePlayer(bool isActive);

		// Token: 0x06008C04 RID: 35844
		[OperationContract(Action = "http://tempuri.org/ICenterService/CreatePlayer", ReplyAction = "http://tempuri.org/ICenterService/CreatePlayerResponse")]
		bool CreatePlayer(int id, string name, string password, int areaid, bool isFirst);

		// Token: 0x06008C05 RID: 35845
		[OperationContract(Action = "http://tempuri.org/ICenterService/ValidateLoginAndGetID", ReplyAction = "http://tempuri.org/ICenterService/ValidateLoginAndGetIDResponse")]
		bool ValidateLoginAndGetID(string name, string password, int areaid, ref int userID, ref bool isFirst);

		// Token: 0x06008C06 RID: 35846
		[OperationContract(Action = "http://tempuri.org/ICenterService/AASUpdateState", ReplyAction = "http://tempuri.org/ICenterService/AASUpdateStateResponse")]
		bool AASUpdateState(bool state);

		// Token: 0x06008C07 RID: 35847
		[OperationContract(Action = "http://tempuri.org/ICenterService/AASGetState", ReplyAction = "http://tempuri.org/ICenterService/AASGetStateResponse")]
		int AASGetState();

		// Token: 0x06008C08 RID: 35848
		[OperationContract(Action = "http://tempuri.org/ICenterService/ExperienceRateUpdate", ReplyAction = "http://tempuri.org/ICenterService/ExperienceRateUpdateResponse")]
		int ExperienceRateUpdate(int serverId);

		// Token: 0x06008C09 RID: 35849
		[OperationContract(Action = "http://tempuri.org/ICenterService/NoticeServerUpdate", ReplyAction = "http://tempuri.org/ICenterService/NoticeServerUpdateResponse")]
		int NoticeServerUpdate(int serverId, int type);

		// Token: 0x06008C0A RID: 35850
		[OperationContract(Action = "http://tempuri.org/ICenterService/UpdateConfigState", ReplyAction = "http://tempuri.org/ICenterService/UpdateConfigStateResponse")]
		bool UpdateConfigState(int type, bool state);

		// Token: 0x06008C0B RID: 35851
		[OperationContract(Action = "http://tempuri.org/ICenterService/GetConfigState", ReplyAction = "http://tempuri.org/ICenterService/GetConfigStateResponse")]
		int GetConfigState(int type);

		// Token: 0x06008C0C RID: 35852
		[OperationContract(Action = "http://tempuri.org/ICenterService/Reload", ReplyAction = "http://tempuri.org/ICenterService/ReloadResponse")]
		bool Reload(string type);

		// Token: 0x06008C0D RID: 35853
		[OperationContract(Action = "http://tempuri.org/ICenterService/Reload", ReplyAction = "http://tempuri.org/ICenterService/ReloadResponse")]
		bool CheckUserValidate(int playerID, string keyString);
	}
}
