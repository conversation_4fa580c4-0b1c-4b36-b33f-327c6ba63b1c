﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DFE RID: 3582
	public class AE1205 : BasePetEffect
	{
		// Token: 0x06007DA9 RID: 32169 RVA: 0x0029CF08 File Offset: 0x0029B108
		public AE1205(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1205, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DAA RID: 32170 RVA: 0x0029CF88 File Offset: 0x0029B188
		public override bool Start(Living living)
		{
			AE1205 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1205) as AE1205;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DAB RID: 32171 RVA: 0x0003075B File Offset: 0x0002E95B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DAC RID: 32172 RVA: 0x00030771 File Offset: 0x0002E971
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DAD RID: 32173 RVA: 0x0029CFE8 File Offset: 0x0029B1E8
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddPetEffect(new CE1205(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004C2A RID: 19498
		private int m_type = 0;

		// Token: 0x04004C2B RID: 19499
		private int m_count = 0;

		// Token: 0x04004C2C RID: 19500
		private int m_probability = 0;

		// Token: 0x04004C2D RID: 19501
		private int m_delay = 0;

		// Token: 0x04004C2E RID: 19502
		private int m_coldDown = 0;

		// Token: 0x04004C2F RID: 19503
		private int m_currentId;

		// Token: 0x04004C30 RID: 19504
		private int m_added = 0;
	}
}
