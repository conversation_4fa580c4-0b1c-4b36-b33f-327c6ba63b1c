﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D38 RID: 3384
	public class PetDefensivePosture_CritDamage : BasePetEffect
	{
		// Token: 0x06007988 RID: 31112 RVA: 0x0002DADD File Offset: 0x0002BCDD
		public PetDefensivePosture_CritDamage(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetDefensivePosture_CritDamage, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x06007989 RID: 31113 RVA: 0x0028B2F8 File Offset: 0x002894F8
		public override bool Start(Living living)
		{
			PetDefensivePosture_CritDamage petDefensivePosture_CritDamage = living.PetEffectList.GetOfType(ePetEffectType.PetDefensivePosture_CritDamage) as PetDefensivePosture_CritDamage;
			bool flag = petDefensivePosture_CritDamage != null;
			bool flag2;
			if (flag)
			{
				petDefensivePosture_CritDamage.m_probability = ((this.m_probability > petDefensivePosture_CritDamage.m_probability) ? this.m_probability : petDefensivePosture_CritDamage.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600798A RID: 31114 RVA: 0x0002DB0E File Offset: 0x0002BD0E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerSkip += this.player_PlayerSkip;
		}

		// Token: 0x0600798B RID: 31115 RVA: 0x0002DB24 File Offset: 0x0002BD24
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerSkip -= this.player_PlayerSkip;
		}

		// Token: 0x0600798C RID: 31116 RVA: 0x0028B358 File Offset: 0x00289558
		private void player_PlayerSkip(Player player)
		{
			player.Game.sendShowPicSkil(player, base.ElementInfo, false);
			player.Game.sendShowPicSkil(player, base.ElementInfo, true);
			new PetDefensivePosture_CritDamageEquip(this.m_count, base.Info.ID.ToString()).Start(player);
		}

		// Token: 0x04004733 RID: 18227
		private int m_count;

		// Token: 0x04004734 RID: 18228
		private int m_probability;

		// Token: 0x04004735 RID: 18229
		private int m_currentId;
	}
}
