﻿using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.ServiceModel;
using System.Xml.Serialization;

namespace Bussiness.WebLogin
{
	// Token: 0x02000FBD RID: 4029
	[DebuggerStepThrough]
	[GeneratedCode("System.ServiceModel", "4.0.0.0")]
	[EditorBrowsable(EditorBrowsableState.Advanced)]
	[MessageContract(WrapperName = "Get_UserSexResponse", WrapperNamespace = "dandantang", IsWrapped = true)]
	public class Get_UserSexResponse
	{
		// Token: 0x06008A4E RID: 35406 RVA: 0x0000586E File Offset: 0x00003A6E
		public Get_UserSexResponse()
		{
		}

		// Token: 0x06008A4F RID: 35407 RVA: 0x000363B6 File Offset: 0x000345B6
		public Get_UserSexResponse(bool? Get_UserSexResult)
		{
			this.Get_UserSexResult = Get_UserSexResult;
		}

		// Token: 0x040054A3 RID: 21667
		[MessageBodyMember(Namespace = "dandantang", Order = 0)]
		[XmlElement(IsNullable = true)]
		public bool? Get_UserSexResult;
	}
}
