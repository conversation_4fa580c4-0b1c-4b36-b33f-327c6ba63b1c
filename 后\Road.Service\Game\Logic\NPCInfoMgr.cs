﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using Bussiness;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000CA6 RID: 3238
	public static class NPCInfoMgr
	{
		// Token: 0x060073D3 RID: 29651 RVA: 0x0026458C File Offset: 0x0026278C
		public static bool Init()
		{
			return NPCInfoMgr.ReLoad();
		}

		// Token: 0x060073D4 RID: 29652 RVA: 0x002645A4 File Offset: 0x002627A4
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, NpcInfo> dictionary = NPCInfoMgr.LoadFromDatabase();
				bool flag = dictionary != null && dictionary.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, NpcInfo>>(ref NPCInfoMgr.m_npcs, dictionary);
				}
				return true;
			}
			catch (Exception ex)
			{
				NPCInfoMgr.log.Error("NPCInfoMgr", ex);
			}
			return false;
		}

		// Token: 0x060073D5 RID: 29653 RVA: 0x0026460C File Offset: 0x0026280C
		private static Dictionary<int, NpcInfo> LoadFromDatabase()
		{
			Dictionary<int, NpcInfo> dictionary = new Dictionary<int, NpcInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				NpcInfo[] allNPCInfo = produceBussiness.GetAllNPCInfo();
				NpcInfo[] array = allNPCInfo;
				NpcInfo[] array2 = array;
				NpcInfo[] array3 = array2;
				foreach (NpcInfo npcInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(npcInfo.ID);
					if (flag)
					{
						dictionary.Add(npcInfo.ID, npcInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x060073D6 RID: 29654 RVA: 0x002646A8 File Offset: 0x002628A8
		public static NpcInfo GetNpcInfoById(int id)
		{
			bool flag = NPCInfoMgr.m_npcs.ContainsKey(id);
			NpcInfo npcInfo;
			if (flag)
			{
				npcInfo = NPCInfoMgr.m_npcs[id];
			}
			else
			{
				npcInfo = null;
			}
			return npcInfo;
		}

		// Token: 0x040043D8 RID: 17368
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040043D9 RID: 17369
		private static Dictionary<int, NpcInfo> m_npcs = new Dictionary<int, NpcInfo>();

		// Token: 0x040043DA RID: 17370
		private static ReaderWriterLock m_lock = new ReaderWriterLock();

		// Token: 0x040043DB RID: 17371
		private static ThreadSafeRandom m_rand = new ThreadSafeRandom();
	}
}
