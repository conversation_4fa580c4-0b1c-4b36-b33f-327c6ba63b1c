﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F13 RID: 3859
	[GameCommand(12, "跳过")]
	public class SkipNextCommandP : ICommandHandler
	{
		// Token: 0x060083BF RID: 33727 RVA: 0x002B5778 File Offset: 0x002B3978
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool isAttacking = player.IsAttacking;
			if (isAttacking)
			{
				player.Skip((int)packet.ReadByte());
			}
		}
	}
}
