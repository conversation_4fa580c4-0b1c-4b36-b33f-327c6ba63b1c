﻿using System;
using System.Reflection;
using Game.Base.Packets;
using Game.Logic.Phy.Object;
using log4net;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F0C RID: 3852
	[GameCommand(133, "跳过剧情动画")]
	public class PassDramaCommand : ICommandHandler
	{
		// Token: 0x060083B0 RID: 33712 RVA: 0x002B530C File Offset: 0x002B350C
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool flag = game is PVEGame && game.GameState != eGameState.Playing;
			if (flag)
			{
				PVEGame pvegame = game as PVEGame;
				pvegame.IsPassDrama = packet.ReadBoolean();
				pvegame.CheckState(0);
			}
		}

		// Token: 0x04005225 RID: 21029
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
	}
}
