﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CCB RID: 3275
	[SpellAttibute(82)]
	public class TiredShootSpell : ISpellHandler
	{
		// Token: 0x06007518 RID: 29976 RVA: 0x0026EABC File Offset: 0x0026CCBC
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.TiredShoot = true;
			}
			else
			{
				bool flag = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
				if (flag)
				{
					game.CurrentLiving.TiredShoot = true;
				}
			}
		}
	}
}
