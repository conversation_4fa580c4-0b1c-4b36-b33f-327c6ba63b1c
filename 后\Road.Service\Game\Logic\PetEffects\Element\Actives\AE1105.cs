﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DCB RID: 3531
	public class AE1105 : BasePetEffect
	{
		// Token: 0x06007C9B RID: 31899 RVA: 0x002984D8 File Offset: 0x002966D8
		public AE1105(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1105, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C9C RID: 31900 RVA: 0x00298558 File Offset: 0x00296758
		public override bool Start(Living living)
		{
			AE1105 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1105) as AE1105;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C9D RID: 31901 RVA: 0x0002FC45 File Offset: 0x0002DE45
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
			player.PlayerCompleteShoot += this.Player_PlayerCompleteShoot;
		}

		// Token: 0x06007C9E RID: 31902 RVA: 0x0002FC0D File Offset: 0x0002DE0D
		private void Player_PlayerCompleteShoot(Player player)
		{
			player.PetEffects.AddBloodPercent = 0;
		}

		// Token: 0x06007C9F RID: 31903 RVA: 0x002985B8 File Offset: 0x002967B8
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 1000;
				player.PetEffects.AddBloodPercent = this.m_added;
			}
		}

		// Token: 0x06007CA0 RID: 31904 RVA: 0x0002FC6E File Offset: 0x0002DE6E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
			player.PlayerCompleteShoot -= this.Player_PlayerCompleteShoot;
		}

		// Token: 0x04004AC7 RID: 19143
		private int m_type = 0;

		// Token: 0x04004AC8 RID: 19144
		private int m_count = 0;

		// Token: 0x04004AC9 RID: 19145
		private int m_probability = 0;

		// Token: 0x04004ACA RID: 19146
		private int m_delay = 0;

		// Token: 0x04004ACB RID: 19147
		private int m_coldDown = 0;

		// Token: 0x04004ACC RID: 19148
		private int m_currentId;

		// Token: 0x04004ACD RID: 19149
		private int m_added = 0;
	}
}
