﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EF6 RID: 3830
	public class ReduceStrengthEffect : AbstractEffect
	{
		// Token: 0x0600836C RID: 33644 RVA: 0x00034524 File Offset: 0x00032724
		public ReduceStrengthEffect(int count, int reduce)
			: base(eEffectType.ReduceStrengthEffect)
		{
			this.m_count = count;
			this.m_reduce = reduce;
		}

		// Token: 0x0600836D RID: 33645 RVA: 0x002B3B18 File Offset: 0x002B1D18
		public override bool Start(Living living)
		{
			ReduceStrengthEffect reduceStrengthEffect = living.EffectList.GetOfType(eEffectType.ReduceStrengthEffect) as ReduceStrengthEffect;
			bool flag = reduceStrengthEffect != null;
			bool flag2;
			if (flag)
			{
				reduceStrengthEffect.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600836E RID: 33646 RVA: 0x0003453E File Offset: 0x0003273E
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginFitting;
			living.Game.SendPlayerPicture(living, 1, true);
		}

		// Token: 0x0600836F RID: 33647 RVA: 0x00034563 File Offset: 0x00032763
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginFitting;
			living.Game.SendPlayerPicture(living, 1, false);
		}

		// Token: 0x06008370 RID: 33648 RVA: 0x002B3B60 File Offset: 0x002B1D60
		private void player_BeginFitting(Living living)
		{
			this.m_count--;
			bool flag = living is Player;
			if (flag)
			{
				(living as Player).Energy -= this.m_reduce;
			}
			bool flag2 = this.m_count < 0;
			if (flag2)
			{
				bool flag3 = living is Player;
				if (flag3)
				{
					((Player)living).LimitEnergy = false;
				}
				this.Stop();
			}
		}

		// Token: 0x04005216 RID: 21014
		private int m_count;

		// Token: 0x04005217 RID: 21015
		private int m_reduce;
	}
}
