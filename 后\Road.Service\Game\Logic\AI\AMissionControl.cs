﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.AI
{
	// Token: 0x02000F3D RID: 3901
	public abstract class AMissionControl
	{
		// Token: 0x170014AE RID: 5294
		// (get) Token: 0x060084AA RID: 33962 RVA: 0x002B8BC0 File Offset: 0x002B6DC0
		// (set) Token: 0x060084AB RID: 33963 RVA: 0x0003502C File Offset: 0x0003322C
		public PVEGame Game
		{
			get
			{
				return this.m_game;
			}
			set
			{
				this.m_game = value;
			}
		}

		// Token: 0x170014AF RID: 5295
		// (get) Token: 0x060084AC RID: 33964 RVA: 0x002B8BD8 File Offset: 0x002B6DD8
		// (set) Token: 0x060084AD RID: 33965 RVA: 0x00035036 File Offset: 0x00033236
		public Living Body
		{
			get
			{
				return this.m_body;
			}
			set
			{
				this.m_body = value;
			}
		}

		// Token: 0x060084AE RID: 33966 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnLivingDie(Living living)
		{
		}

		// Token: 0x060084AF RID: 33967 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnPrepareNewGame()
		{
		}

		// Token: 0x060084B0 RID: 33968 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnPrepareNewSession()
		{
		}

		// Token: 0x060084B1 RID: 33969 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnStartMovie()
		{
		}

		// Token: 0x060084B2 RID: 33970 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnStartGame()
		{
		}

		// Token: 0x060084B3 RID: 33971 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnBeginNewTurn()
		{
		}

		// Token: 0x060084B4 RID: 33972 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnNewTurnStarted()
		{
		}

		// Token: 0x060084B5 RID: 33973 RVA: 0x00069EBC File Offset: 0x000680BC
		public virtual bool CanGameOver()
		{
			return true;
		}

		// Token: 0x060084B6 RID: 33974 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnPrepareGameOver()
		{
		}

		// Token: 0x060084B7 RID: 33975 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnGameOverMovie()
		{
		}

		// Token: 0x060084B8 RID: 33976 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnGameOver()
		{
		}

		// Token: 0x060084B9 RID: 33977 RVA: 0x00052AE4 File Offset: 0x00050CE4
		public virtual int CalculateScoreGrade(int score)
		{
			return 0;
		}

		// Token: 0x060084BA RID: 33978 RVA: 0x00052AE4 File Offset: 0x00050CE4
		public virtual int UpdateUIData()
		{
			return 0;
		}

		// Token: 0x060084BB RID: 33979 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void Dispose()
		{
		}

		// Token: 0x060084BC RID: 33980 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void DoOther()
		{
		}

		// Token: 0x060084BD RID: 33981 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnShooted()
		{
		}

		// Token: 0x060084BE RID: 33982 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnMoving()
		{
		}

		// Token: 0x060084BF RID: 33983 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnTakeDamage()
		{
		}

		// Token: 0x060084C0 RID: 33984 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnDied()
		{
		}

		// Token: 0x060084C1 RID: 33985 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnGeneralCommand(GSPacketIn packet)
		{
		}

		// Token: 0x060084C2 RID: 33986 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnPrepareStartGame()
		{
		}

		// Token: 0x060084C3 RID: 33987 RVA: 0x0000F4C1 File Offset: 0x0000D6C1
		internal void OnStartAttacking()
		{
			throw new NotImplementedException();
		}

		// Token: 0x040052AA RID: 21162
		private PVEGame m_game;

		// Token: 0x040052AB RID: 21163
		protected Living m_body;
	}
}
