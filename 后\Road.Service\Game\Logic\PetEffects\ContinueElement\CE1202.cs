﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E95 RID: 3733
	public class CE1202 : BasePetEffect
	{
		// Token: 0x06008119 RID: 33049 RVA: 0x002ABA5C File Offset: 0x002A9C5C
		public CE1202(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1202, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600811A RID: 33050 RVA: 0x002ABADC File Offset: 0x002A9CDC
		public override bool Start(Living living)
		{
			CE1202 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1202) as CE1202;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600811B RID: 33051 RVA: 0x00032812 File Offset: 0x00030A12
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600811C RID: 33052 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600811D RID: 33053 RVA: 0x002ABB3C File Offset: 0x002A9D3C
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 200;
				bool flag2 = living.BaseDamage < (double)this.m_added;
				if (flag2)
				{
					this.m_added = (int)living.BaseDamage - 1;
				}
				living.BaseDamage -= (double)this.m_added;
			}
		}

		// Token: 0x0600811E RID: 33054 RVA: 0x002ABB9C File Offset: 0x002A9D9C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600811F RID: 33055 RVA: 0x002ABBD0 File Offset: 0x002A9DD0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BaseDamage += (double)this.m_added;
			this.m_added = 0;
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005050 RID: 20560
		private int m_type = 0;

		// Token: 0x04005051 RID: 20561
		private int m_count = 0;

		// Token: 0x04005052 RID: 20562
		private int m_probability = 0;

		// Token: 0x04005053 RID: 20563
		private int m_delay = 0;

		// Token: 0x04005054 RID: 20564
		private int m_coldDown = 0;

		// Token: 0x04005055 RID: 20565
		private int m_currentId;

		// Token: 0x04005056 RID: 20566
		private int m_added = 0;
	}
}
