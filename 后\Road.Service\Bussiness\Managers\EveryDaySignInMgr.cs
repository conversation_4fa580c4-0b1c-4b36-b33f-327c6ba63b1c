﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FD0 RID: 4048
	public class EveryDaySignInMgr
	{
		// Token: 0x06008AAC RID: 35500 RVA: 0x002F7FAC File Offset: 0x002F61AC
		public static bool ReLoad()
		{
			try
			{
				TsSignInInfo[] array = EveryDaySignInMgr.LoadEveryDaySignInAwardDb();
				Dictionary<int, List<TsSignInInfo>> dictionary = EveryDaySignInMgr.LoadEveryDaySignInAwards(array);
				bool flag = array.Length != 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, List<TsSignInInfo>>>(ref EveryDaySignInMgr.m_EveryDaySignInAward, dictionary);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = EveryDaySignInMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					EveryDaySignInMgr.log.Error("ReLoad EveryDaySignIn", ex);
				}
				return false;
			}
			return true;
		}

		// Token: 0x06008AAD RID: 35501 RVA: 0x002F8024 File Offset: 0x002F6224
		public static bool Init()
		{
			return EveryDaySignInMgr.ReLoad();
		}

		// Token: 0x06008AAE RID: 35502 RVA: 0x002F803C File Offset: 0x002F623C
		public static TsSignInInfo[] LoadEveryDaySignInAwardDb()
		{
			TsSignInInfo[] allEveryDaySignInAwardInfos;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				allEveryDaySignInAwardInfos = produceBussiness.GetAllEveryDaySignInAwardInfos();
			}
			return allEveryDaySignInAwardInfos;
		}

		// Token: 0x06008AAF RID: 35503 RVA: 0x002F8078 File Offset: 0x002F6278
		public static Dictionary<int, List<TsSignInInfo>> LoadEveryDaySignInAwards(TsSignInInfo[] EveryDaySignInAward)
		{
			Dictionary<int, List<TsSignInInfo>> dictionary = new Dictionary<int, List<TsSignInInfo>>();
			for (int i = 0; i < EveryDaySignInAward.Length; i++)
			{
				TsSignInInfo info = EveryDaySignInAward[i];
				bool flag = !dictionary.Keys.Contains(info.ID);
				if (flag)
				{
					IEnumerable<TsSignInInfo> enumerable = EveryDaySignInAward.Where((TsSignInInfo s) => s.ID == info.ID);
					dictionary.Add(info.ID, enumerable.ToList<TsSignInInfo>());
				}
			}
			return dictionary;
		}

		// Token: 0x06008AB0 RID: 35504 RVA: 0x002F8104 File Offset: 0x002F6304
		public static List<TsSignInInfo> FindEveryDayCurrentID(int ID)
		{
			bool flag = EveryDaySignInMgr.m_EveryDaySignInAward.ContainsKey(ID);
			List<TsSignInInfo> list;
			if (flag)
			{
				list = EveryDaySignInMgr.m_EveryDaySignInAward[ID];
			}
			else
			{
				list = null;
			}
			return list;
		}

		// Token: 0x040054F5 RID: 21749
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040054F6 RID: 21750
		private static Dictionary<int, List<TsSignInInfo>> m_EveryDaySignInAward;
	}
}
