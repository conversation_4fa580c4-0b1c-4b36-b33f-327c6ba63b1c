﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DAD RID: 3501
	public class AE1053 : BasePetEffect
	{
		// Token: 0x06007C01 RID: 31745 RVA: 0x002956A0 File Offset: 0x002938A0
		public AE1053(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1053, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C02 RID: 31746 RVA: 0x00295720 File Offset: 0x00293920
		public override bool Start(Living living)
		{
			AE1053 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1053) as AE1053;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C03 RID: 31747 RVA: 0x0002F733 File Offset: 0x0002D933
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBeginMoving += this.Player_PlayerBeginMoving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007C04 RID: 31748 RVA: 0x00295780 File Offset: 0x00293980
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007C05 RID: 31749 RVA: 0x002957B0 File Offset: 0x002939B0
		private void Player_PlayerBeginMoving(Player player)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				CE1049 ce = player.PetEffectList.GetOfType(ePetEffectType.CE1049) as CE1049;
				bool flag = ce != null;
				if (flag)
				{
					ce.Stop();
				}
				CE1050 ce2 = player.PetEffectList.GetOfType(ePetEffectType.CE1050) as CE1050;
				bool flag2 = ce2 != null;
				if (flag2)
				{
					ce2.Stop();
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007C06 RID: 31750 RVA: 0x0002F75C File Offset: 0x0002D95C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBeginMoving -= this.Player_PlayerBeginMoving;
		}

		// Token: 0x040049F5 RID: 18933
		private int m_type = 0;

		// Token: 0x040049F6 RID: 18934
		private int m_count = 0;

		// Token: 0x040049F7 RID: 18935
		private int m_probability = 0;

		// Token: 0x040049F8 RID: 18936
		private int m_delay = 0;

		// Token: 0x040049F9 RID: 18937
		private int m_coldDown = 0;

		// Token: 0x040049FA RID: 18938
		private int m_currentId;

		// Token: 0x040049FB RID: 18939
		private int m_added = 0;
	}
}
