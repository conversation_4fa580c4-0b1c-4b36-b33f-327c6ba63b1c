﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F14 RID: 3860
	[GameCommand(15, "使用必杀技能")]
	public class StuntCommand : ICommandHandler
	{
		// Token: 0x060083C1 RID: 33729 RVA: 0x002B57A0 File Offset: 0x002B39A0
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool isAttacking = player.IsAttacking;
			if (isAttacking)
			{
				player.UseSpecialSkill();
				player.CurrentShootMinus *= (float)player.CurrentBall.Power;
			}
		}
	}
}
