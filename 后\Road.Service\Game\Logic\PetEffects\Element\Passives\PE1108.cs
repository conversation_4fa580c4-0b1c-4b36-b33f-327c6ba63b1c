﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D62 RID: 3426
	public class PE1108 : BasePetEffect
	{
		// Token: 0x06007A81 RID: 31361 RVA: 0x0028F20C File Offset: 0x0028D40C
		public PE1108(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1108, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A82 RID: 31362 RVA: 0x0028F28C File Offset: 0x0028D48C
		public override bool Start(Living living)
		{
			PE1108 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1108) as PE1108;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A83 RID: 31363 RVA: 0x0002E800 File Offset: 0x0002CA00
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
		}

		// Token: 0x06007A84 RID: 31364 RVA: 0x0002E816 File Offset: 0x0002CA16
		private void Player_BeginSelfTurn(Living living)
		{
			(living as Player).AddPetMP(3);
		}

		// Token: 0x06007A85 RID: 31365 RVA: 0x0002E826 File Offset: 0x0002CA26
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x040047EE RID: 18414
		private int m_type = 0;

		// Token: 0x040047EF RID: 18415
		private int m_count = 0;

		// Token: 0x040047F0 RID: 18416
		private int m_probability = 0;

		// Token: 0x040047F1 RID: 18417
		private int m_delay = 0;

		// Token: 0x040047F2 RID: 18418
		private int m_coldDown = 0;

		// Token: 0x040047F3 RID: 18419
		private int m_currentId;

		// Token: 0x040047F4 RID: 18420
		private int m_added = 0;
	}
}
