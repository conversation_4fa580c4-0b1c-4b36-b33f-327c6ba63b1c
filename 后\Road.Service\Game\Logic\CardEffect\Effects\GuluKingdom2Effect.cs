﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F2A RID: 3882
	public class GuluKingdom2Effect : BaseCardEffect
	{
		// Token: 0x06008432 RID: 33842 RVA: 0x002B7130 File Offset: 0x002B5330
		public GuluKingdom2Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.GuluKingdom2, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008433 RID: 33843 RVA: 0x002B7198 File Offset: 0x002B5398
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.GuluKingdom2) is GuluKingdom2Effect;
			return flag || base.Start(living);
		}

		// Token: 0x06008434 RID: 33844 RVA: 0x00034C50 File Offset: 0x00032E50
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty1;
			player.BeginSelfTurn += this.ChangeProperty;
		}

		// Token: 0x06008435 RID: 33845 RVA: 0x00034C79 File Offset: 0x00032E79
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty1;
			player.BeginSelfTurn -= this.ChangeProperty;
		}

		// Token: 0x06008436 RID: 33846 RVA: 0x002B71D0 File Offset: 0x002B53D0
		private void ChangeProperty(Living player)
		{
			bool flag = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 1;
			if (flag)
			{
				(player as Player).AddDander(this.m_value * 2);
			}
		}

		// Token: 0x06008437 RID: 33847 RVA: 0x002B7220 File Offset: 0x002B5420
		private void ChangeProperty1(Player player)
		{
			bool flag = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 1;
			if (flag)
			{
				player.Game.SendMessage(player.PlayerDetail, "您激活了啵咕王城2件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活啵咕王城2件套卡.", 3);
			}
		}

		// Token: 0x04005273 RID: 21107
		private int m_indexValue = 0;

		// Token: 0x04005274 RID: 21108
		private int m_value = 0;
	}
}
