﻿using System;
using System.Collections;
using System.Data;
using System.Data.SqlClient;

namespace DAL
{
	// Token: 0x02000F9E RID: 3998
	public sealed class SqlHelperParameterCache
	{
		// Token: 0x06008800 RID: 34816 RVA: 0x0000586E File Offset: 0x00003A6E
		private SqlHelperParameterCache()
		{
		}

		// Token: 0x06008801 RID: 34817 RVA: 0x002C94E8 File Offset: 0x002C76E8
		private static SqlParameter[] DiscoverSpParameterSet(string connectionString, string spName, bool includeReturnValueParameter)
		{
			SqlParameter[] array2;
			using (SqlConnection sqlConnection = new SqlConnection(connectionString))
			{
				using (SqlCommand sqlCommand = new SqlCommand(spName, sqlConnection))
				{
					sqlConnection.Open();
					sqlCommand.CommandType = CommandType.StoredProcedure;
					SqlCommandBuilder.DeriveParameters(sqlCommand);
					bool flag = !includeReturnValueParameter;
					if (flag)
					{
						sqlCommand.Parameters.RemoveAt(0);
					}
					SqlParameter[] array = new SqlParameter[sqlCommand.Parameters.Count];
					sqlCommand.Parameters.CopyTo(array, 0);
					array2 = array;
				}
			}
			return array2;
		}

		// Token: 0x06008802 RID: 34818 RVA: 0x002C958C File Offset: 0x002C778C
		private static SqlParameter[] CloneParameters(SqlParameter[] originalParameters)
		{
			SqlParameter[] array = new SqlParameter[originalParameters.Length];
			int i = 0;
			int num = originalParameters.Length;
			while (i < num)
			{
				array[i] = (SqlParameter)((ICloneable)originalParameters[i]).Clone();
				i++;
			}
			return array;
		}

		// Token: 0x06008803 RID: 34819 RVA: 0x002C95D0 File Offset: 0x002C77D0
		public static void CacheParameterSet(string connectionString, string commandText, params SqlParameter[] commandParameters)
		{
			string text = connectionString + ":" + commandText;
			SqlHelperParameterCache.paramCache[text] = commandParameters;
		}

		// Token: 0x06008804 RID: 34820 RVA: 0x002C95F8 File Offset: 0x002C77F8
		public static SqlParameter[] GetCachedParameterSet(string connectionString, string commandText)
		{
			string text = connectionString + ":" + commandText;
			SqlParameter[] array = (SqlParameter[])SqlHelperParameterCache.paramCache[text];
			bool flag = array == null;
			SqlParameter[] array2;
			if (flag)
			{
				array2 = null;
			}
			else
			{
				array2 = SqlHelperParameterCache.CloneParameters(array);
			}
			return array2;
		}

		// Token: 0x06008805 RID: 34821 RVA: 0x002C963C File Offset: 0x002C783C
		public static SqlParameter[] GetSpParameterSet(string connectionString, string spName)
		{
			return SqlHelperParameterCache.GetSpParameterSet(connectionString, spName, false);
		}

		// Token: 0x06008806 RID: 34822 RVA: 0x002C9658 File Offset: 0x002C7858
		public static SqlParameter[] GetSpParameterSet(string connectionString, string spName, bool includeReturnValueParameter)
		{
			string text = connectionString + ":" + spName + (includeReturnValueParameter ? ":include ReturnValue Parameter" : "");
			SqlParameter[] array = (SqlParameter[])SqlHelperParameterCache.paramCache[text];
			bool flag = array == null;
			if (flag)
			{
				object obj = (SqlHelperParameterCache.paramCache[text] = SqlHelperParameterCache.DiscoverSpParameterSet(connectionString, spName, includeReturnValueParameter));
				object obj2 = obj;
				object obj3 = obj2;
				array = (SqlParameter[])obj3;
			}
			return SqlHelperParameterCache.CloneParameters(array);
		}

		// Token: 0x040053C5 RID: 21445
		private static Hashtable paramCache = Hashtable.Synchronized(new Hashtable());
	}
}
