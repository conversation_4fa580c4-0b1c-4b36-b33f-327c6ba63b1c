﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D6C RID: 3436
	public class PE1190 : BasePetEffect
	{
		// Token: 0x06007AB3 RID: 31411 RVA: 0x0028FEF4 File Offset: 0x0028E0F4
		public PE1190(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1190, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AB4 RID: 31412 RVA: 0x0028FF74 File Offset: 0x0028E174
		public override bool Start(Living living)
		{
			PE1190 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1190) as PE1190;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AB5 RID: 31413 RVA: 0x0002E9C8 File Offset: 0x0002CBC8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007AB6 RID: 31414 RVA: 0x0028FFD4 File Offset: 0x0028E1D4
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = living.PetEffects.BonusAgility == 0;
			if (flag)
			{
				this.m_added = 300;
				living.Agility += (double)this.m_added;
				living.PetEffects.BonusAgility += this.m_added;
				living.Game.SendPetBuff(living, base.Info, true);
			}
		}

		// Token: 0x06007AB7 RID: 31415 RVA: 0x0002E9DE File Offset: 0x0002CBDE
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x04004834 RID: 18484
		private int m_type = 0;

		// Token: 0x04004835 RID: 18485
		private int m_count = 0;

		// Token: 0x04004836 RID: 18486
		private int m_probability = 0;

		// Token: 0x04004837 RID: 18487
		private int m_delay = 0;

		// Token: 0x04004838 RID: 18488
		private int m_coldDown = 0;

		// Token: 0x04004839 RID: 18489
		private int m_currentId;

		// Token: 0x0400483A RID: 18490
		private int m_added = 0;
	}
}
