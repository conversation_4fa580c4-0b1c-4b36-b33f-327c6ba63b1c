﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E91 RID: 3729
	public class CE1197 : BasePetEffect
	{
		// Token: 0x06008100 RID: 33024 RVA: 0x002AB370 File Offset: 0x002A9570
		public CE1197(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1197, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008101 RID: 33025 RVA: 0x002AB3F0 File Offset: 0x002A95F0
		public override bool Start(Living living)
		{
			CE1197 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1197) as CE1197;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008102 RID: 33026 RVA: 0x002AB450 File Offset: 0x002A9650
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 150;
				player.Attack += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008103 RID: 33027 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008104 RID: 33028 RVA: 0x002AB4B4 File Offset: 0x002A96B4
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008105 RID: 33029 RVA: 0x000327A5 File Offset: 0x000309A5
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Attack -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005034 RID: 20532
		private int m_type = 0;

		// Token: 0x04005035 RID: 20533
		private int m_count = 0;

		// Token: 0x04005036 RID: 20534
		private int m_probability = 0;

		// Token: 0x04005037 RID: 20535
		private int m_delay = 0;

		// Token: 0x04005038 RID: 20536
		private int m_coldDown = 0;

		// Token: 0x04005039 RID: 20537
		private int m_currentId;

		// Token: 0x0400503A RID: 20538
		private int m_added = 0;
	}
}
