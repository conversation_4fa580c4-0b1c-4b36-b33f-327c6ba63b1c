﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E3F RID: 3647
	public class PE3180 : BasePetEffect
	{
		// Token: 0x06007F06 RID: 32518 RVA: 0x002A364C File Offset: 0x002A184C
		public PE3180(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE3180, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F07 RID: 32519 RVA: 0x002A36C8 File Offset: 0x002A18C8
		public override bool Start(Living living)
		{
			PE3180 pe = living.PetEffectList.GetOfType(ePetEffectType.PE3180) as PE3180;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F08 RID: 32520 RVA: 0x000315AC File Offset: 0x0002F7AC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
		}

		// Token: 0x06007F09 RID: 32521 RVA: 0x000315D5 File Offset: 0x0002F7D5
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
		}

		// Token: 0x06007F0A RID: 32522 RVA: 0x002A3724 File Offset: 0x002A1924
		private void Player_BeginSelfTurn(Living player)
		{
			bool flag = player.PetEffects.ReduceCritical == 0;
			if (flag)
			{
				player.PetEffects.ReduceCritical = 15;
				player.Game.SendPetBuff(player, base.Info, true);
			}
		}

		// Token: 0x06007F0B RID: 32523 RVA: 0x002A3768 File Offset: 0x002A1968
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = criticalAmount > 0;
			if (flag)
			{
				criticalAmount -= criticalAmount * 15 / 100;
			}
		}

		// Token: 0x04004DF4 RID: 19956
		private int m_type = 0;

		// Token: 0x04004DF5 RID: 19957
		private int m_count = 0;

		// Token: 0x04004DF6 RID: 19958
		private int m_probability = 0;

		// Token: 0x04004DF7 RID: 19959
		private int m_delay = 0;

		// Token: 0x04004DF8 RID: 19960
		private int m_coldDown = 0;

		// Token: 0x04004DF9 RID: 19961
		private int m_currentId;

		// Token: 0x04004DFA RID: 19962
		private int m_added = 0;
	}
}
