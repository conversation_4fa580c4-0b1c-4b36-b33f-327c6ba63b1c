﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DF8 RID: 3576
	public class AE1198 : BasePetEffect
	{
		// Token: 0x06007D88 RID: 32136 RVA: 0x0029C664 File Offset: 0x0029A864
		public AE1198(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1198, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D89 RID: 32137 RVA: 0x0029C6E4 File Offset: 0x0029A8E4
		public override bool Start(Living living)
		{
			AE1198 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1198) as AE1198;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D8A RID: 32138 RVA: 0x000305E1 File Offset: 0x0002E7E1
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D8B RID: 32139 RVA: 0x000305F7 File Offset: 0x0002E7F7
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D8C RID: 32140 RVA: 0x0029C744 File Offset: 0x0029A944
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1198(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004C00 RID: 19456
		private int m_type = 0;

		// Token: 0x04004C01 RID: 19457
		private int m_count = 0;

		// Token: 0x04004C02 RID: 19458
		private int m_probability = 0;

		// Token: 0x04004C03 RID: 19459
		private int m_delay = 0;

		// Token: 0x04004C04 RID: 19460
		private int m_coldDown = 0;

		// Token: 0x04004C05 RID: 19461
		private int m_currentId;

		// Token: 0x04004C06 RID: 19462
		private int m_added = 0;
	}
}
