﻿using System;
using System.Collections.Generic;
using System.Drawing;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F5A RID: 3930
	public class LivingMoveToAction : BaseAction
	{
		// Token: 0x0600850C RID: 34060 RVA: 0x002B9A28 File Offset: 0x002B7C28
		public LivingMoveToAction(Living living, List<Point> path, string action, int delay, int speed, string sAction, LivingCallBack callback, int delayCallback)
			: base(delay, 0)
		{
			this.m_living = living;
			this.m_path = path;
			this.m_action = action;
			this.m_saction = sAction;
			this.m_isSent = false;
			this.m_index = 0;
			this.m_callback = callback;
			this.m_speed = speed;
			this.m_delayCallback = delayCallback;
		}

		// Token: 0x0600850D RID: 34061 RVA: 0x002B9A84 File Offset: 0x002B7C84
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			bool flag = !this.m_isSent;
			if (flag)
			{
				this.m_isSent = true;
				game.SendLivingMoveTo(this.m_living, this.m_living.X, this.m_living.Y, this.m_path[this.m_path.Count - 1].X, this.m_path[this.m_path.Count - 1].Y, this.m_action, this.m_speed, this.m_saction);
			}
			this.m_index++;
			bool flag2 = this.m_index >= this.m_path.Count;
			if (flag2)
			{
				bool flag3 = this.m_path[this.m_index - 1].X > this.m_living.X;
				if (flag3)
				{
					this.m_living.Direction = 1;
				}
				else
				{
					this.m_living.Direction = -1;
				}
				this.m_living.SetXY(this.m_path[this.m_index - 1].X, this.m_path[this.m_index - 1].Y);
				bool flag4 = this.m_callback != null;
				if (flag4)
				{
					this.m_living.CallFuction(this.m_callback, this.m_delayCallback);
				}
				base.Finish(tick);
			}
		}

		// Token: 0x04005303 RID: 21251
		private Living m_living;

		// Token: 0x04005304 RID: 21252
		private List<Point> m_path;

		// Token: 0x04005305 RID: 21253
		private string m_action;

		// Token: 0x04005306 RID: 21254
		private bool m_isSent;

		// Token: 0x04005307 RID: 21255
		private string m_saction;

		// Token: 0x04005308 RID: 21256
		private int m_index;

		// Token: 0x04005309 RID: 21257
		private int m_speed;

		// Token: 0x0400530A RID: 21258
		private LivingCallBack m_callback;

		// Token: 0x0400530B RID: 21259
		private int m_delayCallback;
	}
}
