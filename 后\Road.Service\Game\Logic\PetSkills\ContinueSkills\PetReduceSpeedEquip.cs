﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D16 RID: 3350
	public class PetReduceSpeedEquip : AbstractPetEffect
	{
		// Token: 0x060078CF RID: 30927 RVA: 0x00287F1C File Offset: 0x0028611C
		public PetReduceSpeedEquip(int count, string elementID)
			: base(ePetEffectType.PetReduceSpeedEquip, elementID)
		{
			this.m_count = count;
			bool flag = elementID == "1732" || elementID == "1838";
			if (flag)
			{
				this.m_value = 50;
				this.m_percent = true;
			}
		}

		// Token: 0x060078D0 RID: 30928 RVA: 0x00287F70 File Offset: 0x00286170
		public override bool Start(Living living)
		{
			PetReduceSpeedEquip petReduceSpeedEquip = living.PetEffectList.GetOfType(ePetEffectType.PetReduceSpeedEquip) as PetReduceSpeedEquip;
			bool flag = petReduceSpeedEquip != null;
			bool flag2;
			if (flag)
			{
				petReduceSpeedEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078D1 RID: 30929 RVA: 0x00287FB8 File Offset: 0x002861B8
		public override void OnAttached(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				bool percent = this.m_percent;
				if (percent)
				{
					this.m_added = (int)(living.Speed * (double)this.m_value / 100.0);
				}
				else
				{
					this.m_added = this.m_value;
				}
				living.Speed -= (double)this.m_added;
				living.Game.UpdateMaxBlood(living);
			}
			living.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x060078D2 RID: 30930 RVA: 0x0002CED0 File Offset: 0x0002B0D0
		public override void OnRemoved(Living living)
		{
			living.Speed += (double)this.m_added;
			this.m_added = 0;
			living.Game.UpdateMaxBlood(living);
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x060078D3 RID: 30931 RVA: 0x00288044 File Offset: 0x00286244
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
		}

		// Token: 0x040046CF RID: 18127
		private int m_count;

		// Token: 0x040046D0 RID: 18128
		private int m_value;

		// Token: 0x040046D1 RID: 18129
		private bool m_percent;

		// Token: 0x040046D2 RID: 18130
		private int m_added;
	}
}
