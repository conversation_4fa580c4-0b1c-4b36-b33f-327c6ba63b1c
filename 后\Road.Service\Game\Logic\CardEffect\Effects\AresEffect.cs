﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F1F RID: 3871
	public class AresEffect : BaseCardEffect
	{
		// Token: 0x060083F2 RID: 33778 RVA: 0x002B61A8 File Offset: 0x002B43A8
		public AresEffect(int index, CardBuffInfo info)
			: base(eCardEffectType.AresDeck, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x060083F3 RID: 33779 RVA: 0x002B6218 File Offset: 0x002B4418
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.AresDeck) is AresEffect;
			return flag || base.Start(living);
		}

		// Token: 0x060083F4 RID: 33780 RVA: 0x000348CA File Offset: 0x00032ACA
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x060083F5 RID: 33781 RVA: 0x000348E0 File Offset: 0x00032AE0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x060083F6 RID: 33782 RVA: 0x002B6250 File Offset: 0x002B4450
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.AddMaxBlood(-this.m_value);
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVPGame && (player.Game as PVPGame).IsMatchOrFreedom();
			if (flag2)
			{
				player.AddMaxBlood(this.m_value);
				this.m_added = this.m_value;
			}
		}

		// Token: 0x04005253 RID: 21075
		private int m_indexValue = 0;

		// Token: 0x04005254 RID: 21076
		private int m_value = 0;

		// Token: 0x04005255 RID: 21077
		private int m_added = 0;
	}
}
