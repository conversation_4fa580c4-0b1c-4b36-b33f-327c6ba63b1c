﻿using System;
using System.CodeDom.Compiler;
using System.ServiceModel;

namespace Bussiness.WebLogin
{
	// Token: 0x02000FBE RID: 4030
	[GeneratedCode("System.ServiceModel", "4.0.0.0")]
	[ServiceContract(Namespace = "dandantang", ConfigurationName = "WebLogin.PassPortSoap")]
	public interface PassPortSoap
	{
		// Token: 0x06008A50 RID: 35408
		[OperationContract(Action = "dandantang/ChenckValidate", ReplyAction = "*")]
		[XmlSerializerFormat]
		string ChenckValidate(string username, string password);

		// Token: 0x06008A51 RID: 35409
		[OperationContract(Action = "dandantang/Get_UserSex", ReplyAction = "*")]
		[XmlSerializerFormat]
		Get_UserSexResponse Get_UserSex(Get_UserSexRequest request);
	}
}
