﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CF9 RID: 3321
	public class PetStealthPosture_HidePassive : BasePetEffect
	{
		// Token: 0x0600783B RID: 30779 RVA: 0x00284DCC File Offset: 0x00282FCC
		public PetStealthPosture_HidePassive(int probability, string elementID)
			: base(ePetEffectType.PetStealthPosture_HidePassive, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			if (!(elementID == "1558") && !(elementID == "1614"))
			{
				if (!(elementID == "1594") && !(elementID == "1615"))
				{
					if (elementID == "1559")
					{
						this.m_value = 40;
					}
				}
				else
				{
					this.m_value = 30;
				}
			}
			else
			{
				this.m_value = 40;
			}
		}

		// Token: 0x0600783C RID: 30780 RVA: 0x00284E68 File Offset: 0x00283068
		public override bool Start(Living living)
		{
			PetStealthPosture_HidePassive petStealthPosture_HidePassive = living.PetEffectList.GetOfType(ePetEffectType.PetStealthPosture_HidePassive) as PetStealthPosture_HidePassive;
			bool flag = petStealthPosture_HidePassive != null;
			bool flag2;
			if (flag)
			{
				petStealthPosture_HidePassive.m_probability = ((this.m_probability > petStealthPosture_HidePassive.m_probability) ? this.m_probability : petStealthPosture_HidePassive.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600783D RID: 30781 RVA: 0x0002C820 File Offset: 0x0002AA20
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x0600783E RID: 30782 RVA: 0x0002C836 File Offset: 0x0002AA36
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x0600783F RID: 30783 RVA: 0x00284EC8 File Offset: 0x002830C8
		private void player_BeginSelfTurn(Living living)
		{
			bool flag = (double)living.Blood / (double)living.MaxBlood * 100.0 >= (double)this.m_value;
			if (flag)
			{
				living.IsHide = false;
				living.Game.sendShowPicSkil(living, base.Info, true);
			}
			else
			{
				(living as Player).IsHide = true;
				this.IsTrigger = true;
			}
		}

		// Token: 0x04004679 RID: 18041
		private int m_probability = 0;

		// Token: 0x0400467A RID: 18042
		private int m_value;

		// Token: 0x0400467B RID: 18043
		private double m_value1;

		// Token: 0x0400467C RID: 18044
		private double m_added;
	}
}
