﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element
{
	// Token: 0x02000D55 RID: 3413
	public class PetPassiveSkills : BasePetEffect
	{
		// Token: 0x06007A38 RID: 31288 RVA: 0x0028E018 File Offset: 0x0028C218
		public PetPassiveSkills(int count, int probability, int type, int skillId, int delay, string elementID, int add)
			: base(ePetEffectType.PetPassiveSkills, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
			this.m_added = add;
		}

		// Token: 0x06007A39 RID: 31289 RVA: 0x0028E0A0 File Offset: 0x0028C2A0
		public override bool Start(Living living)
		{
			PetPassiveSkills petPassiveSkills = living.PetEffectList.GetOfType(ePetEffectType.PetPassiveSkills) as PetPassiveSkills;
			bool flag = petPassiveSkills != null;
			bool flag2;
			if (flag)
			{
				petPassiveSkills.m_probability = ((this.m_probability > petPassiveSkills.m_probability) ? this.m_probability : petPassiveSkills.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A3A RID: 31290 RVA: 0x0002E47B File Offset: 0x0002C67B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerShoot += this.ChangeProperty;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007A3B RID: 31291 RVA: 0x0002E4A4 File Offset: 0x0002C6A4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerShoot -= this.ChangeProperty;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007A3C RID: 31292 RVA: 0x0002E4CD File Offset: 0x0002C6CD
		private void ChangeProperty(Player player)
		{
			player.PetEffects.DamagePercent += this.m_added;
		}

		// Token: 0x06007A3D RID: 31293 RVA: 0x0002E4E9 File Offset: 0x0002C6E9
		private void player_AfterPlayerShooted(Player player)
		{
			player.PetEffects.DamagePercent -= this.m_added;
		}

		// Token: 0x04004793 RID: 18323
		private int m_type = 0;

		// Token: 0x04004794 RID: 18324
		private int m_count = 0;

		// Token: 0x04004795 RID: 18325
		private int m_probability = 0;

		// Token: 0x04004796 RID: 18326
		private int m_delay = 0;

		// Token: 0x04004797 RID: 18327
		private int m_coldDown = 0;

		// Token: 0x04004798 RID: 18328
		private int m_currentId;

		// Token: 0x04004799 RID: 18329
		private int m_added = 0;
	}
}
