﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F2D RID: 3885
	public class GuluSportsMeeting4Effect : BaseCardEffect
	{
		// Token: 0x06008445 RID: 33861 RVA: 0x002B7588 File Offset: 0x002B5788
		public GuluSportsMeeting4Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.GuluSportsMeeting4, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008446 RID: 33862 RVA: 0x002B75F8 File Offset: 0x002B57F8
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.GuluSportsMeeting4) is GuluSportsMeeting4Effect;
			return flag || base.Start(living);
		}

		// Token: 0x06008447 RID: 33863 RVA: 0x00034D4E File Offset: 0x00032F4E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x06008448 RID: 33864 RVA: 0x00034D64 File Offset: 0x00032F64
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x06008449 RID: 33865 RVA: 0x002B7630 File Offset: 0x002B5830
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.Attack -= (double)this.m_added;
				player.Agility -= (double)this.m_added;
				player.Lucky -= (double)this.m_added;
				player.Defence -= (double)this.m_added;
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 6;
			if (flag2)
			{
				this.m_added = this.m_value;
				player.Attack += (double)this.m_added;
				player.Agility += (double)this.m_added;
				player.Lucky += (double)this.m_added;
				player.Defence += (double)this.m_added;
				player.Game.SendMessage(player.PlayerDetail, "您激活了啵咕运动会4件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活啵咕运动会4件套卡.", 3);
			}
		}

		// Token: 0x0400527B RID: 21115
		private int m_indexValue = 0;

		// Token: 0x0400527C RID: 21116
		private int m_value = 0;

		// Token: 0x0400527D RID: 21117
		private int m_added = 0;
	}
}
