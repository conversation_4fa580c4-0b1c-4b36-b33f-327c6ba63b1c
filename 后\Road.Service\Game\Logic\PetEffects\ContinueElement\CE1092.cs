﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E62 RID: 3682
	public class CE1092 : BasePetEffect
	{
		// Token: 0x06007FD8 RID: 32728 RVA: 0x002A6D50 File Offset: 0x002A4F50
		public CE1092(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1092, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FD9 RID: 32729 RVA: 0x002A6DD0 File Offset: 0x002A4FD0
		public override bool Start(Living living)
		{
			CE1092 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1092) as CE1092;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FDA RID: 32730 RVA: 0x002A6E30 File Offset: 0x002A5030
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.Attack += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007FDB RID: 32731 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FDC RID: 32732 RVA: 0x002A6E90 File Offset: 0x002A5090
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007FDD RID: 32733 RVA: 0x002A6EC4 File Offset: 0x002A50C4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Attack -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004EE9 RID: 20201
		private int m_type = 0;

		// Token: 0x04004EEA RID: 20202
		private int m_count = 0;

		// Token: 0x04004EEB RID: 20203
		private int m_probability = 0;

		// Token: 0x04004EEC RID: 20204
		private int m_delay = 0;

		// Token: 0x04004EED RID: 20205
		private int m_coldDown = 0;

		// Token: 0x04004EEE RID: 20206
		private int m_currentId;

		// Token: 0x04004EEF RID: 20207
		private int m_added = 0;
	}
}
