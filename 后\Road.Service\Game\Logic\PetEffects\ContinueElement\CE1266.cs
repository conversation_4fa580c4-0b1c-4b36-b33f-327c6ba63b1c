﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EBA RID: 3770
	public class CE1266 : BasePetEffect
	{
		// Token: 0x0600820C RID: 33292 RVA: 0x002AF2A0 File Offset: 0x002AD4A0
		public CE1266(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1266, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600820D RID: 33293 RVA: 0x002AF320 File Offset: 0x002AD520
		public override bool Start(Living living)
		{
			CE1266 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1266) as CE1266;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600820E RID: 33294 RVA: 0x000331EC File Offset: 0x000313EC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600820F RID: 33295 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008210 RID: 33296 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_BeginSelfTurn(Living living)
		{
			this.Stop();
		}

		// Token: 0x06008211 RID: 33297 RVA: 0x00033215 File Offset: 0x00031415
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005153 RID: 20819
		private int m_type = 0;

		// Token: 0x04005154 RID: 20820
		private int m_count = 0;

		// Token: 0x04005155 RID: 20821
		private int m_probability = 0;

		// Token: 0x04005156 RID: 20822
		private int m_delay = 0;

		// Token: 0x04005157 RID: 20823
		private int m_coldDown = 0;

		// Token: 0x04005158 RID: 20824
		private int m_currentId;

		// Token: 0x04005159 RID: 20825
		private int m_added = 0;
	}
}
