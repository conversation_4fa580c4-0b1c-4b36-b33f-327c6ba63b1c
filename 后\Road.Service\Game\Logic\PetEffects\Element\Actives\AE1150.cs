﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DD8 RID: 3544
	public class AE1150 : BasePetEffect
	{
		// Token: 0x06007CE4 RID: 31972 RVA: 0x002995F4 File Offset: 0x002977F4
		public AE1150(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1150, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CE5 RID: 31973 RVA: 0x00299674 File Offset: 0x00297874
		public override bool Start(Living living)
		{
			AE1150 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1150) as AE1150;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CE6 RID: 31974 RVA: 0x0002FFDC File Offset: 0x0002E1DC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007CE7 RID: 31975 RVA: 0x002996D4 File Offset: 0x002978D4
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && player.Game is PVPGame;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007CE8 RID: 31976 RVA: 0x00299714 File Offset: 0x00297914
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				this.IsTrigger = false;
				target.Game.SendPetBuff(target, base.ElementInfo, true);
				target.AddPetEffect(new CE1150(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString(), living), 0);
			}
		}

		// Token: 0x06007CE9 RID: 31977 RVA: 0x00030005 File Offset: 0x0002E205
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004B20 RID: 19232
		private int m_type = 0;

		// Token: 0x04004B21 RID: 19233
		private int m_count = 0;

		// Token: 0x04004B22 RID: 19234
		private int m_probability = 0;

		// Token: 0x04004B23 RID: 19235
		private int m_delay = 0;

		// Token: 0x04004B24 RID: 19236
		private int m_coldDown = 0;

		// Token: 0x04004B25 RID: 19237
		private int m_currentId;

		// Token: 0x04004B26 RID: 19238
		private int m_added = 0;
	}
}
