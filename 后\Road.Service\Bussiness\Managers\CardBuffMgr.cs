﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FC9 RID: 4041
	public class CardBuffMgr
	{
		// Token: 0x06008A82 RID: 35458 RVA: 0x002F73D8 File Offset: 0x002F55D8
		public static bool ReLoad()
		{
			try
			{
				CardGroupInfo[] array = CardBuffMgr.LoadCardGroupDb();
				Dictionary<int, List<CardGroupInfo>> dictionary = CardBuffMgr.LoadCardGroups(array);
				bool flag = array.Length != 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, List<CardGroupInfo>>>(ref CardBuffMgr.m_cardGroups, dictionary);
				}
				Dictionary<int, CardInfo> dictionary2 = CardBuffMgr.LoadFromDatabase();
				bool flag2 = dictionary2.Values.Count > 0;
				if (flag2)
				{
					Interlocked.Exchange<Dictionary<int, CardInfo>>(ref CardBuffMgr.m_cards, dictionary2);
				}
				CardBuffInfo[] array2 = CardBuffMgr.LoadCardBuffDb();
				Dictionary<int, List<CardBuffInfo>> dictionary3 = CardBuffMgr.LoadCardBuffs(array2);
				bool flag3 = array2.Length != 0;
				if (flag3)
				{
					Interlocked.Exchange<Dictionary<int, List<CardBuffInfo>>>(ref CardBuffMgr.m_cardBuffs, dictionary3);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = CardBuffMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					CardBuffMgr.log.Error("ReLoad CardGroup", ex);
				}
				return false;
			}
			return true;
		}

		// Token: 0x06008A83 RID: 35459 RVA: 0x002F74A4 File Offset: 0x002F56A4
		public static bool Init()
		{
			return CardBuffMgr.ReLoad();
		}

		// Token: 0x06008A84 RID: 35460 RVA: 0x002F74BC File Offset: 0x002F56BC
		public static CardBuffInfo[] LoadCardBuffDb()
		{
			CardBuffInfo[] allCardBuff;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				allCardBuff = produceBussiness.GetAllCardBuff();
			}
			return allCardBuff;
		}

		// Token: 0x06008A85 RID: 35461 RVA: 0x002F74F8 File Offset: 0x002F56F8
		public static Dictionary<int, List<CardBuffInfo>> LoadCardBuffs(CardBuffInfo[] cardBuffs)
		{
			Dictionary<int, List<CardBuffInfo>> dictionary = new Dictionary<int, List<CardBuffInfo>>();
			for (int i = 0; i < cardBuffs.Length; i++)
			{
				CardBuffInfo info = cardBuffs[i];
				bool flag = !dictionary.Keys.Contains(info.CardID);
				if (flag)
				{
					IEnumerable<CardBuffInfo> enumerable = cardBuffs.Where((CardBuffInfo s) => s.CardID == info.CardID);
					dictionary.Add(info.CardID, enumerable.ToList<CardBuffInfo>());
				}
			}
			return dictionary;
		}

		// Token: 0x06008A86 RID: 35462 RVA: 0x002F7584 File Offset: 0x002F5784
		public static List<CardBuffInfo> FindCardBuffs(int id)
		{
			bool flag = CardBuffMgr.m_cardBuffs.ContainsKey(id);
			List<CardBuffInfo> list2;
			if (flag)
			{
				List<CardBuffInfo> list = new List<CardBuffInfo>();
				foreach (CardBuffInfo cardBuffInfo in CardBuffMgr.m_cardBuffs[id])
				{
					bool flag2 = cardBuffInfo.Condition > 0;
					if (flag2)
					{
						list.Add(cardBuffInfo);
					}
				}
				list2 = list.OrderByDescending((CardBuffInfo a) => a.Condition).ToList<CardBuffInfo>();
			}
			else
			{
				list2 = null;
			}
			return list2;
		}

		// Token: 0x06008A87 RID: 35463 RVA: 0x002F7644 File Offset: 0x002F5844
		private static Dictionary<int, CardInfo> LoadFromDatabase()
		{
			Dictionary<int, CardInfo> dictionary = new Dictionary<int, CardInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				CardInfo[] allCard = produceBussiness.GetAllCard();
				CardInfo[] array = allCard;
				CardInfo[] array2 = array;
				CardInfo[] array3 = array2;
				foreach (CardInfo cardInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(cardInfo.ID);
					if (flag)
					{
						dictionary.Add(cardInfo.ID, cardInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008A88 RID: 35464 RVA: 0x002F76E0 File Offset: 0x002F58E0
		public static Dictionary<int, List<CardGroupInfo>> GetAllCard()
		{
			return CardBuffMgr.m_cardGroups;
		}

		// Token: 0x06008A89 RID: 35465 RVA: 0x002F76F8 File Offset: 0x002F58F8
		public static CardInfo FindCard(int id)
		{
			bool flag = CardBuffMgr.m_cards.Count == 0;
			if (flag)
			{
				CardBuffMgr.Init();
			}
			bool flag2 = CardBuffMgr.m_cards.ContainsKey(id);
			CardInfo cardInfo;
			if (flag2)
			{
				cardInfo = CardBuffMgr.m_cards[id];
			}
			else
			{
				cardInfo = null;
			}
			return cardInfo;
		}

		// Token: 0x06008A8A RID: 35466 RVA: 0x002F7744 File Offset: 0x002F5944
		public static CardGroupInfo[] LoadCardGroupDb()
		{
			CardGroupInfo[] allCardGroup;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				allCardGroup = produceBussiness.GetAllCardGroup();
			}
			return allCardGroup;
		}

		// Token: 0x06008A8B RID: 35467 RVA: 0x002F7780 File Offset: 0x002F5980
		public static Dictionary<int, List<CardGroupInfo>> LoadCardGroups(CardGroupInfo[] cardGroups)
		{
			Dictionary<int, List<CardGroupInfo>> dictionary = new Dictionary<int, List<CardGroupInfo>>();
			for (int i = 0; i < cardGroups.Length; i++)
			{
				CardGroupInfo info = cardGroups[i];
				bool flag = !dictionary.Keys.Contains(info.CardID);
				if (flag)
				{
					IEnumerable<CardGroupInfo> enumerable = cardGroups.Where((CardGroupInfo s) => s.CardID == info.CardID);
					dictionary.Add(info.CardID, enumerable.ToList<CardGroupInfo>());
				}
			}
			return dictionary;
		}

		// Token: 0x06008A8C RID: 35468 RVA: 0x002F780C File Offset: 0x002F5A0C
		public static List<CardGroupInfo> FindCardGroups(int id)
		{
			bool flag = CardBuffMgr.m_cardGroups.ContainsKey(id);
			List<CardGroupInfo> list;
			if (flag)
			{
				list = CardBuffMgr.m_cardGroups[id];
			}
			else
			{
				list = null;
			}
			return list;
		}

		// Token: 0x040054E4 RID: 21732
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040054E5 RID: 21733
		private static Dictionary<int, CardInfo> m_cards = new Dictionary<int, CardInfo>();

		// Token: 0x040054E6 RID: 21734
		private static Dictionary<int, List<CardGroupInfo>> m_cardGroups = new Dictionary<int, List<CardGroupInfo>>();

		// Token: 0x040054E7 RID: 21735
		private static Dictionary<int, List<CardBuffInfo>> m_cardBuffs = new Dictionary<int, List<CardBuffInfo>>();
	}
}
