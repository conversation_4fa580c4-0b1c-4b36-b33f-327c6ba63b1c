﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DD5 RID: 3541
	public class AE1134 : BasePetEffect
	{
		// Token: 0x06007CD2 RID: 31954 RVA: 0x002991B8 File Offset: 0x002973B8
		public AE1134(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1134, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CD3 RID: 31955 RVA: 0x00299238 File Offset: 0x00297438
		public override bool Start(Living living)
		{
			AE1134 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1134) as AE1134;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CD4 RID: 31956 RVA: 0x0002FEF9 File Offset: 0x0002E0F9
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007CD5 RID: 31957 RVA: 0x0002FF22 File Offset: 0x0002E122
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007CD6 RID: 31958 RVA: 0x00299298 File Offset: 0x00297498
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 180;
				player.CurrentDamagePlus += (float)(this.m_added / 100);
			}
		}

		// Token: 0x06007CD7 RID: 31959 RVA: 0x002992E4 File Offset: 0x002974E4
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_added / 100);
				this.m_added = 0;
			}
		}

		// Token: 0x04004B0B RID: 19211
		private int m_type = 0;

		// Token: 0x04004B0C RID: 19212
		private int m_count = 0;

		// Token: 0x04004B0D RID: 19213
		private int m_probability = 0;

		// Token: 0x04004B0E RID: 19214
		private int m_delay = 0;

		// Token: 0x04004B0F RID: 19215
		private int m_coldDown = 0;

		// Token: 0x04004B10 RID: 19216
		private int m_currentId;

		// Token: 0x04004B11 RID: 19217
		private int m_added = 0;
	}
}
