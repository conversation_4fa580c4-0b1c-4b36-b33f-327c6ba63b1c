﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using Bussiness;
using Game.Base.Packets;
using Game.Logic.Actions;
using Game.Logic.CardEffects;
using Game.Logic.Effects;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Actions;
using Game.Logic.Phy.Maps;
using Game.Logic.Phy.Maths;
using SqlDataProvider.Data;

namespace Game.Logic.Phy.Object
{
	// Token: 0x02000CD3 RID: 3283
	public class Living : Physics
	{
		// Token: 0x1700140F RID: 5135
		// (get) Token: 0x06007538 RID: 30008 RVA: 0x0002B6E8 File Offset: 0x000298E8
		// (set) Token: 0x06007539 RID: 30009 RVA: 0x0002B6F0 File Offset: 0x000298F0
		public bool CanAddBlood { get; set; }

		// Token: 0x17001410 RID: 5136
		// (get) Token: 0x0600753A RID: 30010 RVA: 0x0002B6F9 File Offset: 0x000298F9
		// (set) Token: 0x0600753B RID: 30011 RVA: 0x0002B701 File Offset: 0x00029901
		public NpcInfo NpcInfo { get; set; }

		// Token: 0x17001411 RID: 5137
		// (get) Token: 0x0600753C RID: 30012 RVA: 0x0002B70A File Offset: 0x0002990A
		// (set) Token: 0x0600753D RID: 30013 RVA: 0x0002B712 File Offset: 0x00029912
		public bool BeingCrited { get; set; }

		// Token: 0x17001412 RID: 5138
		// (get) Token: 0x0600753E RID: 30014 RVA: 0x0002B71B File Offset: 0x0002991B
		// (set) Token: 0x0600753F RID: 30015 RVA: 0x0002B723 File Offset: 0x00029923
		public int FireX { get; set; }

		// Token: 0x17001413 RID: 5139
		// (get) Token: 0x06007540 RID: 30016 RVA: 0x0002B72C File Offset: 0x0002992C
		// (set) Token: 0x06007541 RID: 30017 RVA: 0x0002B734 File Offset: 0x00029934
		public int FireY { get; set; }

		// Token: 0x17001414 RID: 5140
		// (get) Token: 0x06007542 RID: 30018 RVA: 0x0002B73D File Offset: 0x0002993D
		// (set) Token: 0x06007543 RID: 30019 RVA: 0x0002B745 File Offset: 0x00029945
		public double OldAttack { get; set; }

		// Token: 0x17001415 RID: 5141
		// (get) Token: 0x06007544 RID: 30020 RVA: 0x0002B74E File Offset: 0x0002994E
		// (set) Token: 0x06007545 RID: 30021 RVA: 0x0002B756 File Offset: 0x00029956
		public double OldDefence { get; set; }

		// Token: 0x17001416 RID: 5142
		// (get) Token: 0x06007546 RID: 30022 RVA: 0x0002B75F File Offset: 0x0002995F
		// (set) Token: 0x06007547 RID: 30023 RVA: 0x0002B767 File Offset: 0x00029967
		public double OldAgility { get; set; }

		// Token: 0x17001417 RID: 5143
		// (get) Token: 0x06007548 RID: 30024 RVA: 0x0002B770 File Offset: 0x00029970
		// (set) Token: 0x06007549 RID: 30025 RVA: 0x0002B778 File Offset: 0x00029978
		public double OldLucky { get; set; }

		// Token: 0x17001418 RID: 5144
		// (get) Token: 0x0600754A RID: 30026 RVA: 0x0002B781 File Offset: 0x00029981
		// (set) Token: 0x0600754B RID: 30027 RVA: 0x0002B789 File Offset: 0x00029989
		public double OldBaseDamage { get; set; }

		// Token: 0x17001419 RID: 5145
		// (get) Token: 0x0600754C RID: 30028 RVA: 0x0002B792 File Offset: 0x00029992
		// (set) Token: 0x0600754D RID: 30029 RVA: 0x0002B79A File Offset: 0x0002999A
		public double OldBaseGuard { get; set; }

		// Token: 0x1700141A RID: 5146
		// (get) Token: 0x0600754E RID: 30030 RVA: 0x0002B7A3 File Offset: 0x000299A3
		// (set) Token: 0x0600754F RID: 30031 RVA: 0x0002B7AB File Offset: 0x000299AB
		public int PlaceProp { get; set; }

		// Token: 0x1700141B RID: 5147
		// (get) Token: 0x06007550 RID: 30032 RVA: 0x0002B7B4 File Offset: 0x000299B4
		public BaseGame Game
		{
			get
			{
				return this.m_game;
			}
		}

		// Token: 0x1700141C RID: 5148
		// (get) Token: 0x06007551 RID: 30033 RVA: 0x0002B7BC File Offset: 0x000299BC
		public string Name
		{
			get
			{
				return this.m_name;
			}
		}

		// Token: 0x1700141D RID: 5149
		// (get) Token: 0x06007552 RID: 30034 RVA: 0x0002B7C4 File Offset: 0x000299C4
		public string ModelId
		{
			get
			{
				return this.m_modelId;
			}
		}

		// Token: 0x1700141E RID: 5150
		// (get) Token: 0x06007553 RID: 30035 RVA: 0x0002B7CC File Offset: 0x000299CC
		public int Team
		{
			get
			{
				return this.m_team;
			}
		}

		// Token: 0x1700141F RID: 5151
		// (get) Token: 0x06007554 RID: 30036 RVA: 0x0026EF34 File Offset: 0x0026D134
		// (set) Token: 0x06007555 RID: 30037 RVA: 0x0002B7D4 File Offset: 0x000299D4
		public bool SyncAtTime
		{
			get
			{
				return this.m_syncAtTime;
			}
			set
			{
				this.m_syncAtTime = value;
			}
		}

		// Token: 0x17001420 RID: 5152
		// (get) Token: 0x06007556 RID: 30038 RVA: 0x0026EF4C File Offset: 0x0026D14C
		// (set) Token: 0x06007557 RID: 30039 RVA: 0x0002B7DE File Offset: 0x000299DE
		public LivingConfig Config
		{
			get
			{
				return this.m_config;
			}
			set
			{
				this.m_config = value;
			}
		}

		// Token: 0x17001421 RID: 5153
		// (get) Token: 0x06007558 RID: 30040 RVA: 0x0026EF64 File Offset: 0x0026D164
		// (set) Token: 0x06007559 RID: 30041 RVA: 0x0002B7E8 File Offset: 0x000299E8
		public FightBufferInfo FightBuffers
		{
			get
			{
				return this.m_fightBufferInfo;
			}
			set
			{
				this.m_fightBufferInfo = value;
			}
		}

		// Token: 0x17001422 RID: 5154
		// (get) Token: 0x0600755A RID: 30042 RVA: 0x0026EF7C File Offset: 0x0026D17C
		// (set) Token: 0x0600755B RID: 30043 RVA: 0x0002B7F2 File Offset: 0x000299F2
		public PetEffectInfo PetEffects
		{
			get
			{
				return this.m_petEffects;
			}
			set
			{
				this.m_petEffects = value;
			}
		}

		// Token: 0x17001423 RID: 5155
		// (get) Token: 0x0600755C RID: 30044 RVA: 0x0026EF94 File Offset: 0x0026D194
		// (set) Token: 0x0600755D RID: 30045 RVA: 0x0002B7FC File Offset: 0x000299FC
		public bool VaneOpen
		{
			get
			{
				return this.m_vaneOpen;
			}
			set
			{
				this.m_vaneOpen = value;
			}
		}

		// Token: 0x17001424 RID: 5156
		// (get) Token: 0x0600755E RID: 30046 RVA: 0x0026EFAC File Offset: 0x0026D1AC
		// (set) Token: 0x0600755F RID: 30047 RVA: 0x0002B806 File Offset: 0x00029A06
		public string ActionStr
		{
			get
			{
				return this.m_action;
			}
			set
			{
				this.m_action = value;
			}
		}

		// Token: 0x17001425 RID: 5157
		// (get) Token: 0x06007560 RID: 30048 RVA: 0x0026EFC4 File Offset: 0x0026D1C4
		// (set) Token: 0x06007561 RID: 30049 RVA: 0x0002B810 File Offset: 0x00029A10
		public int SpecialSkillDelay
		{
			get
			{
				return this.m_specialSkillDelay;
			}
			set
			{
				this.m_specialSkillDelay = value;
			}
		}

		// Token: 0x17001426 RID: 5158
		// (get) Token: 0x06007562 RID: 30050 RVA: 0x0026EFDC File Offset: 0x0026D1DC
		// (set) Token: 0x06007563 RID: 30051 RVA: 0x0002B81A File Offset: 0x00029A1A
		public int Degree
		{
			get
			{
				return this.m_degree;
			}
			set
			{
				this.m_degree = value;
			}
		}

		// Token: 0x17001427 RID: 5159
		// (get) Token: 0x06007564 RID: 30052 RVA: 0x0026EFF4 File Offset: 0x0026D1F4
		// (set) Token: 0x06007565 RID: 30053 RVA: 0x0002B824 File Offset: 0x00029A24
		public string CaeateAction
		{
			get
			{
				return this.m_createAction;
			}
			set
			{
				this.m_createAction = value;
			}
		}

		// Token: 0x17001428 RID: 5160
		// (get) Token: 0x06007566 RID: 30054 RVA: 0x0026F00C File Offset: 0x0026D20C
		// (set) Token: 0x06007567 RID: 30055 RVA: 0x0002B82E File Offset: 0x00029A2E
		public int ChangeMaxBeatDis
		{
			get
			{
				return this.MaxBeatDis;
			}
			set
			{
				this.MaxBeatDis = value;
			}
		}

		// Token: 0x17001429 RID: 5161
		// (get) Token: 0x06007568 RID: 30056 RVA: 0x0026F024 File Offset: 0x0026D224
		// (set) Token: 0x06007569 RID: 30057 RVA: 0x0026F03C File Offset: 0x0026D23C
		public int Direction
		{
			get
			{
				return this.m_direction;
			}
			set
			{
				bool flag = this.m_direction != value;
				if (flag)
				{
					this.m_direction = value;
					this.ReSetRectWithDir();
					bool syncAtTime = this.m_syncAtTime;
					if (syncAtTime)
					{
						this.m_game.SendLivingUpdateDirection(this);
					}
				}
			}
		}

		// Token: 0x1700142A RID: 5162
		// (get) Token: 0x0600756A RID: 30058 RVA: 0x0026F084 File Offset: 0x0026D284
		// (set) Token: 0x0600756B RID: 30059 RVA: 0x0002B838 File Offset: 0x00029A38
		public eLivingType Type
		{
			get
			{
				return this.m_type;
			}
			set
			{
				this.m_type = value;
			}
		}

		// Token: 0x1700142B RID: 5163
		// (get) Token: 0x0600756C RID: 30060 RVA: 0x0002B842 File Offset: 0x00029A42
		// (set) Token: 0x0600756D RID: 30061 RVA: 0x0002B84A File Offset: 0x00029A4A
		public bool IsSay { get; set; }

		// Token: 0x1700142C RID: 5164
		// (get) Token: 0x0600756E RID: 30062 RVA: 0x0002B853 File Offset: 0x00029A53
		public EffectList EffectList
		{
			get
			{
				return this.m_effectList;
			}
		}

		// Token: 0x1700142D RID: 5165
		// (get) Token: 0x0600756F RID: 30063 RVA: 0x0002B85B File Offset: 0x00029A5B
		public CardEffectList CardEffectList
		{
			get
			{
				return this.m_cardEffectList;
			}
		}

		// Token: 0x1700142E RID: 5166
		// (get) Token: 0x06007570 RID: 30064 RVA: 0x0002B863 File Offset: 0x00029A63
		public PetEffectList PetEffectList
		{
			get
			{
				return this.m_petEffectList;
			}
		}

		// Token: 0x1700142F RID: 5167
		// (get) Token: 0x06007571 RID: 30065 RVA: 0x0002B86B File Offset: 0x00029A6B
		public bool IsAttacking
		{
			get
			{
				return this.m_isAttacking;
			}
		}

		// Token: 0x17001430 RID: 5168
		// (get) Token: 0x06007572 RID: 30066 RVA: 0x0026F09C File Offset: 0x0026D29C
		// (set) Token: 0x06007573 RID: 30067 RVA: 0x0026F0B4 File Offset: 0x0026D2B4
		public bool IsFrost
		{
			get
			{
				return this.m_isFrost;
			}
			set
			{
				bool flag = this.m_isFrost != value;
				if (flag)
				{
					this.m_isFrost = value;
					bool syncAtTime = this.m_syncAtTime;
					if (syncAtTime)
					{
						this.m_game.SendGameUpdateFrozenState(this);
					}
				}
			}
		}

		// Token: 0x17001431 RID: 5169
		// (get) Token: 0x06007574 RID: 30068 RVA: 0x0026F0F4 File Offset: 0x0026D2F4
		// (set) Token: 0x06007575 RID: 30069 RVA: 0x0026F10C File Offset: 0x0026D30C
		public bool IsNoHole
		{
			get
			{
				return this.m_isNoHole;
			}
			set
			{
				bool flag = this.m_isNoHole != value;
				if (flag)
				{
					this.m_isNoHole = value;
					bool syncAtTime = this.m_syncAtTime;
					if (syncAtTime)
					{
						this.m_game.SendGameUpdateNoHoleState(this);
					}
				}
			}
		}

		// Token: 0x17001432 RID: 5170
		// (get) Token: 0x06007576 RID: 30070 RVA: 0x0026F14C File Offset: 0x0026D34C
		// (set) Token: 0x06007577 RID: 30071 RVA: 0x0026F164 File Offset: 0x0026D364
		public bool IsHide
		{
			get
			{
				return this.m_isHide;
			}
			set
			{
				bool flag = this.m_isHide != value;
				if (flag)
				{
					this.m_isHide = value;
					bool syncAtTime = this.m_syncAtTime;
					if (syncAtTime)
					{
						this.m_game.SendGameUpdateHideState(this);
					}
				}
			}
		}

		// Token: 0x17001433 RID: 5171
		// (get) Token: 0x06007578 RID: 30072 RVA: 0x0026F1A4 File Offset: 0x0026D3A4
		// (set) Token: 0x06007579 RID: 30073 RVA: 0x0026F1BC File Offset: 0x0026D3BC
		public int DoAction
		{
			get
			{
				return this.m_doAction;
			}
			set
			{
				bool flag = this.m_doAction != value;
				if (flag)
				{
					this.m_doAction = value;
				}
			}
		}

		// Token: 0x17001434 RID: 5172
		// (get) Token: 0x0600757A RID: 30074 RVA: 0x0026F1E4 File Offset: 0x0026D3E4
		// (set) Token: 0x0600757B RID: 30075 RVA: 0x0002B873 File Offset: 0x00029A73
		public bool BlockTurn
		{
			get
			{
				return this.m_isBlockTurn;
			}
			set
			{
				this.m_isBlockTurn = value;
			}
		}

		// Token: 0x17001435 RID: 5173
		// (get) Token: 0x0600757C RID: 30076 RVA: 0x0026F1FC File Offset: 0x0026D3FC
		// (set) Token: 0x0600757D RID: 30077 RVA: 0x0026F214 File Offset: 0x0026D414
		public int State
		{
			get
			{
				return this.m_state;
			}
			set
			{
				bool flag = this.m_state != value;
				if (flag)
				{
					this.m_state = value;
					bool syncAtTime = this.m_syncAtTime;
					if (syncAtTime)
					{
						this.m_game.SendLivingUpdateAngryState(this);
					}
				}
			}
		}

		// Token: 0x17001436 RID: 5174
		// (get) Token: 0x0600757E RID: 30078 RVA: 0x0002B87D File Offset: 0x00029A7D
		// (set) Token: 0x0600757F RID: 30079 RVA: 0x0002B885 File Offset: 0x00029A85
		public int KeepLifeCount { get; set; }

		// Token: 0x17001437 RID: 5175
		// (get) Token: 0x06007580 RID: 30080 RVA: 0x0002B88E File Offset: 0x00029A8E
		// (set) Token: 0x06007581 RID: 30081 RVA: 0x0002B896 File Offset: 0x00029A96
		public bool KeepLife { get; set; }

		// Token: 0x17001438 RID: 5176
		// (get) Token: 0x06007582 RID: 30082 RVA: 0x0026F254 File Offset: 0x0026D454
		// (set) Token: 0x06007583 RID: 30083 RVA: 0x0002B89F File Offset: 0x00029A9F
		public int MaxBlood
		{
			get
			{
				return this.m_maxBlood;
			}
			set
			{
				this.m_maxBlood = value;
			}
		}

		// Token: 0x17001439 RID: 5177
		// (get) Token: 0x06007584 RID: 30084 RVA: 0x0026F26C File Offset: 0x0026D46C
		// (set) Token: 0x06007585 RID: 30085 RVA: 0x0002B8A9 File Offset: 0x00029AA9
		public int Blood
		{
			get
			{
				return this.m_blood;
			}
			set
			{
				this.m_blood = value;
			}
		}

		// Token: 0x140000B5 RID: 181
		// (add) Token: 0x06007586 RID: 30086 RVA: 0x0026F284 File Offset: 0x0026D484
		// (remove) Token: 0x06007587 RID: 30087 RVA: 0x0026F2BC File Offset: 0x0026D4BC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingTakedCritDamageEventHandle AfterBeingCrited;

		// Token: 0x140000B6 RID: 182
		// (add) Token: 0x06007588 RID: 30088 RVA: 0x0026F2F4 File Offset: 0x0026D4F4
		// (remove) Token: 0x06007589 RID: 30089 RVA: 0x0026F32C File Offset: 0x0026D52C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingMakeDamageEventHandle BeforeMakeDamage;

		// Token: 0x140000B7 RID: 183
		// (add) Token: 0x0600758A RID: 30090 RVA: 0x0026F364 File Offset: 0x0026D564
		// (remove) Token: 0x0600758B RID: 30091 RVA: 0x0026F39C File Offset: 0x0026D59C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingEventHandle Died;

		// Token: 0x140000B8 RID: 184
		// (add) Token: 0x0600758C RID: 30092 RVA: 0x0026F3D4 File Offset: 0x0026D5D4
		// (remove) Token: 0x0600758D RID: 30093 RVA: 0x0026F40C File Offset: 0x0026D60C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingTakedDamageEventHandle BeforeTakeDamage;

		// Token: 0x140000B9 RID: 185
		// (add) Token: 0x0600758E RID: 30094 RVA: 0x0026F444 File Offset: 0x0026D644
		// (remove) Token: 0x0600758F RID: 30095 RVA: 0x0026F47C File Offset: 0x0026D67C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingTakedDamageEventHandle TakePlayerDamage;

		// Token: 0x140000BA RID: 186
		// (add) Token: 0x06007590 RID: 30096 RVA: 0x0026F4B4 File Offset: 0x0026D6B4
		// (remove) Token: 0x06007591 RID: 30097 RVA: 0x0026F4EC File Offset: 0x0026D6EC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingTakedDamageEventHandle OnMakeDamageEvent;

		// Token: 0x140000BB RID: 187
		// (add) Token: 0x06007592 RID: 30098 RVA: 0x0026F524 File Offset: 0x0026D724
		// (remove) Token: 0x06007593 RID: 30099 RVA: 0x0026F55C File Offset: 0x0026D75C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingEventHandle BeginNextTurn;

		// Token: 0x140000BC RID: 188
		// (add) Token: 0x06007594 RID: 30100 RVA: 0x0026F594 File Offset: 0x0026D794
		// (remove) Token: 0x06007595 RID: 30101 RVA: 0x0026F5CC File Offset: 0x0026D7CC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingEventHandle BeginSelfTurn;

		// Token: 0x140000BD RID: 189
		// (add) Token: 0x06007596 RID: 30102 RVA: 0x0026F604 File Offset: 0x0026D804
		// (remove) Token: 0x06007597 RID: 30103 RVA: 0x0026F63C File Offset: 0x0026D83C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingEventHandle BeginAttacking;

		// Token: 0x140000BE RID: 190
		// (add) Token: 0x06007598 RID: 30104 RVA: 0x0026F674 File Offset: 0x0026D874
		// (remove) Token: 0x06007599 RID: 30105 RVA: 0x0026F6AC File Offset: 0x0026D8AC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingEventHandle BeginAttacked;

		// Token: 0x140000BF RID: 191
		// (add) Token: 0x0600759A RID: 30106 RVA: 0x0026F6E4 File Offset: 0x0026D8E4
		// (remove) Token: 0x0600759B RID: 30107 RVA: 0x0026F71C File Offset: 0x0026D91C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingEventHandle EndAttacking;

		// Token: 0x140000C0 RID: 192
		// (add) Token: 0x0600759C RID: 30108 RVA: 0x0026F754 File Offset: 0x0026D954
		// (remove) Token: 0x0600759D RID: 30109 RVA: 0x0026F78C File Offset: 0x0026D98C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingEventHandle BeginUseProp;

		// Token: 0x140000C1 RID: 193
		// (add) Token: 0x0600759E RID: 30110 RVA: 0x0026F7C4 File Offset: 0x0026D9C4
		// (remove) Token: 0x0600759F RID: 30111 RVA: 0x0026F7FC File Offset: 0x0026D9FC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingEventHandle GameStarted;

		// Token: 0x140000C2 RID: 194
		// (add) Token: 0x060075A0 RID: 30112 RVA: 0x0026F834 File Offset: 0x0026DA34
		// (remove) Token: 0x060075A1 RID: 30113 RVA: 0x0026F86C File Offset: 0x0026DA6C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingEventHandle GameStoped;

		// Token: 0x140000C3 RID: 195
		// (add) Token: 0x060075A2 RID: 30114 RVA: 0x0026F8A4 File Offset: 0x0026DAA4
		// (remove) Token: 0x060075A3 RID: 30115 RVA: 0x0026F8DC File Offset: 0x0026DADC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event KillLivingEventHanlde AfterKillingLiving;

		// Token: 0x140000C4 RID: 196
		// (add) Token: 0x060075A4 RID: 30116 RVA: 0x0026F914 File Offset: 0x0026DB14
		// (remove) Token: 0x060075A5 RID: 30117 RVA: 0x0026F94C File Offset: 0x0026DB4C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event KillLivingEventHanlde AfterKilledByLiving;

		// Token: 0x140000C5 RID: 197
		// (add) Token: 0x060075A6 RID: 30118 RVA: 0x0026F984 File Offset: 0x0026DB84
		// (remove) Token: 0x060075A7 RID: 30119 RVA: 0x0026F9BC File Offset: 0x0026DBBC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingEventHandle PropUseItem;

		// Token: 0x140000C6 RID: 198
		// (add) Token: 0x060075A8 RID: 30120 RVA: 0x0026F9F4 File Offset: 0x0026DBF4
		// (remove) Token: 0x060075A9 RID: 30121 RVA: 0x0026FA2C File Offset: 0x0026DC2C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event LivingTakedDamageEventHandle TakePetDamage;

		// Token: 0x060075AA RID: 30122 RVA: 0x0026FA64 File Offset: 0x0026DC64
		public double getHertAddition(ItemInfo item)
		{
			bool flag = item == null;
			double num;
			if (flag)
			{
				num = 0.0;
			}
			else
			{
				double num2 = (double)item.Template.Property7;
				double num3 = (double)item.StrengthenLevel;
				double num4 = num2 * Math.Pow(1.1, num3) - num2;
				num = Math.Round(num4) + num2;
			}
			return num;
		}

		// Token: 0x060075AB RID: 30123 RVA: 0x0026FAC0 File Offset: 0x0026DCC0
		public Living(int id, BaseGame game, int team, string name, string modelId, int maxBlood, int immunity, int direction)
			: base(id)
		{
			this.m_game = game;
			this.m_team = team;
			this.m_name = name;
			this.m_modelId = modelId;
			this.m_maxBlood = maxBlood;
			this.m_direction = direction;
			this.m_state = 0;
			this.m_doAction = -1;
			this.MaxBeatDis = 100;
			this.m_effectList = new EffectList(this, immunity);
			this.m_petEffectList = new PetEffectList(this, immunity);
			this.m_cardEffectList = new CardEffectList(this, immunity);
			this.m_fightBufferInfo = new FightBufferInfo();
			this.m_petEffects = new PetEffectInfo();
			this.m_config = new LivingConfig();
			this.rand = new Random();
			this.m_syncAtTime = true;
			this.m_vaneOpen = false;
			this.AddArmor = false;
			this.m_action = "";
			this.m_createAction = null;
			this.m_type = eLivingType.Living;
			this.lastShots = new Dictionary<int, int>();
			this.EquipGhostReduceCritDamage = 0;
		}

		// Token: 0x060075AC RID: 30124 RVA: 0x0026FCA8 File Offset: 0x0026DEA8
		public void ChangeDamage(double value)
		{
			this.BaseDamage += value;
			bool flag = this.BaseDamage < 0.0;
			if (flag)
			{
				this.BaseDamage = 0.0;
			}
		}

		// Token: 0x060075AD RID: 30125 RVA: 0x0002B8B3 File Offset: 0x00029AB3
		public void SetRelateDemagemRect(int x, int y, int width, int height)
		{
			this.m_demageRect.X = x;
			this.m_demageRect.Y = y;
			this.m_demageRect.Width = width;
			this.m_demageRect.Height = height;
		}

		// Token: 0x060075AE RID: 30126 RVA: 0x0026FCEC File Offset: 0x0026DEEC
		public void ReSetRectWithDir()
		{
			base.SetRect(-this.m_rect.X - this.m_rect.Width, this.m_rect.Y, this.m_rect.Width, this.m_rect.Height);
			base.SetRectBomb(-this.m_rectBomb.X - this.m_rectBomb.Width, this.m_rectBomb.Y, this.m_rectBomb.Width, this.m_rectBomb.Height);
			this.SetRelateDemagemRect(-this.m_demageRect.X - this.m_demageRect.Width, this.m_demageRect.Y, this.m_demageRect.Width, this.m_demageRect.Height);
		}

		// Token: 0x060075AF RID: 30127 RVA: 0x0026FDBC File Offset: 0x0026DFBC
		public Point GetShootPoint()
		{
			bool flag = !(this is SimpleBoss) && !(this is SimpleNpc);
			Point point;
			if (flag)
			{
				bool flag2 = this.m_direction <= 0;
				if (flag2)
				{
					point = new Point(this.X + this.m_rect.X - 30, this.Y + this.m_rect.Y - 20);
				}
				else
				{
					point = new Point(this.X - this.m_rect.X + 30, this.Y + this.m_rect.Y - 20);
				}
			}
			else
			{
				bool flag3 = this.m_direction <= 0;
				if (flag3)
				{
					point = new Point(this.X + this.FireX, this.Y + this.FireY);
				}
				else
				{
					point = new Point(this.X - this.FireX, this.Y + this.FireY);
				}
			}
			return point;
		}

		// Token: 0x060075B0 RID: 30128 RVA: 0x0026FEB4 File Offset: 0x0026E0B4
		public Rectangle GetDirectDemageRect()
		{
			return new Rectangle(this.X + this.m_demageRect.X, this.Y + this.m_demageRect.Y, this.m_demageRect.Width, this.m_demageRect.Height);
		}

		// Token: 0x060075B1 RID: 30129 RVA: 0x0026FF08 File Offset: 0x0026E108
		public List<Rectangle> GetDirectBoudRect()
		{
			return new List<Rectangle>
			{
				new Rectangle(this.X + base.Bound.X, this.Y + base.Bound.Y, base.Bound.Width, base.Bound.Height),
				new Rectangle(this.X + base.Bound1.X, this.Y + base.Bound1.Y, base.Bound1.Width, base.Bound1.Height)
			};
		}

		// Token: 0x060075B2 RID: 30130 RVA: 0x0026FFC4 File Offset: 0x0026E1C4
		public double Distance(Point p)
		{
			List<double> list = new List<double>();
			Rectangle directDemageRect = this.GetDirectDemageRect();
			for (int i = directDemageRect.X; i <= directDemageRect.X + directDemageRect.Width; i += 10)
			{
				list.Add(Math.Sqrt((double)((i - p.X) * (i - p.X) + (directDemageRect.Y - p.Y) * (directDemageRect.Y - p.Y))));
				list.Add(Math.Sqrt((double)((i - p.X) * (i - p.X) + (directDemageRect.Y + directDemageRect.Height - p.Y) * (directDemageRect.Y + directDemageRect.Height - p.Y))));
			}
			for (int j = directDemageRect.Y; j <= directDemageRect.Y + directDemageRect.Height; j += 10)
			{
				list.Add(Math.Sqrt((double)((directDemageRect.X - p.X) * (directDemageRect.X - p.X) + (j - p.Y) * (j - p.Y))));
				list.Add(Math.Sqrt((double)((directDemageRect.X + directDemageRect.Width - p.X) * (directDemageRect.X + directDemageRect.Width - p.X) + (j - p.Y) * (j - p.Y))));
			}
			return list.Min();
		}

		// Token: 0x060075B3 RID: 30131 RVA: 0x00270178 File Offset: 0x0026E378
		public double BoundDistance(Point p)
		{
			List<double> list = new List<double>();
			foreach (Rectangle rectangle in this.GetDirectBoudRect())
			{
				for (int i = rectangle.X; i <= rectangle.X + rectangle.Width; i += 10)
				{
					list.Add(Math.Sqrt((double)((i - p.X) * (i - p.X) + (rectangle.Y - p.Y) * (rectangle.Y - p.Y))));
					list.Add(Math.Sqrt((double)((i - p.X) * (i - p.X) + (rectangle.Y + rectangle.Height - p.Y) * (rectangle.Y + rectangle.Height - p.Y))));
				}
				for (int j = rectangle.Y; j <= rectangle.Y + rectangle.Height; j += 10)
				{
					list.Add(Math.Sqrt((double)((rectangle.X - p.X) * (rectangle.X - p.X) + (j - p.Y) * (j - p.Y))));
					list.Add(Math.Sqrt((double)((rectangle.X + rectangle.Width - p.X) * (rectangle.X + rectangle.Width - p.X) + (j - p.Y) * (j - p.Y))));
				}
			}
			return list.Min();
		}

		// Token: 0x060075B4 RID: 30132 RVA: 0x0027037C File Offset: 0x0026E57C
		public virtual void Reset()
		{
			this.m_blood = this.m_maxBlood;
			this.m_isFrost = false;
			this.m_isHide = false;
			this.m_isNoHole = false;
			this.m_isLiving = true;
			this.m_isBlockTurn = false;
			this.m_createAction = null;
			this.TurnNum = 0;
			this.TotalHurt = 0;
			this.TotalKill = 0;
			this.TotalShootCount = 0;
			this.TotalHitTargetCount = 0;
			this.TotalCure = 0;
			this.TotalDamageForMatch = 0;
			this.KeepLifeCount = 0;
		}

		// Token: 0x060075B5 RID: 30133 RVA: 0x002703FC File Offset: 0x0026E5FC
		public virtual void PickPhy(PhysicalObj phy)
		{
			bool syncAtTime = this.m_syncAtTime;
			if (syncAtTime)
			{
				phy.Die();
				string name = phy.Name;
				string text = name;
				uint num = <PrivateImplementationDetails>.ComputeStringHash(text);
				if (num <= 1500919970U)
				{
					if (num <= 1450587113U)
					{
						if (num != 1417031875U)
						{
							if (num != 1433809494U)
							{
								if (num == 1450587113U)
								{
									if (text == "shield-4")
									{
										(this.Game as PVEGame).TotalKillCount -= 4;
									}
								}
							}
							else if (text == "shield-5")
							{
								(this.Game as PVEGame).TotalKillCount -= 5;
							}
						}
						else if (text == "shield-6")
						{
							(this.Game as PVEGame).TotalKillCount -= 5;
						}
					}
					else if (num != 1467364732U)
					{
						if (num != 1484142351U)
						{
							if (num == 1500919970U)
							{
								if (text == "shield-1")
								{
									(this.Game as PVEGame).TotalKillCount--;
								}
							}
						}
						else if (text == "shield-2")
						{
							(this.Game as PVEGame).TotalKillCount -= 2;
						}
					}
					else if (text == "shield-3")
					{
						(this.Game as PVEGame).TotalKillCount -= 3;
					}
				}
				else if (num <= 2220304697U)
				{
					if (num != 2186749459U)
					{
						if (num != 2203527078U)
						{
							if (num == 2220304697U)
							{
								if (text == "shield3")
								{
									(this.Game as PVEGame).TotalKillCount += 3;
								}
							}
						}
						else if (text == "shield2")
						{
							(this.Game as PVEGame).TotalKillCount += 2;
						}
					}
					else if (text == "shield1")
					{
						(this.Game as PVEGame).TotalKillCount++;
					}
				}
				else if (num != 2237082316U)
				{
					if (num != 2253859935U)
					{
						if (num == 2270637554U)
						{
							if (text == "shield6")
							{
								(this.Game as PVEGame).TotalKillCount += 6;
							}
						}
					}
					else if (text == "shield5")
					{
						(this.Game as PVEGame).TotalKillCount += 5;
					}
				}
				else if (text == "shield4")
				{
					(this.Game as PVEGame).TotalKillCount += 4;
				}
				bool flag = (this.Game as PVEGame).TotalKillCount <= 0;
				if (flag)
				{
					(this.Game as PVEGame).TotalKillCount = 0;
				}
			}
		}

		// Token: 0x060075B6 RID: 30134 RVA: 0x00270768 File Offset: 0x0026E968
		public virtual void PickBox(SimpleBox box)
		{
			bool flag = box.Type > 1 && this is Player;
			if (flag)
			{
				box.Die();
				bool flag2 = (this as Player).psychic < (this as Player).MaxPsychic;
				if (flag2)
				{
					(this as Player).psychic += ((box.Type == 2) ? 10 : 20);
				}
			}
			else
			{
				box.UserID = base.Id;
				box.Die();
				bool syncAtTime = this.m_syncAtTime;
				if (syncAtTime)
				{
					this.m_game.SendGamePickBox(this, box.Id, 0, "");
				}
				bool flag3 = base.IsLiving && this is Player;
				if (flag3)
				{
					(this as Player).OpenBox(box.Id);
				}
			}
		}

		// Token: 0x060075B7 RID: 30135 RVA: 0x00270840 File Offset: 0x0026EA40
		public override void PrepareNewTurn()
		{
			this.ShootMovieDelay = 0;
			this.CurrentDamagePlus = 1f;
			this.CurrentShootMinus = 1f;
			this.IgnoreArmor = false;
			this.IgnoreGuard = 0;
			this.ControlBall = false;
			this.NoHoleTurn = false;
			this.CurrentIsHitTarget = false;
			this.totalCritical = 0;
			this.totalShotTurn = 0;
			this.IdAcrobacias = 1;
			this.Prop1 = 0;
			this.Prop2 = 0;
			this.Prop3 = 0;
			this.Prop4 = 0;
			this.WillIceForonze = false;
			this.BombFoul = false;
			this.TiredShoot = false;
			this.ClearBuff = false;
			this.LockMove = false;
			this.lastShots.Clear();
			this.OnBeginNewTurn();
		}

		// Token: 0x060075B8 RID: 30136 RVA: 0x0002B8EB File Offset: 0x00029AEB
		public virtual void PrepareSelfTurn()
		{
			this.OnBeginSelfTurn();
		}

		// Token: 0x060075B9 RID: 30137 RVA: 0x0002B8F5 File Offset: 0x00029AF5
		public void StartAttacked()
		{
			this.OnStartAttacked();
		}

		// Token: 0x060075BA RID: 30138 RVA: 0x002708F8 File Offset: 0x0026EAF8
		public virtual void StartAttacking()
		{
			bool flag = !this.m_isAttacking;
			if (flag)
			{
				this.m_isAttacking = true;
				this.OnStartAttacking();
			}
		}

		// Token: 0x060075BB RID: 30139 RVA: 0x00270924 File Offset: 0x0026EB24
		public virtual void StopAttacking()
		{
			bool isAttacking = this.m_isAttacking;
			if (isAttacking)
			{
				this.m_isAttacking = false;
				this.OnStopAttacking();
			}
		}

		// Token: 0x060075BC RID: 30140 RVA: 0x0027094C File Offset: 0x0026EB4C
		public override void CollidedByObject(Physics phy)
		{
			bool flag = phy is SimpleBomb;
			if (flag)
			{
				((SimpleBomb)phy).Bomb();
			}
		}

		// Token: 0x060075BD RID: 30141 RVA: 0x0002B8FF File Offset: 0x00029AFF
		public virtual void BeforeTakedDamage(Living source, ref int damageAmount, ref int criticalAmount)
		{
			this.OnBeforeTakedDamage(source, ref damageAmount, ref criticalAmount);
		}

		// Token: 0x060075BE RID: 30142 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnAfterTakedBomb()
		{
		}

		// Token: 0x060075BF RID: 30143 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnAfterTakedFrozen()
		{
		}

		// Token: 0x060075C0 RID: 30144 RVA: 0x0002B90C File Offset: 0x00029B0C
		public override void StartMoving()
		{
			this.StartMoving(0, 30);
		}

		// Token: 0x060075C1 RID: 30145 RVA: 0x00270978 File Offset: 0x0026EB78
		public virtual void StartMoving(int delay, int speed)
		{
			bool flag = !this.Config.IsFly;
			if (flag)
			{
				bool flag2 = this.m_map.IsEmpty(this.X, this.Y);
				if (flag2)
				{
					this.FallFrom(this.X, this.Y, null, delay, 0, speed);
				}
				base.StartMoving();
			}
		}

		// Token: 0x060075C2 RID: 30146 RVA: 0x0002B919 File Offset: 0x00029B19
		public void SetXY(int x, int y, int delay)
		{
			this.m_game.AddAction(new LivingDirectSetXYAction(this, x, y, delay));
		}

		// Token: 0x060075C3 RID: 30147 RVA: 0x002709D8 File Offset: 0x0026EBD8
		public virtual int RrmoveBlood(int value, int type)
		{
			this.m_blood -= value;
			this.m_game.SendGameUpdateHealth(this, type, value);
			return value;
		}

		// Token: 0x060075C4 RID: 30148 RVA: 0x0002B931 File Offset: 0x00029B31
		public void AddEffect(AbstractEffect effect, int delay)
		{
			this.m_game.AddAction(new LivingDelayEffectAction(this, effect, delay));
		}

		// Token: 0x060075C5 RID: 30149 RVA: 0x0002B948 File Offset: 0x00029B48
		public void AddPetEffect(AbstractPetEffect effect, int delay)
		{
			this.m_game.AddAction(new LivingDelayPetEffectAction(this, effect, delay));
		}

		// Token: 0x060075C6 RID: 30150 RVA: 0x0002B95F File Offset: 0x00029B5F
		public void Say(string msg, int type, int delay, int finishTime)
		{
			this.m_game.AddAction(new LivingSayAction(this, msg, type, delay, finishTime));
		}

		// Token: 0x060075C7 RID: 30151 RVA: 0x0002B979 File Offset: 0x00029B79
		public void Say(string msg, int type, int delay)
		{
			this.m_game.AddAction(new LivingSayAction(this, msg, type, delay, 1000));
		}

		// Token: 0x060075C8 RID: 30152 RVA: 0x0002B996 File Offset: 0x00029B96
		public void SendAfterShootedFrozen(int delay)
		{
			this.m_game.AddAction(new LivingAfterShootedFrozen(this, delay));
		}

		// Token: 0x060075C9 RID: 30153 RVA: 0x0002B9AC File Offset: 0x00029BAC
		public void SendAfterShootedAction(int delay)
		{
			this.m_game.AddAction(new LivingAfterShootedAction(this, this, delay));
		}

		// Token: 0x060075CA RID: 30154 RVA: 0x00270A08 File Offset: 0x0026EC08
		public bool MoveTo(int x, int y, string action, int delay)
		{
			return this.MoveTo(x, y, action, delay, "", 3, null);
		}

		// Token: 0x060075CB RID: 30155 RVA: 0x00270A2C File Offset: 0x0026EC2C
		public bool MoveTo(int x, int y, string action, int delay, int speed)
		{
			return this.MoveTo(x, y, action, delay, "", speed, null);
		}

		// Token: 0x060075CC RID: 30156 RVA: 0x00270A54 File Offset: 0x0026EC54
		public bool MoveTo(int x, int y, string action, int delay, LivingCallBack callback)
		{
			return this.MoveTo(x, y, action, delay, "", 3, callback, 0);
		}

		// Token: 0x060075CD RID: 30157 RVA: 0x00270A7C File Offset: 0x0026EC7C
		public bool MoveTo(int x, int y, string action, int delay, int speed, LivingCallBack callback)
		{
			return this.MoveTo(x, y, action, delay, "", speed, callback, 0);
		}

		// Token: 0x060075CE RID: 30158 RVA: 0x00270AA4 File Offset: 0x0026ECA4
		public bool MoveTo(int x, int y, string action, int delay, LivingCallBack callback, int speed)
		{
			return this.MoveTo(x, y, action, delay, "", speed, callback, 0);
		}

		// Token: 0x060075CF RID: 30159 RVA: 0x00270ACC File Offset: 0x0026ECCC
		public bool MoveTo(int x, int y, string action, int delay, string sAction, int speed, LivingCallBack callback)
		{
			return this.MoveTo(x, y, action, delay, sAction, speed, callback, 0);
		}

		// Token: 0x060075D0 RID: 30160 RVA: 0x00270AF0 File Offset: 0x0026ECF0
		public bool MoveTo(int x, int y, string action, string sAction, int speed, int delay, LivingCallBack callback)
		{
			return this.MoveTo(x, y, action, delay, sAction, speed, callback, 0);
		}

		// Token: 0x060075D1 RID: 30161 RVA: 0x00270B14 File Offset: 0x0026ED14
		public bool MoveTo(int x, int y, string action, int delay, string sAction, int speed)
		{
			return this.MoveTo(x, y, action, delay, sAction, speed, null);
		}

		// Token: 0x060075D2 RID: 30162 RVA: 0x00270B38 File Offset: 0x0026ED38
		public bool MoveTo(int x, int y, string action, int delay, string sAction, int speed, LivingCallBack callback, int delayCallback)
		{
			bool flag = this.m_x == x && this.m_y == y;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = x < 0 || x > this.m_map.Bound.Width;
				if (flag3)
				{
					flag2 = false;
				}
				else
				{
					List<Point> list = new List<Point>();
					int num = this.m_x;
					int num2 = this.m_y;
					int num3 = ((x > num) ? 1 : (-1));
					Point point = new Point(num, num2);
					bool isFly = this.Config.IsFly;
					if (isFly)
					{
						Point point2 = new Point(x - point.X, y - point.Y);
						while (point2.Length() > (double)speed)
						{
							point2 = point2.Normalize(speed);
							point = new Point(point.X + point2.X, point.Y + point2.Y);
							point2 = new Point(x - point.X, y - point.Y);
							bool flag4 = point != Point.Empty;
							if (!flag4)
							{
								list.Add(new Point(x, y));
								break;
							}
							list.Add(point);
						}
					}
					else
					{
						while ((x - num) * num3 > 0)
						{
							point = this.m_map.FindNextWalkPointDown(num, num2, num3, speed * Living.StepX, speed * Living.StepY);
							bool flag5 = point != Point.Empty;
							if (!flag5)
							{
								break;
							}
							list.Add(point);
							num = point.X;
							num2 = point.Y;
						}
					}
					bool flag6 = list.Count > 0;
					if (flag6)
					{
						this.m_game.AddAction(new LivingMoveToAction(this, list, action, delay, speed, sAction, callback, delayCallback));
						flag2 = true;
					}
					else
					{
						flag2 = false;
					}
				}
			}
			return flag2;
		}

		// Token: 0x060075D3 RID: 30163 RVA: 0x00270D1C File Offset: 0x0026EF1C
		public bool FallFrom(int x, int y, string action, int delay, int type, int speed)
		{
			return this.FallFrom(x, y, action, delay, type, speed, null);
		}

		// Token: 0x060075D4 RID: 30164 RVA: 0x00270D40 File Offset: 0x0026EF40
		public bool FallFrom(int x, int y, string action, int delay, int type, int speed, LivingCallBack callback)
		{
			Point point = this.m_map.FindYLineNotEmptyPointDown(x, y);
			bool flag = point == Point.Empty;
			if (flag)
			{
				point = new Point(x, this.m_game.Map.Bound.Height + 1);
			}
			bool flag2 = this.Y < point.Y;
			bool flag3;
			if (flag2)
			{
				this.m_game.AddAction(new LivingFallingAction(this, point.X, point.Y, speed, action, delay, type, callback));
				flag3 = true;
			}
			else
			{
				flag3 = false;
			}
			return flag3;
		}

		// Token: 0x060075D5 RID: 30165 RVA: 0x00270DD8 File Offset: 0x0026EFD8
		public bool FallFromTo(int x, int y, string action, int delay, int type, int speed, LivingCallBack callback)
		{
			this.m_game.AddAction(new LivingFallingAction(this, x, y, speed, action, delay, type, callback));
			return true;
		}

		// Token: 0x060075D6 RID: 30166 RVA: 0x00270E08 File Offset: 0x0026F008
		public bool JumpTo(int x, int y, string action, int delay, int type)
		{
			return this.JumpTo(x, y, action, delay, type, 20, null, 0);
		}

		// Token: 0x060075D7 RID: 30167 RVA: 0x00270E2C File Offset: 0x0026F02C
		public bool JumpTo(int x, int y, string ation, int delay, int type, LivingCallBack callback)
		{
			return this.JumpTo(x, y, ation, delay, type, 20, callback, 0);
		}

		// Token: 0x060075D8 RID: 30168 RVA: 0x00270E50 File Offset: 0x0026F050
		public bool JumpTo(int x, int y, string action, int delay, int type, int speed, LivingCallBack callback)
		{
			return this.JumpTo(x, y, action, delay, type, speed, callback, 0);
		}

		// Token: 0x060075D9 RID: 30169 RVA: 0x00270E74 File Offset: 0x0026F074
		public bool JumpTo(int x, int y, string action, int delay, int type, int speed, LivingCallBack callback, int value)
		{
			Point point = this.m_map.FindYLineNotEmptyPointDown(x, y);
			bool flag = point.Y < this.Y || value == 1;
			bool flag2;
			if (flag)
			{
				this.m_game.AddAction(new LivingJumpAction(this, point.X, point.Y, speed, action, delay, type, callback));
				flag2 = true;
			}
			else
			{
				flag2 = false;
			}
			return flag2;
		}

		// Token: 0x060075DA RID: 30170 RVA: 0x00270EE0 File Offset: 0x0026F0E0
		public bool JumpToSpeed(int x, int y, string action, int delay, int type, int speed, LivingCallBack callback)
		{
			Point point = this.m_map.FindYLineNotEmptyPointDown(x, y);
			int y2 = point.Y;
			this.m_game.AddAction(new LivingJumpAction(this, point.X, point.Y, speed, action, delay, type, callback));
			return true;
		}

		// Token: 0x060075DB RID: 30171 RVA: 0x00270F38 File Offset: 0x0026F138
		public int FindDirection(Living obj)
		{
			bool flag = obj.X > this.X;
			int num;
			if (flag)
			{
				num = 1;
			}
			else
			{
				num = -1;
			}
			return num;
		}

		// Token: 0x060075DC RID: 30172 RVA: 0x00270F64 File Offset: 0x0026F164
		public void ChangeDirection(int direction, int delay)
		{
			bool flag = delay > 0;
			if (flag)
			{
				this.m_game.AddAction(new LivingChangeDirectionAction(this, direction, delay));
			}
			else
			{
				this.Direction = direction;
			}
		}

		// Token: 0x060075DD RID: 30173 RVA: 0x00270F9C File Offset: 0x0026F19C
		public void ChangeDirection(Living obj, int delay)
		{
			int num = this.FindDirection(obj);
			bool flag = delay > 0;
			if (flag)
			{
				this.m_game.AddAction(new LivingChangeDirectionAction(this, num, delay));
			}
			else
			{
				this.Direction = num;
			}
		}

		// Token: 0x060075DE RID: 30174 RVA: 0x00270FDC File Offset: 0x0026F1DC
		public double GetHertAddition(ItemInfo item)
		{
			bool flag = item != null;
			double num4;
			if (flag)
			{
				double num = (double)item.Template.Property7;
				double num2 = (double)item.StrengthenLevel;
				double num3 = num * Math.Pow(1.1, num2) - num;
				num4 = Math.Round(num3) + num;
			}
			else
			{
				num4 = 0.0;
			}
			return num4;
		}

		// Token: 0x060075DF RID: 30175 RVA: 0x00271038 File Offset: 0x0026F238
		public int MakeDamage(Living target)
		{
			bool isChristmasBoss = target.Config.IsChristmasBoss;
			int num;
			if (isChristmasBoss)
			{
				num = 1;
			}
			else
			{
				double num2 = this.BaseDamage;
				double num3 = target.BaseGuard;
				double num4 = target.Defence;
				double attack = this.Attack;
				bool flag = target.AddArmor && (target as Player).DeputyWeapon != null;
				if (flag)
				{
					int num5 = (int)this.GetHertAddition((target as Player).DeputyWeapon);
					num3 += (double)num5;
					num4 += (double)num5;
				}
				bool ignoreArmor = this.IgnoreArmor;
				if (ignoreArmor)
				{
					num3 *= 0.6;
					num4 *= 0.6;
				}
				bool flag2 = this.IgnoreGuard > 0 && !this.IgnoreArmor;
				if (flag2)
				{
					num4 -= num4 / 100.0 * (double)this.IgnoreGuard;
					num3 -= num3 / 100.0 * (double)this.IgnoreGuard;
				}
				bool flag3 = this.AddDamage > 0;
				if (flag3)
				{
					num2 += num2 / 100.0 * (double)this.AddDamage;
				}
				float currentDamagePlus = this.CurrentDamagePlus;
				float currentShootMinus = this.CurrentShootMinus;
				double num6 = 0.95 * (target.BaseGuard - (double)(3 * this.Grade)) / (500.0 + target.BaseGuard - (double)(3 * this.Grade));
				double num7 = ((target.Defence - this.Lucky >= 0.0) ? (0.95 * (target.Defence - this.Lucky) / (600.0 + target.Defence - this.Lucky)) : 0.0);
				double num8 = num2 * (1.0 + attack * 0.001) * (1.0 - (num6 + num7 - num6 * num7)) * (double)currentDamagePlus * (double)currentShootMinus;
				Point point = new Point(this.X, this.Y);
				bool flag4 = num8 < 0.0;
				if (flag4)
				{
					num = 1;
				}
				else
				{
					num = (int)num8;
				}
			}
			return num;
		}

		// Token: 0x060075E0 RID: 30176 RVA: 0x00271268 File Offset: 0x0026F468
		public int MakeCriticalDamage(Living target, int baseDamage)
		{
			double lucky = this.Lucky;
			bool flag = lucky * 45.0 / (800.0 + lucky) + (double)this.PetEffects.CritRate >= (double)this.m_game.Random.Next(100);
			int num2;
			if (flag)
			{
				int reduceCritValue = target.PetEffects.ReduceCritValue;
				int num = (int)((0.5 + lucky * 0.00015) * (double)baseDamage);
				num = num * (100 - reduceCritValue) / 100;
				bool flag2 = this.FightBuffers.ConsortionAddCritical > 0;
				if (flag2)
				{
					num += this.FightBuffers.ConsortionAddCritical;
				}
				bool flag3 = this.FightBuffers.CardAddCriticalLv1 > 0;
				if (flag3)
				{
					num += this.FightBuffers.CardAddCriticalLv1;
				}
				bool flag4 = this.FightBuffers.CardAddCriticalLv2 > 0;
				if (flag4)
				{
					num += this.FightBuffers.CardAddCriticalLv1;
				}
				bool flag5 = this.FightBuffers.CardAddCriticalLv3 > 0;
				if (flag5)
				{
					num += this.FightBuffers.CardAddCriticalLv1;
				}
				bool flag6 = this.FightBuffers.CardAddCriticalLv4 > 0;
				if (flag6)
				{
					num += this.FightBuffers.CardAddCriticalLv1;
				}
				num2 = num - num * target.EquipGhostReduceCritDamage / 100;
			}
			else
			{
				num2 = 0;
			}
			return num2;
		}

		// Token: 0x060075E1 RID: 30177 RVA: 0x002713C0 File Offset: 0x0026F5C0
		public bool Beat(Living target, string action, int demageAmount, int criticalAmount, int delay)
		{
			return this.Beat(target, action, demageAmount, criticalAmount, delay, 1, 1);
		}

		// Token: 0x060075E2 RID: 30178 RVA: 0x002713E4 File Offset: 0x0026F5E4
		public bool Beat(Living target, string action, int demageAmount, int criticalAmount, int delay, int livingCount, int attackEffect)
		{
			bool flag = target == null || !target.IsLiving;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				demageAmount = this.MakeDamage(target);
				this.OnBeforeTakedDamage(target, ref demageAmount, ref criticalAmount);
				this.StartAttacked();
				int num = (int)target.Distance(this.X, this.Y);
				bool flag3 = num <= this.MaxBeatDis;
				if (flag3)
				{
					bool flag4 = this.X - target.X > 0;
					if (flag4)
					{
						this.Direction = -1;
					}
					else
					{
						this.Direction = 1;
					}
					this.m_game.AddAction(new LivingBeatAction(this, target, demageAmount, criticalAmount, action, delay, livingCount, attackEffect));
					flag2 = true;
				}
				else
				{
					flag2 = false;
				}
			}
			return flag2;
		}

		// Token: 0x060075E3 RID: 30179 RVA: 0x0002B9C3 File Offset: 0x00029BC3
		public void BeatDirect(Living target, string action, int delay, int livingCount, int attackEffect)
		{
			this.m_game.AddAction(new LivingBeatDirectAction(this, target, action, delay, livingCount, attackEffect));
		}

		// Token: 0x060075E4 RID: 30180 RVA: 0x002714A0 File Offset: 0x0026F6A0
		public bool RangeAttacking(int fx, int tx, string action, int delay, List<Player> exceptPlayers)
		{
			return this.RangeAttacking(fx, tx, action, delay, true, false, exceptPlayers);
		}

		// Token: 0x060075E5 RID: 30181 RVA: 0x002714C4 File Offset: 0x0026F6C4
		public bool RangeAttacking(int fx, int tx, string action, int delay, bool directDamage)
		{
			return this.RangeAttacking(fx, tx, action, delay, true, directDamage, null);
		}

		// Token: 0x060075E6 RID: 30182 RVA: 0x002714E8 File Offset: 0x0026F6E8
		public bool RangeAttacking(int fx, int tx, string action, int delay, bool removeFrost, List<Player> exceptPlayers)
		{
			return this.RangeAttacking(fx, tx, action, delay, removeFrost, false, exceptPlayers);
		}

		// Token: 0x060075E7 RID: 30183 RVA: 0x0027150C File Offset: 0x0026F70C
		public bool RangeAttacking(int fx, int tx, string action, int delay, bool removeFrost, bool directDamage, List<Player> exceptPlayers)
		{
			bool isLiving = base.IsLiving;
			bool flag;
			if (isLiving)
			{
				this.m_game.AddAction(new LivingRangeAttackingAction(this, fx, tx, action, delay, removeFrost, directDamage, exceptPlayers));
				flag = true;
			}
			else
			{
				flag = false;
			}
			return flag;
		}

		// Token: 0x060075E8 RID: 30184 RVA: 0x0027154C File Offset: 0x0026F74C
		public void GetShootForceAndAngle(ref int x, ref int y, int bombId, int minTime, int maxTime, int bombCount, float time, ref int force, ref int angle)
		{
			bool flag = minTime >= maxTime;
			if (!flag)
			{
				BallInfo ballInfo = BallMgr.FindBall(bombId);
				bool flag2 = this.m_game == null || ballInfo == null;
				if (!flag2)
				{
					Map map = this.m_game.Map;
					Point shootPoint = this.GetShootPoint();
					float num = (float)(x - shootPoint.X);
					float num2 = (float)(y - shootPoint.Y);
					float num3 = map.airResistance * (float)ballInfo.DragIndex;
					float num4 = map.gravity * (float)ballInfo.Weight * (float)ballInfo.Mass;
					float num5 = map.wind * (float)ballInfo.Wind;
					float num6 = (float)ballInfo.Mass;
					for (float num7 = time; num7 <= 4f; num7 += 0.6f)
					{
						double num8 = Living.ComputeVx((double)num, num6, num3, num5, num7);
						double num9 = Living.ComputeVy((double)num2, num6, num3, num4, num7);
						bool flag3 = num9 >= 0.0 || num8 * (double)this.m_direction <= 0.0;
						if (!flag3)
						{
							double num10 = Math.Sqrt(num8 * num8 + num9 * num9);
							bool flag4 = num10 < 2000.0;
							if (flag4)
							{
								force = (int)num10;
								angle = (int)(Math.Atan(num9 / num8) / 3.141592653589793 * 180.0);
								bool flag5 = num8 < 0.0;
								if (flag5)
								{
									angle += 180;
								}
								break;
							}
						}
					}
					x = shootPoint.X;
					y = shootPoint.Y;
				}
			}
		}

		// Token: 0x060075E9 RID: 30185 RVA: 0x00271700 File Offset: 0x0026F900
		public bool ShootPoint(int x, int y, int bombId, int minTime, int maxTime, int bombCount, float time, int delay)
		{
			return this.ShootPoint(x, y, 0, 0, bombId, minTime, maxTime, bombCount, time, delay, null);
		}

		// Token: 0x060075EA RID: 30186 RVA: 0x00271728 File Offset: 0x0026F928
		public bool ShootPoint(int x, int y, int force, int angle, int bombId, int minTime, int maxTime, int bombCount, float time, int delay)
		{
			return this.ShootPoint(x, y, force, angle, bombId, minTime, maxTime, bombCount, time, delay, null);
		}

		// Token: 0x060075EB RID: 30187 RVA: 0x00271754 File Offset: 0x0026F954
		public bool ShootPoint(int x, int y, int force, int angle, int bombId, int minTime, int maxTime, int bombCount, float time, int delay, LivingCallBack callBack)
		{
			this.m_game.AddAction(new LivingShootAction(this, bombId, x, y, force, angle, bombCount, minTime, maxTime, time, delay, callBack));
			return true;
		}

		// Token: 0x060075EC RID: 30188 RVA: 0x0027178C File Offset: 0x0026F98C
		public bool ShootPoint(int x, int y, int bombId, int minTime, int maxTime, int bombCount, float time, int delay, LivingCallBack callBack)
		{
			this.m_game.AddAction(new LivingShootAction(this, bombId, x, y, 0, 0, bombCount, minTime, maxTime, time, delay, callBack));
			return true;
		}

		// Token: 0x060075ED RID: 30189 RVA: 0x002717C4 File Offset: 0x0026F9C4
		public bool IsFriendly(Living living)
		{
			bool flag = living is Player;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = living.Team == this.Team;
				flag2 = flag3;
			}
			return flag2;
		}

		// Token: 0x060075EE RID: 30190 RVA: 0x00271800 File Offset: 0x0026FA00
		public bool Shoot(int bombId, int x, int y, int force, int angle, int bombCount, int delay)
		{
			this.m_game.AddAction(new LivingShootAction(this, bombId, x, y, force, angle, bombCount, delay, 0, 0f, 0, null));
			return true;
		}

		// Token: 0x060075EF RID: 30191 RVA: 0x00271838 File Offset: 0x0026FA38
		public static double ComputeVx(double dx, float m, float af, float f, float t)
		{
			return (dx - (double)(f / m * t * t / 2f)) / (double)t + (double)(af / m) * dx * 0.7;
		}

		// Token: 0x060075F0 RID: 30192 RVA: 0x001D1FBC File Offset: 0x001D01BC
		public static double ComputeVy(double dx, float m, float af, float f, float t)
		{
			return (dx - (double)(f / m * t * t / 2f)) / (double)t + (double)(af / m) * dx * 1.3;
		}

		// Token: 0x060075F1 RID: 30193 RVA: 0x00271874 File Offset: 0x0026FA74
		public static double ComputDX(double v, float m, float af, float f, float dt)
		{
			return v * (double)dt + ((double)f - (double)af * v) / (double)m * (double)dt * (double)dt;
		}

		// Token: 0x060075F2 RID: 30194 RVA: 0x002718A0 File Offset: 0x0026FAA0
		public bool ShootImp(int bombId, int x, int y, int force, int angle, int bombCount, int shootCount)
		{
			BallInfo ballInfo = BallMgr.FindBall(bombId);
			Tile tile = BallMgr.FindTile(bombId);
			BombType ballType = BallMgr.GetBallType(bombId);
			int num = (int)(this.m_map.wind * 10f);
			bool flag = ballInfo != null;
			bool flag7;
			if (flag)
			{
				GSPacketIn gspacketIn = new GSPacketIn(91, base.Id);
				gspacketIn.Parameter1 = base.Id;
				gspacketIn.WriteByte(2);
				gspacketIn.WriteInt(num);
				gspacketIn.WriteBoolean(num > 0);
				gspacketIn.WriteByte(this.m_game.GetVane(num, 1));
				gspacketIn.WriteByte(this.m_game.GetVane(num, 2));
				gspacketIn.WriteByte(this.m_game.GetVane(num, 3));
				gspacketIn.WriteInt(bombCount);
				float num2 = 0f;
				SimpleBomb simpleBomb = null;
				for (int i = 0; i < bombCount; i++)
				{
					double num3 = 1.0;
					int num4 = 0;
					int num5 = i;
					int num6 = num5;
					if (num6 != 1)
					{
						if (num6 == 2)
						{
							num3 = 1.1;
							num4 = 5;
						}
					}
					else
					{
						num3 = 0.9;
						num4 = -5;
					}
					int num7 = (int)((double)force * num3 * Math.Cos((double)(angle + num4) / 180.0 * 3.141592653589793));
					int num8 = (int)((double)force * num3 * Math.Sin((double)(angle + num4) / 180.0 * 3.141592653589793));
					BaseGame game = this.m_game;
					int physicalId = game.PhysicalId;
					game.PhysicalId = physicalId + 1;
					SimpleBomb simpleBomb2 = new SimpleBomb(physicalId, ballType, this, this.m_game, ballInfo, tile, this.ControlBall, angle, false);
					simpleBomb2.SetXY(x, y);
					simpleBomb2.setSpeedXY(num7, num8);
					simpleBomb2.setAngle(angle);
					this.totalShotTurn = ((this.totalShotTurn == 0) ? (bombCount * shootCount) : this.totalShotTurn);
					this.m_map.AddPhysical(simpleBomb2);
					simpleBomb2.StartMoving();
					bool flag2 = i == 0;
					if (flag2)
					{
						simpleBomb = simpleBomb2;
					}
					gspacketIn.WriteInt(bombCount);
					gspacketIn.WriteInt(shootCount);
					gspacketIn.WriteBoolean(simpleBomb2.DigMap);
					gspacketIn.WriteInt(simpleBomb2.Id);
					gspacketIn.WriteInt(x);
					gspacketIn.WriteInt(y);
					gspacketIn.WriteInt(num7);
					gspacketIn.WriteInt(num8);
					gspacketIn.WriteInt(simpleBomb2.BallInfo.ID);
					bool flag3 = this.FlyingPartical != 0;
					if (flag3)
					{
						gspacketIn.WriteString(this.FlyingPartical.ToString());
					}
					else
					{
						gspacketIn.WriteString(ballInfo.FlyingPartical);
					}
					gspacketIn.WriteInt(simpleBomb2.BallInfo.Radii * 1000 / bombCount);
					gspacketIn.WriteInt((int)simpleBomb2.BallInfo.Power * 1000);
					gspacketIn.WriteInt(simpleBomb2.Actions.Count);
					foreach (BombAction bombAction in simpleBomb2.Actions)
					{
						gspacketIn.WriteInt(bombAction.TimeInt);
						gspacketIn.WriteInt(bombAction.Type);
						gspacketIn.WriteInt(bombAction.Param1);
						gspacketIn.WriteInt(bombAction.Param2);
						gspacketIn.WriteInt(bombAction.Param3);
						gspacketIn.WriteInt(bombAction.Param4);
					}
					num2 = Math.Max(num2, simpleBomb2.LifeTime);
				}
				int count = simpleBomb.PetActions.Count;
				bool flag4 = count > 0;
				if (flag4)
				{
					bool flag5 = simpleBomb.PetActions[0].Param1 == -1;
					if (flag5)
					{
						gspacketIn.WriteInt(0);
					}
					else
					{
						gspacketIn.WriteInt(count);
						foreach (BombAction bombAction2 in simpleBomb.PetActions)
						{
							gspacketIn.WriteInt(bombAction2.Param1);
							gspacketIn.WriteInt(bombAction2.Param2);
							gspacketIn.WriteInt(bombAction2.Param4);
							gspacketIn.WriteInt(bombAction2.Param3);
						}
					}
					gspacketIn.WriteInt(1);
				}
				else
				{
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(0);
				}
				this.m_game.SendToAll(gspacketIn);
				this.LastLifeTimeShoot = (int)((num2 + 2f + (float)(bombCount / 3)) * 1000f) + this.SpecialSkillDelay + this.PetEffects.Delay;
				this.m_game.WaitTime(this.LastLifeTimeShoot);
				bool flag6 = this.m_game.IsPVE();
				if (flag6)
				{
					((PVEGame)this.m_game).OnShooted();
				}
				flag7 = true;
			}
			else
			{
				flag7 = false;
			}
			return flag7;
		}

		// Token: 0x060075F3 RID: 30195 RVA: 0x0002B9DF File Offset: 0x00029BDF
		public void PlayMovie(string action, int delay, int MovieTime)
		{
			this.m_game.AddAction(new LivingPlayeMovieAction(this, action, delay, MovieTime, null));
		}

		// Token: 0x060075F4 RID: 30196 RVA: 0x0002B9F8 File Offset: 0x00029BF8
		public void PlayMovie(string action, int delay, int MovieTime, LivingCallBack call)
		{
			this.m_game.AddAction(new LivingPlayeMovieAction(this, action, delay, MovieTime, call));
		}

		// Token: 0x060075F5 RID: 30197 RVA: 0x0002BA12 File Offset: 0x00029C12
		public void BoltMove(int x, int y, int delay)
		{
			this.m_game.AddAction(new LivingBoltMoveAction(this, x, y, "", delay, 0));
		}

		// Token: 0x060075F6 RID: 30198 RVA: 0x00271DC8 File Offset: 0x0026FFC8
		public void SetSeal(bool state, int type)
		{
			bool flag = this.m_isSeal != state;
			if (flag)
			{
				this.m_isSeal = state;
				bool syncAtTime = this.m_syncAtTime;
				if (syncAtTime)
				{
					this.m_game.SendGameUpdateSealState(this, type);
				}
			}
			this.m_game.SendGamePlayerProperty(this, "silenceMany", state.ToString());
		}

		// Token: 0x060075F7 RID: 30199 RVA: 0x00271E24 File Offset: 0x00270024
		public void SetNiutou(bool state)
		{
			bool syncAtTime = this.m_syncAtTime;
			if (syncAtTime)
			{
				this.m_game.SendPlayerPicture(this, 33, state);
			}
		}

		// Token: 0x060075F8 RID: 30200 RVA: 0x00271E50 File Offset: 0x00270050
		public void SetTargeting(bool state)
		{
			bool syncAtTime = this.m_syncAtTime;
			if (syncAtTime)
			{
				this.m_game.SendPlayerPicture(this, 7, state);
			}
		}

		// Token: 0x060075F9 RID: 30201 RVA: 0x00271E7C File Offset: 0x0027007C
		public bool GetSealState()
		{
			return this.m_isSeal;
		}

		// Token: 0x060075FA RID: 30202 RVA: 0x0002BA30 File Offset: 0x00029C30
		public void Seal(Living target, int type, int delay)
		{
			this.m_game.AddAction(new LivingSealAction(this, target, type, delay));
		}

		// Token: 0x060075FB RID: 30203 RVA: 0x0002BA48 File Offset: 0x00029C48
		public void OffSeal(Living target, int delay)
		{
			this.m_game.AddAction(new LivingOffSealAction(this, target, delay));
		}

		// Token: 0x060075FC RID: 30204 RVA: 0x0002BA5F File Offset: 0x00029C5F
		public void AddRemoveEnergy(int value)
		{
			this.m_game.SendCurrentPlayerProperty(this, "energy", value.ToString());
		}

		// Token: 0x060075FD RID: 30205 RVA: 0x0002BA7B File Offset: 0x00029C7B
		public void NoFly(bool value)
		{
			this.m_game.SendCurrentPlayerProperty(this, "nofly", value.ToString());
		}

		// Token: 0x060075FE RID: 30206 RVA: 0x0002BA97 File Offset: 0x00029C97
		public void SetHidden(bool state)
		{
			this.m_game.SendGamePlayerProperty(this, "visible", state.ToString());
		}

		// Token: 0x060075FF RID: 30207 RVA: 0x0002BAB3 File Offset: 0x00029CB3
		public void SpeedMultX(int value)
		{
			this.m_game.SendGamePlayerProperty(this, "speedX", value.ToString());
		}

		// Token: 0x06007600 RID: 30208 RVA: 0x0002BACF File Offset: 0x00029CCF
		public void SpeedMultY(int value)
		{
			this.m_game.SendGamePlayerProperty(this, "speedY", value.ToString());
		}

		// Token: 0x06007601 RID: 30209 RVA: 0x0002BAEB File Offset: 0x00029CEB
		public void SetOffsetX(int value)
		{
			this.m_game.SendGamePlayerProperty(this, "offsetX", value.ToString());
		}

		// Token: 0x06007602 RID: 30210 RVA: 0x0002BB07 File Offset: 0x00029D07
		public void SetOffsetY(int value)
		{
			this.m_game.SendGamePlayerProperty(this, "offsetY", value.ToString());
		}

		// Token: 0x06007603 RID: 30211 RVA: 0x0002BB23 File Offset: 0x00029D23
		public void OnSmallMap(bool state)
		{
			this.m_game.SendGamePlayerProperty(this, "onSmallMap", state.ToString());
		}

		// Token: 0x06007604 RID: 30212 RVA: 0x0002BB3F File Offset: 0x00029D3F
		public void SetThumbnailBoss(bool state)
		{
			this.m_game.SendGamePlayerProperty(this, "hideBossThumbnail", state.ToString());
		}

		// Token: 0x06007605 RID: 30213 RVA: 0x0002BB5B File Offset: 0x00029D5B
		public void ShowImprisonment(bool state)
		{
			this.m_game.SendGamePlayerProperty(this, "showImprisonment", state.ToString());
		}

		// Token: 0x06007606 RID: 30214 RVA: 0x0002BB77 File Offset: 0x00029D77
		public void showProvoke1(bool state)
		{
			this.m_game.SendGamePlayerProperty(this, "showProvoke1", state.ToString());
		}

		// Token: 0x06007607 RID: 30215 RVA: 0x0002BB93 File Offset: 0x00029D93
		public void ShowProvoke2(bool state)
		{
			this.m_game.SendGamePlayerProperty(this, "showProvoke2", state.ToString());
		}

		// Token: 0x06007608 RID: 30216 RVA: 0x0002BBAF File Offset: 0x00029DAF
		public void ShowBlind(bool state)
		{
			this.m_game.SendGamePlayerProperty(this, "showBlind", state.ToString());
		}

		// Token: 0x06007609 RID: 30217 RVA: 0x0002BA97 File Offset: 0x00029C97
		public void SetVisible(bool state)
		{
			this.m_game.SendGamePlayerProperty(this, "visible", state.ToString());
		}

		// Token: 0x0600760A RID: 30218 RVA: 0x00271E94 File Offset: 0x00270094
		public virtual int AddBlood(int value)
		{
			bool flag = value > 0 && this.ForbidAidBlood;
			if (flag)
			{
				value = 0;
			}
			return this.AddBlood(value, 0);
		}

		// Token: 0x0600760B RID: 30219 RVA: 0x00271EC4 File Offset: 0x002700C4
		public virtual int AddBlood(int value, int type)
		{
			bool flag = value > 0 && this.ForbidAidBlood;
			if (flag)
			{
				value = 0;
			}
			this.m_blood += value;
			bool flag2 = this.m_blood > this.m_maxBlood;
			if (flag2)
			{
				this.m_blood = this.m_maxBlood;
			}
			bool syncAtTime = this.m_syncAtTime;
			if (syncAtTime)
			{
				this.m_game.SendGameUpdateHealth(this, type, value);
			}
			bool flag3 = this.m_blood <= 0;
			if (flag3)
			{
				this.Die();
			}
			return value;
		}

		// Token: 0x0600760C RID: 30220 RVA: 0x00271F54 File Offset: 0x00270154
		public virtual int AddMaxBlood(int value)
		{
			bool flag = value != 0;
			if (flag)
			{
				this.MaxBlood += value;
			}
			return value;
		}

		// Token: 0x0600760D RID: 30221 RVA: 0x00271F80 File Offset: 0x00270180
		public virtual bool TakeDamage(Living source, ref int damageAmount, ref int criticalAmount, string msg)
		{
			bool flag = this.Config.IsHelper && !this.Config.IsHelperVortex && (this is SimpleNpc || this is SimpleBoss) && source is Player;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = false;
				bool flag4 = !this.IsFrost && this.m_blood > 0;
				if (flag4)
				{
					bool flag5 = source != this || source.Team == this.Team;
					if (flag5)
					{
						this.OnBeforeTakedDamage(source, ref damageAmount, ref criticalAmount);
						this.StartAttacked();
					}
					bool flag6 = this.FightBuffers.CardReduceDamageLv1 > 0;
					if (flag6)
					{
						damageAmount -= this.FightBuffers.CardReduceDamageLv1;
					}
					bool flag7 = this.FightBuffers.CardReduceDamageLv2 > 0;
					if (flag7)
					{
						damageAmount -= this.FightBuffers.CardReduceDamageLv2;
					}
					bool flag8 = this.FightBuffers.CardReduceDamageLv3 > 0;
					if (flag8)
					{
						damageAmount -= this.FightBuffers.CardReduceDamageLv3;
					}
					bool flag9 = this.FightBuffers.CardReduceDamageLv4 > 0;
					if (flag9)
					{
						damageAmount -= this.FightBuffers.CardReduceDamageLv4;
					}
					bool flag10 = damageAmount < 0;
					if (flag10)
					{
						damageAmount = 0;
					}
					this.m_blood -= damageAmount + criticalAmount;
					bool flag11 = this.m_blood <= 0 && this.Config.KeepLife;
					if (flag11)
					{
						this.m_blood = 1;
						this.m_game.SendGameUpdateHealth(this, 1, 1);
					}
					bool flag12 = !(this is Player) && this.m_blood <= 0 && this.Config.MinBlood > 0;
					if (flag12)
					{
						this.m_blood = 1;
					}
					bool flag13 = source.WillIceForonze && source.IsLiving;
					if (flag13)
					{
						this.BeginNextTurn = (LivingEventHandle)Delegate.Combine(this.BeginNextTurn, new LivingEventHandle(this.SetIceFronze));
					}
					bool syncAtTime = this.m_syncAtTime;
					if (syncAtTime)
					{
						bool flag14 = this is SimpleBoss && (((SimpleBoss)this).NpcInfo.ID == 22 || ((SimpleBoss)this).NpcInfo.ID == 29);
						if (flag14)
						{
							this.m_game.SendGameUpdateHealth(this, 6, damageAmount + criticalAmount);
						}
						else
						{
							this.m_game.SendGameUpdateHealth(this, 1, damageAmount + criticalAmount);
						}
					}
					this.OnAfterTakedDamage(source, damageAmount, criticalAmount);
					bool flag15 = source.Prop4 >= 2;
					if (flag15)
					{
						bool flag16 = criticalAmount > 0 && source != this;
						if (flag16)
						{
							BaseGame game = this.m_game;
							eAcrobaciaType eAcrobaciaType = eAcrobaciaType.PeritoEmGolpes;
							int num = source.IdAcrobacias;
							source.IdAcrobacias = num + 1;
							game.AddAction(new FightAchievementAction(source, eAcrobaciaType, num, 1200));
							source.totalCritical++;
						}
						bool flag17 = source.totalCritical >= source.totalShotTurn;
						if (flag17)
						{
							BaseGame game2 = this.m_game;
							eAcrobaciaType eAcrobaciaType2 = eAcrobaciaType.ExplosaoDePersonagem;
							int num = source.IdAcrobacias;
							source.IdAcrobacias = num + 1;
							game2.AddAction(new FightAchievementAction(source, eAcrobaciaType2, num, 1200));
						}
					}
					bool flag18 = this.m_blood <= 0;
					if (flag18)
					{
						this.Die();
					}
					source.OnAfterKillingLiving(this, damageAmount, criticalAmount);
					flag3 = true;
				}
				this.EffectList.StopEffect(typeof(IceFronzeEffect));
				this.EffectList.StopEffect(typeof(HideEffect));
				this.EffectList.StopEffect(typeof(NoHoleEffect));
				flag2 = flag3;
			}
			return flag2;
		}

		// Token: 0x0600760E RID: 30222 RVA: 0x0002BBCB File Offset: 0x00029DCB
		public void SetIceFronze(Living living)
		{
			new IceFronzeEffect(2).Start(this);
			this.BeginNextTurn = (LivingEventHandle)Delegate.Remove(this.BeginNextTurn, new LivingEventHandle(this.SetIceFronze));
		}

		// Token: 0x0600760F RID: 30223 RVA: 0x00272318 File Offset: 0x00270518
		public virtual bool PetTakeDamage(Living source, ref int damageAmount, ref int criticalAmount, string msg)
		{
			bool flag = this.Config.IsHelper && (this is SimpleNpc || this is SimpleBoss);
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = false;
				bool flag4 = this.m_blood > 0;
				if (flag4)
				{
					this.m_blood -= damageAmount + criticalAmount;
					bool flag5 = this.m_blood <= 0;
					if (flag5)
					{
						bool keepLife = this.Config.KeepLife;
						if (keepLife)
						{
							this.m_blood = 1;
						}
						else
						{
							bool flag6 = this.Config.MinBlood > 0;
							if (flag6)
							{
								this.m_blood = 1;
							}
							else
							{
								this.Die();
							}
						}
					}
					flag3 = true;
				}
				flag2 = flag3;
			}
			return flag2;
		}

		// Token: 0x06007610 RID: 30224 RVA: 0x002723D8 File Offset: 0x002705D8
		public virtual void Die(int delay)
		{
			bool flag = base.IsLiving && this.m_game != null;
			if (flag)
			{
				this.m_game.AddAction(new LivingDieAction(this, delay));
			}
		}

		// Token: 0x06007611 RID: 30225 RVA: 0x00272414 File Offset: 0x00270614
		public override void Die()
		{
			bool flag = this is Player;
			if (flag)
			{
				bool flag2 = this.m_game is PVPGame;
				if (flag2)
				{
					(this.m_game as PVPGame).SendTempStyle();
				}
				bool flag3 = this.m_game is PVEGame;
				if (flag3)
				{
					(this.m_game as PVEGame).SendTempStyle(0);
				}
			}
			bool flag4 = this.m_blood > 0;
			if (flag4)
			{
				this.m_blood = 0;
				bool syncAtTime = this.m_syncAtTime;
				if (syncAtTime)
				{
					this.m_game.SendGameUpdateHealth(this, 6, 0);
				}
			}
			bool isLiving = base.IsLiving;
			if (isLiving)
			{
				bool isAttacking = this.IsAttacking;
				if (isAttacking)
				{
					this.StopAttacking();
				}
				base.Die();
				this.OnDied();
				this.m_game.CheckState(0);
			}
		}

		// Token: 0x06007612 RID: 30226 RVA: 0x0002BBFD File Offset: 0x00029DFD
		public override void Revive()
		{
			base.Revive();
		}

		// Token: 0x06007613 RID: 30227 RVA: 0x002724F0 File Offset: 0x002706F0
		protected void OnDied()
		{
			bool flag = this.Died != null;
			if (flag)
			{
				this.Died(this);
			}
			bool flag2 = this is Player && this.Game is PVEGame && (this as Player).IsActive;
			if (flag2)
			{
				((PVEGame)this.Game).DoOther();
			}
			bool flag3 = (this is SimpleBoss || this is SimpleNpc) && this.Game is PVEGame;
			if (flag3)
			{
				((PVEGame)this.Game).OnDied();
			}
		}

		// Token: 0x06007614 RID: 30228 RVA: 0x0027258C File Offset: 0x0027078C
		protected void OnBeforeTakedDamage(Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.BeforeTakeDamage != null;
			if (flag)
			{
				this.BeforeTakeDamage(this, source, ref damageAmount, ref criticalAmount);
			}
		}

		// Token: 0x06007615 RID: 30229 RVA: 0x002725BC File Offset: 0x002707BC
		public void OnTakedDamage(Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.TakePlayerDamage != null;
			if (flag)
			{
				this.TakePlayerDamage(this, source, ref damageAmount, ref criticalAmount);
			}
		}

		// Token: 0x06007616 RID: 30230 RVA: 0x002725EC File Offset: 0x002707EC
		protected void OnBeginNewTurn()
		{
			bool flag = this.BeginNextTurn != null;
			if (flag)
			{
				this.BeginNextTurn(this);
			}
		}

		// Token: 0x06007617 RID: 30231 RVA: 0x00272618 File Offset: 0x00270818
		protected void OnBeginSelfTurn()
		{
			bool flag = this.BeginSelfTurn != null;
			if (flag)
			{
				this.BeginSelfTurn(this);
			}
		}

		// Token: 0x06007618 RID: 30232 RVA: 0x00272644 File Offset: 0x00270844
		protected void OnStartAttacked()
		{
			bool flag = this.BeginAttacked != null;
			if (flag)
			{
				this.BeginAttacked(this);
			}
		}

		// Token: 0x06007619 RID: 30233 RVA: 0x00272670 File Offset: 0x00270870
		protected void OnStartAttacking()
		{
			bool flag = this.BeginAttacking != null;
			if (flag)
			{
				this.BeginAttacking(this);
			}
		}

		// Token: 0x0600761A RID: 30234 RVA: 0x0027269C File Offset: 0x0027089C
		protected void OnStopAttacking()
		{
			bool flag = this.EndAttacking != null;
			if (flag)
			{
				this.EndAttacking(this);
			}
		}

		// Token: 0x0600761B RID: 30235 RVA: 0x002726C8 File Offset: 0x002708C8
		protected void OnBeginUseProp()
		{
			bool flag = this.BeginUseProp != null;
			if (flag)
			{
				this.BeginUseProp(this);
			}
		}

		// Token: 0x0600761C RID: 30236 RVA: 0x002726F4 File Offset: 0x002708F4
		public virtual void OnAfterKillingLiving(Living target, int damageAmount, int criticalAmount)
		{
			bool flag = target.Team != this.Team;
			if (flag)
			{
				this.CurrentIsHitTarget = true;
				this.TotalHurt += damageAmount + criticalAmount;
				bool flag2 = !target.IsLiving;
				if (flag2)
				{
					this.TotalKill++;
				}
				this.m_game.CurrentTurnTotalDamage = damageAmount + criticalAmount;
				this.m_game.TotalHurt += damageAmount + criticalAmount;
			}
			bool flag3 = this.AfterKillingLiving != null;
			if (flag3)
			{
				this.AfterKillingLiving(this, target, damageAmount, criticalAmount);
			}
			bool flag4 = target.DefenceEffectTrigger && target is Player && (target as Player).DefenceInformation;
			if (flag4)
			{
				this.Game.SendMessage((target as Player).PlayerDetail, LanguageMgr.GetTranslation("PlayerEquipEffect.Success2", Array.Empty<object>()), LanguageMgr.GetTranslation("PlayerEquipEffect.Success3", new object[] { (target as Player).PlayerDetail.PlayerCharacter.NickName }), 3);
				(target as Player).DefenceInformation = false;
				target.DefenceEffectTrigger = false;
			}
		}

		// Token: 0x0600761D RID: 30237 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnAfterTakeDamage(Living source)
		{
		}

		// Token: 0x0600761E RID: 30238 RVA: 0x00272818 File Offset: 0x00270A18
		public void OnAfterTakedDamage(Living target, int damageAmount, int criticalAmount)
		{
			bool flag = this.AfterKilledByLiving != null;
			if (flag)
			{
				this.AfterKilledByLiving(this, target, damageAmount, criticalAmount);
			}
		}

		// Token: 0x0600761F RID: 30239 RVA: 0x00272848 File Offset: 0x00270A48
		public void OnMakeDamage(Living source, Living target, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.OnMakeDamageEvent != null;
			if (flag)
			{
				this.OnMakeDamageEvent(target, source, ref damageAmount, ref criticalAmount);
			}
		}

		// Token: 0x06007620 RID: 30240 RVA: 0x00272878 File Offset: 0x00270A78
		public void OnTakedPetDamage(Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.TakePetDamage != null;
			if (flag)
			{
				this.TakePetDamage(this, source, ref damageAmount, ref criticalAmount);
			}
		}

		// Token: 0x06007621 RID: 30241 RVA: 0x002728A8 File Offset: 0x00270AA8
		public void OnPropUseItem()
		{
			bool flag = this.PropUseItem != null;
			if (flag)
			{
				this.PropUseItem(this);
			}
		}

		// Token: 0x06007622 RID: 30242 RVA: 0x002728D4 File Offset: 0x00270AD4
		public void OnGameStarted()
		{
			bool flag = this.GameStarted != null;
			if (flag)
			{
				this.GameStarted(this);
			}
		}

		// Token: 0x06007623 RID: 30243 RVA: 0x00272900 File Offset: 0x00270B00
		public void OnGameStoped()
		{
			bool flag = this.GameStoped != null;
			if (flag)
			{
				this.GameStoped(this);
			}
		}

		// Token: 0x06007624 RID: 30244 RVA: 0x0027292C File Offset: 0x00270B2C
		public void CallFuction(LivingCallBack func, int delay)
		{
			bool flag = this.m_game != null;
			if (flag)
			{
				this.m_game.AddAction(new LivingCallFunctionAction(this, func, delay));
			}
		}

		// Token: 0x06007625 RID: 30245 RVA: 0x00272960 File Offset: 0x00270B60
		public virtual int RemoveBlood(int value)
		{
			return this.AddBlood(-value, 1);
		}

		// Token: 0x06007626 RID: 30246 RVA: 0x0027297C File Offset: 0x00270B7C
		public void SetSystemState(bool state)
		{
			bool flag = this.m_isSeal != state;
			if (flag)
			{
				this.m_isSeal = state;
			}
			this.m_game.SendGamePlayerProperty(this, "system", state.ToString());
		}

		// Token: 0x06007627 RID: 30247 RVA: 0x0000F4C1 File Offset: 0x0000D6C1
		internal void AddPetEffect(ContinueReduceBloodEffect continueReduceBloodEffect, int v)
		{
			throw new NotImplementedException();
		}

		// Token: 0x040044E2 RID: 17634
		protected BaseGame m_game;

		// Token: 0x040044E3 RID: 17635
		protected int m_maxBlood;

		// Token: 0x040044E4 RID: 17636
		protected int m_blood;

		// Token: 0x040044E5 RID: 17637
		private int m_team;

		// Token: 0x040044E6 RID: 17638
		private string m_name;

		// Token: 0x040044E7 RID: 17639
		public bool BlackTiger;

		// Token: 0x040044E8 RID: 17640
		public bool Devil;

		// Token: 0x040044E9 RID: 17641
		public int AttackGemLimit;

		// Token: 0x040044EA RID: 17642
		public bool canStopEffect;

		// Token: 0x040044EB RID: 17643
		public int miss;

		// Token: 0x040044EC RID: 17644
		public bool CanFrozen;

		// Token: 0x040044ED RID: 17645
		public double Guard = 0.0;

		// Token: 0x040044EE RID: 17646
		public double Speed = 0.0;

		// Token: 0x040044EF RID: 17647
		public double MagicDefence = 10.0;

		// Token: 0x040044F0 RID: 17648
		public bool ExtraDamageByY;

		// Token: 0x040044F1 RID: 17649
		public double MagicAttack = 10.0;

		// Token: 0x040044F2 RID: 17650
		public double FireDefence = 0.0;

		// Token: 0x040044F3 RID: 17651
		public double Tenacity = 0.0;

		// Token: 0x040044F4 RID: 17652
		public bool CanAttack = true;

		// Token: 0x040044F5 RID: 17653
		public bool WhiteTiger;

		// Token: 0x040044F6 RID: 17654
		public bool MustCrit;

		// Token: 0x040044F7 RID: 17655
		private string m_action;

		// Token: 0x040044F8 RID: 17656
		public bool PetKeepLife;

		// Token: 0x040044F9 RID: 17657
		public double PetReduceHealingAmountValue;

		// Token: 0x040044FA RID: 17658
		public bool IsReduceHealingAmount;

		// Token: 0x040044FB RID: 17659
		private string m_modelId;

		// Token: 0x040044FC RID: 17660
		private Rectangle m_demageRect;

		// Token: 0x040044FD RID: 17661
		private int m_state;

		// Token: 0x040044FE RID: 17662
		private int m_doAction;

		// Token: 0x040044FF RID: 17663
		private int m_degree;

		// Token: 0x04004500 RID: 17664
		public int m_direction;

		// Token: 0x04004501 RID: 17665
		private string m_createAction;

		// Token: 0x04004502 RID: 17666
		private eLivingType m_type;

		// Token: 0x04004503 RID: 17667
		private int m_specialSkillDelay;

		// Token: 0x04004504 RID: 17668
		public int IdAcrobacias;

		// Token: 0x04004505 RID: 17669
		private Random rand;

		// Token: 0x04004506 RID: 17670
		public double BaseDamage = 10.0;

		// Token: 0x04004507 RID: 17671
		public double BaseGuard = 10.0;

		// Token: 0x04004508 RID: 17672
		public double Defence = 10.0;

		// Token: 0x04004509 RID: 17673
		public double Attack = 10.0;

		// Token: 0x0400450A RID: 17674
		public double Agility = 10.0;

		// Token: 0x0400450B RID: 17675
		public double Lucky = 10.0;

		// Token: 0x0400450C RID: 17676
		public int Prop1;

		// Token: 0x0400450D RID: 17677
		public int Prop2;

		// Token: 0x0400450E RID: 17678
		public int Prop3;

		// Token: 0x0400450F RID: 17679
		public int Prop4;

		// Token: 0x04004510 RID: 17680
		public int Grade = 1;

		// Token: 0x04004511 RID: 17681
		public int Experience = 10;

		// Token: 0x04004512 RID: 17682
		public int LastLifeTimeShoot = 0;

		// Token: 0x04004513 RID: 17683
		public float CurrentDamagePlus;

		// Token: 0x04004514 RID: 17684
		public float CurrentShootMinus;

		// Token: 0x04004515 RID: 17685
		public bool IgnoreArmor;

		// Token: 0x04004516 RID: 17686
		public int IgnoreGuard;

		// Token: 0x04004517 RID: 17687
		public bool ControlBall;

		// Token: 0x04004518 RID: 17688
		public bool NoHoleTurn;

		// Token: 0x04004519 RID: 17689
		public bool CurrentIsHitTarget;

		// Token: 0x0400451A RID: 17690
		public int FlyingPartical;

		// Token: 0x0400451B RID: 17691
		public double chouhen = 0.0;

		// Token: 0x0400451C RID: 17692
		public int TurnNum;

		// Token: 0x0400451D RID: 17693
		public int TotalHurt;

		// Token: 0x0400451E RID: 17694
		public int TotalHitTargetCount;

		// Token: 0x0400451F RID: 17695
		public int TotalShootCount;

		// Token: 0x04004520 RID: 17696
		public int TotalKill;

		// Token: 0x04004521 RID: 17697
		public int TotalDameLiving;

		// Token: 0x04004522 RID: 17698
		public int TotalDamagePlayer;

		// Token: 0x04004523 RID: 17699
		public int TotalCure;

		// Token: 0x04004524 RID: 17700
		public int TotalDamageForMatch;

		// Token: 0x04004525 RID: 17701
		public int MaxBeatDis;

		// Token: 0x04004526 RID: 17702
		public int EffectsCount;

		// Token: 0x04004527 RID: 17703
		public int ShootMovieDelay;

		// Token: 0x04004528 RID: 17704
		public bool WillIceForonze;

		// Token: 0x04004529 RID: 17705
		public int EquipGhostReduceCritDamage;

		// Token: 0x0400452A RID: 17706
		private EffectList m_effectList;

		// Token: 0x0400452B RID: 17707
		private CardEffectList m_cardEffectList;

		// Token: 0x0400452C RID: 17708
		private LivingConfig m_config;

		// Token: 0x0400452D RID: 17709
		private FightBufferInfo m_fightBufferInfo;

		// Token: 0x0400452E RID: 17710
		private PetEffectInfo m_petEffects;

		// Token: 0x0400452F RID: 17711
		private PetEffectList m_petEffectList;

		// Token: 0x04004530 RID: 17712
		public bool AttackEffectTrigger;

		// Token: 0x04004531 RID: 17713
		public bool DefenceEffectTrigger;

		// Token: 0x04004532 RID: 17714
		public bool EffectTrigger;

		// Token: 0x04004533 RID: 17715
		public bool PetEffectTrigger;

		// Token: 0x04004534 RID: 17716
		protected bool m_syncAtTime;

		// Token: 0x04004535 RID: 17717
		public int totalCritical;

		// Token: 0x04004536 RID: 17718
		public int totalShotTurn;

		// Token: 0x04004537 RID: 17719
		private bool m_vaneOpen;

		// Token: 0x04004538 RID: 17720
		public bool TiredShoot;

		// Token: 0x04004539 RID: 17721
		public bool ClearBuff;

		// Token: 0x0400453A RID: 17722
		public bool LockMove;

		// Token: 0x0400453B RID: 17723
		public bool BombFoul;

		// Token: 0x0400453C RID: 17724
		public Dictionary<int, int> lastShots;

		// Token: 0x0400453D RID: 17725
		public bool AddArmor;

		// Token: 0x0400453E RID: 17726
		public int AddDamage;

		// Token: 0x0400453F RID: 17727
		public bool IsShowEffectA = true;

		// Token: 0x04004540 RID: 17728
		public bool IsShowEffectB = true;

		// Token: 0x04004541 RID: 17729
		public bool ForbidAidBlood;

		// Token: 0x04004542 RID: 17730
		public bool IsAddTurn;

		// Token: 0x04004543 RID: 17731
		public bool YHM_UseSkillPetWithProp = false;

		// Token: 0x04004544 RID: 17732
		private bool m_isAttacking;

		// Token: 0x04004545 RID: 17733
		protected static int MOVE_SPEED = 2;

		// Token: 0x04004546 RID: 17734
		protected static int GHOST_MOVE_SPEED = 8;

		// Token: 0x04004547 RID: 17735
		protected static int StepX = 1;

		// Token: 0x04004548 RID: 17736
		protected static int StepY = 3;

		// Token: 0x04004549 RID: 17737
		private bool m_isFrost;

		// Token: 0x0400454A RID: 17738
		private bool m_isHide;

		// Token: 0x0400454B RID: 17739
		private bool m_isNoHole;

		// Token: 0x0400454C RID: 17740
		private bool m_isSeal;

		// Token: 0x0400454D RID: 17741
		private bool m_isBlockTurn;
	}
}
