﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DF3 RID: 3571
	public class AE1189 : BasePetEffect
	{
		// Token: 0x06007D6E RID: 32110 RVA: 0x0029BF88 File Offset: 0x0029A188
		public AE1189(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1189, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D6F RID: 32111 RVA: 0x0029C008 File Offset: 0x0029A208
		public override bool Start(Living living)
		{
			AE1189 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1189) as AE1189;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D70 RID: 32112 RVA: 0x000304F2 File Offset: 0x0002E6F2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBeginMoving += this.Player_PlayerBeginMoving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007D71 RID: 32113 RVA: 0x0029C068 File Offset: 0x0029A268
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007D72 RID: 32114 RVA: 0x0029C098 File Offset: 0x0029A298
		private void Player_PlayerBeginMoving(Player player)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				CE1184 ce = player.PetEffectList.GetOfType(ePetEffectType.CE1184) as CE1184;
				bool flag = ce != null;
				if (flag)
				{
					ce.Stop();
				}
				CE1185 ce2 = player.PetEffectList.GetOfType(ePetEffectType.CE1185) as CE1185;
				bool flag2 = ce2 != null;
				if (flag2)
				{
					ce2.Stop();
				}
				CE1186 ce3 = player.PetEffectList.GetOfType(ePetEffectType.CE1186) as CE1186;
				bool flag3 = ce3 != null;
				if (flag3)
				{
					ce3.Stop();
				}
				CE1187 ce4 = player.PetEffectList.GetOfType(ePetEffectType.CE1187) as CE1187;
				bool flag4 = ce4 != null;
				if (flag4)
				{
					ce4.Stop();
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007D73 RID: 32115 RVA: 0x0003051B File Offset: 0x0002E71B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBeginMoving -= this.Player_PlayerBeginMoving;
		}

		// Token: 0x04004BDD RID: 19421
		private int m_type = 0;

		// Token: 0x04004BDE RID: 19422
		private int m_count = 0;

		// Token: 0x04004BDF RID: 19423
		private int m_probability = 0;

		// Token: 0x04004BE0 RID: 19424
		private int m_delay = 0;

		// Token: 0x04004BE1 RID: 19425
		private int m_coldDown = 0;

		// Token: 0x04004BE2 RID: 19426
		private int m_currentId;

		// Token: 0x04004BE3 RID: 19427
		private int m_added = 0;
	}
}
