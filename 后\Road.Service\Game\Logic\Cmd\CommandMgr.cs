﻿using System;
using System.Collections.Generic;
using System.Reflection;
using Game.Base.Events;

namespace Game.Logic.Cmd
{
	// Token: 0x02000EFE RID: 3838
	public class CommandMgr
	{
		// Token: 0x0600838F RID: 33679 RVA: 0x002B47B0 File Offset: 0x002B29B0
		public static ICommandHandler LoadCommandHandler(int code)
		{
			return CommandMgr.handles[code];
		}

		// Token: 0x06008390 RID: 33680 RVA: 0x00034740 File Offset: 0x00032940
		[ScriptLoadedEvent]
		public static void OnScriptCompiled(RoadEvent ev, object sender, EventArgs args)
		{
			CommandMgr.handles.Clear();
			CommandMgr.SearchCommandHandlers(Assembly.GetAssembly(typeof(BaseGame)));
		}

		// Token: 0x06008391 RID: 33681 RVA: 0x002B47D0 File Offset: 0x002B29D0
		protected static int SearchCommandHandlers(Assembly assembly)
		{
			int num = 0;
			Type[] types = assembly.GetTypes();
			Type[] array = types;
			Type[] array2 = array;
			foreach (Type type in array2)
			{
				bool flag = type.IsClass && !(type.GetInterface("Game.Logic.Cmd.ICommandHandler") == null);
				if (flag)
				{
					GameCommandAttribute[] array4 = (GameCommandAttribute[])type.GetCustomAttributes(typeof(GameCommandAttribute), true);
					bool flag2 = array4.Length != 0;
					if (flag2)
					{
						num++;
						CommandMgr.RegisterCommandHandler(array4[0].Code, Activator.CreateInstance(type) as ICommandHandler);
					}
				}
			}
			return num;
		}

		// Token: 0x06008392 RID: 33682 RVA: 0x00034763 File Offset: 0x00032963
		protected static void RegisterCommandHandler(int code, ICommandHandler handle)
		{
			CommandMgr.handles.Add(code, handle);
		}

		// Token: 0x04005221 RID: 21025
		private static Dictionary<int, ICommandHandler> handles = new Dictionary<int, ICommandHandler>();
	}
}
