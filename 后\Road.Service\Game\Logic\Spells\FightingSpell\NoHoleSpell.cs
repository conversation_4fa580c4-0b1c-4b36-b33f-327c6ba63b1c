﻿using System;
using Game.Logic.Effects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CC8 RID: 3272
	[SpellAttibute(9)]
	public class NoHoleSpell : ISpellHandler
	{
		// Token: 0x06007512 RID: 29970 RVA: 0x0026E940 File Offset: 0x0026CB40
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				new NoHoleEffect(item.Property3).Start(player);
			}
			else
			{
				bool flag = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
				if (flag)
				{
					new NoHoleEffect(item.Property3).Start(game.CurrentLiving);
				}
			}
		}
	}
}
