﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E11 RID: 3601
	public class AE1231 : BasePetEffect
	{
		// Token: 0x06007E0A RID: 32266 RVA: 0x0029EBA4 File Offset: 0x0029CDA4
		public AE1231(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1231, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E0B RID: 32267 RVA: 0x0029EC24 File Offset: 0x0029CE24
		public override bool Start(Living living)
		{
			AE1231 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1231) as AE1231;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E0C RID: 32268 RVA: 0x00030AF5 File Offset: 0x0002ECF5
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E0D RID: 32269 RVA: 0x00030B0B File Offset: 0x0002ED0B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E0E RID: 32270 RVA: 0x0029EC84 File Offset: 0x0029CE84
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1231(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004CAF RID: 19631
		private int m_type = 0;

		// Token: 0x04004CB0 RID: 19632
		private int m_count = 0;

		// Token: 0x04004CB1 RID: 19633
		private int m_probability = 0;

		// Token: 0x04004CB2 RID: 19634
		private int m_delay = 0;

		// Token: 0x04004CB3 RID: 19635
		private int m_coldDown = 0;

		// Token: 0x04004CB4 RID: 19636
		private int m_currentId;

		// Token: 0x04004CB5 RID: 19637
		private int m_added = 0;
	}
}
