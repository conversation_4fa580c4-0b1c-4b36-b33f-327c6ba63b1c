﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DD2 RID: 3538
	public class AE1127 : BasePetEffect
	{
		// Token: 0x06007CC1 RID: 31937 RVA: 0x00298D94 File Offset: 0x00296F94
		public AE1127(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1127, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CC2 RID: 31938 RVA: 0x00298E14 File Offset: 0x00297014
		public override bool Start(Living living)
		{
			AE1127 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1127) as AE1127;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CC3 RID: 31939 RVA: 0x0002FE29 File Offset: 0x0002E029
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007CC4 RID: 31940 RVA: 0x0002FE52 File Offset: 0x0002E052
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007CC5 RID: 31941 RVA: 0x00298E74 File Offset: 0x00297074
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 140;
				player.CurrentDamagePlus += (float)(this.m_added / 100);
			}
		}

		// Token: 0x06007CC6 RID: 31942 RVA: 0x00298EC0 File Offset: 0x002970C0
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_added / 100);
				this.m_added = 0;
			}
		}

		// Token: 0x04004AF6 RID: 19190
		private int m_type = 0;

		// Token: 0x04004AF7 RID: 19191
		private int m_count = 0;

		// Token: 0x04004AF8 RID: 19192
		private int m_probability = 0;

		// Token: 0x04004AF9 RID: 19193
		private int m_delay = 0;

		// Token: 0x04004AFA RID: 19194
		private int m_coldDown = 0;

		// Token: 0x04004AFB RID: 19195
		private int m_currentId;

		// Token: 0x04004AFC RID: 19196
		private int m_added = 0;
	}
}
