﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DBA RID: 3514
	public class AE1085 : BasePetEffect
	{
		// Token: 0x06007C43 RID: 31811 RVA: 0x002969C0 File Offset: 0x00294BC0
		public AE1085(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1085, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C44 RID: 31812 RVA: 0x00296A40 File Offset: 0x00294C40
		public override bool Start(Living living)
		{
			AE1085 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1085) as AE1085;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C45 RID: 31813 RVA: 0x0002F956 File Offset: 0x0002DB56
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C46 RID: 31814 RVA: 0x00296AA0 File Offset: 0x00294CA0
		protected override void OnPausedOnPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Lucky -= (double)this.m_added;
			this.IsTrigger = false;
		}

		// Token: 0x06007C47 RID: 31815 RVA: 0x00296AF0 File Offset: 0x00294CF0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && !this.IsTrigger;
			if (flag)
			{
				this.IsTrigger = true;
				this.m_added = 500;
				player.Lucky += (double)this.m_added;
				player.Game.SendPetBuff(player, base.ElementInfo, true);
			}
		}

		// Token: 0x04004A50 RID: 19024
		private int m_type = 0;

		// Token: 0x04004A51 RID: 19025
		private int m_count = 0;

		// Token: 0x04004A52 RID: 19026
		private int m_probability = 0;

		// Token: 0x04004A53 RID: 19027
		private int m_delay = 0;

		// Token: 0x04004A54 RID: 19028
		private int m_coldDown = 0;

		// Token: 0x04004A55 RID: 19029
		private int m_currentId;

		// Token: 0x04004A56 RID: 19030
		private int m_added = 0;
	}
}
