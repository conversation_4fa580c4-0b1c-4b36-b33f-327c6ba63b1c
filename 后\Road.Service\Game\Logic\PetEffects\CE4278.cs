﻿using System;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects
{
	// Token: 0x02000D52 RID: 3410
	public class CE4278 : BasePetEffect
	{
		// Token: 0x06007A1D RID: 31261 RVA: 0x0002E34C File Offset: 0x0002C54C
		public CE4278(int count, int skillId, string elementID)
			: base(ePetEffectType.CE4278, elementID)
		{
			this.m_count = count;
			this.m_element = elementID;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A1E RID: 31262 RVA: 0x0028D81C File Offset: 0x0028BA1C
		public override bool Start(Living living)
		{
			bool flag = !(living.PetEffectList.GetOfType(ePetEffectType.CE4278) is CE4278);
			return !flag || base.Start(living);
		}

		// Token: 0x06007A1F RID: 31263 RVA: 0x0002E37F File Offset: 0x0002C57F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
			player.PlayerBuffSkillPet += this.playerBuffSkillPet;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
		}

		// Token: 0x06007A20 RID: 31264 RVA: 0x0028D85C File Offset: 0x0028BA5C
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.m_added == 0 && living.BlackTiger && this.canreduce;
			if (flag)
			{
				this.m_added = 1;
				damageAmount = 0;
				criticalAmount = 0;
				living.Game.AddAction(new LivingSayAction(living, "免疫", 9, 0, 1000));
			}
		}

		// Token: 0x06007A21 RID: 31265 RVA: 0x0028D8B8 File Offset: 0x0028BAB8
		private void playerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.sendShowPicSkil(player, base.Info, true);
				this.canreduce = true;
			}
		}

		// Token: 0x06007A22 RID: 31266 RVA: 0x0028D8FC File Offset: 0x0028BAFC
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			this.m_added = 0;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.m_count = 1;
				this.canreduce = false;
			}
		}

		// Token: 0x06007A23 RID: 31267 RVA: 0x0002E3BB File Offset: 0x0002C5BB
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.PlayerBuffSkillPet -= this.playerBuffSkillPet;
		}

		// Token: 0x04004782 RID: 18306
		private string m_element;

		// Token: 0x04004783 RID: 18307
		private int m_count = 0;

		// Token: 0x04004784 RID: 18308
		private int m_added = 0;

		// Token: 0x04004785 RID: 18309
		private bool canreduce;

		// Token: 0x04004786 RID: 18310
		private int m_currentId;
	}
}
