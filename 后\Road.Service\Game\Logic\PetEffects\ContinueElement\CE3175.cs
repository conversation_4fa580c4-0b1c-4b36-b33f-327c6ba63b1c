﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EC2 RID: 3778
	public class CE3175 : BasePetEffect
	{
		// Token: 0x06008240 RID: 33344 RVA: 0x002B00F0 File Offset: 0x002AE2F0
		public CE3175(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE3175, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008241 RID: 33345 RVA: 0x002B016C File Offset: 0x002AE36C
		public override bool Start(Living living)
		{
			CE3175 ce = living.PetEffectList.GetOfType(ePetEffectType.CE3175) as CE3175;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008242 RID: 33346 RVA: 0x002B01C8 File Offset: 0x002AE3C8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			this.m_added = player.MaxBlood * 25 / 100;
			player.MaxBlood += this.m_added;
			player.Game.SendGameUpdateHealth(player, 1, 0);
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008243 RID: 33347 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008244 RID: 33348 RVA: 0x002B0234 File Offset: 0x002AE434
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.MaxBlood -= this.m_added;
				bool flag2 = living.Blood >= living.MaxBlood;
				if (flag2)
				{
					living.Blood = living.MaxBlood;
					living.Game.SendGameUpdateHealth(living, 1, 0);
				}
				else
				{
					living.Game.SendGameUpdateHealth(living, 1, 0);
				}
				living.Game.SendPetBuff(living, base.Info, false);
				this.Stop();
			}
		}

		// Token: 0x06008245 RID: 33349 RVA: 0x00033374 File Offset: 0x00031574
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x0400518C RID: 20876
		private int m_type = 0;

		// Token: 0x0400518D RID: 20877
		private int m_count = 0;

		// Token: 0x0400518E RID: 20878
		private int m_probability = 0;

		// Token: 0x0400518F RID: 20879
		private int m_delay = 0;

		// Token: 0x04005190 RID: 20880
		private int m_coldDown = 0;

		// Token: 0x04005191 RID: 20881
		private int m_currentId;

		// Token: 0x04005192 RID: 20882
		private int m_added = 0;
	}
}
