﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DBF RID: 3519
	public class AE1091 : BasePetEffect
	{
		// Token: 0x06007C5D RID: 31837 RVA: 0x00297234 File Offset: 0x00295434
		public AE1091(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1091, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C5E RID: 31838 RVA: 0x002972B4 File Offset: 0x002954B4
		public override bool Start(Living living)
		{
			AE1091 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1091) as AE1091;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C5F RID: 31839 RVA: 0x0002F9ED File Offset: 0x0002DBED
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBeginMoving += this.Player_PlayerBeginMoving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007C60 RID: 31840 RVA: 0x00297314 File Offset: 0x00295514
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007C61 RID: 31841 RVA: 0x00297344 File Offset: 0x00295544
		private void Player_PlayerBeginMoving(Player player)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				AE1086 ae = player.PetEffectList.GetOfType(ePetEffectType.AE1086) as AE1086;
				bool flag = ae != null;
				if (flag)
				{
					ae.Pause();
				}
				AE1087 ae2 = player.PetEffectList.GetOfType(ePetEffectType.AE1087) as AE1087;
				bool flag2 = ae2 != null;
				if (flag2)
				{
					ae2.Pause();
				}
				AE1088 ae3 = player.PetEffectList.GetOfType(ePetEffectType.AE1088) as AE1088;
				bool flag3 = ae3 != null;
				if (flag3)
				{
					ae3.Pause();
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007C62 RID: 31842 RVA: 0x0002FA16 File Offset: 0x0002DC16
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBeginMoving -= this.Player_PlayerBeginMoving;
		}

		// Token: 0x04004A73 RID: 19059
		private int m_type = 0;

		// Token: 0x04004A74 RID: 19060
		private int m_count = 0;

		// Token: 0x04004A75 RID: 19061
		private int m_probability = 0;

		// Token: 0x04004A76 RID: 19062
		private int m_delay = 0;

		// Token: 0x04004A77 RID: 19063
		private int m_coldDown = 0;

		// Token: 0x04004A78 RID: 19064
		private int m_currentId;

		// Token: 0x04004A79 RID: 19065
		private int m_added = 0;
	}
}
