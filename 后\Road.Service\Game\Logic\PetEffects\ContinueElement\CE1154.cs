﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E77 RID: 3703
	public class CE1154 : BasePetEffect
	{
		// Token: 0x0600805B RID: 32859 RVA: 0x002A8C44 File Offset: 0x002A6E44
		public CE1154(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1154, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600805C RID: 32860 RVA: 0x002A8CC4 File Offset: 0x002A6EC4
		public override bool Start(Living living)
		{
			CE1154 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1154) as CE1154;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600805D RID: 32861 RVA: 0x002A8D24 File Offset: 0x002A6F24
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				this.m_added = 70;
				player.BaseDamage += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600805E RID: 32862 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600805F RID: 32863 RVA: 0x002A8D98 File Offset: 0x002A6F98
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008060 RID: 32864 RVA: 0x002A8DCC File Offset: 0x002A6FCC
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BaseDamage -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F7A RID: 20346
		private int m_type = 0;

		// Token: 0x04004F7B RID: 20347
		private int m_count = 0;

		// Token: 0x04004F7C RID: 20348
		private int m_probability = 0;

		// Token: 0x04004F7D RID: 20349
		private int m_delay = 0;

		// Token: 0x04004F7E RID: 20350
		private int m_coldDown = 0;

		// Token: 0x04004F7F RID: 20351
		private int m_currentId;

		// Token: 0x04004F80 RID: 20352
		private int m_added = 0;
	}
}
