﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E81 RID: 3713
	public class CE1175 : BasePetEffect
	{
		// Token: 0x0600809B RID: 32923 RVA: 0x002A9A48 File Offset: 0x002A7C48
		public CE1175(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1175, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600809C RID: 32924 RVA: 0x002A9AC8 File Offset: 0x002A7CC8
		public override bool Start(Living living)
		{
			CE1175 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1175) as CE1175;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600809D RID: 32925 RVA: 0x00032483 File Offset: 0x00030683
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.Game.SendPetBuff(player, base.ElementInfo, true);
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600809E RID: 32926 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600809F RID: 32927 RVA: 0x002A9B28 File Offset: 0x002A7D28
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060080A0 RID: 32928 RVA: 0x000324C0 File Offset: 0x000306C0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004FC2 RID: 20418
		private int m_type = 0;

		// Token: 0x04004FC3 RID: 20419
		private int m_count = 0;

		// Token: 0x04004FC4 RID: 20420
		private int m_probability = 0;

		// Token: 0x04004FC5 RID: 20421
		private int m_delay = 0;

		// Token: 0x04004FC6 RID: 20422
		private int m_coldDown = 0;

		// Token: 0x04004FC7 RID: 20423
		private int m_currentId;

		// Token: 0x04004FC8 RID: 20424
		private int m_added = 0;
	}
}
