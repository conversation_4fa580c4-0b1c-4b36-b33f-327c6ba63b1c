﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D91 RID: 3473
	public class AE1018 : BasePetEffect
	{
		// Token: 0x06007B6F RID: 31599 RVA: 0x00292EBC File Offset: 0x002910BC
		public AE1018(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1018, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B70 RID: 31600 RVA: 0x00292F3C File Offset: 0x0029113C
		public override bool Start(Living living)
		{
			AE1018 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1018) as AE1018;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B71 RID: 31601 RVA: 0x0002F17F File Offset: 0x0002D37F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007B72 RID: 31602 RVA: 0x0002F195 File Offset: 0x0002D395
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007B73 RID: 31603 RVA: 0x00292F9C File Offset: 0x0029119C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1018(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004931 RID: 18737
		private int m_type = 0;

		// Token: 0x04004932 RID: 18738
		private int m_count = 0;

		// Token: 0x04004933 RID: 18739
		private int m_probability = 0;

		// Token: 0x04004934 RID: 18740
		private int m_delay = 0;

		// Token: 0x04004935 RID: 18741
		private int m_coldDown = 0;

		// Token: 0x04004936 RID: 18742
		private int m_currentId;

		// Token: 0x04004937 RID: 18743
		private int m_added = 0;
	}
}
