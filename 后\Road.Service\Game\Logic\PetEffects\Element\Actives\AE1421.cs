﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E2B RID: 3627
	public class AE1421 : BasePetEffect
	{
		// Token: 0x06007E97 RID: 32407 RVA: 0x002A1144 File Offset: 0x0029F344
		public AE1421(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1421, elementID)
		{
			this.m_count = -1;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E98 RID: 32408 RVA: 0x002A11C4 File Offset: 0x0029F3C4
		public override bool Start(Living living)
		{
			AE1421 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1421) as AE1421;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E99 RID: 32409 RVA: 0x0003110F File Offset: 0x0002F30F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.PlayerShoot += this.ChangeProperty;
		}

		// Token: 0x06007E9A RID: 32410 RVA: 0x00031138 File Offset: 0x0002F338
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.PlayerShoot -= this.ChangeProperty;
		}

		// Token: 0x06007E9B RID: 32411 RVA: 0x002A1224 File Offset: 0x0029F424
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 20;
				player.PetEffects.CritRate += this.m_added;
				this.m_count = this.m_coldDown;
				player.Game.SendPetBuff(player, base.ElementInfo, true);
			}
		}

		// Token: 0x06007E9C RID: 32412 RVA: 0x002A128C File Offset: 0x0029F48C
		private void ChangeProperty(Player player)
		{
			this.m_count--;
			bool flag = this.m_count == 0;
			if (flag)
			{
				player.PetEffects.CritRate -= this.m_added;
				this.m_added = 0;
				player.Game.SendPetBuff(player, base.ElementInfo, false);
			}
		}

		// Token: 0x04004D65 RID: 19813
		private int m_type = 0;

		// Token: 0x04004D66 RID: 19814
		private int m_count = 0;

		// Token: 0x04004D67 RID: 19815
		private int m_probability = 0;

		// Token: 0x04004D68 RID: 19816
		private int m_delay = 0;

		// Token: 0x04004D69 RID: 19817
		private int m_coldDown = 0;

		// Token: 0x04004D6A RID: 19818
		private int m_currentId;

		// Token: 0x04004D6B RID: 19819
		private int m_added = 0;
	}
}
