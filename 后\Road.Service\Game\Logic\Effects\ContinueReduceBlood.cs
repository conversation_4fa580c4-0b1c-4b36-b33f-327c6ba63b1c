﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EE4 RID: 3812
	public class ContinueReduceBlood : AbstractEffect
	{
		// Token: 0x06008303 RID: 33539 RVA: 0x00033E59 File Offset: 0x00032059
		public ContinueReduceBlood(int count, int blood, Living liv)
			: base(eEffectType.ContinueReduceBlood)
		{
			this.m_count = count;
			this.m_blood = blood;
			this.m_liv = liv;
		}

		// Token: 0x06008304 RID: 33540 RVA: 0x002B2640 File Offset: 0x002B0840
		public override bool Start(Living living)
		{
			ContinueReduceBlood continueReduceBlood = living.EffectList.GetOfType(eEffectType.ContinueReduceBlood) as ContinueReduceBlood;
			bool flag = continueReduceBlood != null;
			bool flag2;
			if (flag)
			{
				continueReduceBlood.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008305 RID: 33541 RVA: 0x00033E7A File Offset: 0x0003207A
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginFitting;
			living.Game.SendPlayerPicture(living, 28, true);
		}

		// Token: 0x06008306 RID: 33542 RVA: 0x00033EA0 File Offset: 0x000320A0
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginFitting;
			living.Game.SendPlayerPicture(living, 28, false);
		}

		// Token: 0x06008307 RID: 33543 RVA: 0x002B2688 File Offset: 0x002B0888
		private void player_BeginFitting(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				living.AddBlood(-this.m_blood, 1);
				bool flag2 = living.Blood <= 0;
				if (flag2)
				{
					living.Die();
					bool flag3 = this.m_liv != null && this.m_liv is Player;
					if (flag3)
					{
						(this.m_liv as Player).PlayerDetail.OnKillingLiving(this.m_liv.Game, 2, living.Id, living.IsLiving, this.m_blood);
					}
				}
			}
		}

		// Token: 0x040051F1 RID: 20977
		private int m_count;

		// Token: 0x040051F2 RID: 20978
		private int m_blood;

		// Token: 0x040051F3 RID: 20979
		private Living m_liv;
	}
}
