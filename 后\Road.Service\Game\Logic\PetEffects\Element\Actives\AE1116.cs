﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DCD RID: 3533
	public class AE1116 : BasePetEffect
	{
		// Token: 0x06007CA6 RID: 31910 RVA: 0x00298748 File Offset: 0x00296948
		public AE1116(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1116, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CA7 RID: 31911 RVA: 0x002987C4 File Offset: 0x002969C4
		public override bool Start(Living living)
		{
			AE1116 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1116) as AE1116;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CA8 RID: 31912 RVA: 0x0002FCC3 File Offset: 0x0002DEC3
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007CA9 RID: 31913 RVA: 0x0002FCD9 File Offset: 0x0002DED9
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007CAA RID: 31914 RVA: 0x00298820 File Offset: 0x00296A20
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && player.Game is PVPGame;
			if (flag)
			{
				player.AddPetEffect(new CE1116(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004AD5 RID: 19157
		private int m_type = 0;

		// Token: 0x04004AD6 RID: 19158
		private int m_count = 0;

		// Token: 0x04004AD7 RID: 19159
		private int m_probability = 0;

		// Token: 0x04004AD8 RID: 19160
		private int m_delay = 0;

		// Token: 0x04004AD9 RID: 19161
		private int m_coldDown = 0;

		// Token: 0x04004ADA RID: 19162
		private int m_currentId;

		// Token: 0x04004ADB RID: 19163
		private int m_added = 0;
	}
}
