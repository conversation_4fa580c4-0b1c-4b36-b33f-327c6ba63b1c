﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F57 RID: 3927
	public class LivingDirectSetXYAction : BaseAction
	{
		// Token: 0x06008506 RID: 34054 RVA: 0x00035416 File Offset: 0x00033616
		public LivingDirectSetXYAction(Living living, int x, int y, int delay)
			: base(delay)
		{
			this.m_living = living;
			this.m_x = x;
			this.m_y = y;
		}

		// Token: 0x06008507 RID: 34055 RVA: 0x00035437 File Offset: 0x00033637
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			this.m_living.SetXY(this.m_x, this.m_y);
			base.Finish(tick);
		}

		// Token: 0x040052F0 RID: 21232
		private Living m_living;

		// Token: 0x040052F1 RID: 21233
		private int m_x;

		// Token: 0x040052F2 RID: 21234
		private int m_y;
	}
}
