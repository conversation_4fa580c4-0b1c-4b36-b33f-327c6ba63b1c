﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.NormalSpell
{
	// Token: 0x02000CBC RID: 3260
	[SpellAttibute(90)]
	public class PassBallSpell : ISpellHandler
	{
		// Token: 0x060074FA RID: 29946 RVA: 0x0026E224 File Offset: 0x0026C424
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.SetBall(110);
			}
		}
	}
}
