﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E3D RID: 3645
	public class AE4857 : BasePetEffect
	{
		// Token: 0x06007EFB RID: 32507 RVA: 0x002A33C8 File Offset: 0x002A15C8
		public AE4857(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE4857, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007EFC RID: 32508 RVA: 0x002A3448 File Offset: 0x002A1648
		public override bool Start(Living living)
		{
			AE4857 ae = living.PetEffectList.GetOfType(ePetEffectType.AE4857) as AE4857;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007EFD RID: 32509 RVA: 0x0003152E File Offset: 0x0002F72E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EFE RID: 32510 RVA: 0x00031544 File Offset: 0x0002F744
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EFF RID: 32511 RVA: 0x002A34A8 File Offset: 0x002A16A8
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE8888(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004DE6 RID: 19942
		private int m_type = 0;

		// Token: 0x04004DE7 RID: 19943
		private int m_count = 0;

		// Token: 0x04004DE8 RID: 19944
		private int m_probability = 0;

		// Token: 0x04004DE9 RID: 19945
		private int m_delay = 0;

		// Token: 0x04004DEA RID: 19946
		private int m_coldDown = 0;

		// Token: 0x04004DEB RID: 19947
		private int m_currentId;

		// Token: 0x04004DEC RID: 19948
		private int m_added = 0;
	}
}
