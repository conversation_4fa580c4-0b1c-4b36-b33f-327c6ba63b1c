﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F4F RID: 3919
	public class LivingBeatAction : BaseAction
	{
		// Token: 0x060084F5 RID: 34037 RVA: 0x002B9358 File Offset: 0x002B7558
		public LivingBeatAction(Living living, Living target, int demageAmount, int criticalAmount, string action, int delay, int livingCount, int attackEffect)
			: base(delay)
		{
			this.m_living = living;
			this.m_target = target;
			this.m_demageAmount = demageAmount;
			this.m_criticalAmount = criticalAmount;
			this.m_action = action;
			this.m_livingCount = livingCount;
			this.m_attackEffect = attackEffect;
		}

		// Token: 0x060084F6 RID: 34038 RVA: 0x002B93A4 File Offset: 0x002B75A4
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			this.m_target.SyncAtTime = false;
			try
			{
				this.m_target.SyncAtTime = true;
				bool flag = this.m_target is Player;
				if (flag)
				{
					this.m_target.OnTakedDamage(this.m_target, ref this.m_demageAmount, ref this.m_criticalAmount);
				}
				bool flag2 = this.m_target.TakeDamage(this.m_living, ref this.m_demageAmount, ref this.m_criticalAmount, "LivingFire");
				if (flag2)
				{
					int num = this.m_demageAmount + this.m_criticalAmount;
					game.SendLivingBeat(this.m_living, this.m_target, num, this.m_action, this.m_livingCount, this.m_attackEffect);
				}
				this.m_target.IsFrost = false;
				base.Finish(tick);
			}
			finally
			{
			}
		}

		// Token: 0x040052D7 RID: 21207
		private Living m_living;

		// Token: 0x040052D8 RID: 21208
		private Living m_target;

		// Token: 0x040052D9 RID: 21209
		private int m_demageAmount;

		// Token: 0x040052DA RID: 21210
		private int m_criticalAmount;

		// Token: 0x040052DB RID: 21211
		private string m_action;

		// Token: 0x040052DC RID: 21212
		private int m_livingCount;

		// Token: 0x040052DD RID: 21213
		private int m_attackEffect;
	}
}
