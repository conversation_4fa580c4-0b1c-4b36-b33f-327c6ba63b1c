﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EB2 RID: 3762
	public class CE1250 : BasePetEffect
	{
		// Token: 0x060081D8 RID: 33240 RVA: 0x002AE60C File Offset: 0x002AC80C
		public CE1250(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1250, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081D9 RID: 33241 RVA: 0x002AE68C File Offset: 0x002AC88C
		public override bool Start(Living living)
		{
			CE1250 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1250) as CE1250;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081DA RID: 33242 RVA: 0x00033041 File Offset: 0x00031241
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081DB RID: 33243 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081DC RID: 33244 RVA: 0x002AE6EC File Offset: 0x002AC8EC
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			living.Game.SendPetBuff(living, base.ElementInfo, true);
			this.m_added = 350;
			damageAmount -= this.m_added;
			bool flag = damageAmount < 0;
			if (flag)
			{
				damageAmount = 1;
			}
		}

		// Token: 0x060081DD RID: 33245 RVA: 0x002AE734 File Offset: 0x002AC934
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060081DE RID: 33246 RVA: 0x0003307D File Offset: 0x0003127D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
		}

		// Token: 0x0400511B RID: 20763
		private int m_type = 0;

		// Token: 0x0400511C RID: 20764
		private int m_count = 0;

		// Token: 0x0400511D RID: 20765
		private int m_probability = 0;

		// Token: 0x0400511E RID: 20766
		private int m_delay = 0;

		// Token: 0x0400511F RID: 20767
		private int m_coldDown = 0;

		// Token: 0x04005120 RID: 20768
		private int m_currentId;

		// Token: 0x04005121 RID: 20769
		private int m_added = 0;
	}
}
