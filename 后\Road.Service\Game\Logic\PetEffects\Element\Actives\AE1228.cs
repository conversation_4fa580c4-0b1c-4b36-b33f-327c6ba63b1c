﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E0E RID: 3598
	public class AE1228 : BasePetEffect
	{
		// Token: 0x06007DFB RID: 32251 RVA: 0x0029E7D0 File Offset: 0x0029C9D0
		public AE1228(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1228, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DFC RID: 32252 RVA: 0x0029E850 File Offset: 0x0029CA50
		public override bool Start(Living living)
		{
			AE1228 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1228) as AE1228;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DFD RID: 32253 RVA: 0x00030A71 File Offset: 0x0002EC71
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DFE RID: 32254 RVA: 0x00030A87 File Offset: 0x0002EC87
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DFF RID: 32255 RVA: 0x0029E8B0 File Offset: 0x0029CAB0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1228(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004C9A RID: 19610
		private int m_type = 0;

		// Token: 0x04004C9B RID: 19611
		private int m_count = 0;

		// Token: 0x04004C9C RID: 19612
		private int m_probability = 0;

		// Token: 0x04004C9D RID: 19613
		private int m_delay = 0;

		// Token: 0x04004C9E RID: 19614
		private int m_coldDown = 0;

		// Token: 0x04004C9F RID: 19615
		private int m_currentId;

		// Token: 0x04004CA0 RID: 19616
		private int m_added = 0;
	}
}
