﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EC0 RID: 3776
	public class CE2965 : BasePetEffect
	{
		// Token: 0x06008231 RID: 33329 RVA: 0x002AFBE8 File Offset: 0x002ADDE8
		public CE2965(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE2965, elementID)
		{
			this.m_count = 3;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			bool flag = !(elementID == "2965");
			if (flag)
			{
				bool flag2 = elementID == "3085";
				if (flag2)
				{
					this.m_value = 50;
					this.m_value2 = 100;
				}
			}
			else
			{
				this.m_value = 30;
				this.m_value2 = 60;
			}
		}

		// Token: 0x06008232 RID: 33330 RVA: 0x002AFC84 File Offset: 0x002ADE84
		public override bool Start(Living living)
		{
			CE2965 ce = living.PetEffectList.GetOfType(ePetEffectType.CE2965) as CE2965;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008233 RID: 33331 RVA: 0x002AFCE4 File Offset: 0x002ADEE4
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
			player.PlayerBuffSkillHorse += this.Player_PlayerBuffSkillPet;
			player.OnMakeDamageEvent += this.OnMakeDamage;
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
		}

		// Token: 0x06008234 RID: 33332 RVA: 0x002AFD54 File Offset: 0x002ADF54
		private void Player_PlayerBuffSkillPet(Player player)
		{
			this.m_added++;
			bool flag = this.m_value == 30 && !this.istri && this.m_added == 3;
			if (flag)
			{
				this.m_count = 3;
				player.Game.sendShowPicSkil(player, base.Info, true);
				this.istri = true;
				this.m_added = 0;
			}
			bool flag2 = this.m_value == 50 && !this.istri && this.m_added % 2 == 0;
			if (flag2)
			{
				this.m_count = 3;
				player.Game.sendShowPicSkil(player, base.Info, true);
				this.istri = true;
				this.m_added = 0;
			}
		}

		// Token: 0x06008235 RID: 33333 RVA: 0x002AFE0C File Offset: 0x002AE00C
		private void OnMakeDamage(Living living, Living source, ref int damageamount, ref int criticalamount)
		{
			bool flag = this.istri && living.Game.RoomType != eRoomType.Element_PVE;
			if (flag)
			{
				bool flag2 = living.Game.IsPVE();
				if (flag2)
				{
					damageamount += damageamount * this.m_value2 / 100;
					criticalamount += criticalamount * this.m_value2 / 100;
				}
				else
				{
					damageamount += damageamount * this.m_value / 100;
				}
			}
		}

		// Token: 0x06008236 RID: 33334 RVA: 0x002AFE88 File Offset: 0x002AE088
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.istri;
			if (flag)
			{
				int num = damageAmount * 50 / 100;
				int num2 = criticalAmount * 50 / 100;
				damageAmount -= num;
				criticalAmount -= num2;
			}
		}

		// Token: 0x06008237 RID: 33335 RVA: 0x002AFEC4 File Offset: 0x002AE0C4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
			player.PlayerBuffSkillHorse -= this.Player_PlayerBuffSkillPet;
			player.OnMakeDamageEvent -= this.OnMakeDamage;
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
		}

		// Token: 0x06008238 RID: 33336 RVA: 0x002AFF34 File Offset: 0x002AE134
		private void Player_BeginSelfTurn(Living living)
		{
			bool flag = this.istri;
			if (flag)
			{
				this.m_count--;
			}
			bool flag2 = this.m_count < 0 && living is Player && this.istri;
			if (flag2)
			{
				this.istri = false;
				this.m_added = 0;
				this.m_count = 3;
				living.Game.sendShowPicSkil(living, base.Info, false);
			}
		}

		// Token: 0x0400517F RID: 20863
		private int m_count = 0;

		// Token: 0x04005180 RID: 20864
		private int m_probability = 0;

		// Token: 0x04005181 RID: 20865
		private int m_value;

		// Token: 0x04005182 RID: 20866
		private int m_value2;

		// Token: 0x04005183 RID: 20867
		private int m_added = 0;

		// Token: 0x04005184 RID: 20868
		private bool istri = false;
	}
}
