﻿using System;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CC4 RID: 3268
	[SpellAttibute(8)]
	public class BreachDefenceSpell : ISpellHandler
	{
		// Token: 0x0600750A RID: 29962 RVA: 0x0026E740 File Offset: 0x0026C940
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.IgnoreArmor = true;
				eAcrobaciaType eAcrobaciaType = eAcrobaciaType.IgnorarArmadura;
				int num = player.IdAcrobacias;
				player.IdAcrobacias = num + 1;
				game.AddAction(new FightAchievementAction(player, eAcrobaciaType, num, 1200));
			}
			else
			{
				bool flag = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
				if (flag)
				{
					game.CurrentLiving.IgnoreArmor = true;
					Living currentLiving = game.CurrentLiving;
					eAcrobaciaType eAcrobaciaType2 = eAcrobaciaType.IgnorarArmadura;
					int num = player.IdAcrobacias;
					player.IdAcrobacias = num + 1;
					game.AddAction(new FightAchievementAction(currentLiving, eAcrobaciaType2, num, 1200));
				}
			}
		}
	}
}
