﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills
{
	// Token: 0x02000CEA RID: 3306
	public class CE4283 : BasePetEffect
	{
		// Token: 0x060077EA RID: 30698 RVA: 0x0002C2F4 File Offset: 0x0002A4F4
		public CE4283(int count, string elementID)
			: base(ePetEffectType.CE4283, elementID)
		{
			this.m_count = count;
			this.m_element = elementID;
		}

		// Token: 0x060077EB RID: 30699 RVA: 0x0028340C File Offset: 0x0028160C
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.CE4283) is CE4283;
			return flag || base.Start(living);
		}

		// Token: 0x060077EC RID: 30700 RVA: 0x0002C319 File Offset: 0x0002A519
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
		}

		// Token: 0x060077ED RID: 30701 RVA: 0x00283448 File Offset: 0x00281648
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 10;
				living.miss += this.m_added;
			}
		}

		// Token: 0x060077EE RID: 30702 RVA: 0x0002C32F File Offset: 0x0002A52F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
			this.m_added = 0;
		}

		// Token: 0x0400463E RID: 17982
		private int m_count;

		// Token: 0x0400463F RID: 17983
		private int m_added;

		// Token: 0x04004640 RID: 17984
		private string m_element;

		// Token: 0x04004641 RID: 17985
		private bool Say = false;
	}
}
