﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D8A RID: 3466
	public class PE1435 : BasePetEffect
	{
		// Token: 0x06007B47 RID: 31559 RVA: 0x002926BC File Offset: 0x002908BC
		public PE1435(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1435, elementID)
		{
			this.m_count = count;
			this.m_coldDown = 3;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B48 RID: 31560 RVA: 0x0029273C File Offset: 0x0029093C
		public override bool Start(Living living)
		{
			PE1435 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1435) as PE1435;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B49 RID: 31561 RVA: 0x0002EEA8 File Offset: 0x0002D0A8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKilledByLiving += this.player_afterKilledByLiving;
			player.BeginNextTurn += this.player_beginNextTurn;
		}

		// Token: 0x06007B4A RID: 31562 RVA: 0x0002EED1 File Offset: 0x0002D0D1
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.player_afterKilledByLiving;
			player.BeginNextTurn -= this.player_beginNextTurn;
		}

		// Token: 0x06007B4B RID: 31563 RVA: 0x0029279C File Offset: 0x0029099C
		private void player_afterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = living.Game is PVPGame && this.m_coldDown > 0 && criticalAmount != 0;
			if (flag)
			{
				this.m_added = living.MaxBlood * 5 / 100;
				living.SyncAtTime = true;
				living.AddBlood(this.m_added);
				living.SyncAtTime = false;
				living.Game.SendPetBuff(living, base.ElementInfo, true);
				this.m_coldDown--;
			}
		}

		// Token: 0x06007B4C RID: 31564 RVA: 0x00292820 File Offset: 0x00290A20
		public void player_beginNextTurn(Living living)
		{
			bool flag = this.m_coldDown > 0 && this.m_added != 0;
			if (flag)
			{
				living.Game.SendPetBuff(living, base.ElementInfo, false);
				this.m_added = 0;
			}
		}

		// Token: 0x04004906 RID: 18694
		private int m_type = 0;

		// Token: 0x04004907 RID: 18695
		private int m_count = 0;

		// Token: 0x04004908 RID: 18696
		private int m_probability = 0;

		// Token: 0x04004909 RID: 18697
		private int m_delay = 0;

		// Token: 0x0400490A RID: 18698
		private int m_coldDown = 0;

		// Token: 0x0400490B RID: 18699
		private int m_currentId;

		// Token: 0x0400490C RID: 18700
		private int m_added = 0;
	}
}
