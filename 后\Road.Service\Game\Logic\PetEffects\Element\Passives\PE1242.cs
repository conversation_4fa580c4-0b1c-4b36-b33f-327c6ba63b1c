﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D7A RID: 3450
	public class PE1242 : BasePetEffect
	{
		// Token: 0x06007AF7 RID: 31479 RVA: 0x00291178 File Offset: 0x0028F378
		public PE1242(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1242, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AF8 RID: 31480 RVA: 0x002911F8 File Offset: 0x0028F3F8
		public override bool Start(Living living)
		{
			PE1242 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1242) as PE1242;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AF9 RID: 31481 RVA: 0x0002EBE8 File Offset: 0x0002CDE8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.player_beginNextTurn;
		}

		// Token: 0x06007AFA RID: 31482 RVA: 0x0002EBFE File Offset: 0x0002CDFE
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.player_beginNextTurn;
		}

		// Token: 0x06007AFB RID: 31483 RVA: 0x00291258 File Offset: 0x0028F458
		public void player_beginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 130;
				living.BaseGuard += (double)this.m_added;
			}
		}

		// Token: 0x04004896 RID: 18582
		private int m_type = 0;

		// Token: 0x04004897 RID: 18583
		private int m_count = 0;

		// Token: 0x04004898 RID: 18584
		private int m_probability = 0;

		// Token: 0x04004899 RID: 18585
		private int m_delay = 0;

		// Token: 0x0400489A RID: 18586
		private int m_coldDown = 0;

		// Token: 0x0400489B RID: 18587
		private int m_currentId;

		// Token: 0x0400489C RID: 18588
		private int m_added = 0;
	}
}
