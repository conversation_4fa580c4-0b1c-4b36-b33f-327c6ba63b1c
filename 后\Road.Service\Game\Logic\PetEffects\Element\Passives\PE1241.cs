﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D79 RID: 3449
	public class PE1241 : BasePetEffect
	{
		// Token: 0x06007AF2 RID: 31474 RVA: 0x0029105C File Offset: 0x0028F25C
		public PE1241(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1241, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AF3 RID: 31475 RVA: 0x002910DC File Offset: 0x0028F2DC
		public override bool Start(Living living)
		{
			PE1241 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1241) as PE1241;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AF4 RID: 31476 RVA: 0x0002EBBC File Offset: 0x0002CDBC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.player_beginNextTurn;
		}

		// Token: 0x06007AF5 RID: 31477 RVA: 0x0002EBD2 File Offset: 0x0002CDD2
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.player_beginNextTurn;
		}

		// Token: 0x06007AF6 RID: 31478 RVA: 0x0029113C File Offset: 0x0028F33C
		public void player_beginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 90;
				living.BaseGuard += (double)this.m_added;
			}
		}

		// Token: 0x0400488F RID: 18575
		private int m_type = 0;

		// Token: 0x04004890 RID: 18576
		private int m_count = 0;

		// Token: 0x04004891 RID: 18577
		private int m_probability = 0;

		// Token: 0x04004892 RID: 18578
		private int m_delay = 0;

		// Token: 0x04004893 RID: 18579
		private int m_coldDown = 0;

		// Token: 0x04004894 RID: 18580
		private int m_currentId;

		// Token: 0x04004895 RID: 18581
		private int m_added = 0;
	}
}
