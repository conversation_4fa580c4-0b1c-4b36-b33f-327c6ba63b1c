﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F1E RID: 3870
	public class AntCaveEffect : BaseCardEffect
	{
		// Token: 0x060083ED RID: 33773 RVA: 0x002B6040 File Offset: 0x002B4240
		public AntCaveEffect(int index, CardBuffInfo info)
			: base(eCardEffectType.AntCave, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x060083EE RID: 33774 RVA: 0x002B60B0 File Offset: 0x002B42B0
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.AntCave) is AntCaveEffect;
			return flag || base.Start(living);
		}

		// Token: 0x060083EF RID: 33775 RVA: 0x0003489E File Offset: 0x00032A9E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x060083F0 RID: 33776 RVA: 0x000348B4 File Offset: 0x00032AB4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x060083F1 RID: 33777 RVA: 0x002B60E8 File Offset: 0x002B42E8
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.AddMaxBlood(-this.m_value);
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 2;
			if (flag2)
			{
				player.AddMaxBlood(this.m_value);
				this.m_added = this.m_value;
				player.Game.SendPlayerPicture(player, 219, true);
				player.Game.SendMessage(player.PlayerDetail, "您激活了魔蚁巢穴套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活魔蚁巢穴套卡.", 3);
			}
		}

		// Token: 0x04005250 RID: 21072
		private int m_indexValue = 0;

		// Token: 0x04005251 RID: 21073
		private int m_value = 0;

		// Token: 0x04005252 RID: 21074
		private int m_added = 0;
	}
}
