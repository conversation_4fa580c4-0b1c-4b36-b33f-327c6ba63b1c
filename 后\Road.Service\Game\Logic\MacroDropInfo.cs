﻿using System;

namespace Game.Logic
{
	// Token: 0x02000CA2 RID: 3234
	public class MacroDropInfo
	{
		// Token: 0x170013D5 RID: 5077
		// (get) Token: 0x060073B5 RID: 29621 RVA: 0x0002B0B7 File Offset: 0x000292B7
		// (set) Token: 0x060073B6 RID: 29622 RVA: 0x0002B0BF File Offset: 0x000292BF
		public int SelfDropCount { get; set; }

		// Token: 0x170013D6 RID: 5078
		// (get) Token: 0x060073B7 RID: 29623 RVA: 0x0002B0C8 File Offset: 0x000292C8
		// (set) Token: 0x060073B8 RID: 29624 RVA: 0x0002B0D0 File Offset: 0x000292D0
		public int DropCount { get; set; }

		// Token: 0x170013D7 RID: 5079
		// (get) Token: 0x060073B9 RID: 29625 RVA: 0x0002B0D9 File Offset: 0x000292D9
		// (set) Token: 0x060073BA RID: 29626 RVA: 0x0002B0E1 File Offset: 0x000292E1
		public int MaxDropCount { get; set; }

		// Token: 0x060073BB RID: 29627 RVA: 0x0002B0EA File Offset: 0x000292EA
		public MacroDropInfo(int dropCount, int maxDropCount)
		{
			this.DropCount = dropCount;
			this.MaxDropCount = maxDropCount;
		}
	}
}
