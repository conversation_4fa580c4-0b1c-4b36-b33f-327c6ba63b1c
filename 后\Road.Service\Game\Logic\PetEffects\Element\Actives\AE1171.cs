﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DE2 RID: 3554
	public class AE1171 : BasePetEffect
	{
		// Token: 0x06007D19 RID: 32025 RVA: 0x0029A588 File Offset: 0x00298788
		public AE1171(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1171, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D1A RID: 32026 RVA: 0x0029A608 File Offset: 0x00298808
		public override bool Start(Living living)
		{
			AE1171 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1171) as AE1171;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D1B RID: 32027 RVA: 0x00030206 File Offset: 0x0002E406
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007D1C RID: 32028 RVA: 0x0029A668 File Offset: 0x00298868
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.AddPetEffect(new CE1171(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString(), player), 0);
				}
			}
		}

		// Token: 0x06007D1D RID: 32029 RVA: 0x0003021C File Offset: 0x0002E41C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004B66 RID: 19302
		private int m_type = 0;

		// Token: 0x04004B67 RID: 19303
		private int m_count = 0;

		// Token: 0x04004B68 RID: 19304
		private int m_probability = 0;

		// Token: 0x04004B69 RID: 19305
		private int m_delay = 0;

		// Token: 0x04004B6A RID: 19306
		private int m_coldDown = 0;

		// Token: 0x04004B6B RID: 19307
		private int m_currentId;

		// Token: 0x04004B6C RID: 19308
		private int m_added = 0;
	}
}
