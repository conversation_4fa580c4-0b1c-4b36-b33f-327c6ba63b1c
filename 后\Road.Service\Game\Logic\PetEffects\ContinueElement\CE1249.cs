﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EB1 RID: 3761
	public class CE1249 : BasePetEffect
	{
		// Token: 0x060081D1 RID: 33233 RVA: 0x002AE4B0 File Offset: 0x002AC6B0
		public CE1249(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1249, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081D2 RID: 33234 RVA: 0x002AE530 File Offset: 0x002AC730
		public override bool Start(Living living)
		{
			CE1249 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1249) as CE1249;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081D3 RID: 33235 RVA: 0x00032FC8 File Offset: 0x000311C8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081D4 RID: 33236 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081D5 RID: 33237 RVA: 0x002AE590 File Offset: 0x002AC790
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			living.Game.SendPetBuff(living, base.ElementInfo, true);
			this.m_added = 250;
			damageAmount -= this.m_added;
			bool flag = damageAmount < 0;
			if (flag)
			{
				damageAmount = 1;
			}
		}

		// Token: 0x060081D6 RID: 33238 RVA: 0x002AE5D8 File Offset: 0x002AC7D8
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060081D7 RID: 33239 RVA: 0x00033004 File Offset: 0x00031204
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
		}

		// Token: 0x04005114 RID: 20756
		private int m_type = 0;

		// Token: 0x04005115 RID: 20757
		private int m_count = 0;

		// Token: 0x04005116 RID: 20758
		private int m_probability = 0;

		// Token: 0x04005117 RID: 20759
		private int m_delay = 0;

		// Token: 0x04005118 RID: 20760
		private int m_coldDown = 0;

		// Token: 0x04005119 RID: 20761
		private int m_currentId;

		// Token: 0x0400511A RID: 20762
		private int m_added = 0;
	}
}
