﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D30 RID: 3376
	public class PetChangeDamageWind : AbstractPetEffect
	{
		// Token: 0x0600795B RID: 31067 RVA: 0x00289E64 File Offset: 0x00288064
		public PetChangeDamageWind(int skillId, string elementID)
			: base(ePetEffectType.PetChangeDamageWind, elementID)
		{
			this.m_skillID = skillId;
			if (skillId <= 573)
			{
				if (skillId != 572)
				{
					if (skillId == 573)
					{
						this.m_added = 30;
					}
				}
				else
				{
					this.m_added = 15;
				}
			}
			else if (skillId != 599)
			{
				if (skillId != 623)
				{
					if (skillId == 624)
					{
						this.m_added = 10;
					}
				}
				else
				{
					this.m_added = 5;
				}
			}
			else
			{
				this.m_added = 30;
				this.m_removed = 0;
			}
		}

		// Token: 0x0600795C RID: 31068 RVA: 0x00289F04 File Offset: 0x00288104
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetChangeDamageWind) is PetChangeDamageWind;
			return flag || base.Start(living);
		}

		// Token: 0x0600795D RID: 31069 RVA: 0x0002D838 File Offset: 0x0002BA38
		public override void OnAttached(Living living)
		{
			living.BeginNextTurn += this.player_BeginNextTurn;
			living.OnMakeDamageEvent += this.player_OnMakeDamageEvent;
		}

		// Token: 0x0600795E RID: 31070 RVA: 0x0002D861 File Offset: 0x0002BA61
		private void player_BeginNextTurn(Living living)
		{
			living.Game.sendShowPicSkil(living, base.ElementInfo, true);
			living.BeginNextTurn -= this.player_BeginNextTurn;
		}

		// Token: 0x0600795F RID: 31071 RVA: 0x00289F40 File Offset: 0x00288140
		private void player_OnMakeDamageEvent(Living living, Living target, ref int damageAmount, ref int criticalAmount)
		{
			float num = 0f;
			float wind = living.Game.Wind;
			float num2 = (float)target.Direction;
			float num3 = wind * num2;
			bool flag = num3 < 0f;
			if (flag)
			{
				Math.Abs(wind);
			}
			else
			{
				bool flag2 = num3 >= 0f;
				if (flag2)
				{
					num = (float)((int)(Math.Abs(wind) * (float)this.m_added));
				}
				else
				{
					damageAmount += (int)(num * (float)damageAmount / 100f);
				}
			}
		}

		// Token: 0x06007960 RID: 31072 RVA: 0x0002D88B File Offset: 0x0002BA8B
		public override void OnRemoved(Living living)
		{
			living.OnMakeDamageEvent -= this.player_OnMakeDamageEvent;
		}

		// Token: 0x04004718 RID: 18200
		private int m_added;

		// Token: 0x04004719 RID: 18201
		private int m_skillID;

		// Token: 0x0400471A RID: 18202
		private int m_removed = 10;
	}
}
