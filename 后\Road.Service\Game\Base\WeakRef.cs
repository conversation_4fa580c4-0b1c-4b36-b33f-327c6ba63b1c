﻿using System;

namespace Game.Base
{
	// Token: 0x02000F7F RID: 3967
	public class WeakRef : WeakReference
	{
		// Token: 0x170014CB RID: 5323
		// (get) Token: 0x060085FE RID: 34302 RVA: 0x002BD6D8 File Offset: 0x002BB8D8
		// (set) Token: 0x060085FF RID: 34303 RVA: 0x00035BBD File Offset: 0x00033DBD
		public override object Target
		{
			get
			{
				object target = base.Target;
				return (target == WeakRef.NULL) ? null : target;
			}
			set
			{
				base.Target = ((value == null) ? WeakRef.NULL : value);
			}
		}

		// Token: 0x06008600 RID: 34304 RVA: 0x00035BD2 File Offset: 0x00033DD2
		public WeakRef(object target)
			: base((target == null) ? WeakRef.NULL : target)
		{
		}

		// Token: 0x06008601 RID: 34305 RVA: 0x00035BE7 File Offset: 0x00033DE7
		public WeakRef(object target, bool trackResurrection)
			: base((target == null) ? WeakRef.NULL : target, trackResurrection)
		{
		}

		// Token: 0x0400538D RID: 21389
		private static readonly WeakRef.NullValue NULL = new WeakRef.NullValue();

		// Token: 0x02000F80 RID: 3968
		private class NullValue
		{
		}
	}
}
