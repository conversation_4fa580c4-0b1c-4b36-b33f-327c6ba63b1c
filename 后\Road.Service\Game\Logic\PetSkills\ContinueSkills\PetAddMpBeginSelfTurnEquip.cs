﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D04 RID: 3332
	public class PetAddMpBeginSelfTurnEquip : BasePetEffect
	{
		// Token: 0x06007871 RID: 30833 RVA: 0x0028636C File Offset: 0x0028456C
		public PetAddMpBeginSelfTurnEquip(int count, string elementID)
			: base(ePetEffectType.PetAddMpBeginSelfTurnEquip, elementID)
		{
			this.m_count = count;
			bool flag = elementID == "1789";
			if (flag)
			{
				this.m_percent = false;
				this.m_value = 20;
			}
		}

		// Token: 0x06007872 RID: 30834 RVA: 0x002863B0 File Offset: 0x002845B0
		public override bool Start(Living living)
		{
			PetAddMpBeginSelfTurnEquip petAddMpBeginSelfTurnEquip = living.PetEffectList.GetOfType(ePetEffectType.PetAddMpBeginSelfTurnEquip) as PetAddMpBeginSelfTurnEquip;
			bool flag = petAddMpBeginSelfTurnEquip != null;
			bool flag2;
			if (flag)
			{
				petAddMpBeginSelfTurnEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007873 RID: 30835 RVA: 0x0002CA1D File Offset: 0x0002AC1D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x06007874 RID: 30836 RVA: 0x0002CA33 File Offset: 0x0002AC33
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x06007875 RID: 30837 RVA: 0x002863F8 File Offset: 0x002845F8
		private void player_BeginSelfTurn(Living living)
		{
			bool flag = base.ElementInfo.Pic != -1;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
			}
			this.m_count--;
			bool flag2 = this.m_count < 0;
			if (flag2)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
			else
			{
				bool flag3 = living is Player;
				if (flag3)
				{
					bool flag4 = !this.m_percent;
					if (flag4)
					{
						(living as Player).AddPetMP(this.m_value);
					}
					else
					{
						(living as Player).AddPetMP((living as Player).PetMP / 100 * this.m_value);
					}
				}
			}
		}

		// Token: 0x0400469D RID: 18077
		private int m_count;

		// Token: 0x0400469E RID: 18078
		private bool m_percent;

		// Token: 0x0400469F RID: 18079
		private int m_value;
	}
}
