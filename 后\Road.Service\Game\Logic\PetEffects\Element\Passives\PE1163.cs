﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D68 RID: 3432
	public class PE1163 : BasePetEffect
	{
		// Token: 0x06007A9F RID: 31391 RVA: 0x0028FA5C File Offset: 0x0028DC5C
		public PE1163(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1163, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AA0 RID: 31392 RVA: 0x0028FADC File Offset: 0x0028DCDC
		public override bool Start(Living living)
		{
			PE1163 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1163) as PE1163;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AA1 RID: 31393 RVA: 0x0002E918 File Offset: 0x0002CB18
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007AA2 RID: 31394 RVA: 0x0028FB3C File Offset: 0x0028DD3C
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 150;
				living.Game.SendPetBuff(living, base.ElementInfo, true);
				living.Attack += (double)this.m_added;
			}
		}

		// Token: 0x06007AA3 RID: 31395 RVA: 0x0002E92E File Offset: 0x0002CB2E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x04004818 RID: 18456
		private int m_type = 0;

		// Token: 0x04004819 RID: 18457
		private int m_count = 0;

		// Token: 0x0400481A RID: 18458
		private int m_probability = 0;

		// Token: 0x0400481B RID: 18459
		private int m_delay = 0;

		// Token: 0x0400481C RID: 18460
		private int m_coldDown = 0;

		// Token: 0x0400481D RID: 18461
		private int m_currentId;

		// Token: 0x0400481E RID: 18462
		private int m_added = 0;
	}
}
