﻿using System;
using System.Collections.Specialized;
using System.Reflection;
using System.Threading;
using log4net;

namespace Game.Base.Events
{
	// Token: 0x02000F90 RID: 3984
	public class RoadEventHandlerCollection
	{
		// Token: 0x06008796 RID: 34710 RVA: 0x00035EF6 File Offset: 0x000340F6
		public RoadEventHandlerCollection()
		{
			this.m_lock = new ReaderWriterLock();
			this.m_events = new HybridDictionary();
		}

		// Token: 0x06008797 RID: 34711 RVA: 0x002C7B30 File Offset: 0x002C5D30
		public void AddHandler(RoadEvent e, RoadEventHandler del)
		{
			try
			{
				this.m_lock.AcquireWriterLock(3000);
				try
				{
					WeakMulticastDelegate weakMulticastDelegate = (WeakMulticastDelegate)this.m_events[e];
					bool flag = weakMulticastDelegate == null;
					if (flag)
					{
						this.m_events[e] = new WeakMulticastDelegate(del);
					}
					else
					{
						this.m_events[e] = WeakMulticastDelegate.Combine(weakMulticastDelegate, del);
					}
				}
				finally
				{
					this.m_lock.ReleaseWriterLock();
				}
			}
			catch (ApplicationException ex)
			{
				bool isErrorEnabled = RoadEventHandlerCollection.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					RoadEventHandlerCollection.log.Error("Failed to add event handler!", ex);
				}
			}
		}

		// Token: 0x06008798 RID: 34712 RVA: 0x002C7BF0 File Offset: 0x002C5DF0
		public void AddHandlerUnique(RoadEvent e, RoadEventHandler del)
		{
			try
			{
				this.m_lock.AcquireWriterLock(3000);
				try
				{
					WeakMulticastDelegate weakMulticastDelegate = (WeakMulticastDelegate)this.m_events[e];
					bool flag = weakMulticastDelegate == null;
					if (flag)
					{
						this.m_events[e] = new WeakMulticastDelegate(del);
					}
					else
					{
						this.m_events[e] = WeakMulticastDelegate.CombineUnique(weakMulticastDelegate, del);
					}
				}
				finally
				{
					this.m_lock.ReleaseWriterLock();
				}
			}
			catch (ApplicationException ex)
			{
				bool isErrorEnabled = RoadEventHandlerCollection.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					RoadEventHandlerCollection.log.Error("Failed to add event handler!", ex);
				}
			}
		}

		// Token: 0x06008799 RID: 34713 RVA: 0x002C7CB0 File Offset: 0x002C5EB0
		public void RemoveHandler(RoadEvent e, RoadEventHandler del)
		{
			try
			{
				this.m_lock.AcquireWriterLock(3000);
				try
				{
					WeakMulticastDelegate weakMulticastDelegate = (WeakMulticastDelegate)this.m_events[e];
					bool flag = weakMulticastDelegate != null;
					if (flag)
					{
						weakMulticastDelegate = WeakMulticastDelegate.Remove(weakMulticastDelegate, del);
						bool flag2 = weakMulticastDelegate == null;
						if (flag2)
						{
							this.m_events.Remove(e);
						}
						else
						{
							this.m_events[e] = weakMulticastDelegate;
						}
					}
				}
				finally
				{
					this.m_lock.ReleaseWriterLock();
				}
			}
			catch (ApplicationException ex)
			{
				bool isErrorEnabled = RoadEventHandlerCollection.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					RoadEventHandlerCollection.log.Error("Failed to remove event handler!", ex);
				}
			}
		}

		// Token: 0x0600879A RID: 34714 RVA: 0x002C7D78 File Offset: 0x002C5F78
		public void RemoveAllHandlers(RoadEvent e)
		{
			try
			{
				this.m_lock.AcquireWriterLock(3000);
				try
				{
					this.m_events.Remove(e);
				}
				finally
				{
					this.m_lock.ReleaseWriterLock();
				}
			}
			catch (ApplicationException ex)
			{
				bool isErrorEnabled = RoadEventHandlerCollection.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					RoadEventHandlerCollection.log.Error("Failed to remove event handlers!", ex);
				}
			}
		}

		// Token: 0x0600879B RID: 34715 RVA: 0x002C7E00 File Offset: 0x002C6000
		public void RemoveAllHandlers()
		{
			try
			{
				this.m_lock.AcquireWriterLock(3000);
				try
				{
					this.m_events.Clear();
				}
				finally
				{
					this.m_lock.ReleaseWriterLock();
				}
			}
			catch (ApplicationException ex)
			{
				bool isErrorEnabled = RoadEventHandlerCollection.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					RoadEventHandlerCollection.log.Error("Failed to remove all event handlers!", ex);
				}
			}
		}

		// Token: 0x0600879C RID: 34716 RVA: 0x00035F24 File Offset: 0x00034124
		public void Notify(RoadEvent e)
		{
			this.Notify(e, null, null);
		}

		// Token: 0x0600879D RID: 34717 RVA: 0x00035F31 File Offset: 0x00034131
		public void Notify(RoadEvent e, object sender)
		{
			this.Notify(e, sender, null);
		}

		// Token: 0x0600879E RID: 34718 RVA: 0x00035F3E File Offset: 0x0003413E
		public void Notify(RoadEvent e, EventArgs args)
		{
			this.Notify(e, null, args);
		}

		// Token: 0x0600879F RID: 34719 RVA: 0x002C7E84 File Offset: 0x002C6084
		public void Notify(RoadEvent e, object sender, EventArgs eArgs)
		{
			try
			{
				this.m_lock.AcquireReaderLock(3000);
				WeakMulticastDelegate weakMulticastDelegate;
				try
				{
					weakMulticastDelegate = (WeakMulticastDelegate)this.m_events[e];
				}
				finally
				{
					this.m_lock.ReleaseReaderLock();
				}
				if (weakMulticastDelegate != null)
				{
					weakMulticastDelegate.InvokeSafe(new object[] { e, sender, eArgs });
				}
			}
			catch (ApplicationException ex)
			{
				bool isErrorEnabled = RoadEventHandlerCollection.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					RoadEventHandlerCollection.log.Error("Failed to notify event handler!", ex);
				}
			}
		}

		// Token: 0x040053B5 RID: 21429
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040053B6 RID: 21430
		protected const int TIMEOUT = 3000;

		// Token: 0x040053B7 RID: 21431
		protected readonly ReaderWriterLock m_lock = null;

		// Token: 0x040053B8 RID: 21432
		protected readonly HybridDictionary m_events = null;
	}
}
