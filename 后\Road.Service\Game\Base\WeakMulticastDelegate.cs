﻿using System;
using System.Reflection;
using System.Text;
using log4net;

namespace Game.Base
{
	// Token: 0x02000F7E RID: 3966
	public class WeakMulticastDelegate
	{
		// Token: 0x060085EF RID: 34287 RVA: 0x002BD030 File Offset: 0x002BB230
		public WeakMulticastDelegate(Delegate realDelegate)
		{
			bool flag = realDelegate.Target != null;
			if (flag)
			{
				this.weakRef = new WeakRef(realDelegate.Target);
			}
			this.method = realDelegate.Method;
		}

		// Token: 0x060085F0 RID: 34288 RVA: 0x002BD088 File Offset: 0x002BB288
		public static WeakMulticastDelegate Combine(WeakMulticastDelegate weakDelegate, Delegate realDelegate)
		{
			bool flag = realDelegate == null;
			WeakMulticastDelegate weakMulticastDelegate;
			if (flag)
			{
				weakMulticastDelegate = null;
			}
			else
			{
				weakMulticastDelegate = ((weakDelegate == null) ? new WeakMulticastDelegate(realDelegate) : weakDelegate.Combine(realDelegate));
			}
			return weakMulticastDelegate;
		}

		// Token: 0x060085F1 RID: 34289 RVA: 0x002BD0BC File Offset: 0x002BB2BC
		public static WeakMulticastDelegate CombineUnique(WeakMulticastDelegate weakDelegate, Delegate realDelegate)
		{
			bool flag = realDelegate == null;
			WeakMulticastDelegate weakMulticastDelegate;
			if (flag)
			{
				weakMulticastDelegate = null;
			}
			else
			{
				weakMulticastDelegate = ((weakDelegate == null) ? new WeakMulticastDelegate(realDelegate) : weakDelegate.CombineUnique(realDelegate));
			}
			return weakMulticastDelegate;
		}

		// Token: 0x060085F2 RID: 34290 RVA: 0x002BD0F0 File Offset: 0x002BB2F0
		private WeakMulticastDelegate Combine(Delegate realDelegate)
		{
			this.prev = new WeakMulticastDelegate(realDelegate)
			{
				prev = this.prev
			};
			return this;
		}

		// Token: 0x060085F3 RID: 34291 RVA: 0x002BD120 File Offset: 0x002BB320
		protected bool Equals(Delegate realDelegate)
		{
			bool flag = this.weakRef == null;
			bool flag3;
			if (flag)
			{
				bool flag2 = realDelegate.Target == null && this.method == realDelegate.Method;
				flag3 = flag2;
			}
			else
			{
				bool flag4 = this.weakRef.Target == realDelegate.Target && this.method == realDelegate.Method;
				flag3 = flag4;
			}
			return flag3;
		}

		// Token: 0x060085F4 RID: 34292 RVA: 0x002BD19C File Offset: 0x002BB39C
		private WeakMulticastDelegate CombineUnique(Delegate realDelegate)
		{
			bool flag = this.Equals(realDelegate);
			bool flag2 = !flag && this.prev != null;
			if (flag2)
			{
				WeakMulticastDelegate weakMulticastDelegate = this.prev;
				while (!flag && weakMulticastDelegate != null)
				{
					bool flag3 = weakMulticastDelegate.Equals(realDelegate);
					if (flag3)
					{
						flag = true;
					}
					weakMulticastDelegate = weakMulticastDelegate.prev;
				}
			}
			return flag ? this : this.Combine(realDelegate);
		}

		// Token: 0x060085F5 RID: 34293 RVA: 0x002BD20C File Offset: 0x002BB40C
		public static WeakMulticastDelegate operator +(WeakMulticastDelegate d, Delegate realD)
		{
			return WeakMulticastDelegate.Combine(d, realD);
		}

		// Token: 0x060085F6 RID: 34294 RVA: 0x002BD228 File Offset: 0x002BB428
		public static WeakMulticastDelegate operator -(WeakMulticastDelegate d, Delegate realD)
		{
			return WeakMulticastDelegate.Remove(d, realD);
		}

		// Token: 0x060085F7 RID: 34295 RVA: 0x002BD244 File Offset: 0x002BB444
		public static WeakMulticastDelegate Remove(WeakMulticastDelegate weakDelegate, Delegate realDelegate)
		{
			bool flag = realDelegate == null || weakDelegate == null;
			WeakMulticastDelegate weakMulticastDelegate;
			if (flag)
			{
				weakMulticastDelegate = null;
			}
			else
			{
				weakMulticastDelegate = weakDelegate.Remove(realDelegate);
			}
			return weakMulticastDelegate;
		}

		// Token: 0x060085F8 RID: 34296 RVA: 0x002BD270 File Offset: 0x002BB470
		private WeakMulticastDelegate Remove(Delegate realDelegate)
		{
			bool flag = this.Equals(realDelegate);
			WeakMulticastDelegate weakMulticastDelegate;
			if (flag)
			{
				weakMulticastDelegate = this.prev;
			}
			else
			{
				WeakMulticastDelegate weakMulticastDelegate2 = this.prev;
				WeakMulticastDelegate weakMulticastDelegate3 = this;
				while (weakMulticastDelegate2 != null)
				{
					bool flag2 = weakMulticastDelegate2.Equals(realDelegate);
					if (flag2)
					{
						weakMulticastDelegate3.prev = weakMulticastDelegate2.prev;
						weakMulticastDelegate2.prev = null;
						break;
					}
					weakMulticastDelegate3 = weakMulticastDelegate2;
					weakMulticastDelegate2 = weakMulticastDelegate2.prev;
				}
				weakMulticastDelegate = this;
			}
			return weakMulticastDelegate;
		}

		// Token: 0x060085F9 RID: 34297 RVA: 0x002BD2DC File Offset: 0x002BB4DC
		public void Invoke(object[] args)
		{
			for (WeakMulticastDelegate weakMulticastDelegate = this; weakMulticastDelegate != null; weakMulticastDelegate = weakMulticastDelegate.prev)
			{
				int tickCount = Environment.TickCount;
				bool flag = weakMulticastDelegate.weakRef == null;
				if (flag)
				{
					weakMulticastDelegate.method.Invoke(null, args);
				}
				else
				{
					bool isAlive = weakMulticastDelegate.weakRef.IsAlive;
					if (isAlive)
					{
						weakMulticastDelegate.method.Invoke(weakMulticastDelegate.weakRef.Target, args);
					}
				}
				bool flag2 = Environment.TickCount - tickCount > 500 && WeakMulticastDelegate.log.IsWarnEnabled;
				if (flag2)
				{
					WeakMulticastDelegate.log.Warn("Invoke took " + (Environment.TickCount - tickCount).ToString() + "ms! " + weakMulticastDelegate.ToString());
				}
			}
		}

		// Token: 0x060085FA RID: 34298 RVA: 0x002BD3A8 File Offset: 0x002BB5A8
		public void InvokeSafe(object[] args)
		{
			for (WeakMulticastDelegate weakMulticastDelegate = this; weakMulticastDelegate != null; weakMulticastDelegate = weakMulticastDelegate.prev)
			{
				int tickCount = Environment.TickCount;
				try
				{
					bool flag = weakMulticastDelegate.weakRef == null;
					if (flag)
					{
						weakMulticastDelegate.method.Invoke(null, args);
					}
					else
					{
						bool isAlive = weakMulticastDelegate.weakRef.IsAlive;
						if (isAlive)
						{
							weakMulticastDelegate.method.Invoke(weakMulticastDelegate.weakRef.Target, args);
						}
					}
				}
				catch (Exception ex)
				{
					bool isErrorEnabled = WeakMulticastDelegate.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						WeakMulticastDelegate.log.Error("InvokeSafe", ex);
					}
				}
				bool flag2 = Environment.TickCount - tickCount > 500 && WeakMulticastDelegate.log.IsWarnEnabled;
				if (flag2)
				{
					WeakMulticastDelegate.log.Warn("InvokeSafe took " + (Environment.TickCount - tickCount).ToString() + "ms! " + weakMulticastDelegate.ToString());
				}
			}
		}

		// Token: 0x060085FB RID: 34299 RVA: 0x002BD4B4 File Offset: 0x002BB6B4
		public string Dump()
		{
			StringBuilder stringBuilder = new StringBuilder();
			WeakMulticastDelegate weakMulticastDelegate = this;
			int num = 0;
			while (weakMulticastDelegate != null)
			{
				num++;
				bool flag = weakMulticastDelegate.weakRef == null;
				if (flag)
				{
					stringBuilder.Append("\t");
					stringBuilder.Append(num);
					stringBuilder.Append(") ");
					stringBuilder.Append(weakMulticastDelegate.method.Name);
					stringBuilder.Append(Environment.NewLine);
				}
				else
				{
					bool isAlive = weakMulticastDelegate.weakRef.IsAlive;
					if (isAlive)
					{
						stringBuilder.Append("\t");
						stringBuilder.Append(num);
						stringBuilder.Append(") ");
						stringBuilder.Append(weakMulticastDelegate.weakRef.Target);
						stringBuilder.Append(".");
						stringBuilder.Append(weakMulticastDelegate.method.Name);
						stringBuilder.Append(Environment.NewLine);
					}
					else
					{
						stringBuilder.Append("\t");
						stringBuilder.Append(num);
						stringBuilder.Append(") INVALID.");
						stringBuilder.Append(weakMulticastDelegate.method.Name);
						stringBuilder.Append(Environment.NewLine);
					}
				}
				weakMulticastDelegate = weakMulticastDelegate.prev;
			}
			return stringBuilder.ToString();
		}

		// Token: 0x060085FC RID: 34300 RVA: 0x002BD5FC File Offset: 0x002BB7FC
		public override string ToString()
		{
			Type type = null;
			bool flag = this.method != null;
			if (flag)
			{
				type = this.method.DeclaringType;
			}
			object obj = null;
			bool flag2 = this.weakRef != null && this.weakRef.IsAlive;
			if (flag2)
			{
				obj = this.weakRef.Target;
			}
			return new StringBuilder(64).Append("method: ").Append((type == null) ? "(null)" : type.FullName).Append('.')
				.Append((this.method == null) ? "(null)" : this.method.Name)
				.Append(" target: ")
				.Append((obj == null) ? "null" : obj.ToString())
				.ToString();
		}

		// Token: 0x04005389 RID: 21385
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x0400538A RID: 21386
		private WeakReference weakRef = null;

		// Token: 0x0400538B RID: 21387
		private MethodInfo method = null;

		// Token: 0x0400538C RID: 21388
		private WeakMulticastDelegate prev = null;
	}
}
