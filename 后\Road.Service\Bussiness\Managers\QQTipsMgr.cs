﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FE4 RID: 4068
	public class QQTipsMgr
	{
		// Token: 0x06008B37 RID: 35639 RVA: 0x002FB3B4 File Offset: 0x002F95B4
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, QQTipsInfo> dictionary = new Dictionary<int, QQTipsInfo>();
				bool flag = QQTipsMgr.LoadItem(dictionary);
				if (flag)
				{
					QQTipsMgr.m_tips = dictionary;
					return true;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = QQTipsMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					QQTipsMgr.log.Error("ReLoad QQTips", ex);
				}
			}
			return false;
		}

		// Token: 0x06008B38 RID: 35640 RVA: 0x002FB420 File Offset: 0x002F9620
		public static bool Init()
		{
			bool flag;
			try
			{
				QQTipsMgr.m_tips = new Dictionary<int, QQTipsInfo>();
				QQTipsMgr.ReLoad();
				flag = QQTipsMgr.LoadItem(QQTipsMgr.m_tips);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = QQTipsMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					QQTipsMgr.log.Error("Init QQTips", ex);
				}
				flag = false;
			}
			return flag;
		}

		// Token: 0x06008B39 RID: 35641 RVA: 0x002FB484 File Offset: 0x002F9684
		public static bool LoadItem(Dictionary<int, QQTipsInfo> infos)
		{
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				QQTipsInfo[] allQQtipsMessagesLoad = produceBussiness.GetAllQQtipsMessagesLoad();
				QQTipsInfo[] array = allQQtipsMessagesLoad;
				QQTipsInfo[] array2 = array;
				QQTipsInfo[] array3 = array2;
				foreach (QQTipsInfo qqtipsInfo in array3)
				{
					bool flag = !infos.Keys.Contains(qqtipsInfo.ID);
					if (flag)
					{
						infos.Add(qqtipsInfo.ID, qqtipsInfo);
					}
				}
			}
			return true;
		}

		// Token: 0x06008B3A RID: 35642 RVA: 0x002FB51C File Offset: 0x002F971C
		public static QQTipsInfo GetQQtipsMessages()
		{
			bool flag = QQTipsMgr.m_tips == null;
			if (flag)
			{
				QQTipsMgr.Init();
			}
			List<QQTipsInfo> list = new List<QQTipsInfo>();
			foreach (QQTipsInfo qqtipsInfo in QQTipsMgr.m_tips.Values)
			{
				list.Add(qqtipsInfo);
			}
			bool flag2 = list.Count > 0;
			QQTipsInfo qqtipsInfo2;
			if (flag2)
			{
				int num = ThreadSafeRandom.NextStatic(list.Count);
				qqtipsInfo2 = list[num];
			}
			else
			{
				qqtipsInfo2 = null;
			}
			return qqtipsInfo2;
		}

		// Token: 0x04005538 RID: 21816
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005539 RID: 21817
		private static Dictionary<int, QQTipsInfo> m_tips;
	}
}
