﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EA4 RID: 3748
	public class CE1228 : BasePetEffect
	{
		// Token: 0x0600817B RID: 33147 RVA: 0x002AD188 File Offset: 0x002AB388
		public CE1228(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1228, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600817C RID: 33148 RVA: 0x002AD208 File Offset: 0x002AB408
		public override bool Start(Living living)
		{
			CE1228 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1228) as CE1228;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600817D RID: 33149 RVA: 0x00032BAA File Offset: 0x00030DAA
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600817E RID: 33150 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600817F RID: 33151 RVA: 0x002AD268 File Offset: 0x002AB468
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			this.m_added = 600;
			target.SyncAtTime = true;
			target.AddBlood(-this.m_added, 1);
			target.SyncAtTime = false;
			bool flag = target.Blood <= 0;
			if (flag)
			{
				target.Die();
				bool flag2 = living != null && living is Player;
				if (flag2)
				{
					(living as Player).PlayerDetail.OnKillingLiving(living.Game, 2, target.Id, target.IsLiving, this.m_added);
				}
			}
		}

		// Token: 0x06008180 RID: 33152 RVA: 0x002AD2F8 File Offset: 0x002AB4F8
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.m_added = 0;
				this.Stop();
			}
		}

		// Token: 0x06008181 RID: 33153 RVA: 0x00032BE6 File Offset: 0x00030DE6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x040050B9 RID: 20665
		private int m_type = 0;

		// Token: 0x040050BA RID: 20666
		private int m_count = 0;

		// Token: 0x040050BB RID: 20667
		private int m_probability = 0;

		// Token: 0x040050BC RID: 20668
		private int m_delay = 0;

		// Token: 0x040050BD RID: 20669
		private int m_coldDown = 0;

		// Token: 0x040050BE RID: 20670
		private int m_currentId;

		// Token: 0x040050BF RID: 20671
		private int m_added = 0;
	}
}
