﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D22 RID: 3362
	public class FireDefenceSkill : AbstractPetEffect
	{
		// Token: 0x06007910 RID: 30992 RVA: 0x0002D309 File Offset: 0x0002B509
		public FireDefenceSkill(int skillID, int value)
			: base(ePetEffectType.WaterDefenceSkill, "")
		{
			this.m_value = value;
			this.m_skillid = skillID;
		}

		// Token: 0x06007911 RID: 30993 RVA: 0x0002D32B File Offset: 0x0002B52B
		public override void OnAttached(Living player)
		{
			player.AfterBeingCrited += this.player_BeforeTakeDamage;
		}

		// Token: 0x06007912 RID: 30994 RVA: 0x0002D341 File Offset: 0x0002B541
		public override void OnRemoved(Living player)
		{
			player.AfterBeingCrited -= this.player_BeforeTakeDamage;
		}

		// Token: 0x06007913 RID: 30995 RVA: 0x002890E0 File Offset: 0x002872E0
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			double num = living.FireDefence / 200000.0;
			living.Tenacity = 0.0;
			bool flag = criticalAmount != 0;
			if (flag)
			{
				living.AddPetEffect(new PetGetShieldEquip(0, 0, 0, "3219"), 0);
				bool flag2 = damageAmount - (int)((double)living.MaxBlood * num) <= 0;
				if (flag2)
				{
					int num2 = -damageAmount + (int)((double)living.MaxBlood * num);
					damageAmount = 0;
					criticalAmount -= num2;
					bool flag3 = criticalAmount < 0;
					if (flag3)
					{
						criticalAmount = 0;
					}
				}
				else
				{
					damageAmount -= (int)((double)living.MaxBlood * num);
				}
			}
			bool flag4 = living.Blood <= living.MaxBlood * 25 / 100 || living.Blood <= source.MaxBlood / 2;
			if (flag4)
			{
				damageAmount -= damageAmount * 15 / 100;
			}
			criticalAmount -= criticalAmount * 15 / 100;
		}

		// Token: 0x040046EC RID: 18156
		private int m_value;

		// Token: 0x040046ED RID: 18157
		private int m_rate;

		// Token: 0x040046EE RID: 18158
		private int m_skillid;

		// Token: 0x040046EF RID: 18159
		private int m_added;

		// Token: 0x040046F0 RID: 18160
		private int damage;
	}
}
