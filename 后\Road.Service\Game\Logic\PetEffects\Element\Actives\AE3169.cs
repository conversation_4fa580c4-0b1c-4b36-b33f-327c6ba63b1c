﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E32 RID: 3634
	public class AE3169 : BasePetEffect
	{
		// Token: 0x06007EBE RID: 32446 RVA: 0x002A2388 File Offset: 0x002A0588
		public AE3169(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE3169, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007EBF RID: 32447 RVA: 0x002A2404 File Offset: 0x002A0604
		public override bool Start(Living living)
		{
			AE3169 ae = living.PetEffectList.GetOfType(ePetEffectType.AE3169) as AE3169;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007EC0 RID: 32448 RVA: 0x00031266 File Offset: 0x0002F466
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007EC1 RID: 32449 RVA: 0x0003128F File Offset: 0x0002F48F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007EC2 RID: 32450 RVA: 0x002A2460 File Offset: 0x002A0660
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 100;
				player.PetEffects.CritRate += this.m_added;
			}
		}

		// Token: 0x06007EC3 RID: 32451 RVA: 0x002A24A8 File Offset: 0x002A06A8
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.PetEffects.CritRate -= this.m_added;
				this.m_added = 0;
			}
		}

		// Token: 0x04004D99 RID: 19865
		private int m_type = 0;

		// Token: 0x04004D9A RID: 19866
		private int m_count = 0;

		// Token: 0x04004D9B RID: 19867
		private int m_probability = 0;

		// Token: 0x04004D9C RID: 19868
		private int m_delay = 0;

		// Token: 0x04004D9D RID: 19869
		private int m_coldDown = 0;

		// Token: 0x04004D9E RID: 19870
		private int m_currentId;

		// Token: 0x04004D9F RID: 19871
		private int m_added = 0;
	}
}
