﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E50 RID: 3664
	public class CE1033 : BasePetEffect
	{
		// Token: 0x06007F6C RID: 32620 RVA: 0x002A5168 File Offset: 0x002A3368
		public CE1033(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1033, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F6D RID: 32621 RVA: 0x002A51E4 File Offset: 0x002A33E4
		public override bool Start(Living living)
		{
			CE1033 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1033) as CE1033;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F6E RID: 32622 RVA: 0x000318E5 File Offset: 0x0002FAE5
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F6F RID: 32623 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F70 RID: 32624 RVA: 0x002A5240 File Offset: 0x002A3440
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			this.m_added = 200;
			damageAmount -= this.m_added;
			bool flag = damageAmount < 0;
			if (flag)
			{
				damageAmount = 1;
			}
		}

		// Token: 0x06007F71 RID: 32625 RVA: 0x002A5274 File Offset: 0x002A3474
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F72 RID: 32626 RVA: 0x00031921 File Offset: 0x0002FB21
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
		}

		// Token: 0x04004E6B RID: 20075
		private int m_type = 0;

		// Token: 0x04004E6C RID: 20076
		private int m_count = 0;

		// Token: 0x04004E6D RID: 20077
		private int m_probability = 0;

		// Token: 0x04004E6E RID: 20078
		private int m_delay = 0;

		// Token: 0x04004E6F RID: 20079
		private int m_coldDown = 0;

		// Token: 0x04004E70 RID: 20080
		private int m_currentId;

		// Token: 0x04004E71 RID: 20081
		private int m_added = 0;
	}
}
