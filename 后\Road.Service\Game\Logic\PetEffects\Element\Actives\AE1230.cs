﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E10 RID: 3600
	public class AE1230 : BasePetEffect
	{
		// Token: 0x06007E05 RID: 32261 RVA: 0x0029EA50 File Offset: 0x0029CC50
		public AE1230(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1230, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E06 RID: 32262 RVA: 0x0029EAD0 File Offset: 0x0029CCD0
		public override bool Start(Living living)
		{
			AE1230 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1230) as AE1230;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E07 RID: 32263 RVA: 0x00030AC9 File Offset: 0x0002ECC9
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E08 RID: 32264 RVA: 0x00030ADF File Offset: 0x0002ECDF
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E09 RID: 32265 RVA: 0x0029EB30 File Offset: 0x0029CD30
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1230(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004CA8 RID: 19624
		private int m_type = 0;

		// Token: 0x04004CA9 RID: 19625
		private int m_count = 0;

		// Token: 0x04004CAA RID: 19626
		private int m_probability = 0;

		// Token: 0x04004CAB RID: 19627
		private int m_delay = 0;

		// Token: 0x04004CAC RID: 19628
		private int m_coldDown = 0;

		// Token: 0x04004CAD RID: 19629
		private int m_currentId;

		// Token: 0x04004CAE RID: 19630
		private int m_added = 0;
	}
}
