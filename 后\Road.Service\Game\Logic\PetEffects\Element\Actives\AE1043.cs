﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DA5 RID: 3493
	public class AE1043 : BasePetEffect
	{
		// Token: 0x06007BD9 RID: 31705 RVA: 0x00294C18 File Offset: 0x00292E18
		public AE1043(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1043, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BDA RID: 31706 RVA: 0x00294C98 File Offset: 0x00292E98
		public override bool Start(Living living)
		{
			AE1043 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1043) as AE1043;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BDB RID: 31707 RVA: 0x0002F5D3 File Offset: 0x0002D7D3
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007BDC RID: 31708 RVA: 0x00294CF8 File Offset: 0x00292EF8
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1043(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007BDD RID: 31709 RVA: 0x0002F5E9 File Offset: 0x0002D7E9
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x040049BD RID: 18877
		private int m_type = 0;

		// Token: 0x040049BE RID: 18878
		private int m_count = 0;

		// Token: 0x040049BF RID: 18879
		private int m_probability = 0;

		// Token: 0x040049C0 RID: 18880
		private int m_delay = 0;

		// Token: 0x040049C1 RID: 18881
		private int m_coldDown = 0;

		// Token: 0x040049C2 RID: 18882
		private int m_currentId;

		// Token: 0x040049C3 RID: 18883
		private int m_added = 0;
	}
}
