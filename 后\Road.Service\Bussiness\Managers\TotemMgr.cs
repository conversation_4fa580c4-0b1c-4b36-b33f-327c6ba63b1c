﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FEF RID: 4079
	public class TotemMgr
	{
		// Token: 0x06008B80 RID: 35712 RVA: 0x002FCE04 File Offset: 0x002FB004
		public static bool Init()
		{
			bool flag;
			try
			{
				TotemMgr.m_totem = new Dictionary<int, TotemInfo>();
				flag = TotemMgr.Load(TotemMgr.m_totem);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = TotemMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					TotemMgr.log.Error("TotemMgr", ex);
				}
				flag = false;
			}
			return flag;
		}

		// Token: 0x06008B81 RID: 35713 RVA: 0x002FCE64 File Offset: 0x002FB064
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, TotemInfo> dictionary = new Dictionary<int, TotemInfo>();
				bool flag = TotemMgr.Load(dictionary);
				if (flag)
				{
					try
					{
						TotemMgr.m_totem = dictionary;
						return true;
					}
					catch
					{
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = TotemMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					TotemMgr.log.Error("TotemMgr", ex);
				}
			}
			return false;
		}

		// Token: 0x06008B82 RID: 35714 RVA: 0x002FCEE4 File Offset: 0x002FB0E4
		private static bool Load(Dictionary<int, TotemInfo> totem)
		{
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				TotemInfo[] allTotem = produceBussiness.GetAllTotem();
				TotemInfo[] array = allTotem;
				TotemInfo[] array2 = array;
				TotemInfo[] array3 = array2;
				foreach (TotemInfo totemInfo in array3)
				{
					bool flag = !totem.ContainsKey(totemInfo.ID);
					if (flag)
					{
						totem.Add(totemInfo.ID, totemInfo);
					}
				}
			}
			return true;
		}

		// Token: 0x06008B83 RID: 35715 RVA: 0x002FCF78 File Offset: 0x002FB178
		public static int MaxTotem()
		{
			return 10350;
		}

		// Token: 0x06008B84 RID: 35716 RVA: 0x002FCF90 File Offset: 0x002FB190
		public static TotemInfo FindTotemInfo(int id)
		{
			TotemMgr.m_clientLocker.AcquireWriterLock(-1);
			try
			{
				bool flag = TotemMgr.m_totem.ContainsKey(id);
				if (flag)
				{
					return TotemMgr.m_totem[id];
				}
			}
			finally
			{
				TotemMgr.m_clientLocker.ReleaseWriterLock();
			}
			return null;
		}

		// Token: 0x06008B85 RID: 35717 RVA: 0x002FCFF0 File Offset: 0x002FB1F0
		public static int GetTotemProp(int id, string typeOf, double ratePlus = 0.0)
		{
			int num = 0;
			for (int i = 10001; i <= id; i++)
			{
				TotemInfo totemInfo = TotemMgr.FindTotemInfo(i);
				uint num2 = <PrivateImplementationDetails>.ComputeStringHash(typeOf);
				if (num2 <= 1060448440U)
				{
					if (num2 != 727817140U)
					{
						if (num2 != 809586664U)
						{
							if (num2 == 1060448440U)
							{
								if (typeOf == "gua")
								{
									num += totemInfo.AddGuard;
								}
							}
						}
						else if (typeOf == "agi")
						{
							num += totemInfo.AddAgility;
						}
					}
					else if (typeOf == "att")
					{
						num += totemInfo.AddAttack;
					}
				}
				else if (num2 <= 1539579532U)
				{
					if (num2 != 1536304315U)
					{
						if (num2 == 1539579532U)
						{
							if (typeOf == "blo")
							{
								num += totemInfo.AddBlood;
							}
						}
					}
					else if (typeOf == "luc")
					{
						num += totemInfo.AddLuck;
					}
				}
				else if (num2 != 3310976652U)
				{
					if (num2 == 4031722721U)
					{
						if (typeOf == "dam")
						{
							num += totemInfo.AddDamage;
						}
					}
				}
				else if (typeOf == "def")
				{
					num += totemInfo.AddDefence;
				}
			}
			return num + (int)Math.Floor((double)num * (ratePlus / 100.0));
		}

		// Token: 0x04005556 RID: 21846
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005557 RID: 21847
		private static ReaderWriterLock m_clientLocker = new ReaderWriterLock();

		// Token: 0x04005558 RID: 21848
		private static Dictionary<int, TotemInfo> m_totem;
	}
}
