﻿namespace Game.Manager.Data
{
	// Token: 0x02000C7C RID: 3196
	public partial class EditDrop : global::System.Windows.Forms.Form
	{
		// Token: 0x06007109 RID: 28937 RVA: 0x00252764 File Offset: 0x00250964
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600710A RID: 28938 RVA: 0x0025279C File Offset: 0x0025099C
		private void InitializeComponent()
		{
			this.components = new global::System.ComponentModel.Container();
			global::System.ComponentModel.ComponentResourceManager componentResourceManager = new global::System.ComponentModel.ComponentResourceManager(typeof(global::Game.Manager.Data.EditDrop));
			this.EditDropBox = new global::System.Windows.Forms.DataGridView();
			this.Id = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.DropId = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.ItemId = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.ItemName = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.ValueDate = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Random = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.BeginData = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.EndData = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.IsBind = new global::System.Windows.Forms.DataGridViewCheckBoxColumn();
			this.IsTips = new global::System.Windows.Forms.DataGridViewCheckBoxColumn();
			this.IsLogs = new global::System.Windows.Forms.DataGridViewCheckBoxColumn();
			this.Name_Text = new global::System.Windows.Forms.TextBox();
			this.Name_Button = new global::System.Windows.Forms.Button();
			this.bdnInfo = new global::System.Windows.Forms.BindingNavigator(this.components);
			this.bindingNavigatorCountItem = new global::System.Windows.Forms.ToolStripLabel();
			this.bindingNavigatorMoveFirstItem = new global::System.Windows.Forms.ToolStripButton();
			this.bindingNavigatorMovePreviousItem = new global::System.Windows.Forms.ToolStripButton();
			this.bindingNavigatorSeparator = new global::System.Windows.Forms.ToolStripSeparator();
			this.bindingNavigatorPositionItem = new global::System.Windows.Forms.ToolStripTextBox();
			this.bindingNavigatorSeparator1 = new global::System.Windows.Forms.ToolStripSeparator();
			this.bindingNavigatorMoveNextItem = new global::System.Windows.Forms.ToolStripButton();
			this.bindingNavigatorMoveLastItem = new global::System.Windows.Forms.ToolStripButton();
			this.bindingNavigatorSeparator2 = new global::System.Windows.Forms.ToolStripSeparator();
			this.LastPage = new global::System.Windows.Forms.ToolStripButton();
			this.txtCurrentPage = new global::System.Windows.Forms.ToolStripLabel();
			this.toolStripLabel1 = new global::System.Windows.Forms.ToolStripLabel();
			this.lblPageCount = new global::System.Windows.Forms.ToolStripLabel();
			this.NextPage = new global::System.Windows.Forms.ToolStripButton();
			this.label1 = new global::System.Windows.Forms.Label();
			this.AddDrop = new global::System.Windows.Forms.Button();
			this.bdsInfo = new global::System.Windows.Forms.BindingSource(this.components);
			this.FindButton = new global::System.Windows.Forms.Button();
			((global::System.ComponentModel.ISupportInitialize)this.EditDropBox).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.bdnInfo).BeginInit();
			this.bdnInfo.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.bdsInfo).BeginInit();
			base.SuspendLayout();
			this.EditDropBox.AllowUserToAddRows = false;
			this.EditDropBox.AllowUserToDeleteRows = false;
			this.EditDropBox.AllowUserToResizeColumns = false;
			this.EditDropBox.AllowUserToResizeRows = false;
			this.EditDropBox.ColumnHeadersHeightSizeMode = global::System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.EditDropBox.Columns.AddRange(new global::System.Windows.Forms.DataGridViewColumn[]
			{
				this.Id, this.DropId, this.ItemId, this.ItemName, this.ValueDate, this.Random, this.BeginData, this.EndData, this.IsBind, this.IsTips,
				this.IsLogs
			});
			this.EditDropBox.EditMode = global::System.Windows.Forms.DataGridViewEditMode.EditOnEnter;
			this.EditDropBox.Location = new global::System.Drawing.Point(0, 42);
			this.EditDropBox.Name = "EditDropBox";
			this.EditDropBox.RowTemplate.Height = 23;
			this.EditDropBox.Size = new global::System.Drawing.Size(1146, 491);
			this.EditDropBox.TabIndex = 0;
			this.EditDropBox.CellEndEdit += new global::System.Windows.Forms.DataGridViewCellEventHandler(this.EditDropBox_CellEndEdit);
			this.Id.DataPropertyName = "Id";
			this.Id.HeaderText = "ID";
			this.Id.Name = "Id";
			this.Id.ReadOnly = true;
			this.Id.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.DropId.DataPropertyName = "DropId";
			this.DropId.HeaderText = "掉落ID";
			this.DropId.Name = "DropId";
			this.DropId.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.ItemId.DataPropertyName = "ItemId";
			this.ItemId.HeaderText = "物品ID";
			this.ItemId.Name = "ItemId";
			this.ItemId.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.ItemName.DataPropertyName = "ItemName";
			this.ItemName.HeaderText = "物品名称";
			this.ItemName.Name = "ItemName";
			this.ItemName.ReadOnly = true;
			this.ItemName.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.ValueDate.DataPropertyName = "ValueDate";
			this.ValueDate.HeaderText = "期限";
			this.ValueDate.Name = "ValueDate";
			this.ValueDate.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.Random.DataPropertyName = "Random";
			this.Random.HeaderText = "几率";
			this.Random.Name = "Random";
			this.Random.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.BeginData.DataPropertyName = "BeginData";
			this.BeginData.HeaderText = "最小数量";
			this.BeginData.Name = "BeginData";
			this.BeginData.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.EndData.DataPropertyName = "EndData";
			this.EndData.HeaderText = "最大数量";
			this.EndData.Name = "EndData";
			this.EndData.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.IsBind.DataPropertyName = "IsBind";
			this.IsBind.HeaderText = "是否绑定";
			this.IsBind.Name = "IsBind";
			this.IsTips.DataPropertyName = "IsTips";
			this.IsTips.HeaderText = "是否通知";
			this.IsTips.Name = "IsTips";
			this.IsLogs.DataPropertyName = "IsLogs";
			this.IsLogs.HeaderText = "是否记录";
			this.IsLogs.Name = "IsLogs";
			this.Name_Text.Location = new global::System.Drawing.Point(791, 13);
			this.Name_Text.Name = "Name_Text";
			this.Name_Text.Size = new global::System.Drawing.Size(168, 21);
			this.Name_Text.TabIndex = 11;
			this.Name_Text.TabStop = false;
			this.Name_Text.TextChanged += new global::System.EventHandler(this.Name_Text_TextChanged);
			this.Name_Button.Location = new global::System.Drawing.Point(965, 12);
			this.Name_Button.Name = "Name_Button";
			this.Name_Button.Size = new global::System.Drawing.Size(75, 23);
			this.Name_Button.TabIndex = 10;
			this.Name_Button.TabStop = false;
			this.Name_Button.Text = "查询";
			this.Name_Button.UseVisualStyleBackColor = true;
			this.Name_Button.Click += new global::System.EventHandler(this.Name_Button_Click);
			this.bdnInfo.AddNewItem = null;
			this.bdnInfo.AutoSize = false;
			this.bdnInfo.BackColor = global::System.Drawing.SystemColors.ActiveBorder;
			this.bdnInfo.BackgroundImageLayout = global::System.Windows.Forms.ImageLayout.Zoom;
			this.bdnInfo.CountItem = this.bindingNavigatorCountItem;
			this.bdnInfo.CountItemFormat = "/ 共{0}行";
			this.bdnInfo.DeleteItem = null;
			this.bdnInfo.Dock = global::System.Windows.Forms.DockStyle.None;
			this.bdnInfo.Font = new global::System.Drawing.Font("Microsoft YaHei UI", 9f);
			this.bdnInfo.ImeMode = global::System.Windows.Forms.ImeMode.NoControl;
			this.bdnInfo.Items.AddRange(new global::System.Windows.Forms.ToolStripItem[]
			{
				this.bindingNavigatorMoveFirstItem, this.bindingNavigatorMovePreviousItem, this.bindingNavigatorSeparator, this.bindingNavigatorPositionItem, this.bindingNavigatorCountItem, this.bindingNavigatorSeparator1, this.bindingNavigatorMoveNextItem, this.bindingNavigatorMoveLastItem, this.bindingNavigatorSeparator2, this.LastPage,
				this.txtCurrentPage, this.toolStripLabel1, this.lblPageCount, this.NextPage
			});
			this.bdnInfo.Location = new global::System.Drawing.Point(0, 537);
			this.bdnInfo.MoveFirstItem = this.bindingNavigatorMoveFirstItem;
			this.bdnInfo.MoveLastItem = this.bindingNavigatorMoveLastItem;
			this.bdnInfo.MoveNextItem = this.bindingNavigatorMoveNextItem;
			this.bdnInfo.MovePreviousItem = this.bindingNavigatorMovePreviousItem;
			this.bdnInfo.Name = "bdnInfo";
			this.bdnInfo.PositionItem = this.bindingNavigatorPositionItem;
			this.bdnInfo.RightToLeft = global::System.Windows.Forms.RightToLeft.No;
			this.bdnInfo.Size = new global::System.Drawing.Size(998, 42);
			this.bdnInfo.TabIndex = 13;
			this.bdnInfo.Text = "bindingNavigator1";
			this.bindingNavigatorCountItem.Name = "bindingNavigatorCountItem";
			this.bindingNavigatorCountItem.Size = new global::System.Drawing.Size(56, 39);
			this.bindingNavigatorCountItem.Text = "/ 共{0}行";
			this.bindingNavigatorCountItem.ToolTipText = "总项数";
			this.bindingNavigatorMoveFirstItem.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.bindingNavigatorMoveFirstItem.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("bindingNavigatorMoveFirstItem.Image");
			this.bindingNavigatorMoveFirstItem.Name = "bindingNavigatorMoveFirstItem";
			this.bindingNavigatorMoveFirstItem.RightToLeftAutoMirrorImage = true;
			this.bindingNavigatorMoveFirstItem.Size = new global::System.Drawing.Size(23, 39);
			this.bindingNavigatorMoveFirstItem.Text = "移到第一条记录";
			this.bindingNavigatorMovePreviousItem.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.bindingNavigatorMovePreviousItem.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("bindingNavigatorMovePreviousItem.Image");
			this.bindingNavigatorMovePreviousItem.Name = "bindingNavigatorMovePreviousItem";
			this.bindingNavigatorMovePreviousItem.RightToLeftAutoMirrorImage = true;
			this.bindingNavigatorMovePreviousItem.Size = new global::System.Drawing.Size(23, 39);
			this.bindingNavigatorMovePreviousItem.Text = "移到上一条记录";
			this.bindingNavigatorSeparator.Name = "bindingNavigatorSeparator";
			this.bindingNavigatorSeparator.Size = new global::System.Drawing.Size(6, 42);
			this.bindingNavigatorPositionItem.AccessibleName = "位置";
			this.bindingNavigatorPositionItem.AutoSize = false;
			this.bindingNavigatorPositionItem.Font = new global::System.Drawing.Font("Microsoft YaHei UI", 9f);
			this.bindingNavigatorPositionItem.Name = "bindingNavigatorPositionItem";
			this.bindingNavigatorPositionItem.Size = new global::System.Drawing.Size(50, 23);
			this.bindingNavigatorPositionItem.Text = "0";
			this.bindingNavigatorPositionItem.ToolTipText = "当前位置";
			this.bindingNavigatorSeparator1.Name = "bindingNavigatorSeparator1";
			this.bindingNavigatorSeparator1.Size = new global::System.Drawing.Size(6, 42);
			this.bindingNavigatorMoveNextItem.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.bindingNavigatorMoveNextItem.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("bindingNavigatorMoveNextItem.Image");
			this.bindingNavigatorMoveNextItem.Name = "bindingNavigatorMoveNextItem";
			this.bindingNavigatorMoveNextItem.RightToLeftAutoMirrorImage = true;
			this.bindingNavigatorMoveNextItem.Size = new global::System.Drawing.Size(23, 39);
			this.bindingNavigatorMoveNextItem.Text = "移到下一条记录";
			this.bindingNavigatorMoveLastItem.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.bindingNavigatorMoveLastItem.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("bindingNavigatorMoveLastItem.Image");
			this.bindingNavigatorMoveLastItem.Name = "bindingNavigatorMoveLastItem";
			this.bindingNavigatorMoveLastItem.RightToLeftAutoMirrorImage = true;
			this.bindingNavigatorMoveLastItem.Size = new global::System.Drawing.Size(23, 39);
			this.bindingNavigatorMoveLastItem.Text = "移到最后一条记录";
			this.bindingNavigatorSeparator2.Name = "bindingNavigatorSeparator2";
			this.bindingNavigatorSeparator2.Size = new global::System.Drawing.Size(6, 42);
			this.LastPage.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Text;
			this.LastPage.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("LastPage.Image");
			this.LastPage.ImageTransparentColor = global::System.Drawing.Color.Magenta;
			this.LastPage.Name = "LastPage";
			this.LastPage.Size = new global::System.Drawing.Size(48, 39);
			this.LastPage.Text = "上一页";
			this.LastPage.Click += new global::System.EventHandler(this.LastPage_Click);
			this.txtCurrentPage.Name = "txtCurrentPage";
			this.txtCurrentPage.Size = new global::System.Drawing.Size(15, 39);
			this.txtCurrentPage.Text = "1";
			this.toolStripLabel1.Name = "toolStripLabel1";
			this.toolStripLabel1.Size = new global::System.Drawing.Size(13, 39);
			this.toolStripLabel1.Text = "/";
			this.lblPageCount.Name = "lblPageCount";
			this.lblPageCount.Size = new global::System.Drawing.Size(29, 39);
			this.lblPageCount.Text = "100";
			this.NextPage.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Text;
			this.NextPage.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("NextPage.Image");
			this.NextPage.ImageTransparentColor = global::System.Drawing.Color.Magenta;
			this.NextPage.Name = "NextPage";
			this.NextPage.Size = new global::System.Drawing.Size(48, 39);
			this.NextPage.Text = "下一页";
			this.NextPage.Click += new global::System.EventHandler(this.NextPage_Click);
			this.label1.AutoSize = true;
			this.label1.Font = new global::System.Drawing.Font("宋体", 11f);
			this.label1.Location = new global::System.Drawing.Point(717, 16);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(68, 15);
			this.label1.TabIndex = 12;
			this.label1.Text = "掉落ID：";
			this.label1.Click += new global::System.EventHandler(this.label1_Click);
			this.AddDrop.Location = new global::System.Drawing.Point(1001, 537);
			this.AddDrop.Name = "AddDrop";
			this.AddDrop.Size = new global::System.Drawing.Size(145, 42);
			this.AddDrop.TabIndex = 14;
			this.AddDrop.Text = "添加掉落";
			this.AddDrop.UseVisualStyleBackColor = true;
			this.AddDrop.Click += new global::System.EventHandler(this.AddDrop_Click);
			this.FindButton.Location = new global::System.Drawing.Point(1046, 12);
			this.FindButton.Name = "FindButton";
			this.FindButton.Size = new global::System.Drawing.Size(75, 23);
			this.FindButton.TabIndex = 10;
			this.FindButton.TabStop = false;
			this.FindButton.Text = "模糊查询";
			this.FindButton.UseVisualStyleBackColor = true;
			this.FindButton.Click += new global::System.EventHandler(this.FindButton_Click);
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(6f, 12f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.AutoSizeMode = global::System.Windows.Forms.AutoSizeMode.GrowAndShrink;
			this.BackColor = global::System.Drawing.SystemColors.ActiveBorder;
			base.ClientSize = new global::System.Drawing.Size(1146, 582);
			base.Controls.Add(this.AddDrop);
			base.Controls.Add(this.Name_Text);
			base.Controls.Add(this.FindButton);
			base.Controls.Add(this.Name_Button);
			base.Controls.Add(this.bdnInfo);
			base.Controls.Add(this.label1);
			base.Controls.Add(this.EditDropBox);
			base.Icon = (global::System.Drawing.Icon)componentResourceManager.GetObject("$this.Icon");
			base.Name = "EditDrop";
			this.Text = "副本掉落编辑";
			base.Load += new global::System.EventHandler(this.EditDrop_Load);
			((global::System.ComponentModel.ISupportInitialize)this.EditDropBox).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.bdnInfo).EndInit();
			this.bdnInfo.ResumeLayout(false);
			this.bdnInfo.PerformLayout();
			((global::System.ComponentModel.ISupportInitialize)this.bdsInfo).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04003C9B RID: 15515
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04003C9C RID: 15516
		private global::System.Windows.Forms.DataGridView EditDropBox;

		// Token: 0x04003C9D RID: 15517
		private global::System.Windows.Forms.TextBox Name_Text;

		// Token: 0x04003C9E RID: 15518
		private global::System.Windows.Forms.Button Name_Button;

		// Token: 0x04003C9F RID: 15519
		private global::System.Windows.Forms.BindingNavigator bdnInfo;

		// Token: 0x04003CA0 RID: 15520
		private global::System.Windows.Forms.ToolStripLabel bindingNavigatorCountItem;

		// Token: 0x04003CA1 RID: 15521
		private global::System.Windows.Forms.ToolStripButton bindingNavigatorMoveFirstItem;

		// Token: 0x04003CA2 RID: 15522
		private global::System.Windows.Forms.ToolStripButton bindingNavigatorMovePreviousItem;

		// Token: 0x04003CA3 RID: 15523
		private global::System.Windows.Forms.ToolStripSeparator bindingNavigatorSeparator;

		// Token: 0x04003CA4 RID: 15524
		private global::System.Windows.Forms.ToolStripTextBox bindingNavigatorPositionItem;

		// Token: 0x04003CA5 RID: 15525
		private global::System.Windows.Forms.ToolStripSeparator bindingNavigatorSeparator1;

		// Token: 0x04003CA6 RID: 15526
		private global::System.Windows.Forms.ToolStripButton bindingNavigatorMoveNextItem;

		// Token: 0x04003CA7 RID: 15527
		private global::System.Windows.Forms.ToolStripButton bindingNavigatorMoveLastItem;

		// Token: 0x04003CA8 RID: 15528
		private global::System.Windows.Forms.ToolStripSeparator bindingNavigatorSeparator2;

		// Token: 0x04003CA9 RID: 15529
		private global::System.Windows.Forms.ToolStripButton LastPage;

		// Token: 0x04003CAA RID: 15530
		private global::System.Windows.Forms.ToolStripLabel txtCurrentPage;

		// Token: 0x04003CAB RID: 15531
		private global::System.Windows.Forms.ToolStripLabel toolStripLabel1;

		// Token: 0x04003CAC RID: 15532
		private global::System.Windows.Forms.ToolStripLabel lblPageCount;

		// Token: 0x04003CAD RID: 15533
		private global::System.Windows.Forms.ToolStripButton NextPage;

		// Token: 0x04003CAE RID: 15534
		private global::System.Windows.Forms.Label label1;

		// Token: 0x04003CAF RID: 15535
		private global::System.Windows.Forms.BindingSource bdsInfo;

		// Token: 0x04003CB0 RID: 15536
		private global::System.Windows.Forms.DataGridViewTextBoxColumn Id;

		// Token: 0x04003CB1 RID: 15537
		private global::System.Windows.Forms.DataGridViewTextBoxColumn DropId;

		// Token: 0x04003CB2 RID: 15538
		private global::System.Windows.Forms.DataGridViewTextBoxColumn ItemId;

		// Token: 0x04003CB3 RID: 15539
		private global::System.Windows.Forms.DataGridViewTextBoxColumn ItemName;

		// Token: 0x04003CB4 RID: 15540
		private global::System.Windows.Forms.DataGridViewTextBoxColumn ValueDate;

		// Token: 0x04003CB5 RID: 15541
		private global::System.Windows.Forms.DataGridViewTextBoxColumn Random;

		// Token: 0x04003CB6 RID: 15542
		private global::System.Windows.Forms.DataGridViewTextBoxColumn BeginData;

		// Token: 0x04003CB7 RID: 15543
		private global::System.Windows.Forms.DataGridViewTextBoxColumn EndData;

		// Token: 0x04003CB8 RID: 15544
		private global::System.Windows.Forms.DataGridViewCheckBoxColumn IsBind;

		// Token: 0x04003CB9 RID: 15545
		private global::System.Windows.Forms.DataGridViewCheckBoxColumn IsTips;

		// Token: 0x04003CBA RID: 15546
		private global::System.Windows.Forms.DataGridViewCheckBoxColumn IsLogs;

		// Token: 0x04003CBB RID: 15547
		private global::System.Windows.Forms.Button AddDrop;

		// Token: 0x04003CBC RID: 15548
		private global::System.Windows.Forms.Button FindButton;
	}
}
