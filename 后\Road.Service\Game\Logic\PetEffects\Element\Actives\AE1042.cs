﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DA4 RID: 3492
	public class AE1042 : BasePetEffect
	{
		// Token: 0x06007BD4 RID: 31700 RVA: 0x00294AC4 File Offset: 0x00292CC4
		public AE1042(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1042, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BD5 RID: 31701 RVA: 0x00294B44 File Offset: 0x00292D44
		public override bool Start(Living living)
		{
			AE1042 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1042) as AE1042;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BD6 RID: 31702 RVA: 0x0002F5A7 File Offset: 0x0002D7A7
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BD7 RID: 31703 RVA: 0x0002F5BD File Offset: 0x0002D7BD
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BD8 RID: 31704 RVA: 0x00294BA4 File Offset: 0x00292DA4
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1042(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x040049B6 RID: 18870
		private int m_type = 0;

		// Token: 0x040049B7 RID: 18871
		private int m_count = 0;

		// Token: 0x040049B8 RID: 18872
		private int m_probability = 0;

		// Token: 0x040049B9 RID: 18873
		private int m_delay = 0;

		// Token: 0x040049BA RID: 18874
		private int m_coldDown = 0;

		// Token: 0x040049BB RID: 18875
		private int m_currentId;

		// Token: 0x040049BC RID: 18876
		private int m_added = 0;
	}
}
