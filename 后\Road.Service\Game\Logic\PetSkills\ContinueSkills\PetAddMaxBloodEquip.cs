﻿using System;
using Game.Logic.Actions;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D03 RID: 3331
	public class PetAddMaxBloodEquip : AbstractPetEffect
	{
		// Token: 0x0600786B RID: 30827 RVA: 0x00285F30 File Offset: 0x00284130
		public PetAddMaxBloodEquip(int count, string elementID)
			: base(ePetEffectType.PetAddMaxBloodEquip, elementID)
		{
			this.m_count = count;
			bool flag = elementID == null;
			if (!flag)
			{
				int length = elementID.Length;
				bool flag2 = length != 4;
				if (!flag2)
				{
					switch (elementID[1])
					{
					case '1':
					{
						bool flag3 = elementID == "3175";
						if (flag3)
						{
							this.m_value = 25;
							this.m_percent = true;
							this.m_added1 = 100;
						}
						return;
					}
					case '2':
					case '5':
					case '6':
						return;
					case '3':
					{
						bool flag4 = !(elementID == "4397");
						if (flag4)
						{
							bool flag5 = elementID == "4399";
							if (flag5)
							{
								this.m_value = 40;
								this.m_count = 4;
								this.m_percent = true;
								this.m_added1 = 100;
							}
						}
						else
						{
							this.m_value = 25;
							this.m_count = 4;
							this.m_percent = true;
							this.m_added1 = 100;
						}
						return;
					}
					case '4':
					{
						bool flag6 = elementID == "4400";
						if (flag6)
						{
							this.m_value = 60;
							this.m_count = 4;
							this.m_percent = true;
							this.m_added1 = 100;
						}
						return;
					}
					case '7':
					{
						bool flag7 = !(elementID == "2755");
						if (!flag7)
						{
							this.m_value = 10;
							this.m_percent = true;
							this.m_added1 = 100;
							return;
						}
						bool flag8 = !(elementID == "1735");
						if (flag8)
						{
							return;
						}
						break;
					}
					case '8':
					{
						bool flag9 = !(elementID == "1839");
						if (flag9)
						{
							return;
						}
						break;
					}
					default:
						return;
					}
					this.m_value = 100;
					this.m_percent = true;
					this.m_added1 = 30;
				}
			}
		}

		// Token: 0x0600786C RID: 30828 RVA: 0x0028610C File Offset: 0x0028430C
		public override bool Start(Living living)
		{
			PetAddMaxBloodEquip petAddMaxBloodEquip = living.PetEffectList.GetOfType(ePetEffectType.PetAddMaxBloodEquip) as PetAddMaxBloodEquip;
			bool flag = petAddMaxBloodEquip != null;
			bool flag2;
			if (flag)
			{
				petAddMaxBloodEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600786D RID: 30829 RVA: 0x00286154 File Offset: 0x00284354
		private bool canAdd(Living living)
		{
			PetShowFireofResentmentEquip petShowFireofResentmentEquip = living.PetEffectList.GetOfType(ePetEffectType.PetShowFireofResentmentEquip) as PetShowFireofResentmentEquip;
			PetShowFireOfHatredEquip petShowFireOfHatredEquip = living.PetEffectList.GetOfType(ePetEffectType.PetShowFireOfHatredEquip) as PetShowFireOfHatredEquip;
			bool flag = petShowFireofResentmentEquip != null || petShowFireOfHatredEquip != null;
			bool flag2;
			if (flag)
			{
				if (petShowFireofResentmentEquip != null)
				{
					petShowFireofResentmentEquip.Stop();
				}
				if (petShowFireOfHatredEquip != null)
				{
					petShowFireOfHatredEquip.Stop();
				}
				flag2 = true;
			}
			else
			{
				flag2 = false;
			}
			return flag2;
		}

		// Token: 0x0600786E RID: 30830 RVA: 0x002861C0 File Offset: 0x002843C0
		public override void OnAttached(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = living.MaxBlood * this.m_value / 100;
				living.AddMaxBlood(this.m_added);
				bool flag2 = this.canAdd(living);
				if (flag2)
				{
					living.AddBlood(this.m_added * this.m_added1 * 2 / 100);
					living.Game.AddAction(new LivingSayAction(living, "幽怨之火", 9, 0, 1000));
					living.Game.SendGameUpdateHealth(living, 0, 2 * this.m_added * this.m_added1 / 100);
				}
				else
				{
					living.AddBlood(this.m_added * this.m_added1 / 100);
					living.Game.SendGameUpdateHealth(living, 0, this.m_added * this.m_added1 / 100);
				}
			}
			living.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x0600786F RID: 30831 RVA: 0x002862B4 File Offset: 0x002844B4
		public override void OnRemoved(Living living)
		{
			living.AddMaxBlood(-this.m_added);
			bool flag = living.Blood >= living.MaxBlood;
			if (flag)
			{
				living.Blood = living.MaxBlood;
			}
			living.Game.SendGameUpdateHealth(living, 1, living.Blood);
			this.m_added = 0;
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x06007870 RID: 30832 RVA: 0x00286324 File Offset: 0x00284524
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
		}

		// Token: 0x04004697 RID: 18071
		private int m_count;

		// Token: 0x04004698 RID: 18072
		private int m_value;

		// Token: 0x04004699 RID: 18073
		private bool m_percent;

		// Token: 0x0400469A RID: 18074
		private int m_added;

		// Token: 0x0400469B RID: 18075
		private int m_added1;

		// Token: 0x0400469C RID: 18076
		private int m_added2;
	}
}
