﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using System.Xml;
using ProtoBuf;
using zlib;

namespace Game.Base
{
	// Token: 0x02000F7A RID: 3962
	public class Marshal
	{
		// Token: 0x060085A6 RID: 34214 RVA: 0x002BBF98 File Offset: 0x002BA198
		public static string ConvertToString(byte[] cstyle)
		{
			bool flag = cstyle == null;
			string text;
			if (flag)
			{
				text = null;
			}
			else
			{
				for (int i = 0; i < cstyle.Length; i++)
				{
					bool flag2 = cstyle[i] == 0;
					if (flag2)
					{
						return Encoding.Default.GetString(cstyle, 0, i);
					}
				}
				text = Encoding.Default.GetString(cstyle);
			}
			return text;
		}

		// Token: 0x060085A7 RID: 34215 RVA: 0x002BBFF4 File Offset: 0x002BA1F4
		public static int ConvertToInt32(byte[] val)
		{
			return Marshal.ConvertToInt32(val, 0);
		}

		// Token: 0x060085A8 RID: 34216 RVA: 0x002BC010 File Offset: 0x002BA210
		public static long ConvertToInt64(int v1, uint v2)
		{
			int num = 1;
			bool flag = v1 < 0;
			if (flag)
			{
				num = -1;
			}
			return (long)((double)num * (Math.Abs((double)v1 * Math.Pow(2.0, 32.0)) + v2));
		}

		// Token: 0x060085A9 RID: 34217 RVA: 0x002BC058 File Offset: 0x002BA258
		public static int ConvertToInt32(byte[] val, int startIndex)
		{
			return Marshal.ConvertToInt32(val[startIndex], val[startIndex + 1], val[startIndex + 2], val[startIndex + 3]);
		}

		// Token: 0x060085AA RID: 34218 RVA: 0x002BC084 File Offset: 0x002BA284
		public static int ConvertToInt32(byte v1, byte v2, byte v3, byte v4)
		{
			return ((int)v1 << 24) | ((int)v2 << 16) | ((int)v3 << 8) | (int)v4;
		}

		// Token: 0x060085AB RID: 34219 RVA: 0x002BC0A8 File Offset: 0x002BA2A8
		public static uint ConvertToUInt32(byte v1, byte v2, byte v3, byte v4)
		{
			return (uint)(((int)v1 << 24) | ((int)v2 << 16) | ((int)v3 << 8) | (int)v4);
		}

		// Token: 0x060085AC RID: 34220 RVA: 0x002BC0CC File Offset: 0x002BA2CC
		public static uint ConvertToUInt32(byte[] val)
		{
			return Marshal.ConvertToUInt32(val, 0);
		}

		// Token: 0x060085AD RID: 34221 RVA: 0x002BC0E8 File Offset: 0x002BA2E8
		public static uint ConvertToUInt32(byte[] val, int startIndex)
		{
			return Marshal.ConvertToUInt32(val[startIndex], val[startIndex + 1], val[startIndex + 2], val[startIndex + 3]);
		}

		// Token: 0x060085AE RID: 34222 RVA: 0x002BC114 File Offset: 0x002BA314
		public static short ConvertToInt16(byte[] val)
		{
			return Marshal.ConvertToInt16(val, 0);
		}

		// Token: 0x060085AF RID: 34223 RVA: 0x002BC130 File Offset: 0x002BA330
		public static short ConvertToInt16(byte[] val, int startIndex)
		{
			return Marshal.ConvertToInt16(val[startIndex], val[startIndex + 1]);
		}

		// Token: 0x060085B0 RID: 34224 RVA: 0x002BC150 File Offset: 0x002BA350
		public static short ConvertToInt16(byte v1, byte v2)
		{
			return (short)(((int)v1 << 8) | (int)v2);
		}

		// Token: 0x060085B1 RID: 34225 RVA: 0x002BC168 File Offset: 0x002BA368
		public static ushort ConvertToUInt16(byte[] val)
		{
			return Marshal.ConvertToUInt16(val, 0);
		}

		// Token: 0x060085B2 RID: 34226 RVA: 0x002BC184 File Offset: 0x002BA384
		public static ushort ConvertToUInt16(byte[] val, int startIndex)
		{
			return Marshal.ConvertToUInt16(val[startIndex], val[startIndex + 1]);
		}

		// Token: 0x060085B3 RID: 34227 RVA: 0x002BC1A4 File Offset: 0x002BA3A4
		public static ushort ConvertToUInt16(byte v1, byte v2)
		{
			return (ushort)((int)v2 | ((int)v1 << 8));
		}

		// Token: 0x060085B4 RID: 34228 RVA: 0x002BC1BC File Offset: 0x002BA3BC
		public static string ToHexDump(string description, byte[] dump)
		{
			return Marshal.ToHexDump(description, dump, 0, dump.Length);
		}

		// Token: 0x060085B5 RID: 34229 RVA: 0x000F07E0 File Offset: 0x000EE9E0
		public static string ToHexDump(string description, byte[] dump, int start, int count)
		{
			StringBuilder stringBuilder = new StringBuilder();
			bool flag = description != null;
			if (flag)
			{
				stringBuilder.Append(description).Append("\n");
			}
			int num = start + count;
			for (int i = start; i < num; i += 16)
			{
				StringBuilder stringBuilder2 = new StringBuilder();
				StringBuilder stringBuilder3 = new StringBuilder();
				stringBuilder3.Append(i.ToString("X4"));
				stringBuilder3.Append(": ");
				for (int j = 0; j < 16; j++)
				{
					bool flag2 = j + i < num;
					if (flag2)
					{
						byte b = dump[j + i];
						stringBuilder3.Append(dump[j + i].ToString("X2"));
						stringBuilder3.Append(" ");
						bool flag3 = b >= 32 && b <= 127;
						if (flag3)
						{
							stringBuilder2.Append((char)b);
						}
						else
						{
							stringBuilder2.Append(".");
						}
					}
					else
					{
						stringBuilder3.Append("   ");
						stringBuilder2.Append(" ");
					}
				}
				stringBuilder3.Append("  ");
				stringBuilder3.Append(stringBuilder2.ToString());
				stringBuilder3.Append('\n');
				stringBuilder.Append(stringBuilder3.ToString());
			}
			return stringBuilder.ToString();
		}

		// Token: 0x060085B6 RID: 34230 RVA: 0x002BC1DC File Offset: 0x002BA3DC
		public static byte[] Compress(byte[] src)
		{
			return Marshal.Compress(src, 0, src.Length);
		}

		// Token: 0x060085B7 RID: 34231 RVA: 0x002BC1F8 File Offset: 0x002BA3F8
		public static byte[] Compress(byte[] src, int offset, int length)
		{
			MemoryStream memoryStream = new MemoryStream();
			Stream stream = new ZOutputStream(memoryStream, 9);
			stream.Write(src, offset, length);
			stream.Close();
			return memoryStream.ToArray();
		}

		// Token: 0x060085B8 RID: 34232 RVA: 0x002BC230 File Offset: 0x002BA430
		public static byte[] Uncompress(byte[] src)
		{
			return Marshal.Uncompress(src, 0);
		}

		// Token: 0x060085B9 RID: 34233 RVA: 0x002BC24C File Offset: 0x002BA44C
		public static byte[] Uncompress(byte[] src, int offset)
		{
			MemoryStream memoryStream = new MemoryStream();
			Stream stream = new ZOutputStream(memoryStream);
			stream.Write(src, offset, src.Length);
			stream.Close();
			return memoryStream.ToArray();
		}

		// Token: 0x060085BA RID: 34234 RVA: 0x002BC284 File Offset: 0x002BA484
		public static byte[] HexToByteArray(string str)
		{
			Dictionary<string, byte> dictionary = new Dictionary<string, byte>();
			for (int i = 0; i <= 255; i++)
			{
				dictionary.Add(i.ToString("X2"), (byte)i);
			}
			List<byte> list = new List<byte>();
			for (int j = 0; j < str.Length; j += 2)
			{
				list.Add(dictionary[str.Substring(j, 2)]);
			}
			return list.ToArray();
		}

		// Token: 0x060085BB RID: 34235 RVA: 0x002BC30C File Offset: 0x002BA50C
		public static byte[] ObjectToByteArray(object obj)
		{
			BinaryFormatter binaryFormatter = new BinaryFormatter();
			byte[] array;
			using (MemoryStream memoryStream = new MemoryStream())
			{
				binaryFormatter.Serialize(memoryStream, obj);
				array = memoryStream.ToArray();
			}
			return array;
		}

		// Token: 0x060085BC RID: 34236 RVA: 0x002BC354 File Offset: 0x002BA554
		public static object ByteArrayToObject(byte[] arrBytes)
		{
			object obj;
			using (MemoryStream memoryStream = new MemoryStream())
			{
				BinaryFormatter binaryFormatter = new BinaryFormatter();
				memoryStream.Write(arrBytes, 0, arrBytes.Length);
				memoryStream.Seek(0L, SeekOrigin.Begin);
				obj = binaryFormatter.Deserialize(memoryStream);
			}
			return obj;
		}

		// Token: 0x060085BD RID: 34237 RVA: 0x002BC3AC File Offset: 0x002BA5AC
		public static XmlDocument LoadXMLData(string filename, bool isEncrypt)
		{
			try
			{
				byte[] array = File.ReadAllBytes("xml/" + filename + ".xml");
				if (isEncrypt)
				{
					array = Marshal.Uncompress(array);
				}
				XmlDocument xmlDocument = new XmlDocument();
				xmlDocument.Load(new MemoryStream(array));
				return xmlDocument;
			}
			catch (Exception)
			{
				Console.WriteLine("File " + filename + "not found!");
			}
			return null;
		}

		// Token: 0x060085BE RID: 34238 RVA: 0x002BC428 File Offset: 0x002BA628
		public static T LoadDataFile<T>(string filename, bool isEncrypt)
		{
			bool flag = !File.Exists("datas/" + filename + ".snapedata");
			T t;
			if (flag)
			{
				t = default(T);
			}
			else
			{
				try
				{
					byte[] array = File.ReadAllBytes("datas/" + filename + ".snapedata");
					if (isEncrypt)
					{
						array = Marshal.Uncompress(array);
					}
					MemoryStream memoryStream = new MemoryStream(array);
					memoryStream.Position = 0L;
					T t2 = Serializer.Deserialize<T>(memoryStream);
					memoryStream.Dispose();
					return t2;
				}
				catch (Exception)
				{
					Console.WriteLine("Data " + filename + " is error!");
				}
				t = default(T);
			}
			return t;
		}

		// Token: 0x060085BF RID: 34239 RVA: 0x002BC4EC File Offset: 0x002BA6EC
		public static bool SaveDataFile<T>(T instance, string filename, bool isEncrypt)
		{
			try
			{
				bool flag = instance == null;
				byte[] array;
				if (flag)
				{
					array = new byte[0];
				}
				else
				{
					MemoryStream memoryStream = new MemoryStream();
					Serializer.Serialize<T>(memoryStream, instance);
					array = new byte[memoryStream.Length];
					memoryStream.Position = 0L;
					memoryStream.Read(array, 0, array.Length);
					memoryStream.Dispose();
				}
				if (isEncrypt)
				{
					array = Marshal.Compress(array);
				}
				File.WriteAllBytes("datas/" + filename + ".snapedata", array);
				return true;
			}
			catch (Exception ex)
			{
				Console.WriteLine(ex);
			}
			return false;
		}

		// Token: 0x060085C0 RID: 34240 RVA: 0x002BC5A0 File Offset: 0x002BA7A0
		public static long GetTimeStampMilliseconds(DateTime dateTime)
		{
			return new DateTimeOffset(dateTime).ToUnixTimeMilliseconds();
		}
	}
}
