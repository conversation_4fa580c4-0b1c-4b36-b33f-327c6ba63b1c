﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C6F RID: 3183
	public class OnlineTimeCondition : BaseCondition
	{
		// Token: 0x060070B4 RID: 28852 RVA: 0x0002A421 File Offset: 0x00028621
		public OnlineTimeCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x060070B5 RID: 28853 RVA: 0x0002A6BB File Offset: 0x000288BB
		public override void AddTrigger(GamePlayer player)
		{
			player.PingTimeOnline += this.player_PlayerEventHandle;
		}

		// Token: 0x060070B6 RID: 28854 RVA: 0x00250474 File Offset: 0x0024E674
		private void player_PlayerEventHandle(GamePlayer player)
		{
			int value = base.Value;
			base.Value = value + 1;
		}

		// Token: 0x060070B7 RID: 28855 RVA: 0x0002A6D1 File Offset: 0x000288D1
		public override void RemoveTrigger(GamePlayer player)
		{
			player.PingTimeOnline -= this.player_PlayerEventHandle;
		}

		// Token: 0x060070B8 RID: 28856 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
