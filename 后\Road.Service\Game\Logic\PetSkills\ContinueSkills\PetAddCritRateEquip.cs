﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D01 RID: 3329
	public class PetAddCritRateEquip : AbstractPetEffect
	{
		// Token: 0x06007861 RID: 30817 RVA: 0x00285AA4 File Offset: 0x00283CA4
		public PetAddCritRateEquip(int count, string elementID)
			: base(ePetEffectType.PetAddCritRateEquip, elementID)
		{
			this.m_count = count;
			bool flag = elementID == null;
			if (!flag)
			{
				int length = elementID.Length;
				bool flag2 = length != 4;
				if (!flag2)
				{
					switch (elementID[3])
					{
					case '1':
					{
						bool flag3 = elementID == "1421";
						if (!flag3)
						{
							bool flag4 = !(elementID == "1491");
							if (flag4)
							{
								return;
							}
							goto IL_01B0;
						}
						break;
					}
					case '2':
					{
						bool flag5 = !(elementID == "1212");
						if (flag5)
						{
							bool flag6 = elementID == "2972";
							if (flag6)
							{
								this.m_value = 50;
								this.m_count = 0;
							}
							return;
						}
						break;
					}
					case '3':
					{
						bool flag7 = !(elementID == "1213");
						if (flag7)
						{
							return;
						}
						goto IL_01B0;
					}
					case '4':
					case '6':
						return;
					case '5':
					{
						bool flag8 = elementID == "2975";
						if (flag8)
						{
							this.m_value = 50;
							this.m_count = 0;
						}
						return;
					}
					case '7':
					{
						bool flag9 = elementID == "1507";
						if (flag9)
						{
							this.m_value = 25;
						}
						return;
					}
					case '8':
					{
						bool flag10 = elementID == "1198";
						if (flag10)
						{
							this.m_value = 30;
						}
						return;
					}
					case '9':
					{
						bool flag11 = !(elementID == "1199");
						if (flag11)
						{
							bool flag12 = elementID == "1499";
							if (flag12)
							{
								this.m_value = 75;
							}
							return;
						}
						goto IL_01B0;
					}
					default:
						return;
					}
					this.m_value = 20;
					return;
					IL_01B0:
					this.m_value = 50;
				}
			}
		}

		// Token: 0x06007862 RID: 30818 RVA: 0x00285C6C File Offset: 0x00283E6C
		public override bool Start(Living living)
		{
			PetAddCritRateEquip petAddCritRateEquip = living.PetEffectList.GetOfType(ePetEffectType.PetAddCritRateEquip) as PetAddCritRateEquip;
			bool flag = petAddCritRateEquip != null;
			bool flag2;
			if (flag)
			{
				petAddCritRateEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007863 RID: 30819 RVA: 0x0002C9BD File Offset: 0x0002ABBD
		public override void OnAttached(Living living)
		{
			(living as Player).PetEffects.CritRate += this.m_value;
			living.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x06007864 RID: 30820 RVA: 0x0002C9F1 File Offset: 0x0002ABF1
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x06007865 RID: 30821 RVA: 0x00285CB4 File Offset: 0x00283EB4
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count <= 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.Info, false);
				(living as Player).PetEffects.CritRate = 0;
				this.m_count = 0;
				this.Stop();
			}
		}

		// Token: 0x04004691 RID: 18065
		private int m_count;

		// Token: 0x04004692 RID: 18066
		private int m_value;
	}
}
