﻿using System;
using System.Collections;
using System.Reflection;
using Game.Logic.Phy.Object;
using log4net;

namespace Game.Logic.Effects
{
	// Token: 0x02000EE8 RID: 3816
	public class EffectList
	{
		// Token: 0x170014A1 RID: 5281
		// (get) Token: 0x06008318 RID: 33560 RVA: 0x00034018 File Offset: 0x00032218
		public ArrayList List
		{
			get
			{
				return this.m_effects;
			}
		}

		// Token: 0x06008319 RID: 33561 RVA: 0x00034020 File Offset: 0x00032220
		public EffectList(Living owner, int immunity)
		{
			this.m_owner = owner;
			this.m_effects = new ArrayList(3);
			this.m_immunity = immunity;
		}

		// Token: 0x0600831A RID: 33562 RVA: 0x002B2BC4 File Offset: 0x002B0DC4
		public bool CanAddEffect(int id)
		{
			return this.m_owner.IsLiving && (id > 35 || id < 0 || ((1 << id - 1) & this.m_immunity) == 0);
		}

		// Token: 0x0600831B RID: 33563 RVA: 0x002B2C04 File Offset: 0x002B0E04
		public virtual bool Add(AbstractEffect effect)
		{
			bool flag = this.CanAddEffect(effect.TypeValue);
			bool flag3;
			if (flag)
			{
				ArrayList effects = this.m_effects;
				lock (effects)
				{
					this.m_effects.Add(effect);
				}
				effect.OnAttached(this.m_owner);
				this.OnEffectsChanged(effect);
				flag3 = true;
			}
			else
			{
				bool flag4 = effect.TypeValue == 9 && this.m_owner is SimpleBoss;
				if (flag4)
				{
					this.m_owner.State = 0;
				}
				flag3 = false;
			}
			return flag3;
		}

		// Token: 0x0600831C RID: 33564 RVA: 0x002B2CB0 File Offset: 0x002B0EB0
		public virtual bool Remove(AbstractEffect effect)
		{
			int num = -1;
			ArrayList effects = this.m_effects;
			lock (effects)
			{
				num = this.m_effects.IndexOf(effect);
				bool flag2 = num < 0;
				if (flag2)
				{
					return false;
				}
				this.m_effects.RemoveAt(num);
			}
			bool flag3 = num != -1;
			bool flag4;
			if (flag3)
			{
				effect.OnRemoved(this.m_owner);
				this.OnEffectsChanged(effect);
				flag4 = true;
			}
			else
			{
				flag4 = false;
			}
			return flag4;
		}

		// Token: 0x0600831D RID: 33565 RVA: 0x002B2D50 File Offset: 0x002B0F50
		public virtual void OnEffectsChanged(AbstractEffect changedEffect)
		{
			bool flag = this.m_changesCount <= 0;
			if (flag)
			{
				this.UpdateChangedEffects();
			}
		}

		// Token: 0x0600831E RID: 33566 RVA: 0x00034044 File Offset: 0x00032244
		public void BeginChanges()
		{
			this.m_changesCount += 1;
		}

		// Token: 0x0600831F RID: 33567 RVA: 0x002B2D7C File Offset: 0x002B0F7C
		public virtual void CommitChanges()
		{
			sbyte b = this.m_changesCount - 1;
			this.m_changesCount = b;
			bool flag = b < 0;
			if (flag)
			{
				bool isWarnEnabled = EffectList.log.IsWarnEnabled;
				if (isWarnEnabled)
				{
					EffectList.log.Warn("changes count is less than zero, forgot BeginChanges()?\n" + Environment.StackTrace);
				}
				this.m_changesCount = 0;
			}
			bool flag2 = this.m_changesCount == 0;
			if (flag2)
			{
				this.UpdateChangedEffects();
			}
		}

		// Token: 0x06008320 RID: 33568 RVA: 0x00005683 File Offset: 0x00003883
		protected virtual void UpdateChangedEffects()
		{
		}

		// Token: 0x06008321 RID: 33569 RVA: 0x002B2DF4 File Offset: 0x002B0FF4
		public virtual AbstractEffect GetOfType(eEffectType effectType)
		{
			ArrayList effects = this.m_effects;
			lock (effects)
			{
				foreach (object obj in this.m_effects)
				{
					AbstractEffect abstractEffect = (AbstractEffect)obj;
					bool flag2 = abstractEffect.Type == effectType;
					if (flag2)
					{
						return abstractEffect;
					}
				}
			}
			return null;
		}

		// Token: 0x06008322 RID: 33570 RVA: 0x002B2E9C File Offset: 0x002B109C
		public virtual IList GetAllOfType(Type effectType)
		{
			ArrayList arrayList = new ArrayList();
			ArrayList effects = this.m_effects;
			lock (effects)
			{
				foreach (object obj in this.m_effects)
				{
					AbstractEffect abstractEffect = (AbstractEffect)obj;
					bool flag2 = abstractEffect.GetType().Equals(effectType);
					if (flag2)
					{
						arrayList.Add(abstractEffect);
					}
				}
			}
			return arrayList;
		}

		// Token: 0x06008323 RID: 33571 RVA: 0x002B2F54 File Offset: 0x002B1154
		public void StopEffect(Type effectType)
		{
			IList allOfType = this.GetAllOfType(effectType);
			this.BeginChanges();
			foreach (object obj in allOfType)
			{
				AbstractEffect abstractEffect = (AbstractEffect)obj;
				abstractEffect.Stop();
			}
			this.CommitChanges();
		}

		// Token: 0x06008324 RID: 33572 RVA: 0x002B2FC8 File Offset: 0x002B11C8
		public void StopAllEffect()
		{
			bool flag = this.m_effects.Count > 0;
			if (flag)
			{
				bool flag2 = !this.m_owner.SyncAtTime;
				if (flag2)
				{
					this.m_owner.SyncAtTime = true;
				}
				AbstractEffect[] array = new AbstractEffect[this.m_effects.Count];
				this.m_effects.CopyTo(array);
				AbstractEffect[] array2 = array;
				AbstractEffect[] array3 = array2;
				AbstractEffect[] array4 = array3;
				foreach (AbstractEffect abstractEffect in array4)
				{
					abstractEffect.Stop();
				}
				this.m_effects.Clear();
			}
		}

		// Token: 0x040051FC RID: 20988
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040051FD RID: 20989
		protected ArrayList m_effects;

		// Token: 0x040051FE RID: 20990
		protected readonly Living m_owner;

		// Token: 0x040051FF RID: 20991
		protected volatile sbyte m_changesCount;

		// Token: 0x04005200 RID: 20992
		protected int m_immunity;
	}
}
