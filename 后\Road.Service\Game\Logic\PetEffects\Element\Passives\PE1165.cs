﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D6A RID: 3434
	public class PE1165 : BasePetEffect
	{
		// Token: 0x06007AA9 RID: 31401 RVA: 0x0028FCBC File Offset: 0x0028DEBC
		public PE1165(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1165, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AAA RID: 31402 RVA: 0x0028FD3C File Offset: 0x0028DF3C
		public override bool Start(Living living)
		{
			PE1165 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1165) as PE1165;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AAB RID: 31403 RVA: 0x0002E970 File Offset: 0x0002CB70
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007AAC RID: 31404 RVA: 0x0028FD9C File Offset: 0x0028DF9C
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				living.BaseDamage += (double)this.m_added;
			}
		}

		// Token: 0x06007AAD RID: 31405 RVA: 0x0002E986 File Offset: 0x0002CB86
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x04004826 RID: 18470
		private int m_type = 0;

		// Token: 0x04004827 RID: 18471
		private int m_count = 0;

		// Token: 0x04004828 RID: 18472
		private int m_probability = 0;

		// Token: 0x04004829 RID: 18473
		private int m_delay = 0;

		// Token: 0x0400482A RID: 18474
		private int m_coldDown = 0;

		// Token: 0x0400482B RID: 18475
		private int m_currentId;

		// Token: 0x0400482C RID: 18476
		private int m_added = 0;
	}
}
