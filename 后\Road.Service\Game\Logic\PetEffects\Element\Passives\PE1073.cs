﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D57 RID: 3415
	public class PE1073 : BasePetEffect
	{
		// Token: 0x06007A45 RID: 31301 RVA: 0x0028E2B8 File Offset: 0x0028C4B8
		public PE1073(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1073, elementID)
		{
			this.m_count = count;
			this.m_coldDown = 0;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A46 RID: 31302 RVA: 0x0028E334 File Offset: 0x0028C534
		public override bool Start(Living living)
		{
			PE1073 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1073) as PE1073;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A47 RID: 31303 RVA: 0x0002E562 File Offset: 0x0002C762
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
			player.AfterKilledByLiving += this.zplayer_AfterKilledByLiving;
		}

		// Token: 0x06007A48 RID: 31304 RVA: 0x0002E58B File Offset: 0x0002C78B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
			player.AfterKilledByLiving -= this.zplayer_AfterKilledByLiving;
		}

		// Token: 0x06007A49 RID: 31305 RVA: 0x0028E390 File Offset: 0x0028C590
		private void player_BeginSelfTurn(Living liv)
		{
			bool flag = this.m_coldDown < 5;
			if (flag)
			{
				bool flag2 = this.m_added == 0;
				if (flag2)
				{
					this.m_added = 30;
				}
				Player player = liv as Player;
				player.Game.SendPetBuff(liv, base.ElementInfo, true);
				player.BaseDamage += (double)this.m_added;
				this.m_count += this.m_added;
				this.m_coldDown++;
			}
		}

		// Token: 0x06007A4A RID: 31306 RVA: 0x0002E5B4 File Offset: 0x0002C7B4
		private void zplayer_AfterKilledByLiving(Living liv, Living target, int damageAmount, int criticalAmount)
		{
			this.ExitEffect(liv);
		}

		// Token: 0x06007A4B RID: 31307 RVA: 0x0028E414 File Offset: 0x0028C614
		private void ExitEffect(Living liv)
		{
			bool flag = this.m_coldDown > 0;
			if (flag)
			{
				Player player = liv as Player;
				int num = 30 * this.m_coldDown;
				Console.WriteLine(string.Format("remove ={0}", num));
				player.BaseDamage -= (double)num;
				player.Game.SendPetBuff(player, base.ElementInfo, false);
				this.m_coldDown = 0;
				this.m_added = 0;
			}
		}

		// Token: 0x040047A1 RID: 18337
		private int m_type = 0;

		// Token: 0x040047A2 RID: 18338
		private int m_count = 0;

		// Token: 0x040047A3 RID: 18339
		private int m_probability = 0;

		// Token: 0x040047A4 RID: 18340
		private int m_delay = 0;

		// Token: 0x040047A5 RID: 18341
		private int m_coldDown = 0;

		// Token: 0x040047A6 RID: 18342
		private int m_currentId;

		// Token: 0x040047A7 RID: 18343
		private int m_added = 0;
	}
}
