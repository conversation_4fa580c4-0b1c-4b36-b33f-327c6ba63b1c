﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D85 RID: 3461
	public class PE1425 : BasePetEffect
	{
		// Token: 0x06007B2E RID: 31534 RVA: 0x00292090 File Offset: 0x00290290
		public PE1425(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1425, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B2F RID: 31535 RVA: 0x00292110 File Offset: 0x00290310
		public override bool Start(Living living)
		{
			PE1425 pe = living.PetEffectList.GetOfType(ePetEffectType.AE1425) as PE1425;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B30 RID: 31536 RVA: 0x0002EDCC File Offset: 0x0002CFCC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.player_beginNextTurn;
		}

		// Token: 0x06007B31 RID: 31537 RVA: 0x0002EDE2 File Offset: 0x0002CFE2
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.player_beginNextTurn;
		}

		// Token: 0x06007B32 RID: 31538 RVA: 0x00292170 File Offset: 0x00290370
		public void player_beginNextTurn(Living living)
		{
			bool flag = this.m_added != 0;
			if (!flag)
			{
				List<Player> allEnemyPlayers = living.Game.GetAllEnemyPlayers(living);
				foreach (Player player in allEnemyPlayers)
				{
					bool flag2 = player.Defence != 0.0;
					if (flag2)
					{
						this.m_added = (int)(player.Defence * 15.0 / 100.0);
						player.Defence -= (double)this.m_added;
						player.Game.SendPetBuff(player, base.ElementInfo, true);
					}
				}
			}
		}

		// Token: 0x040048E3 RID: 18659
		private int m_type = 0;

		// Token: 0x040048E4 RID: 18660
		private int m_count = 0;

		// Token: 0x040048E5 RID: 18661
		private int m_probability = 0;

		// Token: 0x040048E6 RID: 18662
		private int m_delay = 0;

		// Token: 0x040048E7 RID: 18663
		private int m_coldDown = 0;

		// Token: 0x040048E8 RID: 18664
		private int m_currentId;

		// Token: 0x040048E9 RID: 18665
		private int m_added = 0;
	}
}
