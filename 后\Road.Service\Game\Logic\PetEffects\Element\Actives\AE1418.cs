﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E29 RID: 3625
	public class AE1418 : BasePetEffect
	{
		// Token: 0x06007E8B RID: 32395 RVA: 0x002A0E5C File Offset: 0x0029F05C
		public AE1418(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1418, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E8C RID: 32396 RVA: 0x002A0EDC File Offset: 0x0029F0DC
		public override bool Start(Living living)
		{
			AE1418 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1418) as AE1418;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E8D RID: 32397 RVA: 0x0003106B File Offset: 0x0002F26B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007E8E RID: 32398 RVA: 0x00031094 File Offset: 0x0002F294
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007E8F RID: 32399 RVA: 0x002A0F3C File Offset: 0x0029F13C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 150;
				player.CurrentDamagePlus += (float)(this.m_added / 100);
			}
		}

		// Token: 0x06007E90 RID: 32400 RVA: 0x002A0F88 File Offset: 0x0029F188
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_added / 100);
				this.m_added = 0;
			}
		}

		// Token: 0x04004D57 RID: 19799
		private int m_type = 0;

		// Token: 0x04004D58 RID: 19800
		private int m_count = 0;

		// Token: 0x04004D59 RID: 19801
		private int m_probability = 0;

		// Token: 0x04004D5A RID: 19802
		private int m_delay = 0;

		// Token: 0x04004D5B RID: 19803
		private int m_coldDown = 0;

		// Token: 0x04004D5C RID: 19804
		private int m_currentId;

		// Token: 0x04004D5D RID: 19805
		private int m_added = 0;
	}
}
