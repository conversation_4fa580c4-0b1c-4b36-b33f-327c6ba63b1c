﻿using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace Game.Manager.Properties
{
	// Token: 0x02000C79 RID: 3193
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
	[DebuggerNonUserCode]
	[CompilerGenerated]
	internal class Resources
	{
		// Token: 0x17001327 RID: 4903
		// (get) Token: 0x060070E6 RID: 28902 RVA: 0x002508E4 File Offset: 0x0024EAE4
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				bool flag = Resources.resourceMan == null;
				if (flag)
				{
					ResourceManager resourceManager = new ResourceManager("Game.Manager.Properties.Resources", typeof(Resources).Assembly);
					Resources.resourceMan = resourceManager;
				}
				return Resources.resourceMan;
			}
		}

		// Token: 0x17001328 RID: 4904
		// (get) Token: 0x060070E7 RID: 28903 RVA: 0x0025092C File Offset: 0x0024EB2C
		// (set) Token: 0x060070E8 RID: 28904 RVA: 0x0002A82F File Offset: 0x00028A2F
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return Resources.resourceCulture;
			}
			set
			{
				Resources.resourceCulture = value;
			}
		}

		// Token: 0x17001329 RID: 4905
		// (get) Token: 0x060070E9 RID: 28905 RVA: 0x00250944 File Offset: 0x0024EB44
		internal static Bitmap _1
		{
			get
			{
				object @object = Resources.ResourceManager.GetObject("1", Resources.resourceCulture);
				return (Bitmap)@object;
			}
		}

		// Token: 0x1700132A RID: 4906
		// (get) Token: 0x060070EA RID: 28906 RVA: 0x00250974 File Offset: 0x0024EB74
		internal static Bitmap _1_asset_core_icon_2
		{
			get
			{
				object @object = Resources.ResourceManager.GetObject("1_asset.core.icon_2", Resources.resourceCulture);
				return (Bitmap)@object;
			}
		}

		// Token: 0x1700132B RID: 4907
		// (get) Token: 0x060070EB RID: 28907 RVA: 0x002509A4 File Offset: 0x0024EBA4
		internal static Bitmap _2
		{
			get
			{
				object @object = Resources.ResourceManager.GetObject("2", Resources.resourceCulture);
				return (Bitmap)@object;
			}
		}

		// Token: 0x1700132C RID: 4908
		// (get) Token: 0x060070EC RID: 28908 RVA: 0x002509D4 File Offset: 0x0024EBD4
		internal static Bitmap _3
		{
			get
			{
				object @object = Resources.ResourceManager.GetObject("3", Resources.resourceCulture);
				return (Bitmap)@object;
			}
		}

		// Token: 0x1700132D RID: 4909
		// (get) Token: 0x060070ED RID: 28909 RVA: 0x00250A04 File Offset: 0x0024EC04
		internal static Bitmap _4
		{
			get
			{
				object @object = Resources.ResourceManager.GetObject("4", Resources.resourceCulture);
				return (Bitmap)@object;
			}
		}

		// Token: 0x060070EE RID: 28910 RVA: 0x0000586E File Offset: 0x00003A6E
		internal Resources()
		{
		}

		// Token: 0x04003C65 RID: 15461
		private static ResourceManager resourceMan;

		// Token: 0x04003C66 RID: 15462
		private static CultureInfo resourceCulture;
	}
}
