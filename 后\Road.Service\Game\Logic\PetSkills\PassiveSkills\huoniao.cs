﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CED RID: 3309
	public class huoniao : BasePetEffect
	{
		// Token: 0x060077F9 RID: 30713 RVA: 0x0028379C File Offset: 0x0028199C
		public huoniao(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.huoniao, elementID)
		{
			this.int_1 = 2;
			this.int_4 = 5;
			this.int_2 = ((probability == -1) ? 10000 : probability);
			this.int_0 = type;
			this.int_3 = delay;
			this.int_5 = skillId;
		}

		// Token: 0x060077FA RID: 30714 RVA: 0x002837F0 File Offset: 0x002819F0
		private void method_0(Living living_0, Living living_1, int int_7, int int_8)
		{
			int num = living_0.MaxBlood / 100;
			bool flag = living_0.Blood <= num;
			if (flag)
			{
				bool flag2 = living_0.KeepLifeCount < this.int_1;
				if (flag2)
				{
					this.IsTrigger = true;
					living_0.EffectTrigger = true;
					living_0.SyncAtTime = true;
					living_0.Blood = Convert.ToInt32((double)living_0.MaxBlood * 0.5);
					living_0.Game.SendGameUpdateHealth(living_0, 1, living_0.Blood);
					living_0.SyncAtTime = false;
					living_0.Game.SendEquipEffect(living_0, "触发<凤凰涅槃>免疫本次致命伤害,恢复自身血量50%");
					int keepLifeCount = living_0.KeepLifeCount;
					living_0.KeepLifeCount = keepLifeCount + 1;
				}
				else
				{
					living_0.KeepLife = false;
					this.Stop();
				}
			}
		}

		// Token: 0x060077FB RID: 30715 RVA: 0x0002C403 File Offset: 0x0002A603
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKilledByLiving += this.method_0;
			player.AfterKillingLiving += this.method_0;
		}

		// Token: 0x060077FC RID: 30716 RVA: 0x0002C42C File Offset: 0x0002A62C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.method_0;
			player.AfterKillingLiving -= this.method_0;
		}

		// Token: 0x060077FD RID: 30717 RVA: 0x002838B8 File Offset: 0x00281AB8
		public override bool Start(Living living)
		{
			huoniao huoniao = living.PetEffectList.GetOfType(ePetEffectType.huoniao) as huoniao;
			bool flag = huoniao == null;
			bool flag2;
			if (flag)
			{
				flag2 = base.Start(living);
			}
			else
			{
				huoniao.int_2 = ((this.int_2 > huoniao.int_2) ? this.int_2 : huoniao.int_2);
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x0400464C RID: 17996
		private int int_0;

		// Token: 0x0400464D RID: 17997
		private int int_1;

		// Token: 0x0400464E RID: 17998
		private int int_2;

		// Token: 0x0400464F RID: 17999
		private int int_3;

		// Token: 0x04004650 RID: 18000
		private int int_4;

		// Token: 0x04004651 RID: 18001
		private int int_5;

		// Token: 0x04004652 RID: 18002
		private int int_6;
	}
}
