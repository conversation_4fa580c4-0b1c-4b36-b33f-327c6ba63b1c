﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DF2 RID: 3570
	public class AE1187 : BasePetEffect
	{
		// Token: 0x06007D69 RID: 32105 RVA: 0x0029BE48 File Offset: 0x0029A048
		public AE1187(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1187, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D6A RID: 32106 RVA: 0x0029BEC8 File Offset: 0x0029A0C8
		public override bool Start(Living living)
		{
			AE1187 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1187) as AE1187;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D6B RID: 32107 RVA: 0x000304C6 File Offset: 0x0002E6C6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D6C RID: 32108 RVA: 0x000304DC File Offset: 0x0002E6DC
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D6D RID: 32109 RVA: 0x0029BF28 File Offset: 0x0029A128
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1187(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004BD6 RID: 19414
		private int m_type = 0;

		// Token: 0x04004BD7 RID: 19415
		private int m_count = 0;

		// Token: 0x04004BD8 RID: 19416
		private int m_probability = 0;

		// Token: 0x04004BD9 RID: 19417
		private int m_delay = 0;

		// Token: 0x04004BDA RID: 19418
		private int m_coldDown = 0;

		// Token: 0x04004BDB RID: 19419
		private int m_currentId;

		// Token: 0x04004BDC RID: 19420
		private int m_added = 0;
	}
}
