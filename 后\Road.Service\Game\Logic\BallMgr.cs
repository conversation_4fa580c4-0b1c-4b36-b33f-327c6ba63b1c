﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Net;
using System.Reflection;
using System.Text;
using System.Threading;
using Bussiness;
using Game.Base;
using Game.Logic.Phy.Maps;
using Game.Logic.Phy.Object;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000C84 RID: 3204
	public class BallMgr
	{
		// Token: 0x0600715E RID: 29022 RVA: 0x0025B708 File Offset: 0x00259908
		public static bool Init()
		{
			return BallMgr.ReLoad(false);
		}

		// Token: 0x0600715F RID: 29023 RVA: 0x0025B720 File Offset: 0x00259920
		public static bool ReLoad(bool buildSuccess)
		{
			try
			{
				Dictionary<int, BallInfo> dictionary = BallMgr.LoadFromDatabase();
				Dictionary<int, Tile> dictionary2 = BallMgr.LoadFromFiles(dictionary, buildSuccess);
				bool flag = dictionary.Values.Count > 0 && dictionary2.Values.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, BallInfo>>(ref BallMgr.m_infos, dictionary);
					Interlocked.Exchange<Dictionary<int, Tile>>(ref BallMgr.m_tiles, dictionary2);
					return true;
				}
				Console.WriteLine("ballcount: " + dictionary.Count.ToString() + "|" + dictionary2.Count.ToString());
			}
			catch (Exception ex)
			{
				BallMgr.log.Error("Ball Mgr init error:", ex);
			}
			return false;
		}

		// Token: 0x06007160 RID: 29024 RVA: 0x0025B7E0 File Offset: 0x002599E0
		private static Dictionary<int, BallInfo> LoadFromDatabase()
		{
			Dictionary<int, BallInfo> dictionary = new Dictionary<int, BallInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				BallInfo[] allBall = produceBussiness.GetAllBall();
				BallInfo[] array = allBall;
				BallInfo[] array2 = array;
				BallInfo[] array3 = array2;
				foreach (BallInfo ballInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(ballInfo.ID);
					if (flag)
					{
						dictionary.Add(ballInfo.ID, ballInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06007161 RID: 29025 RVA: 0x0025B87C File Offset: 0x00259A7C
		private static Dictionary<int, Tile> LoadFromFiles(Dictionary<int, BallInfo> list, bool buildSuccess)
		{
			bool flag = true;
			Dictionary<int, Tile> dictionary = new Dictionary<int, Tile>();
			foreach (BallInfo ballInfo in list.Values)
			{
				bool hasTunnel = ballInfo.HasTunnel;
				if (hasTunnel)
				{
					string text = string.Format("bomb\\{0}.bomb", ballInfo.ID);
					Tile tile = null;
					bool flag2 = File.Exists(text);
					if (flag2)
					{
						tile = new Tile(text, false);
					}
					dictionary.Add(ballInfo.ID, tile);
					bool flag3 = tile == null && ballInfo.Crater != "NULL" && ballInfo.Crater != "0" && ballInfo.ID != 1 && ballInfo.ID != 2 && ballInfo.ID != 3 && ballInfo.Crater != string.Empty;
					if (flag3)
					{
						BallMgr.log.ErrorFormat("can't find bomb file:{0}", text);
						flag = false;
						BallMgr.DownloadBombs(ballInfo.ID.ToString(), ballInfo.Crater);
					}
				}
			}
			bool flag4 = !flag;
			if (flag4)
			{
				BallMgr.log.Error("Trying to build bomb files, please wait a moment...");
				BallMgr.BuildBombs();
			}
			if (buildSuccess)
			{
				BallMgr.log.Warn("Build bomb files Success!");
			}
			return dictionary;
		}

		// Token: 0x06007162 RID: 29026 RVA: 0x0025BA00 File Offset: 0x00259C00
		private static void BuildBombs()
		{
			string text = "tempDownload/crater";
			string text2 = "Bomb";
			string[] files = Directory.GetFiles(text, "*.png");
			bool flag = files.Length != 0 || !string.IsNullOrEmpty(text);
			if (flag)
			{
				string[] array = files;
				string[] array2 = array;
				foreach (string text3 in array2)
				{
					try
					{
						FileInfo fileInfo = new FileInfo(text3);
						bool flag2 = File.Exists(fileInfo.FullName);
						if (flag2)
						{
							Tile tile = new Tile(new Bitmap(fileInfo.FullName), true);
							FileInfo fileInfo2 = new FileInfo(text2 + "/" + fileInfo.Name.Substring(0, fileInfo.Name.Length - 4) + ".bomb");
							bool flag3 = !Directory.Exists(fileInfo2.DirectoryName);
							if (flag3)
							{
								Directory.CreateDirectory(fileInfo2.DirectoryName);
							}
							FileStream fileStream = File.Create(fileInfo2.FullName);
							BinaryWriter binaryWriter = new BinaryWriter(fileStream);
							binaryWriter.Write(tile.Width);
							binaryWriter.Write(tile.Height);
							binaryWriter.Flush();
							fileStream.Write(tile.Data, 0, tile.Data.Length);
							fileStream.Close();
						}
					}
					catch
					{
					}
				}
			}
			BallMgr.ReLoad(true);
		}

		// Token: 0x06007163 RID: 29027 RVA: 0x0025BB78 File Offset: 0x00259D78
		private static void DownloadBombs(string BallId, string CraterId)
		{
			string text = "tempDownload/crater";
			bool flag = !File.Exists(text);
			if (flag)
			{
				Directory.CreateDirectory("tempDownload/crater");
			}
			try
			{
				WebClient webClient = new WebClient();
				webClient.DownloadFile("http://cdnres.ddt.1322game.com/image/bomb/crater/" + CraterId + "/crater.png", "tempDownload/crater/" + BallId + ".png");
				string @string = Encoding.UTF8.GetString(webClient.DownloadData("http://cdnres.ddt.1322game.com/image/bomb/crater/" + CraterId + "/crater.png"));
				bool flag2 = @string.Contains("^_^");
				if (flag2)
				{
					BallMgr.Uncompress("tempDownload/crater/" + BallId + ".png");
				}
			}
			catch
			{
				Console.WriteLine("自动生成弹坑存在问题");
			}
		}

		// Token: 0x06007164 RID: 29028 RVA: 0x0025BC44 File Offset: 0x00259E44
		public static void Uncompress(string path)
		{
			FileStream fileStream = File.OpenRead(path);
			byte[] array = new byte[fileStream.Length];
			fileStream.Read(array, 0, (int)fileStream.Length);
			fileStream.Close();
			ByteArray byteArray = new ByteArray(array);
			uint position = byteArray.Position;
			byteArray.Position = 0U;
			byteArray.ReadUTF();
			ByteArray byteArray2 = new ByteArray();
			byteArray.ReadBytes(byteArray2, 0U, 16U);
			int num = (int)byteArray.ReadByte();
			num = ~num;
			ByteArray byteArray3 = new ByteArray();
			byteArray.ReadBytes(byteArray3, 0U, byteArray3.BytesAvailable);
			FileStream fileStream2 = File.OpenWrite(path);
			byte[] array2 = byteArray2.ToArray();
			byte[] array3 = byteArray3.ToArray();
			fileStream2.Write(array2, 0, 16);
			fileStream2.WriteByte((byte)num);
			fileStream2.Write(array3, 0, array3.Length);
			byteArray.Position = position;
			fileStream2.Flush();
			fileStream2.Close();
		}

		// Token: 0x06007165 RID: 29029 RVA: 0x0025BD2C File Offset: 0x00259F2C
		public static BallInfo FindBall(int id)
		{
			bool flag = BallMgr.m_infos.ContainsKey(id);
			BallInfo ballInfo;
			if (flag)
			{
				ballInfo = BallMgr.m_infos[id];
			}
			else
			{
				ballInfo = null;
			}
			return ballInfo;
		}

		// Token: 0x06007166 RID: 29030 RVA: 0x0025BD60 File Offset: 0x00259F60
		public static Tile FindTile(int id)
		{
			bool flag = BallMgr.m_tiles.ContainsKey(id);
			Tile tile;
			if (flag)
			{
				tile = BallMgr.m_tiles[id];
			}
			else
			{
				tile = null;
			}
			return tile;
		}

		// Token: 0x06007167 RID: 29031 RVA: 0x0025BD94 File Offset: 0x00259F94
		public static BombType GetBallType(int ballId)
		{
			if (ballId <= 110)
			{
				if (ballId <= 59)
				{
					switch (ballId)
					{
					case 1:
						break;
					case 2:
					case 4:
						goto IL_00E6;
					case 3:
						goto IL_00D4;
					case 5:
						goto IL_00D8;
					default:
						if (ballId != 56)
						{
							if (ballId != 59)
							{
								goto IL_00E6;
							}
							goto IL_00D8;
						}
						break;
					}
				}
				else if (ballId <= 98)
				{
					if (ballId != 64 && ballId - 97 > 1)
					{
						goto IL_00E6;
					}
					goto IL_00D8;
				}
				else if (ballId != 99)
				{
					if (ballId != 110)
					{
						goto IL_00E6;
					}
					goto IL_00DC;
				}
				return BombType.FORZEN;
			}
			if (ballId <= 189)
			{
				if (ballId <= 120)
				{
					if (ballId == 117)
					{
						goto IL_00DC;
					}
					if (ballId != 120)
					{
						goto IL_00E6;
					}
					goto IL_00D8;
				}
				else
				{
					if (ballId - 128 <= 1)
					{
						return BombType.CATCHINSECT;
					}
					if (ballId != 189)
					{
						goto IL_00E6;
					}
					goto IL_00D8;
				}
			}
			else if (ballId <= 10009)
			{
				if (ballId != 298)
				{
					if (ballId != 10009)
					{
						goto IL_00E6;
					}
					goto IL_00D8;
				}
			}
			else
			{
				if (ballId - 11409 > 1 && ballId != 130033)
				{
					goto IL_00E6;
				}
				goto IL_00D8;
			}
			IL_00D4:
			return BombType.FLY;
			IL_00D8:
			return BombType.CURE;
			IL_00DC:
			return BombType.WORLDCUP;
			IL_00E6:
			return BombType.Normal;
		}

		// Token: 0x04003DAD RID: 15789
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04003DAE RID: 15790
		private static Dictionary<int, BallInfo> m_infos;

		// Token: 0x04003DAF RID: 15791
		private static Dictionary<int, Tile> m_tiles;
	}
}
