﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DD1 RID: 3537
	public class AE1126 : BasePetEffect
	{
		// Token: 0x06007CBB RID: 31931 RVA: 0x00298C24 File Offset: 0x00296E24
		public AE1126(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1126, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CBC RID: 31932 RVA: 0x00298CA4 File Offset: 0x00296EA4
		public override bool Start(Living living)
		{
			AE1126 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1126) as AE1126;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CBD RID: 31933 RVA: 0x0002FDD7 File Offset: 0x0002DFD7
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007CBE RID: 31934 RVA: 0x0002FE00 File Offset: 0x0002E000
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007CBF RID: 31935 RVA: 0x00298D04 File Offset: 0x00296F04
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 120;
				player.CurrentDamagePlus += (float)(this.m_added / 100);
			}
		}

		// Token: 0x06007CC0 RID: 31936 RVA: 0x00298D4C File Offset: 0x00296F4C
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_added / 100);
				this.m_added = 0;
			}
		}

		// Token: 0x04004AEF RID: 19183
		private int m_type = 0;

		// Token: 0x04004AF0 RID: 19184
		private int m_count = 0;

		// Token: 0x04004AF1 RID: 19185
		private int m_probability = 0;

		// Token: 0x04004AF2 RID: 19186
		private int m_delay = 0;

		// Token: 0x04004AF3 RID: 19187
		private int m_coldDown = 0;

		// Token: 0x04004AF4 RID: 19188
		private int m_currentId;

		// Token: 0x04004AF5 RID: 19189
		private int m_added = 0;
	}
}
