﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E05 RID: 3589
	public class AE1212 : BasePetEffect
	{
		// Token: 0x06007DCC RID: 32204 RVA: 0x0029DA10 File Offset: 0x0029BC10
		public AE1212(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1212, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DCD RID: 32205 RVA: 0x0029DA90 File Offset: 0x0029BC90
		public override bool Start(Living living)
		{
			AE1212 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1212) as AE1212;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DCE RID: 32206 RVA: 0x0003088F File Offset: 0x0002EA8F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DCF RID: 32207 RVA: 0x000308A5 File Offset: 0x0002EAA5
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DD0 RID: 32208 RVA: 0x0029DAF0 File Offset: 0x0029BCF0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.PetEffects.CritRate = 20;
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1212(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004C5B RID: 19547
		private int m_type = 0;

		// Token: 0x04004C5C RID: 19548
		private int m_count = 0;

		// Token: 0x04004C5D RID: 19549
		private int m_probability = 0;

		// Token: 0x04004C5E RID: 19550
		private int m_delay = 0;

		// Token: 0x04004C5F RID: 19551
		private int m_coldDown = 0;

		// Token: 0x04004C60 RID: 19552
		private int m_currentId;

		// Token: 0x04004C61 RID: 19553
		private int m_added = 0;
	}
}
