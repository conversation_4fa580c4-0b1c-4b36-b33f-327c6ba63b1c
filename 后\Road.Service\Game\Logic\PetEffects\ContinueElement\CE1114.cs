﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E6D RID: 3693
	public class CE1114 : BasePetEffect
	{
		// Token: 0x0600801A RID: 32794 RVA: 0x002A7D9C File Offset: 0x002A5F9C
		public CE1114(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1114, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600801B RID: 32795 RVA: 0x002A7E18 File Offset: 0x002A6018
		public override bool Start(Living living)
		{
			CE1114 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1114) as CE1114;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600801C RID: 32796 RVA: 0x00031E54 File Offset: 0x00030054
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600801D RID: 32797 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600801E RID: 32798 RVA: 0x002A7E74 File Offset: 0x002A6074
		private void Player_BeginNextTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600801F RID: 32799 RVA: 0x00031E7D File Offset: 0x0003007D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x04004F36 RID: 20278
		private int m_type = 0;

		// Token: 0x04004F37 RID: 20279
		private int m_count = 0;

		// Token: 0x04004F38 RID: 20280
		private int m_probability = 0;

		// Token: 0x04004F39 RID: 20281
		private int m_delay = 0;

		// Token: 0x04004F3A RID: 20282
		private int m_coldDown = 0;

		// Token: 0x04004F3B RID: 20283
		private int m_currentId;

		// Token: 0x04004F3C RID: 20284
		private int m_added = 0;
	}
}
