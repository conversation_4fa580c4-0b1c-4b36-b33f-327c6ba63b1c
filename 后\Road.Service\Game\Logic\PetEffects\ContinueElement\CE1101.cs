﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E6B RID: 3691
	public class CE1101 : BasePetEffect
	{
		// Token: 0x0600800E RID: 32782 RVA: 0x002A7B18 File Offset: 0x002A5D18
		public CE1101(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1101, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600800F RID: 32783 RVA: 0x002A7B98 File Offset: 0x002A5D98
		public override bool Start(Living living)
		{
			CE1101 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1101) as CE1101;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008010 RID: 32784 RVA: 0x002A7BF8 File Offset: 0x002A5DF8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 2000;
				player.AddMaxBlood(this.m_added);
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008011 RID: 32785 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008012 RID: 32786 RVA: 0x002A7C54 File Offset: 0x002A5E54
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008013 RID: 32787 RVA: 0x00031DEA File Offset: 0x0002FFEA
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AddMaxBlood(-this.m_added);
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F28 RID: 20264
		private int m_type = 0;

		// Token: 0x04004F29 RID: 20265
		private int m_count = 0;

		// Token: 0x04004F2A RID: 20266
		private int m_probability = 0;

		// Token: 0x04004F2B RID: 20267
		private int m_delay = 0;

		// Token: 0x04004F2C RID: 20268
		private int m_coldDown = 0;

		// Token: 0x04004F2D RID: 20269
		private int m_currentId;

		// Token: 0x04004F2E RID: 20270
		private int m_added = 0;
	}
}
