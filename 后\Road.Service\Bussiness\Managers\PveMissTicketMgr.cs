﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using EntityDatabase.ServerModels;
using log4net;

namespace Bussiness.Managers
{
	// Token: 0x02000FE3 RID: 4067
	public class PveMissTicketMgr
	{
		// Token: 0x06008B31 RID: 35633 RVA: 0x002FB274 File Offset: 0x002F9474
		public static bool Init()
		{
			return PveMissTicketMgr.Reload();
		}

		// Token: 0x06008B32 RID: 35634 RVA: 0x002FB28C File Offset: 0x002F948C
		public static TS_PveMissTicket GetMissTicket(int id)
		{
			bool flag = PveMissTicketMgr.m_pvemissticket.ContainsKey(id);
			TS_PveMissTicket ts_PveMissTicket;
			if (flag)
			{
				ts_PveMissTicket = PveMissTicketMgr.m_pvemissticket[id];
			}
			else
			{
				ts_PveMissTicket = null;
			}
			return ts_PveMissTicket;
		}

		// Token: 0x06008B33 RID: 35635 RVA: 0x002FB2C0 File Offset: 0x002F94C0
		private static Dictionary<int, TS_PveMissTicket> LoadFromDatabase()
		{
			Dictionary<int, TS_PveMissTicket> dictionary = new Dictionary<int, TS_PveMissTicket>();
			Dictionary<int, TS_PveMissTicket> dictionary2;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				TS_PveMissTicket[] allPveRequire = produceBussiness.GetAllPveRequire();
				TS_PveMissTicket[] array = allPveRequire;
				TS_PveMissTicket[] array2 = array;
				foreach (TS_PveMissTicket ts_PveMissTicket in array2)
				{
					bool flag = !dictionary.ContainsKey(ts_PveMissTicket.PveID);
					if (flag)
					{
						dictionary.Add(ts_PveMissTicket.PveID, ts_PveMissTicket);
					}
				}
				dictionary2 = dictionary;
			}
			return dictionary2;
		}

		// Token: 0x06008B34 RID: 35636 RVA: 0x002FB354 File Offset: 0x002F9554
		public static bool Reload()
		{
			try
			{
				Dictionary<int, TS_PveMissTicket> dictionary = PveMissTicketMgr.LoadFromDatabase();
				bool flag = dictionary.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, TS_PveMissTicket>>(ref PveMissTicketMgr.m_pvemissticket, dictionary);
				}
				return true;
			}
			catch (Exception ex)
			{
				PveMissTicketMgr.log.Error("MissTicketMgr", ex);
			}
			return false;
		}

		// Token: 0x04005536 RID: 21814
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005537 RID: 21815
		private static Dictionary<int, TS_PveMissTicket> m_pvemissticket = new Dictionary<int, TS_PveMissTicket>();
	}
}
