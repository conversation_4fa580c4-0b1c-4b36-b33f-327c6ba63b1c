﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DAB RID: 3499
	public class AE1049 : BasePetEffect
	{
		// Token: 0x06007BF7 RID: 31735 RVA: 0x00295400 File Offset: 0x00293600
		public AE1049(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1049, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BF8 RID: 31736 RVA: 0x00295480 File Offset: 0x00293680
		public override bool Start(Living living)
		{
			AE1049 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1049) as AE1049;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BF9 RID: 31737 RVA: 0x0002F6DB File Offset: 0x0002D8DB
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BFA RID: 31738 RVA: 0x0002F6F1 File Offset: 0x0002D8F1
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BFB RID: 31739 RVA: 0x002954E0 File Offset: 0x002936E0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1049(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x040049E7 RID: 18919
		private int m_type = 0;

		// Token: 0x040049E8 RID: 18920
		private int m_count = 0;

		// Token: 0x040049E9 RID: 18921
		private int m_probability = 0;

		// Token: 0x040049EA RID: 18922
		private int m_delay = 0;

		// Token: 0x040049EB RID: 18923
		private int m_coldDown = 0;

		// Token: 0x040049EC RID: 18924
		private int m_currentId;

		// Token: 0x040049ED RID: 18925
		private int m_added = 0;
	}
}
