﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EC5 RID: 3781
	public class CE3179 : BasePetEffect
	{
		// Token: 0x06008252 RID: 33362 RVA: 0x002B0530 File Offset: 0x002AE730
		public CE3179(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE3179, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008253 RID: 33363 RVA: 0x002B05AC File Offset: 0x002AE7AC
		public override bool Start(Living living)
		{
			CE3179 ce = living.PetEffectList.GetOfType(ePetEffectType.CE3179) as CE3179;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008254 RID: 33364 RVA: 0x00033415 File Offset: 0x00031615
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008255 RID: 33365 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008256 RID: 33366 RVA: 0x002B0608 File Offset: 0x002AE808
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.SendPetBuff(living, base.ElementInfo, false);
				this.Stop();
			}
		}

		// Token: 0x06008257 RID: 33367 RVA: 0x0003343E File Offset: 0x0003163E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x040051A1 RID: 20897
		private int m_type = 0;

		// Token: 0x040051A2 RID: 20898
		private int m_count = 0;

		// Token: 0x040051A3 RID: 20899
		private int m_probability = 0;

		// Token: 0x040051A4 RID: 20900
		private int m_delay = 0;

		// Token: 0x040051A5 RID: 20901
		private int m_coldDown = 0;

		// Token: 0x040051A6 RID: 20902
		private int m_currentId;

		// Token: 0x040051A7 RID: 20903
		private int m_added = 0;
	}
}
