﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DE9 RID: 3561
	public class AE1178 : BasePetEffect
	{
		// Token: 0x06007D3C RID: 32060 RVA: 0x0029AFA4 File Offset: 0x002991A4
		public AE1178(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1178, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D3D RID: 32061 RVA: 0x0029B024 File Offset: 0x00299224
		public override bool Start(Living living)
		{
			AE1178 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1178) as AE1178;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D3E RID: 32062 RVA: 0x0003033A File Offset: 0x0002E53A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D3F RID: 32063 RVA: 0x00030350 File Offset: 0x0002E550
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D40 RID: 32064 RVA: 0x0029B084 File Offset: 0x00299284
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1178(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004B97 RID: 19351
		private int m_type = 0;

		// Token: 0x04004B98 RID: 19352
		private int m_count = 0;

		// Token: 0x04004B99 RID: 19353
		private int m_probability = 0;

		// Token: 0x04004B9A RID: 19354
		private int m_delay = 0;

		// Token: 0x04004B9B RID: 19355
		private int m_coldDown = 0;

		// Token: 0x04004B9C RID: 19356
		private int m_currentId;

		// Token: 0x04004B9D RID: 19357
		private int m_added = 0;
	}
}
