﻿using System;
using Game.Base.Packets;
using Game.Server.GameObjects;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C4D RID: 3149
	[ActiveSystemHandleAttbute(35)]
	public class CatchbeastBuybuff : IActiveSystemCommandHadler
	{
		// Token: 0x0600701C RID: 28700 RVA: 0x0024D434 File Offset: 0x0024B634
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			bool flag = packet.ReadBoolean();
			Player.SendMessage("当前BUFF功能已暂停使用");
			return false;
		}
	}
}
