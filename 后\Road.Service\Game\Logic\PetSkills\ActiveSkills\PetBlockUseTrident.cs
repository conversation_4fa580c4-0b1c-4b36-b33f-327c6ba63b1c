﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D2F RID: 3375
	public class PetBlockUseTrident : BasePetEffect
	{
		// Token: 0x06007956 RID: 31062 RVA: 0x0002D7DB File Offset: 0x0002B9DB
		public PetBlockUseTrident(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetBlockUseTrident, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_skillId = skillId;
		}

		// Token: 0x06007957 RID: 31063 RVA: 0x00289D8C File Offset: 0x00287F8C
		public override bool Start(Living living)
		{
			PetBlockUseTrident petBlockUseTrident = living.PetEffectList.GetOfType(ePetEffectType.PetBlockUseTrident) as PetBlockUseTrident;
			bool flag = petBlockUseTrident != null;
			bool flag2;
			if (flag)
			{
				petBlockUseTrident.m_probability = ((this.m_probability > petBlockUseTrident.m_probability) ? this.m_probability : petBlockUseTrident.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007958 RID: 31064 RVA: 0x0002D80C File Offset: 0x0002BA0C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x06007959 RID: 31065 RVA: 0x0002D822 File Offset: 0x0002BA22
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x0600795A RID: 31066 RVA: 0x00289DEC File Offset: 0x00287FEC
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_skillId;
			if (flag)
			{
				for (int i = 0; i < this.m_count; i++)
				{
					living.Game.sendShowPicSkil(living, base.Info, true);
				}
				living.AddPetEffect(new PetBlockUseTridentEquip(this.m_count, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004715 RID: 18197
		private int m_count;

		// Token: 0x04004716 RID: 18198
		private int m_probability;

		// Token: 0x04004717 RID: 18199
		private int m_skillId;
	}
}
