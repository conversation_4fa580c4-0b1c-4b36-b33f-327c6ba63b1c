﻿using System;
using Game.Logic;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C6B RID: 3179
	public class GuildFightWinCondition : BaseCondition
	{
		// Token: 0x060070A4 RID: 28836 RVA: 0x0002A421 File Offset: 0x00028621
		public GuildFightWinCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x060070A5 RID: 28837 RVA: 0x0002A622 File Offset: 0x00028822
		public override void AddTrigger(GamePlayer player)
		{
			player.GameOver += this.player_GameOver;
		}

		// Token: 0x060070A6 RID: 28838 RVA: 0x002503D4 File Offset: 0x0024E5D4
		private void player_GameOver(AbstractGame game, bool isWin, int gainXp, bool isSpanArea)
		{
			bool flag = game.GameType == eGameType.Guild || game.GameType == eGameType.GuildLeage;
			if (flag)
			{
				int value = base.Value;
				base.Value = value + 1;
			}
		}

		// Token: 0x060070A7 RID: 28839 RVA: 0x0002A638 File Offset: 0x00028838
		public override void RemoveTrigger(GamePlayer player)
		{
			player.GameOver -= this.player_GameOver;
		}

		// Token: 0x060070A8 RID: 28840 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
