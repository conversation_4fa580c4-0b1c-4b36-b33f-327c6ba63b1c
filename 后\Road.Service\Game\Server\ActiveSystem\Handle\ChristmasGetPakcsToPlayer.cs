﻿using System;
using Bussiness;
using Game.Base.Packets;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C54 RID: 3156
	[ActiveSystemHandleAttbute(27)]
	public class ChristmasGetPakcsToPlayer : IActiveSystemCommandHadler
	{
		// Token: 0x0600702B RID: 28715 RVA: 0x0024DB1C File Offset: 0x0024BD1C
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			UserChristmasInfo christmas = Player.Actives.Christmas;
			string text = "堆雪人活动奖励";
			byte b = packet.ReadByte();
			int[] array = new int[] { 201146, 201147 };
			int num = ChristmasGetPakcsToPlayer.random.Next(array.Length);
			bool flag = DateTime.Compare(Player.LastOpenChristmasPackage.AddSeconds(1.0), DateTime.Now) > 0;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = b == 1 && christmas.DayPacks < 2;
				if (flag3)
				{
					UserChristmasInfo userChristmasInfo = christmas;
					int dayPacks = userChristmasInfo.DayPacks;
					userChristmasInfo.DayPacks = dayPacks + 1;
					Player.SendMessage(LanguageMgr.GetTranslation("ActiveSystemHandler.Msg4", Array.Empty<object>()));
					Player.SendItemToMail(array[num], "", text);
				}
				else
				{
					bool flag4 = christmas.Count < 3;
					if (flag4)
					{
						Player.SendMessage(LanguageMgr.GetTranslation("ActiveSystemHandler.Msg5", Array.Empty<object>()));
					}
					else
					{
						gspacketIn.WriteByte(27);
						gspacketIn.WriteBoolean(true);
						gspacketIn.WriteInt(christmas.DayPacks);
						gspacketIn.WriteInt(0);
						gspacketIn.WriteInt(0);
						Player.Out.SendTCP(gspacketIn);
					}
				}
				Player.LastOpenChristmasPackage = DateTime.Now;
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x04003C4E RID: 15438
		public static Random random = new Random();
	}
}
