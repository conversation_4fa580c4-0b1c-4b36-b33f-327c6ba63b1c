﻿using System;

namespace Game.Base.Events
{
	// Token: 0x02000F8E RID: 3982
	public abstract class RoadEvent
	{
		// Token: 0x170014D1 RID: 5329
		// (get) Token: 0x0600878E RID: 34702 RVA: 0x00035EDD File Offset: 0x000340DD
		public string Name
		{
			get
			{
				return this.m_EventName;
			}
		}

		// Token: 0x0600878F RID: 34703 RVA: 0x00035EE5 File Offset: 0x000340E5
		public RoadEvent(string name)
		{
			this.m_EventName = name;
		}

		// Token: 0x06008790 RID: 34704 RVA: 0x002C7B08 File Offset: 0x002C5D08
		public override string ToString()
		{
			return "DOLEvent(" + this.m_EventName + ")";
		}

		// Token: 0x06008791 RID: 34705 RVA: 0x00069EBC File Offset: 0x000680BC
		public virtual bool IsValidFor(object o)
		{
			return true;
		}

		// Token: 0x040053B4 RID: 21428
		protected string m_EventName;
	}
}
