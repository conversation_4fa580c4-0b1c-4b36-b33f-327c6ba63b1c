﻿using System;
using Bussiness;
using Game.Base.Packets;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C57 RID: 3159
	[ActiveSystemHandleAttbute(26)]
	public class ChristmasPacks : IActiveSystemCommandHadler
	{
		// Token: 0x06007032 RID: 28722 RVA: 0x0024DD8C File Offset: 0x0024BF8C
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			UserChristmasInfo christmas = Player.Actives.Christmas;
			string text = "冰雪圣诞活动奖励:";
			int num = packet.ReadInt();
			bool flag = DateTime.Compare(Player.LastOpenChristmasPackage.AddSeconds(1.0), DateTime.Now) > 0;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = christmas.PacksNumber >= GameProperties.ChristmasGiftsMaxNum - 1;
				if (flag3)
				{
					Player.SendMessage(LanguageMgr.GetTranslation("ActiveSystemHandler.Msg6", Array.Empty<object>()));
					flag2 = false;
				}
				else
				{
					string[] array = GameProperties.ChristmasGifts.Split(new char[] { '|' });
					string text2 = "";
					int num2 = 0;
					string[] array2 = array;
					string[] array3 = array2;
					foreach (string text3 in array3)
					{
						bool flag4 = text3.Split(new char[] { ',' })[0] == num.ToString();
						if (flag4)
						{
							text2 = text3;
							break;
						}
						num2++;
					}
					bool flag5 = text2 != "";
					if (flag5)
					{
						int num3 = int.Parse(text2.Split(new char[] { ',' })[1]);
						bool flag6 = ((christmas.AwardState >> num2) & 1) != 0;
						if (flag6)
						{
							flag2 = false;
						}
						else
						{
							bool flag7 = num2 >= array.Length - 1;
							if (flag7)
							{
								string text4 = array[num2 - 1];
								num3 += int.Parse(text4.Split(new char[] { ',' })[1]);
							}
							bool flag8 = num3 <= christmas.Count;
							if (flag8)
							{
								UserChristmasInfo userChristmasInfo = christmas;
								int packsNumber = userChristmasInfo.PacksNumber;
								userChristmasInfo.PacksNumber = packsNumber + 1;
								christmas.AwardState |= 1 << num2;
								Player.SendMessage(LanguageMgr.GetTranslation("ActiveSystemHandler.Msg4", Array.Empty<object>()));
								Player.SendItemToMail(num, "", text);
								gspacketIn.WriteByte(26);
								gspacketIn.WriteInt(christmas.AwardState);
								gspacketIn.WriteInt(christmas.PacksNumber);
								gspacketIn.WriteInt(num);
								Player.Out.SendTCP(gspacketIn);
								Player.LastOpenChristmasPackage = DateTime.Now;
								flag2 = true;
							}
							else
							{
								Player.SendMessage(LanguageMgr.GetTranslation("ActiveSystemHandler.Msg7", Array.Empty<object>()));
								flag2 = false;
							}
						}
					}
					else
					{
						Player.SendMessage(LanguageMgr.GetTranslation("ActiveSystemHandler.Msg8", Array.Empty<object>()));
						flag2 = false;
					}
				}
			}
			return flag2;
		}
	}
}
