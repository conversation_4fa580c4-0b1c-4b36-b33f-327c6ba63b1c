﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F37 RID: 3895
	public class TimeVortex3Effect : BaseCardEffect
	{
		// Token: 0x06008479 RID: 33913 RVA: 0x002B84BC File Offset: 0x002B66BC
		public TimeVortex3Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.TimeVortex3, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x0600847A RID: 33914 RVA: 0x002B852C File Offset: 0x002B672C
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.TimeVortex3) is TimeVortex3Effect;
			return flag || base.Start(living);
		}

		// Token: 0x0600847B RID: 33915 RVA: 0x00034F34 File Offset: 0x00033134
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x0600847C RID: 33916 RVA: 0x00034F4A File Offset: 0x0003314A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x0600847D RID: 33917 RVA: 0x002B8564 File Offset: 0x002B6764
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.Attack -= (double)this.m_added;
				player.Agility -= (double)this.m_added;
				player.Lucky -= (double)this.m_added;
				player.Defence -= (double)this.m_added;
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 12;
			if (flag2)
			{
				this.m_added = this.m_value;
				player.Attack += (double)this.m_added;
				player.Agility += (double)this.m_added;
				player.Lucky += (double)this.m_added;
				player.Defence += (double)this.m_added;
			}
		}

		// Token: 0x04005299 RID: 21145
		private int m_indexValue = 0;

		// Token: 0x0400529A RID: 21146
		private int m_value = 0;

		// Token: 0x0400529B RID: 21147
		private int m_added = 0;
	}
}
