﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E45 RID: 3653
	public class CE1019 : BasePetEffect
	{
		// Token: 0x06007F29 RID: 32553 RVA: 0x002A3EF8 File Offset: 0x002A20F8
		public CE1019(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1019, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F2A RID: 32554 RVA: 0x002A3F78 File Offset: 0x002A2178
		public override bool Start(Living living)
		{
			CE1019 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1019) as CE1019;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F2B RID: 32555 RVA: 0x002A3FD8 File Offset: 0x002A21D8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 400;
				player.Defence += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F2C RID: 32556 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F2D RID: 32557 RVA: 0x002A403C File Offset: 0x002A223C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F2E RID: 32558 RVA: 0x002A4070 File Offset: 0x002A2270
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Defence -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E1E RID: 19998
		private int m_type = 0;

		// Token: 0x04004E1F RID: 19999
		private int m_count = 0;

		// Token: 0x04004E20 RID: 20000
		private int m_probability = 0;

		// Token: 0x04004E21 RID: 20001
		private int m_delay = 0;

		// Token: 0x04004E22 RID: 20002
		private int m_coldDown = 0;

		// Token: 0x04004E23 RID: 20003
		private int m_currentId;

		// Token: 0x04004E24 RID: 20004
		private int m_added = 0;
	}
}
