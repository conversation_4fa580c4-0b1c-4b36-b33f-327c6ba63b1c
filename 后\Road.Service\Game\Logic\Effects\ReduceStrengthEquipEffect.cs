﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EF7 RID: 3831
	public class ReduceStrengthEquipEffect : BasePlayerEffect
	{
		// Token: 0x06008371 RID: 33649 RVA: 0x00034588 File Offset: 0x00032788
		public ReduceStrengthEquipEffect(int count, int probability)
			: base(eEffectType.ReduceStrengthEquipEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x06008372 RID: 33650 RVA: 0x002B3BD8 File Offset: 0x002B1DD8
		public override bool Start(Living living)
		{
			ReduceStrengthEquipEffect reduceStrengthEquipEffect = living.EffectList.GetOfType(eEffectType.ReduceStrengthEquipEffect) as ReduceStrengthEquipEffect;
			bool flag = reduceStrengthEquipEffect != null;
			bool flag2;
			if (flag)
			{
				reduceStrengthEquipEffect.m_probability = ((this.m_probability > reduceStrengthEquipEffect.m_probability) ? this.m_probability : reduceStrengthEquipEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008373 RID: 33651 RVA: 0x000345B0 File Offset: 0x000327B0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.ChangeProperty;
			player.AfterKillingLiving += this.player_AfterKillingLiving;
		}

		// Token: 0x06008374 RID: 33652 RVA: 0x002B3C34 File Offset: 0x002B1E34
		private void player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				target.AddEffect(new ReduceStrengthEffect(2, this.m_count), 0);
			}
		}

		// Token: 0x06008375 RID: 33653 RVA: 0x000345D9 File Offset: 0x000327D9
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.ChangeProperty;
			player.AfterKillingLiving -= this.player_AfterKillingLiving;
		}

		// Token: 0x06008376 RID: 33654 RVA: 0x002B3C64 File Offset: 0x002B1E64
		private void ChangeProperty(Player player, int ball)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				this.IsTrigger = true;
				player.AttackEffectTrigger = true;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("ReduceStrengthEquipEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x04005218 RID: 21016
		private int m_count = 0;

		// Token: 0x04005219 RID: 21017
		private int m_probability = 0;
	}
}
