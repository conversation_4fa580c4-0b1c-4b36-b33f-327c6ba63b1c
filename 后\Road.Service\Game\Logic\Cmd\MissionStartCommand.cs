﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F09 RID: 3849
	[GameCommand(82, "关卡开始")]
	public class MissionStartCommand : ICommandHandler
	{
		// Token: 0x060083AA RID: 33706 RVA: 0x002B51C8 File Offset: 0x002B33C8
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool flag = (game.GameState == eGameState.SessionPrepared || game.GameState == eGameState.GameOver) && packet.ReadBoolean();
			if (flag)
			{
				player.Ready = true;
				game.CheckState(0);
			}
		}
	}
}
