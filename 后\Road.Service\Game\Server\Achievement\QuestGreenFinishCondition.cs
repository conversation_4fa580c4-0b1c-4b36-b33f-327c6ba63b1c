﻿using System;
using Game.Server.GameObjects;
using Game.Server.Quests;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C72 RID: 3186
	public class QuestGreenFinishCondition : BaseCondition
	{
		// Token: 0x060070C3 RID: 28867 RVA: 0x0002A421 File Offset: 0x00028621
		public QuestGreenFinishCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x060070C4 RID: 28868 RVA: 0x0002A754 File Offset: 0x00028954
		public override void AddTrigger(GamePlayer player)
		{
			player.PlayerQuestFinish += this.player_PlayerQuestFinish;
		}

		// Token: 0x060070C5 RID: 28869 RVA: 0x00250588 File Offset: 0x0024E788
		private void player_PlayerQuestFinish(BaseQuest baseQuest)
		{
			bool flag = baseQuest.Info.RandDouble > 1;
			if (flag)
			{
				int value = base.Value;
				base.Value = value + 1;
			}
		}

		// Token: 0x060070C6 RID: 28870 RVA: 0x0002A76A File Offset: 0x0002896A
		public override void RemoveTrigger(GamePlayer player)
		{
			player.PlayerQuestFinish -= this.player_PlayerQuestFinish;
		}

		// Token: 0x060070C7 RID: 28871 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
