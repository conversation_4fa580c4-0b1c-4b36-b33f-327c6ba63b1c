﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D08 RID: 3336
	public class PetChangeSuitEquip : BasePetEffect
	{
		// Token: 0x06007885 RID: 30853 RVA: 0x0002CB38 File Offset: 0x0002AD38
		public PetChangeSuitEquip(int count, string suit, string elementID)
			: base(ePetEffectType.PetChangeSuitEquip, elementID)
		{
			this.m_count = count;
			this.m_suit = suit;
		}

		// Token: 0x06007886 RID: 30854 RVA: 0x002867E4 File Offset: 0x002849E4
		public override bool Start(Living living)
		{
			PetChangeSuitEquip petChangeSuitEquip = living.PetEffectList.GetOfType(ePetEffectType.PetChangeSuitEquip) as PetChangeSuitEquip;
			bool flag = petChangeSuitEquip != null;
			bool flag2;
			if (flag)
			{
				petChangeSuitEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007887 RID: 30855 RVA: 0x0028682C File Offset: 0x00284A2C
		protected override void OnAttachedToPlayer(Player player)
		{
			string[] array = player.PlayerDetail.PlayerCharacter.Style.Split(new char[] { ',' });
			array[7] = this.m_suit;
			string text = string.Join(",", array);
			player.Game.SendTempStyle(player, text, true);
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x06007888 RID: 30856 RVA: 0x0002CB56 File Offset: 0x0002AD56
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x06007889 RID: 30857 RVA: 0x00286894 File Offset: 0x00284A94
		private void player_BeginSelfTurn(Living living)
		{
			bool flag = base.ElementInfo.Pic != -1;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
			}
			this.m_count--;
			bool flag2 = this.m_count < 0;
			if (flag2)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				bool blackTiger = living.BlackTiger;
				if (blackTiger)
				{
					living.BlackTiger = false;
				}
				bool whiteTiger = living.WhiteTiger;
				if (whiteTiger)
				{
					living.WhiteTiger = false;
					living.miss -= 10;
				}
				bool devil = living.Devil;
				if (devil)
				{
					living.Devil = false;
					living.Die();
					Player player = living.Game.FindRandomPlayer(living);
					if (player != null)
					{
						player.Die();
					}
				}
				living.Game.SendTempStyle(living as Player, "", false);
				this.Stop();
			}
		}

		// Token: 0x040046A7 RID: 18087
		private int m_count;

		// Token: 0x040046A8 RID: 18088
		private string m_suit;
	}
}
