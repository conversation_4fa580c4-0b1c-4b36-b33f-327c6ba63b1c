﻿using System;

namespace Game.Logic.Actions
{
	// Token: 0x02000F62 RID: 3938
	public class LockFocusAction : BaseAction
	{
		// Token: 0x0600851E RID: 34078 RVA: 0x0003558C File Offset: 0x0003378C
		public LockFocusAction(bool isLock, int delay, int finishTime)
			: base(delay, finishTime)
		{
			this.m_isLock = isLock;
		}

		// Token: 0x0600851F RID: 34079 RVA: 0x0003559F File Offset: 0x0003379F
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			game.SendLockFocus(this.m_isLock);
			base.Finish(tick);
		}

		// Token: 0x0400532E RID: 21294
		private bool m_isLock;
	}
}
