﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E5E RID: 3678
	public class CE1063 : BasePetEffect
	{
		// Token: 0x06007FBE RID: 32702 RVA: 0x002A6580 File Offset: 0x002A4780
		public CE1063(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1063, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FBF RID: 32703 RVA: 0x002A6600 File Offset: 0x002A4800
		public override bool Start(Living living)
		{
			CE1063 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1063) as CE1063;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FC0 RID: 32704 RVA: 0x00031B73 File Offset: 0x0002FD73
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007FC1 RID: 32705 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FC2 RID: 32706 RVA: 0x002A6660 File Offset: 0x002A4860
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 500;
				living.SyncAtTime = true;
				living.AddBlood(this.m_added);
				living.SyncAtTime = false;
				foreach (Living living2 in living.Game.Map.FindAllNearestSameTeam(living.X, living.Y, 250.0, living))
				{
					bool flag2 = living2 != living;
					if (flag2)
					{
						living2.SyncAtTime = true;
						living2.AddBlood(this.m_added);
						living2.SyncAtTime = false;
					}
				}
			}
		}

		// Token: 0x06007FC3 RID: 32707 RVA: 0x00031B9C File Offset: 0x0002FD9C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004ECD RID: 20173
		private int m_type = 0;

		// Token: 0x04004ECE RID: 20174
		private int m_count = 0;

		// Token: 0x04004ECF RID: 20175
		private int m_probability = 0;

		// Token: 0x04004ED0 RID: 20176
		private int m_delay = 0;

		// Token: 0x04004ED1 RID: 20177
		private int m_coldDown = 0;

		// Token: 0x04004ED2 RID: 20178
		private int m_currentId;

		// Token: 0x04004ED3 RID: 20179
		private int m_added = 0;
	}
}
