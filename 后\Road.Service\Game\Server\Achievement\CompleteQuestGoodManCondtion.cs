﻿using System;
using Game.Server.GameObjects;
using Game.Server.Quests;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C60 RID: 3168
	public class CompleteQuestGoodManCondtion : BaseCondition
	{
		// Token: 0x0600706E RID: 28782 RVA: 0x0002A421 File Offset: 0x00028621
		public CompleteQuestGoodManCondtion(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x0600706F RID: 28783 RVA: 0x0002A42E File Offset: 0x0002862E
		public override void AddTrigger(GamePlayer player)
		{
			player.PlayerQuestFinish += this.player_PlayerQuestFinish;
		}

		// Token: 0x06007070 RID: 28784 RVA: 0x00250030 File Offset: 0x0024E230
		private void player_PlayerQuestFinish(BaseQuest baseQuest)
		{
			bool flag = baseQuest.Info.ID == 86;
			if (flag)
			{
				int value = base.Value;
				base.Value = value + 1;
			}
		}

		// Token: 0x06007071 RID: 28785 RVA: 0x0002A444 File Offset: 0x00028644
		public override void RemoveTrigger(GamePlayer player)
		{
			player.PlayerQuestFinish -= this.player_PlayerQuestFinish;
		}

		// Token: 0x06007072 RID: 28786 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
