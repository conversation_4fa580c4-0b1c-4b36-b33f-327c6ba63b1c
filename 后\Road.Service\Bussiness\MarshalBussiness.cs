﻿using System;
using System.IO;
using Newtonsoft.Json;
using zlib;

namespace Bussiness
{
	// Token: 0x02000FB3 RID: 4019
	public class MarshalBussiness : BaseBussiness
	{
		// Token: 0x060088BD RID: 35005 RVA: 0x002D348C File Offset: 0x002D168C
		public byte[] Compress(byte[] src)
		{
			return this.Compress(src, 0, src.Length);
		}

		// Token: 0x060088BE RID: 35006 RVA: 0x002D34AC File Offset: 0x002D16AC
		public byte[] Compress(byte[] src, int offset, int length)
		{
			MemoryStream memoryStream = new MemoryStream();
			Stream stream = new ZOutputStream(memoryStream, 9);
			stream.Write(src, offset, length);
			stream.Close();
			return memoryStream.ToArray();
		}

		// Token: 0x060088BF RID: 35007 RVA: 0x002D34E4 File Offset: 0x002D16E4
		public byte[] Uncompress(byte[] src)
		{
			return this.Uncompress(src, 0);
		}

		// Token: 0x060088C0 RID: 35008 RVA: 0x002D3500 File Offset: 0x002D1700
		public byte[] Uncompress(byte[] src, int offset)
		{
			MemoryStream memoryStream = new MemoryStream();
			Stream stream = new ZOutputStream(memoryStream);
			stream.Write(src, offset, src.Length);
			stream.Close();
			return memoryStream.ToArray();
		}

		// Token: 0x060088C1 RID: 35009 RVA: 0x002D3538 File Offset: 0x002D1738
		public T LoadDataJsonFile<T>(string filename, bool isEncrypt = false)
		{
			bool flag = !File.Exists(this.dataPatch + filename + ".json");
			T t;
			if (flag)
			{
				t = default(T);
			}
			else
			{
				try
				{
					using (StreamReader streamReader = File.OpenText(this.dataPatch + filename + ".json"))
					{
						JsonSerializer jsonSerializer = new JsonSerializer();
						return (T)((object)jsonSerializer.Deserialize(streamReader, typeof(T)));
					}
				}
				catch (Exception ex)
				{
					string text = "LoadDataJsonFile: ";
					Exception ex2 = ex;
					Console.WriteLine(text + ((ex2 != null) ? ex2.ToString() : null));
				}
				t = default(T);
			}
			return t;
		}

		// Token: 0x060088C2 RID: 35010 RVA: 0x002D3604 File Offset: 0x002D1804
		public bool SaveDataJsonFile<T>(T instance, string filename, bool isEncrypt = false)
		{
			try
			{
				bool flag = !Directory.Exists(this.dataPatch);
				if (flag)
				{
					Directory.CreateDirectory(this.dataPatch);
				}
				using (StreamWriter streamWriter = File.CreateText(this.dataPatch + filename + ".json"))
				{
					JsonSerializer jsonSerializer = new JsonSerializer
					{
						Formatting = Formatting.Indented
					};
					jsonSerializer.Serialize(streamWriter, instance);
				}
			}
			catch (Exception ex)
			{
				string text = "SaveDataJsonFile: ";
				Exception ex2 = ex;
				Console.WriteLine(text + ((ex2 != null) ? ex2.ToString() : null));
			}
			return false;
		}

		// Token: 0x060088C3 RID: 35011 RVA: 0x002D36BC File Offset: 0x002D18BC
		public bool SaveLogJsonFile<T>(T instance, string filename, bool isEncrypt = false)
		{
			string text = this.dataPatch + "\\logs\\";
			try
			{
				bool flag = !Directory.Exists(text);
				if (flag)
				{
					Directory.CreateDirectory(text);
				}
				using (StreamWriter streamWriter = File.CreateText(text + filename + ".json"))
				{
					JsonSerializer jsonSerializer = new JsonSerializer
					{
						Formatting = Formatting.Indented
					};
					jsonSerializer.Serialize(streamWriter, instance);
				}
			}
			catch (Exception ex)
			{
				string text2 = "SaveLogJsonFile: ";
				Exception ex2 = ex;
				Console.WriteLine(text2 + ((ex2 != null) ? ex2.ToString() : null));
			}
			return false;
		}

		// Token: 0x04005499 RID: 21657
		private string dataPatch = "datas\\";
	}
}
