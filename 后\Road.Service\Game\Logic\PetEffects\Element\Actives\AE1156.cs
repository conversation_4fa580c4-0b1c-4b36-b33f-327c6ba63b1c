﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DDE RID: 3550
	public class AE1156 : BasePetEffect
	{
		// Token: 0x06007D05 RID: 32005 RVA: 0x00299E64 File Offset: 0x00298064
		public AE1156(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1156, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D06 RID: 32006 RVA: 0x00299EE4 File Offset: 0x002980E4
		public override bool Start(Living living)
		{
			AE1156 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1156) as AE1156;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D07 RID: 32007 RVA: 0x00030156 File Offset: 0x0002E356
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D08 RID: 32008 RVA: 0x0003016C File Offset: 0x0002E36C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D09 RID: 32009 RVA: 0x00299F44 File Offset: 0x00298144
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1156(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004B4A RID: 19274
		private int m_type = 0;

		// Token: 0x04004B4B RID: 19275
		private int m_count = 0;

		// Token: 0x04004B4C RID: 19276
		private int m_probability = 0;

		// Token: 0x04004B4D RID: 19277
		private int m_delay = 0;

		// Token: 0x04004B4E RID: 19278
		private int m_coldDown = 0;

		// Token: 0x04004B4F RID: 19279
		private int m_currentId;

		// Token: 0x04004B50 RID: 19280
		private int m_added = 0;
	}
}
