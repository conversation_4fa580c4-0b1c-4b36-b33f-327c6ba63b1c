﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E94 RID: 3732
	public class CE1201 : BasePetEffect
	{
		// Token: 0x06008112 RID: 33042 RVA: 0x002AB898 File Offset: 0x002A9A98
		public CE1201(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1201, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008113 RID: 33043 RVA: 0x002AB918 File Offset: 0x002A9B18
		public override bool Start(Living living)
		{
			CE1201 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1201) as CE1201;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008114 RID: 33044 RVA: 0x000327D6 File Offset: 0x000309D6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008115 RID: 33045 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008116 RID: 33046 RVA: 0x002AB978 File Offset: 0x002A9B78
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				bool flag2 = living.BaseDamage < (double)this.m_added;
				if (flag2)
				{
					this.m_added = (int)living.BaseDamage - 1;
				}
				living.BaseDamage -= (double)this.m_added;
			}
		}

		// Token: 0x06008117 RID: 33047 RVA: 0x002AB9D8 File Offset: 0x002A9BD8
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008118 RID: 33048 RVA: 0x002ABA0C File Offset: 0x002A9C0C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BaseDamage += (double)this.m_added;
			this.m_added = 0;
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005049 RID: 20553
		private int m_type = 0;

		// Token: 0x0400504A RID: 20554
		private int m_count = 0;

		// Token: 0x0400504B RID: 20555
		private int m_probability = 0;

		// Token: 0x0400504C RID: 20556
		private int m_delay = 0;

		// Token: 0x0400504D RID: 20557
		private int m_coldDown = 0;

		// Token: 0x0400504E RID: 20558
		private int m_currentId;

		// Token: 0x0400504F RID: 20559
		private int m_added = 0;
	}
}
