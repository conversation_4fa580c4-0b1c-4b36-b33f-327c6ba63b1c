﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E72 RID: 3698
	public class CE1132 : BasePetEffect
	{
		// Token: 0x0600803A RID: 32826 RVA: 0x002A840C File Offset: 0x002A660C
		public CE1132(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1132, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600803B RID: 32827 RVA: 0x002A848C File Offset: 0x002A668C
		public override bool Start(Living living)
		{
			CE1132 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1132) as CE1132;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600803C RID: 32828 RVA: 0x002A84EC File Offset: 0x002A66EC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.Attack -= (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600803D RID: 32829 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600803E RID: 32830 RVA: 0x002A854C File Offset: 0x002A674C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600803F RID: 32831 RVA: 0x00031FE4 File Offset: 0x000301E4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Attack += (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F54 RID: 20308
		private int m_type = 0;

		// Token: 0x04004F55 RID: 20309
		private int m_count = 0;

		// Token: 0x04004F56 RID: 20310
		private int m_probability = 0;

		// Token: 0x04004F57 RID: 20311
		private int m_delay = 0;

		// Token: 0x04004F58 RID: 20312
		private int m_coldDown = 0;

		// Token: 0x04004F59 RID: 20313
		private int m_currentId;

		// Token: 0x04004F5A RID: 20314
		private int m_added = 0;
	}
}
