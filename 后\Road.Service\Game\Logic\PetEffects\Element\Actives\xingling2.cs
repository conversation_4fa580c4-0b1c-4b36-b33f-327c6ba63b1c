﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E42 RID: 3650
	public class xingling2 : BasePetEffect
	{
		// Token: 0x06007F18 RID: 32536 RVA: 0x002A3A28 File Offset: 0x002A1C28
		public xingling2(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.xingling2, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F19 RID: 32537 RVA: 0x002A3AA8 File Offset: 0x002A1CA8
		public override bool Start(Living living)
		{
			xingling2 xingling = living.PetEffectList.GetOfType(ePetEffectType.xingling2) as xingling2;
			bool flag = xingling != null;
			bool flag2;
			if (flag)
			{
				xingling.m_probability = ((this.m_probability > xingling.m_probability) ? this.m_probability : xingling.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F1A RID: 32538 RVA: 0x000316AC File Offset: 0x0002F8AC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007F1B RID: 32539 RVA: 0x000316C2 File Offset: 0x0002F8C2
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007F1C RID: 32540 RVA: 0x002A3B08 File Offset: 0x002A1D08
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddPetEffect(new xingling3(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004E09 RID: 19977
		private int m_type = 0;

		// Token: 0x04004E0A RID: 19978
		private int m_count = 0;

		// Token: 0x04004E0B RID: 19979
		private int m_probability = 0;

		// Token: 0x04004E0C RID: 19980
		private int m_delay = 0;

		// Token: 0x04004E0D RID: 19981
		private int m_coldDown = 0;

		// Token: 0x04004E0E RID: 19982
		private int m_currentId;

		// Token: 0x04004E0F RID: 19983
		private int m_added = 0;
	}
}
