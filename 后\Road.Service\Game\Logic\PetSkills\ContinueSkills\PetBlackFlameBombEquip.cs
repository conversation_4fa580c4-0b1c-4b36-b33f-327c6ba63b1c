﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D06 RID: 3334
	public class PetBlackFlameBombEquip : BasePetEffect
	{
		// Token: 0x0600787B RID: 30843 RVA: 0x0002CA81 File Offset: 0x0002AC81
		public PetBlackFlameBombEquip(int count, string elementID)
			: base(ePetEffectType.PetBlackFlameBombEquip, elementID)
		{
			this.m_count = count;
			this.oldPowerRatio = 100;
		}

		// Token: 0x0600787C RID: 30844 RVA: 0x00286670 File Offset: 0x00284870
		public override bool Start(Living living)
		{
			PetBlackFlameBombEquip petBlackFlameBombEquip = living.PetEffectList.GetOfType(ePetEffectType.PetBlackFlameBombEquip) as PetBlackFlameBombEquip;
			bool flag = petBlackFlameBombEquip != null;
			bool flag2;
			if (flag)
			{
				petBlackFlameBombEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600787D RID: 30845 RVA: 0x0002CAA0 File Offset: 0x0002ACA0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x0600787E RID: 30846 RVA: 0x0002CAB6 File Offset: 0x0002ACB6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x0600787F RID: 30847 RVA: 0x002866B8 File Offset: 0x002848B8
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count <= 0;
			if (flag)
			{
				((Player)living).IsBlackFlame = false;
				living.CanFrozen = true;
				this.Stop();
			}
			((Player)living).SetBall(200);
			((Player)living).IsBlackFlame = true;
			living.CanFrozen = false;
		}

		// Token: 0x040046A4 RID: 18084
		private int m_count;

		// Token: 0x040046A5 RID: 18085
		private int oldPowerRatio;
	}
}
