﻿using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.ServiceModel;
using System.ServiceModel.Channels;

namespace Bussiness.WebLogin
{
	// Token: 0x02000FC0 RID: 4032
	[DebuggerStepThrough]
	[GeneratedCode("System.ServiceModel", "4.0.0.0")]
	public class PassPortSoapClient : ClientBase<PassPortSoap>, PassPortSoap
	{
		// Token: 0x06008A52 RID: 35410 RVA: 0x000363C7 File Offset: 0x000345C7
		public PassPortSoapClient()
		{
		}

		// Token: 0x06008A53 RID: 35411 RVA: 0x000363D1 File Offset: 0x000345D1
		public PassPortSoapClient(string endpointConfigurationName)
			: base(endpointConfigurationName)
		{
		}

		// Token: 0x06008A54 RID: 35412 RVA: 0x000363DC File Offset: 0x000345DC
		public PassPortSoapClient(string endpointConfigurationName, string remoteAddress)
			: base(endpointConfigurationName, remoteAddress)
		{
		}

		// Token: 0x06008A55 RID: 35413 RVA: 0x000363E8 File Offset: 0x000345E8
		public PassPortSoapClient(string endpointConfigurationName, EndpointAddress remoteAddress)
			: base(endpointConfigurationName, remoteAddress)
		{
		}

		// Token: 0x06008A56 RID: 35414 RVA: 0x000363F4 File Offset: 0x000345F4
		public PassPortSoapClient(Binding binding, EndpointAddress remoteAddress)
			: base(binding, remoteAddress)
		{
		}

		// Token: 0x06008A57 RID: 35415 RVA: 0x002F680C File Offset: 0x002F4A0C
		public string ChenckValidate(string username, string password)
		{
			return base.Channel.ChenckValidate(username, password);
		}

		// Token: 0x06008A58 RID: 35416 RVA: 0x002F682C File Offset: 0x002F4A2C
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		Get_UserSexResponse PassPortSoap.Get_UserSex(Get_UserSexRequest request)
		{
			return base.Channel.Get_UserSex(request);
		}

		// Token: 0x06008A59 RID: 35417 RVA: 0x002F684C File Offset: 0x002F4A4C
		public bool? Get_UserSex(string username)
		{
			Get_UserSexResponse get_UserSexResponse = ((PassPortSoap)this).Get_UserSex(new Get_UserSexRequest
			{
				username = username
			});
			return get_UserSexResponse.Get_UserSexResult;
		}
	}
}
