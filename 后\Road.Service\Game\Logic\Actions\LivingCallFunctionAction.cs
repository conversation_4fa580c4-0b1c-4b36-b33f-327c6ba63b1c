﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F52 RID: 3922
	public class LivingCallFunctionAction : BaseAction
	{
		// Token: 0x060084FC RID: 34044 RVA: 0x0003532D File Offset: 0x0003352D
		public LivingCallFunctionAction(Living living, LivingCallBack func, int delay)
			: base(delay)
		{
			this.m_living = living;
			this.m_func = func;
		}

		// Token: 0x060084FD RID: 34045 RVA: 0x002B9730 File Offset: 0x002B7930
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			try
			{
				this.m_func();
			}
			finally
			{
				base.Finish(tick);
			}
		}

		// Token: 0x040052E7 RID: 21223
		private Living m_living;

		// Token: 0x040052E8 RID: 21224
		private LivingCallBack m_func;
	}
}
