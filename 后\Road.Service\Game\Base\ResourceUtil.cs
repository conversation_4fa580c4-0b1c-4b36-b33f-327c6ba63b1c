﻿using System;
using System.IO;
using System.Reflection;

namespace Game.Base
{
	// Token: 0x02000F7C RID: 3964
	public class ResourceUtil
	{
		// Token: 0x060085EA RID: 34282 RVA: 0x002BCF38 File Offset: 0x002BB138
		public static Stream GetResourceStream(string fileName, Assembly assem)
		{
			fileName = fileName.ToLower();
			string[] manifestResourceNames = assem.GetManifestResourceNames();
			string[] array = manifestResourceNames;
			string[] array2 = array;
			foreach (string text in array2)
			{
				bool flag = text.ToLower().EndsWith(fileName);
				if (flag)
				{
					return assem.GetManifestResourceStream(text);
				}
			}
			return null;
		}

		// Token: 0x060085EB RID: 34283 RVA: 0x00035B9B File Offset: 0x00033D9B
		public static void ExtractResource(string fileName, Assembly assembly)
		{
			ResourceUtil.ExtractResource(fileName, fileName, assembly);
		}

		// Token: 0x060085EC RID: 34284 RVA: 0x002BCFA0 File Offset: 0x002BB1A0
		public static void ExtractResource(string resourceName, string fileName, Assembly assembly)
		{
			FileInfo fileInfo = new FileInfo(fileName);
			bool flag = !fileInfo.Directory.Exists;
			if (flag)
			{
				fileInfo.Directory.Create();
			}
			using (StreamReader streamReader = new StreamReader(ResourceUtil.GetResourceStream(resourceName, assembly)))
			{
				using (StreamWriter streamWriter = new StreamWriter(File.Create(fileName)))
				{
					streamWriter.Write(streamReader.ReadToEnd());
				}
			}
		}
	}
}
