﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F2E RID: 3886
	public class GuluSportsMeeting5Effect : BaseCardEffect
	{
		// Token: 0x0600844A RID: 33866 RVA: 0x002B7764 File Offset: 0x002B5964
		public GuluSportsMeeting5Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.GuluSportsMeeting5, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x0600844B RID: 33867 RVA: 0x002B77D4 File Offset: 0x002B59D4
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.GuluSportsMeeting5) is GuluSportsMeeting5Effect;
			return flag || base.Start(living);
		}

		// Token: 0x0600844C RID: 33868 RVA: 0x00034D7A File Offset: 0x00032F7A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x0600844D RID: 33869 RVA: 0x00034D90 File Offset: 0x00032F90
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x0600844E RID: 33870 RVA: 0x002B780C File Offset: 0x002B5A0C
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.Attack -= (double)this.m_added;
				player.Agility -= (double)this.m_added;
				player.Lucky -= (double)this.m_added;
				player.Defence -= (double)this.m_added;
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 6;
			if (flag2)
			{
				this.m_added = this.m_value;
				player.Attack += (double)this.m_added;
				player.Agility += (double)this.m_added;
				player.Lucky += (double)this.m_added;
				player.Defence += (double)this.m_added;
				player.Game.SendMessage(player.PlayerDetail, "您激活了啵咕运动会5件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活啵咕运动会5件套卡.", 3);
			}
		}

		// Token: 0x0400527E RID: 21118
		private int m_indexValue = 0;

		// Token: 0x0400527F RID: 21119
		private int m_value = 0;

		// Token: 0x04005280 RID: 21120
		private int m_added = 0;
	}
}
