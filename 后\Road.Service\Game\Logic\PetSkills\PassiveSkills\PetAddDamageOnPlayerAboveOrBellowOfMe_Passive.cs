﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CF2 RID: 3314
	public class PetAddDamageOnPlayerAboveOrBellowOfMe_Passive : BasePetEffect
	{
		// Token: 0x06007813 RID: 30739 RVA: 0x00283DC8 File Offset: 0x00281FC8
		public PetAddDamageOnPlayerAboveOrBellowOfMe_Passive(int probability, string elementID)
			: base(ePetEffectType.PetAddDamageOnPlayerAboveOrBellowOfMe_Passive, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			bool flag = elementID == null;
			if (!flag)
			{
				int length = elementID.Length;
				bool flag2 = length != 4;
				if (!flag2)
				{
					switch (elementID[3])
					{
					case '3':
					{
						bool flag3 = elementID == "1723";
						if (flag3)
						{
							this.damagePerPixel = 10;
							this.pixels = 100;
							this.AboveOrBelow = PlayerAboveOrBelow.Below;
							this.maxValue = 120;
						}
						break;
					}
					case '4':
					{
						bool flag4 = elementID == "1724";
						if (flag4)
						{
							this.damagePerPixel = 20;
							this.pixels = 100;
							this.AboveOrBelow = PlayerAboveOrBelow.Below;
							this.maxValue = 120;
						}
						break;
					}
					case '5':
					{
						bool flag5 = elementID == "1725" || elementID == "1775";
						if (flag5)
						{
							this.damagePerPixel = 30;
							this.pixels = 100;
							this.AboveOrBelow = PlayerAboveOrBelow.Below;
							this.maxValue = 120;
						}
						break;
					}
					case '7':
					{
						bool flag6 = elementID == "1807";
						if (flag6)
						{
							this.damagePerPixel = 6;
							this.pixels = 100;
							this.AboveOrBelow = PlayerAboveOrBelow.Below;
							this.maxValue = 60;
						}
						break;
					}
					case '8':
					{
						bool flag7 = elementID == "1808";
						if (flag7)
						{
							this.damagePerPixel = 8;
							this.pixels = 100;
							this.AboveOrBelow = PlayerAboveOrBelow.Below;
							this.maxValue = 60;
						}
						break;
					}
					case '9':
					{
						bool flag8 = elementID == "1809";
						if (flag8)
						{
							this.damagePerPixel = 10;
							this.pixels = 100;
							this.AboveOrBelow = PlayerAboveOrBelow.Below;
							this.maxValue = 60;
						}
						break;
					}
					}
				}
			}
		}

		// Token: 0x06007814 RID: 30740 RVA: 0x00283FA4 File Offset: 0x002821A4
		public override bool Start(Living living)
		{
			bool flag = living.Game.IsPVE();
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				PetAddDamageOnPlayerAboveOrBellowOfMe_Passive petAddDamageOnPlayerAboveOrBellowOfMe_Passive = living.PetEffectList.GetOfType(ePetEffectType.PetAddDamageOnPlayerAboveOrBellowOfMe_Passive) as PetAddDamageOnPlayerAboveOrBellowOfMe_Passive;
				bool flag3 = petAddDamageOnPlayerAboveOrBellowOfMe_Passive != null;
				if (flag3)
				{
					petAddDamageOnPlayerAboveOrBellowOfMe_Passive.m_probability = ((this.m_probability > petAddDamageOnPlayerAboveOrBellowOfMe_Passive.m_probability) ? this.m_probability : petAddDamageOnPlayerAboveOrBellowOfMe_Passive.m_probability);
					flag2 = true;
				}
				else
				{
					flag2 = base.Start(living);
				}
			}
			return flag2;
		}

		// Token: 0x06007815 RID: 30741 RVA: 0x0002C5CC File Offset: 0x0002A7CC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.OnMakeDamageEvent += this.player_OnMakeDamageEvent;
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x06007816 RID: 30742 RVA: 0x0002C5F5 File Offset: 0x0002A7F5
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.OnMakeDamageEvent -= this.player_OnMakeDamageEvent;
		}

		// Token: 0x06007817 RID: 30743 RVA: 0x0002C60B File Offset: 0x0002A80B
		private void player_BeginSelfTurn(Living living)
		{
			living.Game.sendShowPicSkil(living, base.ElementInfo, true);
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x06007818 RID: 30744 RVA: 0x00284018 File Offset: 0x00282218
		private void player_OnMakeDamageEvent(Living target, Living source, ref int damageamount, ref int criticalamount)
		{
			int num = 0;
			bool flag = this.AboveOrBelow == PlayerAboveOrBelow.Below;
			if (flag)
			{
				bool flag2 = source.Y < target.Y;
				if (flag2)
				{
					int num2 = Math.Abs(target.Y - source.Y);
					num = this.damagePerPixel * num2 / this.pixels;
				}
				source.ExtraDamageByY = true;
			}
			bool flag3 = num >= this.maxValue;
			if (flag3)
			{
				num = this.maxValue;
			}
			damageamount += damageamount * num / 100;
			criticalamount += criticalamount * num / 100;
		}

		// Token: 0x04004660 RID: 18016
		private int m_probability;

		// Token: 0x04004661 RID: 18017
		private int maxValue;

		// Token: 0x04004662 RID: 18018
		private int damagePerPixel;

		// Token: 0x04004663 RID: 18019
		private int pixels;

		// Token: 0x04004664 RID: 18020
		private PlayerAboveOrBelow AboveOrBelow;
	}
}
