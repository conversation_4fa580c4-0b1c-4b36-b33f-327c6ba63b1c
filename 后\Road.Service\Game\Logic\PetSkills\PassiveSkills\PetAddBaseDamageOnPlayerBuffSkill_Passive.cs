﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CEF RID: 3311
	public class PetAddBaseDamageOnPlayerBuffSkill_Passive : BasePetEffect
	{
		// Token: 0x06007804 RID: 30724 RVA: 0x00283ACC File Offset: 0x00281CCC
		public PetAddBaseDamageOnPlayerBuffSkill_Passive(string elementID)
			: base(ePetEffectType.PetAddBaseDamageOnPlayerBuffSkill_Passive, elementID)
		{
			bool flag = !(elementID == "3218");
			if (flag)
			{
				bool flag2 = elementID == "3229";
				if (flag2)
				{
					this.m_added = 20;
				}
			}
			else
			{
				this.m_added = 10;
			}
		}

		// Token: 0x06007805 RID: 30725 RVA: 0x00283B20 File Offset: 0x00281D20
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetAddBaseDamageOnPlayerBuffSkill_Passive) is PetAddBaseDamageOnPlayerBuffSkill_Passive;
			return flag || base.Start(living);
		}

		// Token: 0x06007806 RID: 30726 RVA: 0x0002C4BA File Offset: 0x0002A6BA
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkill += new PlayerEventHandle(this.player_PlayerBuffSkill);
		}

		// Token: 0x06007807 RID: 30727 RVA: 0x0002C4D0 File Offset: 0x0002A6D0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkill -= new PlayerEventHandle(this.player_PlayerBuffSkill);
			player.BaseDamage = player.OldBaseDamage;
			player.Game.sendShowPicSkil(player, base.ElementInfo, false);
		}

		// Token: 0x06007808 RID: 30728 RVA: 0x00283B5C File Offset: 0x00281D5C
		private void player_PlayerBuffSkill(Living living)
		{
			living.Game.sendShowPicSkil(living, base.ElementInfo, false);
			living.BaseDamage += living.BaseDamage * (double)this.m_added / 100.0;
			living.Game.sendShowPicSkil(living, base.ElementInfo, true);
		}

		// Token: 0x04004657 RID: 18007
		private int m_added;
	}
}
