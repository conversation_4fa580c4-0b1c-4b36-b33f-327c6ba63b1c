﻿using System;
using Game.Logic.Effects;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F60 RID: 3936
	public class LivingSealAction : BaseAction
	{
		// Token: 0x0600851A RID: 34074 RVA: 0x00035542 File Offset: 0x00033742
		public LivingSealAction(Living Living, Living target, int type, int delay)
			: base(delay, 2000)
		{
			this.m_Living = Living;
			this.m_Target = target;
			this.m_Type = type;
		}

		// Token: 0x0600851B RID: 34075 RVA: 0x00035568 File Offset: 0x00033768
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			this.m_Target.AddEffect(new SealEffect(2, this.m_Type), 0);
			base.Finish(tick);
		}

		// Token: 0x04005320 RID: 21280
		private Living m_Living;

		// Token: 0x04005321 RID: 21281
		private Living m_Target;

		// Token: 0x04005322 RID: 21282
		private int m_Type;
	}
}
