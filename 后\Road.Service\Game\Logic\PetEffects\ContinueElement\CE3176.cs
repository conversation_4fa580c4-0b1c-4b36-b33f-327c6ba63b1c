﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EC3 RID: 3779
	public class CE3176 : BasePetEffect
	{
		// Token: 0x06008246 RID: 33350 RVA: 0x002B02D4 File Offset: 0x002AE4D4
		public CE3176(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE3176, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008247 RID: 33351 RVA: 0x002B0350 File Offset: 0x002AE550
		public override bool Start(Living living)
		{
			CE3176 ce = living.PetEffectList.GetOfType(ePetEffectType.CE3176) as CE3176;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008248 RID: 33352 RVA: 0x0003338A File Offset: 0x0003158A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008249 RID: 33353 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600824A RID: 33354 RVA: 0x002B03AC File Offset: 0x002AE5AC
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				living.SyncAtTime = true;
				int num = living.Blood * 25 / 100;
				living.AddBlood(num);
				living.SyncAtTime = false;
			}
		}

		// Token: 0x0600824B RID: 33355 RVA: 0x000333B3 File Offset: 0x000315B3
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005193 RID: 20883
		private int m_type = 0;

		// Token: 0x04005194 RID: 20884
		private int m_count = 0;

		// Token: 0x04005195 RID: 20885
		private int m_probability = 0;

		// Token: 0x04005196 RID: 20886
		private int m_delay = 0;

		// Token: 0x04005197 RID: 20887
		private int m_coldDown = 0;

		// Token: 0x04005198 RID: 20888
		private int m_currentId;

		// Token: 0x04005199 RID: 20889
		private int m_added = 0;
	}
}
