﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.NormalSpell
{
	// Token: 0x02000CB8 RID: 3256
	[SpellAttibute(128)]
	public class CatchBallSpell : ISpellHandler
	{
		// Token: 0x060074F2 RID: 29938 RVA: 0x0026E054 File Offset: 0x0026C254
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.SetBall(128);
			}
		}
	}
}
