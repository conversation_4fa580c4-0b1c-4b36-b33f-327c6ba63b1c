﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.NormalSpell
{
	// Token: 0x02000CB9 RID: 3257
	[SpellAttibute(2)]
	public class FrostSpell : ISpellHandler
	{
		// Token: 0x060074F4 RID: 29940 RVA: 0x0026E07C File Offset: 0x0026C27C
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.SetBall(1);
			}
			else
			{
				bool flag = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
				if (flag)
				{
					(game.CurrentLiving as Player).SetBall(1);
				}
			}
		}
	}
}
