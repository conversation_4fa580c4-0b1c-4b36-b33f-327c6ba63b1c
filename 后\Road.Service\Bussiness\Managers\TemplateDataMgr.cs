﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using EntityDatabase.ServerModels;
using log4net;

namespace Bussiness.Managers
{
	// Token: 0x02000FEC RID: 4076
	public class TemplateDataMgr
	{
		// Token: 0x06008B71 RID: 35697 RVA: 0x002FC9D0 File Offset: 0x002FABD0
		public static bool Init()
		{
			bool flag;
			try
			{
				flag = TemplateDataMgr.Load(TemplateDataMgr.m_tsUpgrades);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = TemplateDataMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					TemplateDataMgr.log.Error("TemplateDataMgr", ex);
				}
				flag = false;
			}
			return flag;
		}

		// Token: 0x06008B72 RID: 35698 RVA: 0x002FCA24 File Offset: 0x002FAC24
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, TS_Upgrade> dictionary = new Dictionary<int, TS_Upgrade>();
				bool flag = TemplateDataMgr.Load(dictionary);
				if (flag)
				{
					try
					{
						TemplateDataMgr.m_tsUpgrades = dictionary;
						return true;
					}
					catch
					{
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = TemplateDataMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					TemplateDataMgr.log.Error("TemplateDataMgr", ex);
				}
			}
			return false;
		}

		// Token: 0x06008B73 RID: 35699 RVA: 0x002FCAA4 File Offset: 0x002FACA4
		private static bool Load(Dictionary<int, TS_Upgrade> item)
		{
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				TS_Upgrade[] allTemplateData = produceBussiness.GetAllTemplateData();
				TS_Upgrade[] array = allTemplateData;
				foreach (TS_Upgrade ts_Upgrade in array)
				{
					bool flag = !item.ContainsKey(ts_Upgrade.Types);
					if (flag)
					{
						item.Add(ts_Upgrade.Types, ts_Upgrade);
					}
					int types = ts_Upgrade.Types;
					int num = types;
					if (num != 101)
					{
						if (num == 104)
						{
							bool flag2 = !TemplateDataMgr.m_enchantSpiritDic.ContainsKey(ts_Upgrade.Grades);
							if (flag2)
							{
								TemplateDataMgr.m_enchantSpiritDic.Add(ts_Upgrade.Grades, ts_Upgrade);
							}
						}
					}
					else
					{
						bool flag3 = !TemplateDataMgr.m_totemUpgrades.ContainsKey(ts_Upgrade.TemplateId);
						if (flag3)
						{
							TemplateDataMgr.m_totemUpgrades.Add(ts_Upgrade.TemplateId, new List<TS_Upgrade>());
						}
						TemplateDataMgr.m_totemUpgrades[ts_Upgrade.TemplateId].Add(ts_Upgrade);
					}
				}
			}
			return true;
		}

		// Token: 0x06008B74 RID: 35700 RVA: 0x002FCBD4 File Offset: 0x002FADD4
		public static TS_Upgrade GetTotemUpGradeInfo(int templateId, int grade)
		{
			TS_Upgrade ts_Upgrade = null;
			bool flag = TemplateDataMgr.m_totemUpgrades.ContainsKey(templateId);
			TS_Upgrade ts_Upgrade2;
			if (flag)
			{
				ts_Upgrade2 = TemplateDataMgr.m_totemUpgrades[templateId].SingleOrDefault((TS_Upgrade a) => a.Grades == grade);
			}
			else
			{
				ts_Upgrade2 = ts_Upgrade;
			}
			return ts_Upgrade2;
		}

		// Token: 0x06008B75 RID: 35701 RVA: 0x002FCC28 File Offset: 0x002FAE28
		public static TS_Upgrade GetEnchantUpgradeInfo(int grade)
		{
			bool flag = TemplateDataMgr.m_enchantSpiritDic.ContainsKey(grade);
			TS_Upgrade ts_Upgrade;
			if (flag)
			{
				ts_Upgrade = TemplateDataMgr.m_enchantSpiritDic[grade];
			}
			else
			{
				ts_Upgrade = null;
			}
			return ts_Upgrade;
		}

		// Token: 0x0400554F RID: 21839
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005550 RID: 21840
		private static Dictionary<int, TS_Upgrade> m_tsUpgrades = new Dictionary<int, TS_Upgrade>();

		// Token: 0x04005551 RID: 21841
		private static Dictionary<int, List<TS_Upgrade>> m_totemUpgrades = new Dictionary<int, List<TS_Upgrade>>();

		// Token: 0x04005552 RID: 21842
		private static Dictionary<int, TS_Upgrade> m_enchantSpiritDic = new Dictionary<int, TS_Upgrade>();
	}
}
