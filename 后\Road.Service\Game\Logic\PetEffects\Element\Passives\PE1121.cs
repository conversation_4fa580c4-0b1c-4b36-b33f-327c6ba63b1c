﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D67 RID: 3431
	public class PE1121 : BasePetEffect
	{
		// Token: 0x06007A9A RID: 31386 RVA: 0x0028F918 File Offset: 0x0028DB18
		public PE1121(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1121, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A9B RID: 31387 RVA: 0x0028F994 File Offset: 0x0028DB94
		public override bool Start(Living living)
		{
			PE1121 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1121) as PE1121;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A9C RID: 31388 RVA: 0x0002E8EC File Offset: 0x0002CAEC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007A9D RID: 31389 RVA: 0x0028F9F0 File Offset: 0x0028DBF0
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = living.PetEffects.AddGuardValue == 0;
			if (flag)
			{
				this.m_added = 100;
				living.BaseGuard += (double)this.m_added;
				living.PetEffects.AddGuardValue += this.m_added;
				living.Game.SendPetBuff(living, base.Info, true);
			}
		}

		// Token: 0x06007A9E RID: 31390 RVA: 0x0002E902 File Offset: 0x0002CB02
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x04004811 RID: 18449
		private int m_type = 0;

		// Token: 0x04004812 RID: 18450
		private int m_count = 0;

		// Token: 0x04004813 RID: 18451
		private int m_probability = 0;

		// Token: 0x04004814 RID: 18452
		private int m_delay = 0;

		// Token: 0x04004815 RID: 18453
		private int m_coldDown = 0;

		// Token: 0x04004816 RID: 18454
		private int m_currentId;

		// Token: 0x04004817 RID: 18455
		private int m_added = 0;
	}
}
