﻿using System;
using Game.Logic.CardEffect;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffects
{
	// Token: 0x02000F19 RID: 3865
	public abstract class AbstractCardEffect
	{
		// Token: 0x170014A4 RID: 5284
		// (get) Token: 0x060083CB RID: 33739 RVA: 0x00034807 File Offset: 0x00032A07
		public CardBuffInfo BuffInfo
		{
			get
			{
				return this.m_buffInfo;
			}
		}

		// Token: 0x170014A5 RID: 5285
		// (get) Token: 0x060083CC RID: 33740 RVA: 0x0003480F File Offset: 0x00032A0F
		public eCardEffectType Type
		{
			get
			{
				return this.m_type;
			}
		}

		// Token: 0x170014A6 RID: 5286
		// (get) Token: 0x060083CD RID: 33741 RVA: 0x0003480F File Offset: 0x00032A0F
		public int TypeValue
		{
			get
			{
				return (int)this.m_type;
			}
		}

		// Token: 0x060083CE RID: 33742 RVA: 0x00034817 File Offset: 0x00032A17
		public AbstractCardEffect(eCardEffectType type, CardBuffInfo buff)
		{
			this.rand = new Random();
			this.m_type = type;
			this.m_buffInfo = buff;
		}

		// Token: 0x060083CF RID: 33743 RVA: 0x002B5A3C File Offset: 0x002B3C3C
		public virtual bool Start(Living living)
		{
			this.m_living = living;
			return this.m_living.CardEffectList.Add(this);
		}

		// Token: 0x060083D0 RID: 33744 RVA: 0x002B5A70 File Offset: 0x002B3C70
		public virtual bool Stop()
		{
			bool flag = this.m_living != null;
			return flag && this.m_living.CardEffectList.Remove(this);
		}

		// Token: 0x060083D1 RID: 33745 RVA: 0x002B5AA8 File Offset: 0x002B3CA8
		public virtual bool Pause()
		{
			bool flag = this.m_living != null;
			return flag && this.m_living.CardEffectList.Pause(this);
		}

		// Token: 0x060083D2 RID: 33746 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnAttached(Living living)
		{
		}

		// Token: 0x060083D3 RID: 33747 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnRemoved(Living living)
		{
		}

		// Token: 0x060083D4 RID: 33748 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnPaused(Living living)
		{
		}

		// Token: 0x04005226 RID: 21030
		private eCardEffectType m_type;

		// Token: 0x04005227 RID: 21031
		protected Living m_living;

		// Token: 0x04005228 RID: 21032
		public Random rand;

		// Token: 0x04005229 RID: 21033
		public bool IsTrigger;

		// Token: 0x0400522A RID: 21034
		private CardBuffInfo m_buffInfo;
	}
}
