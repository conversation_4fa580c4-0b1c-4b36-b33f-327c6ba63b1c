﻿using System;
using Bussiness;
using Game.Base.Packets;
using Game.Server.GameObjects;
using Game.Server.Managers;
using Game.Server.Packets;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C5A RID: 3162
	[ActiveSystemHandleAttbute(77)]
	public class Ddplayexchange : IActiveSystemCommandHadler
	{
		// Token: 0x06007038 RID: 28728 RVA: 0x0024E354 File Offset: 0x0024C554
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			int num = packet.ReadInt();
			bool flag = num < 1;
			bool flag2;
			if (flag)
			{
				Player.SendMessage("当前兑换数量显示异常请重新输入!");
				flag2 = false;
			}
			else
			{
				int num2 = int.Parse(NewActivityMgr.GetConfigByID(8).Params2) * num;
				bool flag3 = num2 < Player.PlayerCharacter.Info.DDPlayPoint;
				if (flag3)
				{
					using (PlayerBussiness playerBussiness = new PlayerBussiness())
					{
						playerBussiness.SendMailAndItemByNickName("恭喜你在弹弹乐获得中成功兑换了" + num.ToString() + "个乐币", "此邮件为系统发送，请勿回复此邮件", Player.PlayerCharacter.NickName, 201310, num, 0, 0, 0, 0, 0, 0, 0, 0, true);
						Player.Out.SendMailResponse(Player.PlayerCharacter.ID, eMailRespose.Receiver);
						Player.PlayerCharacter.Info.DDPlayPoint -= num2;
						Player.SendMessage("兑换成功!");
					}
					gspacketIn.WriteByte(75);
					gspacketIn.WriteInt(Player.PlayerCharacter.Info.DDPlayPoint);
					Player.SendTCP(gspacketIn);
				}
				else
				{
					Player.SendMessage("积分不足无法兑换!");
				}
				flag2 = true;
			}
			return flag2;
		}
	}
}
