﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using Bussiness;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000CAB RID: 3243
	public static class PveInfoMgr
	{
		// Token: 0x060074B7 RID: 29879 RVA: 0x0026B2AC File Offset: 0x002694AC
		public static bool Init()
		{
			return PveInfoMgr.ReLoad();
		}

		// Token: 0x060074B8 RID: 29880 RVA: 0x0026B2C4 File Offset: 0x002694C4
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, PveInfo> dictionary = PveInfoMgr.LoadFromDatabase();
				bool flag = dictionary.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, PveInfo>>(ref PveInfoMgr.m_pveInfos, dictionary);
				}
				return true;
			}
			catch (Exception ex)
			{
				PveInfoMgr.log.Error("PveInfoMgr", ex);
			}
			return false;
		}

		// Token: 0x060074B9 RID: 29881 RVA: 0x0026B324 File Offset: 0x00269524
		public static Dictionary<int, PveInfo> LoadFromDatabase()
		{
			Dictionary<int, PveInfo> dictionary = new Dictionary<int, PveInfo>();
			using (PveBussiness pveBussiness = new PveBussiness())
			{
				PveInfo[] allPveInfos = pveBussiness.GetAllPveInfos();
				PveInfo[] array = allPveInfos;
				PveInfo[] array2 = array;
				PveInfo[] array3 = array2;
				foreach (PveInfo pveInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(pveInfo.ID);
					if (flag)
					{
						dictionary.Add(pveInfo.ID, pveInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x060074BA RID: 29882 RVA: 0x0026B3C0 File Offset: 0x002695C0
		public static PveInfo GetPveInfoById(int id)
		{
			bool flag = PveInfoMgr.m_pveInfos.ContainsKey(id);
			PveInfo pveInfo;
			if (flag)
			{
				pveInfo = PveInfoMgr.m_pveInfos[id];
			}
			else
			{
				pveInfo = null;
			}
			return pveInfo;
		}

		// Token: 0x060074BB RID: 29883 RVA: 0x0026B3F4 File Offset: 0x002695F4
		public static PveInfo[] GetPveInfo()
		{
			bool flag = PveInfoMgr.m_pveInfos == null;
			if (flag)
			{
				PveInfoMgr.ReLoad();
			}
			return PveInfoMgr.m_pveInfos.Values.ToArray<PveInfo>();
		}

		// Token: 0x060074BC RID: 29884 RVA: 0x0026B42C File Offset: 0x0026962C
		public static PveInfo GetPveInfoByType(eRoomType roomType, int levelLimits)
		{
			bool flag = roomType == eRoomType.Boss || roomType == eRoomType.Dungeon || roomType == eRoomType.Academy;
			if (flag)
			{
				foreach (PveInfo pveInfo in PveInfoMgr.m_pveInfos.Values)
				{
					bool flag2 = pveInfo.Type == (int)roomType;
					if (flag2)
					{
						return pveInfo;
					}
				}
			}
			else
			{
				bool flag3 = roomType == eRoomType.Exploration;
				if (flag3)
				{
					foreach (PveInfo pveInfo2 in PveInfoMgr.m_pveInfos.Values)
					{
						bool flag4 = pveInfo2.Type == (int)roomType && pveInfo2.LevelLimits == levelLimits;
						if (flag4)
						{
							return pveInfo2;
						}
					}
				}
			}
			return null;
		}

		// Token: 0x060074BD RID: 29885 RVA: 0x0026B52C File Offset: 0x0026972C
		public static PveInfo GetRandomPve()
		{
			List<PveInfo> list = PveInfoMgr.m_pveInfos.Values.Where((PveInfo x) => x.Type == 4).ToList<PveInfo>();
			return list[PveInfoMgr.m_rand.Next(0, list.Count<PveInfo>())];
		}

		// Token: 0x04004443 RID: 17475
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04004444 RID: 17476
		private static Dictionary<int, PveInfo> m_pveInfos = new Dictionary<int, PveInfo>();

		// Token: 0x04004445 RID: 17477
		private static ReaderWriterLock m_lock = new ReaderWriterLock();

		// Token: 0x04004446 RID: 17478
		private static ThreadSafeRandom m_rand = new ThreadSafeRandom();
	}
}
