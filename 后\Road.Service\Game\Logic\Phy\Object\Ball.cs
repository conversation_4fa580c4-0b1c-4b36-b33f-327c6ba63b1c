﻿using System;
using System.Drawing;

namespace Game.Logic.Phy.Object
{
	// Token: 0x02000CCD RID: 3277
	public class Ball : PhysicalObj
	{
		// Token: 0x17001407 RID: 5127
		// (get) Token: 0x0600751A RID: 29978 RVA: 0x00005350 File Offset: 0x00003550
		public override int Type
		{
			get
			{
				return 0;
			}
		}

		// Token: 0x0600751B RID: 29979 RVA: 0x0002B5B2 File Offset: 0x000297B2
		public Ball(int id, string name, string defaultAction, int scale, int rotation)
			: base(id, name, "asset.game.six.ball", defaultAction, scale, rotation, 0)
		{
			this.m_rect = new Rectangle(-30, -30, 60, 60);
			base.CanPenetrate = true;
		}

		// Token: 0x0600751C RID: 29980 RVA: 0x0026EB1C File Offset: 0x0026CD1C
		public override void CollidedByObject(Physics phy)
		{
			bool flag = phy is SimpleBomb;
			if (flag)
			{
				(phy as <PERSON><PERSON><PERSON>).Owner.PickPhy(this);
			}
		}
	}
}
