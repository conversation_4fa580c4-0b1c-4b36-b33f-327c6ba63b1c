﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D12 RID: 3346
	public class PetReduceBloodEquip : AbstractPetEffect
	{
		// Token: 0x060078BB RID: 30907 RVA: 0x002875A8 File Offset: 0x002857A8
		public PetReduceBloodEquip(int count, string elementID, Living liv)
			: base(ePetEffectType.PetReduceBloodEquip, elementID)
		{
			this.m_count = count;
			this.m_liv = liv;
			bool flag = elementID == null;
			if (!flag)
			{
				int length = elementID.Length;
				bool flag2 = length != 4;
				if (!flag2)
				{
					switch (elementID[3])
					{
					case '0':
						if (elementID == "1150")
						{
							this.m_value = 1;
							this.m_percent = true;
							return;
						}
						if (elementID == "1210")
						{
							this.m_value = 500;
							return;
						}
						if (elementID == "1270")
						{
							this.m_value = 800;
							return;
						}
						if (elementID == "2760")
						{
							this.m_value = 20;
							this.m_percent = true;
							return;
						}
						if (!(elementID == "1170"))
						{
							return;
						}
						goto IL_032E;
					case '1':
						if (elementID == "1151")
						{
							this.m_value = 2;
							this.m_percent = true;
							return;
						}
						if (elementID == "1271")
						{
							this.m_value = 1300;
							return;
						}
						if (elementID == "1171")
						{
							this.m_value = 2000;
							return;
						}
						if (elementID == "1801")
						{
							this.m_value = 3;
							this.m_percent = true;
							return;
						}
						if (!(elementID == "1211"))
						{
							return;
						}
						goto IL_032E;
					case '2':
					{
						bool flag3 = !(elementID == "1152");
						if (!flag3)
						{
							this.m_value = 3;
							this.m_percent = true;
							return;
						}
						bool flag4 = !(elementID == "1802");
						if (flag4)
						{
							return;
						}
						break;
					}
					case '3':
					{
						bool flag5 = !(elementID == "1803");
						if (flag5)
						{
							return;
						}
						goto IL_031D;
					}
					case '4':
					case '5':
						return;
					case '6':
					{
						bool flag6 = elementID == "1176";
						if (flag6)
						{
							this.m_value = 2;
							this.m_percent = true;
						}
						return;
					}
					case '7':
					{
						bool flag7 = !(elementID == "1177") && !(elementID == "1717");
						if (flag7)
						{
							return;
						}
						break;
					}
					case '8':
					{
						bool flag8 = !(elementID == "1498");
						if (flag8)
						{
							bool flag9 = elementID == "1718";
							if (flag9)
							{
								this.m_value = 6;
								this.m_percent = true;
							}
							return;
						}
						goto IL_031D;
					}
					case '9':
					{
						bool flag10 = !(elementID == "1719");
						if (flag10)
						{
							bool flag11 = elementID == "2759";
							if (flag11)
							{
								this.m_value = 30;
								this.m_percent = true;
							}
						}
						else
						{
							this.m_value = 8;
							this.m_percent = true;
						}
						return;
					}
					default:
						return;
					}
					this.m_value = 4;
					this.m_percent = true;
					return;
					IL_031D:
					this.m_value = 5;
					this.m_percent = true;
					return;
					IL_032E:
					this.m_value = 1000;
				}
			}
		}

		// Token: 0x060078BC RID: 30908 RVA: 0x002878F0 File Offset: 0x00285AF0
		public override bool Start(Living living)
		{
			PetReduceBloodEquip petReduceBloodEquip = living.PetEffectList.GetOfType(ePetEffectType.PetReduceBloodEquip) as PetReduceBloodEquip;
			bool flag = petReduceBloodEquip != null;
			bool flag2;
			if (flag)
			{
				petReduceBloodEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078BD RID: 30909 RVA: 0x0002CE3F File Offset: 0x0002B03F
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x060078BE RID: 30910 RVA: 0x0002CE55 File Offset: 0x0002B055
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x060078BF RID: 30911 RVA: 0x00287938 File Offset: 0x00285B38
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				living.Game.SendPlayerPicture(living, 2, false);
			}
			else
			{
				living.SyncAtTime = true;
				bool percent = this.m_percent;
				if (percent)
				{
					living.AddBlood(-(living.MaxBlood * this.m_value / 100), 1);
				}
				else
				{
					living.AddBlood(-this.m_value, 1);
				}
				living.SyncAtTime = false;
				bool flag2 = living.Blood <= 0;
				if (flag2)
				{
					living.Die();
					bool flag3 = this.m_liv != null && this.m_liv is Player;
					if (flag3)
					{
						(this.m_liv as Player).OnAfterKillingLiving(living, this.m_value, 0);
					}
				}
			}
		}

		// Token: 0x040046C0 RID: 18112
		private int m_count;

		// Token: 0x040046C1 RID: 18113
		private int m_value;

		// Token: 0x040046C2 RID: 18114
		private bool m_percent;

		// Token: 0x040046C3 RID: 18115
		private Living m_liv;
	}
}
