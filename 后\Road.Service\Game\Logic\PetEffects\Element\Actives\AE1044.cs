﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DA6 RID: 3494
	public class AE1044 : BasePetEffect
	{
		// Token: 0x06007BDE RID: 31710 RVA: 0x00294D6C File Offset: 0x00292F6C
		public AE1044(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1044, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BDF RID: 31711 RVA: 0x00294DEC File Offset: 0x00292FEC
		public override bool Start(Living living)
		{
			AE1044 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1044) as AE1044;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BE0 RID: 31712 RVA: 0x0002F5FF File Offset: 0x0002D7FF
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007BE1 RID: 31713 RVA: 0x00294E4C File Offset: 0x0029304C
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1044(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007BE2 RID: 31714 RVA: 0x0002F615 File Offset: 0x0002D815
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x040049C4 RID: 18884
		private int m_type = 0;

		// Token: 0x040049C5 RID: 18885
		private int m_count = 0;

		// Token: 0x040049C6 RID: 18886
		private int m_probability = 0;

		// Token: 0x040049C7 RID: 18887
		private int m_delay = 0;

		// Token: 0x040049C8 RID: 18888
		private int m_coldDown = 0;

		// Token: 0x040049C9 RID: 18889
		private int m_currentId;

		// Token: 0x040049CA RID: 18890
		private int m_added = 0;
	}
}
