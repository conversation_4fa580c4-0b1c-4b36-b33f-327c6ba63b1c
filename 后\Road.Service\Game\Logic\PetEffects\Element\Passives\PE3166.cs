﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D8C RID: 3468
	public class PE3166 : BasePetEffect
	{
		// Token: 0x06007B52 RID: 31570 RVA: 0x0029296C File Offset: 0x00290B6C
		public PE3166(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE3166, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B53 RID: 31571 RVA: 0x002929E8 File Offset: 0x00290BE8
		public override bool Start(Living living)
		{
			PE3166 pe = living.PetEffectList.GetOfType(ePetEffectType.PE3166) as PE3166;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B54 RID: 31572 RVA: 0x0002EF36 File Offset: 0x0002D136
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerShoot += this.ChangeProperty;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007B55 RID: 31573 RVA: 0x0002EF5F File Offset: 0x0002D15F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerShoot -= this.ChangeProperty;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007B56 RID: 31574 RVA: 0x0002EF88 File Offset: 0x0002D188
		private void ChangeProperty(Player player)
		{
			this.m_added = 15;
			player.PetEffects.PetBaseAtt += this.m_added;
		}

		// Token: 0x06007B57 RID: 31575 RVA: 0x0002EFAC File Offset: 0x0002D1AC
		private void player_AfterPlayerShooted(Player player)
		{
			player.PetEffects.PetBaseAtt -= this.m_added;
			this.m_added = 0;
		}

		// Token: 0x0400490E RID: 18702
		private int m_type = 0;

		// Token: 0x0400490F RID: 18703
		private int m_count = 0;

		// Token: 0x04004910 RID: 18704
		private int m_probability = 0;

		// Token: 0x04004911 RID: 18705
		private int m_delay = 0;

		// Token: 0x04004912 RID: 18706
		private int m_coldDown = 0;

		// Token: 0x04004913 RID: 18707
		private int m_currentId;

		// Token: 0x04004914 RID: 18708
		private int m_added = 0;
	}
}
