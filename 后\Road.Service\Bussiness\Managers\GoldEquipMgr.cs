﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FD6 RID: 4054
	public class GoldEquipMgr
	{
		// Token: 0x06008AD0 RID: 35536 RVA: 0x002F8AA8 File Offset: 0x002F6CA8
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, GoldEquipTemplateInfo> dictionary = new Dictionary<int, GoldEquipTemplateInfo>();
				bool flag = GoldEquipMgr.LoadItem(dictionary);
				if (flag)
				{
					try
					{
						GoldEquipMgr._items = dictionary;
						return true;
					}
					catch
					{
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = GoldEquipMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					GoldEquipMgr.log.Error("ReLoad", ex);
				}
			}
			return false;
		}

		// Token: 0x06008AD1 RID: 35537 RVA: 0x002F8B28 File Offset: 0x002F6D28
		public static bool Init()
		{
			bool flag;
			try
			{
				GoldEquipMgr._items = new Dictionary<int, GoldEquipTemplateInfo>();
				flag = GoldEquipMgr.LoadItem(GoldEquipMgr._items);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = GoldEquipMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					GoldEquipMgr.log.Error("Init", ex);
				}
				flag = false;
			}
			return flag;
		}

		// Token: 0x06008AD2 RID: 35538 RVA: 0x002F8B88 File Offset: 0x002F6D88
		public static bool LoadItem(Dictionary<int, GoldEquipTemplateInfo> infos)
		{
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				GoldEquipTemplateInfo[] allGoldEquipTemplateLoad = produceBussiness.GetAllGoldEquipTemplateLoad();
				GoldEquipTemplateInfo[] array = allGoldEquipTemplateLoad;
				GoldEquipTemplateInfo[] array2 = array;
				GoldEquipTemplateInfo[] array3 = array2;
				foreach (GoldEquipTemplateInfo goldEquipTemplateInfo in array3)
				{
					bool flag = !infos.Keys.Contains(goldEquipTemplateInfo.OldTemplateId);
					if (flag)
					{
						infos.Add(goldEquipTemplateInfo.OldTemplateId, goldEquipTemplateInfo);
					}
				}
			}
			return true;
		}

		// Token: 0x06008AD3 RID: 35539 RVA: 0x002F8C20 File Offset: 0x002F6E20
		public static GoldEquipTemplateInfo FindGoldEquipByTemplate(int templateId)
		{
			try
			{
				bool flag = GoldEquipMgr._items.Keys.Contains(templateId);
				if (flag)
				{
					return GoldEquipMgr._items[templateId];
				}
			}
			catch
			{
			}
			return null;
		}

		// Token: 0x06008AD4 RID: 35540 RVA: 0x002F8C70 File Offset: 0x002F6E70
		public static GoldEquipTemplateInfo FindGoldEquipOldTemplate(int TemplateId)
		{
			try
			{
				foreach (GoldEquipTemplateInfo goldEquipTemplateInfo in GoldEquipMgr._items.Values)
				{
					string text = goldEquipTemplateInfo.OldTemplateId.ToString();
					bool flag = goldEquipTemplateInfo.NewTemplateId == TemplateId && text.Substring(4) != "4";
					if (flag)
					{
						return goldEquipTemplateInfo;
					}
				}
			}
			catch
			{
			}
			return null;
		}

		// Token: 0x04005504 RID: 21764
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005505 RID: 21765
		private static Dictionary<int, GoldEquipTemplateInfo> _items;
	}
}
