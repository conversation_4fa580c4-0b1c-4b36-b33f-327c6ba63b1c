﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D78 RID: 3448
	public class PE1237 : BasePetEffect
	{
		// Token: 0x06007AED RID: 31469 RVA: 0x00290EC0 File Offset: 0x0028F0C0
		public PE1237(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1237, elementID)
		{
			this.m_count = count;
			this.m_coldDown = 3;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AEE RID: 31470 RVA: 0x00290F40 File Offset: 0x0028F140
		public override bool Start(Living living)
		{
			PE1237 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1237) as PE1237;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AEF RID: 31471 RVA: 0x0002EB90 File Offset: 0x0002CD90
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
		}

		// Token: 0x06007AF0 RID: 31472 RVA: 0x00290FA0 File Offset: 0x0028F1A0
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.rand.Next(100) >= 45;
			if (!flag)
			{
				this.m_added = 800;
				source.SyncAtTime = true;
				source.AddBlood(-this.m_added, 1);
				source.SyncAtTime = false;
				bool flag2 = source.Blood < 0;
				if (flag2)
				{
					source.Die();
					bool flag3 = living != null && living is Player;
					if (flag3)
					{
						(living as Player).PlayerDetail.OnKillingLiving(living.Game, 2, source.Id, source.IsLiving, this.m_added);
					}
				}
				living.PetEffects.ReboundDamage = this.m_added;
			}
		}

		// Token: 0x06007AF1 RID: 31473 RVA: 0x0002EBA6 File Offset: 0x0002CDA6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
		}

		// Token: 0x04004888 RID: 18568
		private int m_type = 0;

		// Token: 0x04004889 RID: 18569
		private int m_count = 0;

		// Token: 0x0400488A RID: 18570
		private int m_probability = 0;

		// Token: 0x0400488B RID: 18571
		private int m_delay = 0;

		// Token: 0x0400488C RID: 18572
		private int m_coldDown = 0;

		// Token: 0x0400488D RID: 18573
		private int m_currentId;

		// Token: 0x0400488E RID: 18574
		private int m_added = 0;
	}
}
