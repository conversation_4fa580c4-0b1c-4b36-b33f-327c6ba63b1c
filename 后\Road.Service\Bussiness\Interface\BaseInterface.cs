﻿using System;
using System.Configuration;
using System.IO;
using System.Net;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using Bussiness.CenterService;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Interface
{
	// Token: 0x02000FFF RID: 4095
	public abstract class BaseInterface
	{
		// Token: 0x170014EA RID: 5354
		// (get) Token: 0x06008BCA RID: 35786 RVA: 0x00036BB7 File Offset: 0x00034DB7
		public static string GetInterName
		{
			get
			{
				return ConfigurationManager.AppSettings["InterName"].ToLower();
			}
		}

		// Token: 0x170014EB RID: 5355
		// (get) Token: 0x06008BCB RID: 35787 RVA: 0x00036BCD File Offset: 0x00034DCD
		public static string GetLoginKey
		{
			get
			{
				return ConfigurationManager.AppSettings["LoginKey"];
			}
		}

		// Token: 0x170014EC RID: 5356
		// (get) Token: 0x06008BCC RID: 35788 RVA: 0x00036BDE File Offset: 0x00034DDE
		public static string GetChargeKey
		{
			get
			{
				return ConfigurationManager.AppSettings["ChargeKey"];
			}
		}

		// Token: 0x170014ED RID: 5357
		// (get) Token: 0x06008BCD RID: 35789 RVA: 0x00036BEF File Offset: 0x00034DEF
		public static string LoginUrl
		{
			get
			{
				return ConfigurationManager.AppSettings["LoginUrl"];
			}
		}

		// Token: 0x170014EE RID: 5358
		// (get) Token: 0x06008BCE RID: 35790 RVA: 0x00036C00 File Offset: 0x00034E00
		public virtual int ActiveGold
		{
			get
			{
				return int.Parse(ConfigurationManager.AppSettings["DefaultGold"]);
			}
		}

		// Token: 0x170014EF RID: 5359
		// (get) Token: 0x06008BCF RID: 35791 RVA: 0x00036C16 File Offset: 0x00034E16
		public virtual int ActiveMoney
		{
			get
			{
				return int.Parse(ConfigurationManager.AppSettings["DefaultMoney"]);
			}
		}

		// Token: 0x170014F0 RID: 5360
		// (get) Token: 0x06008BD0 RID: 35792 RVA: 0x00036C2C File Offset: 0x00034E2C
		public virtual int ActiveGiftToken
		{
			get
			{
				return int.Parse(ConfigurationManager.AppSettings["DefaultGiftToken"]);
			}
		}

		// Token: 0x06008BD1 RID: 35793 RVA: 0x002FE3AC File Offset: 0x002FC5AC
		public static string GetNameBySite(string user, string site)
		{
			bool flag = !string.IsNullOrEmpty(site);
			if (flag)
			{
				string text = ConfigurationManager.AppSettings["LoginKey_" + site];
				bool flag2 = !string.IsNullOrEmpty(text);
				if (flag2)
				{
					user = site + "_" + user;
				}
			}
			return user;
		}

		// Token: 0x06008BD2 RID: 35794 RVA: 0x002FE404 File Offset: 0x002FC604
		public static DateTime ConvertIntDateTime(double d)
		{
			DateTime minValue = DateTime.MinValue;
			return TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1)).AddSeconds(d);
		}

		// Token: 0x06008BD3 RID: 35795 RVA: 0x002FE43C File Offset: 0x002FC63C
		public static int ConvertDateTimeInt(DateTime time)
		{
			DateTime dateTime = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
			double totalSeconds = (time - dateTime).TotalSeconds;
			return (int)totalSeconds;
		}

		// Token: 0x06008BD4 RID: 35796 RVA: 0x002FE484 File Offset: 0x002FC684
		public static string md5(string str)
		{
			return BitConverter.ToString(BaseInterface.encryptData(str)).Replace("-", "").ToLower();
		}

		// Token: 0x06008BD5 RID: 35797 RVA: 0x002FE4B8 File Offset: 0x002FC6B8
		private static byte[] encryptData(string data)
		{
			MD5CryptoServiceProvider md5CryptoServiceProvider = new MD5CryptoServiceProvider();
			UTF8Encoding utf8Encoding = new UTF8Encoding();
			return md5CryptoServiceProvider.ComputeHash(utf8Encoding.GetBytes(data));
		}

		// Token: 0x06008BD6 RID: 35798 RVA: 0x002FE4E4 File Offset: 0x002FC6E4
		public static string RequestContent(string Url)
		{
			return BaseInterface.RequestContent(Url, 2560);
		}

		// Token: 0x06008BD7 RID: 35799 RVA: 0x002FE504 File Offset: 0x002FC704
		public static string RequestContent(string Url, int byteLength)
		{
			byte[] array = new byte[byteLength];
			HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(Url);
			httpWebRequest.ContentType = "text/plain";
			HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
			Stream responseStream = httpWebResponse.GetResponseStream();
			int num = responseStream.Read(array, 0, array.Length);
			string @string = Encoding.UTF8.GetString(array, 0, num);
			responseStream.Close();
			return @string;
		}

		// Token: 0x06008BD8 RID: 35800 RVA: 0x002FE570 File Offset: 0x002FC770
		public static string RequestContent(string Url, string param, string code)
		{
			Encoding encoding = Encoding.GetEncoding(code);
			byte[] bytes = encoding.GetBytes(param);
			string @string = encoding.GetString(bytes);
			byte[] array = new byte[2560];
			HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(Url);
			httpWebRequest.ServicePoint.Expect100Continue = false;
			httpWebRequest.Method = "POST";
			httpWebRequest.ContentType = "application/x-www-form-urlencoded";
			httpWebRequest.ContentLength = (long)bytes.Length;
			using (Stream requestStream = httpWebRequest.GetRequestStream())
			{
				requestStream.Write(bytes, 0, bytes.Length);
			}
			string string2;
			using (WebResponse response = httpWebRequest.GetResponse())
			{
				HttpWebResponse httpWebResponse = (HttpWebResponse)response;
				Stream responseStream = httpWebResponse.GetResponseStream();
				int num = responseStream.Read(array, 0, array.Length);
				string2 = Encoding.UTF8.GetString(array, 0, num);
			}
			return string2;
		}

		// Token: 0x06008BD9 RID: 35801 RVA: 0x002FE670 File Offset: 0x002FC870
		public static BaseInterface CreateInterface()
		{
			string getInterName = BaseInterface.GetInterName;
			if (!true)
			{
			}
			BaseInterface baseInterface;
			if (!(getInterName == "qunying"))
			{
				if (!(getInterName == "sevenroad"))
				{
					if (!(getInterName == "duowan"))
					{
						baseInterface = null;
					}
					else
					{
						baseInterface = new DWInterface();
					}
				}
				else
				{
					baseInterface = new SRInterface();
				}
			}
			else
			{
				baseInterface = new QYInterface();
			}
			if (!true)
			{
			}
			return baseInterface;
		}

		// Token: 0x06008BDA RID: 35802 RVA: 0x002FE6F0 File Offset: 0x002FC8F0
		public static bool CheckRnd(string str)
		{
			return !string.IsNullOrEmpty(str);
		}

		// Token: 0x06008BDB RID: 35803 RVA: 0x002FE70C File Offset: 0x002FC90C
		public virtual PlayerInfo CreateLogin(string name, string password, int areaid, ref string message, ref int isFirst, string IP, ref bool isError, bool firstValidate, ref bool isActive, string site, string nickname)
		{
			try
			{
				using (PlayerBussiness playerBussiness = new PlayerBussiness())
				{
					bool flag = true;
					DateTime now = DateTime.Now;
					PlayerInfo playerInfo = playerBussiness.LoginGame(name, ref isFirst, ref flag, ref isError, firstValidate, ref now, nickname);
					bool flag2 = playerInfo == null;
					if (flag2)
					{
						bool flag3 = !playerBussiness.ActivePlayer(ref playerInfo, name, password, true, this.ActiveGold, this.ActiveMoney, this.ActiveGiftToken, IP, site);
						if (flag3)
						{
							playerInfo = null;
							message = LanguageMgr.GetTranslation("BaseInterface.LoginAndUpdate.Fail", Array.Empty<object>());
						}
						else
						{
							isActive = true;
							using (CenterServiceClient centerServiceClient = new CenterServiceClient())
							{
								centerServiceClient.ActivePlayer(true);
							}
						}
					}
					else
					{
						bool flag4 = !flag;
						if (flag4)
						{
							message = LanguageMgr.GetTranslation("ManageBussiness.Forbid1", new object[] { now.Year, now.Month, now.Day, now.Hour, now.Minute });
							return null;
						}
						using (CenterServiceClient centerServiceClient2 = new CenterServiceClient())
						{
							centerServiceClient2.CreatePlayer(playerInfo.ID, name, password, areaid, isFirst == 0);
						}
					}
					return playerInfo;
				}
			}
			catch (Exception ex)
			{
				BaseInterface.log.Error("LoginAndUpdate", ex);
			}
			return null;
		}

		// Token: 0x06008BDC RID: 35804 RVA: 0x002FE8E8 File Offset: 0x002FCAE8
		public virtual PlayerInfo LoginGame(string name, string pass, int areaid, ref bool isFirst)
		{
			try
			{
				using (CenterServiceClient centerServiceClient = new CenterServiceClient())
				{
					int num = 0;
					bool flag = centerServiceClient.ValidateLoginAndGetID(name, pass, areaid, ref num, ref isFirst);
					if (flag)
					{
						return new PlayerInfo
						{
							ID = num,
							UserName = name
						};
					}
				}
			}
			catch (Exception ex)
			{
				BaseInterface.log.Error("LoginGame", ex);
			}
			return null;
		}

		// Token: 0x06008BDD RID: 35805 RVA: 0x002FE978 File Offset: 0x002FCB78
		public virtual string[] UnEncryptLogin(string content, ref int result, string site)
		{
			try
			{
				string text = string.Empty;
				bool flag = !string.IsNullOrEmpty(site);
				if (flag)
				{
					text = ConfigurationManager.AppSettings["LoginKey_" + site];
				}
				bool flag2 = string.IsNullOrEmpty(text);
				if (flag2)
				{
					text = BaseInterface.GetLoginKey;
				}
				bool flag3 = !string.IsNullOrEmpty(text);
				if (flag3)
				{
					string[] array = content.Split(new char[] { '|' });
					bool flag4 = array.Length > 3;
					if (flag4)
					{
						string text2 = BaseInterface.md5(array[0] + array[1] + array[2] + text);
						bool flag5 = text2 == array[3].ToLower();
						if (flag5)
						{
							return array;
						}
						result = 5;
					}
					else
					{
						result = 2;
					}
				}
				else
				{
					result = 4;
				}
			}
			catch (Exception ex)
			{
				BaseInterface.log.Error("UnEncryptLogin", ex);
			}
			return new string[0];
		}

		// Token: 0x06008BDE RID: 35806 RVA: 0x002FEA74 File Offset: 0x002FCC74
		public virtual string[] UnEncryptCharge(string content, ref int result, string site)
		{
			try
			{
				string text = string.Empty;
				bool flag = !string.IsNullOrEmpty(site);
				if (flag)
				{
					text = ConfigurationManager.AppSettings["ChargeKey_" + site];
				}
				bool flag2 = string.IsNullOrEmpty(text);
				if (flag2)
				{
					text = BaseInterface.GetChargeKey;
				}
				bool flag3 = !string.IsNullOrEmpty(text);
				if (flag3)
				{
					string[] array = content.Split(new char[] { '|' });
					string text2 = BaseInterface.md5(string.Concat(new string[]
					{
						array[0],
						array[1],
						array[2],
						array[3],
						array[4],
						text
					}));
					bool flag4 = array.Length > 5;
					if (flag4)
					{
						bool flag5 = text2 == array[5].ToLower();
						if (flag5)
						{
							return array;
						}
						result = 7;
					}
					else
					{
						result = 8;
					}
				}
				else
				{
					result = 6;
				}
			}
			catch (Exception ex)
			{
				BaseInterface.log.Error("UnEncryptCharge", ex);
			}
			return new string[0];
		}

		// Token: 0x06008BDF RID: 35807 RVA: 0x002FEB94 File Offset: 0x002FCD94
		public virtual string[] UnEncryptSentReward(string content, ref int result, string key)
		{
			try
			{
				string[] array = content.Split(new char[] { '#' });
				bool flag = array.Length == 8;
				if (flag)
				{
					string text = ConfigurationManager.AppSettings["SentRewardTimeSpan"];
					int num = int.Parse(string.IsNullOrEmpty(text) ? "1" : text);
					TimeSpan timeSpan = (string.IsNullOrEmpty(array[6]) ? new TimeSpan(1, 1, 1) : (DateTime.Now - BaseInterface.ConvertIntDateTime(double.Parse(array[6]))));
					bool flag2 = timeSpan.Days == 0 && timeSpan.Hours == 0 && timeSpan.Minutes < num;
					if (flag2)
					{
						bool flag3 = string.IsNullOrEmpty(key);
						if (flag3)
						{
							return array;
						}
						string text2 = BaseInterface.md5(string.Concat(new string[]
						{
							array[2],
							array[3],
							array[4],
							array[5],
							array[6],
							key
						}));
						bool flag4 = text2 == array[7].ToLower();
						if (flag4)
						{
							return array;
						}
						result = 5;
					}
					else
					{
						result = 7;
					}
				}
				else
				{
					result = 6;
				}
			}
			catch (Exception ex)
			{
				BaseInterface.log.Error("UnEncryptSentReward", ex);
			}
			return new string[0];
		}

		// Token: 0x06008BE0 RID: 35808 RVA: 0x00069EBC File Offset: 0x000680BC
		public virtual bool GetUserSex(string name)
		{
			return true;
		}

		// Token: 0x04005578 RID: 21880
		protected static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
	}
}
