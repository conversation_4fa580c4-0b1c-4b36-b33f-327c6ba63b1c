﻿using System;
using System.Collections.Generic;
using System.Drawing;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Phy.Maps
{
	// Token: 0x02000CE6 RID: 3302
	public class Map
	{
		// Token: 0x1700148B RID: 5259
		// (get) Token: 0x060077B1 RID: 30641 RVA: 0x002816F0 File Offset: 0x0027F8F0
		// (set) Token: 0x060077B2 RID: 30642 RVA: 0x0002C240 File Offset: 0x0002A440
		public float wind
		{
			get
			{
				return this._wind;
			}
			set
			{
				this._wind = value;
			}
		}

		// Token: 0x1700148C RID: 5260
		// (get) Token: 0x060077B3 RID: 30643 RVA: 0x00281708 File Offset: 0x0027F908
		// (set) Token: 0x060077B4 RID: 30644 RVA: 0x0002C24A File Offset: 0x0002A44A
		public float windRate
		{
			get
			{
				return this._windRate;
			}
			set
			{
				this._windRate = value;
			}
		}

		// Token: 0x1700148D RID: 5261
		// (get) Token: 0x060077B5 RID: 30645 RVA: 0x0002C254 File Offset: 0x0002A454
		public float gravity
		{
			get
			{
				return (float)this._info.Weight;
			}
		}

		// Token: 0x1700148E RID: 5262
		// (get) Token: 0x060077B6 RID: 30646 RVA: 0x0002C262 File Offset: 0x0002A462
		public float airResistance
		{
			get
			{
				return (float)this._info.DragIndex;
			}
		}

		// Token: 0x1700148F RID: 5263
		// (get) Token: 0x060077B7 RID: 30647 RVA: 0x0002C270 File Offset: 0x0002A470
		public Tile Ground
		{
			get
			{
				return this._layer1;
			}
		}

		// Token: 0x17001490 RID: 5264
		// (get) Token: 0x060077B8 RID: 30648 RVA: 0x0002C278 File Offset: 0x0002A478
		public MapInfo Info
		{
			get
			{
				return this._info;
			}
		}

		// Token: 0x17001491 RID: 5265
		// (get) Token: 0x060077B9 RID: 30649 RVA: 0x0002C280 File Offset: 0x0002A480
		public Rectangle Bound
		{
			get
			{
				return this._bound;
			}
		}

		// Token: 0x060077BA RID: 30650 RVA: 0x00281720 File Offset: 0x0027F920
		public Map(MapInfo info, Tile layer1, Tile layer2)
		{
			this._info = info;
			this._objects = new HashSet<Physics>();
			this._layer1 = layer1;
			this._layer2 = layer2;
			this._rand = new Random();
			bool flag = this._layer1 != null;
			if (flag)
			{
				this._bound = new Rectangle(0, 0, this._layer1.Width, this._layer1.Height);
			}
			else
			{
				this._bound = new Rectangle(0, 0, this._layer2.Width, this._layer2.Height);
			}
		}

		// Token: 0x060077BB RID: 30651 RVA: 0x002817D0 File Offset: 0x0027F9D0
		public void Dig(int cx, int cy, Tile surface, Tile border)
		{
			bool flag = this._layer1 != null;
			if (flag)
			{
				this._layer1.Dig(cx, cy, surface, border);
			}
			bool flag2 = this._layer2 != null;
			if (flag2)
			{
				this._layer2.Dig(cx, cy, surface, border);
			}
		}

		// Token: 0x060077BC RID: 30652 RVA: 0x00281820 File Offset: 0x0027FA20
		public bool IsEmpty(int x, int y)
		{
			return (this._layer1 == null || this._layer1.IsEmpty(x, y)) && (this._layer2 == null || this._layer2.IsEmpty(x, y));
		}

		// Token: 0x060077BD RID: 30653 RVA: 0x00281864 File Offset: 0x0027FA64
		public bool IsSpecialMap()
		{
			int id = this.Info.ID;
			int num = id;
			return num == 1303;
		}

		// Token: 0x060077BE RID: 30654 RVA: 0x00281898 File Offset: 0x0027FA98
		public bool IsRectangleEmpty(Rectangle rect)
		{
			return (this._layer1 == null || this._layer1.IsRectangleEmptyQuick(rect)) && (this._layer2 == null || this._layer2.IsRectangleEmptyQuick(rect));
		}

		// Token: 0x060077BF RID: 30655 RVA: 0x002818DC File Offset: 0x0027FADC
		public Point FindYLineNotEmptyPointDown(int x, int y, int h)
		{
			x = ((x >= 0) ? ((x >= this._bound.Width) ? (this._bound.Width - 1) : x) : 0);
			y = ((y >= 0) ? y : 0);
			h = ((y + h >= this._bound.Height) ? (this._bound.Height - y - 1) : h);
			for (int i = 0; i < h; i++)
			{
				bool flag = !this.IsEmpty(x - 1, y) || !this.IsEmpty(x + 1, y);
				if (flag)
				{
					return new Point(x, y);
				}
				y++;
			}
			return Point.Empty;
		}

		// Token: 0x060077C0 RID: 30656 RVA: 0x0028198C File Offset: 0x0027FB8C
		public Point FindYLineNotEmptyPointDown(int x, int y)
		{
			return this.FindYLineNotEmptyPointDown(x, y, this._bound.Height);
		}

		// Token: 0x060077C1 RID: 30657 RVA: 0x002819B4 File Offset: 0x0027FBB4
		public Point FindYLineNotEmptyPointUp(int x, int y, int h)
		{
			x = ((x >= 0) ? ((x >= this._bound.Width) ? (this._bound.Width - 1) : x) : 0);
			y = ((y >= 0) ? y : 0);
			h = ((y + h >= this._bound.Height) ? (this._bound.Height - y) : h);
			for (int i = 0; i < h; i++)
			{
				bool flag = !this.IsEmpty(x - 1, y) || !this.IsEmpty(x + 1, y);
				if (flag)
				{
					return new Point(x, y);
				}
				y--;
			}
			return Point.Empty;
		}

		// Token: 0x060077C2 RID: 30658 RVA: 0x00281A60 File Offset: 0x0027FC60
		public Point FindNextWalkPoint(int x, int y, int direction, int stepX, int stepY)
		{
			bool flag = direction != 1 && direction != -1;
			Point point;
			if (flag)
			{
				point = Point.Empty;
			}
			else
			{
				int num = x + direction * stepX;
				bool flag2 = num < 0 || num > this._bound.Width;
				if (flag2)
				{
					point = Point.Empty;
				}
				else
				{
					Point point2 = this.FindYLineNotEmptyPointDown(num, y - stepY - 1, this._bound.Height);
					bool flag3 = point2 != Point.Empty && Math.Abs(point2.Y - y) > stepY;
					if (flag3)
					{
						point2 = Point.Empty;
					}
					point = point2;
				}
			}
			return point;
		}

		// Token: 0x060077C3 RID: 30659 RVA: 0x00281B04 File Offset: 0x0027FD04
		public Point FindNextWalkPointDown(int x, int y, int direction, int stepX, int stepY)
		{
			bool flag = direction != 1 && direction != -1;
			Point point;
			if (flag)
			{
				point = Point.Empty;
			}
			else
			{
				int num = x + direction * stepX;
				bool flag2 = num < 0 || num > this._bound.Width;
				if (flag2)
				{
					point = Point.Empty;
				}
				else
				{
					Point point2 = this.FindYLineNotEmptyPointDown(num, y - stepY - 1, this._bound.Width);
					bool flag3 = point2 != Point.Empty && Math.Abs(point2.Y - y) > stepY;
					if (flag3)
					{
						point2 = Point.Empty;
					}
					point = point2;
				}
			}
			return point;
		}

		// Token: 0x060077C4 RID: 30660 RVA: 0x00281BA8 File Offset: 0x0027FDA8
		public bool canMove(int x, int y)
		{
			return this.IsEmpty(x, y) && !this.IsOutMap(x, y);
		}

		// Token: 0x060077C5 RID: 30661 RVA: 0x00281BD4 File Offset: 0x0027FDD4
		public bool IsOutMap(int x, int y)
		{
			return x < 0 || x >= this._bound.Width || y >= this._bound.Height;
		}

		// Token: 0x060077C6 RID: 30662 RVA: 0x00281C18 File Offset: 0x0027FE18
		public void AddPhysical(Physics phy)
		{
			phy.SetMap(this);
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				this._objects.Add(phy);
			}
		}

		// Token: 0x060077C7 RID: 30663 RVA: 0x00281C6C File Offset: 0x0027FE6C
		public void RemovePhysical(Physics phy)
		{
			phy.SetMap(null);
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				this._objects.Remove(phy);
			}
		}

		// Token: 0x060077C8 RID: 30664 RVA: 0x00281CC0 File Offset: 0x0027FEC0
		public List<Physics> GetAllPhysicalSafe()
		{
			List<Physics> list = new List<Physics>();
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					list.Add(physics);
				}
			}
			return list;
		}

		// Token: 0x060077C9 RID: 30665 RVA: 0x00281D58 File Offset: 0x0027FF58
		public List<PhysicalObj> GetAllPhysicalObjSafe()
		{
			List<PhysicalObj> list = new List<PhysicalObj>();
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					bool flag2 = physics is PhysicalObj;
					if (flag2)
					{
						list.Add(physics as PhysicalObj);
					}
				}
			}
			return list;
		}

		// Token: 0x060077CA RID: 30666 RVA: 0x00281E04 File Offset: 0x00280004
		public Physics[] FindPhysicalObjects(Rectangle rect, Physics except)
		{
			List<Physics> list = new List<Physics>();
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					bool flag2 = physics.IsLiving && physics != except;
					if (flag2)
					{
						Rectangle bound = physics.Bound;
						Rectangle bound2 = physics.Bound1;
						bound.Offset(physics.X, physics.Y);
						bound2.Offset(physics.X, physics.Y);
						bool flag3 = bound.IntersectsWith(rect) || bound2.IntersectsWith(rect);
						if (flag3)
						{
							list.Add(physics);
						}
					}
				}
			}
			return list.ToArray();
		}

		// Token: 0x060077CB RID: 30667 RVA: 0x00281F1C File Offset: 0x0028011C
		public bool FindPlayers(Point p, int radius)
		{
			int num = 0;
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					bool flag2 = physics is Player && physics.IsLiving && (physics as Player).BoundDistance(p) < (double)radius;
					if (flag2)
					{
						num++;
					}
					bool flag3 = num >= 2;
					if (flag3)
					{
						return true;
					}
				}
			}
			return false;
		}

		// Token: 0x060077CC RID: 30668 RVA: 0x00281FEC File Offset: 0x002801EC
		public List<Player> FindPlayers(int x, int y, int radius)
		{
			List<Player> list = new List<Player>();
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					bool flag2 = physics is Player && physics.IsLiving && physics.Distance(x, y) < (double)radius;
					if (flag2)
					{
						list.Add(physics as Player);
					}
				}
			}
			return list;
		}

		// Token: 0x060077CD RID: 30669 RVA: 0x002820B0 File Offset: 0x002802B0
		public List<Living> FindLivings(int x, int y, int radius)
		{
			List<Living> list = new List<Living>();
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					bool flag2 = physics is Living && physics.IsLiving && physics.Distance(x, y) < (double)radius;
					if (flag2)
					{
						list.Add(physics as Living);
					}
				}
			}
			return list;
		}

		// Token: 0x060077CE RID: 30670 RVA: 0x00282174 File Offset: 0x00280374
		public List<Living> FindPlayers(int fx, int tx, List<Player> exceptPlayers)
		{
			List<Living> list = new List<Living>();
			HashSet<Physics> objects = this._objects;
			List<Living> list2;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					bool flag2 = !(physics is Player) || !(physics is Living) || !physics.IsLiving || physics.X <= fx || physics.X >= tx || (physics as Living).Config.IsHelper || !(physics as Player).IsActive;
					if (!flag2)
					{
						bool flag3 = exceptPlayers != null;
						if (flag3)
						{
							foreach (Player player in exceptPlayers)
							{
								bool flag4 = ((Player)physics).PlayerDetail != player.PlayerDetail;
								if (flag4)
								{
									list.Add(physics as Living);
								}
							}
						}
						else
						{
							list.Add(physics as Living);
						}
					}
				}
				list2 = list;
			}
			return list2;
		}

		// Token: 0x060077CF RID: 30671 RVA: 0x0028230C File Offset: 0x0028050C
		public List<Living> FindRandomPlayer(int fx, int tx, List<Player> exceptPlayers)
		{
			List<Living> list = new List<Living>();
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					bool flag2 = !(physics is Player) || !physics.IsLiving || physics.X <= fx || physics.X >= tx;
					if (!flag2)
					{
						foreach (Player player in exceptPlayers)
						{
							bool flag3 = ((Player)physics).PlayerDetail == player.PlayerDetail;
							if (flag3)
							{
								list.Add(physics as Living);
							}
						}
					}
				}
			}
			List<Living> list2 = new List<Living>();
			bool flag4 = list.Count > 0;
			if (flag4)
			{
				list2.Add(list[this._rand.Next(list.Count)]);
			}
			return list2;
		}

		// Token: 0x060077D0 RID: 30672 RVA: 0x00282470 File Offset: 0x00280670
		public List<Living> FindRandomLiving(int fx, int tx)
		{
			List<Living> list = new List<Living>();
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					bool flag2 = (physics is SimpleNpc || physics is SimpleBoss) && physics.IsLiving && physics.X > fx && physics.X < tx;
					if (flag2)
					{
						list.Add(physics as Living);
					}
				}
			}
			List<Living> list2 = new List<Living>();
			bool flag3 = list.Count > 0;
			if (flag3)
			{
				list2.Add(list[this._rand.Next(list.Count)]);
			}
			return list2;
		}

		// Token: 0x060077D1 RID: 30673 RVA: 0x0028257C File Offset: 0x0028077C
		public List<Living> FindHitByHitPiont()
		{
			List<Living> list = new List<Living>();
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					bool flag2 = physics is Living && physics.IsLiving;
					if (flag2)
					{
						list.Add(physics as Living);
					}
				}
			}
			return list;
		}

		// Token: 0x060077D2 RID: 30674 RVA: 0x00282634 File Offset: 0x00280834
		public List<Living> FindHitByHitPiont(Point p, int radius)
		{
			List<Living> list = new List<Living>();
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					bool flag2 = physics is Living && physics.IsLiving && (physics as Living).BoundDistance(p) < (double)radius;
					if (flag2)
					{
						list.Add(physics as Living);
					}
				}
			}
			return list;
		}

		// Token: 0x060077D3 RID: 30675 RVA: 0x002826FC File Offset: 0x002808FC
		public Living FindNearestEnemy(int x, int y, double maxdistance, Living except)
		{
			Living living = null;
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					bool flag2 = physics is Living && physics != except && physics.IsLiving && ((Living)physics).Team != except.Team;
					if (flag2)
					{
						double num = physics.Distance(x, y);
						bool flag3 = num < maxdistance;
						if (flag3)
						{
							living = physics as Living;
							maxdistance = num;
						}
					}
				}
			}
			return living;
		}

		// Token: 0x060077D4 RID: 30676 RVA: 0x002827E4 File Offset: 0x002809E4
		public List<Living> FindAllNearestEnemy(int x, int y, double maxdistance, Living except)
		{
			List<Living> list = new List<Living>();
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					bool flag2 = physics is Living && physics != except && physics.IsLiving && ((Living)physics).Team != except.Team;
					if (flag2)
					{
						double num = physics.Distance(x, y);
						bool flag3 = num < maxdistance;
						if (flag3)
						{
							list.Add(physics as Living);
							maxdistance = num;
						}
					}
				}
			}
			return list;
		}

		// Token: 0x060077D5 RID: 30677 RVA: 0x002828D8 File Offset: 0x00280AD8
		public List<Living> FindAllNearestSameTeam(int x, int y, double maxdistance, Living except)
		{
			List<Living> list = new List<Living>();
			HashSet<Physics> objects = this._objects;
			lock (objects)
			{
				foreach (Physics physics in this._objects)
				{
					bool flag2 = physics is Living && physics != except && physics.IsLiving && ((Living)physics).Team == except.Team;
					if (flag2)
					{
						double num = physics.Distance(x, y);
						bool flag3 = num < maxdistance;
						if (flag3)
						{
							list.Add(physics as Living);
							maxdistance = num;
						}
					}
				}
			}
			return list;
		}

		// Token: 0x060077D6 RID: 30678 RVA: 0x002829C8 File Offset: 0x00280BC8
		public void Dispose()
		{
			foreach (Physics physics in this._objects)
			{
				physics.Dispose();
			}
		}

		// Token: 0x060077D7 RID: 30679 RVA: 0x00282A20 File Offset: 0x00280C20
		public Map Clone()
		{
			Tile tile = ((this._layer1 != null) ? this._layer1.Clone() : null);
			Tile tile2 = ((this._layer2 != null) ? this._layer2.Clone() : null);
			return new Map(this._info, tile, tile2);
		}

		// Token: 0x04004614 RID: 17940
		private MapInfo _info;

		// Token: 0x04004615 RID: 17941
		private float _wind = 0f;

		// Token: 0x04004616 RID: 17942
		private float _windRate = 1f;

		// Token: 0x04004617 RID: 17943
		private HashSet<Physics> _objects;

		// Token: 0x04004618 RID: 17944
		protected Tile _layer1;

		// Token: 0x04004619 RID: 17945
		protected Tile _layer2;

		// Token: 0x0400461A RID: 17946
		protected Rectangle _bound;

		// Token: 0x0400461B RID: 17947
		private Random _rand;
	}
}
