﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EE2 RID: 3810
	public class ContinueReduceBaseDamageEffect : AbstractEffect
	{
		// Token: 0x060082F8 RID: 33528 RVA: 0x00033DC5 File Offset: 0x00031FC5
		public ContinueReduceBaseDamageEffect(int count, int effectcount)
			: base(eEffectType.ContinueReduceBaseDamageEffect)
		{
			this.m_count = count;
			this.m_effectcount = effectcount;
		}

		// Token: 0x060082F9 RID: 33529 RVA: 0x002B2414 File Offset: 0x002B0614
		public override bool Start(Living living)
		{
			ContinueReduceBaseDamageEffect continueReduceBaseDamageEffect = living.EffectList.GetOfType(eEffectType.ContinueReduceBaseDamageEffect) as ContinueReduceBaseDamageEffect;
			bool flag = continueReduceBaseDamageEffect != null;
			bool flag2;
			if (flag)
			{
				continueReduceBaseDamageEffect.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082FA RID: 33530 RVA: 0x002B245C File Offset: 0x002B065C
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginFitting;
			living.BaseDamage = living.BaseDamage * (double)(100 - this.m_effectcount) / 100.0;
			living.Game.SendPlayerPicture(living, 4, true);
		}

		// Token: 0x060082FB RID: 33531 RVA: 0x002B24B0 File Offset: 0x002B06B0
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginFitting;
			living.BaseDamage = living.BaseDamage / (double)(100 - this.m_effectcount) * 100.0;
			living.Game.SendPlayerPicture(living, 4, false);
		}

		// Token: 0x060082FC RID: 33532 RVA: 0x002B2504 File Offset: 0x002B0704
		private void player_BeginFitting(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x040051ED RID: 20973
		private int m_count;

		// Token: 0x040051EE RID: 20974
		private int m_effectcount;
	}
}
