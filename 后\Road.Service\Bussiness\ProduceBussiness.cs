﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using EntityDatabase.ServerModels;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness
{
	// Token: 0x02000FB5 RID: 4021
	public class ProduceBussiness : BaseBussiness
	{
		// Token: 0x0600899F RID: 35231 RVA: 0x002E9CC4 File Offset: 0x002E7EC4
		public ItemTemplateInfo[] GetAllGoods()
		{
			List<ItemTemplateInfo> list = new List<ItemTemplateInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Items_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitItemTemplateInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089A0 RID: 35232 RVA: 0x002E9D80 File Offset: 0x002E7F80
		public ItemTemplateInfo GetSingleGoods(int goodsID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", SqlDbType.Int, 4)
				};
				array[0].Value = goodsID;
				this.db.GetReader(ref sqlDataReader, "SP_Items_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return this.InitItemTemplateInfo(sqlDataReader);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x060089A1 RID: 35233 RVA: 0x002E9E50 File Offset: 0x002E8050
		public ItemTemplateInfo[] GetSingleCategory(int CategoryID)
		{
			List<ItemTemplateInfo> list = new List<ItemTemplateInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@CategoryID", SqlDbType.Int, 4)
				};
				array[0].Value = CategoryID;
				this.db.GetReader(ref sqlDataReader, "SP_Items_Category_Single", array);
				while (sqlDataReader.Read())
				{
					list.Add(this.InitItemTemplateInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089A2 RID: 35234 RVA: 0x002E9F34 File Offset: 0x002E8134
		public ItemTemplateInfo InitItemTemplateInfo(SqlDataReader reader)
		{
			return new ItemTemplateInfo
			{
				AddTime = reader["AddTime"].ToString(),
				Agility = (int)reader["Agility"],
				Attack = (int)reader["Attack"],
				CanDelete = (bool)reader["CanDelete"],
				CanDrop = (bool)reader["CanDrop"],
				CanEquip = (bool)reader["CanEquip"],
				CanUse = (bool)reader["CanUse"],
				CategoryID = (int)reader["CategoryID"],
				Colors = reader["Colors"].ToString(),
				Defence = (int)reader["Defence"],
				Description = reader["Description"].ToString(),
				Level = (int)reader["Level"],
				Luck = (int)reader["Luck"],
				MaxCount = (int)reader["MaxCount"],
				Name = reader["Name"].ToString(),
				NeedSex = (int)reader["NeedSex"],
				Pic = reader["Pic"].ToString(),
				Data = ((reader["Data"] == null) ? "" : reader["Data"].ToString()),
				Property1 = (int)reader["Property1"],
				Property2 = (int)reader["Property2"],
				Property3 = (int)reader["Property3"],
				Property4 = (int)reader["Property4"],
				Property5 = (int)reader["Property5"],
				Property6 = (int)reader["Property6"],
				Property7 = (int)reader["Property7"],
				Property8 = (int)reader["Property8"],
				Property9 = (int)reader["Property9"],
				Quality = (int)reader["Quality"],
				Script = reader["Script"].ToString(),
				TemplateID = (int)reader["TemplateID"],
				CanCompose = (bool)reader["CanCompose"],
				CanStrengthen = (bool)reader["CanStrengthen"],
				NeedLevel = (int)reader["NeedLevel"],
				BindType = (int)reader["BindType"],
				FusionType = (int)reader["FusionType"],
				FusionRate = (int)reader["FusionRate"],
				FusionNeedRate = (int)reader["FusionNeedRate"],
				Hole = ((reader["Hole"] == null) ? "" : reader["Hole"].ToString()),
				RefineryLevel = (int)reader["RefineryLevel"],
				ReclaimValue = (int)reader["ReclaimValue"],
				ReclaimType = (int)reader["ReclaimType"],
				CanRecycle = (int)reader["CanRecycle"],
				IsDirty = false
			};
		}

		// Token: 0x060089A3 RID: 35235 RVA: 0x002EA344 File Offset: 0x002E8544
		public ItemBoxInfo[] GetItemBoxInfos()
		{
			List<ItemBoxInfo> list = new List<ItemBoxInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Items_Box_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitItemBoxInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ILog log = BaseBussiness.log;
					string text = "Init@Shop_Goods_Box：";
					Exception ex2 = ex;
					log.Error(text + ((ex2 != null) ? ex2.ToString() : null));
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089A4 RID: 35236 RVA: 0x002EA414 File Offset: 0x002E8614
		public ItemBoxInfo InitItemBoxInfo(SqlDataReader reader)
		{
			return new ItemBoxInfo
			{
				Id = (int)reader["id"],
				DataId = (int)reader["DataId"],
				TemplateId = (int)reader["TemplateId"],
				IsSelect = (bool)reader["IsSelect"],
				IsBind = (bool)reader["IsBind"],
				ItemValid = (int)reader["ItemValid"],
				ItemCount = (int)reader["ItemCount"],
				StrengthenLevel = (int)reader["StrengthenLevel"],
				AttackCompose = (int)reader["AttackCompose"],
				DefendCompose = (int)reader["DefendCompose"],
				AgilityCompose = (int)reader["AgilityCompose"],
				LuckCompose = (int)reader["LuckCompose"],
				Random = (int)reader["Random"],
				IsTips = (bool)reader["IsTips"],
				IsLogs = (bool)reader["IsLogs"],
				SortID = (int)reader["SortID"]
			};
		}

		// Token: 0x060089A5 RID: 35237 RVA: 0x002EA5A0 File Offset: 0x002E87A0
		public LevelInfo[] GetAllLevel()
		{
			List<LevelInfo> list = new List<LevelInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Level_All");
				while (sqlDataReader.Read())
				{
					list.Add(new LevelInfo
					{
						Grade = (int)sqlDataReader["Grade"],
						GP = (int)sqlDataReader["GP"],
						Blood = (int)sqlDataReader["Blood"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllLevel", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089A6 RID: 35238 RVA: 0x002EA6A4 File Offset: 0x002E88A4
		public MagicClothInfo[] GetAllMagicCloth()
		{
			List<MagicClothInfo> list = new List<MagicClothInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_MagicCloth_All");
				while (sqlDataReader.Read())
				{
					list.Add(new MagicClothInfo
					{
						ID = (int)sqlDataReader["ID"],
						Name = (string)sqlDataReader["Name"],
						HasShow = (int)sqlDataReader["HasShow"],
						Type = (int)sqlDataReader["Type"],
						HeadID = (int)sqlDataReader["HeadID"],
						HairID = (int)sqlDataReader["HairID"],
						EffID = (int)sqlDataReader["EffID"],
						ClothID = (int)sqlDataReader["ClothID"],
						GlassID = (int)sqlDataReader["GlassID"],
						FaceID = (int)sqlDataReader["FaceID"],
						WingID = (int)sqlDataReader["WingID"],
						SuitsID = (int)sqlDataReader["SuitsID"],
						Sex = (int)sqlDataReader["Sex"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllMagicCloth", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089A7 RID: 35239 RVA: 0x002EA8AC File Offset: 0x002E8AAC
		public CategoryInfo[] GetAllCategory()
		{
			List<CategoryInfo> list = new List<CategoryInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Items_Category_All");
				while (sqlDataReader.Read())
				{
					list.Add(new CategoryInfo
					{
						ID = (int)sqlDataReader["ID"],
						Name = ((sqlDataReader["Name"] == null) ? "" : sqlDataReader["Name"].ToString()),
						Place = (int)sqlDataReader["Place"],
						Remark = ((sqlDataReader["Remark"] == null) ? "" : sqlDataReader["Remark"].ToString())
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089A8 RID: 35240 RVA: 0x002EA9F4 File Offset: 0x002E8BF4
		public BallInfo[] GetAllBall()
		{
			List<BallInfo> list = new List<BallInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Ball_All");
				while (sqlDataReader.Read())
				{
					list.Add(new BallInfo
					{
						Amount = (int)sqlDataReader["Amount"],
						ID = (int)sqlDataReader["ID"],
						Name = sqlDataReader["Name"].ToString(),
						Power = (double)sqlDataReader["Power"],
						Radii = (int)sqlDataReader["Radii"],
						BombPartical = sqlDataReader["BombPartical"].ToString(),
						Crater = ((sqlDataReader["Crater"] == null) ? "" : sqlDataReader["Crater"].ToString()),
						FlyingPartical = sqlDataReader["FlyingPartical"].ToString(),
						IsSpin = (bool)sqlDataReader["IsSpin"],
						Mass = (int)sqlDataReader["Mass"],
						SpinV = (int)sqlDataReader["SpinV"],
						SpinVA = (double)sqlDataReader["SpinVA"],
						Wind = (int)sqlDataReader["Wind"],
						DragIndex = (int)sqlDataReader["DragIndex"],
						Weight = (int)sqlDataReader["Weight"],
						Shake = (bool)sqlDataReader["Shake"],
						Delay = (int)sqlDataReader["Delay"],
						ShootSound = ((sqlDataReader["ShootSound"] == null) ? "" : sqlDataReader["ShootSound"].ToString()),
						BombSound = ((sqlDataReader["BombSound"] == null) ? "" : sqlDataReader["BombSound"].ToString()),
						ActionType = (int)sqlDataReader["ActionType"],
						HasTunnel = (bool)sqlDataReader["HasTunnel"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089A9 RID: 35241 RVA: 0x002EACF0 File Offset: 0x002E8EF0
		public BombConfigInfo[] GetBombConfigInfo()
		{
			List<BombConfigInfo> list = new List<BombConfigInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Bomb_Config_All");
				while (sqlDataReader.Read())
				{
					list.Add(new BombConfigInfo
					{
						TemplateID = (int)sqlDataReader["TemplateID"],
						Common = (int)sqlDataReader["Common"],
						CommonAddWound = (int)sqlDataReader["CommonAddWound"],
						CommonMultiBall = (int)sqlDataReader["CommonMultiBall"],
						Special = (int)sqlDataReader["Special"],
						SpecialII = (int)sqlDataReader["SpecialII"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089AA RID: 35242 RVA: 0x002EAE3C File Offset: 0x002E903C
		public ShopItemInfo[] GetALllShop()
		{
			List<ShopItemInfo> list = new List<ShopItemInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Shop_All");
				while (sqlDataReader.Read())
				{
					list.Add(new ShopItemInfo
					{
						ID = int.Parse(sqlDataReader["ID"].ToString()),
						ShopID = int.Parse(sqlDataReader["ShopID"].ToString()),
						GroupID = int.Parse(sqlDataReader["GroupID"].ToString()),
						TemplateID = int.Parse(sqlDataReader["TemplateID"].ToString()),
						BuyType = int.Parse(sqlDataReader["BuyType"].ToString()),
						IsContinue = bool.Parse(sqlDataReader["IsContinue"].ToString()),
						IsBind = int.Parse(sqlDataReader["IsBind"].ToString()),
						IsVouch = int.Parse(sqlDataReader["IsVouch"].ToString()),
						Label = (float)int.Parse(sqlDataReader["Label"].ToString()),
						Beat = decimal.Parse(sqlDataReader["Beat"].ToString()),
						AUnit = int.Parse(sqlDataReader["AUnit"].ToString()),
						APrice1 = int.Parse(sqlDataReader["APrice1"].ToString()),
						AValue1 = int.Parse(sqlDataReader["AValue1"].ToString()),
						APrice2 = int.Parse(sqlDataReader["APrice2"].ToString()),
						AValue2 = int.Parse(sqlDataReader["AValue2"].ToString()),
						APrice3 = int.Parse(sqlDataReader["APrice3"].ToString()),
						AValue3 = int.Parse(sqlDataReader["AValue3"].ToString()),
						BUnit = int.Parse(sqlDataReader["BUnit"].ToString()),
						BPrice1 = int.Parse(sqlDataReader["BPrice1"].ToString()),
						BValue1 = int.Parse(sqlDataReader["BValue1"].ToString()),
						BPrice2 = int.Parse(sqlDataReader["BPrice2"].ToString()),
						BValue2 = int.Parse(sqlDataReader["BValue2"].ToString()),
						BPrice3 = int.Parse(sqlDataReader["BPrice3"].ToString()),
						BValue3 = int.Parse(sqlDataReader["BValue3"].ToString()),
						CUnit = int.Parse(sqlDataReader["CUnit"].ToString()),
						CPrice1 = int.Parse(sqlDataReader["CPrice1"].ToString()),
						CValue1 = int.Parse(sqlDataReader["CValue1"].ToString()),
						CPrice2 = int.Parse(sqlDataReader["CPrice2"].ToString()),
						CValue2 = int.Parse(sqlDataReader["CValue2"].ToString()),
						CPrice3 = int.Parse(sqlDataReader["CPrice3"].ToString()),
						CValue3 = int.Parse(sqlDataReader["CValue3"].ToString()),
						IsCheap = bool.Parse(sqlDataReader["IsCheap"].ToString()),
						LimitCount = int.Parse(sqlDataReader["LimitCount"].ToString()),
						StartDate = DateTime.Parse(sqlDataReader["StartDate"].ToString()),
						EndDate = DateTime.Parse(sqlDataReader["EndDate"].ToString())
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089AB RID: 35243 RVA: 0x002EB2F0 File Offset: 0x002E94F0
		public ShopGoodsShowListInfo[] GetAllShopGoodsShowList()
		{
			List<ShopGoodsShowListInfo> list = new List<ShopGoodsShowListInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Shop_Goods_Show");
				while (sqlDataReader.Read())
				{
					list.Add(new ShopGoodsShowListInfo
					{
						Type = (int)sqlDataReader["Type"],
						ShopId = (int)sqlDataReader["ShopId"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089AC RID: 35244 RVA: 0x002EB3DC File Offset: 0x002E95DC
		public FusionInfo[] GetAllFusion()
		{
			List<FusionInfo> list = new List<FusionInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Fusion_All");
				while (sqlDataReader.Read())
				{
					list.Add(new FusionInfo
					{
						FusionID = (int)sqlDataReader["FusionID"],
						Item1 = (int)sqlDataReader["Item1"],
						Item2 = (int)sqlDataReader["Item2"],
						Item3 = (int)sqlDataReader["Item3"],
						Item4 = (int)sqlDataReader["Item4"],
						Reward = (int)sqlDataReader["Reward"],
						IsTips = (bool)sqlDataReader["IsTips"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllFusion", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089AD RID: 35245 RVA: 0x002EB55C File Offset: 0x002E975C
		public StrengthenInfo[] GetAllStrengthen()
		{
			List<StrengthenInfo> list = new List<StrengthenInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Item_Strengthen_All");
				while (sqlDataReader.Read())
				{
					list.Add(new StrengthenInfo
					{
						StrengthenLevel = (int)sqlDataReader["StrengthenLevel"],
						Random = (int)sqlDataReader["Random"],
						Rock = (int)sqlDataReader["Rock"],
						Rock1 = (int)sqlDataReader["Rock1"],
						Rock2 = (int)sqlDataReader["Rock2"],
						Rock3 = (int)sqlDataReader["Rock3"],
						StoneLevelMin = (int)sqlDataReader["StoneLevelMin"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllStrengthen", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089AE RID: 35246 RVA: 0x002EB6DC File Offset: 0x002E98DC
		public StrengthenGoodsInfo[] GetAllStrengthenGoodsInfo()
		{
			List<StrengthenGoodsInfo> list = new List<StrengthenGoodsInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Item_StrengthenGoodsInfo_All");
				while (sqlDataReader.Read())
				{
					list.Add(new StrengthenGoodsInfo
					{
						ID = (int)sqlDataReader["ID"],
						Level = (int)sqlDataReader["Level"],
						CurrentEquip = (int)sqlDataReader["CurrentEquip"],
						GainEquip = (int)sqlDataReader["GainEquip"],
						OrginEquip = (int)sqlDataReader["OrginEquip"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllStrengthenGoodsInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089AF RID: 35247 RVA: 0x002EB814 File Offset: 0x002E9A14
		public StrengthenInfo[] GetAllRefineryStrengthen()
		{
			List<StrengthenInfo> list = new List<StrengthenInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Item_Refinery_Strengthen_All");
				while (sqlDataReader.Read())
				{
					list.Add(new StrengthenInfo
					{
						StrengthenLevel = (int)sqlDataReader["StrengthenLevel"],
						Rock = (int)sqlDataReader["Rock"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllRefineryStrengthen", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089B0 RID: 35248 RVA: 0x002EB900 File Offset: 0x002E9B00
		public QuestInfo[] GetALlQuest()
		{
			List<QuestInfo> list = new List<QuestInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Quest_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitQuest(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089B1 RID: 35249 RVA: 0x002EB9BC File Offset: 0x002E9BBC
		public QuestAwardInfo[] GetAllQuestGoods()
		{
			List<QuestAwardInfo> list = new List<QuestAwardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Quest_Goods_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitQuestGoods(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089B2 RID: 35250 RVA: 0x002EBA78 File Offset: 0x002E9C78
		public QuestConditionInfo[] GetAllQuestCondiction()
		{
			List<QuestConditionInfo> list = new List<QuestConditionInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Quest_Condiction_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitQuestCondiction(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089B3 RID: 35251 RVA: 0x002EBB34 File Offset: 0x002E9D34
		public QuestInfo GetSingleQuest(int questID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@QuestID", SqlDbType.Int, 4)
				};
				array[0].Value = questID;
				this.db.GetReader(ref sqlDataReader, "SP_Quest_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return this.InitQuest(sqlDataReader);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x060089B4 RID: 35252 RVA: 0x002EBC04 File Offset: 0x002E9E04
		public QuestInfo InitQuest(SqlDataReader reader)
		{
			return new QuestInfo
			{
				ID = (int)reader["ID"],
				QuestID = (int)reader["QuestID"],
				Title = ((reader["Title"] == null) ? "" : reader["Title"].ToString()),
				Detail = ((reader["Detail"] == null) ? "" : reader["Detail"].ToString()),
				Objective = ((reader["Objective"] == null) ? "" : reader["Objective"].ToString()),
				NeedMinLevel = (int)reader["NeedMinLevel"],
				NeedMaxLevel = (int)reader["NeedMaxLevel"],
				PreQuestID = ((reader["PreQuestID"] == null) ? "" : reader["PreQuestID"].ToString()),
				NextQuestID = ((reader["NextQuestID"] == null) ? "" : reader["NextQuestID"].ToString()),
				IsOther = (int)reader["IsOther"],
				CanRepeat = (bool)reader["CanRepeat"],
				RepeatInterval = (int)reader["RepeatInterval"],
				RepeatMax = (int)reader["RepeatMax"],
				RewardGP = (int)reader["RewardGP"],
				RewardGold = (int)reader["RewardGold"],
				RewardGiftToken = (int)reader["RewardGiftToken"],
				RewardOffer = (int)reader["RewardOffer"],
				RewardRiches = (int)reader["RewardRiches"],
				RewardBuffID = (int)reader["RewardBuffID"],
				RewardBuffDate = (int)reader["RewardBuffDate"],
				RewardMoney = (int)reader["RewardMoney"],
				Rands = (decimal)reader["Rands"],
				RandDouble = (int)reader["RandDouble"],
				TimeMode = (bool)reader["TimeMode"],
				StartDate = (DateTime)reader["StartDate"],
				EndDate = (DateTime)reader["EndDate"],
				MapID = (int)reader["MapID"],
				AutoEquip = (bool)reader["AutoEquip"],
				RewardMedal = (int)reader["RewardMedal"],
				Rank = ((reader["Rank"] == null) ? "" : reader["Rank"].ToString()),
				StarLev = (int)reader["StarLev"],
				NotMustCount = (int)reader["NotMustCount"]
			};
		}

		// Token: 0x060089B5 RID: 35253 RVA: 0x002EBF78 File Offset: 0x002EA178
		public QuestAwardInfo InitQuestGoods(SqlDataReader reader)
		{
			return new QuestAwardInfo
			{
				QuestID = (int)reader["QuestID"],
				RewardItemID = (int)reader["RewardItemID"],
				IsSelect = (bool)reader["IsSelect"],
				RewardItemValid = (int)reader["RewardItemValid"],
				RewardItemCount = (int)reader["RewardItemCount"],
				StrengthenLevel = (int)reader["StrengthenLevel"],
				AttackCompose = (int)reader["AttackCompose"],
				DefendCompose = (int)reader["DefendCompose"],
				AgilityCompose = (int)reader["AgilityCompose"],
				LuckCompose = (int)reader["LuckCompose"],
				IsCount = (bool)reader["IsCount"],
				IsBind = (bool)reader["IsBind"]
			};
		}

		// Token: 0x060089B6 RID: 35254 RVA: 0x002EC0A8 File Offset: 0x002EA2A8
		public QuestConditionInfo InitQuestCondiction(SqlDataReader reader)
		{
			return new QuestConditionInfo
			{
				QuestID = (int)reader["QuestID"],
				CondictionID = (int)reader["CondictionID"],
				CondictionTitle = ((reader["CondictionTitle"] == null) ? "" : reader["CondictionTitle"].ToString()),
				CondictionType = (int)reader["CondictionType"],
				Para1 = (int)reader["Para1"],
				Para2 = (int)reader["Para2"],
				isOpitional = (bool)reader["isOpitional"]
			};
		}

		// Token: 0x060089B7 RID: 35255 RVA: 0x002EC178 File Offset: 0x002EA378
		public DropCondiction[] GetAllDropCondictions()
		{
			List<DropCondiction> list = new List<DropCondiction>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Drop_Condiction_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitDropCondiction(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089B8 RID: 35256 RVA: 0x002EC234 File Offset: 0x002EA434
		public DropItem[] GetAllDropItems()
		{
			List<DropItem> list = new List<DropItem>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Drop_Item_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitDropItem(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089B9 RID: 35257 RVA: 0x002EC2F0 File Offset: 0x002EA4F0
		public DropCondiction InitDropCondiction(SqlDataReader reader)
		{
			return new DropCondiction
			{
				DropId = (int)reader["DropID"],
				CondictionType = (int)reader["CondictionType"],
				Para1 = (string)reader["Para1"],
				Para2 = (string)reader["Para2"]
			};
		}

		// Token: 0x060089BA RID: 35258 RVA: 0x002EC368 File Offset: 0x002EA568
		public DropItem InitDropItem(SqlDataReader reader)
		{
			return new DropItem
			{
				Id = (int)reader["Id"],
				DropId = (int)reader["DropId"],
				ItemId = (int)reader["ItemId"],
				ValueDate = (int)reader["ValueDate"],
				IsBind = (bool)reader["IsBind"],
				Random = (int)reader["Random"],
				BeginData = (int)reader["BeginData"],
				EndData = (int)reader["EndData"],
				IsTips = (bool)reader["IsTips"],
				IsLogs = (bool)reader["IsLogs"]
			};
		}

		// Token: 0x060089BB RID: 35259 RVA: 0x002EC468 File Offset: 0x002EA668
		public AASInfo[] GetAllAASInfo()
		{
			List<AASInfo> list = new List<AASInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_AASInfo_All");
				while (sqlDataReader.Read())
				{
					list.Add(new AASInfo
					{
						UserID = (int)sqlDataReader["ID"],
						Name = sqlDataReader["Name"].ToString(),
						IDNumber = sqlDataReader["IDNumber"].ToString(),
						State = (int)sqlDataReader["State"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllAASInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089BC RID: 35260 RVA: 0x002EC584 File Offset: 0x002EA784
		public bool AddAASInfo(AASInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@Name", info.Name),
					new SqlParameter("@IDNumber", info.IDNumber),
					new SqlParameter("@State", info.State),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[4].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_ASSInfo_Add", array);
				flag = (int)array[4].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateAASInfo", ex);
				}
			}
			return flag;
		}

		// Token: 0x060089BD RID: 35261 RVA: 0x002EC670 File Offset: 0x002EA870
		public string GetASSInfoSingle(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", UserID)
				};
				this.db.GetReader(ref sqlDataReader, "SP_ASSInfo_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return sqlDataReader["IDNumber"].ToString();
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetASSInfoSingle", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return "";
		}

		// Token: 0x060089BE RID: 35262 RVA: 0x002EC744 File Offset: 0x002EA944
		public DailyAwardInfo[] GetAllDailyAward()
		{
			List<DailyAwardInfo> list = new List<DailyAwardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Daily_Award_All");
				while (sqlDataReader.Read())
				{
					list.Add(new DailyAwardInfo
					{
						Count = (int)sqlDataReader["Count"],
						ID = (int)sqlDataReader["ID"],
						IsBinds = (bool)sqlDataReader["IsBinds"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						Type = (int)sqlDataReader["Type"],
						ValidDate = (int)sqlDataReader["ValidDate"],
						Sex = (int)sqlDataReader["Sex"],
						Remark = ((sqlDataReader["Remark"] == null) ? "" : sqlDataReader["Remark"].ToString()),
						CountRemark = ((sqlDataReader["CountRemark"] == null) ? "" : sqlDataReader["CountRemark"].ToString()),
						GetWay = (int)sqlDataReader["GetWay"],
						AwardDays = (int)sqlDataReader["AwardDays"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllDailyAward", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089BF RID: 35263 RVA: 0x002EC948 File Offset: 0x002EAB48
		public PlayerAwardInfo[] GetAllPlayerAward()
		{
			List<PlayerAwardInfo> list = new List<PlayerAwardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Player_Award_All");
				while (sqlDataReader.Read())
				{
					list.Add(new PlayerAwardInfo
					{
						ID = (int)sqlDataReader["ID"],
						AwardIndex = (int)sqlDataReader["AwardIndex"],
						Type = (int)sqlDataReader["Type"],
						PlayerLevel = (int)sqlDataReader["PlayerLevel"],
						Reward = ((sqlDataReader["Reward"] == null) ? "" : sqlDataReader["Reward"].ToString())
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllPlayerAward", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089C0 RID: 35264 RVA: 0x002ECA94 File Offset: 0x002EAC94
		public FightLibDropAwardInfo[] GetAllFightLibAward()
		{
			List<FightLibDropAwardInfo> list = new List<FightLibDropAwardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_FightLib_Award_All");
				while (sqlDataReader.Read())
				{
					list.Add(new FightLibDropAwardInfo
					{
						Count = (int)sqlDataReader["Count"],
						ID = (int)sqlDataReader["ID"],
						AwardItem = (int)sqlDataReader["AwardItem"],
						Easy = (int)sqlDataReader["Easy"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllFightLibAward", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089C1 RID: 35265 RVA: 0x002ECBB0 File Offset: 0x002EADB0
		public UserBoxInfo[] GetAllUserBox()
		{
			List<UserBoxInfo> list = new List<UserBoxInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_User_Box_All");
				while (sqlDataReader.Read())
				{
					list.Add(new UserBoxInfo
					{
						ID = (int)sqlDataReader["ID"],
						Type = (int)sqlDataReader["Type"],
						Level = (int)sqlDataReader["Level"],
						Condition = (int)sqlDataReader["Condition"],
						TemplateID = (int)sqlDataReader["TemplateID"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllUserBox", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089C2 RID: 35266 RVA: 0x002ECCE8 File Offset: 0x002EAEE8
		public NpcInfo[] GetAllNPCInfo()
		{
			List<NpcInfo> list = new List<NpcInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_NPC_Info_All");
				while (sqlDataReader.Read())
				{
					list.Add(new NpcInfo
					{
						ID = (int)sqlDataReader["ID"],
						Name = ((sqlDataReader["Name"] == null) ? "" : sqlDataReader["Name"].ToString()),
						Level = (int)sqlDataReader["Level"],
						Camp = (int)sqlDataReader["Camp"],
						Type = (int)sqlDataReader["Type"],
						Blood = (int)sqlDataReader["Blood"],
						X = (int)sqlDataReader["X"],
						Y = (int)sqlDataReader["Y"],
						Width = (int)sqlDataReader["Width"],
						Height = (int)sqlDataReader["Height"],
						MoveMin = (int)sqlDataReader["MoveMin"],
						MoveMax = (int)sqlDataReader["MoveMax"],
						BaseDamage = (int)sqlDataReader["BaseDamage"],
						BaseGuard = (int)sqlDataReader["BaseGuard"],
						Attack = (int)sqlDataReader["Attack"],
						Defence = (int)sqlDataReader["Defence"],
						Agility = (int)sqlDataReader["Agility"],
						Lucky = (int)sqlDataReader["Lucky"],
						ModelID = ((sqlDataReader["ModelID"] == null) ? "" : sqlDataReader["ModelID"].ToString()),
						ResourcesPath = ((sqlDataReader["ResourcesPath"] == null) ? "" : sqlDataReader["ResourcesPath"].ToString()),
						DropRate = ((sqlDataReader["DropRate"] == null) ? 2 : Convert.ToInt32(sqlDataReader["DropRate"].ToString())),
						Experience = (int)sqlDataReader["Experience"],
						Delay = (int)sqlDataReader["Delay"],
						Immunity = (int)sqlDataReader["Immunity"],
						Alert = (int)sqlDataReader["Alert"],
						Range = (int)sqlDataReader["Range"],
						Preserve = (int)sqlDataReader["Preserve"],
						Script = ((sqlDataReader["Script"] == null) ? "" : sqlDataReader["Script"].ToString()),
						FireX = (int)sqlDataReader["FireX"],
						FireY = (int)sqlDataReader["FireY"],
						DropId = (int)sqlDataReader["DropId"],
						CurrentBallId = (int)sqlDataReader["CurrentBallId"],
						Speed = (int)sqlDataReader["Speed"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllNPCInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089C3 RID: 35267 RVA: 0x002ED120 File Offset: 0x002EB320
		public AchievementInfo[] GetAllAchievement()
		{
			List<AchievementInfo> list = new List<AchievementInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Get_Achievement");
				while (sqlDataReader.Read())
				{
					list.Add(new AchievementInfo
					{
						ID = (int)sqlDataReader["ID"],
						PlaceID = (int)sqlDataReader["PlaceID"],
						Title = (string)sqlDataReader["Title"],
						Detail = (string)sqlDataReader["Detail"],
						NeedMinLevel = (int)sqlDataReader["NeedMinLevel"],
						NeedMaxLevel = (int)sqlDataReader["NeedMaxLevel"],
						PreAchievementID = (string)sqlDataReader["PreAchievementID"],
						IsOther = (int)sqlDataReader["IsOther"],
						AchievementType = (int)sqlDataReader["AchievementType"],
						CanHide = (bool)sqlDataReader["CanHide"],
						StartDate = (DateTime)sqlDataReader["StartDate"],
						EndDate = (DateTime)sqlDataReader["EndDate"],
						AchievementPoint = (int)sqlDataReader["AchievementPoint"],
						IsActive = (int)sqlDataReader["IsActive"],
						PicID = (int)sqlDataReader["PicID"],
						IsShare = (bool)sqlDataReader["IsShare"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllAchievement", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089C4 RID: 35268 RVA: 0x002ED36C File Offset: 0x002EB56C
		public AchievementConditionInfo[] GetAllAchievementCondiction()
		{
			List<AchievementConditionInfo> list = new List<AchievementConditionInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Get_AchievementCondiction");
				while (sqlDataReader.Read())
				{
					list.Add(new AchievementConditionInfo
					{
						AchievementID = (int)sqlDataReader["AchievementID"],
						CondictionID = (int)sqlDataReader["CondictionID"],
						CondictionType = (int)sqlDataReader["CondictionType"],
						Condiction_Para1 = (int)sqlDataReader["Condiction_Para1"],
						Condiction_Para2 = (int)sqlDataReader["Condiction_Para2"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllAchievementCondiction", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089C5 RID: 35269 RVA: 0x002ED4A4 File Offset: 0x002EB6A4
		public AchievementGoodsInfo[] GetAllAchievementGoods()
		{
			List<AchievementGoodsInfo> list = new List<AchievementGoodsInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Get_AchievementGoods");
				while (sqlDataReader.Read())
				{
					list.Add(new AchievementGoodsInfo
					{
						AchievementID = (int)sqlDataReader["AchievementID"],
						RewardType = (int)sqlDataReader["RewardType"],
						RewardPara = (string)sqlDataReader["RewardPara"],
						RewardValueId = (int)sqlDataReader["RewardValueId"],
						RewardCount = (int)sqlDataReader["RewardCount"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllAchievementGoods", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089C6 RID: 35270 RVA: 0x002ED5DC File Offset: 0x002EB7DC
		public GoldEquipTemplateInfo[] GetAllGoldEquipTemplateLoad()
		{
			List<GoldEquipTemplateInfo> list = new List<GoldEquipTemplateInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_GoldEquipTemplate_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitGoldEquipTemplateLoad(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllGoldEquipTemplateLoad", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089C7 RID: 35271 RVA: 0x002ED698 File Offset: 0x002EB898
		public GoldEquipTemplateInfo InitGoldEquipTemplateLoad(SqlDataReader reader)
		{
			return new GoldEquipTemplateInfo
			{
				ID = (int)reader["ID"],
				OldTemplateId = (int)reader["OldTemplateId"],
				NewTemplateId = (int)reader["NewTemplateId"],
				CategoryID = (int)reader["CategoryID"],
				Strengthen = (int)reader["Strengthen"],
				Attack = (int)reader["Attack"],
				Defence = (int)reader["Defence"],
				Agility = (int)reader["Agility"],
				Luck = (int)reader["Luck"],
				Damage = (int)reader["Damage"],
				Guard = (int)reader["Guard"],
				Boold = (int)reader["Boold"],
				BlessID = (int)reader["BlessID"],
				Pic = ((reader["pic"] == null) ? "" : reader["pic"].ToString())
			};
		}

		// Token: 0x060089C8 RID: 35272 RVA: 0x002ED808 File Offset: 0x002EBA08
		public MissionInfo[] GetAllMissionInfo()
		{
			List<MissionInfo> list = new List<MissionInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Mission_Info_All");
				while (sqlDataReader.Read())
				{
					list.Add(new MissionInfo
					{
						Id = (int)sqlDataReader["ID"],
						Name = ((sqlDataReader["Name"] == null) ? "" : sqlDataReader["Name"].ToString()),
						TotalCount = (int)sqlDataReader["TotalCount"],
						TotalTurn = (int)sqlDataReader["TotalTurn"],
						Script = ((sqlDataReader["Script"] == null) ? "" : sqlDataReader["Script"].ToString()),
						Success = ((sqlDataReader["Success"] == null) ? "" : sqlDataReader["Success"].ToString()),
						Failure = ((sqlDataReader["Failure"] == null) ? "" : sqlDataReader["Failure"].ToString()),
						Description = ((sqlDataReader["Description"] == null) ? "" : sqlDataReader["Description"].ToString()),
						IncrementDelay = (int)sqlDataReader["IncrementDelay"],
						Delay = (int)sqlDataReader["Delay"],
						Title = ((sqlDataReader["Title"] == null) ? "" : sqlDataReader["Title"].ToString()),
						Param1 = (int)sqlDataReader["Param1"],
						Param2 = (int)sqlDataReader["Param2"],
						Param3 = (int)sqlDataReader["Param3"],
						Param4 = (int)sqlDataReader["Param4"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllMissionInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089C9 RID: 35273 RVA: 0x002EDAB8 File Offset: 0x002EBCB8
		public ExerciseInfo[] GetAllExercise()
		{
			List<ExerciseInfo> list = new List<ExerciseInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Exercise_All");
				while (sqlDataReader.Read())
				{
					list.Add(new ExerciseInfo
					{
						Grage = (int)sqlDataReader["Grage"],
						GP = (int)sqlDataReader["GP"],
						ExerciseA = (int)sqlDataReader["ExerciseA"],
						ExerciseAG = (int)sqlDataReader["ExerciseAG"],
						ExerciseD = (int)sqlDataReader["ExerciseD"],
						ExerciseH = (int)sqlDataReader["ExerciseH"],
						ExerciseL = (int)sqlDataReader["ExerciseL"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllExercise", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089CA RID: 35274 RVA: 0x002EDC38 File Offset: 0x002EBE38
		public EdictumInfo InitEdictum(SqlDataReader reader)
		{
			return new EdictumInfo
			{
				ID = (int)reader["ID"],
				Title = ((reader["Title"] == null) ? "" : reader["Title"].ToString()),
				BeginDate = (DateTime)reader["BeginDate"],
				BeginTime = (DateTime)reader["BeginTime"],
				EndDate = (DateTime)reader["EndDate"],
				EndTime = (DateTime)reader["EndTime"],
				Text = ((reader["Text"] == null) ? "" : reader["Text"].ToString()),
				IsExist = (bool)reader["IsExist"]
			};
		}

		// Token: 0x060089CB RID: 35275 RVA: 0x002EDD30 File Offset: 0x002EBF30
		public EdictumInfo[] GetAllEdictum()
		{
			List<EdictumInfo> list = new List<EdictumInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Edictum_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitEdictum(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllEdictum", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089CC RID: 35276 RVA: 0x002EDDEC File Offset: 0x002EBFEC
		public PetLevel[] GetAllPetLevel()
		{
			List<PetLevel> list = new List<PetLevel>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_PetLevel_All");
				while (sqlDataReader.Read())
				{
					list.Add(new PetLevel
					{
						Level = (int)sqlDataReader["Level"],
						GP = (int)sqlDataReader["GP"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllPetLevel", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089CD RID: 35277 RVA: 0x002EDED8 File Offset: 0x002EC0D8
		public PetTemplateInfo[] GetAllPetTemplateInfo()
		{
			List<PetTemplateInfo> list = new List<PetTemplateInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_PetTemplateInfo_All");
				while (sqlDataReader.Read())
				{
					list.Add(new PetTemplateInfo
					{
						TemplateID = (int)sqlDataReader["TemplateID"],
						Name = (string)sqlDataReader["Name"],
						KindID = (int)sqlDataReader["KindID"],
						Description = (string)sqlDataReader["Description"],
						Pic = (string)sqlDataReader["Pic"],
						RareLevel = (int)sqlDataReader["RareLevel"],
						MP = (int)sqlDataReader["MP"],
						StarLevel = (int)sqlDataReader["StarLevel"],
						GameAssetUrl = (string)sqlDataReader["GameAssetUrl"],
						EvolutionID = (int)sqlDataReader["EvolutionID"],
						HighAgility = (int)sqlDataReader["HighAgility"],
						HighAgilityGrow = (int)sqlDataReader["HighAgilityGrow"],
						HighAttack = (int)sqlDataReader["HighAttack"],
						HighAttackGrow = (int)sqlDataReader["HighAttackGrow"],
						HighBlood = (int)sqlDataReader["HighBlood"],
						HighBloodGrow = (int)sqlDataReader["HighBloodGrow"],
						HighDamage = (int)sqlDataReader["HighDamage"],
						HighDamageGrow = (int)sqlDataReader["HighDamageGrow"],
						HighDefence = (int)sqlDataReader["HighDefence"],
						HighDefenceGrow = (int)sqlDataReader["HighDefenceGrow"],
						HighGuard = (int)sqlDataReader["HighGuard"],
						HighGuardGrow = (int)sqlDataReader["HighGuardGrow"],
						HighLuck = (int)sqlDataReader["HighLuck"],
						HighLuckGrow = (int)sqlDataReader["HighLuckGrow"],
						LowBloodGrow = (int)sqlDataReader["LowBloodGrow"],
						LowAttackGrow = (int)sqlDataReader["LowAttackGrow"],
						LowDefenceGrow = (int)sqlDataReader["LowDefenceGrow"],
						LowAgilityGrow = (int)sqlDataReader["LowAgilityGrow"],
						LowLuckGrow = (int)sqlDataReader["LowLuckGrow"],
						WashGetCount = (int)sqlDataReader["WashGetCount"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllPetTemplateInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089CE RID: 35278 RVA: 0x002EE268 File Offset: 0x002EC468
		public PetSkillInfo[] GetAllPetSkillInfo()
		{
			List<PetSkillInfo> list = new List<PetSkillInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_PetSkillInfo_All");
				while (sqlDataReader.Read())
				{
					list.Add(new PetSkillInfo
					{
						ID = (int)sqlDataReader["ID"],
						Name = sqlDataReader["Name"].ToString(),
						ElementIDs = sqlDataReader["ElementIDs"].ToString(),
						Description = sqlDataReader["Description"].ToString(),
						BallType = (int)sqlDataReader["BallType"],
						NewBallID = (int)sqlDataReader["NewBallID"],
						CostMP = (int)sqlDataReader["CostMP"],
						CostFlag = (int)sqlDataReader["CostFlag"],
						Pic = (int)sqlDataReader["Pic"],
						Action = sqlDataReader["Action"].ToString(),
						EffectPic = sqlDataReader["EffectPic"].ToString(),
						Delay = (int)sqlDataReader["Delay"],
						ColdDown = (int)sqlDataReader["ColdDown"],
						GameType = (int)sqlDataReader["GameType"],
						Probability = (int)sqlDataReader["Probability"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllPetSkillInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089CF RID: 35279 RVA: 0x002EE4A0 File Offset: 0x002EC6A0
		public PetConfig[] GetAllPetConfig()
		{
			List<PetConfig> list = new List<PetConfig>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_PetConfig_All");
				while (sqlDataReader.Read())
				{
					list.Add(new PetConfig
					{
						ID = (int)sqlDataReader["ID"],
						Name = sqlDataReader["Name"].ToString(),
						Value = sqlDataReader["Value"].ToString()
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllPetConfig", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089D0 RID: 35280 RVA: 0x002EE5A4 File Offset: 0x002EC7A4
		public PetSkillTemplateInfo[] GetAllPetSkillTemplateInfo()
		{
			List<PetSkillTemplateInfo> list = new List<PetSkillTemplateInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_PetSkillTemplateInfo_All");
				while (sqlDataReader.Read())
				{
					list.Add(new PetSkillTemplateInfo
					{
						PetTemplateID = (int)sqlDataReader["PetTemplateID"],
						KindID = (int)sqlDataReader["KindID"],
						GetType = (int)sqlDataReader["GetType"],
						SkillID = (int)sqlDataReader["SkillID"],
						SkillBookID = (int)sqlDataReader["SkillBookID"],
						MinLevel = (int)sqlDataReader["MinLevel"],
						DeleteSkillIDs = sqlDataReader["DeleteSkillIDs"].ToString()
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllPetSkillTemplateInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089D1 RID: 35281 RVA: 0x002EE724 File Offset: 0x002EC924
		public PetSkillElementInfo[] GetAllPetSkillElementInfo()
		{
			List<PetSkillElementInfo> list = new List<PetSkillElementInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_PetSkillElementInfo_All");
				while (sqlDataReader.Read())
				{
					list.Add(new PetSkillElementInfo
					{
						ID = (int)sqlDataReader["ID"],
						Name = sqlDataReader["Name"].ToString(),
						EffectPic = sqlDataReader["EffectPic"].ToString(),
						Description = sqlDataReader["Description"].ToString(),
						Pic = (int)sqlDataReader["Pic"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllPetSkillElementInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089D2 RID: 35282 RVA: 0x002EE85C File Offset: 0x002ECA5C
		public PetExpItemPriceInfo[] GetAllPetExpItemPrice()
		{
			List<PetExpItemPriceInfo> list = new List<PetExpItemPriceInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_PetExpItemPriceInfo_All");
				while (sqlDataReader.Read())
				{
					list.Add(new PetExpItemPriceInfo
					{
						ID = (int)sqlDataReader["ID"],
						Count = (int)sqlDataReader["Count"],
						Money = (int)sqlDataReader["Money"],
						ItemCount = (int)sqlDataReader["ItemCount"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllPetExpItemPrice", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089D3 RID: 35283 RVA: 0x002EE978 File Offset: 0x002ECB78
		public DailyLeagueLevelInfo[] GetAllDailyLeagueLevelInfo()
		{
			List<DailyLeagueLevelInfo> list = new List<DailyLeagueLevelInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_DailyLeagueLevelInfo_All");
				while (sqlDataReader.Read())
				{
					list.Add(new DailyLeagueLevelInfo
					{
						Name = sqlDataReader["Name"].ToString(),
						Level = (int)sqlDataReader["Level"],
						Score = (int)sqlDataReader["Score"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllDailyLeagueLevelInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089D4 RID: 35284 RVA: 0x002EEA7C File Offset: 0x002ECC7C
		public DailyLeagueAwardInfo[] GetAllDailyLeagueAwardInfo()
		{
			List<DailyLeagueAwardInfo> list = new List<DailyLeagueAwardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_DailyLeagueAwardInfo_All");
				while (sqlDataReader.Read())
				{
					list.Add(new DailyLeagueAwardInfo
					{
						Level = (int)sqlDataReader["Level"],
						Class = (int)sqlDataReader["Class"],
						Count = (int)sqlDataReader["Count"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						RewardID = (int)sqlDataReader["RewardID"],
						StrengthenLevel = (int)sqlDataReader["StrengthenLevel"],
						ItemValid = (int)sqlDataReader["ItemValid"],
						IsBind = (bool)sqlDataReader["IsBind"],
						AgilityCompose = (int)sqlDataReader["AgilityCompose"],
						AttackCompose = (int)sqlDataReader["AttackCompose"],
						DefendCompose = (int)sqlDataReader["DefendCompose"],
						LuckCompose = (int)sqlDataReader["LuckCompose"],
						Hole1 = (int)sqlDataReader["Hole1"],
						Hole2 = (int)sqlDataReader["Hole2"],
						Hole3 = (int)sqlDataReader["Hole3"],
						Hole4 = (int)sqlDataReader["Hole4"],
						Hole5 = (int)sqlDataReader["Hole5"],
						Hole5Exp = (int)sqlDataReader["Hole5Exp"],
						Hole5Level = (int)sqlDataReader["Hole5Level"],
						Hole6 = (int)sqlDataReader["Hole6"],
						Hole6Exp = (int)sqlDataReader["Hole6Exp"],
						Hole6Level = (int)sqlDataReader["Hole6Level"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllDailyLeagueAwardInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089D5 RID: 35285 RVA: 0x002EED54 File Offset: 0x002ECF54
		public TsPairBoxInfo[] GetAllPairBoxAwardInfo()
		{
			List<TsPairBoxInfo> list = new List<TsPairBoxInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_PairBox_Award_All");
				while (sqlDataReader.Read())
				{
					list.Add(new TsPairBoxInfo
					{
						ID = (int)sqlDataReader["ID"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						Count = (int)sqlDataReader["Count"],
						Type = (int)sqlDataReader["Type"],
						IsBind = (bool)sqlDataReader["IsBind"],
						Random = (int)sqlDataReader["Random"],
						IsArea = (int)sqlDataReader["IsArea"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllPairBoxAwardInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089D6 RID: 35286 RVA: 0x002EEED4 File Offset: 0x002ED0D4
		public CardUpdateConditionInfo[] GetAllCardUpdateCondition()
		{
			List<CardUpdateConditionInfo> list = new List<CardUpdateConditionInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Get_CardUpdateCondiction");
				while (sqlDataReader.Read())
				{
					list.Add(new CardUpdateConditionInfo
					{
						Level = (int)sqlDataReader["Level"],
						Exp = (int)sqlDataReader["Exp"],
						MinExp = (int)sqlDataReader["MinExp"],
						MaxExp = (int)sqlDataReader["MaxExp"],
						UpdateCardCount = (int)sqlDataReader["UpdateCardCount"],
						ResetCardCount = (int)sqlDataReader["ResetCardCount"],
						ResetMoney = (int)sqlDataReader["ResetMoney"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089D7 RID: 35287 RVA: 0x002EF054 File Offset: 0x002ED254
		public CardUpdateInfo[] GetAllCardUpdateInfo()
		{
			List<CardUpdateInfo> list = new List<CardUpdateInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Get_CardUpdateInfo");
				while (sqlDataReader.Read())
				{
					list.Add(new CardUpdateInfo
					{
						Id = (int)sqlDataReader["Id"],
						Level = (int)sqlDataReader["Level"],
						Attack = (int)sqlDataReader["Attack"],
						Defend = (int)sqlDataReader["Defend"],
						Agility = (int)sqlDataReader["Agility"],
						Lucky = (int)sqlDataReader["Lucky"],
						Guard = (int)sqlDataReader["Guard"],
						Damage = (int)sqlDataReader["Damage"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089D8 RID: 35288 RVA: 0x002EF1E8 File Offset: 0x002ED3E8
		public CardInfo InitCard(SqlDataReader reader)
		{
			return new CardInfo
			{
				ID = (int)reader["ID"],
				SuitID = (int)reader["SuitID"],
				Name = ((reader["Name"] == null) ? "" : reader["Name"].ToString()),
				Description = ((reader["Description"] == null) ? "" : reader["Description"].ToString())
			};
		}

		// Token: 0x060089D9 RID: 35289 RVA: 0x002EF288 File Offset: 0x002ED488
		public CardGroupInfo InitCardGroup(SqlDataReader reader)
		{
			return new CardGroupInfo
			{
				ID = (int)reader["ID"],
				CardID = (int)reader["CardID"],
				TemplateID = (int)reader["TemplateID"]
			};
		}

		// Token: 0x060089DA RID: 35290 RVA: 0x002EF2E8 File Offset: 0x002ED4E8
		public CardBuffInfo InitCardBuff(SqlDataReader reader)
		{
			return new CardBuffInfo
			{
				ID = (int)reader["ID"],
				CardID = (int)reader["CardID"],
				Condition = (int)reader["condition"],
				PropertiesDscripID = (int)reader["PropertiesDscripID"],
				Value = ((reader["value"] == null) ? "" : reader["value"].ToString()),
				Description = ((reader["Description"] == null) ? "" : reader["Description"].ToString())
			};
		}

		// Token: 0x060089DB RID: 35291 RVA: 0x002EF3B4 File Offset: 0x002ED5B4
		public CardGroupInfo[] GetAllCardGroup()
		{
			List<CardGroupInfo> list = new List<CardGroupInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Card_Group_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitCardGroup(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllCardGroup", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089DC RID: 35292 RVA: 0x002EF470 File Offset: 0x002ED670
		public CardInfo[] GetAllCard()
		{
			List<CardInfo> list = new List<CardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Card_Info_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitCard(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllCard", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089DD RID: 35293 RVA: 0x002EF52C File Offset: 0x002ED72C
		public CardBuffInfo[] GetAllCardBuff()
		{
			List<CardBuffInfo> list = new List<CardBuffInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Card_Buff_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitCardBuff(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllCardBuff", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089DE RID: 35294 RVA: 0x002EF5E8 File Offset: 0x002ED7E8
		public QQTipsInfo[] GetAllQQtipsMessagesLoad()
		{
			List<QQTipsInfo> list = new List<QQTipsInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_QQtipsMessages_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitQQtipsMessagesLoad(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllQQtipsMessagesLoad", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089DF RID: 35295 RVA: 0x002EF6A4 File Offset: 0x002ED8A4
		public QQTipsInfo InitQQtipsMessagesLoad(SqlDataReader reader)
		{
			return new QQTipsInfo
			{
				ID = (int)reader["ID"],
				title = ((reader["title"] == null) ? "QQTips" : reader["title"].ToString()),
				content = ((reader["content"] == null) ? "欢迎来到弹弹堂的世界！官方唯一QQ群：" : reader["content"].ToString()),
				maxLevel = (int)reader["maxLevel"],
				minLevel = (int)reader["minLevel"],
				outInType = (int)reader["outInType"],
				moduleType = (int)reader["moduleType"],
				inItemID = (int)reader["inItemID"],
				url = ((reader["url"] == null) ? "http://www.52ddt.com/" : reader["url"].ToString()),
				ZoneID = (int)reader["ZoneID"]
			};
		}

		// Token: 0x060089E0 RID: 35296 RVA: 0x002EF7E0 File Offset: 0x002ED9E0
		public NewTitleInfo[] GetAllNewTitle()
		{
			List<NewTitleInfo> list = new List<NewTitleInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_New_Title_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitNewTitleInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("InitNewTitleInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089E1 RID: 35297 RVA: 0x002EF89C File Offset: 0x002EDA9C
		public NewTitleInfo InitNewTitleInfo(SqlDataReader dr)
		{
			return new NewTitleInfo
			{
				ID = (int)dr["ID"],
				Order = (int)dr["Order"],
				Show = (int)dr["Show"],
				Name = (string)dr["Name"],
				Pic = (int)dr["Pic"],
				Att = (int)dr["Att"],
				Def = (int)dr["Def"],
				Agi = (int)dr["Agi"],
				Luck = (int)dr["Luck"],
				Desc = (string)dr["Desc"]
			};
		}

		// Token: 0x060089E2 RID: 35298 RVA: 0x002EF99C File Offset: 0x002EDB9C
		public TotemInfo[] GetAllTotem()
		{
			List<TotemInfo> list = new List<TotemInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Totem_All");
				while (sqlDataReader.Read())
				{
					list.Add(new TotemInfo
					{
						ID = (int)sqlDataReader["ID"],
						ConsumeExp = (int)sqlDataReader["ConsumeExp"],
						ConsumeHonor = (int)sqlDataReader["ConsumeHonor"],
						AddAttack = (int)sqlDataReader["AddAttack"],
						AddDefence = (int)sqlDataReader["AddDefence"],
						AddAgility = (int)sqlDataReader["AddAgility"],
						AddLuck = (int)sqlDataReader["AddLuck"],
						AddBlood = (int)sqlDataReader["AddBlood"],
						AddDamage = (int)sqlDataReader["AddDamage"],
						AddGuard = (int)sqlDataReader["AddGuard"],
						Random = (int)sqlDataReader["Random"],
						Page = (int)sqlDataReader["Page"],
						Layers = (int)sqlDataReader["Layers"],
						Location = (int)sqlDataReader["Location"],
						Point = (int)sqlDataReader["Point"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetTotemAll", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089E3 RID: 35299 RVA: 0x002EFBD4 File Offset: 0x002EDDD4
		public TotemHonorTemplateInfo[] GetAllTotemHonorTemplate()
		{
			List<TotemHonorTemplateInfo> list = new List<TotemHonorTemplateInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_TotemHonorTemplate_All");
				while (sqlDataReader.Read())
				{
					list.Add(new TotemHonorTemplateInfo
					{
						ID = (int)sqlDataReader["ID"],
						NeedMoney = (int)sqlDataReader["NeedMoney"],
						Type = (int)sqlDataReader["Type"],
						AddHonor = (int)sqlDataReader["AddHonor"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetTotemHonorTemplateInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089E4 RID: 35300 RVA: 0x002EFCF0 File Offset: 0x002EDEF0
		public TreasureAwardInfo[] GetAllTreasureAward()
		{
			List<TreasureAwardInfo> list = new List<TreasureAwardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Treasure_Award_All");
				while (sqlDataReader.Read())
				{
					list.Add(new TreasureAwardInfo
					{
						ID = (int)sqlDataReader["ID"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						Type = (int)sqlDataReader["Type"],
						ItemValid = (int)sqlDataReader["ItemValid"],
						BeginData = (int)sqlDataReader["BeginData"],
						EndData = (int)sqlDataReader["EndData"],
						Random = (int)sqlDataReader["Random"],
						Score = (int)sqlDataReader["Score"],
						IsTip = (bool)sqlDataReader["IsTip"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllTreasureAward", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089E5 RID: 35301 RVA: 0x002EFE9C File Offset: 0x002EE09C
		public TreasureRankInfo[] GetAllTreasureRank()
		{
			List<TreasureRankInfo> list = new List<TreasureRankInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Treasure_Rank_All");
				while (sqlDataReader.Read())
				{
					list.Add(new TreasureRankInfo
					{
						ID = (int)sqlDataReader["ID"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						Count = (int)sqlDataReader["Count"],
						ValidDate = (int)sqlDataReader["ValidDate"],
						IsBinds = (bool)sqlDataReader["IsBinds"],
						IsUsed = (bool)sqlDataReader["IsUsed"],
						AttackCompose = (int)sqlDataReader["AttackCompose"],
						DefendCompose = (int)sqlDataReader["DefendCompose"],
						AgilityCompose = (int)sqlDataReader["AgilityCompose"],
						LuckCompose = (int)sqlDataReader["LuckCompose"],
						StrengthenLevel = (int)sqlDataReader["StrengthenLevel"],
						Hole1 = (int)sqlDataReader["Hole1"],
						Hole2 = (int)sqlDataReader["Hole2"],
						Hole3 = (int)sqlDataReader["Hole3"],
						Hole4 = (int)sqlDataReader["Hole4"],
						Hole5 = (int)sqlDataReader["Hole5"],
						Hole6 = (int)sqlDataReader["Hole6"],
						Hole5Exp = (int)sqlDataReader["Hole5Exp"],
						Hole5Level = (int)sqlDataReader["Hole5Level"],
						Hole6Exp = (int)sqlDataReader["Hole6Exp"],
						Hole6Level = (int)sqlDataReader["Hole6Level"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllTreasureRank", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089E6 RID: 35302 RVA: 0x002F015C File Offset: 0x002EE35C
		public WorldBossAwardInfo[] GetAllWorldBossAward()
		{
			List<WorldBossAwardInfo> list = new List<WorldBossAwardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_WorldBoss_Award_All");
				while (sqlDataReader.Read())
				{
					list.Add(new WorldBossAwardInfo
					{
						ID = (int)sqlDataReader["ID"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						Count = (int)sqlDataReader["Count"],
						ValidDate = (int)sqlDataReader["ValidDate"],
						IsBinds = (bool)sqlDataReader["IsBinds"],
						IsUsed = (bool)sqlDataReader["IsUsed"],
						AttackCompose = (int)sqlDataReader["AttackCompose"],
						DefendCompose = (int)sqlDataReader["DefendCompose"],
						AgilityCompose = (int)sqlDataReader["AgilityCompose"],
						LuckCompose = (int)sqlDataReader["LuckCompose"],
						StrengthenLevel = (int)sqlDataReader["StrengthenLevel"],
						Hole1 = (int)sqlDataReader["Hole1"],
						Hole2 = (int)sqlDataReader["Hole2"],
						Hole3 = (int)sqlDataReader["Hole3"],
						Hole4 = (int)sqlDataReader["Hole4"],
						Hole5 = (int)sqlDataReader["Hole5"],
						Hole6 = (int)sqlDataReader["Hole6"],
						Hole5Exp = (int)sqlDataReader["Hole5Exp"],
						Hole5Level = (int)sqlDataReader["Hole5Level"],
						Hole6Exp = (int)sqlDataReader["Hole6Exp"],
						Hole6Level = (int)sqlDataReader["Hole6Level"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllWorldBossAward", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089E7 RID: 35303 RVA: 0x002F041C File Offset: 0x002EE61C
		public GmActiveRewardInfo[] GetAllGmActiveReward()
		{
			List<GmActiveRewardInfo> list = new List<GmActiveRewardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_GM_Active_Reward_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitGmActiveRewardInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllGmActiveReward", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089E8 RID: 35304 RVA: 0x002F04D8 File Offset: 0x002EE6D8
		public GmActiveRewardInfo InitGmActiveRewardInfo(SqlDataReader dr)
		{
			return new GmActiveRewardInfo
			{
				giftId = (string)dr["giftId"],
				templateId = (int)dr["templateId"],
				count = (int)dr["count"],
				isBind = (bool)dr["isBind"],
				occupationOrSex = (int)dr["occupationOrSex"],
				rewardType = (int)dr["rewardType"],
				validDate = (int)dr["validDate"],
				property = (string)dr["property"],
				remain1 = (string)dr["remain1"]
			};
		}

		// Token: 0x060089E9 RID: 35305 RVA: 0x002F05C0 File Offset: 0x002EE7C0
		public GmActiveConditionInfo[] GetAllGmActiveCondition()
		{
			List<GmActiveConditionInfo> list = new List<GmActiveConditionInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_GM_Active_Condition_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitGmActiveConditionInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllGmActiveCondition", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089EA RID: 35306 RVA: 0x002F067C File Offset: 0x002EE87C
		public GmActiveConditionInfo InitGmActiveConditionInfo(SqlDataReader dr)
		{
			return new GmActiveConditionInfo
			{
				giftbagId = (string)dr["giftbagId"],
				conditionIndex = (int)dr["conditionIndex"],
				conditionValue = (int)dr["conditionValue"],
				remain1 = (int)dr["remain1"],
				remain2 = ((dr["remain2"] == DBNull.Value) ? null : ((string)dr["remain2"]))
			};
		}

		// Token: 0x060089EB RID: 35307 RVA: 0x002F0720 File Offset: 0x002EE920
		public GmGiftInfo[] GetAllGmGift()
		{
			List<GmGiftInfo> list = new List<GmGiftInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_GM_Gift_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitGmGiftInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllGmGift", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089EC RID: 35308 RVA: 0x002F07DC File Offset: 0x002EE9DC
		public GmGiftInfo InitGmGiftInfo(SqlDataReader dr)
		{
			return new GmGiftInfo
			{
				giftbagId = (string)dr["giftbagId"],
				activityId = (string)dr["activityId"],
				rewardMark = (int)dr["rewardMark"],
				giftbagOrder = (int)dr["giftbagOrder"]
			};
		}

		// Token: 0x060089ED RID: 35309 RVA: 0x002F0854 File Offset: 0x002EEA54
		public GmActivityInfo[] GetAllGmActivity()
		{
			List<GmActivityInfo> list = new List<GmActivityInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_GM_Activity_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitGmActivityInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllGmActivity", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089EE RID: 35310 RVA: 0x002F0910 File Offset: 0x002EEB10
		public GmActivityInfo InitGmActivityInfo(SqlDataReader dr)
		{
			return new GmActivityInfo
			{
				activityId = (string)dr["activityId"],
				activityName = (string)dr["activityName"],
				activityType = (int)dr["activityType"],
				activityChildType = (int)dr["activityChildType"],
				getWay = (int)dr["getWay"],
				desc = (string)dr["desc"],
				rewardDesc = (string)dr["rewardDesc"],
				beginTime = (DateTime)dr["beginTime"],
				beginShowTime = (DateTime)dr["beginShowTime"],
				endTime = (DateTime)dr["endTime"],
				endShowTime = (DateTime)dr["endShowTime"],
				icon = (int)dr["icon"],
				isContinue = (int)dr["isContinue"],
				status = (int)dr["status"],
				remain1 = (int)dr["remain1"],
				remain2 = (string)dr["remain2"]
			};
		}

		// Token: 0x060089EF RID: 35311 RVA: 0x002F0A9C File Offset: 0x002EEC9C
		public TS_Upgrade[] GetAllTemplateData()
		{
			List<TS_Upgrade> list = new List<TS_Upgrade>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_TSUpgradeInfo_All");
				while (sqlDataReader.Read())
				{
					list.Add(new TS_Upgrade
					{
						Types = (int)sqlDataReader["Types"],
						TemplateId = (int)sqlDataReader["TemplateId"],
						Grades = (int)sqlDataReader["Grades"],
						Data = (int)sqlDataReader["Data"],
						TemplateName = (string)sqlDataReader["TemplateName"],
						ActiveObj = (string)sqlDataReader["ActiveObj"],
						ItemTempId1 = (int)sqlDataReader["ItemTempId1"],
						Param1 = (int)sqlDataReader["Param1"],
						ItemTempId2 = (int)sqlDataReader["ItemTempId2"],
						Param2 = (int)sqlDataReader["Param2"],
						ItemTempId3 = (int)sqlDataReader["ItemTempId3"],
						Param3 = (int)sqlDataReader["Param3"],
						ItemTempId4 = (int)sqlDataReader["ItemTempId4"],
						Param4 = (int)sqlDataReader["Param4"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetTotemAll", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089F0 RID: 35312 RVA: 0x002F0CBC File Offset: 0x002EEEBC
		public TsEmblemInfo InitEmblem(SqlDataReader reader)
		{
			return new TsEmblemInfo
			{
				TemplateID = (int)reader["TemplateID"],
				TemplateName = (string)reader["TemplateName"],
				Types = (int)reader["Types"],
				Profile = (int)reader["Profile"],
				MainType = (int)reader["MainType"],
				MainValue = (string)reader["MainValue"],
				SubCount = (int)reader["SubCount"],
				SubValue = (string)reader["SubValue"],
				SkillValue = (string)reader["SkillValue"],
				SaleValue = (int)reader["SaleValue"],
				NeedItem1 = (int)reader["NeedItem1"],
				ItemCount1 = (int)reader["ItemCount1"],
				NeedItem2 = (int)reader["NeedItem2"],
				ItemCount2 = (int)reader["ItemCount2"],
				NeedItem3 = (int)reader["NeedItem3"],
				ItemCount3 = (int)reader["ItemCount3"],
				NeedItem4 = (int)reader["NeedItem4"],
				ItemCount4 = (int)reader["ItemCount4"]
			};
		}

		// Token: 0x060089F1 RID: 35313 RVA: 0x002F0E74 File Offset: 0x002EF074
		public TsScrollInfo InitScroll(SqlDataReader reader)
		{
			return new TsScrollInfo
			{
				TemplateID = (int)reader["TemplateID"],
				TemplateName = (string)reader["TemplateName"],
				Types = (int)reader["Types"],
				Profile = (int)reader["Profile"]
			};
		}

		// Token: 0x060089F2 RID: 35314 RVA: 0x002F0EEC File Offset: 0x002EF0EC
		public TsEmblemInfo[] GetAllEmblems()
		{
			List<TsEmblemInfo> list = new List<TsEmblemInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_TS_Emblem_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitEmblem(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089F3 RID: 35315 RVA: 0x002F0FA8 File Offset: 0x002EF1A8
		public TsScrollInfo[] GetAllScrolls()
		{
			List<TsScrollInfo> list = new List<TsScrollInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_TS_Scroll_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitScroll(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089F4 RID: 35316 RVA: 0x002F1064 File Offset: 0x002EF264
		public BoguAdventureRewardInfo InitBoguAdventureRewardInfo(SqlDataReader dr)
		{
			return new BoguAdventureRewardInfo
			{
				AwardID = (int)dr["AwardID"],
				TemplateID = (int)dr["TemplateID"],
				Count = (int)dr["Count"],
				ValidDate = (int)dr["ValidDate"],
				StrengthenLevel = (int)dr["StrengthenLevel"],
				AttackCompose = (int)dr["AttackCompose"],
				DefendCompose = (int)dr["DefendCompose"],
				AgilityCompose = (int)dr["AgilityCompose"],
				LuckCompose = (int)dr["LuckCompose"],
				IsBinds = (bool)dr["IsBinds"]
			};
		}

		// Token: 0x060089F5 RID: 35317 RVA: 0x002F1164 File Offset: 0x002EF364
		public BoguAdventureRewardInfo[] GetAllBoguAdventureReward()
		{
			List<BoguAdventureRewardInfo> list = new List<BoguAdventureRewardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_BoguAdventure_Reward_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitBoguAdventureRewardInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllBoguAdventureReward", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089F6 RID: 35318 RVA: 0x002F1220 File Offset: 0x002EF420
		public ClothPropertyTemplateInfo[] GetAllClothPropertyTemplateInfos()
		{
			List<ClothPropertyTemplateInfo> list = new List<ClothPropertyTemplateInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Cloth_Property_All");
				while (sqlDataReader.Read())
				{
					list.Add(new ClothPropertyTemplateInfo
					{
						ID = (int)sqlDataReader["ID"],
						Sex = (int)sqlDataReader["Sex"],
						Name = ((sqlDataReader["Name"] == null) ? "" : sqlDataReader["Name"].ToString()),
						Attack = (int)sqlDataReader["Attack"],
						Defend = (int)sqlDataReader["Defend"],
						Agility = (int)sqlDataReader["Agility"],
						Luck = (int)sqlDataReader["Luck"],
						Blood = (int)sqlDataReader["Blood"],
						Damage = (int)sqlDataReader["Damage"],
						Guard = (int)sqlDataReader["Guard"],
						Cost = (int)sqlDataReader["Cost"],
						Type = (int)sqlDataReader["Type"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllClothPropertyTemplateInfos", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089F7 RID: 35319 RVA: 0x002F1424 File Offset: 0x002EF624
		public ClothGroupTemplateInfo[] GetAllClothGroupTemplateInfos()
		{
			List<ClothGroupTemplateInfo> list = new List<ClothGroupTemplateInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Cloth_Group_All");
				while (sqlDataReader.Read())
				{
					list.Add(new ClothGroupTemplateInfo
					{
						ID = (int)sqlDataReader["ID"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						Sex = (int)sqlDataReader["Sex"],
						Description = (int)sqlDataReader["Description"],
						Cost = (int)sqlDataReader["Cost"],
						Type = (int)sqlDataReader["Type"],
						Type = (int)sqlDataReader["Type"],
						OtherTemplateID = ((sqlDataReader["OtherTemplateID"] == null) ? "" : sqlDataReader["OtherTemplateID"].ToString())
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllClothGroupTemplateInfos", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089F8 RID: 35320 RVA: 0x002F15CC File Offset: 0x002EF7CC
		public MysteryShopInfo[] GetAllMysteryShop()
		{
			List<MysteryShopInfo> list = new List<MysteryShopInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Mystery_Shop_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitMysteryShopInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("InitMysteryShopInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089F9 RID: 35321 RVA: 0x002F1688 File Offset: 0x002EF888
		public MysteryShopInfo InitMysteryShopInfo(SqlDataReader dr)
		{
			return new MysteryShopInfo
			{
				ID = (int)dr["ID"],
				LableType = (int)dr["LableType"],
				InfoID = (int)dr["InfoID"],
				Unit = (int)dr["Unit"],
				Num = (int)dr["Num"],
				Price = (int)dr["Price"],
				CanBuy = (int)dr["CanBuy"],
				Random = (int)dr["Random"],
				Quality = (int)dr["Quality"]
			};
		}

		// Token: 0x060089FA RID: 35322 RVA: 0x002F1770 File Offset: 0x002EF970
		public EliteGameAwardInfo[] GetAllEliteGameAward()
		{
			List<EliteGameAwardInfo> list = new List<EliteGameAwardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_EliteGame_Award_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitEliteGameAward(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllEliteGameAward", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089FB RID: 35323 RVA: 0x002F182C File Offset: 0x002EFA2C
		public EliteGameAwardInfo InitEliteGameAward(SqlDataReader reader)
		{
			return new EliteGameAwardInfo
			{
				ID = (int)reader["ID"],
				Result = (int)reader["Result"],
				Description = ((reader["Description"] == null) ? "" : reader["Description"].ToString()),
				TemplateID = (int)reader["TemplateID"],
				Count = (int)reader["Count"],
				Valid = (int)reader["Valid"],
				IsBind = (int)reader["IsBind"],
				StrengthenLevel = (int)reader["StrengthenLevel"],
				Agility = (int)reader["Agility"],
				Attack = (int)reader["Attack"],
				Defend = (int)reader["Defend"],
				Luck = (int)reader["Luck"],
				GoldValidate = (int)reader["GoldValidate"],
				Hole1 = (int)reader["Hole1"],
				Hole2 = (int)reader["Hole2"],
				Hole3 = (int)reader["Hole3"],
				Hole4 = (int)reader["Hole4"],
				Hole5 = (int)reader["Hole5"],
				Hole5Exp = (int)reader["Hole5Exp"],
				Hole5Level = (int)reader["Hole5Level"],
				Hole6 = (int)reader["Hole6"],
				Hole6Exp = (int)reader["Hole6Exp"],
				Hole6Level = (int)reader["Hole6Level"]
			};
		}

		// Token: 0x060089FC RID: 35324 RVA: 0x002F1A68 File Offset: 0x002EFC68
		public MoonLightBoxAwardInfo[] GetAllMoonLightAward()
		{
			List<MoonLightBoxAwardInfo> list = new List<MoonLightBoxAwardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_MoonLight_Award_All");
				while (sqlDataReader.Read())
				{
					list.Add(new MoonLightBoxAwardInfo
					{
						ItemId = (int)sqlDataReader["ItemId"],
						Name = (string)sqlDataReader["Name"],
						ValidDate = (int)sqlDataReader["ValidDate"],
						SortId = (int)sqlDataReader["SortId"],
						Number = (int)sqlDataReader["Number"],
						JoinLimit = (int)sqlDataReader["JoinLimit"],
						Probability = (string)sqlDataReader["Probability"],
						Weight = (int)sqlDataReader["Weight"],
						ConvertNum = (int)sqlDataReader["ConvertNum"],
						IsBinds = (int)sqlDataReader["IsBinds"],
						IsShow = (int)sqlDataReader["IsShow"],
						IsTips = (int)sqlDataReader["IsTips"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllMoonLightAward", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089FD RID: 35325 RVA: 0x002F1C58 File Offset: 0x002EFE58
		public EventRewardInfo[] GetAllEventRewardInfo()
		{
			List<EventRewardInfo> list = new List<EventRewardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Event_Reward_All");
				while (sqlDataReader.Read())
				{
					list.Add(new EventRewardInfo
					{
						ActivityType = (int)sqlDataReader["ActivityType"],
						SubActivityType = (int)sqlDataReader["SubActivityType"],
						Condition = (int)sqlDataReader["Condition"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllEventRewardInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089FE RID: 35326 RVA: 0x002F1D5C File Offset: 0x002EFF5C
		public EventRewardGoodsInfo[] GetAllEventRewardGoods()
		{
			List<EventRewardGoodsInfo> list = new List<EventRewardGoodsInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Event_RewardGoods_A11");
				while (sqlDataReader.Read())
				{
					list.Add(new EventRewardGoodsInfo
					{
						ActivityType = (int)sqlDataReader["ActivityType"],
						SubActivityType = (int)sqlDataReader["SubActivityType"],
						TemplateId = (int)sqlDataReader["TemplateId"],
						StrengthLevel = (int)sqlDataReader["StrengthLevel"],
						AttackCompose = (int)sqlDataReader["AttackCompose"],
						DefendCompose = (int)sqlDataReader["DefendCompose"],
						LuckCompose = (int)sqlDataReader["LuckCompose"],
						AgilityCompose = (int)sqlDataReader["AgilityCompose"],
						IsBind = (bool)sqlDataReader["IsBind"],
						ValidDate = (int)sqlDataReader["ValidDate"],
						Count = (int)sqlDataReader["Count"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllEventRewardGoods", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060089FF RID: 35327 RVA: 0x002F1F38 File Offset: 0x002F0138
		public TsSignInInfo[] GetAllEveryDaySignInAwardInfos()
		{
			List<TsSignInInfo> list = new List<TsSignInInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_EveryDaySignIn_All");
				while (sqlDataReader.Read())
				{
					list.Add(new TsSignInInfo
					{
						ID = (int)sqlDataReader["ID"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						Count = (int)sqlDataReader["Count"],
						ValidTime = (int)sqlDataReader["ValidTime"],
						IsBind = (int)sqlDataReader["IsBind"],
						Rate = (int)sqlDataReader["Rate"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ILog log = BaseBussiness.log;
					string text = "TS_SignInAward：";
					Exception ex2 = ex;
					log.Error(text + ((ex2 != null) ? ex2.ToString() : null));
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A00 RID: 35328 RVA: 0x002F20B0 File Offset: 0x002F02B0
		public ChargeAwardInfo[] GetAllChargeAwardInfos()
		{
			List<ChargeAwardInfo> list = new List<ChargeAwardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_ChargeAward_All");
				while (sqlDataReader.Read())
				{
					list.Add(new ChargeAwardInfo
					{
						ActivityType = (int)sqlDataReader["ActivityType"],
						Quality = (int)sqlDataReader["Quality"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						Probability = (int)sqlDataReader["Probability"],
						StrengthLevel = (int)sqlDataReader["StrengthLevel"],
						AttackCompose = (int)sqlDataReader["AttackCompose"],
						DefendCompose = (int)sqlDataReader["DefendCompose"],
						AgilityCompose = (int)sqlDataReader["AgilityCompose"],
						LuckCompose = (int)sqlDataReader["LuckCompose"],
						IsBind = (bool)sqlDataReader["IsBind"],
						ValidDate = (int)sqlDataReader["ValidDate"],
						Count = (int)sqlDataReader["Count"],
						Rate = (int)sqlDataReader["Rate"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ILog log = BaseBussiness.log;
					string text = "GetAllChargeAwardInfos：";
					Exception ex2 = ex;
					log.Error(text + ((ex2 != null) ? ex2.ToString() : null));
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A01 RID: 35329 RVA: 0x002F22C8 File Offset: 0x002F04C8
		public TsChangeItemInfo[] GetAllChargeChangeInfos()
		{
			List<TsChangeItemInfo> list = new List<TsChangeItemInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_ChargeChange_All");
				while (sqlDataReader.Read())
				{
					list.Add(new TsChangeItemInfo
					{
						ID = (int)sqlDataReader["ID"],
						TemplateID1 = (int)sqlDataReader["TemplateID1"],
						Count1 = (int)sqlDataReader["Count1"],
						TemplateID2 = (int)sqlDataReader["TemplateID2"],
						Count2 = (int)sqlDataReader["Count2"],
						GiveTemplateID = (int)sqlDataReader["GiveTemplateID"],
						GiveItemCount = (int)sqlDataReader["GiveItemCount"],
						GiveIsBind = (int)sqlDataReader["GiveIsBind"],
						GiveValidDate = (int)sqlDataReader["GiveValidDate"],
						ChangeCount = (int)sqlDataReader["ChangeCount"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ILog log = BaseBussiness.log;
					string text = "GetAllChargeChangeInfos：";
					Exception ex2 = ex;
					log.Error(text + ((ex2 != null) ? ex2.ToString() : null));
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A02 RID: 35330 RVA: 0x002F249C File Offset: 0x002F069C
		public DebrisPropertyConfig[] GetAllDebrisPropertyConfig()
		{
			List<DebrisPropertyConfig> list = new List<DebrisPropertyConfig>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_DebrisPropertyConfig_All");
				while (sqlDataReader.Read())
				{
					list.Add(new DebrisPropertyConfig
					{
						Id = (int)sqlDataReader["Id"],
						DebrisId = (int)sqlDataReader["DebrisId"],
						AttributeType = (int)sqlDataReader["AttributeType"],
						MinValue = (int)sqlDataReader["MinValue"],
						MaxValue = (int)sqlDataReader["MaxValue"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllDebrisPropertyConfig", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A03 RID: 35331 RVA: 0x002F25D4 File Offset: 0x002F07D4
		public EngraveDebrisInfo[] GetAllEngraveDebris()
		{
			List<EngraveDebrisInfo> list = new List<EngraveDebrisInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_EngraveDebris_All");
				while (sqlDataReader.Read())
				{
					list.Add(new EngraveDebrisInfo
					{
						Id = (int)sqlDataReader["Id"],
						Place = (int)sqlDataReader["Place"],
						Character = (int)sqlDataReader["Character"],
						SetID = (int)sqlDataReader["SetID"],
						AttributeAdd1 = (int)sqlDataReader["AttributeAdd1"],
						AttributeAdd2 = (int)sqlDataReader["AttributeAdd2"],
						AttributeAdd3 = (int)sqlDataReader["AttributeAdd3"],
						AttributeAdd4 = (int)sqlDataReader["AttributeAdd4"],
						AttributeAdd5 = (int)sqlDataReader["AttributeAdd5"],
						AttributeAdd6 = (int)sqlDataReader["AttributeAdd6"],
						AttributeAdd7 = (int)sqlDataReader["AttributeAdd7"],
						AttributeAdd8 = (int)sqlDataReader["AttributeAdd8"],
						AttributeAdd9 = (int)sqlDataReader["AttributeAdd9"],
						AttributeAdd10 = (int)sqlDataReader["AttributeAdd10"],
						AttributeAdd11 = (int)sqlDataReader["AttributeAdd11"],
						AttributeAdd12 = (int)sqlDataReader["AttributeAdd12"],
						AttributeAdd13 = (int)sqlDataReader["AttributeAdd13"],
						AttributeAdd14 = (int)sqlDataReader["AttributeAdd14"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllEngraveDebris", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A04 RID: 35332 RVA: 0x002F2850 File Offset: 0x002F0A50
		public EngraveFocusDebrisInfo[] EngraveFocusDebris()
		{
			List<EngraveFocusDebrisInfo> list = new List<EngraveFocusDebrisInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_EngraveFocusDebris_All");
				while (sqlDataReader.Read())
				{
					list.Add(new EngraveFocusDebrisInfo
					{
						ItemID = (int)sqlDataReader["ItemID"],
						FocusId = (int)sqlDataReader["FocusId"],
						Place = (int)sqlDataReader["Place"],
						Character = (int)sqlDataReader["Character"],
						Attribute1 = (int)sqlDataReader["Attribute1"],
						Attribute2 = (int)sqlDataReader["Attribute2"],
						Attribute3 = (int)sqlDataReader["Attribute3"],
						Attribute4 = (int)sqlDataReader["Attribute4"],
						Attribute5 = (int)sqlDataReader["Attribute5"],
						Attribute6 = (int)sqlDataReader["Attribute6"],
						Attribute7 = (int)sqlDataReader["Attribute7"],
						Attribute8 = (int)sqlDataReader["Attribute8"],
						Attribute9 = (int)sqlDataReader["Attribute9"],
						Attribute10 = (int)sqlDataReader["Attribute10"],
						Attribute11 = (int)sqlDataReader["Attribute11"],
						Attribute12 = (int)sqlDataReader["Attribute12"],
						Attribute13 = (int)sqlDataReader["Attribute13"],
						Attribute14 = (int)sqlDataReader["Attribute14"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("EngraveFocusDebris", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A05 RID: 35333 RVA: 0x002F2ACC File Offset: 0x002F0CCC
		public EngraveFocusElement[] GetAllEngraveFocusElement()
		{
			List<EngraveFocusElement> list = new List<EngraveFocusElement>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_EngraveFocusElement_All");
				while (sqlDataReader.Read())
				{
					list.Add(new EngraveFocusElement
					{
						FocusId = (int)sqlDataReader["FocusId"],
						Name = sqlDataReader["Name"].ToString(),
						Expain = sqlDataReader["Expain"].ToString(),
						NeedPlaces = sqlDataReader["NeedPlaces"].ToString(),
						NeedAllNumber = (int)sqlDataReader["NeedAllNumber"],
						NeedCharacterNum = sqlDataReader["NeedCharacterNum"].ToString(),
						AttributeValue = sqlDataReader["AttributeValue"].ToString(),
						AttributePercent = (int)sqlDataReader["AttributePercent"],
						HelpExplain = sqlDataReader["HelpExplain"].ToString()
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllEngraveFocusElement", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A06 RID: 35334 RVA: 0x002F2C78 File Offset: 0x002F0E78
		public EngraveRefineryConfigInfo[] GetAllEngraveRefineryConfig()
		{
			List<EngraveRefineryConfigInfo> list = new List<EngraveRefineryConfigInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_EngraveRefineryConfig_All");
				while (sqlDataReader.Read())
				{
					list.Add(new EngraveRefineryConfigInfo
					{
						Id = (int)sqlDataReader["Id"],
						Grade = (int)sqlDataReader["Grade"],
						Character = (int)sqlDataReader["Character"],
						Currency = (int)sqlDataReader["Currency"],
						Expend = (int)sqlDataReader["Expend"],
						Material = (int)sqlDataReader["Material"],
						NeedMaterial = (int)sqlDataReader["NeedMaterial"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllEngraveRefineryConfig", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A07 RID: 35335 RVA: 0x002F2DF8 File Offset: 0x002F0FF8
		public EngraveSetElementInfo[] GetAllEngraveSetElement()
		{
			List<EngraveSetElementInfo> list = new List<EngraveSetElementInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_EngraveSetElement_All");
				while (sqlDataReader.Read())
				{
					list.Add(new EngraveSetElementInfo
					{
						Id = (int)sqlDataReader["Id"],
						Name = sqlDataReader["Name"].ToString(),
						SetId = (int)sqlDataReader["SetId"],
						SkillId = (int)sqlDataReader["SkillId"],
						Explain = sqlDataReader["Explain"].ToString(),
						Demand = (int)sqlDataReader["Demand"],
						Attribute = sqlDataReader["Attribute"].ToString(),
						Quality = (int)sqlDataReader["Quality"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllEngraveSetElement", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A08 RID: 35336 RVA: 0x002F2F8C File Offset: 0x002F118C
		public EngraveSetInfo[] GetAllEngraveSet()
		{
			List<EngraveSetInfo> list = new List<EngraveSetInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_EngraveSet_All");
				while (sqlDataReader.Read())
				{
					list.Add(new EngraveSetInfo
					{
						SetId = (int)sqlDataReader["SetId"],
						Name = sqlDataReader["Name"].ToString(),
						HelpExplain = sqlDataReader["HelpExplain"].ToString()
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllEngraveSet", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A09 RID: 35337 RVA: 0x002F3090 File Offset: 0x002F1290
		public EngraveTemperConfigInfo[] GetAllEngraveTemperConfig()
		{
			List<EngraveTemperConfigInfo> list = new List<EngraveTemperConfigInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_EngraveTemperConfig_All");
				while (sqlDataReader.Read())
				{
					list.Add(new EngraveTemperConfigInfo
					{
						Level = (int)sqlDataReader["Level"],
						Character = (int)sqlDataReader["Character"],
						Currency = (int)sqlDataReader["Currency"],
						Expend = (int)sqlDataReader["Expend"],
						SuccessRate = (int)sqlDataReader["SuccessRate"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllEngraveTemperConfig", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A0A RID: 35338 RVA: 0x002F31C8 File Offset: 0x002F13C8
		public TSSigilProValueLimitTemp[] GetAllSigilProValueLimitTemp()
		{
			List<TSSigilProValueLimitTemp> list = new List<TSSigilProValueLimitTemp>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_SigilProValueLimitTemp_All");
				while (sqlDataReader.Read())
				{
					list.Add(new TSSigilProValueLimitTemp
					{
						Id = (int)sqlDataReader["Id"],
						Quality = (int)sqlDataReader["Quality"],
						ProType = (int)sqlDataReader["ProType"],
						Name = sqlDataReader["Name"].ToString(),
						RandomConfig = sqlDataReader["RandomConfig"].ToString()
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllSigilProValueLimitTemp", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A0B RID: 35339 RVA: 0x002F3300 File Offset: 0x002F1500
		public TSSigilSkillTemplate[] GetAllSigilSkillTemplate()
		{
			List<TSSigilSkillTemplate> list = new List<TSSigilSkillTemplate>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_SigilSkillTemplate_All");
				while (sqlDataReader.Read())
				{
					list.Add(new TSSigilSkillTemplate
					{
						SkillID = (int)sqlDataReader["SkillID"],
						SkillType = (int)sqlDataReader["SkillType"],
						SkillLv = (int)sqlDataReader["SkillLv"],
						SkillName = sqlDataReader["SkillName"].ToString(),
						IsNotice = (int)sqlDataReader["IsNotice"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllSigilSkillTemplate", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A0C RID: 35340 RVA: 0x002F3438 File Offset: 0x002F1638
		public TsActivityInfo[] GetAllActivityConfig()
		{
			List<TsActivityInfo> list = new List<TsActivityInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Activity_Config_All");
				while (sqlDataReader.Read())
				{
					list.Add(new TsActivityInfo
					{
						Num = (int)sqlDataReader["Num"],
						Name = (string)sqlDataReader["Name"],
						OpenSwitch = (int)sqlDataReader["OpenSwitch"],
						StartDate = (DateTime)sqlDataReader["StartDate"],
						EndDate = (DateTime)sqlDataReader["EndDate"],
						ShowDate = (DateTime)sqlDataReader["ShowDate"],
						Params1 = (string)sqlDataReader["Params1"],
						Params2 = (string)sqlDataReader["Params2"],
						Params3 = (string)sqlDataReader["Params3"],
						Params4 = (string)sqlDataReader["Params4"],
						Params5 = (string)sqlDataReader["Params5"],
						PrivateParams = (string)sqlDataReader["PrivateParams"],
						RankAreaAward = (string)sqlDataReader["RankAreaAward"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllActivityConfig", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A0D RID: 35341 RVA: 0x002F3640 File Offset: 0x002F1840
		public TSBoGuTurnInfo[] GetAllBogu()
		{
			List<TSBoGuTurnInfo> list = new List<TSBoGuTurnInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_BoguTurn_All");
				while (sqlDataReader.Read())
				{
					list.Add(new TSBoGuTurnInfo
					{
						Type = (int)sqlDataReader["Type"],
						Level = (int)sqlDataReader["Level"],
						ConvertCount = (int)sqlDataReader["ConvertCount"],
						ProtectAddCost = (int)sqlDataReader["ProtectAddCost"],
						CoinCostLimit = (int)sqlDataReader["CoinCostLimit"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllBogu", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A0E RID: 35342 RVA: 0x002F3778 File Offset: 0x002F1978
		public TSBoGuTurnAwardInfo[] GetAllBoguAward()
		{
			List<TSBoGuTurnAwardInfo> list = new List<TSBoGuTurnAwardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_BoguTurn_Award_All");
				while (sqlDataReader.Read())
				{
					list.Add(new TSBoGuTurnAwardInfo
					{
						ID = (int)sqlDataReader["ID"],
						Type = (int)sqlDataReader["Type"],
						Level = (int)sqlDataReader["Level"],
						Index = (int)sqlDataReader["Index"],
						ItemID = (int)sqlDataReader["ItemID"],
						Count = (int)sqlDataReader["Count"],
						IsBind = (bool)sqlDataReader["IsBind"],
						ValidDay = (int)sqlDataReader["ValidDay"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllBoguAward", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A0F RID: 35343 RVA: 0x002F390C File Offset: 0x002F1B0C
		public HomeTempPracticeInfo InitHomeTempPractice(SqlDataReader reader)
		{
			return new HomeTempPracticeInfo
			{
				Level = (int)reader["Level"],
				Exp = (int)reader["Exp"],
				Attack = (int)reader["Attack"],
				Defence = (int)reader["Defence"],
				Agility = (int)reader["Agility"],
				Luck = (int)reader["Luck"],
				Blood = (int)reader["Blood"],
				Damage = (int)reader["Damage"]
			};
		}

		// Token: 0x06008A10 RID: 35344 RVA: 0x002F39E0 File Offset: 0x002F1BE0
		public HomeTempPracticeInfo[] GetAllHomeTempPractice()
		{
			List<HomeTempPracticeInfo> list = new List<HomeTempPracticeInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Home_Temp_Practice_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitHomeTempPractice(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllHomeTempPractice", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A11 RID: 35345 RVA: 0x002F3A9C File Offset: 0x002F1C9C
		public HomeTempAdvanceInfo InitHomeTempAdvance(SqlDataReader reader)
		{
			return new HomeTempAdvanceInfo
			{
				Level = (int)reader["Level"],
				Item1 = (int)reader["Item1"],
				Count1 = (int)reader["Count1"],
				Item2 = (int)reader["Item2"],
				Count2 = (int)reader["Count2"],
				Attack = (int)reader["Attack"],
				Defence = (int)reader["Defence"],
				Agility = (int)reader["Agility"],
				Luck = (int)reader["Luck"],
				Blood = (int)reader["Blood"],
				Guard = (int)reader["Guard"],
				Name = (string)reader["Name"]
			};
		}

		// Token: 0x06008A12 RID: 35346 RVA: 0x002F3BCC File Offset: 0x002F1DCC
		public HomeTempAdvanceInfo[] GetAllHomeTempAdvance()
		{
			List<HomeTempAdvanceInfo> list = new List<HomeTempAdvanceInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Home_Temp_Advance_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitHomeTempAdvance(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllHomeTempAdvance", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A13 RID: 35347 RVA: 0x002F3C88 File Offset: 0x002F1E88
		public NecklaceStrengInfo InitNecklaceStreng(SqlDataReader reader)
		{
			return new NecklaceStrengInfo
			{
				Level = (int)reader["Level"],
				Exp = (int)reader["Exp"],
				NecklaceStrengthExp = (int)reader["NecklaceStrengthExp"],
				NecklaceStrengthPlus = (int)reader["NecklaceStrengthPlus"]
			};
		}

		// Token: 0x06008A14 RID: 35348 RVA: 0x002F3D00 File Offset: 0x002F1F00
		public NecklaceStrengInfo[] GetAllNecklaceStreng()
		{
			List<NecklaceStrengInfo> list = new List<NecklaceStrengInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Necklace_Streng_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitNecklaceStreng(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllNecklaceStreng", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A15 RID: 35349 RVA: 0x002F3DBC File Offset: 0x002F1FBC
		public NecklaceCastingInfo InitNecklaceCasting(SqlDataReader reader)
		{
			return new NecklaceCastingInfo
			{
				Level = (int)reader["Level"],
				NeedItemCount1 = (int)reader["NeedItemCount1"],
				NeedItemCount2 = (int)reader["NeedItemCount2"],
				Attack = (int)reader["Attack"],
				Defence = (int)reader["Defence"],
				Agility = (int)reader["Agility"],
				Luck = (int)reader["Luck"],
				Blood = (int)reader["Blood"]
			};
		}

		// Token: 0x06008A16 RID: 35350 RVA: 0x002F3E90 File Offset: 0x002F2090
		public NecklaceCastingInfo[] GetAllNecklaceCasting()
		{
			List<NecklaceCastingInfo> list = new List<NecklaceCastingInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Necklace_Casting_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitNecklaceCasting(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllNecklaceCasting", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A17 RID: 35351 RVA: 0x002F3F4C File Offset: 0x002F214C
		public AmuletItemInfo InitAmuletItem(SqlDataReader reader)
		{
			return new AmuletItemInfo
			{
				Level = (int)reader["Level"],
				HP = (int)reader["HP"],
				GuaranteeTimes = (int)reader["GuaranteeTimes"],
				LvlupStep = (int)reader["LvlupStep"],
				WashsStep = (int)reader["WashsStep"],
				SuccessChance = (int)reader["SuccessChance"]
			};
		}

		// Token: 0x06008A18 RID: 35352 RVA: 0x002F3FF0 File Offset: 0x002F21F0
		public AmuletGradeItemInfo InitAmuletGradeItem(SqlDataReader reader)
		{
			return new AmuletGradeItemInfo
			{
				WahsLevel = (int)reader["WahsLevel"],
				WahsTimes = (int)reader["WahsTimes"],
				Minvalue = (int)reader["Minvalue"],
				Maxvalue = (int)reader["Maxvalue"]
			};
		}

		// Token: 0x06008A19 RID: 35353 RVA: 0x002F4068 File Offset: 0x002F2268
		public AmuletPhaseItemInfo InitAmuletPhaseItem(SqlDataReader reader)
		{
			return new AmuletPhaseItemInfo
			{
				AmuletLevel = (int)reader["AmuletLevel"],
				Attack = (int)reader["Attack"],
				Defence = (int)reader["Defence"],
				Agility = (int)reader["Agility"],
				Luck = (int)reader["Luck"],
				Damage = (int)reader["Damage"],
				Guard = (int)reader["Guard"],
				Expend = (int)reader["Expend"],
				LockPrice = (int)reader["LockPrice"],
				Phase = (int)reader["Phase"]
			};
		}

		// Token: 0x06008A1A RID: 35354 RVA: 0x002F4168 File Offset: 0x002F2368
		public AmuletItemInfo[] GetAllAmuletItem()
		{
			List<AmuletItemInfo> list = new List<AmuletItemInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Amulet_Info_Item_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitAmuletItem(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllAmuletItem", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A1B RID: 35355 RVA: 0x002F4224 File Offset: 0x002F2424
		public AmuletGradeItemInfo[] GetAllAmuletGradeItem()
		{
			List<AmuletGradeItemInfo> list = new List<AmuletGradeItemInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Amulet_Grade_Item_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitAmuletGradeItem(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllAmuletGradeItem", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A1C RID: 35356 RVA: 0x002F42E0 File Offset: 0x002F24E0
		public AmuletPhaseItemInfo[] GetAllAmuletPhaseItem()
		{
			List<AmuletPhaseItemInfo> list = new List<AmuletPhaseItemInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Amulet_Phase_Item_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitAmuletPhaseItem(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllAmuletPhaseItem", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A1D RID: 35357 RVA: 0x002F439C File Offset: 0x002F259C
		public SpiritInfo[] GetAllSpirit()
		{
			List<SpiritInfo> list = new List<SpiritInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Spirit_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitSpirit(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllSetsBuildTemp", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A1E RID: 35358 RVA: 0x002F4458 File Offset: 0x002F2658
		public SpiritInfo InitSpirit(SqlDataReader reader)
		{
			return new SpiritInfo
			{
				Level = (int)reader["Level"],
				MustGetTimes = (int)reader["MustGetTimes"],
				BaseSuccessPro = (int)reader["BaseSuccessPro"],
				RefrenceValue = (int)reader["RefrenceValue"],
				SkillId = (int)reader["SkillId"],
				AttackAdd = (int)reader["AttackAdd"],
				LuckAdd = (int)reader["LuckAdd"],
				DefendAdd = (int)reader["DefendAdd"],
				AgilityAdd = (int)reader["AgilityAdd"],
				BagType = (int)reader["BagType"],
				BagPlace = (int)reader["BagPlace"],
				CategoryId = (int)reader["CategoryId"]
			};
		}

		// Token: 0x06008A1F RID: 35359 RVA: 0x002F4588 File Offset: 0x002F2788
		public SetsBuildTempInfo[] GetAllSetsBuildTemp()
		{
			List<SetsBuildTempInfo> list = new List<SetsBuildTempInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Sets_Build_Temp_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitSetsBuildTemp(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllSetsBuildTemp", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A20 RID: 35360 RVA: 0x002F4644 File Offset: 0x002F2844
		public SetsBuildTempInfo InitSetsBuildTemp(SqlDataReader reader)
		{
			return new SetsBuildTempInfo
			{
				Level = (int)reader["Level"],
				SetsType = (int)reader["SetsType"],
				UseItemTemplate = (int)reader["UseItemTemplate"],
				Exp = (int)reader["Exp"],
				AttackGrow = (int)reader["AttackGrow"],
				DefenceGrow = (int)reader["DefenceGrow"],
				AgilityGrow = (int)reader["AgilityGrow"],
				LuckGrow = (int)reader["LuckGrow"],
				DamageGrow = (int)reader["DamageGrow"],
				GuardGrow = (int)reader["GuardGrow"]
			};
		}

		// Token: 0x06008A21 RID: 35361 RVA: 0x002F4744 File Offset: 0x002F2944
		public DevilTreasItemInfo[] GetAllDevilTreasItems()
		{
			List<DevilTreasItemInfo> list = new List<DevilTreasItemInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_DevilTreasItem_All");
				while (sqlDataReader.Read())
				{
					list.Add(new DevilTreasItemInfo
					{
						ID = (int)sqlDataReader["ID"],
						Type = (int)sqlDataReader["Type"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						Value = (int)sqlDataReader["Value"],
						Random = (int)sqlDataReader["Random"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ILog log = BaseBussiness.log;
					string text = "GetAllDevilTreasItems：";
					Exception ex2 = ex;
					log.Error(text + ((ex2 != null) ? ex2.ToString() : null));
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A22 RID: 35362 RVA: 0x002F488C File Offset: 0x002F2A8C
		public DevilTreasPointsInfo[] GetAllDevilTreasPoints()
		{
			List<DevilTreasPointsInfo> list = new List<DevilTreasPointsInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_DevilTreasPoint_All");
				while (sqlDataReader.Read())
				{
					list.Add(new DevilTreasPointsInfo
					{
						ID = (int)sqlDataReader["ID"],
						Points = (int)sqlDataReader["Points"],
						TemplateID = (int)sqlDataReader["TemplateID"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ILog log = BaseBussiness.log;
					string text = "GetAllDevilTreasPoints：";
					Exception ex2 = ex;
					log.Error(text + ((ex2 != null) ? ex2.ToString() : null));
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A23 RID: 35363 RVA: 0x002F49A0 File Offset: 0x002F2BA0
		public DevilTreasRankRewardInfo[] GetAllDevilTreasRankRewards()
		{
			List<DevilTreasRankRewardInfo> list = new List<DevilTreasRankRewardInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_DevilTreasRankReward_All");
				while (sqlDataReader.Read())
				{
					list.Add(new DevilTreasRankRewardInfo
					{
						ID = (int)sqlDataReader["ID"],
						RankMin = (int)sqlDataReader["RankMin"],
						RankMax = (int)sqlDataReader["RankMax"],
						Desc = (string)sqlDataReader["Desc"],
						Percent = (int)sqlDataReader["Percent"],
						Reward = (int)sqlDataReader["Reward"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ILog log = BaseBussiness.log;
					string text = "GetAllDevilTreasRankRewards：";
					Exception ex2 = ex;
					log.Error(text + ((ex2 != null) ? ex2.ToString() : null));
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A24 RID: 35364 RVA: 0x002F4B18 File Offset: 0x002F2D18
		public DevilTreasSarahToBoxInfo[] GetAllDevilTreasSarahToBoxs()
		{
			List<DevilTreasSarahToBoxInfo> list = new List<DevilTreasSarahToBoxInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_DevilTreasSarahToBox_All");
				while (sqlDataReader.Read())
				{
					list.Add(new DevilTreasSarahToBoxInfo
					{
						ID = (int)sqlDataReader["ID"],
						Exchange = (string)sqlDataReader["Exchange"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ILog log = BaseBussiness.log;
					string text = "GetAllDevilTreasSarahToBoxs：";
					Exception ex2 = ex;
					log.Error(text + ((ex2 != null) ? ex2.ToString() : null));
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A25 RID: 35365 RVA: 0x002F4C18 File Offset: 0x002F2E18
		public DevilTreasOpenBoxInfo[] GetAllDevilTreasOpenBoxs()
		{
			List<DevilTreasOpenBoxInfo> list = new List<DevilTreasOpenBoxInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_DevilTreasOpenBox_All");
				while (sqlDataReader.Read())
				{
					list.Add(new DevilTreasOpenBoxInfo
					{
						ID = (int)sqlDataReader["ID"],
						BoxId = (int)sqlDataReader["BoxId"],
						BoxIndex = (int)sqlDataReader["BoxIndex"],
						NeedMoney = (int)sqlDataReader["NeedMoney"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ILog log = BaseBussiness.log;
					string text = "GetAllDevilTreasOpenBoxs：";
					Exception ex2 = ex;
					log.Error(text + ((ex2 != null) ? ex2.ToString() : null));
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A26 RID: 35366 RVA: 0x002F4D44 File Offset: 0x002F2F44
		public MagicItemTemplateInfo InitMagicItemTemplateInfo(SqlDataReader dr)
		{
			return new MagicItemTemplateInfo
			{
				Lv = (int)dr["Lv"],
				Exp = (int)dr["Exp"],
				Damage = (int)dr["Damage"],
				Guard = (int)dr["Guard"]
			};
		}

		// Token: 0x06008A27 RID: 35367 RVA: 0x002F4DBC File Offset: 0x002F2FBC
		public TS_WarPass_QuestTemplate[] GetAllWarPassQuestTemplate()
		{
			List<TS_WarPass_QuestTemplate> list = new List<TS_WarPass_QuestTemplate>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_TS_WarPass_QuestTemplate");
				while (sqlDataReader.Read())
				{
					TS_WarPass_QuestTemplate ts_WarPass_QuestTemplate = new TS_WarPass_QuestTemplate
					{
						QID = (int)sqlDataReader["QID"],
						SType = (int)sqlDataReader["SType"],
						QType = (int)sqlDataReader["QType"],
						Condition1 = (int)sqlDataReader["Condition1"],
						Condition2 = (int)sqlDataReader["Condition2"],
						Desc = (string)sqlDataReader["Desc"],
						AddGp = (int)sqlDataReader["AddGp"],
						FinishPrice = (int)sqlDataReader["FinishPrice"],
						HardLevel = (int)sqlDataReader["HardLevel"]
					};
					list.Add(ts_WarPass_QuestTemplate);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllWarPassQuestTemplate", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A28 RID: 35368 RVA: 0x002F4F68 File Offset: 0x002F3168
		public MagicItemTemplateInfo[] GetAllMagicItemTemplate()
		{
			List<MagicItemTemplateInfo> list = new List<MagicItemTemplateInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Magic_Item_Template_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitMagicItemTemplateInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("InitMagicItemTemplateInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A29 RID: 35369 RVA: 0x002F5024 File Offset: 0x002F3224
		public TS_PveMissTicket[] GetAllPveRequire()
		{
			List<TS_PveMissTicket> list = new List<TS_PveMissTicket>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_MissTicket_All");
				while (sqlDataReader.Read())
				{
					TS_PveMissTicket ts_PveMissTicket = new TS_PveMissTicket
					{
						PveID = (int)sqlDataReader["PveID"],
						TicketID = (int)sqlDataReader["TicketID"],
						TicketCount = (int)sqlDataReader["TicketCount"],
						Attack = (int)sqlDataReader["Attack"],
						Defence = (int)sqlDataReader["Defence"],
						Agility = (int)sqlDataReader["Agility"],
						Luck = (int)sqlDataReader["Luck"],
						MinFightPower = (int)sqlDataReader["MinFightPower"],
						MaxFightPower = (int)sqlDataReader["MaxFightPower"],
						IsAcadeMy = (int)sqlDataReader["IsAcadeMy"],
						IsCouples = (int)sqlDataReader["IsCouples"],
						IsGuild = (int)sqlDataReader["IsGuild"],
						MinPeople = (int)sqlDataReader["MinPeople"],
						MaxPeople = (int)sqlDataReader["MaxPeople"],
						Equilibrium = (bool)sqlDataReader["Equilibrium"],
						IsSelect = (bool)sqlDataReader["IsSelect"]
					};
					list.Add(ts_PveMissTicket);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllMissionRequire", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008A2A RID: 35370 RVA: 0x002F5270 File Offset: 0x002F3470
		public FightSpiritTemplateInfo[] GetAllFightSpiritTemplate()
		{
			List<FightSpiritTemplateInfo> list = new List<FightSpiritTemplateInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_FightSpiritTemplate_All");
				while (sqlDataReader.Read())
				{
					list.Add(new FightSpiritTemplateInfo
					{
						ID = (int)sqlDataReader["ID"],
						FightSpiritID = (int)sqlDataReader["FightSpiritID"],
						FightSpiritIcon = (string)sqlDataReader["FightSpiritIcon"],
						Level = (int)sqlDataReader["Level"],
						Exp = (int)sqlDataReader["Exp"],
						Attack = (int)sqlDataReader["Attack"],
						Defence = (int)sqlDataReader["Defence"],
						Agility = (int)sqlDataReader["Agility"],
						Lucky = (int)sqlDataReader["Lucky"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetFightSpiritTemplateAll", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}
	}
}
