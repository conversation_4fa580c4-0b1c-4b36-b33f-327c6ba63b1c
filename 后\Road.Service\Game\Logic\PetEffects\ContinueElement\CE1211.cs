﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E9E RID: 3742
	public class CE1211 : BasePetEffect
	{
		// Token: 0x06008156 RID: 33110 RVA: 0x002AC880 File Offset: 0x002AAA80
		public CE1211(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1211, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008157 RID: 33111 RVA: 0x002AC900 File Offset: 0x002AAB00
		public override bool Start(Living living)
		{
			CE1211 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1211) as CE1211;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008158 RID: 33112 RVA: 0x00032A85 File Offset: 0x00030C85
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008159 RID: 33113 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600815A RID: 33114 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginNextTurn(Living living)
		{
		}

		// Token: 0x0600815B RID: 33115 RVA: 0x002AC960 File Offset: 0x002AAB60
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			this.m_added = 1000;
			living.AddBlood(-this.m_added, 1);
			bool flag2 = living.Blood <= 0;
			if (flag2)
			{
				living.Die();
				bool flag3 = living.Game.CurrentLiving != null && living.Game.CurrentLiving is Player;
				if (flag3)
				{
					(living.Game.CurrentLiving as Player).PlayerDetail.OnKillingLiving(living.Game, 2, living.Id, living.IsLiving, this.m_added);
				}
			}
		}

		// Token: 0x0600815C RID: 33116 RVA: 0x00032AC1 File Offset: 0x00030CC1
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x0400508F RID: 20623
		private int m_type = 0;

		// Token: 0x04005090 RID: 20624
		private int m_count = 0;

		// Token: 0x04005091 RID: 20625
		private int m_probability = 0;

		// Token: 0x04005092 RID: 20626
		private int m_delay = 0;

		// Token: 0x04005093 RID: 20627
		private int m_coldDown = 0;

		// Token: 0x04005094 RID: 20628
		private int m_currentId;

		// Token: 0x04005095 RID: 20629
		private int m_added = 0;
	}
}
