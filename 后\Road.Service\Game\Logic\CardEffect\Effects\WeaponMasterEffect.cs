﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F3B RID: 3899
	public class WeaponMasterEffect : BaseCardEffect
	{
		// Token: 0x0600848E RID: 33934 RVA: 0x002B89F0 File Offset: 0x002B6BF0
		public WeaponMasterEffect(int index, CardBuffInfo info)
			: base(eCardEffectType.WeaponMasterDeck, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x0600848F RID: 33935 RVA: 0x002B8A60 File Offset: 0x002B6C60
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.WeaponMasterDeck) is WeaponMasterEffect;
			return flag || base.Start(living);
		}

		// Token: 0x06008490 RID: 33936 RVA: 0x00034FEC File Offset: 0x000331EC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x06008491 RID: 33937 RVA: 0x00035002 File Offset: 0x00033202
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x06008492 RID: 33938 RVA: 0x002B8A98 File Offset: 0x002B6C98
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.Attack -= (double)this.m_added;
				player.Agility -= (double)this.m_added;
				player.Lucky -= (double)this.m_added;
				player.Defence -= (double)this.m_added;
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVPGame && (player.Game as PVPGame).IsMatchOrFreedom();
			if (flag2)
			{
				this.m_added = this.m_value;
				player.Attack += (double)this.m_added;
				player.Agility += (double)this.m_added;
				player.Lucky += (double)this.m_added;
				player.Defence += (double)this.m_added;
			}
		}

		// Token: 0x040052A5 RID: 21157
		private int m_indexValue = 0;

		// Token: 0x040052A6 RID: 21158
		private int m_value = 0;

		// Token: 0x040052A7 RID: 21159
		private int m_added = 0;
	}
}
