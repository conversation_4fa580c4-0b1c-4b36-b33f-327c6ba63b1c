﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F33 RID: 3891
	public class RunRunChicken2Effect : BaseCardEffect
	{
		// Token: 0x06008463 RID: 33891 RVA: 0x002B7EB8 File Offset: 0x002B60B8
		public RunRunChicken2Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.RunRunChicken2, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008464 RID: 33892 RVA: 0x002B7F30 File Offset: 0x002B6130
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.RunRunChicken2) is RunRunChicken2Effect;
			return flag || base.Start(living);
		}

		// Token: 0x06008465 RID: 33893 RVA: 0x00034E56 File Offset: 0x00033056
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x06008466 RID: 33894 RVA: 0x00034E6C File Offset: 0x0003306C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x06008467 RID: 33895 RVA: 0x002B7F68 File Offset: 0x002B6168
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0.0;
			if (flag)
			{
				player.Defence -= this.m_added;
				this.m_added = 0.0;
			}
			bool flag2 = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 7;
			if (flag2)
			{
				this.m_added = player.Defence * (double)this.m_value / 100.0;
				player.Defence += this.m_added;
				player.Game.SendMessage(player.PlayerDetail, "您激活了小鸡快跑2件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活小鸡快跑2件套卡.", 3);
			}
		}

		// Token: 0x0400528D RID: 21133
		private int m_indexValue = 0;

		// Token: 0x0400528E RID: 21134
		private int m_value = 0;

		// Token: 0x0400528F RID: 21135
		private double m_added = 0.0;
	}
}
