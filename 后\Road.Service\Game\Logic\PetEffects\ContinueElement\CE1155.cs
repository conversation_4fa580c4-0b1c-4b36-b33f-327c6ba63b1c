﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E78 RID: 3704
	public class CE1155 : BasePetEffect
	{
		// Token: 0x06008061 RID: 32865 RVA: 0x002A8E1C File Offset: 0x002A701C
		public CE1155(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1155, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008062 RID: 32866 RVA: 0x002A8E9C File Offset: 0x002A709C
		public override bool Start(Living living)
		{
			CE1155 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1155) as CE1155;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008063 RID: 32867 RVA: 0x002A8EFC File Offset: 0x002A70FC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 70;
				bool flag2 = player.BaseGuard < (double)this.m_added;
				if (flag2)
				{
					this.m_added = (int)(player.BaseGuard - 1.0);
				}
				player.BaseGuard -= (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008064 RID: 32868 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008065 RID: 32869 RVA: 0x002A8F88 File Offset: 0x002A7188
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008066 RID: 32870 RVA: 0x00032180 File Offset: 0x00030380
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BaseGuard += (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F81 RID: 20353
		private int m_type = 0;

		// Token: 0x04004F82 RID: 20354
		private int m_count = 0;

		// Token: 0x04004F83 RID: 20355
		private int m_probability = 0;

		// Token: 0x04004F84 RID: 20356
		private int m_delay = 0;

		// Token: 0x04004F85 RID: 20357
		private int m_coldDown = 0;

		// Token: 0x04004F86 RID: 20358
		private int m_currentId;

		// Token: 0x04004F87 RID: 20359
		private int m_added = 0;
	}
}
