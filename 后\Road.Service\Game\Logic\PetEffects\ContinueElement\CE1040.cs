﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E53 RID: 3667
	public class CE1040 : BasePetEffect
	{
		// Token: 0x06007F80 RID: 32640 RVA: 0x002A5504 File Offset: 0x002A3704
		public CE1040(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1040, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F81 RID: 32641 RVA: 0x002A5584 File Offset: 0x002A3784
		public override bool Start(Living living)
		{
			CE1040 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1040) as CE1040;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F82 RID: 32642 RVA: 0x002A55E4 File Offset: 0x002A37E4
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.Lucky += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F83 RID: 32643 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F84 RID: 32644 RVA: 0x002A5644 File Offset: 0x002A3844
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F85 RID: 32645 RVA: 0x002A5678 File Offset: 0x002A3878
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Lucky -= (double)this.m_added;
			this.m_added = 0;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E80 RID: 20096
		private int m_type = 0;

		// Token: 0x04004E81 RID: 20097
		private int m_count = 0;

		// Token: 0x04004E82 RID: 20098
		private int m_probability = 0;

		// Token: 0x04004E83 RID: 20099
		private int m_delay = 0;

		// Token: 0x04004E84 RID: 20100
		private int m_coldDown = 0;

		// Token: 0x04004E85 RID: 20101
		private int m_currentId;

		// Token: 0x04004E86 RID: 20102
		private int m_added = 0;
	}
}
