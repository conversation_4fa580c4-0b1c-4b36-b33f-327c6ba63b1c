﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F61 RID: 3937
	public class LivingShootAction : BaseAction
	{
		// Token: 0x0600851C RID: 34076 RVA: 0x002BA2B4 File Offset: 0x002B84B4
		public LivingShootAction(Living living, int bombId, int x, int y, int force, int angle, int bombCount, int minTime, int maxTime, float time, int delay, LivingCallBack callBack)
			: base(delay, 1000)
		{
			this.m_living = living;
			this.m_tx = x;
			this.m_ty = y;
			this.m_force = force;
			this.m_angle = angle;
			this.m_bombCount = bombCount;
			this.m_bombId = bombId;
			this.m_minTime = minTime;
			this.m_maxTime = maxTime;
			this.m_Time = time;
			this.m_callBack = callBack;
		}

		// Token: 0x0600851D RID: 34077 RVA: 0x002BA328 File Offset: 0x002B8528
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			bool flag = this.m_living is SimpleBoss || this.m_living is SimpleNpc;
			if (flag)
			{
				this.m_living.GetShootForceAndAngle(ref this.m_tx, ref this.m_ty, this.m_bombId, this.m_minTime, this.m_maxTime, this.m_bombCount, this.m_Time, ref this.m_force, ref this.m_angle);
			}
			this.m_living.ShootImp(this.m_bombId, this.m_tx, this.m_ty, this.m_force, this.m_angle, this.m_bombCount, 0);
			bool flag2 = this.m_callBack != null;
			if (flag2)
			{
				this.m_living.CallFuction(this.m_callBack, this.m_living.LastLifeTimeShoot);
			}
			base.Finish(tick);
		}

		// Token: 0x04005323 RID: 21283
		private Living m_living;

		// Token: 0x04005324 RID: 21284
		private int m_tx;

		// Token: 0x04005325 RID: 21285
		private int m_ty;

		// Token: 0x04005326 RID: 21286
		private int m_bombId;

		// Token: 0x04005327 RID: 21287
		private int m_force;

		// Token: 0x04005328 RID: 21288
		private int m_angle;

		// Token: 0x04005329 RID: 21289
		private int m_bombCount;

		// Token: 0x0400532A RID: 21290
		private int m_minTime;

		// Token: 0x0400532B RID: 21291
		private int m_maxTime;

		// Token: 0x0400532C RID: 21292
		private float m_Time;

		// Token: 0x0400532D RID: 21293
		private LivingCallBack m_callBack;
	}
}
