﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EF4 RID: 3828
	public class RecoverBloodEffect : BasePlayerEffect
	{
		// Token: 0x06008362 RID: 33634 RVA: 0x0003447C File Offset: 0x0003267C
		public RecoverBloodEffect(int count, int probability)
			: base(eEffectType.RecoverBloodEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x06008363 RID: 33635 RVA: 0x002B3930 File Offset: 0x002B1B30
		public override bool Start(Living living)
		{
			RecoverBloodEffect recoverBloodEffect = living.EffectList.GetOfType(eEffectType.RecoverBloodEffect) as RecoverBloodEffect;
			bool flag = recoverBloodEffect != null;
			bool flag2;
			if (flag)
			{
				this.m_probability = ((this.m_probability > recoverBloodEffect.m_probability) ? this.m_probability : recoverBloodEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008364 RID: 33636 RVA: 0x000344A4 File Offset: 0x000326A4
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKilledByLiving += this.ChangeProperty;
		}

		// Token: 0x06008365 RID: 33637 RVA: 0x000344BA File Offset: 0x000326BA
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.ChangeProperty;
		}

		// Token: 0x06008366 RID: 33638 RVA: 0x002B398C File Offset: 0x002B1B8C
		public void ChangeProperty(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isLiving = living.IsLiving;
			if (isLiving)
			{
				this.IsTrigger = false;
				bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
				if (flag)
				{
					this.IsTrigger = true;
					living.DefenceEffectTrigger = true;
					living.SyncAtTime = true;
					living.AddBlood(this.m_count);
					living.SyncAtTime = false;
					living.Game.AddAction(new LivingSayAction(living, LanguageMgr.GetTranslation("RecoverBloodEffect.msg", Array.Empty<object>()), 9, 0, 1000));
				}
			}
		}

		// Token: 0x04005212 RID: 21010
		private int m_count = 0;

		// Token: 0x04005213 RID: 21011
		private int m_probability = 0;
	}
}
