﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F0E RID: 3854
	[GameCommand(144, "使用宠物技能")]
	public class PetKillCommand : ICommandHandler
	{
		// Token: 0x060083B5 RID: 33717 RVA: 0x002B5490 File Offset: 0x002B3690
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool flag = game.GameState == eGameState.Playing && !player.GetSealState();
			if (flag)
			{
				int num = packet.ReadInt();
				player.PetUseKill(num);
			}
		}
	}
}
