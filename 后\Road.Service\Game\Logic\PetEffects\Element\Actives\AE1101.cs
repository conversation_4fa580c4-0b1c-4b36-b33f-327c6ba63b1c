﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DC9 RID: 3529
	public class AE1101 : BasePetEffect
	{
		// Token: 0x06007C90 RID: 31888 RVA: 0x00298224 File Offset: 0x00296424
		public AE1101(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1101, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C91 RID: 31889 RVA: 0x002982A4 File Offset: 0x002964A4
		public override bool Start(Living living)
		{
			AE1101 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1101) as AE1101;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C92 RID: 31890 RVA: 0x0002FBB8 File Offset: 0x0002DDB8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C93 RID: 31891 RVA: 0x0002FBCE File Offset: 0x0002DDCE
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C94 RID: 31892 RVA: 0x00298304 File Offset: 0x00296504
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.AddPetEffect(new CE1101(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004AB9 RID: 19129
		private int m_type = 0;

		// Token: 0x04004ABA RID: 19130
		private int m_count = 0;

		// Token: 0x04004ABB RID: 19131
		private int m_probability = 0;

		// Token: 0x04004ABC RID: 19132
		private int m_delay = 0;

		// Token: 0x04004ABD RID: 19133
		private int m_coldDown = 0;

		// Token: 0x04004ABE RID: 19134
		private int m_currentId;

		// Token: 0x04004ABF RID: 19135
		private int m_added = 0;
	}
}
