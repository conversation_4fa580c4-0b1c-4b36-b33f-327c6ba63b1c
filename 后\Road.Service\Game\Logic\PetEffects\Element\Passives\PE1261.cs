﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D7C RID: 3452
	public class PE1261 : BasePetEffect
	{
		// Token: 0x06007B01 RID: 31489 RVA: 0x00291374 File Offset: 0x0028F574
		public PE1261(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1261, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B02 RID: 31490 RVA: 0x002913F4 File Offset: 0x0028F5F4
		public override bool Start(Living living)
		{
			PE1261 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1261) as PE1261;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B03 RID: 31491 RVA: 0x0002EC40 File Offset: 0x0002CE40
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.player_beginNextTurn;
		}

		// Token: 0x06007B04 RID: 31492 RVA: 0x0002EC56 File Offset: 0x0002CE56
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.player_beginNextTurn;
		}

		// Token: 0x06007B05 RID: 31493 RVA: 0x00291454 File Offset: 0x0028F654
		public void player_beginNextTurn(Living living)
		{
			bool flag = living.PetEffects.BonusLucky == 0;
			if (flag)
			{
				this.m_added = 300;
				living.Lucky += (double)this.m_added;
				living.PetEffects.BonusLucky += this.m_added;
				living.Game.SendPetBuff(living, base.Info, true);
			}
		}

		// Token: 0x040048A4 RID: 18596
		private int m_type = 0;

		// Token: 0x040048A5 RID: 18597
		private int m_count = 0;

		// Token: 0x040048A6 RID: 18598
		private int m_probability = 0;

		// Token: 0x040048A7 RID: 18599
		private int m_delay = 0;

		// Token: 0x040048A8 RID: 18600
		private int m_coldDown = 0;

		// Token: 0x040048A9 RID: 18601
		private int m_currentId;

		// Token: 0x040048AA RID: 18602
		private int m_added = 0;
	}
}
