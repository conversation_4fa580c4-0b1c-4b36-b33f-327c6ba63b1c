﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E35 RID: 3637
	public class AE3172 : BasePetEffect
	{
		// Token: 0x06007ED0 RID: 32464 RVA: 0x002A27C8 File Offset: 0x002A09C8
		public AE3172(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE3172, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007ED1 RID: 32465 RVA: 0x002A2844 File Offset: 0x002A0A44
		public override bool Start(Living living)
		{
			AE3172 ae = living.PetEffectList.GetOfType(ePetEffectType.AE3172) as AE3172;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007ED2 RID: 32466 RVA: 0x0003135C File Offset: 0x0002F55C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007ED3 RID: 32467 RVA: 0x00031385 File Offset: 0x0002F585
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007ED4 RID: 32468 RVA: 0x002A28A0 File Offset: 0x002A0AA0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 150;
				player.CurrentDamagePlus += (float)(this.m_added / 100);
			}
		}

		// Token: 0x06007ED5 RID: 32469 RVA: 0x002A28EC File Offset: 0x002A0AEC
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_added / 100);
				this.m_added = 0;
			}
		}

		// Token: 0x04004DAE RID: 19886
		private int m_type = 0;

		// Token: 0x04004DAF RID: 19887
		private int m_count = 0;

		// Token: 0x04004DB0 RID: 19888
		private int m_probability = 0;

		// Token: 0x04004DB1 RID: 19889
		private int m_delay = 0;

		// Token: 0x04004DB2 RID: 19890
		private int m_coldDown = 0;

		// Token: 0x04004DB3 RID: 19891
		private int m_currentId;

		// Token: 0x04004DB4 RID: 19892
		private int m_added = 0;
	}
}
