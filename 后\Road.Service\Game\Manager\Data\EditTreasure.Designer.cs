﻿namespace Game.Manager.Data
{
	// Token: 0x02000C7E RID: 3198
	public partial class EditTreasure : global::System.Windows.Forms.Form
	{
		// Token: 0x06007126 RID: 28966 RVA: 0x00255EC0 File Offset: 0x002540C0
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06007127 RID: 28967 RVA: 0x00255EF8 File Offset: 0x002540F8
		private void InitializeComponent()
		{
			this.components = new global::System.ComponentModel.Container();
			global::System.ComponentModel.ComponentResourceManager componentResourceManager = new global::System.ComponentModel.ComponentResourceManager(typeof(global::Game.Manager.Data.EditTreasure));
			this.EditTreasureBox = new global::System.Windows.Forms.DataGridView();
			this.ID = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.TemplateID = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.ItemName = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Type = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.ItemValid = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.BeginData = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.EndData = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Score = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Random = new global::System.Windows.Forms.DataGridViewTextBoxColumn();
			this.IsTip = new global::System.Windows.Forms.DataGridViewCheckBoxColumn();
			this.Name_Text = new global::System.Windows.Forms.TextBox();
			this.Name_Button = new global::System.Windows.Forms.Button();
			this.label1 = new global::System.Windows.Forms.Label();
			this.bdsInfo = new global::System.Windows.Forms.BindingSource(this.components);
			this.bdnInfo = new global::System.Windows.Forms.BindingNavigator(this.components);
			this.bindingNavigatorCountItem = new global::System.Windows.Forms.ToolStripLabel();
			this.bindingNavigatorMoveFirstItem = new global::System.Windows.Forms.ToolStripButton();
			this.bindingNavigatorMovePreviousItem = new global::System.Windows.Forms.ToolStripButton();
			this.bindingNavigatorSeparator = new global::System.Windows.Forms.ToolStripSeparator();
			this.bindingNavigatorPositionItem = new global::System.Windows.Forms.ToolStripTextBox();
			this.bindingNavigatorSeparator1 = new global::System.Windows.Forms.ToolStripSeparator();
			this.bindingNavigatorMoveNextItem = new global::System.Windows.Forms.ToolStripButton();
			this.bindingNavigatorMoveLastItem = new global::System.Windows.Forms.ToolStripButton();
			this.bindingNavigatorSeparator2 = new global::System.Windows.Forms.ToolStripSeparator();
			this.LastPage = new global::System.Windows.Forms.ToolStripButton();
			this.txtCurrentPage = new global::System.Windows.Forms.ToolStripLabel();
			this.toolStripLabel1 = new global::System.Windows.Forms.ToolStripLabel();
			this.lblPageCount = new global::System.Windows.Forms.ToolStripLabel();
			this.NextPage = new global::System.Windows.Forms.ToolStripButton();
			((global::System.ComponentModel.ISupportInitialize)this.EditTreasureBox).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.bdsInfo).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.bdnInfo).BeginInit();
			this.bdnInfo.SuspendLayout();
			base.SuspendLayout();
			this.EditTreasureBox.AllowUserToAddRows = false;
			this.EditTreasureBox.AllowUserToDeleteRows = false;
			this.EditTreasureBox.ColumnHeadersHeightSizeMode = global::System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.EditTreasureBox.Columns.AddRange(new global::System.Windows.Forms.DataGridViewColumn[] { this.ID, this.TemplateID, this.ItemName, this.Type, this.ItemValid, this.BeginData, this.EndData, this.Score, this.Random, this.IsTip });
			this.EditTreasureBox.Location = new global::System.Drawing.Point(2, 41);
			this.EditTreasureBox.Name = "EditTreasureBox";
			this.EditTreasureBox.RowTemplate.Height = 23;
			this.EditTreasureBox.Size = new global::System.Drawing.Size(1044, 485);
			this.EditTreasureBox.TabIndex = 0;
			this.EditTreasureBox.CellEndEdit += new global::System.Windows.Forms.DataGridViewCellEventHandler(this.EditTreasureBox_CellEndEdit);
			this.ID.DataPropertyName = "ID";
			this.ID.HeaderText = "ID";
			this.ID.Name = "ID";
			this.ID.ReadOnly = true;
			this.ID.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.TemplateID.DataPropertyName = "TemplateID";
			this.TemplateID.HeaderText = "物品ID";
			this.TemplateID.Name = "TemplateID";
			this.TemplateID.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.ItemName.DataPropertyName = "ItemName";
			this.ItemName.HeaderText = "物品名称";
			this.ItemName.Name = "ItemName";
			this.ItemName.ReadOnly = true;
			this.ItemName.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.Type.DataPropertyName = "Type";
			this.Type.HeaderText = "分类";
			this.Type.Name = "Type";
			this.Type.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.ItemValid.DataPropertyName = "ItemValid";
			this.ItemValid.HeaderText = "物品期限";
			this.ItemValid.Name = "ItemValid";
			this.ItemValid.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.BeginData.DataPropertyName = "BeginData";
			this.BeginData.HeaderText = "最小数量";
			this.BeginData.Name = "BeginData";
			this.BeginData.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.EndData.DataPropertyName = "EndData";
			this.EndData.HeaderText = "最大数量";
			this.EndData.Name = "EndData";
			this.EndData.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.Score.DataPropertyName = "Score";
			this.Score.HeaderText = "折算积分";
			this.Score.Name = "Score";
			this.Score.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.Random.DataPropertyName = "Random";
			this.Random.HeaderText = "几率";
			this.Random.Name = "Random";
			this.Random.SortMode = global::System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.IsTip.DataPropertyName = "IsTip";
			this.IsTip.HeaderText = "是否通知";
			this.IsTip.Name = "IsTip";
			this.Name_Text.Location = new global::System.Drawing.Point(797, 13);
			this.Name_Text.Name = "Name_Text";
			this.Name_Text.Size = new global::System.Drawing.Size(168, 21);
			this.Name_Text.TabIndex = 14;
			this.Name_Text.TabStop = false;
			this.Name_Button.Location = new global::System.Drawing.Point(971, 12);
			this.Name_Button.Name = "Name_Button";
			this.Name_Button.Size = new global::System.Drawing.Size(75, 23);
			this.Name_Button.TabIndex = 13;
			this.Name_Button.TabStop = false;
			this.Name_Button.Text = "查询";
			this.Name_Button.UseVisualStyleBackColor = true;
			this.Name_Button.Click += new global::System.EventHandler(this.Name_Button_Click);
			this.label1.AutoSize = true;
			this.label1.Font = new global::System.Drawing.Font("宋体", 11f);
			this.label1.Location = new global::System.Drawing.Point(723, 16);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(68, 15);
			this.label1.TabIndex = 15;
			this.label1.Text = "类型ID：";
			this.bdnInfo.AddNewItem = null;
			this.bdnInfo.AutoSize = false;
			this.bdnInfo.BackColor = global::System.Drawing.SystemColors.ActiveBorder;
			this.bdnInfo.BackgroundImageLayout = global::System.Windows.Forms.ImageLayout.Zoom;
			this.bdnInfo.CountItem = this.bindingNavigatorCountItem;
			this.bdnInfo.CountItemFormat = "/ 共{0}行";
			this.bdnInfo.DeleteItem = null;
			this.bdnInfo.Dock = global::System.Windows.Forms.DockStyle.None;
			this.bdnInfo.Font = new global::System.Drawing.Font("Microsoft YaHei UI", 9f);
			this.bdnInfo.ImeMode = global::System.Windows.Forms.ImeMode.NoControl;
			this.bdnInfo.Items.AddRange(new global::System.Windows.Forms.ToolStripItem[]
			{
				this.bindingNavigatorMoveFirstItem, this.bindingNavigatorMovePreviousItem, this.bindingNavigatorSeparator, this.bindingNavigatorPositionItem, this.bindingNavigatorCountItem, this.bindingNavigatorSeparator1, this.bindingNavigatorMoveNextItem, this.bindingNavigatorMoveLastItem, this.bindingNavigatorSeparator2, this.LastPage,
				this.txtCurrentPage, this.toolStripLabel1, this.lblPageCount, this.NextPage
			});
			this.bdnInfo.Location = new global::System.Drawing.Point(2, 529);
			this.bdnInfo.MoveFirstItem = this.bindingNavigatorMoveFirstItem;
			this.bdnInfo.MoveLastItem = this.bindingNavigatorMoveLastItem;
			this.bdnInfo.MoveNextItem = this.bindingNavigatorMoveNextItem;
			this.bdnInfo.MovePreviousItem = this.bindingNavigatorMovePreviousItem;
			this.bdnInfo.Name = "bdnInfo";
			this.bdnInfo.PositionItem = this.bindingNavigatorPositionItem;
			this.bdnInfo.RightToLeft = global::System.Windows.Forms.RightToLeft.No;
			this.bdnInfo.Size = new global::System.Drawing.Size(1044, 42);
			this.bdnInfo.TabIndex = 16;
			this.bdnInfo.Text = "bindingNavigator1";
			this.bindingNavigatorCountItem.Name = "bindingNavigatorCountItem";
			this.bindingNavigatorCountItem.Size = new global::System.Drawing.Size(56, 39);
			this.bindingNavigatorCountItem.Text = "/ 共{0}行";
			this.bindingNavigatorCountItem.ToolTipText = "总项数";
			this.bindingNavigatorMoveFirstItem.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.bindingNavigatorMoveFirstItem.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("bindingNavigatorMoveFirstItem.Image");
			this.bindingNavigatorMoveFirstItem.Name = "bindingNavigatorMoveFirstItem";
			this.bindingNavigatorMoveFirstItem.RightToLeftAutoMirrorImage = true;
			this.bindingNavigatorMoveFirstItem.Size = new global::System.Drawing.Size(23, 39);
			this.bindingNavigatorMoveFirstItem.Text = "移到第一条记录";
			this.bindingNavigatorMovePreviousItem.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.bindingNavigatorMovePreviousItem.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("bindingNavigatorMovePreviousItem.Image");
			this.bindingNavigatorMovePreviousItem.Name = "bindingNavigatorMovePreviousItem";
			this.bindingNavigatorMovePreviousItem.RightToLeftAutoMirrorImage = true;
			this.bindingNavigatorMovePreviousItem.Size = new global::System.Drawing.Size(23, 39);
			this.bindingNavigatorMovePreviousItem.Text = "移到上一条记录";
			this.bindingNavigatorSeparator.Name = "bindingNavigatorSeparator";
			this.bindingNavigatorSeparator.Size = new global::System.Drawing.Size(6, 42);
			this.bindingNavigatorPositionItem.AccessibleName = "位置";
			this.bindingNavigatorPositionItem.AutoSize = false;
			this.bindingNavigatorPositionItem.Font = new global::System.Drawing.Font("Microsoft YaHei UI", 9f);
			this.bindingNavigatorPositionItem.Name = "bindingNavigatorPositionItem";
			this.bindingNavigatorPositionItem.Size = new global::System.Drawing.Size(50, 23);
			this.bindingNavigatorPositionItem.Text = "0";
			this.bindingNavigatorPositionItem.ToolTipText = "当前位置";
			this.bindingNavigatorSeparator1.Name = "bindingNavigatorSeparator1";
			this.bindingNavigatorSeparator1.Size = new global::System.Drawing.Size(6, 42);
			this.bindingNavigatorMoveNextItem.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.bindingNavigatorMoveNextItem.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("bindingNavigatorMoveNextItem.Image");
			this.bindingNavigatorMoveNextItem.Name = "bindingNavigatorMoveNextItem";
			this.bindingNavigatorMoveNextItem.RightToLeftAutoMirrorImage = true;
			this.bindingNavigatorMoveNextItem.Size = new global::System.Drawing.Size(23, 39);
			this.bindingNavigatorMoveNextItem.Text = "移到下一条记录";
			this.bindingNavigatorMoveLastItem.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.bindingNavigatorMoveLastItem.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("bindingNavigatorMoveLastItem.Image");
			this.bindingNavigatorMoveLastItem.Name = "bindingNavigatorMoveLastItem";
			this.bindingNavigatorMoveLastItem.RightToLeftAutoMirrorImage = true;
			this.bindingNavigatorMoveLastItem.Size = new global::System.Drawing.Size(23, 39);
			this.bindingNavigatorMoveLastItem.Text = "移到最后一条记录";
			this.bindingNavigatorSeparator2.Name = "bindingNavigatorSeparator2";
			this.bindingNavigatorSeparator2.Size = new global::System.Drawing.Size(6, 42);
			this.LastPage.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Text;
			this.LastPage.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("LastPage.Image");
			this.LastPage.ImageTransparentColor = global::System.Drawing.Color.Magenta;
			this.LastPage.Name = "LastPage";
			this.LastPage.Size = new global::System.Drawing.Size(48, 39);
			this.LastPage.Text = "上一页";
			this.LastPage.Click += new global::System.EventHandler(this.LastPage_Click);
			this.txtCurrentPage.Name = "txtCurrentPage";
			this.txtCurrentPage.Size = new global::System.Drawing.Size(15, 39);
			this.txtCurrentPage.Text = "1";
			this.toolStripLabel1.Name = "toolStripLabel1";
			this.toolStripLabel1.Size = new global::System.Drawing.Size(13, 39);
			this.toolStripLabel1.Text = "/";
			this.lblPageCount.Name = "lblPageCount";
			this.lblPageCount.Size = new global::System.Drawing.Size(29, 39);
			this.lblPageCount.Text = "100";
			this.NextPage.DisplayStyle = global::System.Windows.Forms.ToolStripItemDisplayStyle.Text;
			this.NextPage.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("NextPage.Image");
			this.NextPage.ImageTransparentColor = global::System.Drawing.Color.Magenta;
			this.NextPage.Name = "NextPage";
			this.NextPage.Size = new global::System.Drawing.Size(48, 39);
			this.NextPage.Text = "下一页";
			this.NextPage.Click += new global::System.EventHandler(this.NextPage_Click);
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(6f, 12f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			this.BackColor = global::System.Drawing.SystemColors.ActiveBorder;
			base.ClientSize = new global::System.Drawing.Size(1047, 572);
			base.Controls.Add(this.bdnInfo);
			base.Controls.Add(this.Name_Text);
			base.Controls.Add(this.Name_Button);
			base.Controls.Add(this.label1);
			base.Controls.Add(this.EditTreasureBox);
			base.Icon = (global::System.Drawing.Icon)componentResourceManager.GetObject("$this.Icon");
			base.Name = "EditTreasure";
			this.Text = "迷宫藏宝掉落编辑";
			base.Load += new global::System.EventHandler(this.EditTreasure_Load);
			((global::System.ComponentModel.ISupportInitialize)this.EditTreasureBox).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.bdsInfo).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.bdnInfo).EndInit();
			this.bdnInfo.ResumeLayout(false);
			this.bdnInfo.PerformLayout();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04003CFA RID: 15610
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04003CFB RID: 15611
		private global::System.Windows.Forms.DataGridView EditTreasureBox;

		// Token: 0x04003CFC RID: 15612
		private global::System.Windows.Forms.DataGridViewTextBoxColumn ID;

		// Token: 0x04003CFD RID: 15613
		private global::System.Windows.Forms.DataGridViewTextBoxColumn TemplateID;

		// Token: 0x04003CFE RID: 15614
		private global::System.Windows.Forms.DataGridViewTextBoxColumn ItemName;

		// Token: 0x04003CFF RID: 15615
		private global::System.Windows.Forms.DataGridViewTextBoxColumn Type;

		// Token: 0x04003D00 RID: 15616
		private global::System.Windows.Forms.DataGridViewTextBoxColumn ItemValid;

		// Token: 0x04003D01 RID: 15617
		private global::System.Windows.Forms.DataGridViewTextBoxColumn BeginData;

		// Token: 0x04003D02 RID: 15618
		private global::System.Windows.Forms.DataGridViewTextBoxColumn EndData;

		// Token: 0x04003D03 RID: 15619
		private global::System.Windows.Forms.DataGridViewTextBoxColumn Score;

		// Token: 0x04003D04 RID: 15620
		private global::System.Windows.Forms.DataGridViewTextBoxColumn Random;

		// Token: 0x04003D05 RID: 15621
		private global::System.Windows.Forms.DataGridViewCheckBoxColumn IsTip;

		// Token: 0x04003D06 RID: 15622
		private global::System.Windows.Forms.TextBox Name_Text;

		// Token: 0x04003D07 RID: 15623
		private global::System.Windows.Forms.Button Name_Button;

		// Token: 0x04003D08 RID: 15624
		private global::System.Windows.Forms.Label label1;

		// Token: 0x04003D09 RID: 15625
		private global::System.Windows.Forms.BindingSource bdsInfo;

		// Token: 0x04003D0A RID: 15626
		private global::System.Windows.Forms.BindingNavigator bdnInfo;

		// Token: 0x04003D0B RID: 15627
		private global::System.Windows.Forms.ToolStripLabel bindingNavigatorCountItem;

		// Token: 0x04003D0C RID: 15628
		private global::System.Windows.Forms.ToolStripButton bindingNavigatorMoveFirstItem;

		// Token: 0x04003D0D RID: 15629
		private global::System.Windows.Forms.ToolStripButton bindingNavigatorMovePreviousItem;

		// Token: 0x04003D0E RID: 15630
		private global::System.Windows.Forms.ToolStripSeparator bindingNavigatorSeparator;

		// Token: 0x04003D0F RID: 15631
		private global::System.Windows.Forms.ToolStripTextBox bindingNavigatorPositionItem;

		// Token: 0x04003D10 RID: 15632
		private global::System.Windows.Forms.ToolStripSeparator bindingNavigatorSeparator1;

		// Token: 0x04003D11 RID: 15633
		private global::System.Windows.Forms.ToolStripButton bindingNavigatorMoveNextItem;

		// Token: 0x04003D12 RID: 15634
		private global::System.Windows.Forms.ToolStripButton bindingNavigatorMoveLastItem;

		// Token: 0x04003D13 RID: 15635
		private global::System.Windows.Forms.ToolStripSeparator bindingNavigatorSeparator2;

		// Token: 0x04003D14 RID: 15636
		private global::System.Windows.Forms.ToolStripButton LastPage;

		// Token: 0x04003D15 RID: 15637
		private global::System.Windows.Forms.ToolStripLabel txtCurrentPage;

		// Token: 0x04003D16 RID: 15638
		private global::System.Windows.Forms.ToolStripLabel toolStripLabel1;

		// Token: 0x04003D17 RID: 15639
		private global::System.Windows.Forms.ToolStripLabel lblPageCount;

		// Token: 0x04003D18 RID: 15640
		private global::System.Windows.Forms.ToolStripButton NextPage;
	}
}
