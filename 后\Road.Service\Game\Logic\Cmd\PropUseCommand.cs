﻿using System;
using System.Linq;
using Bussiness.Managers;
using Game.Base.Packets;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F10 RID: 3856
	[GameCommand(32, "使用道具")]
	public class PropUseCommand : ICommandHandler
	{
		// Token: 0x060083B9 RID: 33721 RVA: 0x002B54C8 File Offset: 0x002B36C8
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool flag = game.GameState != eGameState.Playing || player.GetSealState();
			if (!flag)
			{
				int num = (int)packet.ReadByte();
				int num2 = packet.ReadInt();
				int num3 = packet.ReadInt();
				player.PlaceProp = num2;
				bool flag2 = player.Prop.Contains(10471) || (num3 == 10471 && player.Prop.Count != 0);
				if (!flag2)
				{
					ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(num3);
					int[] array = new int[]
					{
						10001, 10002, 10003, 10004, 10005, 10006, 10007, 10008, 10009, 10010,
						10011, 10012, 10013, 10014, 10015, 10016, 10017, 10018, 10019, 10020,
						10021, 10022
					};
					bool flag3 = !player.CanUseItem(itemTemplateInfo);
					if (!flag3)
					{
						int num4 = num;
						int num5 = num4;
						bool flag4 = num5 == 1;
						if (flag4)
						{
							bool flag5 = array.Contains(num3);
							if (flag5)
							{
								bool flag6 = num3 == 10001;
								if (flag6)
								{
									player.Prop1++;
								}
								bool flag7 = num3 == 10002;
								if (flag7)
								{
									player.Prop2++;
								}
								bool flag8 = num3 == 10003;
								if (flag8)
								{
									player.Prop3++;
								}
								bool flag9 = num3 == 10004;
								if (flag9)
								{
									player.Prop4++;
								}
								player.UseItem(itemTemplateInfo);
								player.OnPropUseItem();
								player.PlayerDetail.OnUsingItem(num3);
							}
							else
							{
								Console.WriteLine("eTankCmdType.PROP type:{0} {1} {2}, Player Hack buff", num, num2, num3);
								player.PlayerDetail.Disconnect();
							}
						}
						else
						{
							bool flag10 = !player.PlayerDetail.UsePropItem(game, num, num2, num3, player.IsLiving);
							if (!flag10)
							{
								bool flag11 = !player.UseItem(itemTemplateInfo);
								if (flag11)
								{
									Console.WriteLine("Using prop error fail!");
								}
								else
								{
									bool flag12 = num3 == 10001;
									if (flag12)
									{
										player.Prop1++;
									}
									bool flag13 = num3 == 10002;
									if (flag13)
									{
										player.Prop2++;
									}
									bool flag14 = num3 == 10003;
									if (flag14)
									{
										player.Prop3++;
									}
									bool flag15 = num3 == 10004;
									if (flag15)
									{
										player.Prop4++;
									}
									player.OnPropUseItem();
								}
							}
						}
					}
				}
			}
		}
	}
}
