﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E9A RID: 3738
	public class CE1207 : BasePetEffect
	{
		// Token: 0x0600813C RID: 33084 RVA: 0x002AC358 File Offset: 0x002AA558
		public CE1207(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1207, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600813D RID: 33085 RVA: 0x002AC3D8 File Offset: 0x002AA5D8
		public override bool Start(Living living)
		{
			CE1207 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1207) as CE1207;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600813E RID: 33086 RVA: 0x0003293E File Offset: 0x00030B3E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600813F RID: 33087 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008140 RID: 33088 RVA: 0x002AC438 File Offset: 0x002AA638
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 500;
				bool flag2 = living.Defence < (double)this.m_added;
				if (flag2)
				{
					this.m_added = (int)living.Defence - 1;
				}
				living.Defence -= (double)this.m_added;
			}
		}

		// Token: 0x06008141 RID: 33089 RVA: 0x002AC498 File Offset: 0x002AA698
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008142 RID: 33090 RVA: 0x002AC4CC File Offset: 0x002AA6CC
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Defence += (double)this.m_added;
			this.m_added = 0;
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005073 RID: 20595
		private int m_type = 0;

		// Token: 0x04005074 RID: 20596
		private int m_count = 0;

		// Token: 0x04005075 RID: 20597
		private int m_probability = 0;

		// Token: 0x04005076 RID: 20598
		private int m_delay = 0;

		// Token: 0x04005077 RID: 20599
		private int m_coldDown = 0;

		// Token: 0x04005078 RID: 20600
		private int m_currentId;

		// Token: 0x04005079 RID: 20601
		private int m_added = 0;
	}
}
