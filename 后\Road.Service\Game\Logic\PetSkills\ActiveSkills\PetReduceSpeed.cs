﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D43 RID: 3395
	public class PetReduceSpeed : BasePetEffect
	{
		// Token: 0x060079C4 RID: 31172 RVA: 0x0002DE8D File Offset: 0x0002C08D
		public PetReduceSpeed(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetReduceSpeed, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x060079C5 RID: 31173 RVA: 0x0028C758 File Offset: 0x0028A958
		public override bool Start(Living living)
		{
			PetReduceSpeed petReduceSpeed = living.PetEffectList.GetOfType(ePetEffectType.PetReduceSpeed) as PetReduceSpeed;
			bool flag = petReduceSpeed != null;
			bool flag2;
			if (flag)
			{
				petReduceSpeed.m_probability = ((this.m_probability > petReduceSpeed.m_probability) ? this.m_probability : petReduceSpeed.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079C6 RID: 31174 RVA: 0x0002DEBE File Offset: 0x0002C0BE
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079C7 RID: 31175 RVA: 0x0002DED4 File Offset: 0x0002C0D4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079C8 RID: 31176 RVA: 0x0028C7B8 File Offset: 0x0028A9B8
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				living.PetEffectTrigger = true;
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				new PetReduceSpeedEquip(this.m_count, base.Info.ID.ToString()).Start(living);
			}
		}

		// Token: 0x04004758 RID: 18264
		private int m_count;

		// Token: 0x04004759 RID: 18265
		private int m_probability;

		// Token: 0x0400475A RID: 18266
		private int m_currentId;
	}
}
