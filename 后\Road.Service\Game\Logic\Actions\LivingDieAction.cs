﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F56 RID: 3926
	public class LivingDieAction : BaseAction
	{
		// Token: 0x06008504 RID: 34052 RVA: 0x000353E8 File Offset: 0x000335E8
		public LivingDieAction(Living living, int delay)
			: base(delay, 1000)
		{
			this.m_living = living;
		}

		// Token: 0x06008505 RID: 34053 RVA: 0x000353FF File Offset: 0x000335FF
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			this.m_living.Die();
			base.Finish(tick);
		}

		// Token: 0x040052EF RID: 21231
		private Living m_living;
	}
}
