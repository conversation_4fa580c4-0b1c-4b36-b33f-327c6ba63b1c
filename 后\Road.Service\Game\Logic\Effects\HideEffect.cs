﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EEA RID: 3818
	public class HideEffect : AbstractEffect
	{
		// Token: 0x0600832D RID: 33581 RVA: 0x00034120 File Offset: 0x00032320
		public HideEffect(int count)
			: base(eEffectType.HideEffect)
		{
			this.m_count = count;
		}

		// Token: 0x0600832E RID: 33582 RVA: 0x002B3190 File Offset: 0x002B1390
		public override bool Start(Living living)
		{
			HideEffect hideEffect = living.EffectList.GetOfType(eEffectType.HideEffect) as HideEffect;
			bool flag = hideEffect != null;
			bool flag2;
			if (flag)
			{
				hideEffect.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600832F RID: 33583 RVA: 0x00034132 File Offset: 0x00032332
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginFitting;
			living.IsHide = true;
		}

		// Token: 0x06008330 RID: 33584 RVA: 0x00034150 File Offset: 0x00032350
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginFitting;
			living.IsHide = false;
		}

		// Token: 0x06008331 RID: 33585 RVA: 0x002B31D4 File Offset: 0x002B13D4
		private void player_BeginFitting(Living player)
		{
			this.m_count--;
			bool flag = this.m_count <= 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x04005203 RID: 20995
		private int m_count;
	}
}
