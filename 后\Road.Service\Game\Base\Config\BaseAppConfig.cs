﻿using System;
using System.Configuration;
using System.Reflection;
using log4net;

namespace Game.Base.Config
{
	// Token: 0x02000F94 RID: 3988
	public abstract class BaseAppConfig
	{
		// Token: 0x060087A5 RID: 34725 RVA: 0x0000586E File Offset: 0x00003A6E
		public BaseAppConfig()
		{
		}

		// Token: 0x060087A6 RID: 34726 RVA: 0x002C7F2C File Offset: 0x002C612C
		protected virtual void Load(Type type)
		{
			ConfigurationManager.RefreshSection("appSettings");
			FieldInfo[] fields = type.GetFields();
			FieldInfo[] array = fields;
			FieldInfo[] array2 = array;
			foreach (FieldInfo fieldInfo in array2)
			{
				object[] customAttributes = fieldInfo.GetCustomAttributes(typeof(ConfigPropertyAttribute), false);
				bool flag = customAttributes.Length != 0;
				if (flag)
				{
					ConfigPropertyAttribute configPropertyAttribute = (ConfigPropertyAttribute)customAttributes[0];
					fieldInfo.SetValue(this, this.LoadConfigProperty(configPropertyAttribute));
				}
			}
		}

		// Token: 0x060087A7 RID: 34727 RVA: 0x002C7FAC File Offset: 0x002C61AC
		private object LoadConfigProperty(ConfigPropertyAttribute attrib)
		{
			string key = attrib.Key;
			string text = ConfigurationManager.AppSettings[key];
			bool flag = text == null;
			if (flag)
			{
				text = attrib.DefaultValue.ToString();
				BaseAppConfig.log.Warn("Loading " + key + " value is null,using default vaule:" + text);
			}
			else
			{
				BaseAppConfig.log.Debug("Loading " + key + " Value is " + text);
			}
			object obj;
			try
			{
				obj = Convert.ChangeType(text, attrib.DefaultValue.GetType());
			}
			catch (Exception ex)
			{
				BaseAppConfig.log.Error("Exception in ServerProperties Load: ", ex);
				obj = null;
			}
			return obj;
		}

		// Token: 0x040053BB RID: 21435
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
	}
}
