﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D94 RID: 3476
	public class AE1021 : BasePetEffect
	{
		// Token: 0x06007B7E RID: 31614 RVA: 0x002932B8 File Offset: 0x002914B8
		public AE1021(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1021, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B7F RID: 31615 RVA: 0x00293334 File Offset: 0x00291534
		public override bool Start(Living living)
		{
			AE1021 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1021) as AE1021;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B80 RID: 31616 RVA: 0x0002F203 File Offset: 0x0002D403
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007B81 RID: 31617 RVA: 0x0002F219 File Offset: 0x0002D419
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007B82 RID: 31618 RVA: 0x00293390 File Offset: 0x00291590
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1021(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004946 RID: 18758
		private int m_type = 0;

		// Token: 0x04004947 RID: 18759
		private int m_count = 0;

		// Token: 0x04004948 RID: 18760
		private int m_probability = 0;

		// Token: 0x04004949 RID: 18761
		private int m_delay = 0;

		// Token: 0x0400494A RID: 18762
		private int m_coldDown = 0;

		// Token: 0x0400494B RID: 18763
		private int m_currentId;

		// Token: 0x0400494C RID: 18764
		private int m_added = 0;
	}
}
