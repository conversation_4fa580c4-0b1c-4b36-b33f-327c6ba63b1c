﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F27 RID: 3879
	public class Goblin2Effect : BaseCardEffect
	{
		// Token: 0x06008421 RID: 33825 RVA: 0x002B6C90 File Offset: 0x002B4E90
		public Goblin2Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.Goblin2, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008422 RID: 33826 RVA: 0x002B6D00 File Offset: 0x002B4F00
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.Goblin2) is Goblin2Effect;
			return flag || base.Start(living);
		}

		// Token: 0x06008423 RID: 33827 RVA: 0x00034B9E File Offset: 0x00032D9E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.ChangeProperty;
			player.PlayerAfterReset += this.ChangeProperty1;
		}

		// Token: 0x06008424 RID: 33828 RVA: 0x00034BC7 File Offset: 0x00032DC7
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.ChangeProperty;
			player.PlayerAfterReset -= this.ChangeProperty1;
		}

		// Token: 0x06008425 RID: 33829 RVA: 0x002B6D38 File Offset: 0x002B4F38
		private void ChangeProperty(Living player)
		{
			bool flag = this.m_added == 0 && player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 5;
			if (flag)
			{
				(player as Player).AddDander(this.m_value * 2);
				this.m_added = this.m_value;
			}
		}

		// Token: 0x06008426 RID: 33830 RVA: 0x002B6D9C File Offset: 0x002B4F9C
		private void ChangeProperty1(Player player)
		{
			bool flag = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 5;
			if (flag)
			{
				player.Game.SendMessage(player.PlayerDetail, "您激活了龙巢之战2件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活龙巢之战2件套卡.", 3);
			}
		}

		// Token: 0x0400526A RID: 21098
		private int m_indexValue = 0;

		// Token: 0x0400526B RID: 21099
		private int m_value = 0;

		// Token: 0x0400526C RID: 21100
		private int m_added = 0;
	}
}
