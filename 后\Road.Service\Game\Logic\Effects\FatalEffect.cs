﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EE9 RID: 3817
	public class FatalEffect : BasePlayerEffect
	{
		// Token: 0x06008326 RID: 33574 RVA: 0x00034070 File Offset: 0x00032270
		public FatalEffect(int count, int probability)
			: base(eEffectType.FatalEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x06008327 RID: 33575 RVA: 0x002B3068 File Offset: 0x002B1268
		public override bool Start(Living living)
		{
			FatalEffect fatalEffect = living.EffectList.GetOfType(eEffectType.FatalEffect) as FatalEffect;
			bool flag = fatalEffect != null;
			bool flag2;
			if (flag)
			{
				fatalEffect.m_probability = ((this.m_probability > fatalEffect.m_probability) ? this.m_probability : fatalEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008328 RID: 33576 RVA: 0x00034097 File Offset: 0x00032297
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.ChangeProperty;
			player.TakePlayerDamage += this.player_BeforeTakeDamage;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06008329 RID: 33577 RVA: 0x000340D3 File Offset: 0x000322D3
		private void player_AfterPlayerShooted(Player player)
		{
			this.IsTrigger = false;
			player.ControlBall = false;
		}

		// Token: 0x0600832A RID: 33578 RVA: 0x002B30C4 File Offset: 0x002B12C4
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.IsTrigger && living is Player;
			if (flag)
			{
				damageAmount = damageAmount * (100 - this.m_count) / 100;
			}
		}

		// Token: 0x0600832B RID: 33579 RVA: 0x000340E4 File Offset: 0x000322E4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.ChangeProperty;
			player.TakePlayerDamage -= this.player_BeforeTakeDamage;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x0600832C RID: 33580 RVA: 0x002B3100 File Offset: 0x002B1300
		private void ChangeProperty(Player player, int ball)
		{
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				player.ShootMovieDelay = 50;
				this.IsTrigger = true;
				bool flag2 = player.CurrentBall.ID != 3;
				if (flag2)
				{
					player.ControlBall = true;
				}
				player.AttackEffectTrigger = true;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("FatalEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x04005201 RID: 20993
		private int m_count = 0;

		// Token: 0x04005202 RID: 20994
		private int m_probability = 0;
	}
}
