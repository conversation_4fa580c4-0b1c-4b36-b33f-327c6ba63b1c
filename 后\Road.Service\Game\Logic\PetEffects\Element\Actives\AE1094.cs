﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DC2 RID: 3522
	public class AE1094 : BasePetEffect
	{
		// Token: 0x06007C6D RID: 31853 RVA: 0x00297734 File Offset: 0x00295934
		public AE1094(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1094, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C6E RID: 31854 RVA: 0x002977B4 File Offset: 0x002959B4
		public override bool Start(Living living)
		{
			AE1094 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1094) as AE1094;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C6F RID: 31855 RVA: 0x0002FA84 File Offset: 0x0002DC84
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C70 RID: 31856 RVA: 0x0002FA9A File Offset: 0x0002DC9A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C71 RID: 31857 RVA: 0x00297814 File Offset: 0x00295A14
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.AddPetEffect(new CE1094(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004A88 RID: 19080
		private int m_type = 0;

		// Token: 0x04004A89 RID: 19081
		private int m_count = 0;

		// Token: 0x04004A8A RID: 19082
		private int m_probability = 0;

		// Token: 0x04004A8B RID: 19083
		private int m_delay = 0;

		// Token: 0x04004A8C RID: 19084
		private int m_coldDown = 0;

		// Token: 0x04004A8D RID: 19085
		private int m_currentId;

		// Token: 0x04004A8E RID: 19086
		private int m_added = 0;
	}
}
