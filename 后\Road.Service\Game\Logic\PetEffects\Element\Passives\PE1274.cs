﻿using System;
using Game.Logic.Event;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D81 RID: 3457
	public class PE1274 : BasePetEffect
	{
		// Token: 0x06007B1A RID: 31514 RVA: 0x00291A30 File Offset: 0x0028FC30
		public PE1274(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1274, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B1B RID: 31515 RVA: 0x00291AAC File Offset: 0x0028FCAC
		public override bool Start(Living living)
		{
			PE1274 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1274) as PE1274;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B1C RID: 31516 RVA: 0x0002ED1C File Offset: 0x0002CF1C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.StartAtombEffect);
		}

		// Token: 0x06007B1D RID: 31517 RVA: 0x0002ED32 File Offset: 0x0002CF32
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.StartAtombEffect);
		}

		// Token: 0x06007B1E RID: 31518 RVA: 0x00291B08 File Offset: 0x0028FD08
		private void StartAtombEffect(Living liv)
		{
			bool flag = this.rand.Next(100) < 20;
			if (flag)
			{
				((Player)liv).SetBall(4);
			}
		}

		// Token: 0x040048C7 RID: 18631
		private int m_type = 0;

		// Token: 0x040048C8 RID: 18632
		private int m_count = 0;

		// Token: 0x040048C9 RID: 18633
		private int m_probability = 0;

		// Token: 0x040048CA RID: 18634
		private int m_delay = 0;

		// Token: 0x040048CB RID: 18635
		private int m_coldDown = 0;

		// Token: 0x040048CC RID: 18636
		private int m_currentId;

		// Token: 0x040048CD RID: 18637
		private int m_added = 0;
	}
}
