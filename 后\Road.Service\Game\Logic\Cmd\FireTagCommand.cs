﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F02 RID: 3842
	[GameCommand(96, "准备开炮")]
	public class FireTagCommand : ICommandHandler
	{
		// Token: 0x0600839C RID: 33692 RVA: 0x002B5034 File Offset: 0x002B3234
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool isAttacking = player.IsAttacking;
			if (isAttacking)
			{
				packet.Parameter1 = player.Id;
				game.SendToAll(packet);
				game.SendSyncLifeTime();
				bool flag = packet.ReadBoolean();
				byte b = packet.ReadByte();
				player.PrepareShoot(b);
			}
		}
	}
}
