﻿using System;
using System.Data;
using System.Data.SqlClient;
using SqlDataProvider.Data;

namespace Bussiness
{
	// Token: 0x02000FA9 RID: 4009
	public class FeedbackBussiness : BaseBussiness
	{
		// Token: 0x0600886A RID: 34922 RVA: 0x002D12F0 File Offset: 0x002CF4F0
		public bool Submit(FeedbackSubmit info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@QuestionTitle", info.QuestionTitle),
					new SqlParameter("@QuestionContent", info.QuestionContent),
					new SqlParameter("@OccurrenceDate", info.OccurrenceDate),
					new SqlParameter("@QuestionType", info.QuestionType),
					new SqlParameter("@GoodsGetMethod", info.GoodsGetMethod),
					new SqlParameter("@GoodsGetDate", info.GoodsGetDate),
					new SqlParameter("@ChargeOrderID", info.ChargeOrderID),
					new SqlParameter("@ChargeMethod", info.ChargeMethod),
					new SqlParameter("@ChargeMoneys", info.ChargeMoneys),
					new SqlParameter("@ActivityIsError", info.ActivityIsError),
					new SqlParameter("@ActivityName", info.ActivityName),
					new SqlParameter("@ReportUserName", info.ReportUserName),
					new SqlParameter("@ReportUrl", info.ReportUrl),
					new SqlParameter("@UserFullName", info.UserFullName),
					new SqlParameter("@UserPhone", info.UserPhone),
					new SqlParameter("@ComplaintsTitle", info.ComplaintsTitle),
					new SqlParameter("@ComplaintsSource", info.ComplaintsSource),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@UserName", info.UserName),
					new SqlParameter("@UserNickName", info.UserNickName),
					new SqlParameter("@IsExist", info.IsExist)
				};
				flag = this.db.RunProcedure("SP_Feedback_Submit_Add", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600886B RID: 34923 RVA: 0x002D1538 File Offset: 0x002CF738
		public bool SubmitContinue(FeedbackSubmitContinue info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@QuestionID", info.QuestionID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@NickName", info.NickName),
					new SqlParameter("@ReplyID", info.ReplyID),
					new SqlParameter("@ReplyContent", info.ReplyContent)
				};
				flag = this.db.RunProcedure("SP_Feedback_SubmitContinue_Add", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600886C RID: 34924 RVA: 0x002D1624 File Offset: 0x002CF824
		public bool Appraisal(FeedbackAppraisal info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@QuestionID", info.QuestionID),
					new SqlParameter("@ReplyID", info.ReplyID),
					new SqlParameter("@AppraisalGrade", info.AppraisalGrade),
					new SqlParameter("@AppraisalContent", info.AppraisalContent)
				};
				flag = this.db.RunProcedure("SP_Feedback_Appraisal_Add", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600886D RID: 34925 RVA: 0x002D16F8 File Offset: 0x002CF8F8
		public bool Reply(FeedbackRead info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@QuestionID", info.QuestionID),
					new SqlParameter("@ReplyID", info.ReplyID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@OccurrenceDate", info.OccurrenceDate),
					new SqlParameter("@Title", info.Title),
					new SqlParameter("@QuestionContent", info.QuestionContent),
					new SqlParameter("@ReplyContent", info.ReplyContent)
				};
				flag = this.db.RunProcedure("SP_Feedback_Reply", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600886E RID: 34926 RVA: 0x002D1808 File Offset: 0x002CFA08
		public FeedbackRead Read(int userID)
		{
			FeedbackRead feedbackRead = new FeedbackRead();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", userID)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Feedback_Read");
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					feedbackRead = new FeedbackRead
					{
						QuestionID = (int)sqlDataReader["QuestionID"],
						ReplyID = (int)sqlDataReader["ReplyID"],
						UserID = (int)sqlDataReader["UserID"],
						OccurrenceDate = (string)sqlDataReader["OccurrenceDate"],
						Title = (string)sqlDataReader["Title"],
						QuestionContent = (string)sqlDataReader["QuestionContent"],
						ReplyContent = (string)sqlDataReader["ReplyContent"],
						IsExist = (bool)sqlDataReader["IsExist"]
					};
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return feedbackRead;
		}

		// Token: 0x0600886F RID: 34927 RVA: 0x002D19A4 File Offset: 0x002CFBA4
		public int Check(int UserID)
		{
			int num = -1;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", UserID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[1].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Feedback_Check", array);
				num = (int)array[1].Value;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return num;
		}
	}
}
