﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E80 RID: 3712
	public class CE1174 : BasePetEffect
	{
		// Token: 0x06008095 RID: 32917 RVA: 0x002A9934 File Offset: 0x002A7B34
		public CE1174(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1174, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008096 RID: 32918 RVA: 0x002A99B4 File Offset: 0x002A7BB4
		public override bool Start(Living living)
		{
			CE1174 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1174) as CE1174;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008097 RID: 32919 RVA: 0x0003241C File Offset: 0x0003061C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.Game.SendPetBuff(player, base.ElementInfo, true);
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008098 RID: 32920 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008099 RID: 32921 RVA: 0x002A9A14 File Offset: 0x002A7C14
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600809A RID: 32922 RVA: 0x00032459 File Offset: 0x00030659
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004FBB RID: 20411
		private int m_type = 0;

		// Token: 0x04004FBC RID: 20412
		private int m_count = 0;

		// Token: 0x04004FBD RID: 20413
		private int m_probability = 0;

		// Token: 0x04004FBE RID: 20414
		private int m_delay = 0;

		// Token: 0x04004FBF RID: 20415
		private int m_coldDown = 0;

		// Token: 0x04004FC0 RID: 20416
		private int m_currentId;

		// Token: 0x04004FC1 RID: 20417
		private int m_added = 0;
	}
}
