﻿using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace Game.Manager.Data
{
	// Token: 0x02000C7E RID: 3198
	public partial class EditTreasure : Form
	{
		// Token: 0x0600711E RID: 28958 RVA: 0x00255A1C File Offset: 0x00253C1C
		public EditTreasure()
		{
			this.InitializeComponent();
		}

		// Token: 0x0600711F RID: 28959 RVA: 0x00255A70 File Offset: 0x00253C70
		private void EditTreasure_Load(object sender, EventArgs e)
		{
			string text = string.Empty;
			text = "SELECT * FROM Treasure_Award";
			this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
			this.InitDataSet();
		}

		// Token: 0x06007120 RID: 28960 RVA: 0x00255AA8 File Offset: 0x00253CA8
		private void InitDataSet()
		{
			this.pageSize = 20;
			this.nMax = this.dtInfo.Rows.Count;
			this.pageCount = this.nMax / this.pageSize;
			bool flag = this.nMax % this.pageSize > 0;
			if (flag)
			{
				this.pageCount++;
			}
			this.pageCurrent = 1;
			this.nCurrent = 0;
			this.LoadData();
		}

		// Token: 0x06007121 RID: 28961 RVA: 0x00255B20 File Offset: 0x00253D20
		private void LoadData()
		{
			DataTable dataTable = this.dtInfo.Clone();
			int num = ((this.pageCurrent != this.pageCount) ? (this.pageSize * this.pageCurrent) : this.nMax);
			int num2 = this.nCurrent;
			this.lblPageCount.Text = this.pageCount.ToString();
			this.txtCurrentPage.Text = Convert.ToString(this.pageCurrent);
			for (int i = num2; i < num; i++)
			{
				dataTable.ImportRow(this.dtInfo.Rows[i]);
				this.nCurrent++;
			}
			this.bdsInfo.DataSource = dataTable;
			this.bdnInfo.BindingSource = this.bdsInfo;
			this.EditTreasureBox.DataSource = this.bdsInfo;
			string text = string.Empty;
			string empty = string.Empty;
			DataTable treasureList = ConnectDataBase.GetTreasureList();
			for (int j = 0; j < dataTable.Rows.Count; j++)
			{
				text = string.Format("SELECT TemplateID, Name FROM Shop_Goods where TemplateID = {0}", dataTable.Rows[j]["TemplateID"]);
				this.EditTreasureBox.Rows[j].Cells["ItemName"].Value = ((ConnectDataBase.GetDataSet(text).Tables[0].Rows.Count == 0) ? "未知" : ConnectDataBase.GetDataSet(text).Tables[0].Rows[0]["Name"].ToString());
			}
		}

		// Token: 0x06007122 RID: 28962 RVA: 0x00255CDC File Offset: 0x00253EDC
		private void Name_Button_Click(object sender, EventArgs e)
		{
			string text = string.Empty;
			bool flag = this.Name_Text.Text != "";
			if (flag)
			{
				text = "SELECT * FROM Treasure_Award where Type = " + this.Name_Text.Text;
				this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
				this.InitDataSet();
			}
			else
			{
				text = "SELECT * FROM Drop_Item";
				this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
				this.InitDataSet();
			}
		}

		// Token: 0x06007123 RID: 28963 RVA: 0x00255D68 File Offset: 0x00253F68
		private void LastPage_Click(object sender, EventArgs e)
		{
			this.pageCurrent--;
			bool flag = this.pageCurrent <= 0;
			if (flag)
			{
				MessageBox.Show("已经是第一页，请点击“下一页”查看！");
			}
			else
			{
				this.nCurrent = this.pageSize * (this.pageCurrent - 1);
				this.LoadData();
			}
		}

		// Token: 0x06007124 RID: 28964 RVA: 0x00255DC0 File Offset: 0x00253FC0
		private void NextPage_Click(object sender, EventArgs e)
		{
			this.pageCurrent++;
			bool flag = this.pageCurrent > this.pageCount;
			if (flag)
			{
				MessageBox.Show("已经是最后一页，请点击“上一页”查看！");
			}
			else
			{
				this.nCurrent = this.pageSize * (this.pageCurrent - 1);
				this.LoadData();
			}
		}

		// Token: 0x06007125 RID: 28965 RVA: 0x00255E18 File Offset: 0x00254018
		private void EditTreasureBox_CellEndEdit(object sender, DataGridViewCellEventArgs e)
		{
			string dataPropertyName = this.EditTreasureBox.Columns[e.ColumnIndex].DataPropertyName;
			string text = this.EditTreasureBox.Rows[e.RowIndex].Cells["Id"].Value.ToString();
			string text2 = this.EditTreasureBox.CurrentCell.Value.ToString();
			string text3 = string.Concat(new string[] { "Update [dbo].[Treasure_Award] set ", dataPropertyName, "='", text2, "'Where Id = ", text });
			ConnectDataBase.GetNonQueryEffectedRow(text3);
		}

		// Token: 0x04003CF4 RID: 15604
		private int pageSize = 0;

		// Token: 0x04003CF5 RID: 15605
		private int nMax = 0;

		// Token: 0x04003CF6 RID: 15606
		private int pageCount = 0;

		// Token: 0x04003CF7 RID: 15607
		private int pageCurrent = 0;

		// Token: 0x04003CF8 RID: 15608
		private int nCurrent = 0;

		// Token: 0x04003CF9 RID: 15609
		private DataTable dtInfo = new DataTable();
	}
}
