﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D72 RID: 3442
	public class PE1215 : BasePetEffect
	{
		// Token: 0x06007AD1 RID: 31441 RVA: 0x00290730 File Offset: 0x0028E930
		public PE1215(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1215, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AD2 RID: 31442 RVA: 0x002907B0 File Offset: 0x0028E9B0
		public override bool Start(Living living)
		{
			PE1215 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1215) as PE1215;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AD3 RID: 31443 RVA: 0x0002EAD0 File Offset: 0x0002CCD0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.player_beginNextTurn;
		}

		// Token: 0x06007AD4 RID: 31444 RVA: 0x0002EAE6 File Offset: 0x0002CCE6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.player_beginNextTurn;
		}

		// Token: 0x06007AD5 RID: 31445 RVA: 0x00290810 File Offset: 0x0028EA10
		public void player_beginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 200;
				living.BaseGuard += (double)this.m_added;
			}
		}

		// Token: 0x0400485E RID: 18526
		private int m_type = 0;

		// Token: 0x0400485F RID: 18527
		private int m_count = 0;

		// Token: 0x04004860 RID: 18528
		private int m_probability = 0;

		// Token: 0x04004861 RID: 18529
		private int m_delay = 0;

		// Token: 0x04004862 RID: 18530
		private int m_coldDown = 0;

		// Token: 0x04004863 RID: 18531
		private int m_currentId;

		// Token: 0x04004864 RID: 18532
		private int m_added = 0;
	}
}
