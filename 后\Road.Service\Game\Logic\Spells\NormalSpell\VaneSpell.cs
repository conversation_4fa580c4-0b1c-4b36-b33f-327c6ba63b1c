﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.NormalSpell
{
	// Token: 0x02000CBD RID: 3261
	[SpellAttibute(7)]
	public class VaneSpell : ISpellHandler
	{
		// Token: 0x060074FC RID: 29948 RVA: 0x0002B59B File Offset: 0x0002979B
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			game.UpdateWind(0f - game.Wind, true);
		}
	}
}
