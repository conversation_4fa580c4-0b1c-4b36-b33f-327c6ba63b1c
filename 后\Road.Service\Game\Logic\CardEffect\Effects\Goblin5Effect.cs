﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F29 RID: 3881
	public class Goblin5Effect : BaseCardEffect
	{
		// Token: 0x0600842D RID: 33837 RVA: 0x002B6F44 File Offset: 0x002B5144
		public Goblin5Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.Goblin5, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x0600842E RID: 33838 RVA: 0x002B6FB4 File Offset: 0x002B51B4
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.Goblin5) is Goblin5Effect;
			return flag || base.Start(living);
		}

		// Token: 0x0600842F RID: 33839 RVA: 0x00034C24 File Offset: 0x00032E24
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x06008430 RID: 33840 RVA: 0x00034C3A File Offset: 0x00032E3A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x06008431 RID: 33841 RVA: 0x002B6FEC File Offset: 0x002B51EC
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.Attack -= (double)this.m_added;
				player.Agility -= (double)this.m_added;
				player.Lucky -= (double)this.m_added;
				player.Defence -= (double)this.m_added;
				this.m_added = 0;
				player.isLockXY = false;
			}
			bool flag2 = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 5;
			if (flag2)
			{
				this.m_added = this.m_value;
				player.Attack += (double)this.m_added;
				player.Agility += (double)this.m_added;
				player.Lucky += (double)this.m_added;
				player.Defence += (double)this.m_added;
				player.isLockXY = true;
				player.Game.SendMessage(player.PlayerDetail, "您激活了龙巢之战5件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活龙巢之战5件套卡.", 3);
			}
		}

		// Token: 0x04005270 RID: 21104
		private int m_indexValue = 0;

		// Token: 0x04005271 RID: 21105
		private int m_value = 0;

		// Token: 0x04005272 RID: 21106
		private int m_added = 0;
	}
}
