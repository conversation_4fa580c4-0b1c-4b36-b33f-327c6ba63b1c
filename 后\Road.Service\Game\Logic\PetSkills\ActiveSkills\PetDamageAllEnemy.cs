﻿using System;
using System.Collections.Generic;
using System.Threading;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D37 RID: 3383
	public class PetDamageAllEnemy : BasePetEffect
	{
		// Token: 0x06007982 RID: 31106 RVA: 0x0028ABFC File Offset: 0x00288DFC
		public PetDamageAllEnemy(int probability, int skillId, string elementID)
			: base(ePetEffectType.PetDamageAllEnemy, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
			bool flag = !(elementID == "1161");
			if (flag)
			{
				bool flag2 = elementID == "1162";
				if (flag2)
				{
					this.m_value = 5000;
				}
			}
			else
			{
				this.m_value = 3000;
			}
			this.m_elementID = elementID;
		}

		// Token: 0x06007983 RID: 31107 RVA: 0x0028AC78 File Offset: 0x00288E78
		public override bool Start(Living living)
		{
			PetDamageAllEnemy petDamageAllEnemy = living.PetEffectList.GetOfType(ePetEffectType.PetDamageAllEnemy) as PetDamageAllEnemy;
			bool flag = petDamageAllEnemy != null;
			bool flag2;
			if (flag)
			{
				petDamageAllEnemy.m_probability = ((this.m_probability > petDamageAllEnemy.m_probability) ? this.m_probability : petDamageAllEnemy.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007984 RID: 31108 RVA: 0x0002DAB1 File Offset: 0x0002BCB1
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
		}

		// Token: 0x06007985 RID: 31109 RVA: 0x0002DAC7 File Offset: 0x0002BCC7
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
		}

		// Token: 0x06007986 RID: 31110 RVA: 0x0028ACD8 File Offset: 0x00288ED8
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				int num = 0;
				string elementID = this.m_elementID;
				string text = elementID;
				bool flag2 = !(text == "2668");
				if (flag2)
				{
					bool flag3 = text == "2669";
					if (flag3)
					{
						num = (int)(player.BaseDamage * 150.0 / 100.0 + player.BaseGuard * 300.0 / 100.0);
						this.m_percent = true;
					}
				}
				else
				{
					num = (int)(player.BaseDamage * 120.0 / 100.0 + player.BaseGuard * 200.0 / 100.0);
					this.m_percent = true;
				}
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.Game.sendShowPicSkil(player2, base.ElementInfo, true);
					player2.SyncAtTime = true;
					bool flag4 = this.m_elementID == "3217";
					if (flag4)
					{
						int num2 = this.MakeDamage(player, player2);
						num2 = num2 * 120 / 100;
						int num3 = 0;
						player.OnTakedDamage(player, ref num2, ref num3);
						double num4 = (double)Math.Round(player2.Blood / player2.MaxBlood, 3) * 100.0;
						bool flag5 = num4 <= 10.0;
						if (flag5)
						{
							player2.Blood = 0;
							player2.Game.SendGameUpdateHealth(player2, 1, int.MaxValue);
							Thread.Sleep(200);
							player2.Die();
							if (player != null)
							{
								player.OnAfterKillingLiving(player2, 999999999, 0);
							}
						}
						player2.AddBlood(-num2, 1);
					}
					else
					{
						bool flag6 = this.m_elementID == "3227";
						if (flag6)
						{
							int num5 = this.MakeDamage(player, player2);
							num5 = num5 * 200 / 100;
							int num6 = 0;
							player.OnTakedDamage(player, ref num5, ref num6);
							bool flag7 = player2.PlayerDetail.PlayerCharacter.IsAutoBot && num5 > player2.Blood * 10 / 100;
							if (flag7)
							{
								num5 = player2.Blood * 10 / 100;
							}
							player2.AddBlood(-num5, 1);
						}
						else
						{
							bool percent = this.m_percent;
							if (percent)
							{
								player2.AddBlood(-num, 1);
							}
							else
							{
								player2.AddBlood(-this.m_value, 1);
							}
						}
					}
					player2.SyncAtTime = false;
					bool flag8 = this.m_elementID == "3227";
					if (flag8)
					{
						double num7 = (double)Math.Round(player2.Blood / player2.MaxBlood, 3) * 100.0;
						bool flag9 = num7 <= 12.0;
						if (flag9)
						{
							player2.Blood = 0;
							player2.Game.SendGameUpdateHealth(player2, 1, int.MaxValue);
							Thread.Sleep(200);
							player2.Die();
							if (player != null)
							{
								player.OnAfterKillingLiving(player2, 999999999, 0);
							}
						}
					}
					bool flag10 = player2.Blood <= 0;
					if (flag10)
					{
						player2.Die();
						if (player != null)
						{
							player.OnAfterKillingLiving(player2, 999999999, 0);
						}
					}
				}
			}
		}

		// Token: 0x06007987 RID: 31111 RVA: 0x0028B0CC File Offset: 0x002892CC
		private int MakeDamage(Player player, Living target)
		{
			double num = player.BaseDamage;
			double num2 = target.BaseGuard;
			double num3 = target.Defence;
			double attack = player.Attack;
			bool flag = target.AddArmor && (target as Player).DeputyWeapon != null;
			if (flag)
			{
				int num4 = (int)target.getHertAddition((target as Player).DeputyWeapon);
				num2 += (double)num4;
				num3 += (double)num4;
			}
			bool ignoreArmor = player.IgnoreArmor;
			if (ignoreArmor)
			{
				num2 *= 0.6;
				num3 *= 0.6;
			}
			bool flag2 = player.AddDamage > 0;
			if (flag2)
			{
				num += num / 100.0 * (double)player.AddDamage;
			}
			bool flag3 = player.IgnoreGuard > 0 && !player.IgnoreArmor;
			if (flag3)
			{
				num3 -= num3 / 100.0 * (double)player.IgnoreGuard;
				num2 -= num2 / 100.0 * (double)player.IgnoreGuard;
			}
			float currentShootMinus = player.CurrentShootMinus;
			double num5 = num2 / 12000.0;
			double num6 = num3 / 80000.0;
			bool flag4 = num5 >= 0.9;
			if (flag4)
			{
				num5 = 0.9;
			}
			bool flag5 = num6 >= 0.7;
			if (flag5)
			{
				num6 = 0.7;
			}
			double num7 = num * (1.0 + attack * 0.0004) * (1.0 - num5) * (1.0 - num6);
			double num8 = player.MagicAttack * 0.8 - target.MagicDefence * 0.7;
			bool flag6 = num8 <= 0.0;
			if (flag6)
			{
				num8 = 0.0;
			}
			num7 += num8 / 3.0;
			bool keepLife = target.KeepLife;
			int num9;
			if (keepLife)
			{
				num9 = 0;
			}
			else
			{
				bool flag7 = num7 <= 0.0;
				if (flag7)
				{
					num9 = 1;
				}
				else
				{
					num9 = (int)num7;
				}
			}
			return num9;
		}

		// Token: 0x0400472E RID: 18222
		private int m_probability;

		// Token: 0x0400472F RID: 18223
		private int m_currentId;

		// Token: 0x04004730 RID: 18224
		private int m_value;

		// Token: 0x04004731 RID: 18225
		private string m_elementID;

		// Token: 0x04004732 RID: 18226
		private bool m_percent;
	}
}
