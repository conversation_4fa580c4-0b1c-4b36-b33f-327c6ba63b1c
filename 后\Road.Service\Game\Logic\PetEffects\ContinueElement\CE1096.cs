﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E66 RID: 3686
	public class CE1096 : BasePetEffect
	{
		// Token: 0x06007FF0 RID: 32752 RVA: 0x002A73C8 File Offset: 0x002A55C8
		public CE1096(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1096, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FF1 RID: 32753 RVA: 0x002A7448 File Offset: 0x002A5648
		public override bool Start(Living living)
		{
			CE1096 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1096) as CE1096;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FF2 RID: 32754 RVA: 0x002A74A8 File Offset: 0x002A56A8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.Agility += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007FF3 RID: 32755 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FF4 RID: 32756 RVA: 0x002A7508 File Offset: 0x002A5708
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007FF5 RID: 32757 RVA: 0x00031CF5 File Offset: 0x0002FEF5
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Agility -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F05 RID: 20229
		private int m_type = 0;

		// Token: 0x04004F06 RID: 20230
		private int m_count = 0;

		// Token: 0x04004F07 RID: 20231
		private int m_probability = 0;

		// Token: 0x04004F08 RID: 20232
		private int m_delay = 0;

		// Token: 0x04004F09 RID: 20233
		private int m_coldDown = 0;

		// Token: 0x04004F0A RID: 20234
		private int m_currentId;

		// Token: 0x04004F0B RID: 20235
		private int m_added = 0;
	}
}
