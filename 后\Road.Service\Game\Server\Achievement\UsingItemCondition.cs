﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C76 RID: 3190
	public class UsingItemCondition : BaseCondition
	{
		// Token: 0x060070D7 RID: 28887 RVA: 0x0002A7EE File Offset: 0x000289EE
		public UsingItemCondition(BaseAchievement quest, AchievementConditionInfo info, int templateid, int value)
			: base(quest, info, value)
		{
			this.item = templateid;
		}

		// Token: 0x060070D8 RID: 28888 RVA: 0x0002A803 File Offset: 0x00028A03
		public override void AddTrigger(GamePlayer player)
		{
			player.AfterUsingItem += this.player_AfterUsingItem;
		}

		// Token: 0x060070D9 RID: 28889 RVA: 0x00250644 File Offset: 0x0024E844
		private void player_AfterUsingItem(int id)
		{
			bool flag = this.item == id;
			if (flag)
			{
				int value = base.Value;
				base.Value = value + 1;
			}
		}

		// Token: 0x060070DA RID: 28890 RVA: 0x0002A819 File Offset: 0x00028A19
		public override void RemoveTrigger(GamePlayer player)
		{
			player.AfterUsingItem -= this.player_AfterUsingItem;
		}

		// Token: 0x060070DB RID: 28891 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}

		// Token: 0x04003C64 RID: 15460
		private int item;
	}
}
