﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DA1 RID: 3489
	public class AE1039 : BasePetEffect
	{
		// Token: 0x06007BC4 RID: 31684 RVA: 0x002946A8 File Offset: 0x002928A8
		public AE1039(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1039, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BC5 RID: 31685 RVA: 0x00294728 File Offset: 0x00292928
		public override bool Start(Living living)
		{
			AE1039 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1039) as AE1039;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BC6 RID: 31686 RVA: 0x0002F4FD File Offset: 0x0002D6FD
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007BC7 RID: 31687 RVA: 0x0002F526 File Offset: 0x0002D726
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007BC8 RID: 31688 RVA: 0x00294788 File Offset: 0x00292988
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 150;
				player.CurrentDamagePlus += (float)(this.m_added / 100);
			}
		}

		// Token: 0x06007BC9 RID: 31689 RVA: 0x002947D4 File Offset: 0x002929D4
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_added / 100);
				this.m_added = 0;
			}
		}

		// Token: 0x040049A1 RID: 18849
		private int m_type = 0;

		// Token: 0x040049A2 RID: 18850
		private int m_count = 0;

		// Token: 0x040049A3 RID: 18851
		private int m_probability = 0;

		// Token: 0x040049A4 RID: 18852
		private int m_delay = 0;

		// Token: 0x040049A5 RID: 18853
		private int m_coldDown = 0;

		// Token: 0x040049A6 RID: 18854
		private int m_currentId;

		// Token: 0x040049A7 RID: 18855
		private int m_added = 0;
	}
}
