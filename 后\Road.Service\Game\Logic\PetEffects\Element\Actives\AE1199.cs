﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DF9 RID: 3577
	public class AE1199 : BasePetEffect
	{
		// Token: 0x06007D8D RID: 32141 RVA: 0x0029C7B8 File Offset: 0x0029A9B8
		public AE1199(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1199, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D8E RID: 32142 RVA: 0x0029C838 File Offset: 0x0029AA38
		public override bool Start(Living living)
		{
			AE1199 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1199) as AE1199;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D8F RID: 32143 RVA: 0x0003060D File Offset: 0x0002E80D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D90 RID: 32144 RVA: 0x00030623 File Offset: 0x0002E823
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D91 RID: 32145 RVA: 0x0029C898 File Offset: 0x0029AA98
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1199(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004C07 RID: 19463
		private int m_type = 0;

		// Token: 0x04004C08 RID: 19464
		private int m_count = 0;

		// Token: 0x04004C09 RID: 19465
		private int m_probability = 0;

		// Token: 0x04004C0A RID: 19466
		private int m_delay = 0;

		// Token: 0x04004C0B RID: 19467
		private int m_coldDown = 0;

		// Token: 0x04004C0C RID: 19468
		private int m_currentId;

		// Token: 0x04004C0D RID: 19469
		private int m_added = 0;
	}
}
