﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C70 RID: 3184
	public class PropertisCharacterCondition : BaseCondition
	{
		// Token: 0x060070B9 RID: 28857 RVA: 0x0002A6E7 File Offset: 0x000288E7
		public PropertisCharacterCondition(BaseAchievement quest, AchievementConditionInfo info, int value, string type)
			: base(quest, info, value)
		{
			this.m_type = type;
		}

		// Token: 0x060070BA RID: 28858 RVA: 0x0002A6FC File Offset: 0x000288FC
		public override void AddTrigger(GamePlayer player)
		{
			player.PropertisChange += this.player_PlayerPropertisChange;
		}

		// Token: 0x060070BB RID: 28859 RVA: 0x00250494 File Offset: 0x0024E694
		private void player_PlayerPropertisChange(GamePlayer m_player)
		{
			string type = this.m_type;
			string text = type;
			if (!(text == "defence"))
			{
				if (!(text == "luck"))
				{
					if (!(text == "agility"))
					{
						if (!(text == "attack"))
						{
							if (text == "fightpower")
							{
								base.Value = m_player.PlayerCharacter.FightPower;
							}
						}
						else
						{
							base.Value = m_player.PlayerCharacter.Attack;
						}
					}
					else
					{
						base.Value = m_player.PlayerCharacter.Agility;
					}
				}
				else
				{
					base.Value = m_player.PlayerCharacter.Luck;
				}
			}
			else
			{
				base.Value = m_player.PlayerCharacter.Defence;
			}
		}

		// Token: 0x060070BC RID: 28860 RVA: 0x0002A712 File Offset: 0x00028912
		public override void RemoveTrigger(GamePlayer player)
		{
			player.PropertisChange -= this.player_PlayerPropertisChange;
		}

		// Token: 0x060070BD RID: 28861 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}

		// Token: 0x04003C63 RID: 15459
		private string m_type;
	}
}
