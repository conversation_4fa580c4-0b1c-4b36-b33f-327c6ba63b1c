﻿using System;

namespace Game.Base
{
	// Token: 0x02000F6C RID: 3948
	public abstract class AbstractCommandHandler
	{
		// Token: 0x06008538 RID: 34104 RVA: 0x0003578E File Offset: 0x0003398E
		public virtual void DisplayMessage(BaseClient client, string format, params object[] args)
		{
			this.DisplayMessage(client, string.Format(format, args));
		}

		// Token: 0x06008539 RID: 34105 RVA: 0x000357A0 File Offset: 0x000339A0
		public virtual void DisplayMessage(BaseClient client, string message)
		{
			if (client != null)
			{
				client.DisplayMessage(message);
			}
		}

		// Token: 0x0600853A RID: 34106 RVA: 0x002BA790 File Offset: 0x002B8990
		public virtual void DisplaySyntax(BaseClient client)
		{
			bool flag = client == null;
			if (!flag)
			{
				CmdAttribute[] array = (CmdAttribute[])base.GetType().GetCustomAttributes(typeof(CmdAttribute), false);
				bool flag2 = array.Length != 0;
				if (flag2)
				{
					client.DisplayMessage(array[0].Description);
					string[] usage = array[0].Usage;
					string[] array2 = usage;
					string[] array3 = array2;
					foreach (string text in array3)
					{
						client.DisplayMessage(text);
					}
				}
			}
		}
	}
}
