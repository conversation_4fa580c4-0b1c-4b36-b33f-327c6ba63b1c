﻿using System;
using System.Collections;
using System.Reflection;
using Game.Logic.CardEffect;
using Game.Logic.Phy.Object;
using log4net;

namespace Game.Logic.CardEffects
{
	// Token: 0x02000F1C RID: 3868
	public class CardEffectList
	{
		// Token: 0x170014A7 RID: 5287
		// (get) Token: 0x060083DE RID: 33758 RVA: 0x00034846 File Offset: 0x00032A46
		public ArrayList List
		{
			get
			{
				return this.m_effects;
			}
		}

		// Token: 0x060083DF RID: 33759 RVA: 0x0003484E File Offset: 0x00032A4E
		public CardEffectList(Living owner, int immunity)
		{
			this.m_owner = owner;
			this.m_effects = new ArrayList(5);
			this.m_immunity = immunity;
		}

		// Token: 0x060083E0 RID: 33760 RVA: 0x002B5B90 File Offset: 0x002B3D90
		public bool CanAddEffect(int id)
		{
			bool flag = id > 999 || id < 0;
			return flag || ((1 << id - 1) & this.m_immunity) == 0;
		}

		// Token: 0x060083E1 RID: 33761 RVA: 0x002B5BCC File Offset: 0x002B3DCC
		public virtual bool Add(AbstractCardEffect effect)
		{
			bool flag = this.CanAddEffect(effect.TypeValue);
			bool flag3;
			if (flag)
			{
				ArrayList effects = this.m_effects;
				lock (effects)
				{
					this.m_effects.Add(effect);
				}
				effect.OnAttached(this.m_owner);
				this.OnEffectsChanged(effect);
				flag3 = true;
			}
			else
			{
				flag3 = false;
			}
			return flag3;
		}

		// Token: 0x060083E2 RID: 33762 RVA: 0x002B5C48 File Offset: 0x002B3E48
		public virtual bool Pause(AbstractCardEffect effect)
		{
			ArrayList effects = this.m_effects;
			lock (effects)
			{
				int num = this.m_effects.IndexOf(effect);
				bool flag2 = num < 0;
				if (flag2)
				{
					return false;
				}
			}
			effect.OnPaused(this.m_owner);
			this.OnEffectsChanged(effect);
			return true;
		}

		// Token: 0x060083E3 RID: 33763 RVA: 0x002B5CC0 File Offset: 0x002B3EC0
		public virtual bool Remove(AbstractCardEffect effect)
		{
			int num = -1;
			ArrayList effects = this.m_effects;
			lock (effects)
			{
				num = this.m_effects.IndexOf(effect);
				bool flag2 = num < 0;
				if (flag2)
				{
					return false;
				}
				this.m_effects.RemoveAt(num);
			}
			bool flag3 = num != -1;
			bool flag4;
			if (flag3)
			{
				effect.OnRemoved(this.m_owner);
				this.OnEffectsChanged(effect);
				flag4 = true;
			}
			else
			{
				flag4 = false;
			}
			return flag4;
		}

		// Token: 0x060083E4 RID: 33764 RVA: 0x002B5D58 File Offset: 0x002B3F58
		public virtual void OnEffectsChanged(AbstractCardEffect changedEffect)
		{
			bool flag = this.m_changesCount <= 0;
			if (flag)
			{
				this.UpdateChangedEffects();
			}
		}

		// Token: 0x060083E5 RID: 33765 RVA: 0x00034872 File Offset: 0x00032A72
		public void BeginChanges()
		{
			this.m_changesCount += 1;
		}

		// Token: 0x060083E6 RID: 33766 RVA: 0x002B5D84 File Offset: 0x002B3F84
		public virtual void CommitChanges()
		{
			sbyte b = this.m_changesCount - 1;
			this.m_changesCount = b;
			bool flag = b < 0;
			if (flag)
			{
				bool isWarnEnabled = CardEffectList.log.IsWarnEnabled;
				if (isWarnEnabled)
				{
					CardEffectList.log.Warn("changes count is less than zero, forgot BeginChanges()?\n" + Environment.StackTrace);
				}
				this.m_changesCount = 0;
			}
			bool flag2 = this.m_changesCount == 0;
			if (flag2)
			{
				this.UpdateChangedEffects();
			}
		}

		// Token: 0x060083E7 RID: 33767 RVA: 0x00005683 File Offset: 0x00003883
		protected virtual void UpdateChangedEffects()
		{
		}

		// Token: 0x060083E8 RID: 33768 RVA: 0x002B5DFC File Offset: 0x002B3FFC
		public virtual AbstractCardEffect GetOfType(eCardEffectType effectType)
		{
			ArrayList effects = this.m_effects;
			lock (effects)
			{
				foreach (object obj in this.m_effects)
				{
					AbstractCardEffect abstractCardEffect = (AbstractCardEffect)obj;
					bool flag2 = abstractCardEffect.Type == effectType;
					if (flag2)
					{
						return abstractCardEffect;
					}
				}
			}
			return null;
		}

		// Token: 0x060083E9 RID: 33769 RVA: 0x002B5EA0 File Offset: 0x002B40A0
		public virtual IList GetAllOfType(Type effectType)
		{
			ArrayList arrayList = new ArrayList();
			ArrayList effects = this.m_effects;
			lock (effects)
			{
				foreach (object obj in this.m_effects)
				{
					AbstractCardEffect abstractCardEffect = (AbstractCardEffect)obj;
					bool flag2 = abstractCardEffect.GetType().Equals(effectType);
					if (flag2)
					{
						arrayList.Add(abstractCardEffect);
					}
				}
			}
			return arrayList;
		}

		// Token: 0x060083EA RID: 33770 RVA: 0x002B5F54 File Offset: 0x002B4154
		public void StopEffect(Type effectType)
		{
			IList allOfType = this.GetAllOfType(effectType);
			this.BeginChanges();
			foreach (object obj in allOfType)
			{
				AbstractCardEffect abstractCardEffect = (AbstractCardEffect)obj;
				abstractCardEffect.Stop();
			}
			this.CommitChanges();
		}

		// Token: 0x060083EB RID: 33771 RVA: 0x002B5FC4 File Offset: 0x002B41C4
		public void StopAllEffect()
		{
			bool flag = this.m_effects.Count > 0;
			if (flag)
			{
				AbstractCardEffect[] array = new AbstractCardEffect[this.m_effects.Count];
				this.m_effects.CopyTo(array);
				AbstractCardEffect[] array2 = array;
				AbstractCardEffect[] array3 = array2;
				AbstractCardEffect[] array4 = array3;
				foreach (AbstractCardEffect abstractCardEffect in array4)
				{
					abstractCardEffect.Stop();
				}
				this.m_effects.Clear();
			}
		}

		// Token: 0x0400522C RID: 21036
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x0400522D RID: 21037
		protected ArrayList m_effects;

		// Token: 0x0400522E RID: 21038
		protected readonly Living m_owner;

		// Token: 0x0400522F RID: 21039
		protected volatile sbyte m_changesCount;

		// Token: 0x04005230 RID: 21040
		protected int m_immunity;
	}
}
