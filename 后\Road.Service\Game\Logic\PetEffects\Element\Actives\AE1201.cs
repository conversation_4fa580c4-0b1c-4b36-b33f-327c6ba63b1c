﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DFA RID: 3578
	public class AE1201 : BasePetEffect
	{
		// Token: 0x06007D92 RID: 32146 RVA: 0x0029C90C File Offset: 0x0029AB0C
		public AE1201(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1201, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D93 RID: 32147 RVA: 0x0029C98C File Offset: 0x0029AB8C
		public override bool Start(Living living)
		{
			AE1201 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1201) as AE1201;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D94 RID: 32148 RVA: 0x00030639 File Offset: 0x0002E839
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007D95 RID: 32149 RVA: 0x0029C9EC File Offset: 0x0029ABEC
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007D96 RID: 32150 RVA: 0x0029CA1C File Offset: 0x0029AC1C
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				this.IsTrigger = false;
				target.AddPetEffect(new CE1201(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007D97 RID: 32151 RVA: 0x00030662 File Offset: 0x0002E862
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004C0E RID: 19470
		private int m_type = 0;

		// Token: 0x04004C0F RID: 19471
		private int m_count = 0;

		// Token: 0x04004C10 RID: 19472
		private int m_probability = 0;

		// Token: 0x04004C11 RID: 19473
		private int m_delay = 0;

		// Token: 0x04004C12 RID: 19474
		private int m_coldDown = 0;

		// Token: 0x04004C13 RID: 19475
		private int m_currentId;

		// Token: 0x04004C14 RID: 19476
		private int m_added = 0;
	}
}
