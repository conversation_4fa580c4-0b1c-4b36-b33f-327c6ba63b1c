﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E6F RID: 3695
	public class CE1116 : BasePetEffect
	{
		// Token: 0x06008027 RID: 32807 RVA: 0x002A806C File Offset: 0x002A626C
		public CE1116(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1116, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008028 RID: 32808 RVA: 0x002A80E8 File Offset: 0x002A62E8
		public override bool Start(Living living)
		{
			CE1116 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1116) as CE1116;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008029 RID: 32809 RVA: 0x002A8144 File Offset: 0x002A6344
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
			this.IsTrigger = true;
		}

		// Token: 0x0600802A RID: 32810 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600802B RID: 32811 RVA: 0x002A8194 File Offset: 0x002A6394
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = living != target && this.IsTrigger;
			if (flag)
			{
				this.m_added = 1000;
				bool isLiving = living.IsLiving;
				if (isLiving)
				{
					living.SyncAtTime = true;
					living.AddBlood(this.m_added);
					living.SyncAtTime = false;
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x0600802C RID: 32812 RVA: 0x002A81F0 File Offset: 0x002A63F0
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			this.IsTrigger = true;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.m_added = 0;
				this.Stop();
			}
		}

		// Token: 0x0600802D RID: 32813 RVA: 0x00031EBC File Offset: 0x000300BC
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x04004F44 RID: 20292
		private int m_type = 0;

		// Token: 0x04004F45 RID: 20293
		private int m_count = 0;

		// Token: 0x04004F46 RID: 20294
		private int m_probability = 0;

		// Token: 0x04004F47 RID: 20295
		private int m_delay = 0;

		// Token: 0x04004F48 RID: 20296
		private int m_coldDown = 0;

		// Token: 0x04004F49 RID: 20297
		private int m_currentId;

		// Token: 0x04004F4A RID: 20298
		private int m_added = 0;
	}
}
