﻿using System;
using System.Collections.Specialized;
using System.Net;
using System.Net.Sockets;
using System.Reflection;
using log4net;

namespace Game.Base
{
	// Token: 0x02000F6F RID: 3951
	public class BaseServer
	{
		// Token: 0x170014BB RID: 5307
		// (get) Token: 0x06008567 RID: 34151 RVA: 0x000358EC File Offset: 0x00033AEC
		public int ClientCount
		{
			get
			{
				return this._clients.Count;
			}
		}

		// Token: 0x06008568 RID: 34152 RVA: 0x000358F9 File Offset: 0x00033AF9
		public BaseServer()
		{
			this.ac_event = new SocketAsyncEventArgs();
			this.ac_event.Completed += this.AcceptAsyncCompleted;
		}

		// Token: 0x06008569 RID: 34153 RVA: 0x002BAFC0 File Offset: 0x002B91C0
		private void AcceptAsync()
		{
			try
			{
				bool flag = this._linstener != null;
				if (flag)
				{
					SocketAsyncEventArgs socketAsyncEventArgs = new SocketAsyncEventArgs();
					socketAsyncEventArgs.Completed += this.AcceptAsyncCompleted;
					this._linstener.AcceptAsync(socketAsyncEventArgs);
				}
			}
			catch (Exception ex)
			{
				BaseServer.log.Error("AcceptAsync is error!", ex);
			}
		}

		// Token: 0x0600856A RID: 34154 RVA: 0x002BB030 File Offset: 0x002B9230
		private void AcceptAsyncCompleted(object sender, SocketAsyncEventArgs e)
		{
			Socket socket = null;
			try
			{
				socket = e.AcceptSocket;
				socket.SendBufferSize = BaseServer.SEND_BUFF_SIZE;
				BaseClient newClient = this.GetNewClient();
				try
				{
					bool isInfoEnabled = BaseServer.log.IsInfoEnabled;
					if (isInfoEnabled)
					{
						string text = (socket.Connected ? socket.RemoteEndPoint.ToString() : "socket disconnected");
						BaseServer.log.Info("Incoming connection from " + text);
					}
					object syncRoot = this._clients.SyncRoot;
					lock (syncRoot)
					{
						this._clients.Add(newClient, newClient);
						newClient.Disconnected += this.client_Disconnected;
					}
					newClient.Connect(socket);
					newClient.ReceiveAsync();
				}
				catch (Exception ex)
				{
					BaseServer.log.ErrorFormat("create client failed:{0}", ex);
					newClient.Disconnect();
				}
			}
			catch
			{
				bool flag2 = socket != null;
				if (flag2)
				{
					try
					{
						socket.Close();
					}
					catch
					{
					}
				}
			}
			finally
			{
				e.Dispose();
				this.AcceptAsync();
			}
		}

		// Token: 0x0600856B RID: 34155 RVA: 0x00035931 File Offset: 0x00033B31
		private void client_Disconnected(BaseClient client)
		{
			client.Disconnected -= this.client_Disconnected;
			this.RemoveClient(client);
		}

		// Token: 0x0600856C RID: 34156 RVA: 0x002BB190 File Offset: 0x002B9390
		protected virtual BaseClient GetNewClient()
		{
			return new BaseClient(new byte[8192], new byte[8192]);
		}

		// Token: 0x0600856D RID: 34157 RVA: 0x002BB1BC File Offset: 0x002B93BC
		public virtual bool InitSocket(IPAddress ip, int port)
		{
			try
			{
				this._linstener = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
				this._linstener.Bind(new IPEndPoint(ip, port));
			}
			catch (Exception ex)
			{
				BaseServer.log.Error("InitSocket", ex);
				return false;
			}
			return true;
		}

		// Token: 0x0600856E RID: 34158 RVA: 0x002BB21C File Offset: 0x002B941C
		public virtual bool Start()
		{
			bool flag = this._linstener == null;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				try
				{
					this._linstener.Listen(100);
					this.AcceptAsync();
					bool isDebugEnabled = BaseServer.log.IsDebugEnabled;
					if (isDebugEnabled)
					{
						BaseServer.log.Debug("Server is now listening to incoming connections!");
					}
				}
				catch (Exception ex)
				{
					bool isErrorEnabled = BaseServer.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						BaseServer.log.Error("Start", ex);
					}
					bool flag3 = this._linstener != null;
					if (flag3)
					{
						this._linstener.Close();
					}
					return false;
				}
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x0600856F RID: 34159 RVA: 0x002BB2D4 File Offset: 0x002B94D4
		public virtual void Stop()
		{
			BaseServer.log.Debug("Stopping server! - Entering method");
			try
			{
				bool flag = this._linstener != null;
				if (flag)
				{
					Socket linstener = this._linstener;
					this._linstener = null;
					linstener.Close();
					BaseServer.log.Debug("Server is no longer listening for incoming connections!");
				}
			}
			catch (Exception ex)
			{
				BaseServer.log.Error("Stop", ex);
			}
			bool flag2 = this._clients != null;
			if (flag2)
			{
				object syncRoot = this._clients.SyncRoot;
				lock (syncRoot)
				{
					try
					{
						BaseClient[] array = new BaseClient[this._clients.Keys.Count];
						this._clients.Keys.CopyTo(array, 0);
						BaseClient[] array2 = array;
						BaseClient[] array3 = array2;
						BaseClient[] array4 = array3;
						foreach (BaseClient baseClient in array4)
						{
							baseClient.Disconnect();
						}
						BaseServer.log.Debug("Stopping server! - Cleaning up client list!");
					}
					catch (Exception ex2)
					{
						BaseServer.log.Error("Stop", ex2);
					}
				}
			}
			BaseServer.log.Debug("Stopping server! - End of method!");
		}

		// Token: 0x06008570 RID: 34160 RVA: 0x002BB444 File Offset: 0x002B9644
		public virtual void RemoveClient(BaseClient client)
		{
			object syncRoot = this._clients.SyncRoot;
			lock (syncRoot)
			{
				this._clients.Remove(client);
			}
		}

		// Token: 0x06008571 RID: 34161 RVA: 0x002BB498 File Offset: 0x002B9698
		public BaseClient[] GetAllClients()
		{
			object syncRoot = this._clients.SyncRoot;
			BaseClient[] array2;
			lock (syncRoot)
			{
				BaseClient[] array = new BaseClient[this._clients.Count];
				this._clients.Keys.CopyTo(array, 0);
				array2 = array;
			}
			return array2;
		}

		// Token: 0x06008572 RID: 34162 RVA: 0x0003594F File Offset: 0x00033B4F
		public void Dispose()
		{
			this.ac_event.Dispose();
		}

		// Token: 0x0400535A RID: 21338
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x0400535B RID: 21339
		private static readonly int SEND_BUFF_SIZE = 16384;

		// Token: 0x0400535C RID: 21340
		protected readonly HybridDictionary _clients = new HybridDictionary();

		// Token: 0x0400535D RID: 21341
		protected Socket _linstener;

		// Token: 0x0400535E RID: 21342
		protected SocketAsyncEventArgs ac_event;
	}
}
