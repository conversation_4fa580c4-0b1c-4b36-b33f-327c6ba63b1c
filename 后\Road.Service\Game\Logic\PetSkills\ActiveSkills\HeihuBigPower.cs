﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D24 RID: 3364
	public class HeihuBigPower : BasePetEffect
	{
		// Token: 0x0600791A RID: 31002 RVA: 0x0002D3C0 File Offset: 0x0002B5C0
		public HeihuBigPower(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.HeihuBigPower, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x0600791B RID: 31003 RVA: 0x0028929C File Offset: 0x0028749C
		public override bool Start(Living living)
		{
			HeihuBigPower heihuBigPower = living.PetEffectList.GetOfType(ePetEffectType.HeihuBigPower) as HeihuBigPower;
			bool flag = heihuBigPower != null;
			bool flag2;
			if (flag)
			{
				heihuBigPower.m_probability = ((this.m_probability > heihuBigPower.m_probability) ? this.m_probability : heihuBigPower.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600791C RID: 31004 RVA: 0x0002D3F8 File Offset: 0x0002B5F8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
		}

		// Token: 0x0600791D RID: 31005 RVA: 0x0002D40E File Offset: 0x0002B60E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
		}

		// Token: 0x0600791E RID: 31006 RVA: 0x002892FC File Offset: 0x002874FC
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && player.BlackTiger;
			if (flag)
			{
				player.CurrentDamagePlus += 1f;
			}
			else
			{
				bool flag2 = player.PetEffects.CurrentUseSkill != this.m_currentId || !player.WhiteTiger;
				if (!flag2)
				{
					player.SetBall(11463);
					List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
					foreach (Player player2 in allTeamPlayers)
					{
						player2.AddPetEffect(new PetAddAttackEquip(3, base.Info.ID.ToString()), 100);
						player2.Game.SendPlayerPicture(player2, 33, true);
						player2.Game.sendShowPicSkil(player2, base.Info, true);
					}
				}
			}
		}

		// Token: 0x0600791F RID: 31007 RVA: 0x0002D424 File Offset: 0x0002B624
		private void player_AfterPlayerShooted(Player player)
		{
			player.PlayerShoot -= this.onPlayerShoot;
		}

		// Token: 0x06007920 RID: 31008 RVA: 0x00005683 File Offset: 0x00003883
		private void onPlayerShoot(Player player)
		{
		}

		// Token: 0x040046F6 RID: 18166
		private int m_probability = 0;

		// Token: 0x040046F7 RID: 18167
		private int m_currentId;

		// Token: 0x040046F8 RID: 18168
		private int m_added = 0;
	}
}
