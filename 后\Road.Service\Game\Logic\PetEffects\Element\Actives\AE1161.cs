﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DDF RID: 3551
	public class AE1161 : BasePetEffect
	{
		// Token: 0x06007D0A RID: 32010 RVA: 0x00299FA4 File Offset: 0x002981A4
		public AE1161(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1161, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D0B RID: 32011 RVA: 0x0029A024 File Offset: 0x00298224
		public override bool Start(Living living)
		{
			AE1161 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1161) as AE1161;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D0C RID: 32012 RVA: 0x00030182 File Offset: 0x0002E382
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007D0D RID: 32013 RVA: 0x0029A084 File Offset: 0x00298284
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				this.m_added = 2000;
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddBlood(-this.m_added, 1);
					bool flag2 = player2.Blood <= 0;
					if (flag2)
					{
						player2.Die();
						if (player != null)
						{
							player.PlayerDetail.OnKillingLiving(player.Game, 2, player2.Id, player2.IsLiving, this.m_added);
						}
					}
					player2.AddPetEffect(new CE1161(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x06007D0E RID: 32014 RVA: 0x00030198 File Offset: 0x0002E398
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004B51 RID: 19281
		private int m_type = 0;

		// Token: 0x04004B52 RID: 19282
		private int m_count = 0;

		// Token: 0x04004B53 RID: 19283
		private int m_probability = 0;

		// Token: 0x04004B54 RID: 19284
		private int m_delay = 0;

		// Token: 0x04004B55 RID: 19285
		private int m_coldDown = 0;

		// Token: 0x04004B56 RID: 19286
		private int m_currentId;

		// Token: 0x04004B57 RID: 19287
		private int m_added = 0;
	}
}
