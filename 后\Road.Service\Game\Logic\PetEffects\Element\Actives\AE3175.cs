﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E37 RID: 3639
	public class AE3175 : BasePetEffect
	{
		// Token: 0x06007EDC RID: 32476 RVA: 0x002A2AA0 File Offset: 0x002A0CA0
		public AE3175(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE3175, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007EDD RID: 32477 RVA: 0x002A2B1C File Offset: 0x002A0D1C
		public override bool Start(Living living)
		{
			AE3175 ae = living.PetEffectList.GetOfType(ePetEffectType.AE3175) as AE3175;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007EDE RID: 32478 RVA: 0x00031400 File Offset: 0x0002F600
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EDF RID: 32479 RVA: 0x00031416 File Offset: 0x0002F616
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EE0 RID: 32480 RVA: 0x002A2B78 File Offset: 0x002A0D78
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.Info, true);
				player.AddPetEffect(new CE3175(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004DBC RID: 19900
		private int m_type = 0;

		// Token: 0x04004DBD RID: 19901
		private int m_count = 0;

		// Token: 0x04004DBE RID: 19902
		private int m_probability = 0;

		// Token: 0x04004DBF RID: 19903
		private int m_delay = 0;

		// Token: 0x04004DC0 RID: 19904
		private int m_coldDown = 0;

		// Token: 0x04004DC1 RID: 19905
		private int m_currentId;

		// Token: 0x04004DC2 RID: 19906
		private int m_added = 0;
	}
}
