﻿using System;
using System.Collections.Generic;
using Game.Logic.Effects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.NormalSpell
{
	// Token: 0x02000CBB RID: 3259
	[SpellAttibute(3)]
	public class HideSpell : ISpellHandler
	{
		// Token: 0x060074F8 RID: 29944 RVA: 0x0026E10C File Offset: 0x0026C30C
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			int property = item.Property2;
			int num = property;
			if (num != 0)
			{
				if (num == 1)
				{
					List<Player> allFightPlayers = player.Game.GetAllFightPlayers();
					foreach (Player player2 in allFightPlayers)
					{
						bool flag = player2.IsLiving && player2.Team == player.Team;
						if (flag)
						{
							new HideEffect(item.Property3).Start(player2);
						}
					}
				}
			}
			else
			{
				bool isLiving = player.IsLiving;
				if (isLiving)
				{
					new HideEffect(item.Property3).Start(player);
				}
				else
				{
					bool flag2 = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
					if (flag2)
					{
						new HideEffect(item.Property3).Start(game.CurrentLiving);
					}
				}
			}
		}
	}
}
