﻿using System;
using System.Diagnostics;
using System.Net.Sockets;
using System.Reflection;
using System.Threading;
using Game.Base.Packets;
using log4net;

namespace Game.Base
{
	// Token: 0x02000F6D RID: 3949
	public class BaseClient
	{
		// Token: 0x170014B1 RID: 5297
		// (get) Token: 0x0600853C RID: 34108 RVA: 0x002BA81C File Offset: 0x002B8A1C
		// (set) Token: 0x0600853D RID: 34109 RVA: 0x000357B0 File Offset: 0x000339B0
		public Socket Socket
		{
			get
			{
				return this.m_sock;
			}
			set
			{
				this.m_sock = value;
			}
		}

		// Token: 0x170014B2 RID: 5298
		// (get) Token: 0x0600853E RID: 34110 RVA: 0x000357BA File Offset: 0x000339BA
		public byte[] PacketBuf
		{
			get
			{
				return this.m_readBuffer;
			}
		}

		// Token: 0x170014B3 RID: 5299
		// (get) Token: 0x0600853F RID: 34111 RVA: 0x000357C2 File Offset: 0x000339C2
		public bool IsConnected
		{
			get
			{
				return this.m_isConnected == 1;
			}
		}

		// Token: 0x170014B4 RID: 5300
		// (get) Token: 0x06008540 RID: 34112 RVA: 0x002BA834 File Offset: 0x002B8A34
		// (set) Token: 0x06008541 RID: 34113 RVA: 0x000357CD File Offset: 0x000339CD
		public int PacketBufSize
		{
			get
			{
				return this.m_readBufEnd;
			}
			set
			{
				this.m_readBufEnd = value;
			}
		}

		// Token: 0x170014B5 RID: 5301
		// (get) Token: 0x06008542 RID: 34114 RVA: 0x002BA84C File Offset: 0x002B8A4C
		public string TcpEndpoint
		{
			get
			{
				Socket sock = this.m_sock;
				bool flag = sock != null && sock.Connected && sock.RemoteEndPoint != null;
				string text;
				if (flag)
				{
					text = sock.RemoteEndPoint.ToString();
				}
				else
				{
					text = "not connected";
				}
				return text;
			}
		}

		// Token: 0x170014B6 RID: 5302
		// (get) Token: 0x06008543 RID: 34115 RVA: 0x000357D7 File Offset: 0x000339D7
		public byte[] SendBuffer
		{
			get
			{
				return this.m_sendBuffer;
			}
		}

		// Token: 0x170014B7 RID: 5303
		// (get) Token: 0x06008544 RID: 34116 RVA: 0x002BA894 File Offset: 0x002B8A94
		// (set) Token: 0x06008545 RID: 34117 RVA: 0x000357DF File Offset: 0x000339DF
		public bool Encryted
		{
			get
			{
				return this.m_encryted;
			}
			set
			{
				this.m_encryted = value;
			}
		}

		// Token: 0x170014B8 RID: 5304
		// (get) Token: 0x06008546 RID: 34118 RVA: 0x002BA8AC File Offset: 0x002B8AAC
		// (set) Token: 0x06008547 RID: 34119 RVA: 0x000357E9 File Offset: 0x000339E9
		public bool Strict
		{
			get
			{
				return this.m_strict;
			}
			set
			{
				this.m_strict = value;
			}
		}

		// Token: 0x170014B9 RID: 5305
		// (get) Token: 0x06008548 RID: 34120 RVA: 0x002BA8C4 File Offset: 0x002B8AC4
		// (set) Token: 0x06008549 RID: 34121 RVA: 0x000357F3 File Offset: 0x000339F3
		public bool AsyncPostSend
		{
			get
			{
				return this.m_asyncPostSend;
			}
			set
			{
				this.m_asyncPostSend = value;
			}
		}

		// Token: 0x140000E0 RID: 224
		// (add) Token: 0x0600854A RID: 34122 RVA: 0x002BA8DC File Offset: 0x002B8ADC
		// (remove) Token: 0x0600854B RID: 34123 RVA: 0x002BA914 File Offset: 0x002B8B14
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event ClientEventHandle Connected;

		// Token: 0x140000E1 RID: 225
		// (add) Token: 0x0600854C RID: 34124 RVA: 0x002BA94C File Offset: 0x002B8B4C
		// (remove) Token: 0x0600854D RID: 34125 RVA: 0x002BA984 File Offset: 0x002B8B84
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event ClientEventHandle Disconnected;

		// Token: 0x0600854E RID: 34126 RVA: 0x000357FD File Offset: 0x000339FD
		public virtual void OnRecv(int num_bytes)
		{
			this.m_processor.ReceiveBytes(num_bytes);
		}

		// Token: 0x0600854F RID: 34127 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnRecvPacket(GSPacketIn pkg)
		{
		}

		// Token: 0x06008550 RID: 34128 RVA: 0x002BA9BC File Offset: 0x002B8BBC
		protected virtual void OnConnect()
		{
			bool flag = Interlocked.Exchange(ref this.m_isConnected, 1) == 0 && this.Connected != null;
			if (flag)
			{
				this.Connected(this);
			}
		}

		// Token: 0x06008551 RID: 34129 RVA: 0x002BA9F8 File Offset: 0x002B8BF8
		protected virtual void OnDisconnect()
		{
			bool flag = this.Disconnected != null;
			if (flag)
			{
				this.Disconnected(this);
			}
		}

		// Token: 0x06008552 RID: 34130 RVA: 0x002BAA24 File Offset: 0x002B8C24
		public BaseClient(byte[] readBuffer, byte[] sendBuffer)
		{
			this.m_readBuffer = readBuffer;
			this.m_sendBuffer = sendBuffer;
			this.m_readBufEnd = 0;
			this.rc_event = new SocketAsyncEventArgs();
			this.rc_event.Completed += this.RecvEventCallback;
			this.m_processor = new StreamProcessor(this);
			this.m_encryted = false;
			this.m_strict = true;
		}

		// Token: 0x06008553 RID: 34131 RVA: 0x0003580D File Offset: 0x00033A0D
		public void SetFsm(int adder, int muliter)
		{
			this.m_processor.SetFsm(adder, muliter);
		}

		// Token: 0x06008554 RID: 34132 RVA: 0x0003581E File Offset: 0x00033A1E
		public void ReceiveAsync()
		{
			this.ReceiveAsyncImp(this.rc_event);
		}

		// Token: 0x06008555 RID: 34133 RVA: 0x002BAA94 File Offset: 0x002B8C94
		private void ReceiveAsyncImp(SocketAsyncEventArgs e)
		{
			bool flag = this.m_sock != null && this.m_sock.Connected;
			if (flag)
			{
				int num = this.m_readBuffer.Length;
				bool flag2 = this.m_readBufEnd >= num;
				if (flag2)
				{
					bool isErrorEnabled = BaseClient.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						BaseClient.log.Error(this.TcpEndpoint + " disconnected because of buffer overflow!");
						BaseClient.log.Error("m_pBufEnd=" + this.m_readBufEnd.ToString() + "; buf size=" + num.ToString());
						BaseClient.log.Error(this.m_readBuffer);
					}
					this.Disconnect();
				}
				else
				{
					e.SetBuffer(this.m_readBuffer, this.m_readBufEnd, num - this.m_readBufEnd);
					bool flag3 = !this.m_sock.ReceiveAsync(e);
					if (flag3)
					{
						this.RecvEventCallback(this.m_sock, e);
					}
				}
			}
			else
			{
				this.Disconnect();
			}
		}

		// Token: 0x06008556 RID: 34134 RVA: 0x002BAB9C File Offset: 0x002B8D9C
		private void RecvEventCallback(object sender, SocketAsyncEventArgs e)
		{
			try
			{
				int bytesTransferred = e.BytesTransferred;
				bool flag = bytesTransferred > 0;
				if (flag)
				{
					this.OnRecv(bytesTransferred);
					this.ReceiveAsyncImp(e);
				}
				else
				{
					BaseClient.log.InfoFormat("Disconnecting client ({0}), received bytes={1}", this.TcpEndpoint, bytesTransferred);
					this.Disconnect();
				}
			}
			catch (Exception ex)
			{
				BaseClient.log.ErrorFormat("{0} RecvCallback:{1}", this.TcpEndpoint, ex);
				this.Disconnect();
			}
		}

		// Token: 0x06008557 RID: 34135 RVA: 0x0003582E File Offset: 0x00033A2E
		public virtual void SendTCP(GSPacketIn pkg)
		{
			this.m_processor.SendTCP(pkg);
		}

		// Token: 0x06008558 RID: 34136 RVA: 0x002BAC2C File Offset: 0x002B8E2C
		public bool SendAsync(SocketAsyncEventArgs e)
		{
			int tickCount = Environment.TickCount;
			bool flag = true;
			bool connected = this.m_sock.Connected;
			if (connected)
			{
				flag = this.m_sock.SendAsync(e);
			}
			int num = Environment.TickCount - tickCount;
			bool flag2 = num > 1500;
			if (flag2)
			{
				BaseClient.log.WarnFormat("AsyncTcpSendCallback.BeginSend took {0}ms! (TCP to client: {1})", num, this.TcpEndpoint);
			}
			return flag;
		}

		// Token: 0x06008559 RID: 34137 RVA: 0x002BAC9C File Offset: 0x002B8E9C
		protected void CloseConnections()
		{
			bool flag = this.m_sock != null;
			if (flag)
			{
				try
				{
					this.m_sock.Shutdown(SocketShutdown.Both);
				}
				catch
				{
				}
				try
				{
					this.m_sock.Close();
				}
				catch
				{
				}
			}
		}

		// Token: 0x0600855A RID: 34138 RVA: 0x002BAD04 File Offset: 0x002B8F04
		public virtual bool Connect(Socket connectedSocket)
		{
			this.m_sock = connectedSocket;
			bool connected = this.m_sock.Connected;
			bool flag2;
			if (connected)
			{
				bool flag = Interlocked.Exchange(ref this.m_isConnected, 1) == 0;
				if (flag)
				{
					this.OnConnect();
				}
				flag2 = true;
			}
			else
			{
				flag2 = false;
			}
			return flag2;
		}

		// Token: 0x0600855B RID: 34139 RVA: 0x002BAD50 File Offset: 0x002B8F50
		public virtual void Disconnect()
		{
			try
			{
				int num = Interlocked.Exchange(ref this.m_isConnected, 0);
				bool flag = num == 1;
				if (flag)
				{
					this.CloseConnections();
					this.OnDisconnect();
					this.rc_event.Dispose();
					this.m_processor.Dispose();
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseClient.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseClient.log.Error("Exception", ex);
				}
			}
		}

		// Token: 0x0600855C RID: 34140 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void DisplayMessage(string msg)
		{
		}

		// Token: 0x0600855D RID: 34141 RVA: 0x0003583E File Offset: 0x00033A3E
		public virtual void resetKey()
		{
			this.RECEIVE_KEY = StreamProcessor.CloneArrary(StreamProcessor.KEY, 8);
			this.SEND_KEY = StreamProcessor.CloneArrary(StreamProcessor.KEY, 8);
		}

		// Token: 0x0600855E RID: 34142 RVA: 0x002BADD8 File Offset: 0x002B8FD8
		public virtual void SetKey(byte[] data)
		{
			for (int i = 0; i < 8; i++)
			{
				this.RECEIVE_KEY[i] = data[i];
				this.SEND_KEY[i] = data[i];
			}
		}

		// Token: 0x04005341 RID: 21313
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005342 RID: 21314
		protected Socket m_sock;

		// Token: 0x04005343 RID: 21315
		protected byte[] m_readBuffer;

		// Token: 0x04005344 RID: 21316
		protected int m_readBufEnd;

		// Token: 0x04005345 RID: 21317
		private SocketAsyncEventArgs rc_event;

		// Token: 0x04005346 RID: 21318
		private int m_isConnected;

		// Token: 0x04005347 RID: 21319
		public bool IsClientPacketSended = true;

		// Token: 0x04005348 RID: 21320
		public int numPacketProcces;

		// Token: 0x04005349 RID: 21321
		protected byte[] m_sendBuffer;

		// Token: 0x0400534A RID: 21322
		private bool m_encryted;

		// Token: 0x0400534B RID: 21323
		private bool m_strict;

		// Token: 0x0400534C RID: 21324
		private bool m_asyncPostSend;

		// Token: 0x0400534D RID: 21325
		public StreamProcessor m_processor;

		// Token: 0x0400534E RID: 21326
		public byte[] RECEIVE_KEY;

		// Token: 0x0400534F RID: 21327
		public byte[] SEND_KEY;

		// Token: 0x04005350 RID: 21328
		public byte KEYREF;
	}
}
