﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DC4 RID: 3524
	public class AE1096 : BasePetEffect
	{
		// Token: 0x06007C77 RID: 31863 RVA: 0x00297A54 File Offset: 0x00295C54
		public AE1096(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1096, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C78 RID: 31864 RVA: 0x00297AD4 File Offset: 0x00295CD4
		public override bool Start(Living living)
		{
			AE1096 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1096) as AE1096;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C79 RID: 31865 RVA: 0x0002FADC File Offset: 0x0002DCDC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C7A RID: 31866 RVA: 0x0002FAF2 File Offset: 0x0002DCF2
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C7B RID: 31867 RVA: 0x00297B34 File Offset: 0x00295D34
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.AddPetEffect(new CE1096(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004A96 RID: 19094
		private int m_type = 0;

		// Token: 0x04004A97 RID: 19095
		private int m_count = 0;

		// Token: 0x04004A98 RID: 19096
		private int m_probability = 0;

		// Token: 0x04004A99 RID: 19097
		private int m_delay = 0;

		// Token: 0x04004A9A RID: 19098
		private int m_coldDown = 0;

		// Token: 0x04004A9B RID: 19099
		private int m_currentId;

		// Token: 0x04004A9C RID: 19100
		private int m_added = 0;
	}
}
