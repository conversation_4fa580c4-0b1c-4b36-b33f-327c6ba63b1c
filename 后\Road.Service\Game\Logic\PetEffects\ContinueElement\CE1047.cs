﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E5A RID: 3674
	public class CE1047 : BasePetEffect
	{
		// Token: 0x06007FAA RID: 32682 RVA: 0x002A5FD0 File Offset: 0x002A41D0
		public CE1047(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1047, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FAB RID: 32683 RVA: 0x002A604C File Offset: 0x002A424C
		public override bool Start(Living living)
		{
			CE1047 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1047) as CE1047;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FAC RID: 32684 RVA: 0x002A60A8 File Offset: 0x002A42A8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				player.Defence += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007FAD RID: 32685 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FAE RID: 32686 RVA: 0x002A610C File Offset: 0x002A430C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007FAF RID: 32687 RVA: 0x002A6140 File Offset: 0x002A4340
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Defence -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004EB1 RID: 20145
		private int m_type = 0;

		// Token: 0x04004EB2 RID: 20146
		private int m_count = 0;

		// Token: 0x04004EB3 RID: 20147
		private int m_probability = 0;

		// Token: 0x04004EB4 RID: 20148
		private int m_delay = 0;

		// Token: 0x04004EB5 RID: 20149
		private int m_coldDown = 0;

		// Token: 0x04004EB6 RID: 20150
		private int m_currentId;

		// Token: 0x04004EB7 RID: 20151
		private int m_added = 0;
	}
}
