﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E8A RID: 3722
	public class CE1184 : BasePetEffect
	{
		// Token: 0x060080D6 RID: 32982 RVA: 0x002AA988 File Offset: 0x002A8B88
		public CE1184(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1184, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060080D7 RID: 32983 RVA: 0x002AAA08 File Offset: 0x002A8C08
		public override bool Start(Living living)
		{
			CE1184 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1184) as CE1184;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080D8 RID: 32984 RVA: 0x002AAA68 File Offset: 0x002A8C68
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 150;
				player.BaseDamage += (double)this.m_added;
			}
		}

		// Token: 0x060080D9 RID: 32985 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginNextTurn(Living living)
		{
		}

		// Token: 0x060080DA RID: 32986 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginSelfTurn(Living living)
		{
		}

		// Token: 0x060080DB RID: 32987 RVA: 0x000325F4 File Offset: 0x000307F4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
		}

		// Token: 0x04005003 RID: 20483
		private int m_type = 0;

		// Token: 0x04005004 RID: 20484
		private int m_count = 0;

		// Token: 0x04005005 RID: 20485
		private int m_probability = 0;

		// Token: 0x04005006 RID: 20486
		private int m_delay = 0;

		// Token: 0x04005007 RID: 20487
		private int m_coldDown = 0;

		// Token: 0x04005008 RID: 20488
		private int m_currentId;

		// Token: 0x04005009 RID: 20489
		private int m_added = 0;
	}
}
