﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DA0 RID: 3488
	public class AE1038 : BasePetEffect
	{
		// Token: 0x06007BBF RID: 31679 RVA: 0x0029453C File Offset: 0x0029273C
		public AE1038(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1038, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BC0 RID: 31680 RVA: 0x002945BC File Offset: 0x002927BC
		public override bool Start(Living living)
		{
			AE1038 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1038) as AE1038;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BC1 RID: 31681 RVA: 0x0002F4D1 File Offset: 0x0002D6D1
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BC2 RID: 31682 RVA: 0x0002F4E7 File Offset: 0x0002D6E7
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BC3 RID: 31683 RVA: 0x0029461C File Offset: 0x0029281C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && (this.m_currentId < 25 || this.m_currentId > 27 || !(player.Game is PVEGame));
			if (flag)
			{
				player.AddPetEffect(new CE1038(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x0400499A RID: 18842
		private int m_type = 0;

		// Token: 0x0400499B RID: 18843
		private int m_count = 0;

		// Token: 0x0400499C RID: 18844
		private int m_probability = 0;

		// Token: 0x0400499D RID: 18845
		private int m_delay = 0;

		// Token: 0x0400499E RID: 18846
		private int m_coldDown = 0;

		// Token: 0x0400499F RID: 18847
		private int m_currentId;

		// Token: 0x040049A0 RID: 18848
		private int m_added = 0;
	}
}
