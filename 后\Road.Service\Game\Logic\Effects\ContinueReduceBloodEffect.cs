﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EE5 RID: 3813
	public class ContinueReduceBloodEffect : AbstractEffect
	{
		// Token: 0x06008308 RID: 33544 RVA: 0x00033EC6 File Offset: 0x000320C6
		public ContinueReduceBloodEffect(int count, int turnCount, Living player)
			: base(eEffectType.ContinueReduceBloodEffect)
		{
			this.m_count = count;
			this.m_turn = turnCount;
			this.m_player = player;
		}

		// Token: 0x06008309 RID: 33545 RVA: 0x002B2738 File Offset: 0x002B0938
		public override bool Start(Living living)
		{
			ContinueReduceBloodEffect continueReduceBloodEffect = living.EffectList.GetOfType(eEffectType.ContinueReduceBloodEffect) as ContinueReduceBloodEffect;
			bool flag = continueReduceBloodEffect != null;
			bool flag2;
			if (flag)
			{
				continueReduceBloodEffect.m_turn = this.m_turn;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600830A RID: 33546 RVA: 0x00033EE7 File Offset: 0x000320E7
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginFitting;
			living.Game.SendPlayerPicture(living, 2, true);
		}

		// Token: 0x0600830B RID: 33547 RVA: 0x00033F0C File Offset: 0x0003210C
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginFitting;
			living.Game.SendPlayerPicture(living, 2, false);
		}

		// Token: 0x0600830C RID: 33548 RVA: 0x002B2780 File Offset: 0x002B0980
		private void player_BeginFitting(Living living)
		{
			this.m_turn--;
			bool flag = this.m_turn < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				bool flag2 = living.FightBuffers.CardReduceContinueDamageLv1 > 0;
				if (flag2)
				{
					this.m_count -= living.FightBuffers.CardReduceContinueDamageLv1;
				}
				bool flag3 = living.FightBuffers.CardReduceContinueDamageLv2 > 0;
				if (flag3)
				{
					this.m_count -= living.FightBuffers.CardReduceContinueDamageLv2;
				}
				bool flag4 = living.FightBuffers.CardReduceContinueDamageLv3 > 0;
				if (flag4)
				{
					this.m_count -= living.FightBuffers.CardReduceContinueDamageLv3;
				}
				bool flag5 = living.FightBuffers.CardReduceContinueDamageLv4 > 0;
				if (flag5)
				{
					this.m_count -= living.FightBuffers.CardReduceContinueDamageLv4;
				}
				bool flag6 = living.FightBuffers.CardReducePoisoningLv1 > 0;
				if (flag6)
				{
					this.m_count -= living.FightBuffers.CardReducePoisoningLv1;
				}
				bool flag7 = living.FightBuffers.CardReducePoisoningLv2 > 0;
				if (flag7)
				{
					this.m_count -= living.FightBuffers.CardReducePoisoningLv2;
				}
				bool flag8 = living.FightBuffers.CardReducePoisoningLv3 > 0;
				if (flag8)
				{
					this.m_count -= living.FightBuffers.CardReducePoisoningLv3;
				}
				bool flag9 = living.FightBuffers.CardReducePoisoningLv4 > 0;
				if (flag9)
				{
					this.m_count -= living.FightBuffers.CardReducePoisoningLv4;
				}
				bool flag10 = this.m_count < 0;
				if (flag10)
				{
					this.m_count = 0;
				}
				living.AddBlood(-this.m_count, 1);
				bool flag11 = living.Blood <= 0;
				if (flag11)
				{
					living.Die();
					bool flag12 = this.m_player != null && this.m_player is Player;
					if (flag12)
					{
						(this.m_player as Player).PlayerDetail.OnKillingLiving(this.m_player.Game, 2, living.Id, living.IsLiving, this.m_count);
					}
				}
			}
		}

		// Token: 0x040051F4 RID: 20980
		private int m_count;

		// Token: 0x040051F5 RID: 20981
		private int m_turn;

		// Token: 0x040051F6 RID: 20982
		private Living m_player;
	}
}
