﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D0A RID: 3338
	public class PetDefensivePosture_CritDamageEquip : BasePetEffect
	{
		// Token: 0x0600788F RID: 30863 RVA: 0x00286A2C File Offset: 0x00284C2C
		public PetDefensivePosture_CritDamageEquip(int count, string elementID)
			: base(ePetEffectType.PetDefensivePosture_CritDamageEquip, elementID)
		{
			this.m_count = count;
			if (!(elementID == "1588"))
			{
				if (!(elementID == "1552") && !(elementID == "1589"))
				{
					if (!(elementID == "1553"))
					{
						if (elementID == "20001")
						{
							this.m_value = 5000;
						}
					}
					else
					{
						this.m_value = 3500;
					}
				}
				else
				{
					this.m_value = 2500;
				}
			}
			else
			{
				this.m_value = 2000;
			}
		}

		// Token: 0x06007890 RID: 30864 RVA: 0x00286ACC File Offset: 0x00284CCC
		public override bool Start(Living living)
		{
			PetDefensivePosture_CritDamageEquip petDefensivePosture_CritDamageEquip = living.PetEffectList.GetOfType(ePetEffectType.PetDefensivePosture_CritDamageEquip) as PetDefensivePosture_CritDamageEquip;
			bool flag = petDefensivePosture_CritDamageEquip != null;
			bool flag2;
			if (flag)
			{
				petDefensivePosture_CritDamageEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007891 RID: 30865 RVA: 0x0002CBAF File Offset: 0x0002ADAF
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x06007892 RID: 30866 RVA: 0x0002CBC5 File Offset: 0x0002ADC5
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.sendShowPicSkil(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x06007893 RID: 30867 RVA: 0x00286B14 File Offset: 0x00284D14
		private void player_BeginSelfTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = this.m_value;
				living.Guard += (double)this.m_added;
			}
			this.m_count--;
			bool flag2 = this.m_count < -1;
			if (flag2)
			{
				living.Guard -= (double)this.m_added;
				this.m_added = 0;
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
		}

		// Token: 0x040046AA RID: 18090
		private int m_count;

		// Token: 0x040046AB RID: 18091
		private int m_value;

		// Token: 0x040046AC RID: 18092
		private int m_added;
	}
}
