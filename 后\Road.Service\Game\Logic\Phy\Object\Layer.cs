﻿using System;

namespace Game.Logic.Phy.Object
{
	// Token: 0x02000CD1 RID: 3281
	public class Layer : PhysicalObj
	{
		// Token: 0x1700140D RID: 5133
		// (get) Token: 0x06007533 RID: 30003 RVA: 0x0002B694 File Offset: 0x00029894
		public override int Type
		{
			get
			{
				return this.m_type;
			}
		}

		// Token: 0x06007534 RID: 30004 RVA: 0x0002B69C File Offset: 0x0002989C
		public Layer(int id, string name, string model, string defaultAction, int scale, int rotation)
			: base(id, name, model, defaultAction, scale, rotation, 0)
		{
			this.m_type = 2;
		}

		// Token: 0x06007535 RID: 30005 RVA: 0x0002B6B7 File Offset: 0x000298B7
		public Layer(int id, string name, string model, string defaultAction, int scale, int rotation, int type)
			: base(id, name, model, defaultAction, scale, rotation, type)
		{
			this.m_type = type;
		}

		// Token: 0x040044E1 RID: 17633
		private int m_type;
	}
}
