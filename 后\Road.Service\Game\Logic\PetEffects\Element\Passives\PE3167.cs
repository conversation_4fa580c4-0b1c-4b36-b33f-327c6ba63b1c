﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D8D RID: 3469
	public class PE3167 : BasePetEffect
	{
		// Token: 0x06007B58 RID: 31576 RVA: 0x00292A44 File Offset: 0x00290C44
		public PE3167(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE3167, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B59 RID: 31577 RVA: 0x00292AC0 File Offset: 0x00290CC0
		public override bool Start(Living living)
		{
			PE3167 pe = living.PetEffectList.GetOfType(ePetEffectType.PE3167) as PE3167;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B5A RID: 31578 RVA: 0x0002EFCF File Offset: 0x0002D1CF
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerShoot += this.ChangeProperty;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007B5B RID: 31579 RVA: 0x0002EFF8 File Offset: 0x0002D1F8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerShoot -= this.ChangeProperty;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007B5C RID: 31580 RVA: 0x0002F021 File Offset: 0x0002D221
		private void ChangeProperty(Player player)
		{
			this.m_added = 25;
			player.PetEffects.PetBaseAtt += this.m_added;
		}

		// Token: 0x06007B5D RID: 31581 RVA: 0x0002F045 File Offset: 0x0002D245
		private void player_AfterPlayerShooted(Player player)
		{
			player.PetEffects.PetBaseAtt -= this.m_added;
			this.m_added = 0;
		}

		// Token: 0x04004915 RID: 18709
		private int m_type = 0;

		// Token: 0x04004916 RID: 18710
		private int m_count = 0;

		// Token: 0x04004917 RID: 18711
		private int m_probability = 0;

		// Token: 0x04004918 RID: 18712
		private int m_delay = 0;

		// Token: 0x04004919 RID: 18713
		private int m_coldDown = 0;

		// Token: 0x0400491A RID: 18714
		private int m_currentId;

		// Token: 0x0400491B RID: 18715
		private int m_added = 0;
	}
}
