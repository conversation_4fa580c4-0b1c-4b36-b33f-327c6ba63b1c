﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FD8 RID: 4056
	public class ItemBoxMgr
	{
		// Token: 0x06008AE5 RID: 35557 RVA: 0x002F90B0 File Offset: 0x002F72B0
		public static bool ReLoad()
		{
			try
			{
				ItemBoxInfo[] array = ItemBoxMgr.LoadItemBoxDb();
				Dictionary<int, List<ItemBoxInfo>> dictionary = ItemBoxMgr.LoadItemBoxs(array);
				bool flag = array != null;
				if (flag)
				{
					Interlocked.Exchange<ItemBoxInfo[]>(ref ItemBoxMgr.m_itemBox, array);
					Interlocked.Exchange<Dictionary<int, List<ItemBoxInfo>>>(ref ItemBoxMgr.m_itemBoxs, dictionary);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = ItemBoxMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ItemBoxMgr.log.Error("ReLoad", ex);
				}
				return false;
			}
			return true;
		}

		// Token: 0x06008AE6 RID: 35558 RVA: 0x002F9134 File Offset: 0x002F7334
		public static bool Init()
		{
			return ItemBoxMgr.ReLoad();
		}

		// Token: 0x06008AE7 RID: 35559 RVA: 0x002F914C File Offset: 0x002F734C
		public static ItemBoxInfo[] LoadItemBoxDb()
		{
			Dictionary<int, ItemBoxInfo> dictionary = new Dictionary<int, ItemBoxInfo>();
			ItemBoxInfo[] itemBoxInfos;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				itemBoxInfos = produceBussiness.GetItemBoxInfos();
			}
			return itemBoxInfos;
		}

		// Token: 0x06008AE8 RID: 35560 RVA: 0x002F918C File Offset: 0x002F738C
		public static Dictionary<int, List<ItemBoxInfo>> LoadItemBoxs(ItemBoxInfo[] itemBoxs)
		{
			Dictionary<int, List<ItemBoxInfo>> dictionary = new Dictionary<int, List<ItemBoxInfo>>();
			for (int i = 0; i < itemBoxs.Length; i++)
			{
				ItemBoxInfo info = itemBoxs[i];
				bool flag = !dictionary.Keys.Contains(info.DataId);
				if (flag)
				{
					IEnumerable<ItemBoxInfo> enumerable = itemBoxs.Where((ItemBoxInfo s) => s.DataId == info.DataId);
					dictionary.Add(info.DataId, enumerable.ToList<ItemBoxInfo>());
				}
			}
			return dictionary;
		}

		// Token: 0x06008AE9 RID: 35561 RVA: 0x002F9218 File Offset: 0x002F7418
		public static bool LoadItemBoxs(Dictionary<int, List<ItemBoxInfo>> infos)
		{
			bool flag2;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				ItemBoxInfo[] itemBoxInfos = produceBussiness.GetItemBoxInfos();
				ItemBoxInfo[] array = itemBoxInfos;
				ItemBoxInfo[] array2 = array;
				ItemBoxInfo[] array3 = array2;
				ItemBoxInfo[] array4 = array3;
				for (int i = 0; i < array4.Length; i++)
				{
					ItemBoxInfo item = array4[i];
					bool flag = !infos.Keys.Contains(item.DataId);
					if (flag)
					{
						IEnumerable<ItemBoxInfo> enumerable = itemBoxInfos.Where((ItemBoxInfo s) => s.DataId == item.DataId);
						infos.Add(item.DataId, enumerable.ToList<ItemBoxInfo>());
					}
				}
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x06008AEA RID: 35562 RVA: 0x002F92DC File Offset: 0x002F74DC
		public static List<ItemBoxInfo> FindItemBox(int DataId)
		{
			bool flag = ItemBoxMgr.m_itemBoxs.ContainsKey(DataId);
			List<ItemBoxInfo> list;
			if (flag)
			{
				list = ItemBoxMgr.m_itemBoxs[DataId];
			}
			else
			{
				list = null;
			}
			return list;
		}

		// Token: 0x06008AEB RID: 35563 RVA: 0x002F9310 File Offset: 0x002F7510
		public static bool CreateItemBox(int DateId, List<ItemInfo> itemInfos, SpecialItemBoxInfo specialValue)
		{
			List<ItemBoxInfo> list = ItemBoxMgr.FindItemBox(DateId);
			bool flag = list == null;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				List<ItemBoxInfo> list2 = list.Where((ItemBoxInfo s) => s.IsSelect).ToList<ItemBoxInfo>();
				int num = 1;
				int maxRound = 0;
				bool flag3 = list2.Count < list.Count;
				if (flag3)
				{
					maxRound = ThreadSafeRandom.NextStatic((from s in list
						where !s.IsSelect
						select s.Random).Max());
				}
				List<ItemBoxInfo> list3 = list.Where((ItemBoxInfo s) => !s.IsSelect && s.Random >= maxRound).ToList<ItemBoxInfo>();
				int num2 = list3.Count<ItemBoxInfo>();
				bool flag4 = num2 > 0;
				if (flag4)
				{
					num = ((num > num2) ? num2 : num);
					int[] randomUnrepeatArray = ItemBoxMgr.GetRandomUnrepeatArray(0, num2 - 1, num);
					int[] array = randomUnrepeatArray;
					int[] array2 = array;
					int[] array3 = array2;
					foreach (int num3 in array3)
					{
						ItemBoxInfo itemBoxInfo = list3[num3];
						list2.Add(itemBoxInfo);
					}
				}
				foreach (ItemBoxInfo itemBoxInfo2 in list2)
				{
					bool flag5 = itemBoxInfo2 == null;
					if (flag5)
					{
						return false;
					}
					int templateId = itemBoxInfo2.TemplateId;
					int num4 = templateId;
					if (num4 <= -400)
					{
						if (num4 <= -6100)
						{
							if (num4 == -6300)
							{
								specialValue.NiuNiuMoney += itemBoxInfo2.ItemCount;
								continue;
							}
							if (num4 == -6100)
							{
								specialValue.MoonChip += itemBoxInfo2.ItemCount;
								continue;
							}
						}
						else
						{
							if (num4 == -5200)
							{
								specialValue.MarkCount += itemBoxInfo2.ItemCount;
								continue;
							}
							if (num4 == -400)
							{
								specialValue.MyHornor += itemBoxInfo2.ItemCount;
								continue;
							}
						}
					}
					else if (num4 <= -200)
					{
						if (num4 == -300)
						{
							specialValue.GiftToken += itemBoxInfo2.ItemCount;
							continue;
						}
						if (num4 == -200)
						{
							specialValue.Money += itemBoxInfo2.ItemCount;
							continue;
						}
					}
					else
					{
						if (num4 == -100)
						{
							specialValue.Gold += itemBoxInfo2.ItemCount;
							continue;
						}
						if (num4 == 11107)
						{
							specialValue.GP += itemBoxInfo2.ItemCount;
							continue;
						}
					}
					ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(itemBoxInfo2.TemplateId);
					ItemInfo itemInfo = ItemInfo.CreateFromTemplate(itemTemplateInfo, itemBoxInfo2.ItemCount, 101);
					bool flag6 = itemInfo != null;
					if (flag6)
					{
						itemInfo.StrengthenLevel = itemBoxInfo2.StrengthenLevel;
						itemInfo.AttackCompose = itemBoxInfo2.AttackCompose;
						itemInfo.DefendCompose = itemBoxInfo2.DefendCompose;
						itemInfo.AgilityCompose = itemBoxInfo2.AgilityCompose;
						itemInfo.LuckCompose = itemBoxInfo2.LuckCompose;
						itemInfo.IsBinds = itemBoxInfo2.IsBind;
						itemInfo.ValidDate = itemBoxInfo2.ItemValid;
						itemInfo.Count = itemBoxInfo2.ItemCount;
						itemInfo.IsTips = itemBoxInfo2.IsTips;
						itemInfo.IsLogs = itemBoxInfo2.IsLogs;
						bool flag7 = itemInfos == null;
						if (flag7)
						{
							itemInfos = new List<ItemInfo>();
						}
						itemInfos.Add(itemInfo);
					}
				}
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x06008AEC RID: 35564 RVA: 0x002F9720 File Offset: 0x002F7920
		public static bool CreateItemBox(int DateId, List<ItemBoxInfo> tempBox, List<ItemInfo> itemInfos, SpecialItemBoxInfo specialValue)
		{
			List<ItemBoxInfo> list = new List<ItemBoxInfo>();
			List<ItemBoxInfo> list2 = ItemBoxMgr.FindItemBox(DateId);
			bool flag = tempBox != null && tempBox.Count > 0;
			if (flag)
			{
				list2 = tempBox;
			}
			bool flag2 = list2 == null;
			bool flag3;
			if (flag2)
			{
				flag3 = false;
			}
			else
			{
				list = list2.Where((ItemBoxInfo s) => s.IsSelect).ToList<ItemBoxInfo>();
				int num = 1;
				int maxRound = 0;
				bool flag4 = list.Count < list2.Count;
				if (flag4)
				{
					maxRound = ThreadSafeRandom.NextStatic((from s in list2
						where !s.IsSelect
						select s.Random).Max());
					bool flag5 = maxRound <= 0;
					if (flag5)
					{
						maxRound = (from s in list2
							where !s.IsSelect
							select s.Random).Max();
					}
				}
				List<ItemBoxInfo> list3 = list2.Where((ItemBoxInfo s) => !s.IsSelect && s.Random >= maxRound).ToList<ItemBoxInfo>();
				int num2 = list3.Count<ItemBoxInfo>();
				bool flag6 = num2 > 0;
				if (flag6)
				{
					num = ((num > num2) ? num2 : num);
					int[] randomUnrepeatArray = ItemBoxMgr.GetRandomUnrepeatArray(0, num2 - 1, num);
					int[] array = randomUnrepeatArray;
					int[] array2 = array;
					int[] array3 = array2;
					foreach (int num3 in array3)
					{
						ItemBoxInfo itemBoxInfo = list3[num3];
						bool flag7 = list == null;
						if (flag7)
						{
							list = new List<ItemBoxInfo>();
						}
						list.Add(itemBoxInfo);
					}
				}
				foreach (ItemBoxInfo itemBoxInfo2 in list)
				{
					bool flag8 = itemBoxInfo2 == null;
					if (flag8)
					{
						return false;
					}
					int templateId = itemBoxInfo2.TemplateId;
					int num4 = templateId;
					if (num4 <= -400)
					{
						if (num4 == -6100)
						{
							specialValue.MoonChip += itemBoxInfo2.ItemCount;
							continue;
						}
						if (num4 == -5200)
						{
							specialValue.MarkCount += itemBoxInfo2.ItemCount;
							continue;
						}
						if (num4 == -400)
						{
							specialValue.MyHornor += itemBoxInfo2.ItemCount;
							continue;
						}
					}
					else if (num4 <= -200)
					{
						if (num4 == -300)
						{
							specialValue.GiftToken += itemBoxInfo2.ItemCount;
							continue;
						}
						if (num4 == -200)
						{
							specialValue.Money += itemBoxInfo2.ItemCount;
							continue;
						}
					}
					else
					{
						if (num4 == -100)
						{
							specialValue.Gold += itemBoxInfo2.ItemCount;
							continue;
						}
						if (num4 == 11107)
						{
							specialValue.GP += itemBoxInfo2.ItemCount;
							continue;
						}
					}
					ItemInfo itemInfo = ItemInfo.CreateFromTemplate(ItemMgr.FindItemTemplate(itemBoxInfo2.TemplateId), itemBoxInfo2.ItemCount, 101);
					bool flag9 = itemInfo != null;
					if (flag9)
					{
						itemInfo.StrengthenLevel = itemBoxInfo2.StrengthenLevel;
						itemInfo.AttackCompose = itemBoxInfo2.AttackCompose;
						itemInfo.DefendCompose = itemBoxInfo2.DefendCompose;
						itemInfo.AgilityCompose = itemBoxInfo2.AgilityCompose;
						itemInfo.LuckCompose = itemBoxInfo2.LuckCompose;
						itemInfo.IsBinds = itemBoxInfo2.IsBind;
						itemInfo.ValidDate = itemBoxInfo2.ItemValid;
						itemInfo.Count = itemBoxInfo2.ItemCount;
						itemInfo.IsTips = itemBoxInfo2.IsTips;
						itemInfo.IsLogs = itemBoxInfo2.IsLogs;
						bool flag10 = itemInfos == null;
						if (flag10)
						{
							itemInfos = new List<ItemInfo>();
						}
						itemInfos.Add(itemInfo);
					}
				}
				flag3 = true;
			}
			return flag3;
		}

		// Token: 0x06008AED RID: 35565 RVA: 0x002F9B94 File Offset: 0x002F7D94
		public static List<ItemBoxInfo> FindRandomLotteryItemBox(int DataId)
		{
			bool flag = ItemBoxMgr.m_itemBoxs.ContainsKey(DataId);
			List<ItemBoxInfo> list2;
			if (flag)
			{
				List<ItemBoxInfo> list = new List<ItemBoxInfo>();
				foreach (ItemBoxInfo itemBoxInfo in ItemBoxMgr.m_itemBoxs[DataId])
				{
					bool flag2 = true;
					foreach (ItemBoxInfo itemBoxInfo2 in list)
					{
						bool flag3 = itemBoxInfo2.TemplateId == itemBoxInfo.TemplateId && itemBoxInfo2.ItemCount == itemBoxInfo.ItemCount;
						if (flag3)
						{
							flag2 = false;
							break;
						}
					}
					bool flag4 = flag2 && !itemBoxInfo.IsSelect;
					if (flag4)
					{
						list.Add(itemBoxInfo);
					}
				}
				list2 = list;
			}
			else
			{
				list2 = null;
			}
			return list2;
		}

		// Token: 0x06008AEE RID: 35566 RVA: 0x002F9CA4 File Offset: 0x002F7EA4
		public static List<ItemBoxInfo> FindFixedLotteryItemBox(int DataId)
		{
			bool flag = ItemBoxMgr.m_itemBoxs.ContainsKey(DataId);
			List<ItemBoxInfo> list2;
			if (flag)
			{
				List<ItemBoxInfo> list = new List<ItemBoxInfo>();
				foreach (ItemBoxInfo itemBoxInfo in ItemBoxMgr.m_itemBoxs[DataId])
				{
					bool flag2 = true;
					foreach (ItemBoxInfo itemBoxInfo2 in list)
					{
						bool flag3 = itemBoxInfo2.TemplateId == itemBoxInfo.TemplateId && itemBoxInfo2.ItemCount == itemBoxInfo.ItemCount;
						if (flag3)
						{
							flag2 = false;
							break;
						}
					}
					bool flag4 = flag2 && itemBoxInfo.IsSelect;
					if (flag4)
					{
						list.Add(itemBoxInfo);
					}
				}
				list2 = list;
			}
			else
			{
				list2 = null;
			}
			return list2;
		}

		// Token: 0x06008AEF RID: 35567 RVA: 0x002F9DB0 File Offset: 0x002F7FB0
		public static List<ItemBoxInfo> FindLotteryItemBoxByRand(int DateId, int countSelect)
		{
			List<ItemBoxInfo> list = ItemBoxMgr.FindRandomLotteryItemBox(DateId);
			List<ItemBoxInfo> list2 = ItemBoxMgr.FindFixedLotteryItemBox(DateId);
			List<ItemBoxInfo> list3 = new List<ItemBoxInfo>();
			foreach (ItemBoxInfo itemBoxInfo in list2)
			{
				list3.Add(itemBoxInfo);
			}
			countSelect -= list2.Count;
			for (int i = 0; i < countSelect; i++)
			{
				int num = ThreadSafeRandom.NextStatic(0, list.Count);
				bool flag = num < list.Count;
				if (flag)
				{
					list3.Add(list[num]);
					list.Remove(list[num]);
				}
			}
			return list3;
		}

		// Token: 0x06008AF0 RID: 35568 RVA: 0x00263044 File Offset: 0x00261244
		public static int[] GetRandomUnrepeatArray(int minValue, int maxValue, int count)
		{
			int[] array = new int[count];
			for (int i = 0; i < count; i++)
			{
				int num = ThreadSafeRandom.NextStatic(minValue, maxValue + 1);
				int num2 = 0;
				for (int j = 0; j < i; j++)
				{
					bool flag = array[j] == num;
					if (flag)
					{
						num2++;
					}
				}
				bool flag2 = num2 == 0;
				if (flag2)
				{
					array[i] = num;
				}
				else
				{
					i--;
				}
			}
			return array;
		}

		// Token: 0x04005509 RID: 21769
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x0400550A RID: 21770
		private static ItemBoxInfo[] m_itemBox;

		// Token: 0x0400550B RID: 21771
		private static Dictionary<int, List<ItemBoxInfo>> m_itemBoxs;
	}
}
