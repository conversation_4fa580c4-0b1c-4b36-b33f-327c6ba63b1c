﻿using System;
using Game.Logic.CardEffect;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffects
{
	// Token: 0x02000F1A RID: 3866
	public class BaseCardEffect : AbstractCardEffect
	{
		// Token: 0x060083D5 RID: 33749 RVA: 0x0003483A File Offset: 0x00032A3A
		public BaseCardEffect(eCardEffectType type, CardBuffInfo info)
			: base(type, info)
		{
		}

		// Token: 0x060083D6 RID: 33750 RVA: 0x002B5AE0 File Offset: 0x002B3CE0
		public override bool Start(Living living)
		{
			bool flag = living is Player;
			return flag && base.Start(living);
		}

		// Token: 0x060083D7 RID: 33751 RVA: 0x002B5B0C File Offset: 0x002B3D0C
		public sealed override void OnAttached(Living living)
		{
			bool flag = living is Player;
			if (flag)
			{
				this.OnAttachedToPlayer(living as Player);
			}
		}

		// Token: 0x060083D8 RID: 33752 RVA: 0x002B5B38 File Offset: 0x002B3D38
		public sealed override void OnRemoved(Living living)
		{
			bool flag = living is Player;
			if (flag)
			{
				this.OnRemovedFromPlayer(living as Player);
			}
		}

		// Token: 0x060083D9 RID: 33753 RVA: 0x002B5B64 File Offset: 0x002B3D64
		public sealed override void OnPaused(Living living)
		{
			bool flag = living is Player;
			if (flag)
			{
				this.OnPausedOnPlayer(living as Player);
			}
		}

		// Token: 0x060083DA RID: 33754 RVA: 0x00005683 File Offset: 0x00003883
		protected virtual void OnAttachedToPlayer(Player player)
		{
		}

		// Token: 0x060083DB RID: 33755 RVA: 0x00005683 File Offset: 0x00003883
		protected virtual void OnRemovedFromPlayer(Player player)
		{
		}

		// Token: 0x060083DC RID: 33756 RVA: 0x00005683 File Offset: 0x00003883
		protected virtual void OnPausedOnPlayer(Player player)
		{
		}
	}
}
