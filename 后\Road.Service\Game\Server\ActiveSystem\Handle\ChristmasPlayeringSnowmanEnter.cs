﻿using System;
using Bussiness;
using Game.Base.Packets;
using Game.Server.GameObjects;
using Game.Server.Rooms;
using SqlDataProvider.Data;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C58 RID: 3160
	[ActiveSystemHandleAttbute(17)]
	public class ChristmasPlayeringSnowmanEnter : IActiveSystemCommandHadler
	{
		// Token: 0x06007034 RID: 28724 RVA: 0x0024E020 File Offset: 0x0024C220
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			byte b = packet.ReadByte();
			BaseChristmasRoom christmasRoom = RoomMgr.ChristmasRoom;
			UserChristmasInfo christmas = Player.Actives.Christmas;
			switch (b)
			{
			case 0:
			{
				Player.X = christmasRoom.DefaultPosX;
				Player.Y = christmasRoom.DefaultPosY;
				christmasRoom.AddPlayer(Player);
				int num = GameProperties.ChristmasMinute;
				bool flag = !christmas.IsEnter;
				if (flag)
				{
					christmas.GameBeginTime = DateTime.Now;
					christmas.GameEndTime = DateTime.Now.AddMinutes((double)num);
					christmas.IsEnter = true;
					christmas.AvailTime = num;
				}
				else
				{
					num = christmas.AvailTime;
					christmas.GameBeginTime = DateTime.Now;
					christmas.GameEndTime = DateTime.Now.AddMinutes((double)num);
				}
				bool flag2 = Player.Actives.AvailTime();
				gspacketIn.WriteByte(17);
				gspacketIn.WriteBoolean(flag2);
				gspacketIn.WriteDateTime(christmas.GameBeginTime);
				gspacketIn.WriteDateTime(christmas.GameEndTime);
				gspacketIn.WriteInt(christmas.Count);
				Player.Out.SendTCP(gspacketIn);
				break;
			}
			case 1:
				christmasRoom.RemovePlayer(Player);
				break;
			case 2:
			{
				int num2 = packet.ReadInt();
				int num3 = packet.ReadInt();
				Player.X = num2;
				Player.Y = num3;
				bool flag3 = Player.CurrentRoom != null;
				if (flag3)
				{
					Player.CurrentRoom.RemovePlayerUnsafe(Player);
				}
				christmasRoom.AddMoreMonters();
				christmasRoom.SetMonterDie(Player.PlayerCharacter.ID);
				bool flag4 = !Player.Actives.AvailTime();
				if (flag4)
				{
					Player.SendMessage(LanguageMgr.GetTranslation("ActiveSystemHandler.Msg1", Array.Empty<object>()));
					return false;
				}
				gspacketIn.WriteByte(22);
				gspacketIn.WriteByte(0);
				gspacketIn.WriteInt(christmasRoom.Monters.Count);
				foreach (ChrismasMonterInfo chrismasMonterInfo in christmasRoom.Monters.Values)
				{
					gspacketIn.WriteInt(chrismasMonterInfo.ID);
					gspacketIn.WriteInt(chrismasMonterInfo.type);
					gspacketIn.WriteInt(chrismasMonterInfo.state);
					gspacketIn.WriteInt(chrismasMonterInfo.MonsterPos.X);
					gspacketIn.WriteInt(chrismasMonterInfo.MonsterPos.Y);
				}
				Player.Out.SendTCP(gspacketIn);
				christmasRoom.ViewOtherPlayerRoom(Player);
				break;
			}
			}
			return true;
		}
	}
}
