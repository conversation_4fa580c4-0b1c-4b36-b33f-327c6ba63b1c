﻿using System;
using Game.Logic;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C65 RID: 3173
	public class FightMatchWinCondition : BaseCondition
	{
		// Token: 0x06007086 RID: 28806 RVA: 0x0002A421 File Offset: 0x00028621
		public FightMatchWinCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x06007087 RID: 28807 RVA: 0x0002A4F0 File Offset: 0x000286F0
		public override void AddTrigger(GamePlayer player)
		{
			player.GameOver += this.player_PlayerGameOverEventHandle;
		}

		// Token: 0x06007088 RID: 28808 RVA: 0x002500F0 File Offset: 0x0024E2F0
		private void player_PlayerGameOverEventHandle(AbstractGame game, bool isWin, int gainXp, bool isSpanArea)
		{
			bool flag = (game.GameType == eGameType.Free || game.GameType == eGameType.Leage) && game.RoomType == eRoomType.Match;
			if (flag)
			{
				int value = base.Value;
				base.Value = value + 1;
			}
		}

		// Token: 0x06007089 RID: 28809 RVA: 0x0002A506 File Offset: 0x00028706
		public override void RemoveTrigger(GamePlayer player)
		{
			player.GameOver -= this.player_PlayerGameOverEventHandle;
		}

		// Token: 0x0600708A RID: 28810 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
