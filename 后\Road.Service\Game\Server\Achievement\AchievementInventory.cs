﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using Bussiness;
using Bussiness.Managers;
using Game.Server.GameObjects;
using log4net;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C5D RID: 3165
	public class AchievementInventory
	{
		// Token: 0x0600703E RID: 28734 RVA: 0x0024E66C File Offset: 0x0024C86C
		public AchievementInventory(GamePlayer player)
		{
			this.m_player = player;
			this.m_lock = new object();
			this.m_list = new List<BaseAchievement>();
			this.m_process = new Dictionary<int, AchievementProcessInfo>();
			this.m_success = new Dictionary<int, AchievementDataInfo>();
			this.m_listprocess = new List<AchievementProcessInfo>();
		}

		// Token: 0x0600703F RID: 28735 RVA: 0x0024E6C0 File Offset: 0x0024C8C0
		public void LoadFromDatabase(int playerId)
		{
			object @lock = this.m_lock;
			lock (@lock)
			{
				using (PlayerBussiness playerBussiness = new PlayerBussiness())
				{
					AchievementDataInfo[] userAchievement = playerBussiness.GetUserAchievement(playerId);
					this.CheckAchievementData(this.m_player.PlayerCharacter.AchievementProcess);
					AchievementDataInfo[] array = userAchievement;
					AchievementDataInfo[] array2 = array;
					AchievementDataInfo[] array3 = array2;
					AchievementDataInfo[] array4 = array3;
					foreach (AchievementDataInfo achievementDataInfo in array4)
					{
						bool isComplete = achievementDataInfo.IsComplete;
						if (isComplete)
						{
							this.m_success.Add(achievementDataInfo.AchievementID, achievementDataInfo);
						}
						else
						{
							AchievementInfo singleAchievement = AchievementMgr.GetSingleAchievement(achievementDataInfo.AchievementID);
							bool flag2 = singleAchievement != null;
							if (flag2)
							{
								this.FinishAchievement(new BaseAchievement(singleAchievement, achievementDataInfo, this.GetRealProcessAchievement()));
							}
						}
					}
					this.AddAchievementPre();
				}
			}
		}

		// Token: 0x06007040 RID: 28736 RVA: 0x0024E7CC File Offset: 0x0024C9CC
		public void SaveToDatabase()
		{
			object @lock = this.m_lock;
			lock (@lock)
			{
				using (PlayerBussiness playerBussiness = new PlayerBussiness())
				{
					foreach (AchievementDataInfo achievementDataInfo in this.m_success.Values)
					{
						bool isDirty = achievementDataInfo.IsDirty;
						if (isDirty)
						{
							playerBussiness.UpdateDbAchievementDataInfo(achievementDataInfo);
						}
					}
				}
			}
		}

		// Token: 0x06007041 RID: 28737 RVA: 0x0024E888 File Offset: 0x0024CA88
		public bool Finish(BaseAchievement baseAch)
		{
			AchievementInfo info = baseAch.Info;
			AchievementDataInfo data = baseAch.Data;
			this.m_player.BeginAllChanges();
			try
			{
				bool flag = baseAch.Finish(this.m_player);
				if (flag)
				{
					List<AchievementGoodsInfo> achievementGoods = AchievementMgr.GetAchievementGoods(info);
					foreach (AchievementGoodsInfo achievementGoodsInfo in achievementGoods)
					{
						int rewardType = achievementGoodsInfo.RewardType;
						int num = rewardType;
						if (num != 1)
						{
							if (num == 3)
							{
								ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(int.Parse(achievementGoodsInfo.RewardPara));
								bool flag2 = itemTemplateInfo != null;
								if (flag2)
								{
									ItemInfo itemInfo = ItemInfo.CreateFromTemplate(itemTemplateInfo, 1, 106);
									this.m_player.AddTemplate(itemInfo);
									this.m_player.SendMessage(LanguageMgr.GetTranslation("Game.Server.Quests.FinishQuest.Reward", Array.Empty<object>()) + LanguageMgr.GetTranslation("Game.Server.Quests.FinishQuest.RewardProp", new object[] { itemTemplateInfo.Name, 1 }));
								}
							}
						}
						else
						{
							this.m_player.Rank.AddAchievementRank(achievementGoodsInfo.RewardPara);
						}
					}
					bool flag3 = info.AchievementPoint != 0;
					if (flag3)
					{
						this.m_player.AddAchievementPoint(info.AchievementPoint);
					}
					this.m_player.Out.SendAchievementSuccess(data);
					this.FindReward(data);
					this.m_player.OnAchievementFinish(data);
					this.FindNewAchievement(data.AchievementID);
					this.RemoveAchievement(baseAch);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = AchievementInventory.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ILog log = AchievementInventory.log;
					string text = "Achivement Finish：";
					Exception ex2 = ex;
					log.Error(text + ((ex2 != null) ? ex2.ToString() : null));
				}
				return false;
			}
			finally
			{
				this.m_player.CommitAllChanges();
			}
			return true;
		}

		// Token: 0x06007042 RID: 28738 RVA: 0x0024EAC4 File Offset: 0x0024CCC4
		public bool RemoveAchievement(BaseAchievement info)
		{
			info.RemoveFromPlayer(this.m_player);
			return true;
		}

		// Token: 0x06007043 RID: 28739 RVA: 0x0024EAE4 File Offset: 0x0024CCE4
		public void AddAchievementPre()
		{
			foreach (AchievementInfo achievementInfo in AchievementMgr.GetAllAchievements())
			{
				bool flag = this.FindAchievementOver(achievementInfo.ID) == null && !this.FindAchievement(achievementInfo.ID);
				if (flag)
				{
					this.AddAchievement(achievementInfo);
				}
			}
		}

		// Token: 0x06007044 RID: 28740 RVA: 0x0024EB64 File Offset: 0x0024CD64
		private bool FinishAchievement(BaseAchievement baseach)
		{
			List<BaseAchievement> list = this.m_list;
			lock (list)
			{
				this.m_list.Add(baseach);
			}
			baseach.AddToPlayer(this.m_player);
			bool flag2 = baseach.CanCompleted(this.m_player);
			if (flag2)
			{
				this.Finish(baseach);
			}
			return true;
		}

		// Token: 0x06007045 RID: 28741 RVA: 0x0024EBDC File Offset: 0x0024CDDC
		private void FindNewAchievement(int id)
		{
			foreach (AchievementInfo achievementInfo in AchievementMgr.GetAllAchievements())
			{
				bool flag = !(achievementInfo.PreAchievementID != "0,");
				if (!flag)
				{
					string[] array = achievementInfo.PreAchievementID.Split(new char[] { ',' });
					string[] array2 = array;
					string[] array3 = array2;
					string[] array4 = array3;
					foreach (string text in array4)
					{
						bool flag2 = text != null && text != "";
						if (flag2)
						{
							AchievementInfo singleAchievement = AchievementMgr.GetSingleAchievement(int.Parse(text));
							bool flag3 = singleAchievement != null && singleAchievement.ID == id;
							if (flag3)
							{
								this.AddAchievement(achievementInfo);
								break;
							}
						}
					}
				}
			}
		}

		// Token: 0x06007046 RID: 28742 RVA: 0x0024ECE0 File Offset: 0x0024CEE0
		public bool AddAchievement(AchievementInfo info)
		{
			try
			{
				bool flag = info == null || this.m_player.PlayerCharacter.Grade < info.NeedMinLevel || this.m_player.PlayerCharacter.Grade > info.NeedMaxLevel;
				if (flag)
				{
					return false;
				}
				bool flag2 = info.PreAchievementID != "0,";
				if (flag2)
				{
					string[] array = info.PreAchievementID.Split(new char[] { ',' });
					for (int i = 0; i < array.Length - 1; i++)
					{
						bool flag3 = !this.FindAchievement(Convert.ToInt32(array[i]));
						if (flag3)
						{
							return false;
						}
					}
				}
			}
			catch (Exception ex)
			{
				AchievementInventory.log.Info(ex.InnerException);
			}
			BaseAchievement baseAchievement = this.FindAchievementOver(info.ID);
			bool flag4 = baseAchievement != null;
			bool flag5;
			if (flag4)
			{
				flag5 = false;
			}
			else
			{
				this.BeginChange();
				baseAchievement = new BaseAchievement(info, new AchievementDataInfo(), this.GetRealProcessAchievement());
				this.FinishAchievement(baseAchievement);
				this.UpdateChange();
				flag5 = true;
			}
			return flag5;
		}

		// Token: 0x06007047 RID: 28743 RVA: 0x0024EE10 File Offset: 0x0024D010
		private bool FindAchievement(int id)
		{
			Dictionary<int, AchievementDataInfo> success = this.m_success;
			bool flag3;
			lock (success)
			{
				bool flag2 = this.m_success.ContainsKey(id);
				if (flag2)
				{
					flag3 = true;
				}
				else
				{
					flag3 = false;
				}
			}
			return flag3;
		}

		// Token: 0x06007048 RID: 28744 RVA: 0x0024EE68 File Offset: 0x0024D068
		private void FindReward(AchievementDataInfo data)
		{
			Dictionary<int, AchievementDataInfo> success = this.m_success;
			lock (success)
			{
				bool flag2 = !this.m_success.ContainsKey(data.AchievementID);
				if (flag2)
				{
					this.m_success.Add(data.AchievementID, data);
				}
			}
		}

		// Token: 0x06007049 RID: 28745 RVA: 0x0024EED4 File Offset: 0x0024D0D4
		public BaseAchievement FindAchievementOver(int id)
		{
			foreach (BaseAchievement baseAchievement in this.m_list)
			{
				bool flag = baseAchievement.Info.ID == id;
				if (flag)
				{
					return baseAchievement;
				}
			}
			return null;
		}

		// Token: 0x0600704A RID: 28746 RVA: 0x0024EF44 File Offset: 0x0024D144
		public List<AchievementDataInfo> GetSuccessAchievement()
		{
			Dictionary<int, AchievementDataInfo> success = this.m_success;
			List<AchievementDataInfo> list;
			lock (success)
			{
				list = this.m_success.Values.ToList<AchievementDataInfo>();
			}
			return list;
		}

		// Token: 0x0600704B RID: 28747 RVA: 0x0024EF94 File Offset: 0x0024D194
		public Dictionary<int, AchievementProcessInfo> GetRealProcessAchievement()
		{
			Dictionary<int, AchievementProcessInfo> process = this.m_process;
			Dictionary<int, AchievementProcessInfo> process2;
			lock (process)
			{
				process2 = this.m_process;
			}
			return process2;
		}

		// Token: 0x0600704C RID: 28748 RVA: 0x0024EFDC File Offset: 0x0024D1DC
		public List<AchievementProcessInfo> GetProcessAchievement()
		{
			Dictionary<int, AchievementProcessInfo> process = this.m_process;
			List<AchievementProcessInfo> list;
			lock (process)
			{
				list = this.m_process.Values.ToList<AchievementProcessInfo>();
			}
			return list;
		}

		// Token: 0x0600704D RID: 28749 RVA: 0x0024F02C File Offset: 0x0024D22C
		protected void OnAchievementsChanged(AchievementProcessInfo info)
		{
			bool flag = !this.m_listprocess.Contains(info);
			if (flag)
			{
				this.m_listprocess.Add(info);
			}
			bool flag2 = this.m_changeCount <= 0 && this.m_listprocess.Count > 0;
			if (flag2)
			{
				this.UpdateChangedAchievements();
			}
		}

		// Token: 0x0600704E RID: 28750 RVA: 0x0002A365 File Offset: 0x00028565
		private void BeginChange()
		{
			Interlocked.Increment(ref this.m_changeCount);
		}

		// Token: 0x0600704F RID: 28751 RVA: 0x0024F084 File Offset: 0x0024D284
		private void UpdateChange()
		{
			int num = Interlocked.Decrement(ref this.m_changeCount);
			bool flag = num < 0;
			if (flag)
			{
				Thread.VolatileWrite(ref this.m_changeCount, 0);
				bool isErrorEnabled = AchievementInventory.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					AchievementInventory.log.Error("Inventory changes counter is bellow zero (forgot to use BeginChanges?)!\n\n" + Environment.StackTrace);
				}
			}
			bool flag2 = num <= 0 && this.m_listprocess.Count > 0;
			if (flag2)
			{
				this.UpdateChangedAchievements();
			}
		}

		// Token: 0x06007050 RID: 28752 RVA: 0x0002A374 File Offset: 0x00028574
		public void UpdateChangedAchievements()
		{
			this.m_player.Out.SendUpdateAchievementInfo(this.m_listprocess.ToList<AchievementProcessInfo>());
			this.m_listprocess.Clear();
		}

		// Token: 0x06007051 RID: 28753 RVA: 0x0024F104 File Offset: 0x0024D304
		private void CheckAchievementData(string data)
		{
			bool flag = data == null || !(data != "");
			if (!flag)
			{
				Dictionary<int, AchievementProcessInfo> process = this.m_process;
				lock (process)
				{
					string[] array = data.Split(new char[] { '|' });
					string[] array2 = array;
					string[] array3 = array2;
					string[] array4 = array3;
					foreach (string text in array4)
					{
						bool flag3 = text == null || !(text != "");
						if (!flag3)
						{
							string[] array6 = text.Split(new char[] { ',' });
							bool flag4 = array6.Length >= 2;
							if (flag4)
							{
								int num = int.Parse(array6[0]);
								int num2 = int.Parse(array6[1]);
								bool flag5 = !this.m_process.ContainsKey(num);
								if (flag5)
								{
									AchievementProcessInfo achievementProcessInfo = new AchievementProcessInfo(num, num2);
									this.m_process.Add(num, achievementProcessInfo);
								}
							}
						}
					}
				}
			}
		}

		// Token: 0x06007052 RID: 28754 RVA: 0x0024F234 File Offset: 0x0024D434
		private string CanCompleted()
		{
			List<string> list = new List<string>();
			Dictionary<int, AchievementProcessInfo> process = this.m_process;
			lock (process)
			{
				bool flag2 = this.m_process.Count <= 0;
				if (flag2)
				{
					return "";
				}
				foreach (AchievementProcessInfo achievementProcessInfo in this.m_process.Values)
				{
					list.Add(achievementProcessInfo.CondictionType.ToString() + "," + achievementProcessInfo.Value.ToString());
				}
			}
			return string.Join("|", list.ToArray());
		}

		// Token: 0x06007053 RID: 28755 RVA: 0x0024F328 File Offset: 0x0024D528
		public void UpdateProcess(BaseCondition info)
		{
			bool flag = info != null;
			if (flag)
			{
				AchievementProcessInfo achievementProcessInfo = new AchievementProcessInfo(info.Info.CondictionType, info.Value);
				this.UpdateProcess(achievementProcessInfo);
			}
		}

		// Token: 0x06007054 RID: 28756 RVA: 0x0024F360 File Offset: 0x0024D560
		public void UpdateProcess(AchievementProcessInfo info)
		{
			object @lock = this.m_lock;
			lock (@lock)
			{
				bool flag2 = true;
				this.BeginChange();
				bool flag3 = !this.m_process.ContainsKey(info.CondictionType);
				if (flag3)
				{
					this.m_process.Add(info.CondictionType, info);
				}
				else
				{
					bool flag4 = this.m_process[info.CondictionType].Value < info.Value;
					if (flag4)
					{
						this.m_process[info.CondictionType] = info;
					}
					else
					{
						flag2 = false;
					}
				}
				this.UpdateChange();
				bool flag5 = flag2;
				if (flag5)
				{
					this.m_player.PlayerCharacter.AchievementProcess = this.CanCompleted();
					this.OnAchievementsChanged(info);
				}
			}
		}

		// Token: 0x06007055 RID: 28757 RVA: 0x0024F448 File Offset: 0x0024D648
		protected void OnAchievementsChanged(BaseAchievement info)
		{
			bool flag = !this.m_list.Contains(info);
			if (flag)
			{
				this.m_list.Add(info);
			}
			bool flag2 = this.m_list.Count > 0;
			if (flag2)
			{
				this.UpdateChangedAchievements();
			}
		}

		// Token: 0x06007056 RID: 28758 RVA: 0x0002A39F File Offset: 0x0002859F
		public void Update(BaseAchievement info)
		{
			this.OnAchievementsChanged(info);
		}

		// Token: 0x04003C50 RID: 15440
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04003C51 RID: 15441
		private object m_lock;

		// Token: 0x04003C52 RID: 15442
		private int m_changeCount;

		// Token: 0x04003C53 RID: 15443
		private GamePlayer m_player;

		// Token: 0x04003C54 RID: 15444
		protected List<BaseAchievement> m_list;

		// Token: 0x04003C55 RID: 15445
		private Dictionary<int, AchievementDataInfo> m_success;

		// Token: 0x04003C56 RID: 15446
		private Dictionary<int, AchievementProcessInfo> m_process;

		// Token: 0x04003C57 RID: 15447
		protected List<AchievementProcessInfo> m_listprocess;
	}
}
