﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D7F RID: 3455
	public class PE1264 : BasePetEffect
	{
		// Token: 0x06007B10 RID: 31504 RVA: 0x002916F4 File Offset: 0x0028F8F4
		public PE1264(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1264, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B11 RID: 31505 RVA: 0x00291774 File Offset: 0x0028F974
		public override bool Start(Living living)
		{
			PE1264 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1264) as PE1264;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B12 RID: 31506 RVA: 0x0002ECC4 File Offset: 0x0002CEC4
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007B13 RID: 31507 RVA: 0x002917D4 File Offset: 0x0028F9D4
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = living.PetEffects.AddDameValue != 0;
			if (!flag)
			{
				this.m_added = 100;
				List<Player> allTeamPlayers = living.Game.GetAllTeamPlayers(living);
				foreach (Player player in allTeamPlayers)
				{
					player.BaseDamage += (double)this.m_added;
					player.PetEffects.AddDameValue += this.m_added;
					player.Game.SendPetBuff(player, base.Info, true);
				}
			}
		}

		// Token: 0x06007B14 RID: 31508 RVA: 0x0002ECDA File Offset: 0x0002CEDA
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x040048B9 RID: 18617
		private int m_type = 0;

		// Token: 0x040048BA RID: 18618
		private int m_count = 0;

		// Token: 0x040048BB RID: 18619
		private int m_probability = 0;

		// Token: 0x040048BC RID: 18620
		private int m_delay = 0;

		// Token: 0x040048BD RID: 18621
		private int m_coldDown = 0;

		// Token: 0x040048BE RID: 18622
		private int m_currentId;

		// Token: 0x040048BF RID: 18623
		private int m_added = 0;
	}
}
