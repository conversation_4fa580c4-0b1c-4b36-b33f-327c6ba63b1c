﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DE3 RID: 3555
	public class AE1172 : BasePetEffect
	{
		// Token: 0x06007D1E RID: 32030 RVA: 0x0029A72C File Offset: 0x0029892C
		public AE1172(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1172, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D1F RID: 32031 RVA: 0x0029A7AC File Offset: 0x002989AC
		public override bool Start(Living living)
		{
			AE1172 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1172) as AE1172;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D20 RID: 32032 RVA: 0x00030232 File Offset: 0x0002E432
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D21 RID: 32033 RVA: 0x00030248 File Offset: 0x0002E448
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D22 RID: 32034 RVA: 0x0029A80C File Offset: 0x00298A0C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1172(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004B6D RID: 19309
		private int m_type = 0;

		// Token: 0x04004B6E RID: 19310
		private int m_count = 0;

		// Token: 0x04004B6F RID: 19311
		private int m_probability = 0;

		// Token: 0x04004B70 RID: 19312
		private int m_delay = 0;

		// Token: 0x04004B71 RID: 19313
		private int m_coldDown = 0;

		// Token: 0x04004B72 RID: 19314
		private int m_currentId;

		// Token: 0x04004B73 RID: 19315
		private int m_added = 0;
	}
}
