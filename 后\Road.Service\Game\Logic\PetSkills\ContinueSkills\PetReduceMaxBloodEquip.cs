﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D15 RID: 3349
	public class PetReduceMaxBloodEquip : AbstractPetEffect
	{
		// Token: 0x060078CA RID: 30922 RVA: 0x00287D20 File Offset: 0x00285F20
		public PetReduceMaxBloodEquip(int count, string elementID)
			: base(ePetEffectType.PetReduceMaxBloodEquip, elementID)
		{
			this.m_count = count;
			bool flag = elementID == "4254";
			if (flag)
			{
				this.m_value = 20;
				this.m_percent = true;
			}
		}

		// Token: 0x060078CB RID: 30923 RVA: 0x00287D64 File Offset: 0x00285F64
		public override bool Start(Living living)
		{
			PetReduceMaxBloodEquip petReduceMaxBloodEquip = living.PetEffectList.GetOfType(ePetEffectType.PetReduceMaxBloodEquip) as PetReduceMaxBloodEquip;
			bool flag = petReduceMaxBloodEquip != null;
			bool flag2;
			if (flag)
			{
				petReduceMaxBloodEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078CC RID: 30924 RVA: 0x00287DAC File Offset: 0x00285FAC
		public override void OnAttached(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				bool percent = this.m_percent;
				if (percent)
				{
					this.m_added = living.MaxBlood * this.m_value / 100;
				}
				else
				{
					this.m_added = this.m_value;
				}
				living.MaxBlood -= this.m_added;
				bool flag2 = living.Blood >= living.MaxBlood;
				if (flag2)
				{
					living.Blood = living.MaxBlood;
				}
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				living.Game.UpdateMaxBlood(living);
			}
			living.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x060078CD RID: 30925 RVA: 0x00287E6C File Offset: 0x0028606C
		public override void OnRemoved(Living living)
		{
			living.MaxBlood += this.m_added;
			this.m_added = 0;
			living.Game.UpdateBlood(living, 1, living.Blood, 0);
			living.Game.UpdateMaxBlood(living);
			this.m_added = 0;
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x060078CE RID: 30926 RVA: 0x00287ED4 File Offset: 0x002860D4
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
		}

		// Token: 0x040046CB RID: 18123
		private int m_count;

		// Token: 0x040046CC RID: 18124
		private int m_value;

		// Token: 0x040046CD RID: 18125
		private bool m_percent;

		// Token: 0x040046CE RID: 18126
		private int m_added;
	}
}
