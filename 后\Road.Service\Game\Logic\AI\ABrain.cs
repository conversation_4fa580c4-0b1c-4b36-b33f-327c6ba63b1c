﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.AI
{
	// Token: 0x02000F3C RID: 3900
	public abstract class ABrain
	{
		// Token: 0x06008493 RID: 33939 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnDieNewMethod()
		{
		}

		// Token: 0x170014AC RID: 5292
		// (get) Token: 0x06008494 RID: 33940 RVA: 0x002B8B90 File Offset: 0x002B6D90
		// (set) Token: 0x06008495 RID: 33941 RVA: 0x00035018 File Offset: 0x00033218
		public Living Body
		{
			get
			{
				return this.m_body;
			}
			set
			{
				this.m_body = value;
			}
		}

		// Token: 0x170014AD RID: 5293
		// (get) Token: 0x06008496 RID: 33942 RVA: 0x002B8BA8 File Offset: 0x002B6DA8
		// (set) Token: 0x06008497 RID: 33943 RVA: 0x00035022 File Offset: 0x00033222
		public BaseGame Game
		{
			get
			{
				return this.m_game;
			}
			set
			{
				this.m_game = value;
			}
		}

		// Token: 0x06008498 RID: 33944 RVA: 0x0000586E File Offset: 0x00003A6E
		public ABrain()
		{
		}

		// Token: 0x06008499 RID: 33945 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnCreated()
		{
		}

		// Token: 0x0600849A RID: 33946 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnBeginNewTurn()
		{
		}

		// Token: 0x0600849B RID: 33947 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnBeginSelfTurn()
		{
		}

		// Token: 0x0600849C RID: 33948 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnStartAttacking()
		{
		}

		// Token: 0x0600849D RID: 33949 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnStopAttacking()
		{
		}

		// Token: 0x0600849E RID: 33950 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnAfterTakedBomb()
		{
		}

		// Token: 0x0600849F RID: 33951 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnAfterTakeDamage(Living source)
		{
		}

		// Token: 0x060084A0 RID: 33952 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnAfterTakedFrozen()
		{
		}

		// Token: 0x060084A1 RID: 33953 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnKillPlayerSay()
		{
		}

		// Token: 0x060084A2 RID: 33954 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnDie()
		{
		}

		// Token: 0x060084A3 RID: 33955 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnDiedSay()
		{
		}

		// Token: 0x060084A4 RID: 33956 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnDiedEvent()
		{
		}

		// Token: 0x060084A5 RID: 33957 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnShootedSay()
		{
		}

		// Token: 0x060084A6 RID: 33958 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnBeforeTakedDamage(Living source, ref int damageAmount, ref int criticalAmount)
		{
		}

		// Token: 0x060084A7 RID: 33959 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnHeal(int blood)
		{
		}

		// Token: 0x060084A8 RID: 33960 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void Dispose()
		{
		}

		// Token: 0x060084A9 RID: 33961 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnTriggerTakedBomb(Living source)
		{
		}

		// Token: 0x040052A8 RID: 21160
		protected Living m_body;

		// Token: 0x040052A9 RID: 21161
		protected BaseGame m_game;
	}
}
