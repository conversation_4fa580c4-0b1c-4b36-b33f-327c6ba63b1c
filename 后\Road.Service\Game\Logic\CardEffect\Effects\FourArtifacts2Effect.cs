﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F25 RID: 3877
	public class FourArtifacts2Effect : BaseCardEffect
	{
		// Token: 0x06008416 RID: 33814 RVA: 0x002B6A44 File Offset: 0x002B4C44
		public FourArtifacts2Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.FourArtifacts2, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008417 RID: 33815 RVA: 0x002B6AAC File Offset: 0x002B4CAC
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.FourArtifacts2) is FourArtifacts2Effect;
			return flag || base.Start(living);
		}

		// Token: 0x06008418 RID: 33816 RVA: 0x00034AD4 File Offset: 0x00032CD4
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerAfterReset += this.ChangeProperty1;
		}

		// Token: 0x06008419 RID: 33817 RVA: 0x00034AFD File Offset: 0x00032CFD
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerAfterReset -= this.ChangeProperty1;
		}

		// Token: 0x0600841A RID: 33818 RVA: 0x00034B26 File Offset: 0x00032D26
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			(living as Player).AddDander(this.m_value * 2);
		}

		// Token: 0x0600841B RID: 33819 RVA: 0x00034B3D File Offset: 0x00032D3D
		private void ChangeProperty1(Player player)
		{
			player.Game.SendMessage(player.PlayerDetail, "您激活了四神兵2件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活四神兵2件套卡.", 3);
		}

		// Token: 0x04005265 RID: 21093
		private int m_indexValue = 0;

		// Token: 0x04005266 RID: 21094
		private int m_value = 0;
	}
}
