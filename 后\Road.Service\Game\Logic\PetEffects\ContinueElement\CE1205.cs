﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E98 RID: 3736
	public class CE1205 : BasePetEffect
	{
		// Token: 0x0600812E RID: 33070 RVA: 0x002ABFBC File Offset: 0x002AA1BC
		public CE1205(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1205, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600812F RID: 33071 RVA: 0x002AC03C File Offset: 0x002AA23C
		public override bool Start(Living living)
		{
			CE1205 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1205) as CE1205;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008130 RID: 33072 RVA: 0x000328C6 File Offset: 0x00030AC6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008131 RID: 33073 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008132 RID: 33074 RVA: 0x002AC09C File Offset: 0x002AA29C
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 500;
				bool flag2 = living.Attack < (double)this.m_added;
				if (flag2)
				{
					this.m_added = (int)living.Attack - 1;
				}
				living.Attack -= (double)this.m_added;
			}
		}

		// Token: 0x06008133 RID: 33075 RVA: 0x002AC0FC File Offset: 0x002AA2FC
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008134 RID: 33076 RVA: 0x002AC130 File Offset: 0x002AA330
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Attack += (double)this.m_added;
			this.m_added = 0;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005065 RID: 20581
		private int m_type = 0;

		// Token: 0x04005066 RID: 20582
		private int m_count = 0;

		// Token: 0x04005067 RID: 20583
		private int m_probability = 0;

		// Token: 0x04005068 RID: 20584
		private int m_delay = 0;

		// Token: 0x04005069 RID: 20585
		private int m_coldDown = 0;

		// Token: 0x0400506A RID: 20586
		private int m_currentId;

		// Token: 0x0400506B RID: 20587
		private int m_added = 0;
	}
}
