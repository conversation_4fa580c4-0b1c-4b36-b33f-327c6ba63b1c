﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DB2 RID: 3506
	public class AE1063 : BasePetEffect
	{
		// Token: 0x06007C1B RID: 31771 RVA: 0x00295E90 File Offset: 0x00294090
		public AE1063(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1063, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C1C RID: 31772 RVA: 0x00295F10 File Offset: 0x00294110
		public override bool Start(Living living)
		{
			AE1063 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1063) as AE1063;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C1D RID: 31773 RVA: 0x0002F822 File Offset: 0x0002DA22
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007C1E RID: 31774 RVA: 0x00295F70 File Offset: 0x00294170
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1063(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007C1F RID: 31775 RVA: 0x0002F838 File Offset: 0x0002DA38
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004A18 RID: 18968
		private int m_type = 0;

		// Token: 0x04004A19 RID: 18969
		private int m_count = 0;

		// Token: 0x04004A1A RID: 18970
		private int m_probability = 0;

		// Token: 0x04004A1B RID: 18971
		private int m_delay = 0;

		// Token: 0x04004A1C RID: 18972
		private int m_coldDown = 0;

		// Token: 0x04004A1D RID: 18973
		private int m_currentId;

		// Token: 0x04004A1E RID: 18974
		private int m_added = 0;
	}
}
