﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D88 RID: 3464
	public class PE1430 : BasePetEffect
	{
		// Token: 0x06007B3D RID: 31549 RVA: 0x002924A4 File Offset: 0x002906A4
		public PE1430(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1430, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B3E RID: 31550 RVA: 0x00292524 File Offset: 0x00290724
		public override bool Start(Living living)
		{
			PE1430 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1430) as PE1430;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B3F RID: 31551 RVA: 0x0002EE50 File Offset: 0x0002D050
		protected override void OnAttachedToPlayer(Player player)
		{
			player.TakePlayerDamage += this.player_BeforeTakeDamage;
		}

		// Token: 0x06007B40 RID: 31552 RVA: 0x0002EE66 File Offset: 0x0002D066
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.TakePlayerDamage -= this.player_BeforeTakeDamage;
		}

		// Token: 0x06007B41 RID: 31553 RVA: 0x00292584 File Offset: 0x00290784
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.rand.Next(100) < 20;
			if (flag)
			{
				criticalAmount = 0;
			}
		}

		// Token: 0x040048F8 RID: 18680
		private int m_type = 0;

		// Token: 0x040048F9 RID: 18681
		private int m_count = 0;

		// Token: 0x040048FA RID: 18682
		private int m_probability = 0;

		// Token: 0x040048FB RID: 18683
		private int m_delay = 0;

		// Token: 0x040048FC RID: 18684
		private int m_coldDown = 0;

		// Token: 0x040048FD RID: 18685
		private int m_currentId;

		// Token: 0x040048FE RID: 18686
		private int m_added = 0;
	}
}
