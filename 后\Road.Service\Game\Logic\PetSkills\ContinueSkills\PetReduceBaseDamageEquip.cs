﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D0F RID: 3343
	public class PetReduceBaseDamageEquip : AbstractPetEffect
	{
		// Token: 0x060078AC RID: 30892 RVA: 0x0028713C File Offset: 0x0028533C
		public PetReduceBaseDamageEquip(int count, string elementID)
			: base(ePetEffectType.PetReduceBaseDamageEquip, elementID)
		{
			this.m_count = count;
			if (elementID == "1545" || elementID == "1546" || elementID == "1547")
			{
				this.m_value = 30;
				this.m_percent = true;
			}
		}

		// Token: 0x060078AD RID: 30893 RVA: 0x0028719C File Offset: 0x0028539C
		public override bool Start(Living living)
		{
			PetReduceBaseDamageEquip petReduceBaseDamageEquip = living.PetEffectList.GetOfType(ePetEffectType.PetReduceBaseDamageEquip) as PetReduceBaseDamageEquip;
			bool flag = petReduceBaseDamageEquip != null;
			bool flag2;
			if (flag)
			{
				petReduceBaseDamageEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078AE RID: 30894 RVA: 0x002871E4 File Offset: 0x002853E4
		public override void OnAttached(Living living)
		{
			bool flag = this.m_added == 0.0;
			if (flag)
			{
				bool percent = this.m_percent;
				if (percent)
				{
					this.m_added = living.BaseDamage * (double)this.m_value / 100.0;
				}
				else
				{
					this.m_added = (double)this.m_value;
				}
				living.BaseDamage -= this.m_added;
			}
			living.BeginSelfTurn += this.player_BeginFitting;
		}

		// Token: 0x060078AF RID: 30895 RVA: 0x0002CDAB File Offset: 0x0002AFAB
		public override void OnRemoved(Living living)
		{
			living.BaseDamage += this.m_added;
			this.m_added = 0.0;
			living.BeginSelfTurn -= this.player_BeginFitting;
		}

		// Token: 0x060078B0 RID: 30896 RVA: 0x0028726C File Offset: 0x0028546C
		private void player_BeginFitting(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				this.Stop();
			}
		}

		// Token: 0x040046B5 RID: 18101
		private int m_count;

		// Token: 0x040046B6 RID: 18102
		private int m_value;

		// Token: 0x040046B7 RID: 18103
		private bool m_percent;

		// Token: 0x040046B8 RID: 18104
		private double m_added;
	}
}
