﻿using System;
using System.Drawing;
using Game.Logic.Phy.Maths;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F65 RID: 3941
	public class PlayerSpeedMultAction : BaseAction
	{
		// Token: 0x06008524 RID: 34084 RVA: 0x002BA404 File Offset: 0x002B8604
		public PlayerSpeedMultAction(Player player, Point target, int delay)
			: base(0, delay)
		{
			this.m_player = player;
			this.m_target = target;
			this.m_v = new Point(target.X - this.m_player.X, target.Y - this.m_player.Y);
			this.m_v.Normalize(20);
		}

		// Token: 0x06008525 RID: 34085 RVA: 0x002BA468 File Offset: 0x002B8668
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			bool flag = !this.m_isSend;
			if (flag)
			{
				this.m_isSend = true;
				this.m_player.SpeedMultX(18);
				game.SendPlayerMove(this.m_player, 4, this.m_target.X, this.m_target.Y, (byte)((this.m_v.X > 0) ? 1 : (-1)), this.m_player.IsLiving);
			}
			bool flag2 = this.m_target.Distance(this.m_player.X, this.m_player.Y) > 20.0;
			if (flag2)
			{
				this.m_player.SetXY(this.m_player.X + this.m_v.X, this.m_player.Y + this.m_v.Y);
			}
			else
			{
				this.m_player.SetXY(this.m_target.X, this.m_target.Y);
				base.Finish(tick);
			}
		}

		// Token: 0x04005332 RID: 21298
		private Point m_target;

		// Token: 0x04005333 RID: 21299
		private Player m_player;

		// Token: 0x04005334 RID: 21300
		private Point m_v;

		// Token: 0x04005335 RID: 21301
		private bool m_isSend;
	}
}
