﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CC7 RID: 3271
	[SpellAttibute(87)]
	public class LockMoveSpell : ISpellHandler
	{
		// Token: 0x06007510 RID: 29968 RVA: 0x0026E8E0 File Offset: 0x0026CAE0
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.LockMove = true;
			}
			else
			{
				bool flag = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
				if (flag)
				{
					game.CurrentLiving.LockMove = true;
				}
			}
		}
	}
}
