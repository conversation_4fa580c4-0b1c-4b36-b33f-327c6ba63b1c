﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000ED4 RID: 3796
	public class AddDamageTurnEffect : BasePlayerEffect
	{
		// Token: 0x060082A8 RID: 33448 RVA: 0x0003384E File Offset: 0x00031A4E
		public AddDamageTurnEffect(int count, int probability)
			: base(eEffectType.AddDamageTurnEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x060082A9 RID: 33449 RVA: 0x002B1534 File Offset: 0x002AF734
		public override bool Start(Living living)
		{
			AddDamageTurnEffect addDamageTurnEffect = living.EffectList.GetOfType(eEffectType.AddDamageTurnEffect) as AddDamageTurnEffect;
			bool flag = addDamageTurnEffect != null;
			bool flag2;
			if (flag)
			{
				this.m_probability = ((this.m_probability > addDamageTurnEffect.m_probability) ? this.m_probability : addDamageTurnEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082AA RID: 33450 RVA: 0x002B1590 File Offset: 0x002AF790
		protected override void OnAttachedToPlayer(Player player)
		{
			player.TakePlayerDamage += this.player_BeforeTakeDamage;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
			player.BeginSelfTurn += this.player_SelfTurn;
			player.Game.SendPlayerPicture(player, 29, true);
		}

		// Token: 0x060082AB RID: 33451 RVA: 0x002B15E8 File Offset: 0x002AF7E8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.TakePlayerDamage -= this.player_BeforeTakeDamage;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
			player.BeginSelfTurn -= this.player_SelfTurn;
			player.Game.SendPlayerPicture(player, 29, false);
		}

		// Token: 0x060082AC RID: 33452 RVA: 0x000335DB File Offset: 0x000317DB
		private void player_AfterPlayerShooted(Player player)
		{
			player.FlyingPartical = 0;
		}

		// Token: 0x060082AD RID: 33453 RVA: 0x002B1640 File Offset: 0x002AF840
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				damageAmount += this.m_count;
			}
		}

		// Token: 0x060082AE RID: 33454 RVA: 0x002B1668 File Offset: 0x002AF868
		private void player_SelfTurn(Living living)
		{
			this.m_probability--;
			bool flag = this.m_probability < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x040051CD RID: 20941
		private int m_count = 0;

		// Token: 0x040051CE RID: 20942
		private int m_probability = 0;
	}
}
