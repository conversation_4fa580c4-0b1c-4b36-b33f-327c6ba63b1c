﻿using System;
using Game.Server.Managers;

namespace Game.Base.Commands
{
	// Token: 0x02000F98 RID: 3992
	[Cmd("&cs", ePrivLevel.Player, "Compile the C# scripts.", new string[] { "/cs  <source file> <target> <importlib>", "eg: /cs ./scripts temp.dll game.base.dll,game.logic.dll" })]
	public class BuildScriptCommand : Abstract<PERSON>ommand<PERSON><PERSON>ler, ICommandHandler
	{
		// Token: 0x060087C3 RID: 34755 RVA: 0x002C85D0 File Offset: 0x002C67D0
		public bool OnCommand(BaseClient client, string[] args)
		{
			bool flag = args.Length >= 4;
			if (flag)
			{
				string text = args[1];
				string text2 = args[2];
				string text3 = args[3];
				ScriptMgr.CompileScripts(false, text, text2, text3.Split(new char[] { ',' }));
			}
			else
			{
				this.DisplaySyntax(client);
			}
			return true;
		}
	}
}
