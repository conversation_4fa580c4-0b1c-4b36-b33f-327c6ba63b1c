﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D45 RID: 3397
	public class PetReduceTakeDamageForTeamWithFire : BasePetEffect
	{
		// Token: 0x060079CF RID: 31183 RVA: 0x0028C8E4 File Offset: 0x0028AAE4
		public PetReduceTakeDamageForTeamWithFire(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetShareTakeDamageToTeam, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_skillId = skillId;
			int skillId2 = this.m_skillId;
			int num = skillId2;
			switch (num)
			{
			case 560:
				this.m_elementID = "1767";
				break;
			case 561:
				this.m_elementID = "1768";
				break;
			case 562:
				this.m_elementID = "1769";
				break;
			default:
				if (num == 595)
				{
					this.m_elementID = "1774";
				}
				break;
			}
		}

		// Token: 0x060079D0 RID: 31184 RVA: 0x0028C984 File Offset: 0x0028AB84
		public override bool Start(Living living)
		{
			PetReduceTakeDamageForTeamWithFire petReduceTakeDamageForTeamWithFire = living.PetEffectList.GetOfType(ePetEffectType.PetShareTakeDamageToTeam) as PetReduceTakeDamageForTeamWithFire;
			bool flag = petReduceTakeDamageForTeamWithFire != null;
			bool flag2;
			if (flag)
			{
				petReduceTakeDamageForTeamWithFire.m_probability = ((this.m_probability > petReduceTakeDamageForTeamWithFire.m_probability) ? this.m_probability : petReduceTakeDamageForTeamWithFire.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079D1 RID: 31185 RVA: 0x0028C9E4 File Offset: 0x0028ABE4
		private void petUseSkill(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_skillId || player.Game.GetAllTeamPlayers(player).Count <= 1;
			if (!flag)
			{
				PetShowFireofResentmentEquip petShowFireofResentmentEquip = player.PetEffectList.GetOfType(ePetEffectType.PetShowFireofResentmentEquip) as PetShowFireofResentmentEquip;
				bool flag2 = petShowFireofResentmentEquip != null;
				if (flag2)
				{
					petShowFireofResentmentEquip.Stop();
				}
				foreach (Player player2 in player.Game.GetAllTeamPlayers(player))
				{
					player2.AddPetEffect(new PetReduceTakeDamageEquip(this.m_count, this.m_elementID), 0);
				}
			}
		}

		// Token: 0x060079D2 RID: 31186 RVA: 0x0002DF7F File Offset: 0x0002C17F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.petUseSkill;
		}

		// Token: 0x060079D3 RID: 31187 RVA: 0x0002DF95 File Offset: 0x0002C195
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.petUseSkill;
		}

		// Token: 0x0400475E RID: 18270
		private int m_count;

		// Token: 0x0400475F RID: 18271
		private int m_probability;

		// Token: 0x04004760 RID: 18272
		private int m_skillId;

		// Token: 0x04004761 RID: 18273
		private string m_elementID;
	}
}
