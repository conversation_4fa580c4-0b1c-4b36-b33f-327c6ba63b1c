﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FDF RID: 4063
	public class MagicItemMgr
	{
		// Token: 0x06008B11 RID: 35601 RVA: 0x002FA634 File Offset: 0x002F8834
		public static bool ReLoad()
		{
			try
			{
				MagicItemTemplateInfo[] array = MagicItemMgr.LoadMagicItemTemplateDb();
				Dictionary<int, MagicItemTemplateInfo> dictionary = MagicItemMgr.LoadMagicItemTemplates(array);
				bool flag = array.Length != 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, MagicItemTemplateInfo>>(ref MagicItemMgr.m_MagicItemTemplates, dictionary);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = MagicItemMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					MagicItemMgr.log.Error("ReLoad MagicItemTemplate", ex);
				}
				return false;
			}
			return true;
		}

		// Token: 0x06008B12 RID: 35602 RVA: 0x002FA6AC File Offset: 0x002F88AC
		public static bool Init()
		{
			return MagicItemMgr.ReLoad();
		}

		// Token: 0x06008B13 RID: 35603 RVA: 0x002FA6C4 File Offset: 0x002F88C4
		public static MagicItemTemplateInfo[] LoadMagicItemTemplateDb()
		{
			MagicItemTemplateInfo[] allMagicItemTemplate;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				allMagicItemTemplate = produceBussiness.GetAllMagicItemTemplate();
			}
			return allMagicItemTemplate;
		}

		// Token: 0x06008B14 RID: 35604 RVA: 0x002FA700 File Offset: 0x002F8900
		public static Dictionary<int, MagicItemTemplateInfo> LoadMagicItemTemplates(MagicItemTemplateInfo[] MagicItemTemplate)
		{
			Dictionary<int, MagicItemTemplateInfo> dictionary = new Dictionary<int, MagicItemTemplateInfo>();
			foreach (MagicItemTemplateInfo magicItemTemplateInfo in MagicItemTemplate)
			{
				bool flag = !dictionary.Keys.Contains(magicItemTemplateInfo.Lv);
				if (flag)
				{
					dictionary.Add(magicItemTemplateInfo.Lv, magicItemTemplateInfo);
				}
			}
			return dictionary;
		}

		// Token: 0x06008B15 RID: 35605 RVA: 0x002FA75C File Offset: 0x002F895C
		public static MagicItemTemplateInfo FindMagicItemTemplate(int ID)
		{
			MagicItemMgr.m_clientLocker.AcquireWriterLock(-1);
			try
			{
				bool flag = MagicItemMgr.m_MagicItemTemplates.ContainsKey(ID);
				if (flag)
				{
					return MagicItemMgr.m_MagicItemTemplates[ID];
				}
			}
			finally
			{
				MagicItemMgr.m_clientLocker.ReleaseWriterLock();
			}
			return null;
		}

		// Token: 0x0400551E RID: 21790
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x0400551F RID: 21791
		private static Dictionary<int, MagicItemTemplateInfo> m_MagicItemTemplates = new Dictionary<int, MagicItemTemplateInfo>();

		// Token: 0x04005520 RID: 21792
		private static Random random = new Random();

		// Token: 0x04005521 RID: 21793
		private static ReaderWriterLock m_clientLocker = new ReaderWriterLock();
	}
}
