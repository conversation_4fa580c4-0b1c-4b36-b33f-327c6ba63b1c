﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E52 RID: 3666
	public class CE1038 : BasePetEffect
	{
		// Token: 0x06007F7A RID: 32634 RVA: 0x002A53E8 File Offset: 0x002A35E8
		public CE1038(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1038, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F7B RID: 32635 RVA: 0x002A5468 File Offset: 0x002A3668
		public override bool Start(Living living)
		{
			CE1038 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1038) as CE1038;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F7C RID: 32636 RVA: 0x000319D7 File Offset: 0x0002FBD7
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.ControlBall = true;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F7D RID: 32637 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F7E RID: 32638 RVA: 0x002A54C8 File Offset: 0x002A36C8
		private void Player_BeginSelfTurn(Living living)
		{
			living.ControlBall = true;
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F7F RID: 32639 RVA: 0x00031A07 File Offset: 0x0002FC07
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.ControlBall = false;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E79 RID: 20089
		private int m_type = 0;

		// Token: 0x04004E7A RID: 20090
		private int m_count = 0;

		// Token: 0x04004E7B RID: 20091
		private int m_probability = 0;

		// Token: 0x04004E7C RID: 20092
		private int m_delay = 0;

		// Token: 0x04004E7D RID: 20093
		private int m_coldDown = 0;

		// Token: 0x04004E7E RID: 20094
		private int m_currentId;

		// Token: 0x04004E7F RID: 20095
		private int m_added = 0;
	}
}
