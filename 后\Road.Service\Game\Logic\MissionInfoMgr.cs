﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using Bussiness;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000CA5 RID: 3237
	public static class MissionInfoMgr
	{
		// Token: 0x060073CE RID: 29646 RVA: 0x00264444 File Offset: 0x00262644
		public static bool Init()
		{
			return MissionInfoMgr.Reload();
		}

		// Token: 0x060073CF RID: 29647 RVA: 0x0026445C File Offset: 0x0026265C
		public static bool Reload()
		{
			try
			{
				Dictionary<int, MissionInfo> dictionary = MissionInfoMgr.LoadFromDatabase();
				bool flag = dictionary.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, MissionInfo>>(ref MissionInfoMgr.m_missionInfos, dictionary);
				}
				return true;
			}
			catch (Exception ex)
			{
				MissionInfoMgr.log.Error("MissionInfoMgr", ex);
			}
			return false;
		}

		// Token: 0x060073D0 RID: 29648 RVA: 0x002644BC File Offset: 0x002626BC
		private static Dictionary<int, MissionInfo> LoadFromDatabase()
		{
			Dictionary<int, MissionInfo> dictionary = new Dictionary<int, MissionInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				MissionInfo[] allMissionInfo = produceBussiness.GetAllMissionInfo();
				MissionInfo[] array = allMissionInfo;
				MissionInfo[] array2 = array;
				MissionInfo[] array3 = array2;
				foreach (MissionInfo missionInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(missionInfo.Id);
					if (flag)
					{
						dictionary.Add(missionInfo.Id, missionInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x060073D1 RID: 29649 RVA: 0x00264558 File Offset: 0x00262758
		public static MissionInfo GetMissionInfo(int id)
		{
			bool flag = MissionInfoMgr.m_missionInfos.ContainsKey(id);
			MissionInfo missionInfo;
			if (flag)
			{
				missionInfo = MissionInfoMgr.m_missionInfos[id];
			}
			else
			{
				missionInfo = null;
			}
			return missionInfo;
		}

		// Token: 0x040043D4 RID: 17364
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040043D5 RID: 17365
		private static Dictionary<int, MissionInfo> m_missionInfos = new Dictionary<int, MissionInfo>();

		// Token: 0x040043D6 RID: 17366
		private static ReaderWriterLock m_lock = new ReaderWriterLock();

		// Token: 0x040043D7 RID: 17367
		private static ThreadSafeRandom m_rand = new ThreadSafeRandom();
	}
}
