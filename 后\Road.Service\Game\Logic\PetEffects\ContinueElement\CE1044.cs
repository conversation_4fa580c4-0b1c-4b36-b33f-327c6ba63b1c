﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E57 RID: 3671
	public class CE1044 : BasePetEffect
	{
		// Token: 0x06007F98 RID: 32664 RVA: 0x002A5B94 File Offset: 0x002A3D94
		public CE1044(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1044, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F99 RID: 32665 RVA: 0x002A5C14 File Offset: 0x002A3E14
		public override bool Start(Living living)
		{
			CE1044 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1044) as CE1044;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F9A RID: 32666 RVA: 0x00031A77 File Offset: 0x0002FC77
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F9B RID: 32667 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F9C RID: 32668 RVA: 0x002A5C74 File Offset: 0x002A3E74
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 700;
				living.SyncAtTime = true;
				living.AddBlood(this.m_added);
				living.SyncAtTime = false;
			}
		}

		// Token: 0x06007F9D RID: 32669 RVA: 0x00031AA0 File Offset: 0x0002FCA0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E9C RID: 20124
		private int m_type = 0;

		// Token: 0x04004E9D RID: 20125
		private int m_count = 0;

		// Token: 0x04004E9E RID: 20126
		private int m_probability = 0;

		// Token: 0x04004E9F RID: 20127
		private int m_delay = 0;

		// Token: 0x04004EA0 RID: 20128
		private int m_coldDown = 0;

		// Token: 0x04004EA1 RID: 20129
		private int m_currentId;

		// Token: 0x04004EA2 RID: 20130
		private int m_added = 0;
	}
}
