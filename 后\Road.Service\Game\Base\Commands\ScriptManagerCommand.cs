﻿using System;
using System.IO;
using System.Reflection;
using Game.Server.Managers;

namespace Game.Base.Commands
{
	// Token: 0x02000F9A RID: 3994
	[Cmd("&sm", ePrivLevel.Player, "Script Manager console commands.", new string[] { "   /sm  <option>  [para1][para2]...", "eg: /sm -list              : List all assemblies in scripts array.", "    /sm -add <assembly>    : Add assembly into the scripts array.", "    /sm -remove <assembly> : Remove assembly from the scripts array." })]
	public class ScriptManagerCommand : AbstractCommandHandler, ICommandHandler
	{
		// Token: 0x060087C7 RID: 34759 RVA: 0x002C86A0 File Offset: 0x002C68A0
		public bool OnCommand(BaseClient client, string[] args)
		{
			bool flag = args.Length > 1;
			bool flag2;
			if (flag)
			{
				string text = args[1];
				string text2 = text;
				if (!(text2 == "-list"))
				{
					if (!(text2 == "-add"))
					{
						if (!(text2 == "-remove"))
						{
							this.DisplayMessage(client, "Can't fine option:{0}", new object[] { args[1] });
							flag2 = true;
						}
						else
						{
							bool flag3 = args.Length > 2 && args[2] != null && File.Exists(args[2]);
							if (flag3)
							{
								try
								{
									Assembly assembly = Assembly.LoadFile(args[2]);
									bool flag4 = ScriptMgr.RemoveAssembly(assembly);
									if (flag4)
									{
										this.DisplayMessage(client, "Remove assembly success!");
										return true;
									}
									this.DisplayMessage(client, "Assembly didn't exist in the scripts array!");
									return false;
								}
								catch (Exception ex)
								{
									this.DisplayMessage(client, "Remove assembly error:", new object[] { ex.Message });
									return false;
								}
							}
							this.DisplayMessage(client, "Can't find remove assembly!");
							flag2 = false;
						}
					}
					else
					{
						bool flag5 = args.Length > 2 && args[2] != null && File.Exists(args[2]);
						if (flag5)
						{
							try
							{
								Assembly assembly2 = Assembly.LoadFile(args[2]);
								bool flag6 = ScriptMgr.InsertAssembly(assembly2);
								if (flag6)
								{
									this.DisplayMessage(client, "Add assembly success!");
									return true;
								}
								this.DisplayMessage(client, "Assembly already exists in the scripts array!");
								return false;
							}
							catch (Exception ex2)
							{
								this.DisplayMessage(client, "Add assembly error:", new object[] { ex2.Message });
								return false;
							}
						}
						this.DisplayMessage(client, "Can't find add assembly!");
						flag2 = false;
					}
				}
				else
				{
					Assembly[] scripts = ScriptMgr.Scripts;
					Assembly[] array = scripts;
					Assembly[] array2 = array;
					foreach (Assembly assembly3 in array2)
					{
						this.DisplayMessage(client, assembly3.FullName);
					}
					flag2 = true;
				}
			}
			else
			{
				this.DisplaySyntax(client);
				flag2 = true;
			}
			return flag2;
		}
	}
}
