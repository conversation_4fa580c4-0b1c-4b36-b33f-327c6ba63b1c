﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000ED8 RID: 3800
	public class AddGuardTurnEffect : BasePlayerEffect
	{
		// Token: 0x060082BF RID: 33471 RVA: 0x000339B5 File Offset: 0x00031BB5
		public AddGuardTurnEffect(int count, int probability)
			: base(eEffectType.AddGuardTurnEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x060082C0 RID: 33472 RVA: 0x002B1948 File Offset: 0x002AFB48
		public override bool Start(Living living)
		{
			AddGuardTurnEffect addGuardTurnEffect = living.EffectList.GetOfType(eEffectType.AddGuardTurnEffect) as AddGuardTurnEffect;
			bool flag = addGuardTurnEffect != null;
			bool flag2;
			if (flag)
			{
				this.m_probability = ((this.m_probability > addGuardTurnEffect.m_probability) ? this.m_probability : addGuardTurnEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082C1 RID: 33473 RVA: 0x000339DD File Offset: 0x00031BDD
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
			player.BeginSelfTurn += this.player_SelfTurn;
			player.Game.SendPlayerPicture(player, 30, true);
		}

		// Token: 0x060082C2 RID: 33474 RVA: 0x00033A16 File Offset: 0x00031C16
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
			player.BeginSelfTurn -= this.player_SelfTurn;
			player.Game.SendPlayerPicture(player, 30, false);
		}

		// Token: 0x060082C3 RID: 33475 RVA: 0x002B19A4 File Offset: 0x002AFBA4
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			damageAmount -= this.m_count;
			bool flag = damageAmount <= 0;
			if (flag)
			{
				damageAmount = 1;
			}
		}

		// Token: 0x060082C4 RID: 33476 RVA: 0x002B19D0 File Offset: 0x002AFBD0
		private void player_SelfTurn(Living living)
		{
			this.m_probability--;
			bool flag = this.m_probability < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x040051D7 RID: 20951
		private int m_count = 0;

		// Token: 0x040051D8 RID: 20952
		private int m_probability = 0;
	}
}
