﻿using System;
using System.Collections;
using System.IO;
using System.Text;
using System.Xml;

namespace Game.Base.Config
{
	// Token: 0x02000F97 RID: 3991
	public class XMLConfigFile : ConfigElement
	{
		// Token: 0x060087BD RID: 34749 RVA: 0x00036027 File Offset: 0x00034227
		public XMLConfigFile()
			: base(null)
		{
		}

		// Token: 0x060087BE RID: 34750 RVA: 0x00036032 File Offset: 0x00034232
		protected XMLConfigFile(ConfigElement parent)
			: base(parent)
		{
		}

		// Token: 0x060087BF RID: 34751 RVA: 0x002C825C File Offset: 0x002C645C
		protected bool IsBadXMLElementName(string name)
		{
			bool flag = name == null;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = name.IndexOf("\\") != -1;
				if (flag3)
				{
					flag2 = true;
				}
				else
				{
					bool flag4 = name.IndexOf("/") != -1;
					if (flag4)
					{
						flag2 = true;
					}
					else
					{
						bool flag5 = name.IndexOf("<") != -1;
						if (flag5)
						{
							flag2 = true;
						}
						else
						{
							bool flag6 = name.IndexOf(">") != -1;
							flag2 = flag6;
						}
					}
				}
			}
			return flag2;
		}

		// Token: 0x060087C0 RID: 34752 RVA: 0x002C82E8 File Offset: 0x002C64E8
		protected void SaveElement(XmlTextWriter writer, string name, ConfigElement element)
		{
			bool flag = this.IsBadXMLElementName(name);
			bool hasChildren = element.HasChildren;
			if (hasChildren)
			{
				bool flag2 = name == null;
				if (flag2)
				{
					name = "root";
				}
				bool flag3 = flag;
				if (flag3)
				{
					writer.WriteStartElement("param");
					writer.WriteAttributeString("name", name);
				}
				else
				{
					writer.WriteStartElement(name);
				}
				foreach (object obj in element.Children)
				{
					DictionaryEntry dictionaryEntry = (DictionaryEntry)obj;
					this.SaveElement(writer, (string)dictionaryEntry.Key, (ConfigElement)dictionaryEntry.Value);
				}
				writer.WriteEndElement();
			}
			else
			{
				bool flag4 = name != null;
				if (flag4)
				{
					bool flag5 = flag;
					if (flag5)
					{
						writer.WriteStartElement("param");
						writer.WriteAttributeString("name", name);
						writer.WriteString(element.GetString());
						writer.WriteEndElement();
					}
					else
					{
						writer.WriteElementString(name, element.GetString());
					}
				}
			}
		}

		// Token: 0x060087C1 RID: 34753 RVA: 0x002C8418 File Offset: 0x002C6618
		public void Save(FileInfo configFile)
		{
			bool exists = configFile.Exists;
			if (exists)
			{
				configFile.Delete();
			}
			XmlTextWriter xmlTextWriter = new XmlTextWriter(configFile.FullName, Encoding.UTF8);
			xmlTextWriter.Formatting = Formatting.Indented;
			xmlTextWriter.WriteStartDocument();
			this.SaveElement(xmlTextWriter, null, this);
			xmlTextWriter.WriteEndDocument();
			xmlTextWriter.Close();
		}

		// Token: 0x060087C2 RID: 34754 RVA: 0x002C8474 File Offset: 0x002C6674
		public static XMLConfigFile ParseXMLFile(FileInfo configFile)
		{
			XMLConfigFile xmlconfigFile = new XMLConfigFile(null);
			bool flag = !configFile.Exists;
			XMLConfigFile xmlconfigFile2;
			if (flag)
			{
				xmlconfigFile2 = xmlconfigFile;
			}
			else
			{
				ConfigElement configElement = xmlconfigFile;
				XmlTextReader xmlTextReader = new XmlTextReader(configFile.OpenRead());
				while (xmlTextReader.Read())
				{
					bool flag2 = xmlTextReader.NodeType == XmlNodeType.Element;
					if (flag2)
					{
						bool flag3 = xmlTextReader.Name == "root";
						if (!flag3)
						{
							bool flag4 = xmlTextReader.Name == "param";
							if (flag4)
							{
								string attribute = xmlTextReader.GetAttribute("name");
								bool flag5 = attribute != null && attribute != "root";
								if (flag5)
								{
									ConfigElement configElement2 = (configElement[attribute] = new ConfigElement(configElement));
									ConfigElement configElement3 = configElement2;
									ConfigElement configElement4 = configElement3;
									configElement = configElement4;
								}
							}
							else
							{
								ConfigElement configElement5 = new ConfigElement(configElement);
								configElement[xmlTextReader.Name] = configElement5;
								configElement = configElement5;
							}
						}
					}
					else
					{
						bool flag6 = xmlTextReader.NodeType == XmlNodeType.Text;
						if (flag6)
						{
							configElement.Set(xmlTextReader.Value);
						}
						else
						{
							bool flag7 = xmlTextReader.NodeType == XmlNodeType.EndElement && xmlTextReader.Name != "root";
							if (flag7)
							{
								configElement = configElement.Parent;
							}
						}
					}
				}
				xmlTextReader.Close();
				xmlconfigFile2 = xmlconfigFile;
			}
			return xmlconfigFile2;
		}
	}
}
