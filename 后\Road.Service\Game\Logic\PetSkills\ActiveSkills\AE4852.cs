﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D21 RID: 3361
	public class AE4852 : BasePetEffect
	{
		// Token: 0x0600790B RID: 30987 RVA: 0x0002D2B2 File Offset: 0x0002B4B2
		public AE4852(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE4852, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentid = skillId;
		}

		// Token: 0x0600790C RID: 30988 RVA: 0x00288FF0 File Offset: 0x002871F0
		public override bool Start(Living living)
		{
			AE4852 ae = living.PetEffectList.GetOfType(ePetEffectType.AE4852) as AE4852;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600790D RID: 30989 RVA: 0x0002D2DD File Offset: 0x0002B4DD
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x0600790E RID: 30990 RVA: 0x0002D2F3 File Offset: 0x0002B4F3
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x0600790F RID: 30991 RVA: 0x00289050 File Offset: 0x00287250
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_currentid;
			if (flag)
			{
				int petMP = (living as Player).PetMP;
				(living as Player).RemovePetMP(petMP);
				int num = (int)((double)(petMP / 20) * living.BaseDamage / 100.0) + living.MakeDamage(living);
				living.SyncAtTime = true;
				living.ChangeDamage((double)num);
				living.SyncAtTime = false;
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
			}
		}

		// Token: 0x040046EA RID: 18154
		private int m_probability;

		// Token: 0x040046EB RID: 18155
		private int m_currentid;
	}
}
