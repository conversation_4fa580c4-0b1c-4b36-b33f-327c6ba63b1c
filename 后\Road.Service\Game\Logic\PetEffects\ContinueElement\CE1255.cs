﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EB4 RID: 3764
	public class CE1255 : BasePetEffect
	{
		// Token: 0x060081E6 RID: 33254 RVA: 0x002AE92C File Offset: 0x002ACB2C
		public CE1255(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1255, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081E7 RID: 33255 RVA: 0x002AE9AC File Offset: 0x002ACBAC
		public override bool Start(Living living)
		{
			CE1255 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1255) as CE1255;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081E8 RID: 33256 RVA: 0x000330F6 File Offset: 0x000312F6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081E9 RID: 33257 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081EA RID: 33258 RVA: 0x002AEA0C File Offset: 0x002ACC0C
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 200;
				bool flag2 = living.Attack < (double)this.m_added;
				if (flag2)
				{
					this.m_added = (int)living.Attack - 1;
				}
				living.Attack -= (double)this.m_added;
			}
		}

		// Token: 0x060081EB RID: 33259 RVA: 0x002AEA6C File Offset: 0x002ACC6C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060081EC RID: 33260 RVA: 0x002AEAA0 File Offset: 0x002ACCA0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Attack += (double)this.m_added;
			this.m_added = 0;
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005129 RID: 20777
		private int m_type = 0;

		// Token: 0x0400512A RID: 20778
		private int m_count = 0;

		// Token: 0x0400512B RID: 20779
		private int m_probability = 0;

		// Token: 0x0400512C RID: 20780
		private int m_delay = 0;

		// Token: 0x0400512D RID: 20781
		private int m_coldDown = 0;

		// Token: 0x0400512E RID: 20782
		private int m_currentId;

		// Token: 0x0400512F RID: 20783
		private int m_added = 0;
	}
}
