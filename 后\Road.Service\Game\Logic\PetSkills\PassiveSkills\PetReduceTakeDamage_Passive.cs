﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CF8 RID: 3320
	public class PetReduceTakeDamage_Passive : AbstractPetEffect
	{
		// Token: 0x06007836 RID: 30774 RVA: 0x002848A0 File Offset: 0x00282AA0
		public PetReduceTakeDamage_Passive(int probability, string elementID)
			: base(ePetEffectType.PetReduceTakeDamage_Passive, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_elementID = elementID;
			bool flag = elementID == null;
			if (!flag)
			{
				int length = elementID.Length;
				bool flag2 = length != 4;
				if (!flag2)
				{
					switch (elementID[3])
					{
					case '0':
					{
						bool flag3 = !(elementID == "4260");
						if (flag3)
						{
							bool flag4 = elementID == "3490";
							if (flag4)
							{
								this.m_value = 60.0;
								this.m_percent = true;
							}
						}
						else
						{
							this.m_value = 20.0;
							this.m_percent = true;
						}
						return;
					}
					case '1':
						return;
					case '2':
					{
						bool flag5 = elementID == "4262";
						if (flag5)
						{
							this.m_value = 50.0;
							this.m_percent = true;
						}
						return;
					}
					case '3':
					{
						bool flag6 = elementID == "3223";
						if (flag6)
						{
							this.m_value = 30.0;
							this.m_percent = true;
						}
						return;
					}
					case '4':
						if (!(elementID == "1804"))
						{
							if (!(elementID == "1774"))
							{
								if (elementID == "1654")
								{
									this.m_value = 30.0;
									this.m_percent = true;
								}
							}
							else
							{
								this.m_value = 7.0;
								this.m_percent = true;
							}
						}
						else
						{
							this.m_value = 2.0;
							this.m_percent = true;
						}
						return;
					case '5':
					{
						bool flag7 = !(elementID == "1655");
						if (!flag7)
						{
							this.m_value = 20.0;
							return;
						}
						bool flag8 = !(elementID == "1805");
						if (flag8)
						{
							return;
						}
						goto IL_02DC;
					}
					case '6':
					{
						bool flag9 = !(elementID == "1656");
						if (!flag9)
						{
							this.m_value = 20.0;
							return;
						}
						bool flag10 = !(elementID == "1806");
						if (flag10)
						{
							return;
						}
						break;
					}
					case '7':
					{
						bool flag11 = !(elementID == "1767");
						if (flag11)
						{
							return;
						}
						goto IL_02DC;
					}
					case '8':
					{
						bool flag12 = !(elementID == "1768");
						if (flag12)
						{
							return;
						}
						break;
					}
					case '9':
					{
						bool flag13 = elementID == "1769";
						if (flag13)
						{
							this.m_value = 5.0;
							this.m_percent = true;
						}
						return;
					}
					default:
						return;
					}
					this.m_value = 4.0;
					this.m_percent = true;
					return;
					IL_02DC:
					this.m_value = 3.0;
					this.m_percent = true;
				}
			}
		}

		// Token: 0x06007837 RID: 30775 RVA: 0x00284BA0 File Offset: 0x00282DA0
		public override bool Start(Living living)
		{
			PetReduceTakeDamage_Passive petReduceTakeDamage_Passive = living.PetEffectList.GetOfType(ePetEffectType.PetReduceTakeDamage_Passive) as PetReduceTakeDamage_Passive;
			bool flag = petReduceTakeDamage_Passive != null;
			bool flag2;
			if (flag)
			{
				petReduceTakeDamage_Passive.m_probability = ((this.m_probability > petReduceTakeDamage_Passive.m_probability) ? this.m_probability : petReduceTakeDamage_Passive.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007838 RID: 30776 RVA: 0x0002C7F4 File Offset: 0x0002A9F4
		public override void OnAttached(Living player)
		{
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
		}

		// Token: 0x06007839 RID: 30777 RVA: 0x0002C80A File Offset: 0x0002AA0A
		public override void OnRemoved(Living player)
		{
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
		}

		// Token: 0x0600783A RID: 30778 RVA: 0x00284C00 File Offset: 0x00282E00
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.m_elementID == "3223" || this.m_elementID == "3490" || this.m_elementID == "4260" || this.m_elementID == "1654";
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.Info, false);
				damageAmount -= damageAmount * (int)this.m_value / 100;
				living.Game.sendShowPicSkil(living, base.Info, true);
			}
			else
			{
				bool flag2 = this.m_elementID == "4260" && criticalAmount > 0;
				if (flag2)
				{
					criticalAmount -= criticalAmount * (int)this.m_value / 100;
				}
				bool flag3 = this.m_elementID == "4262" && criticalAmount > 0;
				if (flag3)
				{
					criticalAmount -= criticalAmount * (int)this.m_value / 100;
				}
				bool flag4 = !(this.m_elementID == "1655") && !(this.m_elementID == "1656");
				if (!flag4)
				{
					living.Game.sendShowPicSkil(living, base.Info, false);
					bool flag5 = damageAmount > (int)((double)living.MaxBlood * this.m_value / 100.0);
					if (flag5)
					{
						bool flag6 = damageAmount > (int)((double)living.MaxBlood * this.m_value / 100.0);
						if (flag6)
						{
							damageAmount = (int)((double)living.MaxBlood * this.m_value / 100.0);
						}
						criticalAmount = 0;
					}
					living.Game.sendShowPicSkil(living, base.Info, true);
				}
			}
		}

		// Token: 0x04004675 RID: 18037
		private int m_probability;

		// Token: 0x04004676 RID: 18038
		private double m_value;

		// Token: 0x04004677 RID: 18039
		private bool m_percent;

		// Token: 0x04004678 RID: 18040
		private string m_elementID;
	}
}
