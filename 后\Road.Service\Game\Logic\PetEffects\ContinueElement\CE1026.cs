﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E4C RID: 3660
	public class CE1026 : BasePetEffect
	{
		// Token: 0x06007F53 RID: 32595 RVA: 0x002A4AEC File Offset: 0x002A2CEC
		public CE1026(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1026, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F54 RID: 32596 RVA: 0x002A4B6C File Offset: 0x002A2D6C
		public override bool Start(Living living)
		{
			CE1026 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1026) as CE1026;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F55 RID: 32597 RVA: 0x00031773 File Offset: 0x0002F973
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F56 RID: 32598 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F57 RID: 32599 RVA: 0x002A4BCC File Offset: 0x002A2DCC
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 300;
				living.SyncAtTime = true;
				living.AddBlood(this.m_added);
				living.SyncAtTime = false;
				List<Living> list = living.Game.Map.FindAllNearestSameTeam(living.X, living.Y, 250.0, living);
				foreach (Living living2 in list)
				{
					living2.SyncAtTime = true;
					living2.AddBlood(this.m_added);
					living2.SyncAtTime = false;
				}
			}
		}

		// Token: 0x06007F58 RID: 32600 RVA: 0x0003179C File Offset: 0x0002F99C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E4F RID: 20047
		private int m_type = 0;

		// Token: 0x04004E50 RID: 20048
		private int m_count = 0;

		// Token: 0x04004E51 RID: 20049
		private int m_probability = 0;

		// Token: 0x04004E52 RID: 20050
		private int m_delay = 0;

		// Token: 0x04004E53 RID: 20051
		private int m_coldDown = 0;

		// Token: 0x04004E54 RID: 20052
		private int m_currentId;

		// Token: 0x04004E55 RID: 20053
		private int m_added = 0;
	}
}
