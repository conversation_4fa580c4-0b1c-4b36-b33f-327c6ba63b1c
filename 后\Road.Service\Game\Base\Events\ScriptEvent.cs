﻿using System;

namespace Game.Base.Events
{
	// Token: 0x02000F91 RID: 3985
	public class ScriptEvent : RoadEvent
	{
		// Token: 0x060087A1 RID: 34721 RVA: 0x00035EA3 File Offset: 0x000340A3
		protected ScriptEvent(string name)
			: base(name)
		{
		}

		// Token: 0x040053B9 RID: 21433
		public static readonly ScriptEvent Loaded = new ScriptEvent("Script.Loaded");

		// Token: 0x040053BA RID: 21434
		public static readonly ScriptEvent Unloaded = new ScriptEvent("Script.Unloaded");
	}
}
