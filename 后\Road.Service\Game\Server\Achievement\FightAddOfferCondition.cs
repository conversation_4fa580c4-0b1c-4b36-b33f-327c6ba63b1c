﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C62 RID: 3170
	public class FightAddOfferCondition : BaseCondition
	{
		// Token: 0x06007077 RID: 28791 RVA: 0x0002A421 File Offset: 0x00028621
		public FightAddOfferCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x06007078 RID: 28792 RVA: 0x0002A45A File Offset: 0x0002865A
		public override void AddTrigger(GamePlayer player)
		{
			player.FightAddOfferEvent += this.player_FightAddOfferEvent;
		}

		// Token: 0x06007079 RID: 28793 RVA: 0x0002A470 File Offset: 0x00028670
		private void player_FightAddOfferEvent(int offer)
		{
			base.Value += offer;
		}

		// Token: 0x0600707A RID: 28794 RVA: 0x0002A482 File Offset: 0x00028682
		public override void RemoveTrigger(GamePlayer player)
		{
			player.FightAddOfferEvent -= this.player_FightAddOfferEvent;
		}

		// Token: 0x0600707B RID: 28795 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
