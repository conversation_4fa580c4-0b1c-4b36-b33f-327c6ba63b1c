﻿using System;
using System.Collections.Generic;
using System.Text;
using Bussiness;
using Bussiness.Managers;
using Game.Base.Packets;
using Game.Server.GameObjects;
using Game.Server.Packets;
using SqlDataProvider.Data;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C4F RID: 3151
	[ActiveSystemHandleAttbute(36)]
	public class CatchbeastGetAward : IActiveSystemCommandHadler
	{
		// Token: 0x06007020 RID: 28704 RVA: 0x0024D500 File Offset: 0x0024B700
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			int num = packet.ReadInt();
			string[] array = GameProperties.YearMonsterBoxInfo.Split(new char[] { '|' });
			bool flag = this.CanGetGift(Player.Actives.Info.DamageNum, num, array);
			bool flag2 = flag;
			if (flag2)
			{
				int num2 = int.Parse(array[num].Split(new char[] { ',' })[0]);
				gspacketIn.WriteByte(36);
				gspacketIn.WriteBoolean(flag);
				gspacketIn.WriteInt(num);
				Player.Out.SendTCP(gspacketIn);
				Player.Actives.SetYearMonterBoxState(num);
				List<ItemInfo> list = new List<ItemInfo>();
				SpecialItemBoxInfo specialItemBoxInfo = new SpecialItemBoxInfo();
				ItemBoxMgr.CreateItemBox(num2, list, specialItemBoxInfo);
				StringBuilder stringBuilder = new StringBuilder();
				foreach (ItemInfo itemInfo in list)
				{
					stringBuilder.Append(itemInfo.Template.Name + " x" + itemInfo.Count.ToString() + ", ");
				}
				Player.Out.SendMessage(eMessageType.Normal, stringBuilder.ToString());
				Player.AddTemplate(list);
			}
			return true;
		}

		// Token: 0x06007021 RID: 28705 RVA: 0x0024D670 File Offset: 0x0024B870
		private bool CanGetGift(int damageNum, int id, string[] YearMonsterBoxInfo)
		{
			bool flag = id > 4 || id < 0;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				int num = int.Parse(YearMonsterBoxInfo[id].Split(new char[] { ',' })[1]) * 10000;
				flag2 = num <= damageNum;
			}
			return flag2;
		}
	}
}
