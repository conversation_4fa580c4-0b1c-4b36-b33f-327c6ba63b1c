﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F59 RID: 3929
	public class LivingJumpAction : BaseAction
	{
		// Token: 0x0600850A RID: 34058 RVA: 0x002B98F8 File Offset: 0x002B7AF8
		public LivingJumpAction(Living living, int toX, int toY, int speed, string action, int delay, int type, LivingCallBack callback)
			: base(delay, 2000)
		{
			this.m_living = living;
			this.m_toX = toX;
			this.m_toY = toY;
			this.m_speed = speed;
			this.m_action = action;
			this.m_isSent = false;
			this.m_type = type;
			this.m_callback = callback;
		}

		// Token: 0x0600850B RID: 34059 RVA: 0x002B9950 File Offset: 0x002B7B50
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			bool flag = !this.m_isSent;
			if (flag)
			{
				this.m_isSent = true;
				game.SendLivingJump(this.m_living, this.m_toX, this.m_toY, this.m_speed, this.m_action, this.m_type);
			}
			bool flag2 = this.m_toY < this.m_living.Y - this.m_speed;
			if (flag2)
			{
				this.m_living.SetXY(this.m_toX, this.m_living.Y - this.m_speed);
			}
			else
			{
				this.m_living.SetXY(this.m_toX, this.m_toY);
				bool flag3 = this.m_callback != null;
				if (flag3)
				{
					this.m_living.CallFuction(this.m_callback, 0);
				}
				base.Finish(tick);
			}
		}

		// Token: 0x040052FB RID: 21243
		private Living m_living;

		// Token: 0x040052FC RID: 21244
		private int m_toX;

		// Token: 0x040052FD RID: 21245
		private int m_toY;

		// Token: 0x040052FE RID: 21246
		private int m_speed;

		// Token: 0x040052FF RID: 21247
		private string m_action;

		// Token: 0x04005300 RID: 21248
		private bool m_isSent;

		// Token: 0x04005301 RID: 21249
		private int m_type;

		// Token: 0x04005302 RID: 21250
		private LivingCallBack m_callback;
	}
}
