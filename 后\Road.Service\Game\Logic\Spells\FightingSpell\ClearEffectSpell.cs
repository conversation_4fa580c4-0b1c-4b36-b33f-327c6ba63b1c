﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CC5 RID: 3269
	[SpellAttibute(83)]
	public class ClearEffectSpell : ISpellHandler
	{
		// Token: 0x0600750C RID: 29964 RVA: 0x0026E7F0 File Offset: 0x0026C9F0
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.ClearBuff = true;
			}
			else
			{
				bool flag = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
				if (flag)
				{
					game.CurrentLiving.ClearBuff = true;
				}
			}
		}
	}
}
