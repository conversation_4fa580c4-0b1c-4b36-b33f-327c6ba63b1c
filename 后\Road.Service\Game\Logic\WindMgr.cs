﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Reflection;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000CAF RID: 3247
	public class WindMgr
	{
		// Token: 0x060074D7 RID: 29911 RVA: 0x0026D5E8 File Offset: 0x0026B7E8
		public static bool Init()
		{
			bool flag;
			try
			{
				WindMgr._winds = new Dictionary<int, WindInfo>();
				WindMgr.rand = new Random();
				WindMgr.ReLoad();
				flag = WindMgr.LoadWinds(WindMgr._winds);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = WindMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					WindMgr.log.Error("WindInfoMgr", ex);
				}
				flag = false;
			}
			return flag;
		}

		// Token: 0x060074D8 RID: 29912 RVA: 0x0026D658 File Offset: 0x0026B858
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, WindInfo> dictionary = new Dictionary<int, WindInfo>();
				bool flag = WindMgr.LoadWinds(dictionary);
				if (flag)
				{
					try
					{
						WindMgr._winds = dictionary;
						return true;
					}
					catch
					{
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = WindMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					WindMgr.log.Error("WindMgr", ex);
				}
			}
			return false;
		}

		// Token: 0x060074D9 RID: 29913 RVA: 0x0026D6D8 File Offset: 0x0026B8D8
		public static byte[] CreateImage(string randomcode)
		{
			int num = 20;
			Bitmap bitmap = new Bitmap(16, 29);
			Graphics graphics = Graphics.FromImage(bitmap);
			int num2 = WindMgr.rand.Next(WindMgr.font.Length);
			FontFamily fontFamily = new FontFamily(WindMgr.font[num2]);
			GraphicsPath graphicsPath = new GraphicsPath();
			Pen pen = new Pen(Color.Black, 5f);
			int num3 = WindMgr.rand.Next(2);
			Color color = Color.White;
			Color color2 = Color.BurlyWood;
			int num4 = num3;
			int num5 = num4;
			bool flag = num5 == 1;
			if (flag)
			{
				color2 = WindMgr.c[WindMgr.rand.Next(6)];
				color = Color.White;
			}
			else
			{
				color = WindMgr.c[WindMgr.rand.Next(6)];
				color2 = Color.White;
			}
			num3 = WindMgr.rand.Next(4);
			Rectangle rectangle = new Rectangle(10, 10, 16, 26);
			Color color3 = color;
			Color color4 = color2;
			if (!true)
			{
			}
			LinearGradientMode linearGradientMode;
			switch (num3)
			{
			case 1:
				linearGradientMode = LinearGradientMode.BackwardDiagonal;
				break;
			case 2:
				linearGradientMode = LinearGradientMode.ForwardDiagonal;
				break;
			case 3:
				linearGradientMode = LinearGradientMode.Vertical;
				break;
			default:
				linearGradientMode = LinearGradientMode.Horizontal;
				break;
			}
			if (!true)
			{
			}
			LinearGradientMode linearGradientMode2 = linearGradientMode;
			LinearGradientMode linearGradientMode3 = linearGradientMode2;
			LinearGradientBrush linearGradientBrush = new LinearGradientBrush(rectangle, color3, color4, linearGradientMode3);
			byte[] array;
			try
			{
				graphics.SmoothingMode = SmoothingMode.AntiAlias;
				graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
				StringFormat stringFormat = new StringFormat(StringFormatFlags.NoClip)
				{
					Alignment = StringAlignment.Center,
					LineAlignment = StringAlignment.Center
				};
				graphicsPath.AddString(randomcode, fontFamily, 1, 16f, new Point(1, 1), stringFormat);
				pen.LineJoin = LineJoin.Round;
				Point point = new Point(7, 12);
				float num6 = (float)WindMgr.rand.Next(-num, num);
				graphics.TranslateTransform((float)point.X, (float)point.Y);
				graphics.RotateTransform(num6);
				graphics.DrawPath(pen, graphicsPath);
				graphics.FillPath(linearGradientBrush, graphicsPath);
				graphics.RotateTransform(0f - num6);
				MemoryStream memoryStream = new MemoryStream();
				bitmap.Save(memoryStream, ImageFormat.Png);
				array = memoryStream.ToArray();
			}
			finally
			{
				graphics.Dispose();
				bitmap.Dispose();
				fontFamily.Dispose();
				graphicsPath.Dispose();
				pen.Dispose();
				linearGradientBrush.Dispose();
			}
			return array;
		}

		// Token: 0x060074DA RID: 29914 RVA: 0x0026D938 File Offset: 0x0026BB38
		private static bool LoadWinds(Dictionary<int, WindInfo> Winds)
		{
			int[] windID = WindMgr.WindID;
			int[] array = windID;
			int[] array2 = array;
			foreach (int num in array2)
			{
				WindInfo windInfo = new WindInfo();
				byte[] array4 = WindMgr.CreateImage(WindMgr.fontWind[num]);
				bool flag = array4 != null && array4.Length != 0;
				if (!flag)
				{
					bool isErrorEnabled = WindMgr.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						WindMgr.log.Error("Load Wind Error!");
					}
					return false;
				}
				windInfo.WindID = num;
				windInfo.WindPic = array4;
				bool flag2 = !Winds.ContainsKey(num);
				if (flag2)
				{
					Winds.Add(num, windInfo);
				}
			}
			return true;
		}

		// Token: 0x060074DB RID: 29915 RVA: 0x0026DA04 File Offset: 0x0026BC04
		public static List<WindInfo> GetWind()
		{
			List<WindInfo> list = new List<WindInfo>();
			for (int i = 0; i < WindMgr._winds.Values.Count; i++)
			{
				list.Add(WindMgr._winds[i]);
			}
			bool flag = list.Count > 0;
			List<WindInfo> list2;
			if (flag)
			{
				list2 = list;
			}
			else
			{
				list2 = null;
			}
			return list2;
		}

		// Token: 0x060074DC RID: 29916 RVA: 0x0026DA64 File Offset: 0x0026BC64
		public static byte GetWindID(int wind, int pos)
		{
			bool flag = wind < 10;
			if (flag)
			{
				if (pos == 1)
				{
					return 10;
				}
				if (pos == 3)
				{
					return (byte)((wind == 0) ? 10 : wind);
				}
			}
			bool flag2 = wind >= 10 && wind < 20;
			if (flag2)
			{
				if (pos == 1)
				{
					return 1;
				}
				if (pos == 3)
				{
					return (byte)((wind - 10 == 0) ? 10 : (wind - 10));
				}
			}
			bool flag3 = wind >= 20 && wind < 30;
			if (flag3)
			{
				if (pos == 1)
				{
					return 2;
				}
				if (pos == 3)
				{
					return (byte)((wind - 20 == 0) ? 10 : (wind - 20));
				}
			}
			bool flag4 = wind >= 30 && wind < 40;
			if (flag4)
			{
				if (pos == 1)
				{
					return 3;
				}
				if (pos == 3)
				{
					return (byte)((wind - 30 == 0) ? 10 : (wind - 30));
				}
			}
			bool flag5 = wind >= 40 && wind < 50;
			if (flag5)
			{
				if (pos == 1)
				{
					return 4;
				}
				if (pos == 3)
				{
					return (byte)((wind - 40 == 0) ? 10 : (wind - 40));
				}
			}
			return 0;
		}

		// Token: 0x04004454 RID: 17492
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04004455 RID: 17493
		private static readonly int[] CategoryID = new int[] { 1001, 1002, 1003, 1004, 1005, 1005, 1007, 1008, 1009 };

		// Token: 0x04004456 RID: 17494
		private static readonly int[] WindID = new int[]
		{
			0, 1, 2, 3, 4, 5, 6, 7, 8, 9,
			10
		};

		// Token: 0x04004457 RID: 17495
		private static readonly string[] fontWind = new string[]
		{
			".", "1", "2", "3", "4", "5", "6", "7", "8", "9",
			"0"
		};

		// Token: 0x04004458 RID: 17496
		private static readonly Color[] c = new Color[]
		{
			Color.Yellow,
			Color.Red,
			Color.Blue,
			Color.Green,
			Color.Orange,
			Color.Aqua,
			Color.DarkCyan,
			Color.Purple
		};

		// Token: 0x04004459 RID: 17497
		private static readonly string[] font = new string[] { "Verdana", "Comic Sans MS", "Tahoma" };

		// Token: 0x0400445A RID: 17498
		private static Dictionary<int, WindInfo> _winds;

		// Token: 0x0400445B RID: 17499
		private static Random rand;
	}
}
