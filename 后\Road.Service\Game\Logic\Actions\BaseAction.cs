﻿using System;

namespace Game.Logic.Actions
{
	// Token: 0x02000F42 RID: 3906
	public class BaseAction : IAction
	{
		// Token: 0x060084D3 RID: 34003 RVA: 0x0003506E File Offset: 0x0003326E
		public BaseAction(int delay)
			: this(delay, 0)
		{
		}

		// Token: 0x060084D4 RID: 34004 RVA: 0x0003507A File Offset: 0x0003327A
		public BaseAction(int delay, int finishDelay)
		{
			this.m_tick = TickHelper.GetTickCount() + (long)delay;
			this.m_finishDelay = (long)finishDelay;
			this.m_finishTick = long.MaxValue;
		}

		// Token: 0x060084D5 RID: 34005 RVA: 0x002B8C08 File Offset: 0x002B6E08
		public void Execute(BaseGame game, long tick)
		{
			bool flag = this.m_tick <= tick && this.m_finishTick == long.MaxValue;
			if (flag)
			{
				this.ExecuteImp(game, tick);
			}
		}

		// Token: 0x060084D6 RID: 34006 RVA: 0x000350A9 File Offset: 0x000332A9
		protected virtual void ExecuteImp(BaseGame game, long tick)
		{
			this.Finish(tick);
		}

		// Token: 0x060084D7 RID: 34007 RVA: 0x000350B4 File Offset: 0x000332B4
		public void Finish(long tick)
		{
			this.m_finishTick = tick + this.m_finishDelay;
		}

		// Token: 0x060084D8 RID: 34008 RVA: 0x002B8C44 File Offset: 0x002B6E44
		public bool IsFinished(BaseGame game, long tick)
		{
			return (game is PVEGame && (game as PVEGame).IsPassDrama) || this.m_finishTick <= tick;
		}

		// Token: 0x040052B0 RID: 21168
		private long m_tick;

		// Token: 0x040052B1 RID: 21169
		private long m_finishDelay;

		// Token: 0x040052B2 RID: 21170
		private long m_finishTick;
	}
}
