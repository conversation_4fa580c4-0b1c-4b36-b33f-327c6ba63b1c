﻿using System;
using System.Collections.Generic;
using System.Drawing;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F5D RID: 3933
	public class LivingRangeAttackingAction : BaseAction
	{
		// Token: 0x06008512 RID: 34066 RVA: 0x002B9C5C File Offset: 0x002B7E5C
		public LivingRangeAttackingAction(Living living, int fx, int tx, string action, int delay, bool removeFrost, bool directDamage, List<Player> players)
			: base(delay, 1000)
		{
			this.m_living = living;
			this.m_players = players;
			this.m_fx = fx;
			this.m_tx = tx;
			this.m_action = action;
			this.m_removeFrost = removeFrost;
			this.m_directDamage = directDamage;
		}

		// Token: 0x06008513 RID: 34067 RVA: 0x002B9CB0 File Offset: 0x002B7EB0
		private int MakeDamage(Living p)
		{
			double baseDamage = this.m_living.BaseDamage;
			double num = p.BaseGuard;
			double num2 = p.Defence;
			double attack = this.m_living.Attack;
			bool flag = p.AddArmor && (p as Player).DeputyWeapon != null;
			if (flag)
			{
				int num3 = (p as Player).DeputyWeapon.Template.Property7 + (int)Math.Pow(1.1, (double)(p as Player).DeputyWeapon.StrengthenLevel);
				num += (double)num3;
				num2 += (double)num3;
			}
			bool ignoreArmor = this.m_living.IgnoreArmor;
			if (ignoreArmor)
			{
				num = 0.0;
				num2 = 0.0;
			}
			float currentDamagePlus = this.m_living.CurrentDamagePlus;
			float currentShootMinus = this.m_living.CurrentShootMinus;
			double num4 = 0.95 * (num - (double)(3 * this.m_living.Grade)) / (500.0 + num - (double)(3 * this.m_living.Grade));
			double num5 = ((num2 - this.m_living.Lucky >= 0.0) ? (0.95 * (num2 - this.m_living.Lucky) / (600.0 + num2 - this.m_living.Lucky)) : 0.0);
			double num6 = baseDamage * (1.0 + attack * 0.001) * (1.0 - (num4 + num5 - num4 * num5)) * (double)currentDamagePlus * (double)currentShootMinus;
			bool flag2 = !this.m_directDamage;
			if (flag2)
			{
				Rectangle directDemageRect = p.GetDirectDemageRect();
				double num7 = Math.Sqrt((double)((directDemageRect.X - this.m_living.X) * (directDemageRect.X - this.m_living.X) + (directDemageRect.Y - this.m_living.Y) * (directDemageRect.Y - this.m_living.Y)));
				num6 *= 1.0 - num7 / (double)Math.Abs(this.m_tx - this.m_fx) / 4.0;
			}
			bool flag3 = num6 < 0.0;
			int num8;
			if (flag3)
			{
				num8 = 1;
			}
			else
			{
				num8 = (int)num6;
			}
			return num8;
		}

		// Token: 0x06008514 RID: 34068 RVA: 0x002B9F1C File Offset: 0x002B811C
		private int MakeCriticalDamage(Living p, int baseDamage)
		{
			double lucky = this.m_living.Lucky;
			Random random = new Random();
			bool flag = 75000.0 * lucky / (lucky + 800.0) > (double)random.Next(100000);
			int num2;
			if (flag)
			{
				int num = (int)((0.5 + lucky * 0.0003) * (double)baseDamage);
				num2 = num * 100 / 100;
			}
			else
			{
				num2 = 0;
			}
			return num2;
		}

		// Token: 0x06008515 RID: 34069 RVA: 0x002B9F94 File Offset: 0x002B8194
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, this.m_living.Id);
			gspacketIn.Parameter1 = this.m_living.Id;
			gspacketIn.WriteByte(61);
			List<Living> list = game.Map.FindPlayers(this.m_fx, this.m_tx, this.m_players);
			int num = list.Count;
			foreach (Living living in list)
			{
				bool flag = this.m_living.IsFriendly(living);
				if (flag)
				{
					num--;
				}
			}
			gspacketIn.WriteInt(num);
			this.m_living.SyncAtTime = false;
			try
			{
				foreach (Living living2 in list)
				{
					living2.SyncAtTime = false;
					bool flag2 = this.m_living.IsFriendly(living2);
					if (!flag2)
					{
						int num2 = 0;
						living2.IsHide = false;
						game.SendGameUpdateHideState(living2);
						bool removeFrost = this.m_removeFrost;
						if (removeFrost)
						{
							living2.IsFrost = false;
							game.SendGameUpdateFrozenState(living2);
						}
						int num3 = this.MakeDamage(living2);
						int num4 = this.MakeCriticalDamage(living2, num3);
						int num5 = 0;
						bool flag3 = living2 is Player;
						if (flag3)
						{
							living2.OnTakedDamage(living2, ref num3, ref num4);
						}
						bool flag4 = living2.TakeDamage(this.m_living, ref num3, ref num4, "范围攻击");
						if (flag4)
						{
							num5 = num3 + num4;
							bool flag5 = living2 is Player;
							if (flag5)
							{
								Player player = living2 as Player;
								num2 = player.Dander;
							}
						}
						gspacketIn.WriteInt(living2.Id);
						gspacketIn.WriteInt(num5);
						gspacketIn.WriteInt(living2.Blood);
						gspacketIn.WriteInt(num2);
						gspacketIn.WriteInt(1);
					}
				}
				game.SendToAll(gspacketIn);
				base.Finish(tick);
			}
			finally
			{
				this.m_living.SyncAtTime = true;
				foreach (Living living3 in list)
				{
					living3.SyncAtTime = true;
				}
			}
		}

		// Token: 0x04005312 RID: 21266
		private Living m_living;

		// Token: 0x04005313 RID: 21267
		private List<Player> m_players;

		// Token: 0x04005314 RID: 21268
		private int m_fx;

		// Token: 0x04005315 RID: 21269
		private int m_tx;

		// Token: 0x04005316 RID: 21270
		private string m_action;

		// Token: 0x04005317 RID: 21271
		private bool m_removeFrost;

		// Token: 0x04005318 RID: 21272
		private bool m_directDamage;
	}
}
