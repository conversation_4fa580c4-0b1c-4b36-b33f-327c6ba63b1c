﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E01 RID: 3585
	public class AE1208 : BasePetEffect
	{
		// Token: 0x06007DB8 RID: 32184 RVA: 0x0029D408 File Offset: 0x0029B608
		public AE1208(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1208, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DB9 RID: 32185 RVA: 0x0029D488 File Offset: 0x0029B688
		public override bool Start(Living living)
		{
			AE1208 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1208) as AE1208;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DBA RID: 32186 RVA: 0x000307DF File Offset: 0x0002E9DF
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DBB RID: 32187 RVA: 0x000307F5 File Offset: 0x0002E9F5
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DBC RID: 32188 RVA: 0x0029D4E8 File Offset: 0x0029B6E8
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.RemovePetMP(10);
					player2.AddPetEffect(new CE1208(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004C3F RID: 19519
		private int m_type = 0;

		// Token: 0x04004C40 RID: 19520
		private int m_count = 0;

		// Token: 0x04004C41 RID: 19521
		private int m_probability = 0;

		// Token: 0x04004C42 RID: 19522
		private int m_delay = 0;

		// Token: 0x04004C43 RID: 19523
		private int m_coldDown = 0;

		// Token: 0x04004C44 RID: 19524
		private int m_currentId;

		// Token: 0x04004C45 RID: 19525
		private int m_added = 0;
	}
}
