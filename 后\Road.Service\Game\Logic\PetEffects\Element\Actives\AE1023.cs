﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D96 RID: 3478
	public class AE1023 : BasePetEffect
	{
		// Token: 0x06007B89 RID: 31625 RVA: 0x002935F0 File Offset: 0x002917F0
		public AE1023(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1023, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B8A RID: 31626 RVA: 0x00293670 File Offset: 0x00291870
		public override bool Start(Living living)
		{
			AE1023 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1023) as AE1023;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B8B RID: 31627 RVA: 0x0002F281 File Offset: 0x0002D481
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.PlayerAnyShellThrow += this.Player_PlayerAnyShellThrow;
		}

		// Token: 0x06007B8C RID: 31628 RVA: 0x002936D0 File Offset: 0x002918D0
		private void Player_PlayerAnyShellThrow(Player player)
		{
			bool flag = !this.IsTrigger;
			if (!flag)
			{
				foreach (Player player2 in player.Game.GetAllTeamPlayers(player))
				{
					bool flag2 = player2.PlayerDetail != player.PlayerDetail;
					if (flag2)
					{
						player2.Game.SendPetBuff(player2, base.Info, true);
						player2.Game.SendPlayerPicture(player2, 30, true);
						player2.AddPetEffect(new CE1023(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.Info.ID.ToString()), 0);
					}
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007B8D RID: 31629 RVA: 0x0002F2AA File Offset: 0x0002D4AA
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.PlayerAnyShellThrow -= this.Player_PlayerAnyShellThrow;
		}

		// Token: 0x06007B8E RID: 31630 RVA: 0x002937BC File Offset: 0x002919BC
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x04004954 RID: 18772
		private int m_type = 0;

		// Token: 0x04004955 RID: 18773
		private int m_count = 0;

		// Token: 0x04004956 RID: 18774
		private int m_probability = 0;

		// Token: 0x04004957 RID: 18775
		private int m_delay = 0;

		// Token: 0x04004958 RID: 18776
		private int m_coldDown = 0;

		// Token: 0x04004959 RID: 18777
		private int m_currentId;

		// Token: 0x0400495A RID: 18778
		private int m_added = 0;
	}
}
