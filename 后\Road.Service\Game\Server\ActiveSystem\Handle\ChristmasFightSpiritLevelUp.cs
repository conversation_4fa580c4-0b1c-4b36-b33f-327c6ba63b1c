﻿using System;
using Bussiness;
using Game.Base.Packets;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C53 RID: 3155
	[ActiveSystemHandleAttbute(25)]
	public class ChristmasFightSpiritLevelUp : IActiveSystemCommandHadler
	{
		// Token: 0x06007029 RID: 28713 RVA: 0x0024D9C4 File Offset: 0x0024BBC4
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			UserChristmasInfo christmas = Player.Actives.Christmas;
			int num = 201144;
			int num2 = packet.ReadInt();
			bool flag = packet.ReadBoolean();
			int itemCount = Player.GetItemCount(num);
			bool flag2 = num2 > itemCount;
			bool flag3;
			if (flag2)
			{
				Player.SendMessage(LanguageMgr.GetTranslation("ActiveSystemHandler.Msg3", Array.Empty<object>()));
				flag3 = false;
			}
			else
			{
				bool flag4 = num2 > 5;
				if (flag4)
				{
					num2 = 5;
				}
				bool flag5 = false;
				int num3 = num2;
				int num4 = 0;
				int num5 = 10;
				bool flag6 = flag;
				if (flag6)
				{
					Player.RemoveMoney(GameProperties.ChristmasBuildSnowmanDoubleMoney);
					num3 = num2 * 2;
				}
				christmas.Exp += num3;
				bool flag7 = christmas.Exp >= num5;
				if (flag7)
				{
					christmas.Exp -= num5;
					flag5 = true;
					UserChristmasInfo userChristmasInfo = christmas;
					int count = userChristmasInfo.Count;
					userChristmasInfo.Count = count + 1;
					num4 = 1;
				}
				Player.RemoveTemplate(num, num2);
				gspacketIn.WriteByte(25);
				gspacketIn.WriteBoolean(flag5);
				gspacketIn.WriteInt(christmas.Count);
				gspacketIn.WriteInt(christmas.Exp);
				gspacketIn.WriteInt(num3);
				gspacketIn.WriteInt(num4);
				Player.Out.SendTCP(gspacketIn);
				flag3 = true;
			}
			return flag3;
		}
	}
}
