﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.NormalSpell
{
	// Token: 0x02000CB7 RID: 3255
	[SpellAttibute(5)]
	public class CarrySpell : ISpellHandler
	{
		// Token: 0x060074F0 RID: 29936 RVA: 0x0026DFD4 File Offset: 0x0026C1D4
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool flag = !player.PlayerDetail.PlayerCharacter.IsAutoBot;
			if (flag)
			{
				bool isLiving = player.IsLiving;
				if (isLiving)
				{
					player.SetBall(3);
				}
				else
				{
					bool flag2 = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
					if (flag2)
					{
						(game.CurrentLiving as Player).SetBall(3);
					}
				}
			}
		}
	}
}
