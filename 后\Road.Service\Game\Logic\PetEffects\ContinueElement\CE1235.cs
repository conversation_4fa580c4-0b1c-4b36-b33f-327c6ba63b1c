﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EA8 RID: 3752
	public class CE1235 : BasePetEffect
	{
		// Token: 0x06008196 RID: 33174 RVA: 0x002AD750 File Offset: 0x002AB950
		public CE1235(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1235, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008197 RID: 33175 RVA: 0x002AD7D0 File Offset: 0x002AB9D0
		public override bool Start(Living living)
		{
			CE1235 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1235) as CE1235;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008198 RID: 33176 RVA: 0x00032D40 File Offset: 0x00030F40
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008199 RID: 33177 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600819A RID: 33178 RVA: 0x002AD830 File Offset: 0x002ABA30
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600819B RID: 33179 RVA: 0x00032D69 File Offset: 0x00030F69
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x040050D5 RID: 20693
		private int m_type = 0;

		// Token: 0x040050D6 RID: 20694
		private int m_count = 0;

		// Token: 0x040050D7 RID: 20695
		private int m_probability = 0;

		// Token: 0x040050D8 RID: 20696
		private int m_delay = 0;

		// Token: 0x040050D9 RID: 20697
		private int m_coldDown = 0;

		// Token: 0x040050DA RID: 20698
		private int m_currentId;

		// Token: 0x040050DB RID: 20699
		private int m_added = 0;
	}
}
