﻿using System;
using Game.Server.GameObjects;
using Game.Server.Quests;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C71 RID: 3185
	public class QuestDailyFinishCondition : BaseCondition
	{
		// Token: 0x060070BE RID: 28862 RVA: 0x0002A421 File Offset: 0x00028621
		public QuestDailyFinishCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x060070BF RID: 28863 RVA: 0x0002A728 File Offset: 0x00028928
		public override void AddTrigger(GamePlayer player)
		{
			player.PlayerQuestFinish += this.player_PlayerQuestFinish;
		}

		// Token: 0x060070C0 RID: 28864 RVA: 0x00250554 File Offset: 0x0024E754
		private void player_PlayerQuestFinish(BaseQuest baseQuest)
		{
			bool flag = baseQuest.Info.QuestID == 2;
			if (flag)
			{
				int value = base.Value;
				base.Value = value + 1;
			}
		}

		// Token: 0x060070C1 RID: 28865 RVA: 0x0002A73E File Offset: 0x0002893E
		public override void RemoveTrigger(GamePlayer player)
		{
			player.PlayerQuestFinish -= this.player_PlayerQuestFinish;
		}

		// Token: 0x060070C2 RID: 28866 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
