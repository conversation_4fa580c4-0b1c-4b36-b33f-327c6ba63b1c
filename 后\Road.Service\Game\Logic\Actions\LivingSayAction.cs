﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F5F RID: 3935
	public class LivingSayAction : BaseAction
	{
		// Token: 0x06008518 RID: 34072 RVA: 0x000354FB File Offset: 0x000336FB
		public LivingSayAction(Living living, string msg, int type, int delay, int finishTime)
			: base(delay, finishTime)
		{
			this.m_living = living;
			this.m_msg = msg;
			this.m_type = type;
		}

		// Token: 0x06008519 RID: 34073 RVA: 0x0003551E File Offset: 0x0003371E
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			game.SendLivingSay(this.m_living, this.m_msg, this.m_type);
			base.Finish(tick);
		}

		// Token: 0x0400531D RID: 21277
		private Living m_living;

		// Token: 0x0400531E RID: 21278
		private string m_msg;

		// Token: 0x0400531F RID: 21279
		private int m_type;
	}
}
