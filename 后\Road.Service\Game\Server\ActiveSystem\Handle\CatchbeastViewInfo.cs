﻿using System;
using Bussiness;
using Game.Base.Packets;
using Game.Server.GameObjects;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C50 RID: 3152
	[ActiveSystemHandleAttbute(33)]
	public class CatchbeastViewInfo : IActiveSystemCommandHadler
	{
		// Token: 0x06007023 RID: 28707 RVA: 0x0024D6C0 File Offset: 0x0024B8C0
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			bool flag = Player.Actives.YearMonterValidate();
			if (flag)
			{
				GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
				gspacketIn.WriteByte(33);
				gspacketIn.WriteInt(Player.Actives.Info.ChallengeNum);
				gspacketIn.WriteInt(Player.Actives.Info.BuyBuffNum);
				gspacketIn.WriteInt(GameProperties.YearMonsterBuffMoney);
				gspacketIn.WriteInt(Player.Actives.Info.DamageNum);
				string[] array = GameProperties.YearMonsterBoxInfo.Split(new char[] { '|' });
				string[] array2 = Player.Actives.Info.BoxState.Split(new char[] { '-' });
				gspacketIn.WriteInt(array.Length);
				for (int i = 0; i < array.Length; i++)
				{
					string[] array3 = array[i].Split(new char[] { ',' });
					gspacketIn.WriteInt(int.Parse(array3[0]));
					gspacketIn.WriteInt(int.Parse(array3[1]) * 10000);
					gspacketIn.WriteInt(int.Parse(array2[i]));
				}
				Player.Out.SendTCP(gspacketIn);
			}
			return true;
		}
	}
}
