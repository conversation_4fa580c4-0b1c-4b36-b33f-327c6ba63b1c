﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F23 RID: 3875
	public class FiveGodSoldier2Effect : BaseCardEffect
	{
		// Token: 0x06008408 RID: 33800 RVA: 0x002B66B8 File Offset: 0x002B48B8
		public FiveGodSoldier2Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.FiveGodSoldier2, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008409 RID: 33801 RVA: 0x002B6730 File Offset: 0x002B4930
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.FiveGodSoldier2) is FiveGodSoldier2Effect;
			return flag || base.Start(living);
		}

		// Token: 0x0600840A RID: 33802 RVA: 0x000349C6 File Offset: 0x00032BC6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerAfterReset += this.ChangeProperty1;
		}

		// Token: 0x0600840B RID: 33803 RVA: 0x000349EF File Offset: 0x00032BEF
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.PlayerAfterReset -= this.ChangeProperty1;
		}

		// Token: 0x0600840C RID: 33804 RVA: 0x002B6768 File Offset: 0x002B4968
		private void Player_AfterKillingLiving(Living player, Living target, int damageAmount, int criticalAmount)
		{
			this.m_added = (double)(this.m_value * (damageAmount + criticalAmount) / 100);
			bool flag = this.m_added > 0.0;
			if (flag)
			{
				player.SyncAtTime = true;
				player.AddBlood((int)this.m_added);
				player.SyncAtTime = false;
			}
		}

		// Token: 0x0600840D RID: 33805 RVA: 0x00034A18 File Offset: 0x00032C18
		private void ChangeProperty1(Player player)
		{
			player.Game.SendMessage(player.PlayerDetail, "您激活了五神兵2件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活五神兵2件套卡.", 3);
		}

		// Token: 0x0600840E RID: 33806 RVA: 0x002B67C0 File Offset: 0x002B49C0
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0.0;
			if (flag)
			{
				player.AddMaxBlood(-this.m_value);
				this.m_added = 0.0;
			}
			bool flag2 = player.Game is PVPGame && (player.Game as PVPGame).IsMatchOrFreedom();
			if (flag2)
			{
				player.AddMaxBlood(this.m_value);
				this.m_added = (double)this.m_value;
			}
		}

		// Token: 0x0400525F RID: 21087
		private int m_indexValue = 0;

		// Token: 0x04005260 RID: 21088
		private int m_value = 0;

		// Token: 0x04005261 RID: 21089
		private double m_added = 0.0;
	}
}
