﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C69 RID: 3177
	public class GiftTokenCollectionCondition : BaseCondition
	{
		// Token: 0x0600709A RID: 28826 RVA: 0x0002A421 File Offset: 0x00028621
		public GiftTokenCollectionCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x0600709B RID: 28827 RVA: 0x0002A5CA File Offset: 0x000287CA
		public override void AddTrigger(GamePlayer player)
		{
			player.PlayerAddItem += this.player_PlayerAddItem;
		}

		// Token: 0x0600709C RID: 28828 RVA: 0x00250374 File Offset: 0x0024E574
		private void player_PlayerAddItem(string type, int value)
		{
			bool flag = type == "GiftToken";
			if (flag)
			{
				base.Value += value;
			}
		}

		// Token: 0x0600709D RID: 28829 RVA: 0x0002A5E0 File Offset: 0x000287E0
		public override void RemoveTrigger(GamePlayer player)
		{
			player.PlayerAddItem -= this.player_PlayerAddItem;
		}

		// Token: 0x0600709E RID: 28830 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
