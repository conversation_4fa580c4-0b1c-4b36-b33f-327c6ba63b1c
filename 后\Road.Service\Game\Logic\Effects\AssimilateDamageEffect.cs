﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EDE RID: 3806
	public class AssimilateDamageEffect : BasePlayerEffect
	{
		// Token: 0x060082E2 RID: 33506 RVA: 0x00033C79 File Offset: 0x00031E79
		public AssimilateDamageEffect(int count, int probability)
			: base(eEffectType.AssimilateDamageEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
			this.m_turn = 5;
		}

		// Token: 0x060082E3 RID: 33507 RVA: 0x002B20A4 File Offset: 0x002B02A4
		public override bool Start(Living living)
		{
			AssimilateDamageEffect assimilateDamageEffect = living.EffectList.GetOfType(eEffectType.AssimilateDamageEffect) as AssimilateDamageEffect;
			bool flag = assimilateDamageEffect != null;
			bool flag2;
			if (flag)
			{
				assimilateDamageEffect.m_probability = ((this.m_probability > assimilateDamageEffect.m_probability) ? this.m_probability : assimilateDamageEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082E4 RID: 33508 RVA: 0x00033CAF File Offset: 0x00031EAF
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x060082E5 RID: 33509 RVA: 0x00033CD8 File Offset: 0x00031ED8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x060082E6 RID: 33510 RVA: 0x00033D01 File Offset: 0x00031F01
		protected void player_BeginSelfTurn(Living living)
		{
			this.m_turn++;
		}

		// Token: 0x060082E7 RID: 33511 RVA: 0x002B2100 File Offset: 0x002B0300
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000 && this.m_turn >= 5;
			if (flag)
			{
				damageAmount = ((damageAmount > this.m_count) ? this.m_count : damageAmount);
				this.m_turn = 0;
				this.IsTrigger = true;
				living.DefenceEffectTrigger = true;
				living.SyncAtTime = true;
				living.AddBlood(damageAmount);
				living.SyncAtTime = false;
				damageAmount -= damageAmount;
				criticalAmount -= criticalAmount;
				living.Game.AddAction(new LivingSayAction(living, LanguageMgr.GetTranslation("AssimilateDamageEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051E6 RID: 20966
		private int m_count = 0;

		// Token: 0x040051E7 RID: 20967
		private int m_probability = 0;

		// Token: 0x040051E8 RID: 20968
		private int m_turn = 5;
	}
}
