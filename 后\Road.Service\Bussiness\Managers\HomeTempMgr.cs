﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FD7 RID: 4055
	public class HomeTempMgr
	{
		// Token: 0x06008AD7 RID: 35543 RVA: 0x002F8D1C File Offset: 0x002F6F1C
		public static bool Init()
		{
			return HomeTempMgr.ReLoad();
		}

		// Token: 0x06008AD8 RID: 35544 RVA: 0x002F8D34 File Offset: 0x002F6F34
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, HomeTempPracticeInfo> dictionary = HomeTempMgr.LoadPracticeFromDb();
				Dictionary<int, HomeTempAdvanceInfo> dictionary2 = HomeTempMgr.LoadAdvanceFromDb();
				bool flag = dictionary.Values.Count > 0 && dictionary2.Values.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, HomeTempPracticeInfo>>(ref HomeTempMgr.m_homeTempPractices, dictionary);
					Interlocked.Exchange<Dictionary<int, HomeTempAdvanceInfo>>(ref HomeTempMgr.m_homeTempAdvance, dictionary2);
					return true;
				}
			}
			catch (Exception ex)
			{
				HomeTempMgr.log.Error("HomeTempMgr init error:", ex);
			}
			return false;
		}

		// Token: 0x06008AD9 RID: 35545 RVA: 0x002F8DC0 File Offset: 0x002F6FC0
		private static Dictionary<int, HomeTempPracticeInfo> LoadPracticeFromDb()
		{
			Dictionary<int, HomeTempPracticeInfo> dictionary = new Dictionary<int, HomeTempPracticeInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				HomeTempPracticeInfo[] allHomeTempPractice = produceBussiness.GetAllHomeTempPractice();
				HomeTempPracticeInfo[] array = allHomeTempPractice;
				HomeTempPracticeInfo[] array2 = array;
				HomeTempPracticeInfo[] array3 = array2;
				foreach (HomeTempPracticeInfo homeTempPracticeInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(homeTempPracticeInfo.Level);
					if (flag)
					{
						dictionary.Add(homeTempPracticeInfo.Level, homeTempPracticeInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008ADA RID: 35546 RVA: 0x002F8E5C File Offset: 0x002F705C
		private static Dictionary<int, HomeTempAdvanceInfo> LoadAdvanceFromDb()
		{
			Dictionary<int, HomeTempAdvanceInfo> dictionary = new Dictionary<int, HomeTempAdvanceInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				HomeTempAdvanceInfo[] allHomeTempAdvance = produceBussiness.GetAllHomeTempAdvance();
				HomeTempAdvanceInfo[] array = allHomeTempAdvance;
				HomeTempAdvanceInfo[] array2 = array;
				HomeTempAdvanceInfo[] array3 = array2;
				foreach (HomeTempAdvanceInfo homeTempAdvanceInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(homeTempAdvanceInfo.Level);
					if (flag)
					{
						dictionary.Add(homeTempAdvanceInfo.Level, homeTempAdvanceInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008ADB RID: 35547 RVA: 0x002F8EF8 File Offset: 0x002F70F8
		public static int PracticesMaxLevel()
		{
			return HomeTempMgr.m_homeTempPractices.Count - 1;
		}

		// Token: 0x06008ADC RID: 35548 RVA: 0x002F8F18 File Offset: 0x002F7118
		public static int AdvanceMaxLevel()
		{
			return HomeTempMgr.m_homeTempAdvance.Count - 1;
		}

		// Token: 0x06008ADD RID: 35549 RVA: 0x002F8F38 File Offset: 0x002F7138
		public static HomeTempPracticeInfo FindHomeTempPractice(int id)
		{
			bool flag = HomeTempMgr.m_homeTempPractices.ContainsKey(id);
			HomeTempPracticeInfo homeTempPracticeInfo;
			if (flag)
			{
				homeTempPracticeInfo = HomeTempMgr.m_homeTempPractices[id];
			}
			else
			{
				homeTempPracticeInfo = null;
			}
			return homeTempPracticeInfo;
		}

		// Token: 0x06008ADE RID: 35550 RVA: 0x002F8F6C File Offset: 0x002F716C
		public static HomeTempAdvanceInfo FindHomeTempAdvance(int id)
		{
			bool flag = HomeTempMgr.m_homeTempAdvance.ContainsKey(id);
			HomeTempAdvanceInfo homeTempAdvanceInfo;
			if (flag)
			{
				homeTempAdvanceInfo = HomeTempMgr.m_homeTempAdvance[id];
			}
			else
			{
				homeTempAdvanceInfo = null;
			}
			return homeTempAdvanceInfo;
		}

		// Token: 0x06008ADF RID: 35551 RVA: 0x002F8FA0 File Offset: 0x002F71A0
		public static void BuildPracticeProp(int level, ref int att, ref int def, ref int agi, ref int luck, ref int blood)
		{
			HomeTempPracticeInfo homeTempPracticeInfo = HomeTempMgr.FindHomeTempPractice(level);
			bool flag = homeTempPracticeInfo != null;
			if (flag)
			{
				att += homeTempPracticeInfo.Attack;
				def += homeTempPracticeInfo.Defence;
				agi += homeTempPracticeInfo.Agility;
				luck += homeTempPracticeInfo.Luck;
				blood += homeTempPracticeInfo.Blood;
			}
		}

		// Token: 0x06008AE0 RID: 35552 RVA: 0x002F8FFC File Offset: 0x002F71FC
		public static void BuildAdvanceProp(int level, ref int att, ref int def, ref int agi, ref int luck, ref int blood)
		{
			HomeTempAdvanceInfo homeTempAdvanceInfo = HomeTempMgr.FindHomeTempAdvance(level);
			bool flag = homeTempAdvanceInfo != null;
			if (flag)
			{
				att += homeTempAdvanceInfo.Attack;
				def += homeTempAdvanceInfo.Defence;
				agi += homeTempAdvanceInfo.Agility;
				luck += homeTempAdvanceInfo.Luck;
				blood += homeTempAdvanceInfo.Blood;
			}
		}

		// Token: 0x06008AE1 RID: 35553 RVA: 0x002F9058 File Offset: 0x002F7258
		public static void BuildPracticeProp(int level, ref double damage)
		{
			HomeTempPracticeInfo homeTempPracticeInfo = HomeTempMgr.FindHomeTempPractice(level);
			bool flag = homeTempPracticeInfo != null;
			if (flag)
			{
				damage += (double)homeTempPracticeInfo.Damage;
			}
		}

		// Token: 0x06008AE2 RID: 35554 RVA: 0x002F9084 File Offset: 0x002F7284
		public static void BuildAdvanceProp(int level, ref double guard)
		{
			HomeTempAdvanceInfo homeTempAdvanceInfo = HomeTempMgr.FindHomeTempAdvance(level);
			bool flag = homeTempAdvanceInfo != null;
			if (flag)
			{
				guard += (double)homeTempAdvanceInfo.Guard;
			}
		}

		// Token: 0x04005506 RID: 21766
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005507 RID: 21767
		private static Dictionary<int, HomeTempPracticeInfo> m_homeTempPractices = new Dictionary<int, HomeTempPracticeInfo>();

		// Token: 0x04005508 RID: 21768
		private static Dictionary<int, HomeTempAdvanceInfo> m_homeTempAdvance = new Dictionary<int, HomeTempAdvanceInfo>();
	}
}
