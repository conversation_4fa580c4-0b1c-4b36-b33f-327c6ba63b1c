﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DFC RID: 3580
	public class AE1203 : BasePetEffect
	{
		// Token: 0x06007D9E RID: 32158 RVA: 0x0029CBE4 File Offset: 0x0029ADE4
		public AE1203(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1203, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D9F RID: 32159 RVA: 0x0029CC64 File Offset: 0x0029AE64
		public override bool Start(Living living)
		{
			AE1203 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1203) as AE1203;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DA0 RID: 32160 RVA: 0x000306DD File Offset: 0x0002E8DD
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007DA1 RID: 32161 RVA: 0x0029CCC4 File Offset: 0x0029AEC4
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007DA2 RID: 32162 RVA: 0x0029CCF4 File Offset: 0x0029AEF4
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				this.IsTrigger = false;
				target.AddPetEffect(new CE1203(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007DA3 RID: 32163 RVA: 0x00030706 File Offset: 0x0002E906
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004C1C RID: 19484
		private int m_type = 0;

		// Token: 0x04004C1D RID: 19485
		private int m_count = 0;

		// Token: 0x04004C1E RID: 19486
		private int m_probability = 0;

		// Token: 0x04004C1F RID: 19487
		private int m_delay = 0;

		// Token: 0x04004C20 RID: 19488
		private int m_coldDown = 0;

		// Token: 0x04004C21 RID: 19489
		private int m_currentId;

		// Token: 0x04004C22 RID: 19490
		private int m_added = 0;
	}
}
