﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000ED3 RID: 3795
	public class AddDamageEffect : BasePlayerEffect
	{
		// Token: 0x060082A1 RID: 33441 RVA: 0x000337AF File Offset: 0x000319AF
		public AddDamageEffect(int count, int probability)
			: base(eEffectType.AddDamageEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x060082A2 RID: 33442 RVA: 0x002B1438 File Offset: 0x002AF638
		public override bool Start(Living living)
		{
			AddDamageEffect addDamageEffect = living.EffectList.GetOfType(eEffectType.AddDamageEffect) as AddDamageEffect;
			bool flag = addDamageEffect != null;
			bool flag2;
			if (flag)
			{
				this.m_probability = ((this.m_probability > addDamageEffect.m_probability) ? this.m_probability : addDamageEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082A3 RID: 33443 RVA: 0x000337D6 File Offset: 0x000319D6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.TakePlayerDamage += this.player_BeforeTakeDamage;
			player.BeforePlayerShoot += this.playerShot;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x060082A4 RID: 33444 RVA: 0x000335DB File Offset: 0x000317DB
		private void player_AfterPlayerShooted(Player player)
		{
			player.FlyingPartical = 0;
		}

		// Token: 0x060082A5 RID: 33445 RVA: 0x00033812 File Offset: 0x00031A12
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.TakePlayerDamage -= this.player_BeforeTakeDamage;
			player.BeforePlayerShoot -= this.playerShot;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x060082A6 RID: 33446 RVA: 0x002B1494 File Offset: 0x002AF694
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				damageAmount += this.m_count;
			}
		}

		// Token: 0x060082A7 RID: 33447 RVA: 0x002B14BC File Offset: 0x002AF6BC
		private void playerShot(Player player, int ball)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				player.FlyingPartical = 65;
				this.IsTrigger = true;
				player.AttackEffectTrigger = true;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("AddDamageEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051CB RID: 20939
		private int m_count = 0;

		// Token: 0x040051CC RID: 20940
		private int m_probability = 0;
	}
}
