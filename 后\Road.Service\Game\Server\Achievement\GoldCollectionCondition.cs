﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C6A RID: 3178
	public class GoldCollectionCondition : BaseCondition
	{
		// Token: 0x0600709F RID: 28831 RVA: 0x0002A421 File Offset: 0x00028621
		public GoldCollectionCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x060070A0 RID: 28832 RVA: 0x0002A5F6 File Offset: 0x000287F6
		public override void AddTrigger(GamePlayer player)
		{
			player.PlayerAddItem += this.player_PlayerAddItem;
		}

		// Token: 0x060070A1 RID: 28833 RVA: 0x002503A4 File Offset: 0x0024E5A4
		private void player_PlayerAddItem(string type, int value)
		{
			bool flag = type == "Gold";
			if (flag)
			{
				base.Value += value;
			}
		}

		// Token: 0x060070A2 RID: 28834 RVA: 0x0002A60C File Offset: 0x0002880C
		public override void RemoveTrigger(GamePlayer player)
		{
			player.PlayerAddItem -= this.player_PlayerAddItem;
		}

		// Token: 0x060070A3 RID: 28835 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
