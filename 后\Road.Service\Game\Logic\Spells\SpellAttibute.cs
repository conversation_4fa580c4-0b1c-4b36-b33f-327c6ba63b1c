﻿using System;

namespace Game.Logic.Spells
{
	// Token: 0x02000CB2 RID: 3250
	public class SpellAttibute : Attribute
	{
		// Token: 0x17001406 RID: 5126
		// (get) Token: 0x060074E0 RID: 29920 RVA: 0x0002B548 File Offset: 0x00029748
		// (set) Token: 0x060074E1 RID: 29921 RVA: 0x0002B550 File Offset: 0x00029750
		public int Type { get; private set; }

		// Token: 0x060074E2 RID: 29922 RVA: 0x0002B559 File Offset: 0x00029759
		public SpellAttibute(int type)
		{
			this.Type = type;
		}
	}
}
