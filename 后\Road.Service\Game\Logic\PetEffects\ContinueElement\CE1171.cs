﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E7D RID: 3709
	public class CE1171 : BasePetEffect
	{
		// Token: 0x06008080 RID: 32896 RVA: 0x002A94C0 File Offset: 0x002A76C0
		public CE1171(int count, int probability, int type, int skillId, int delay, string elementID, Living source)
			: base(ePetEffectType.CE1171, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
			this.m_source = source;
		}

		// Token: 0x06008081 RID: 32897 RVA: 0x002A9548 File Offset: 0x002A7748
		public override bool Start(Living living)
		{
			CE1171 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1171) as CE1171;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008082 RID: 32898 RVA: 0x000322ED File Offset: 0x000304ED
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008083 RID: 32899 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008084 RID: 32900 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginNextTurn(Living living)
		{
		}

		// Token: 0x06008085 RID: 32901 RVA: 0x002A95A8 File Offset: 0x002A77A8
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 1000;
				living.AddBlood(-this.m_added, 1);
				bool flag2 = living.Blood <= 0;
				if (flag2)
				{
					living.Die();
					bool flag3 = this.m_source != null && this.m_source is Player;
					if (flag3)
					{
						(this.m_source as Player).PlayerDetail.OnKillingLiving(this.m_source.Game, 2, living.Id, living.IsLiving, this.m_added);
					}
				}
			}
		}

		// Token: 0x06008086 RID: 32902 RVA: 0x00032329 File Offset: 0x00030529
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004FA5 RID: 20389
		private int m_type = 0;

		// Token: 0x04004FA6 RID: 20390
		private int m_count = 0;

		// Token: 0x04004FA7 RID: 20391
		private int m_probability = 0;

		// Token: 0x04004FA8 RID: 20392
		private int m_delay = 0;

		// Token: 0x04004FA9 RID: 20393
		private int m_coldDown = 0;

		// Token: 0x04004FAA RID: 20394
		private int m_currentId;

		// Token: 0x04004FAB RID: 20395
		private int m_added = 0;

		// Token: 0x04004FAC RID: 20396
		private Living m_source;
	}
}
