﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F58 RID: 3928
	public class LivingFallingAction : BaseAction
	{
		// Token: 0x06008508 RID: 34056 RVA: 0x002B976C File Offset: 0x002B796C
		public LivingFallingAction(Living living, int toX, int toY, int speed, string action, int delay, int type, LivingCallBack callback)
			: base(delay, 2000)
		{
			this.m_living = living;
			this.m_fallSpeed = speed;
			this.m_action = action;
			this.m_toX = toX;
			this.m_toY = toY;
			this.m_isSent = false;
			this.m_type = type;
			this.m_callback = callback;
		}

		// Token: 0x06008509 RID: 34057 RVA: 0x002B97C4 File Offset: 0x002B79C4
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			bool flag = !this.m_isSent;
			if (flag)
			{
				this.m_isSent = true;
				game.SendLivingFall(this.m_living, this.m_toX, this.m_toY, this.m_fallSpeed, this.m_action, this.m_type);
			}
			bool flag2 = this.m_toY > this.m_living.Y + this.m_fallSpeed;
			if (flag2)
			{
				this.m_living.SetXY(this.m_toX, this.m_living.Y + this.m_fallSpeed);
			}
			else
			{
				this.m_living.SetXY(this.m_toX, this.m_toY);
				bool flag3 = game.Map.IsOutMap(this.m_toX, this.m_toY);
				if (flag3)
				{
					this.m_living.SyncAtTime = false;
					this.m_living.Die();
					Console.WriteLine("掉落Fall" + this.m_living.IsLiving.ToString());
				}
				bool flag4 = this.m_callback != null;
				if (flag4)
				{
					this.m_living.CallFuction(this.m_callback, 0);
				}
				base.Finish(tick);
			}
		}

		// Token: 0x040052F3 RID: 21235
		private Living m_living;

		// Token: 0x040052F4 RID: 21236
		private int m_fallSpeed;

		// Token: 0x040052F5 RID: 21237
		private int m_toX;

		// Token: 0x040052F6 RID: 21238
		private int m_toY;

		// Token: 0x040052F7 RID: 21239
		private bool m_isSent;

		// Token: 0x040052F8 RID: 21240
		private string m_action;

		// Token: 0x040052F9 RID: 21241
		private int m_type;

		// Token: 0x040052FA RID: 21242
		private LivingCallBack m_callback;
	}
}
