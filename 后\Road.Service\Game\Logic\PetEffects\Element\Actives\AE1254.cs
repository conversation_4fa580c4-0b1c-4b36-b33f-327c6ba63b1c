﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E1C RID: 3612
	public class AE1254 : BasePetEffect
	{
		// Token: 0x06007E41 RID: 32321 RVA: 0x0029F9C8 File Offset: 0x0029DBC8
		public AE1254(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1254, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E42 RID: 32322 RVA: 0x0029FA48 File Offset: 0x0029DC48
		public override bool Start(Living living)
		{
			AE1254 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1254) as AE1254;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E43 RID: 32323 RVA: 0x00030CD9 File Offset: 0x0002EED9
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007E44 RID: 32324 RVA: 0x0029FAA8 File Offset: 0x0029DCA8
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007E45 RID: 32325 RVA: 0x0029FAD8 File Offset: 0x0029DCD8
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				this.IsTrigger = false;
				target.AddPetEffect(new CE1254(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007E46 RID: 32326 RVA: 0x00030D02 File Offset: 0x0002EF02
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004CFC RID: 19708
		private int m_type = 0;

		// Token: 0x04004CFD RID: 19709
		private int m_count = 0;

		// Token: 0x04004CFE RID: 19710
		private int m_probability = 0;

		// Token: 0x04004CFF RID: 19711
		private int m_delay = 0;

		// Token: 0x04004D00 RID: 19712
		private int m_coldDown = 0;

		// Token: 0x04004D01 RID: 19713
		private int m_currentId;

		// Token: 0x04004D02 RID: 19714
		private int m_added = 0;
	}
}
