﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D3F RID: 3391
	public class PetProhibitionAddBloodWithFire : BasePetEffect
	{
		// Token: 0x060079AF RID: 31151 RVA: 0x0028C04C File Offset: 0x0028A24C
		public PetProhibitionAddBloodWithFire(int count, int skillId, string elementID)
			: base(ePetEffectType.PetProhibitionAddBloodWithFire, elementID)
		{
			this.m_skillId = skillId;
			this.m_count = count;
			bool flag = elementID == null;
			if (!flag)
			{
				int length = elementID.Length;
				bool flag2 = length != 4;
				if (!flag2)
				{
					switch (elementID[3])
					{
					case '1':
					{
						bool flag3 = !(elementID == "1801");
						if (flag3)
						{
							return;
						}
						break;
					}
					case '2':
					{
						bool flag4 = !(elementID == "1802");
						if (flag4)
						{
							return;
						}
						break;
					}
					case '3':
					{
						bool flag5 = !(elementID == "1773") && !(elementID == "1803");
						if (flag5)
						{
							return;
						}
						break;
					}
					case '4':
					case '5':
					case '6':
						return;
					case '7':
					{
						bool flag6 = !(elementID == "1717");
						if (flag6)
						{
							return;
						}
						break;
					}
					case '8':
					{
						bool flag7 = !(elementID == "1718");
						if (flag7)
						{
							return;
						}
						break;
					}
					case '9':
					{
						bool flag8 = !(elementID == "1719");
						if (flag8)
						{
							return;
						}
						break;
					}
					default:
						return;
					}
					this.distanceRequired = 300;
				}
			}
		}

		// Token: 0x060079B0 RID: 31152 RVA: 0x0028C1A0 File Offset: 0x0028A3A0
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetProhibitionAddBloodWithFire) is PetProhibitionAddBloodWithFire;
			return flag || base.Start(living);
		}

		// Token: 0x060079B1 RID: 31153 RVA: 0x0002DD7B File Offset: 0x0002BF7B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079B2 RID: 31154 RVA: 0x0002DD91 File Offset: 0x0002BF91
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079B3 RID: 31155 RVA: 0x0028C1DC File Offset: 0x0028A3DC
		private void player_PlayerBuffSkillPet(Living living)
		{
			PetShowFireOfHatredEquip petShowFireOfHatredEquip = living.PetEffectList.GetOfType(ePetEffectType.PetShowFireOfHatredEquip) as PetShowFireOfHatredEquip;
			bool flag = petShowFireOfHatredEquip == null || living.PetEffects.CurrentUseSkill != this.m_skillId;
			if (!flag)
			{
				foreach (Player player in living.Game.GetAllEnemyPlayers(living))
				{
					bool flag2 = this.distanceRequired == -1 || Math.Abs(living.X - player.X) <= this.distanceRequired;
					if (flag2)
					{
						for (int i = 0; i < this.m_count; i++)
						{
							player.Game.sendShowPicSkil(player, base.Info, true);
						}
						player.BeginSelfTurn += this.player_BeginSelfTurn;
					}
				}
				petShowFireOfHatredEquip.Stop();
			}
		}

		// Token: 0x060079B4 RID: 31156 RVA: 0x0028C2F0 File Offset: 0x0028A4F0
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.ForbidAidBlood = false;
				living.NoFly(false);
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				living.BeginSelfTurn -= this.player_BeginSelfTurn;
				this.Stop();
			}
			else
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				living.ForbidAidBlood = true;
				bool flag2 = this.m_skillId == 569;
				if (flag2)
				{
					living.NoFly(true);
				}
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
			}
		}

		// Token: 0x0400474B RID: 18251
		private int m_skillId;

		// Token: 0x0400474C RID: 18252
		private int m_count;

		// Token: 0x0400474D RID: 18253
		private int distanceRequired = -1;
	}
}
