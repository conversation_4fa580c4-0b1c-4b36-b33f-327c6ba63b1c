﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D44 RID: 3396
	public class PetReduceTakeCritDamage : BasePetEffect
	{
		// Token: 0x060079C9 RID: 31177 RVA: 0x0002DEEA File Offset: 0x0002C0EA
		public PetReduceTakeCritDamage(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetReduceTakeCritDamage, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x060079CA RID: 31178 RVA: 0x0028C820 File Offset: 0x0028AA20
		public override bool Start(Living living)
		{
			PetReduceTakeCritDamage petReduceTakeCritDamage = living.PetEffectList.GetOfType(ePetEffectType.PetReduceTakeCritDamage) as PetReduceTakeCritDamage;
			bool flag = petReduceTakeCritDamage != null;
			bool flag2;
			if (flag)
			{
				petReduceTakeCritDamage.m_probability = ((this.m_probability > petReduceTakeCritDamage.m_probability) ? this.m_probability : petReduceTakeCritDamage.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079CB RID: 31179 RVA: 0x0002DF29 File Offset: 0x0002C129
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
		}

		// Token: 0x060079CC RID: 31180 RVA: 0x0002DF3F File Offset: 0x0002C13F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
		}

		// Token: 0x060079CD RID: 31181 RVA: 0x0028C880 File Offset: 0x0028AA80
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.sendShowPicSkil(player, base.ElementInfo, true);
				player.AddPetEffect(new PetReduceTakeCritDamageEquip(this.m_count, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x060079CE RID: 31182 RVA: 0x0002DF55 File Offset: 0x0002C155
		private void enviarImg(Living living)
		{
			living.Game.sendShowPicSkil(living, base.ElementInfo, true);
			living.BeginNextTurn -= this.enviarImg;
		}

		// Token: 0x0400475B RID: 18267
		private int m_probability = 0;

		// Token: 0x0400475C RID: 18268
		private int m_currentId;

		// Token: 0x0400475D RID: 18269
		private int m_count = 0;
	}
}
