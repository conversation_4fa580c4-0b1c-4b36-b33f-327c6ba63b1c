﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D26 RID: 3366
	public class PetAddCritRate : BasePetEffect
	{
		// Token: 0x06007926 RID: 31014 RVA: 0x0002D497 File Offset: 0x0002B697
		public PetAddCritRate(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetAddCritRate, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_count = count;
			this.m_currentId = skillId;
		}

		// Token: 0x06007927 RID: 31015 RVA: 0x002894DC File Offset: 0x002876DC
		public override bool Start(Living living)
		{
			PetAddCritRate petAddCritRate = living.PetEffectList.GetOfType(ePetEffectType.PetAddCritRate) as PetAddCritRate;
			bool flag = petAddCritRate != null;
			bool flag2;
			if (flag)
			{
				petAddCritRate.m_probability = ((this.m_probability > petAddCritRate.m_probability) ? this.m_probability : petAddCritRate.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007928 RID: 31016 RVA: 0x0002D4D6 File Offset: 0x0002B6D6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
		}

		// Token: 0x06007929 RID: 31017 RVA: 0x0002D4EC File Offset: 0x0002B6EC
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
		}

		// Token: 0x0600792A RID: 31018 RVA: 0x0028953C File Offset: 0x0028773C
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.PetEffectTrigger = true;
				player.Game.sendShowPicSkil(player, base.Info, true);
				player.AddPetEffect(new PetAddCritRateEquip(this.m_count, base.Info.ID.ToString()), 0);
			}
		}

		// Token: 0x040046FC RID: 18172
		private int m_probability = 0;

		// Token: 0x040046FD RID: 18173
		private int m_currentId;

		// Token: 0x040046FE RID: 18174
		private int m_count = 0;
	}
}
