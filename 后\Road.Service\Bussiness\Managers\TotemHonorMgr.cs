﻿using System;
using System.Collections.Generic;
using System.Reflection;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FEE RID: 4078
	public class TotemHonorMgr
	{
		// Token: 0x06008B7A RID: 35706 RVA: 0x002FCC5C File Offset: 0x002FAE5C
		public static bool Init()
		{
			bool flag;
			try
			{
				TotemHonorMgr.m__totemHonorTemplate = new Dictionary<int, TotemHonorTemplateInfo>();
				flag = TotemHonorMgr.Load(TotemHonorMgr.m__totemHonorTemplate);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = TotemHonorMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					TotemHonorMgr.log.Error("TotemHonorMgr", ex);
				}
				flag = false;
			}
			return flag;
		}

		// Token: 0x06008B7B RID: 35707 RVA: 0x002FCCBC File Offset: 0x002FAEBC
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, TotemHonorTemplateInfo> dictionary = new Dictionary<int, TotemHonorTemplateInfo>();
				bool flag = TotemHonorMgr.Load(dictionary);
				if (flag)
				{
					try
					{
						TotemHonorMgr.m__totemHonorTemplate = dictionary;
						return true;
					}
					catch
					{
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = TotemHonorMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					TotemHonorMgr.log.Error("TotemHonorMgr", ex);
				}
			}
			return false;
		}

		// Token: 0x06008B7C RID: 35708 RVA: 0x002FCD3C File Offset: 0x002FAF3C
		private static bool Load(Dictionary<int, TotemHonorTemplateInfo> TotemHonorTemplate)
		{
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				TotemHonorTemplateInfo[] allTotemHonorTemplate = produceBussiness.GetAllTotemHonorTemplate();
				TotemHonorTemplateInfo[] array = allTotemHonorTemplate;
				TotemHonorTemplateInfo[] array2 = array;
				TotemHonorTemplateInfo[] array3 = array2;
				foreach (TotemHonorTemplateInfo totemHonorTemplateInfo in array3)
				{
					bool flag = !TotemHonorTemplate.ContainsKey(totemHonorTemplateInfo.ID);
					if (flag)
					{
						TotemHonorTemplate.Add(totemHonorTemplateInfo.ID, totemHonorTemplateInfo);
					}
				}
			}
			return true;
		}

		// Token: 0x06008B7D RID: 35709 RVA: 0x002FCDD0 File Offset: 0x002FAFD0
		public static TotemHonorTemplateInfo FindTotemHonorTemplateInfo(int ID)
		{
			bool flag = TotemHonorMgr.m__totemHonorTemplate.ContainsKey(ID);
			TotemHonorTemplateInfo totemHonorTemplateInfo;
			if (flag)
			{
				totemHonorTemplateInfo = TotemHonorMgr.m__totemHonorTemplate[ID];
			}
			else
			{
				totemHonorTemplateInfo = null;
			}
			return totemHonorTemplateInfo;
		}

		// Token: 0x04005554 RID: 21844
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005555 RID: 21845
		private static Dictionary<int, TotemHonorTemplateInfo> m__totemHonorTemplate;
	}
}
