﻿using System;
using System.Reflection;
using Game.Base.Packets;
using Game.Logic.Cmd;
using Game.Logic.Phy.Object;
using log4net;

namespace Game.Logic.Actions
{
	// Token: 0x02000F67 RID: 3943
	public class ProcessPacketAction : IAction
	{
		// Token: 0x06008528 RID: 34088 RVA: 0x00035664 File Offset: 0x00033864
		public ProcessPacketAction(Player player, GSPacketIn pkg)
		{
			this.m_player = player;
			this.m_packet = pkg;
		}

		// Token: 0x06008529 RID: 34089 RVA: 0x002BA574 File Offset: 0x002B8774
		public void Execute(BaseGame game, long tick)
		{
			bool flag = !this.m_player.IsActive;
			if (!flag)
			{
				eTankCmdType eTankCmdType = (eTankCmdType)this.m_packet.ReadByte();
				Console.WriteLine("Game.Logic.Action.ProcessPacketAction type ={0}", eTankCmdType);
				try
				{
					ICommandHandler commandHandler = CommandMgr.LoadCommandHandler((int)eTankCmdType);
					bool flag2 = commandHandler != null;
					if (flag2)
					{
						commandHandler.HandleCommand(game, this.m_player, this.m_packet);
					}
					else
					{
						ProcessPacketAction.log.Error(string.Format("Player Id: {0}", this.m_player.Id));
					}
				}
				catch (Exception ex)
				{
					ProcessPacketAction.log.Error(string.Format("Player Id: {0}  cmd:0x{1:X2}", this.m_player.Id, (byte)eTankCmdType), ex);
				}
			}
		}

		// Token: 0x0600852A RID: 34090 RVA: 0x00069EBC File Offset: 0x000680BC
		public bool IsFinished(BaseGame game, long tick)
		{
			return true;
		}

		// Token: 0x04005337 RID: 21303
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005338 RID: 21304
		private Player m_player;

		// Token: 0x04005339 RID: 21305
		private GSPacketIn m_packet;
	}
}
