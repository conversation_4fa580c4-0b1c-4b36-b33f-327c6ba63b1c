﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects
{
	// Token: 0x02000D51 RID: 3409
	public class BasePetEffect : AbstractPetEffect
	{
		// Token: 0x06007A15 RID: 31253 RVA: 0x0002E340 File Offset: 0x0002C540
		public BasePetEffect(ePetEffectType type, string elementId)
			: base(type, elementId)
		{
		}

		// Token: 0x06007A16 RID: 31254 RVA: 0x0028D76C File Offset: 0x0028B96C
		public override bool Start(Living living)
		{
			bool flag = living is Player;
			return flag && base.Start(living);
		}

		// Token: 0x06007A17 RID: 31255 RVA: 0x0028D798 File Offset: 0x0028B998
		public override void OnAttached(Living living)
		{
			bool flag = living is Player;
			if (flag)
			{
				this.OnAttachedToPlayer(living as Player);
			}
		}

		// Token: 0x06007A18 RID: 31256 RVA: 0x0028D7C4 File Offset: 0x0028B9C4
		public override void OnRemoved(Living living)
		{
			bool flag = living is Player;
			if (flag)
			{
				this.OnRemovedFromPlayer(living as Player);
			}
		}

		// Token: 0x06007A19 RID: 31257 RVA: 0x0028D7F0 File Offset: 0x0028B9F0
		public sealed override void OnPaused(Living living)
		{
			bool flag = living is Player;
			if (flag)
			{
				this.OnPausedOnPlayer(living as Player);
			}
		}

		// Token: 0x06007A1A RID: 31258 RVA: 0x00005683 File Offset: 0x00003883
		protected virtual void OnAttachedToPlayer(Player player)
		{
		}

		// Token: 0x06007A1B RID: 31259 RVA: 0x00005683 File Offset: 0x00003883
		protected virtual void OnRemovedFromPlayer(Player player)
		{
		}

		// Token: 0x06007A1C RID: 31260 RVA: 0x00005683 File Offset: 0x00003883
		protected virtual void OnPausedOnPlayer(Player player)
		{
		}
	}
}
