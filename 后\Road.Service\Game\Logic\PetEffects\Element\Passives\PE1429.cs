﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D87 RID: 3463
	public class PE1429 : BasePetEffect
	{
		// Token: 0x06007B38 RID: 31544 RVA: 0x00292374 File Offset: 0x00290574
		public PE1429(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1429, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B39 RID: 31545 RVA: 0x002923F4 File Offset: 0x002905F4
		public override bool Start(Living living)
		{
			PE1429 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1429) as PE1429;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B3A RID: 31546 RVA: 0x0002EE24 File Offset: 0x0002D024
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
		}

		// Token: 0x06007B3B RID: 31547 RVA: 0x0002EE3A File Offset: 0x0002D03A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
		}

		// Token: 0x06007B3C RID: 31548 RVA: 0x00292454 File Offset: 0x00290654
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			int num = damageAmount * 20 / 100;
			damageAmount -= num;
			bool activeKimCang = (living as Player).ActiveKimCang;
			if (activeKimCang)
			{
				(living as Player).ActiveKimCang = false;
				living.Game.SendPetBuff(living, base.ElementInfo, true);
			}
		}

		// Token: 0x040048F1 RID: 18673
		private int m_type = 0;

		// Token: 0x040048F2 RID: 18674
		private int m_count = 0;

		// Token: 0x040048F3 RID: 18675
		private int m_probability = 0;

		// Token: 0x040048F4 RID: 18676
		private int m_delay = 0;

		// Token: 0x040048F5 RID: 18677
		private int m_coldDown = 0;

		// Token: 0x040048F6 RID: 18678
		private int m_currentId;

		// Token: 0x040048F7 RID: 18679
		private int m_added = 0;
	}
}
