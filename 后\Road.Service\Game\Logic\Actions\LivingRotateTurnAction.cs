﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F5E RID: 3934
	public class LivingRotateTurnAction : BaseAction
	{
		// Token: 0x06008516 RID: 34070 RVA: 0x000354D1 File Offset: 0x000336D1
		public LivingRotateTurnAction(Player player, int rotation, int speed, string endPlay, int delay)
			: base(0, delay)
		{
			this.m_player = player;
			this.m_rotation = rotation;
			this.m_speed = speed;
			this.m_endPlay = endPlay;
		}

		// Token: 0x06008517 RID: 34071 RVA: 0x002BA258 File Offset: 0x002B8458
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			game.SendLivingTurnRotation(this.m_player, this.m_rotation, this.m_speed, this.m_endPlay);
			base.Finish(tick);
			game.LivingChangeAngle(this.m_player, this.m_rotation, this.m_speed, this.m_endPlay);
			base.Finish(tick);
		}

		// Token: 0x04005319 RID: 21273
		private string m_endPlay;

		// Token: 0x0400531A RID: 21274
		private Player m_player;

		// Token: 0x0400531B RID: 21275
		private int m_rotation;

		// Token: 0x0400531C RID: 21276
		private int m_speed;
	}
}
