﻿using System;
using Game.Logic.Actions;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D39 RID: 3385
	public class PetDefensivePosture_Damage : BasePetEffect
	{
		// Token: 0x0600798D RID: 31117 RVA: 0x0002DB3A File Offset: 0x0002BD3A
		public PetDefensivePosture_Damage(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetDefensivePosture_Damage, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x0600798E RID: 31118 RVA: 0x0028B3B4 File Offset: 0x002895B4
		public override bool Start(Living living)
		{
			PetDefensivePosture_Damage petDefensivePosture_Damage = living.PetEffectList.GetOfType(ePetEffectType.PetDefensivePosture_Damage) as PetDefensivePosture_Damage;
			bool flag = petDefensivePosture_Damage != null;
			bool flag2;
			if (flag)
			{
				petDefensivePosture_Damage.m_probability = ((this.m_probability > petDefensivePosture_Damage.m_probability) ? this.m_probability : petDefensivePosture_Damage.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600798F RID: 31119 RVA: 0x0002DB6B File Offset: 0x0002BD6B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerSkip += this.player_PlayerSkip;
		}

		// Token: 0x06007990 RID: 31120 RVA: 0x0002DB81 File Offset: 0x0002BD81
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerSkip -= this.player_PlayerSkip;
		}

		// Token: 0x06007991 RID: 31121 RVA: 0x0028B414 File Offset: 0x00289614
		private void player_PlayerSkip(Player player)
		{
			player.Game.sendShowPicSkil(player, base.ElementInfo, false);
			player.Game.sendShowPicSkil(player, base.ElementInfo, true);
			player.Game.AddAction(new LivingSayAction(player, "防御姿势", 9, 0, 1000));
			new PetDefensivePosture_DamageEquip(this.m_count, base.Info.ID.ToString()).Start(player);
		}

		// Token: 0x04004736 RID: 18230
		private int m_count;

		// Token: 0x04004737 RID: 18231
		private int m_probability;

		// Token: 0x04004738 RID: 18232
		private int m_currentId;
	}
}
