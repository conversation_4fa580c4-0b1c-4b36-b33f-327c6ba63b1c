﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E84 RID: 3716
	public class CE1178 : BasePetEffect
	{
		// Token: 0x060080AF RID: 32943 RVA: 0x002A9EA4 File Offset: 0x002A80A4
		public CE1178(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1178, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060080B0 RID: 32944 RVA: 0x002A9F24 File Offset: 0x002A8124
		public override bool Start(Living living)
		{
			CE1178 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1178) as CE1178;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080B1 RID: 32945 RVA: 0x002A9F84 File Offset: 0x002A8184
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.Attack += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060080B2 RID: 32946 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060080B3 RID: 32947 RVA: 0x002A9FE4 File Offset: 0x002A81E4
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060080B4 RID: 32948 RVA: 0x002AA018 File Offset: 0x002A8218
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Attack -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004FD9 RID: 20441
		private int m_type = 0;

		// Token: 0x04004FDA RID: 20442
		private int m_count = 0;

		// Token: 0x04004FDB RID: 20443
		private int m_probability = 0;

		// Token: 0x04004FDC RID: 20444
		private int m_delay = 0;

		// Token: 0x04004FDD RID: 20445
		private int m_coldDown = 0;

		// Token: 0x04004FDE RID: 20446
		private int m_currentId;

		// Token: 0x04004FDF RID: 20447
		private int m_added = 0;
	}
}
