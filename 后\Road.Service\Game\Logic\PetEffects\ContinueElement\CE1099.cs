﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E69 RID: 3689
	public class CE1099 : BasePetEffect
	{
		// Token: 0x06008002 RID: 32770 RVA: 0x002A7828 File Offset: 0x002A5A28
		public CE1099(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1099, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008003 RID: 32771 RVA: 0x002A78A8 File Offset: 0x002A5AA8
		public override bool Start(Living living)
		{
			CE1099 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1099) as CE1099;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008004 RID: 32772 RVA: 0x002A7908 File Offset: 0x002A5B08
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				player.Lucky += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008005 RID: 32773 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008006 RID: 32774 RVA: 0x002A796C File Offset: 0x002A5B6C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008007 RID: 32775 RVA: 0x00031D88 File Offset: 0x0002FF88
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Lucky -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F1A RID: 20250
		private int m_type = 0;

		// Token: 0x04004F1B RID: 20251
		private int m_count = 0;

		// Token: 0x04004F1C RID: 20252
		private int m_probability = 0;

		// Token: 0x04004F1D RID: 20253
		private int m_delay = 0;

		// Token: 0x04004F1E RID: 20254
		private int m_coldDown = 0;

		// Token: 0x04004F1F RID: 20255
		private int m_currentId;

		// Token: 0x04004F20 RID: 20256
		private int m_added = 0;
	}
}
