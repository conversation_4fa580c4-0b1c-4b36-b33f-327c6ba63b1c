﻿using System;
using System.Drawing;
using Game.Logic.Phy.Maths;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F4B RID: 3915
	public class GhostMoveAction : BaseAction
	{
		// Token: 0x060084ED RID: 34029 RVA: 0x002B91EC File Offset: 0x002B73EC
		public GhostMoveAction(Player player, Point target)
			: base(0, 1000)
		{
			this.m_player = player;
			this.m_target = target;
			this.m_v = new Point(target.X - this.m_player.X, target.Y - this.m_player.Y);
			this.m_v.Normalize(2);
		}

		// Token: 0x060084EE RID: 34030 RVA: 0x002B9254 File Offset: 0x002B7454
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			bool flag = !this.m_isSend;
			if (flag)
			{
				this.m_isSend = true;
				game.SendPlayerMove(this.m_player, 2, this.m_target.X, this.m_target.Y, (byte)((this.m_v.X > 0) ? 1 : (-1)), this.m_player.IsLiving);
			}
			bool flag2 = this.m_target.Distance(this.m_player.X, this.m_player.Y) > 2.0;
			if (flag2)
			{
				this.m_player.SetXY(this.m_player.X + this.m_v.X, this.m_player.Y + this.m_v.Y);
			}
			else
			{
				game.CheckBox();
				this.m_player.SetXY(this.m_target.X, this.m_target.Y);
				base.Finish(tick);
			}
		}

		// Token: 0x040052D0 RID: 21200
		private Point m_target;

		// Token: 0x040052D1 RID: 21201
		private Player m_player;

		// Token: 0x040052D2 RID: 21202
		private Point m_v;

		// Token: 0x040052D3 RID: 21203
		private bool m_isSend;
	}
}
