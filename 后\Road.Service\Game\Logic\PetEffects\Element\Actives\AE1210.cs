﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E03 RID: 3587
	public class AE1210 : BasePetEffect
	{
		// Token: 0x06007DC2 RID: 32194 RVA: 0x0029D790 File Offset: 0x0029B990
		public AE1210(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1210, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DC3 RID: 32195 RVA: 0x0029D810 File Offset: 0x0029BA10
		public override bool Start(Living living)
		{
			AE1210 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1210) as AE1210;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DC4 RID: 32196 RVA: 0x00030837 File Offset: 0x0002EA37
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DC5 RID: 32197 RVA: 0x0003084D File Offset: 0x0002EA4D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DC6 RID: 32198 RVA: 0x0029D870 File Offset: 0x0029BA70
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1210(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004C4D RID: 19533
		private int m_type = 0;

		// Token: 0x04004C4E RID: 19534
		private int m_count = 0;

		// Token: 0x04004C4F RID: 19535
		private int m_probability = 0;

		// Token: 0x04004C50 RID: 19536
		private int m_delay = 0;

		// Token: 0x04004C51 RID: 19537
		private int m_coldDown = 0;

		// Token: 0x04004C52 RID: 19538
		private int m_currentId;

		// Token: 0x04004C53 RID: 19539
		private int m_added = 0;
	}
}
