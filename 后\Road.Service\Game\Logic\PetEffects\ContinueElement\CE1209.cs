﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E9C RID: 3740
	public class CE1209 : BasePetEffect
	{
		// Token: 0x06008149 RID: 33097 RVA: 0x002AC5FC File Offset: 0x002AA7FC
		public CE1209(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1209, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600814A RID: 33098 RVA: 0x002AC67C File Offset: 0x002AA87C
		public override bool Start(Living living)
		{
			CE1209 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1209) as CE1209;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600814B RID: 33099 RVA: 0x000329CD File Offset: 0x00030BCD
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600814C RID: 33100 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600814D RID: 33101 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_BeginSelfTurn(Living living)
		{
			this.Stop();
		}

		// Token: 0x0600814E RID: 33102 RVA: 0x000329F6 File Offset: 0x00030BF6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005081 RID: 20609
		private int m_type = 0;

		// Token: 0x04005082 RID: 20610
		private int m_count = 0;

		// Token: 0x04005083 RID: 20611
		private int m_probability = 0;

		// Token: 0x04005084 RID: 20612
		private int m_delay = 0;

		// Token: 0x04005085 RID: 20613
		private int m_coldDown = 0;

		// Token: 0x04005086 RID: 20614
		private int m_currentId;

		// Token: 0x04005087 RID: 20615
		private int m_added = 0;
	}
}
