﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EAD RID: 3757
	public class CE1244 : BasePetEffect
	{
		// Token: 0x060081B5 RID: 33205 RVA: 0x002ADF3C File Offset: 0x002AC13C
		public CE1244(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1244, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081B6 RID: 33206 RVA: 0x002ADFBC File Offset: 0x002AC1BC
		public override bool Start(Living living)
		{
			CE1244 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1244) as CE1244;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081B7 RID: 33207 RVA: 0x00032DF8 File Offset: 0x00030FF8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081B8 RID: 33208 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081B9 RID: 33209 RVA: 0x002AE01C File Offset: 0x002AC21C
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			this.m_added = 2500;
			bool isLiving = living.IsLiving;
			if (isLiving)
			{
				living.SyncAtTime = true;
				living.AddBlood(this.m_added);
				living.SyncAtTime = false;
			}
		}

		// Token: 0x060081BA RID: 33210 RVA: 0x002AE060 File Offset: 0x002AC260
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.m_added = 0;
				this.Stop();
			}
		}

		// Token: 0x060081BB RID: 33211 RVA: 0x00032E34 File Offset: 0x00031034
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x040050F8 RID: 20728
		private int m_type = 0;

		// Token: 0x040050F9 RID: 20729
		private int m_count = 0;

		// Token: 0x040050FA RID: 20730
		private int m_probability = 0;

		// Token: 0x040050FB RID: 20731
		private int m_delay = 0;

		// Token: 0x040050FC RID: 20732
		private int m_coldDown = 0;

		// Token: 0x040050FD RID: 20733
		private int m_currentId;

		// Token: 0x040050FE RID: 20734
		private int m_added = 0;
	}
}
