﻿using System;
using System.Reflection;
using log4net;

namespace Game.Logic.Actions
{
	// Token: 0x02000F46 RID: 3910
	public class CheckPVEGameStateAction : IAction
	{
		// Token: 0x060084DF RID: 34015 RVA: 0x000350F5 File Offset: 0x000332F5
		public CheckPVEGameStateAction(int delay)
		{
			this.m_time = TickHelper.GetTickCount() + (long)delay;
			this.m_isFinished = false;
		}

		// Token: 0x060084E0 RID: 34016 RVA: 0x002B8DF0 File Offset: 0x002B6FF0
		public void Execute(BaseGame game, long tick)
		{
			bool flag = this.m_time > tick || game.GetWaitTimer() >= tick;
			if (!flag)
			{
				PVEGame pvegame = game as PVEGame;
				bool flag2 = pvegame != null;
				if (flag2)
				{
					Console.WriteLine("pve.GameState{0}", pvegame.GameState);
					switch (pvegame.GameState)
					{
					case eGameState.Inited:
						pvegame.Prepare();
						break;
					case eGameState.Prepared:
						pvegame.PrepareNewSession();
						break;
					case eGameState.Loading:
					{
						bool flag3 = pvegame.IsAllComplete();
						if (flag3)
						{
							pvegame.StartGame();
							bool openTryAgain = pvegame.OpenTryAgain;
							if (openTryAgain)
							{
								pvegame.OpenTryAgain = false;
							}
							else
							{
								pvegame.PreSessionId++;
							}
						}
						else
						{
							game.WaitTime(1000);
						}
						break;
					}
					case eGameState.GameStartMovie:
					{
						bool flag4 = game.CurrentActionCount <= 1;
						if (flag4)
						{
							pvegame.StartGame();
						}
						else
						{
							pvegame.StartGameMovie();
						}
						break;
					}
					case eGameState.GameStart:
					{
						bool flag5 = game.RoomType == eRoomType.FightLib;
						if (flag5)
						{
							bool flag6 = game.CurrentActionCount <= 1;
							if (flag6)
							{
								pvegame.PrepareFightingLivings();
							}
						}
						else
						{
							pvegame.PrepareNewGame();
						}
						break;
					}
					case eGameState.Playing:
					{
						bool flag7 = (pvegame.CurrentLiving == null || !pvegame.CurrentLiving.IsAttacking) && game.CurrentActionCount <= 1;
						if (flag7)
						{
							bool flag8 = pvegame.CanGameOver();
							if (flag8)
							{
								pvegame.PrepareGameOver();
							}
							else
							{
								pvegame.NextTurn();
							}
						}
						break;
					}
					case eGameState.PrepareGameOver:
					{
						bool flag9 = pvegame.CurrentActionCount <= 1;
						if (flag9)
						{
							pvegame.GameOver();
						}
						break;
					}
					case eGameState.GameOver:
					{
						bool flag10 = pvegame.HasNextSession();
						if (flag10)
						{
							pvegame.PrepareNewSession();
						}
						else
						{
							pvegame.GameOverAllSession();
						}
						break;
					}
					case eGameState.SessionPrepared:
					{
						bool flag11 = pvegame.CanStartNewSession();
						if (flag11)
						{
							pvegame.StartLoading();
						}
						else
						{
							game.WaitTime(1000);
						}
						break;
					}
					case eGameState.ALLSessionStopped:
					{
						bool flag12 = pvegame.PlayerCount == 0 || pvegame.WantTryAgain == 0;
						if (flag12)
						{
							pvegame.Stop();
						}
						else
						{
							bool flag13 = pvegame.WantTryAgain == 1;
							if (flag13)
							{
								pvegame.SessionId--;
								pvegame.ShowLargeCard();
								pvegame.PrepareNewSession();
							}
							else
							{
								bool flag14 = pvegame.WantTryAgain == 2;
								if (flag14)
								{
									bool flag15 = !pvegame.MissionTryAgain;
									if (flag15)
									{
										pvegame.SendMissionTryAgain();
										pvegame.MissionTryAgain = true;
									}
									bool flag16 = !pvegame.ResponseMissionTryAgain();
									if (flag16)
									{
										pvegame.Stop();
									}
								}
								else
								{
									game.WaitTime(1000);
								}
							}
						}
						break;
					}
					}
				}
				this.m_isFinished = true;
			}
		}

		// Token: 0x060084E1 RID: 34017 RVA: 0x002B90D8 File Offset: 0x002B72D8
		public bool IsFinished(BaseGame game, long tick)
		{
			return this.m_isFinished;
		}

		// Token: 0x040052C0 RID: 21184
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040052C1 RID: 21185
		private long m_time;

		// Token: 0x040052C2 RID: 21186
		private bool m_isFinished;
	}
}
