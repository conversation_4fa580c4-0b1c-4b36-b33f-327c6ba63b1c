﻿using System;
using Bussiness;
using Game.Base.Packets;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F17 RID: 3863
	[GameCommand(119, "关卡失败再试一次")]
	public class TryAgainCommand : ICommandHandler
	{
		// Token: 0x060083C7 RID: 33735 RVA: 0x002B589C File Offset: 0x002B3A9C
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool flag = !(game is PVEGame);
			if (!flag)
			{
				PVEGame pvegame = game as PVEGame;
				int num = packet.ReadInt();
				bool flag2 = packet.ReadBoolean();
				bool flag3 = !packet.ReadBoolean();
				if (!flag3)
				{
					bool flag4 = flag2;
					if (flag4)
					{
						bool flag5 = num == 1;
						if (flag5)
						{
							bool flag6 = player.PlayerDetail.PlayerCharacter.HasBagPassword && player.PlayerDetail.PlayerCharacter.IsLocked;
							if (flag6)
							{
								player.PlayerDetail.SendMessage(LanguageMgr.GetTranslation("Bag.Locked", Array.Empty<object>()));
								pvegame.WantTryAgain = 0;
							}
							else
							{
								bool flag7 = false;
								UserBufferInfo fightBuffByType = player.GetFightBuffByType(eBuffType.Level_Try);
								bool flag8 = fightBuffByType != null && !game.IsSpecialPVE() && player.PlayerDetail.UsePayBuff(eBuffType.Level_Try);
								bool flag9;
								if (flag8)
								{
									flag9 = true;
									flag7 = true;
								}
								else
								{
									flag9 = player.PlayerDetail.RemoveMoney(1000) > 0;
								}
								bool flag10 = flag9;
								if (flag10)
								{
									bool flag11 = pvegame.WantTryAgain == 2;
									if (flag11)
									{
										pvegame.WantTryAgain = 1;
										bool flag12 = flag7;
										if (flag12)
										{
											player.PlayerDetail.SendMessage(LanguageMgr.GetTranslation("GameServer.PayBuff.LevelTry.UseNotice", Array.Empty<object>()));
										}
										pvegame.OpenTryAgain = true;
									}
								}
								else
								{
									player.PlayerDetail.SendInsufficientMoney(2);
								}
							}
						}
						else
						{
							pvegame.WantTryAgain = 0;
						}
					}
					else
					{
						pvegame.WantTryAgain = 0;
					}
					pvegame.SendMissionTryAgain();
					pvegame.ClearWaitTimer();
					pvegame.CheckState(0);
				}
			}
		}
	}
}
