﻿using System;
using Game.Base.Packets;
using Game.Server.GameObjects;
using Game.Server.Rooms;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C56 RID: 3158
	[ActiveSystemHandleAttbute(21)]
	public class ChristmasMove : IActiveSystemCommandHadler
	{
		// Token: 0x06007030 RID: 28720 RVA: 0x0024DD00 File Offset: 0x0024BF00
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			BaseChristmasRoom christmasRoom = RoomMgr.ChristmasRoom;
			int num = packet.ReadInt();
			int num2 = packet.ReadInt();
			string text = packet.ReadString();
			Player.X = num;
			Player.Y = num2;
			gspacketIn.WriteByte(21);
			gspacketIn.WriteInt(Player.PlayerId);
			gspacketIn.WriteInt(num);
			gspacketIn.WriteInt(num2);
			gspacketIn.WriteString(text);
			christmasRoom.SendToALL(gspacketIn);
			return true;
		}
	}
}
