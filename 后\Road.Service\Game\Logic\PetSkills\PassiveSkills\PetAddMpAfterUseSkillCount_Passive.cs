﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CF4 RID: 3316
	public class PetAddMpAfterUseSkillCount_Passive : BasePetEffect
	{
		// Token: 0x0600781E RID: 30750 RVA: 0x002841DC File Offset: 0x002823DC
		public PetAddMpAfterUseSkillCount_Passive(string elementID)
			: base(ePetEffectType.PetAddMpAfterUseSkillCount_Passive, elementID)
		{
			if (!(elementID == "1765"))
			{
				if (!(elementID == "1784"))
				{
					if (elementID == "1815")
					{
						this.m_added = 20;
						this.m_value = 4;
					}
				}
				else
				{
					this.m_added = 30;
					this.m_value = 2;
				}
			}
			else
			{
				this.m_added = 20;
				this.m_value = 3;
			}
		}

		// Token: 0x0600781F RID: 30751 RVA: 0x00284258 File Offset: 0x00282458
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetAddMpAfterUseSkillCount_Passive) is PetAddMpAfterUseSkillCount_Passive;
			return flag || base.Start(living);
		}

		// Token: 0x06007820 RID: 30752 RVA: 0x0002C661 File Offset: 0x0002A861
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkill += new PlayerEventHandle(this.player_PlayerBuffSkill);
			player.PlayerBuffSkillHorse += new PlayerEventHandle(this.player_PlayerBuffSkill);
		}

		// Token: 0x06007821 RID: 30753 RVA: 0x0002C68A File Offset: 0x0002A88A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkill -= new PlayerEventHandle(this.player_PlayerBuffSkill);
			player.PlayerBuffSkillHorse -= new PlayerEventHandle(this.player_PlayerBuffSkill);
		}

		// Token: 0x06007822 RID: 30754 RVA: 0x00284294 File Offset: 0x00282494
		private void player_PlayerBuffSkill(Living living)
		{
			this.m_countTotal++;
			bool flag = this.m_countTotal >= this.m_value;
			if (flag)
			{
				this.m_countTotal = 0;
				(living as Player).AddPetMP(this.m_added);
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				living.AddPetEffect(new PetShowFireofResentmentEquip(99, "1750"), 0);
			}
		}

		// Token: 0x04004668 RID: 18024
		private int m_added;

		// Token: 0x04004669 RID: 18025
		private int m_value;

		// Token: 0x0400466A RID: 18026
		private int m_countTotal;
	}
}
