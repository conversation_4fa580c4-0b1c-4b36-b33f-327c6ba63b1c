﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EDB RID: 3803
	public class AddTurnEquipEffect : BasePlayerEffect
	{
		// Token: 0x060082CF RID: 33487 RVA: 0x002B1B4C File Offset: 0x002AFD4C
		public AddTurnEquipEffect(int count, int probability, int templateid)
			: base(eEffectType.AddTurnEquipEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
			this.m_turn = 5;
			this.m_templateid = templateid;
		}

		// Token: 0x060082D0 RID: 33488 RVA: 0x002B1BA4 File Offset: 0x002AFDA4
		public override bool Start(Living living)
		{
			AddTurnEquipEffect addTurnEquipEffect = living.EffectList.GetOfType(eEffectType.AddTurnEquipEffect) as AddTurnEquipEffect;
			bool flag = addTurnEquipEffect != null;
			bool flag2;
			if (flag)
			{
				addTurnEquipEffect.m_probability = ((this.m_probability > addTurnEquipEffect.m_probability) ? this.m_probability : addTurnEquipEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082D1 RID: 33489 RVA: 0x00033B06 File Offset: 0x00031D06
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.ChangeProperty;
			player.BeginNextTurn += this.player_BeginNewTurn;
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x060082D2 RID: 33490 RVA: 0x002B1C00 File Offset: 0x002AFE00
		protected void player_BeginSelfTurn(Living living)
		{
			this.m_turn++;
			bool flag = this.m_turn == 1;
			if (flag)
			{
				this.addDelay = true;
			}
		}

		// Token: 0x060082D3 RID: 33491 RVA: 0x00033B42 File Offset: 0x00031D42
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.ChangeProperty;
			player.BeginNextTurn -= this.player_BeginNewTurn;
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x060082D4 RID: 33492 RVA: 0x002B1C34 File Offset: 0x002AFE34
		public void player_BeginNewTurn(Living living)
		{
			bool flag = this.IsTrigger && living is Player;
			if (flag)
			{
				(living as Player).Delay = (living as Player).DefaultDelay;
				this.IsTrigger = false;
				int templateid = this.m_templateid;
				int num = templateid;
				if (num <= 311229)
				{
					if (num <= 311129)
					{
						if (num == 311112)
						{
							(living as Player).Energy = 100;
							goto IL_015D;
						}
						if (num == 311129)
						{
							(living as Player).Energy = 100;
							goto IL_015D;
						}
					}
					else
					{
						if (num == 311212)
						{
							(living as Player).Energy = 100;
							goto IL_015D;
						}
						if (num == 311229)
						{
							(living as Player).Energy = 160;
							goto IL_015D;
						}
					}
				}
				else if (num <= 311329)
				{
					if (num == 311312)
					{
						(living as Player).Energy = 210;
						goto IL_015D;
					}
					if (num == 311329)
					{
						(living as Player).Energy = 250;
						goto IL_015D;
					}
				}
				else
				{
					if (num == 311412)
					{
						(living as Player).Energy = 260;
						goto IL_015D;
					}
					if (num == 311429)
					{
						(living as Player).Energy = 265;
						goto IL_015D;
					}
				}
				(living as Player).Energy = 100;
				IL_015D:;
			}
			bool flag2 = this.addDelay;
			if (flag2)
			{
				(living as Player).Delay += ((living as Player).Delay - (living as Player).DefaultDelay) * this.m_count / 100;
				this.addDelay = false;
			}
		}

		// Token: 0x060082D5 RID: 33493 RVA: 0x002B1DE8 File Offset: 0x002AFFE8
		private void ChangeProperty(Player player, int ball)
		{
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000 && this.m_turn >= 6;
			if (flag)
			{
				this.m_turn = 0;
				this.IsTrigger = true;
				player.AttackEffectTrigger = true;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("AddTurnEquipEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051DC RID: 20956
		private int m_count = 0;

		// Token: 0x040051DD RID: 20957
		private int m_probability = 0;

		// Token: 0x040051DE RID: 20958
		private int m_turn = 5;

		// Token: 0x040051DF RID: 20959
		private bool addDelay = false;

		// Token: 0x040051E0 RID: 20960
		private int m_templateid = 0;
	}
}
