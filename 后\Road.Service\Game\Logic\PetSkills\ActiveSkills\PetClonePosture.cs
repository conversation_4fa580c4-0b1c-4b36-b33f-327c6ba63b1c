﻿using System;
using System.Collections.Generic;
using System.Drawing;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D34 RID: 3380
	public class PetClonePosture : BasePetEffect
	{
		// Token: 0x06007971 RID: 31089 RVA: 0x0028A4E8 File Offset: 0x002886E8
		public PetClonePosture(int count, int probability, int skillId, int delay, string elementID)
			: base(ePetEffectType.PetClonePosture, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
			this.m_count = count;
			this.m_delay = delay;
		}

		// Token: 0x06007972 RID: 31090 RVA: 0x0028A538 File Offset: 0x00288738
		public override bool Start(Living living)
		{
			PetClonePosture petClonePosture = living.PetEffectList.GetOfType(ePetEffectType.PetClonePosture) as PetClonePosture;
			bool flag = petClonePosture != null;
			bool flag2;
			if (flag)
			{
				petClonePosture.m_probability = ((this.m_probability > petClonePosture.m_probability) ? this.m_probability : petClonePosture.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007973 RID: 31091 RVA: 0x0002D980 File Offset: 0x0002BB80
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
		}

		// Token: 0x06007974 RID: 31092 RVA: 0x0002D996 File Offset: 0x0002BB96
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
		}

		// Token: 0x06007975 RID: 31093 RVA: 0x0028A598 File Offset: 0x00288798
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Point> list = new List<Point>();
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				int num = 0;
				foreach (Player player2 in allEnemyPlayers)
				{
					list.Add(new Point(player2.X, player2.Y));
				}
				foreach (Player player3 in allTeamPlayers)
				{
					list.Add(new Point(player3.X, player3.Y));
				}
				string[] array = (player.Game.Map.Info.PosX + "|" + player.Game.Map.Info.PosX1).Split(new char[] { '|' });
				string[] array2 = array;
				string[] array3 = array2;
				string[] array4 = array3;
				string[] array5 = array4;
				string[] array6 = array5;
				foreach (string text in array6)
				{
					list.Add(new Point(Convert.ToInt32(text.Split(new char[] { ',' })[0]), Convert.ToInt32(text.Split(new char[] { ',' })[1])));
				}
				bool flag2 = list.Count <= 0;
				if (!flag2)
				{
					bool flag3 = this.m_currentId == 501;
					if (flag3)
					{
						int num2 = 50;
						for (int j = 0; j < 3; j++)
						{
							num = this.rand.Next(list.Count);
							int num3 = list[num].X;
							int num4 = list[num].Y;
							Point point = player.Game.Map.FindYLineNotEmptyPointDown(num3, num4);
							bool flag4 = point.IsEmpty && player.Game.Map.Ground != null;
							if (flag4)
							{
								num4 = player.Game.Map.Ground.Height;
							}
							else
							{
								num3 = point.X;
								num4 = point.Y;
							}
							player.Game.AddPlayerSadowHandler(player, num3, num4, num2);
							player.ListObject.Add(num2);
							num2++;
						}
					}
					else
					{
						num = this.rand.Next(list.Count);
						int num5 = list[num].X;
						int num6 = list[num].Y;
						Point point2 = player.Game.Map.FindYLineNotEmptyPointDown(num5, num6);
						bool flag5 = point2.IsEmpty && player.Game.Map.Ground != null;
						if (flag5)
						{
							num6 = player.Game.Map.Ground.Height;
						}
						else
						{
							num5 = point2.X;
							num6 = point2.Y;
						}
						player.Game.AddPlayerSadowHandler(player, num5, num6, 50);
						player.ListObject.Add(50);
					}
					int num7;
					do
					{
						num7 = this.rand.Next(list.Count);
					}
					while (num7 == num);
					int num8 = list[num7].X;
					int num9 = list[num7].Y;
					Point point3 = player.Game.Map.FindYLineNotEmptyPointDown(num8, num9);
					bool flag6 = point3.IsEmpty && player.Game.Map.Ground != null;
					if (flag6)
					{
						num9 = player.Game.Map.Ground.Height;
					}
					else
					{
						num8 = point3.X;
						num9 = point3.Y;
					}
					player.Game.SendlivingBoltmove(player, num8, num9);
					player.PetEffectTrigger = true;
					player.Game.sendShowPicSkil(player, base.Info, true);
					new PetClonePostureEquip(this.m_count, base.Info.ID.ToString()).Start(player);
				}
			}
		}

		// Token: 0x04004724 RID: 18212
		private int m_probability;

		// Token: 0x04004725 RID: 18213
		private int m_currentId;

		// Token: 0x04004726 RID: 18214
		private int m_count;

		// Token: 0x04004727 RID: 18215
		private int m_delay;

		// Token: 0x04004728 RID: 18216
		private Dictionary<Living, int[]> oldPosition = new Dictionary<Living, int[]>();
	}
}
