﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D7D RID: 3453
	public class PE1262 : BasePetEffect
	{
		// Token: 0x06007B06 RID: 31494 RVA: 0x002914C4 File Offset: 0x0028F6C4
		public PE1262(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1262, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B07 RID: 31495 RVA: 0x00291544 File Offset: 0x0028F744
		public override bool Start(Living living)
		{
			PE1262 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1262) as PE1262;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B08 RID: 31496 RVA: 0x0002EC6C File Offset: 0x0002CE6C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.player_beginNextTurn;
		}

		// Token: 0x06007B09 RID: 31497 RVA: 0x0002EC82 File Offset: 0x0002CE82
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.player_beginNextTurn;
		}

		// Token: 0x06007B0A RID: 31498 RVA: 0x002915A4 File Offset: 0x0028F7A4
		public void player_beginNextTurn(Living living)
		{
			bool flag = living.PetEffects.BonusLucky == 0;
			if (flag)
			{
				this.m_added = 500;
				living.Lucky += (double)this.m_added;
				living.PetEffects.BonusLucky += this.m_added;
				living.Game.SendPetBuff(living, base.Info, true);
			}
		}

		// Token: 0x040048AB RID: 18603
		private int m_type = 0;

		// Token: 0x040048AC RID: 18604
		private int m_count = 0;

		// Token: 0x040048AD RID: 18605
		private int m_probability = 0;

		// Token: 0x040048AE RID: 18606
		private int m_delay = 0;

		// Token: 0x040048AF RID: 18607
		private int m_coldDown = 0;

		// Token: 0x040048B0 RID: 18608
		private int m_currentId;

		// Token: 0x040048B1 RID: 18609
		private int m_added = 0;
	}
}
