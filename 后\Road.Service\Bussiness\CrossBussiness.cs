﻿using System;
using System.Reflection;
using log4net;
using SqlDataProvider.BaseClass;

namespace Bussiness
{
	// Token: 0x02000FA7 RID: 4007
	public class CrossBussiness : IDisposable
	{
		// Token: 0x06008862 RID: 34914 RVA: 0x00036156 File Offset: 0x00034356
		public CrossBussiness()
		{
			this.db = new Sql_DbObject("AppConfig", "croCrossString");
		}

		// Token: 0x06008863 RID: 34915 RVA: 0x00036175 File Offset: 0x00034375
		public void Dispose()
		{
			this.db.Dispose();
			GC.SuppressFinalize(this);
		}

		// Token: 0x040053E0 RID: 21472
		protected static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040053E1 RID: 21473
		protected Sql_DbObject db;
	}
}
