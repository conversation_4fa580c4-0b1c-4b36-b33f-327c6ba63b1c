﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E47 RID: 3655
	public class CE1021 : BasePetEffect
	{
		// Token: 0x06007F35 RID: 32565 RVA: 0x002A4288 File Offset: 0x002A2488
		public CE1021(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1021, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F36 RID: 32566 RVA: 0x002A4304 File Offset: 0x002A2504
		public override bool Start(Living living)
		{
			CE1021 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1021) as CE1021;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F37 RID: 32567 RVA: 0x00031706 File Offset: 0x0002F906
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.IsNoHole = true;
			player.Game.SendPlayerPicture(player, 5, true);
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F38 RID: 32568 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F39 RID: 32569 RVA: 0x00031746 File Offset: 0x0002F946
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.IsNoHole = false;
			player.Game.SendPlayerPicture(player, 5, false);
		}

		// Token: 0x06007F3A RID: 32570 RVA: 0x002A4360 File Offset: 0x002A2560
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x04004E2C RID: 20012
		private int m_type = 0;

		// Token: 0x04004E2D RID: 20013
		private int m_count = 0;

		// Token: 0x04004E2E RID: 20014
		private int m_probability = 0;

		// Token: 0x04004E2F RID: 20015
		private int m_delay = 0;

		// Token: 0x04004E30 RID: 20016
		private int m_coldDown = 0;

		// Token: 0x04004E31 RID: 20017
		private int m_currentId;

		// Token: 0x04004E32 RID: 20018
		private int m_added = 0;
	}
}
