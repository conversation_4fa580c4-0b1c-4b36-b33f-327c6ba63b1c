﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E7F RID: 3711
	public class CE1173 : BasePetEffect
	{
		// Token: 0x0600808E RID: 32910 RVA: 0x002A97CC File Offset: 0x002A79CC
		public CE1173(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1173, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600808F RID: 32911 RVA: 0x002A984C File Offset: 0x002A7A4C
		public override bool Start(Living living)
		{
			CE1173 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1173) as CE1173;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008090 RID: 32912 RVA: 0x000323B7 File Offset: 0x000305B7
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008091 RID: 32913 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008092 RID: 32914 RVA: 0x002A98AC File Offset: 0x002A7AAC
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = target != living && this.rand.Next(100) < 50;
			if (flag)
			{
				this.m_added = 1000;
				living.SyncAtTime = true;
				living.AddBlood(this.m_added);
				living.SyncAtTime = false;
			}
		}

		// Token: 0x06008093 RID: 32915 RVA: 0x002A9900 File Offset: 0x002A7B00
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008094 RID: 32916 RVA: 0x000323F3 File Offset: 0x000305F3
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x04004FB4 RID: 20404
		private int m_type = 0;

		// Token: 0x04004FB5 RID: 20405
		private int m_count = 0;

		// Token: 0x04004FB6 RID: 20406
		private int m_probability = 0;

		// Token: 0x04004FB7 RID: 20407
		private int m_delay = 0;

		// Token: 0x04004FB8 RID: 20408
		private int m_coldDown = 0;

		// Token: 0x04004FB9 RID: 20409
		private int m_currentId;

		// Token: 0x04004FBA RID: 20410
		private int m_added = 0;
	}
}
