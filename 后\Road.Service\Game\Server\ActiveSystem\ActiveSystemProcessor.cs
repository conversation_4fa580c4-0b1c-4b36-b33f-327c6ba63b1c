﻿using System;
using Game.Base.Packets;
using Game.Server.GameObjects;

namespace Game.Server.ActiveSystem
{
	// Token: 0x02000C48 RID: 3144
	public class ActiveSystemProcessor
	{
		// Token: 0x0600700D RID: 28685 RVA: 0x0002A287 File Offset: 0x00028487
		public ActiveSystemProcessor(IActiveSystemProcessor processor)
		{
			this._processor = processor;
		}

		// Token: 0x0600700E RID: 28686 RVA: 0x0024D2E0 File Offset: 0x0024B4E0
		public void ProcessData(GamePlayer player, GSPacketIn data)
		{
			object syncStop = ActiveSystemProcessor._syncStop;
			lock (syncStop)
			{
				this._processor.OnGameData(player, data);
			}
		}

		// Token: 0x04003C47 RID: 15431
		private static object _syncStop = new object();

		// Token: 0x04003C48 RID: 15432
		private IActiveSystemProcessor _processor;
	}
}
