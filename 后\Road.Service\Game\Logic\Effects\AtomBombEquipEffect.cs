﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EDF RID: 3807
	public class AtomBombEquipEffect : BasePlayerEffect
	{
		// Token: 0x060082E8 RID: 33512 RVA: 0x00033D12 File Offset: 0x00031F12
		public AtomBombEquipEffect(int count, int probability)
			: base(eEffectType.AtomBomb)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x060082E9 RID: 33513 RVA: 0x002B21C8 File Offset: 0x002B03C8
		public override bool Start(Living living)
		{
			AtomBombEquipEffect atomBombEquipEffect = living.EffectList.GetOfType(eEffectType.AtomBomb) as AtomBombEquipEffect;
			bool flag = atomBombEquipEffect != null;
			bool flag2;
			if (flag)
			{
				atomBombEquipEffect.m_probability = ((this.m_probability > atomBombEquipEffect.m_probability) ? this.m_probability : atomBombEquipEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082EA RID: 33514 RVA: 0x00033D3A File Offset: 0x00031F3A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.ChangeProperty;
		}

		// Token: 0x060082EB RID: 33515 RVA: 0x00033D50 File Offset: 0x00031F50
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.ChangeProperty;
		}

		// Token: 0x060082EC RID: 33516 RVA: 0x002B2224 File Offset: 0x002B0424
		private void ChangeProperty(Player player, int ball)
		{
			bool flag = player.CurrentBall.ID != 1 && player.CurrentBall.ID != 3 && player.CurrentBall.ID != 5 && AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				player.AttackEffectTrigger = true;
				player.SetBall(4);
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("AtomBombEquipEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051E9 RID: 20969
		private int m_count = 0;

		// Token: 0x040051EA RID: 20970
		private int m_probability = 0;
	}
}
