﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EC7 RID: 3783
	public class xingling3 : BasePetEffect
	{
		// Token: 0x0600825E RID: 33374 RVA: 0x002B07C8 File Offset: 0x002AE9C8
		public xingling3(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.xingling2, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600825F RID: 33375 RVA: 0x002B0848 File Offset: 0x002AEA48
		public override bool Start(Living living)
		{
			xingling3 xingling = living.PetEffectList.GetOfType(ePetEffectType.xingling2) as xingling3;
			bool flag = xingling != null;
			bool flag2;
			if (flag)
			{
				xingling.m_probability = ((this.m_probability > xingling.m_probability) ? this.m_probability : xingling.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008260 RID: 33376 RVA: 0x002B08A8 File Offset: 0x002AEAA8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				player.Attack += (double)this.m_added;
				player.Defence *= 1.100000023841858;
				player.Agility *= 1.100000023841858;
				player.Lucky *= 1.100000023841858;
				player.Blood = (int)((float)player.Blood * 1.1f);
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008261 RID: 33377 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008262 RID: 33378 RVA: 0x002B0964 File Offset: 0x002AEB64
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008263 RID: 33379 RVA: 0x002B0998 File Offset: 0x002AEB98
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Attack -= (double)this.m_added;
			this.m_added = 0;
			player.Defence /= 1.100000023841858;
			player.Agility /= 1.100000023841858;
			player.Lucky /= 1.100000023841858;
			player.Blood = (int)((float)player.Blood / 1.1f);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x040051AF RID: 20911
		private int m_type = 0;

		// Token: 0x040051B0 RID: 20912
		private int m_count = 0;

		// Token: 0x040051B1 RID: 20913
		private int m_probability = 0;

		// Token: 0x040051B2 RID: 20914
		private int m_delay = 0;

		// Token: 0x040051B3 RID: 20915
		private int m_coldDown = 0;

		// Token: 0x040051B4 RID: 20916
		private int m_currentId;

		// Token: 0x040051B5 RID: 20917
		private int m_added = 0;
	}
}
