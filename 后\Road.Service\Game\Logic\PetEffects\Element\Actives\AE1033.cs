﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D9D RID: 3485
	public class AE1033 : BasePetEffect
	{
		// Token: 0x06007BAF RID: 31663 RVA: 0x00294134 File Offset: 0x00292334
		public AE1033(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1033, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BB0 RID: 31664 RVA: 0x002941B0 File Offset: 0x002923B0
		public override bool Start(Living living)
		{
			AE1033 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1033) as AE1033;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BB1 RID: 31665 RVA: 0x0002F427 File Offset: 0x0002D627
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BB2 RID: 31666 RVA: 0x0002F43D File Offset: 0x0002D63D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BB3 RID: 31667 RVA: 0x0029420C File Offset: 0x0029240C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1033(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004985 RID: 18821
		private int m_type = 0;

		// Token: 0x04004986 RID: 18822
		private int m_count = 0;

		// Token: 0x04004987 RID: 18823
		private int m_probability = 0;

		// Token: 0x04004988 RID: 18824
		private int m_delay = 0;

		// Token: 0x04004989 RID: 18825
		private int m_coldDown = 0;

		// Token: 0x0400498A RID: 18826
		private int m_currentId;

		// Token: 0x0400498B RID: 18827
		private int m_added = 0;
	}
}
