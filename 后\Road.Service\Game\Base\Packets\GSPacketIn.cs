﻿using System;

namespace Game.Base.Packets
{
	// Token: 0x02000F85 RID: 3973
	public class GSPacketIn : PacketIn
	{
		// Token: 0x170014CC RID: 5324
		// (get) Token: 0x060086AD RID: 34477 RVA: 0x002C61F4 File Offset: 0x002C43F4
		// (set) Token: 0x060086AE RID: 34478 RVA: 0x00035C7D File Offset: 0x00033E7D
		public short Code
		{
			get
			{
				return this.m_code;
			}
			set
			{
				this.m_code = value;
			}
		}

		// Token: 0x170014CD RID: 5325
		// (get) Token: 0x060086AF RID: 34479 RVA: 0x002C620C File Offset: 0x002C440C
		// (set) Token: 0x060086B0 RID: 34480 RVA: 0x00035C87 File Offset: 0x00033E87
		public int ClientID
		{
			get
			{
				return this.m_cliendId;
			}
			set
			{
				this.m_cliendId = value;
			}
		}

		// Token: 0x170014CE RID: 5326
		// (get) Token: 0x060086B1 RID: 34481 RVA: 0x002C6224 File Offset: 0x002C4424
		// (set) Token: 0x060086B2 RID: 34482 RVA: 0x00035C91 File Offset: 0x00033E91
		public int Parameter1
		{
			get
			{
				return this.m_parameter1;
			}
			set
			{
				this.m_parameter1 = value;
			}
		}

		// Token: 0x170014CF RID: 5327
		// (get) Token: 0x060086B3 RID: 34483 RVA: 0x002C623C File Offset: 0x002C443C
		// (set) Token: 0x060086B4 RID: 34484 RVA: 0x00035C9B File Offset: 0x00033E9B
		public int Parameter2
		{
			get
			{
				return this.m_parameter2;
			}
			set
			{
				this.m_parameter2 = value;
			}
		}

		// Token: 0x060086B5 RID: 34485 RVA: 0x00035CA5 File Offset: 0x00033EA5
		public GSPacketIn(byte[] buf, int size)
			: base(buf, size)
		{
		}

		// Token: 0x060086B6 RID: 34486 RVA: 0x00035CB1 File Offset: 0x00033EB1
		public GSPacketIn(short code)
			: this(code, 0, 8192)
		{
		}

		// Token: 0x060086B7 RID: 34487 RVA: 0x00035CC2 File Offset: 0x00033EC2
		public GSPacketIn(short code, int clientId)
			: this(code, clientId, 8192)
		{
		}

		// Token: 0x060086B8 RID: 34488 RVA: 0x00035CD3 File Offset: 0x00033ED3
		public GSPacketIn(short code, int clientId, int size)
			: base(new byte[size], 20)
		{
			this.m_code = code;
			this.m_cliendId = clientId;
			this.m_offset = 20;
		}

		// Token: 0x060086B9 RID: 34489 RVA: 0x00035CFB File Offset: 0x00033EFB
		public GSPacketIn(short code, int clientId, byte[] buf, int size)
			: base(buf, size)
		{
			this.m_code = code;
			this.m_cliendId = clientId;
		}

		// Token: 0x060086BA RID: 34490 RVA: 0x002C6254 File Offset: 0x002C4454
		public void ReadHeader()
		{
			this.ReadShort();
			this.m_length = (int)this.ReadShort();
			this.ReadShort();
			this.m_code = this.ReadShort();
			this.m_cliendId = this.ReadInt();
			this.m_parameter1 = this.ReadInt();
			this.m_parameter2 = this.ReadInt();
		}

		// Token: 0x060086BB RID: 34491 RVA: 0x002C62AC File Offset: 0x002C44AC
		public void WriteHeader()
		{
			lock (this)
			{
				int offset = this.m_offset;
				this.m_offset = 0;
				this.WriteShort(29099);
				this.WriteShort((short)this.m_length);
				this.m_offset = 6;
				this.WriteShort(this.m_code);
				this.WriteInt(this.m_cliendId);
				this.WriteInt(this.m_parameter1);
				this.WriteInt(this.m_parameter2);
				this.m_offset = 4;
				this.WriteShort(this.CheckSum());
				this.m_offset = offset;
			}
		}

		// Token: 0x060086BC RID: 34492 RVA: 0x002C6364 File Offset: 0x002C4564
		public short CheckSum()
		{
			short num = 119;
			int i = 6;
			while (i < this.m_length)
			{
				num += (short)this.m_buffer[i++];
			}
			return num & 32639;
		}

		// Token: 0x060086BD RID: 34493 RVA: 0x00035D16 File Offset: 0x00033F16
		public void WritePacket(GSPacketIn pkg)
		{
			pkg.WriteHeader();
			this.Write(pkg.Buffer, 0, pkg.Length);
		}

		// Token: 0x060086BE RID: 34494 RVA: 0x002C63A4 File Offset: 0x002C45A4
		public GSPacketIn ReadPacket()
		{
			byte[] array = this.ReadBytes();
			GSPacketIn gspacketIn = new GSPacketIn(array, array.Length);
			gspacketIn.ReadHeader();
			return gspacketIn;
		}

		// Token: 0x060086BF RID: 34495 RVA: 0x002C63D0 File Offset: 0x002C45D0
		public void Compress()
		{
			byte[] array = Marshal.Compress(this.m_buffer, 20, base.Length - 20);
			this.m_offset = 20;
			this.Write(array);
			this.m_length = array.Length + 20;
		}

		// Token: 0x060086C0 RID: 34496 RVA: 0x002C6414 File Offset: 0x002C4614
		public void UnCompress()
		{
			this.m_offset = 20;
			byte[] array = Marshal.Uncompress(this.ReadBytes());
			this.m_offset = 20;
			this.Write(array);
			this.m_length = array.Length + 20;
			this.m_offset = 20;
		}

		// Token: 0x060086C1 RID: 34497 RVA: 0x00035D34 File Offset: 0x00033F34
		public void ClearContext()
		{
			this.m_offset = 20;
			this.m_length = 20;
		}

		// Token: 0x060086C2 RID: 34498 RVA: 0x00035D47 File Offset: 0x00033F47
		public void ClearOffset()
		{
			this.m_offset = 20;
		}

		// Token: 0x060086C3 RID: 34499 RVA: 0x002C645C File Offset: 0x002C465C
		public GSPacketIn Clone()
		{
			GSPacketIn gspacketIn = new GSPacketIn(this.m_buffer, this.m_length);
			gspacketIn.ReadHeader();
			gspacketIn.Offset = this.m_length;
			return gspacketIn;
		}

		// Token: 0x060086C4 RID: 34500 RVA: 0x002C6498 File Offset: 0x002C4698
		public GSPacketIn Clones()
		{
			return new GSPacketIn(this.m_code, this.m_cliendId, this.m_buffer, this.m_length)
			{
				Offset = this.m_offset,
				Parameter1 = this.m_parameter1,
				Parameter2 = this.m_parameter2
			};
		}

		// Token: 0x04005395 RID: 21397
		public const ushort HDR_SIZE = 20;

		// Token: 0x04005396 RID: 21398
		public const short HEADER = 29099;

		// Token: 0x04005397 RID: 21399
		protected short m_code;

		// Token: 0x04005398 RID: 21400
		protected int m_cliendId;

		// Token: 0x04005399 RID: 21401
		protected int m_parameter1;

		// Token: 0x0400539A RID: 21402
		protected int m_parameter2;
	}
}
