﻿using System;

namespace Game.Logic
{
	// Token: 0x02000CA0 RID: 3232
	public class LivingConfig
	{
		// Token: 0x170013BC RID: 5052
		// (get) Token: 0x06007381 RID: 29569 RVA: 0x0002AEEF File Offset: 0x000290EF
		// (set) Token: 0x06007382 RID: 29570 RVA: 0x0002AEF7 File Offset: 0x000290F7
		public bool IsWorldBoss { get; set; }

		// Token: 0x170013BD RID: 5053
		// (get) Token: 0x06007383 RID: 29571 RVA: 0x0002AF00 File Offset: 0x00029100
		// (set) Token: 0x06007384 RID: 29572 RVA: 0x0002AF08 File Offset: 0x00029108
		public bool IsChristmasBoss { get; set; }

		// Token: 0x170013BE RID: 5054
		// (get) Token: 0x06007385 RID: 29573 RVA: 0x0002AF11 File Offset: 0x00029111
		// (set) Token: 0x06007386 RID: 29574 RVA: 0x0002AF19 File Offset: 0x00029119
		public bool IsHelper { get; set; }

		// Token: 0x170013BF RID: 5055
		// (get) Token: 0x06007387 RID: 29575 RVA: 0x0002AF22 File Offset: 0x00029122
		// (set) Token: 0x06007388 RID: 29576 RVA: 0x0002AF2A File Offset: 0x0002912A
		public bool CanCountKill { get; set; }

		// Token: 0x170013C0 RID: 5056
		// (get) Token: 0x06007389 RID: 29577 RVA: 0x0002AF33 File Offset: 0x00029133
		// (set) Token: 0x0600738A RID: 29578 RVA: 0x0002AF3B File Offset: 0x0002913B
		public bool IsShield { get; set; }

		// Token: 0x170013C1 RID: 5057
		// (get) Token: 0x0600738B RID: 29579 RVA: 0x0002AF44 File Offset: 0x00029144
		// (set) Token: 0x0600738C RID: 29580 RVA: 0x0002AF4C File Offset: 0x0002914C
		public bool IsDown { get; set; }

		// Token: 0x170013C2 RID: 5058
		// (get) Token: 0x0600738D RID: 29581 RVA: 0x0002AF55 File Offset: 0x00029155
		// (set) Token: 0x0600738E RID: 29582 RVA: 0x0002AF5D File Offset: 0x0002915D
		public bool KeepLife { get; set; }

		// Token: 0x170013C3 RID: 5059
		// (get) Token: 0x0600738F RID: 29583 RVA: 0x0002AF66 File Offset: 0x00029166
		// (set) Token: 0x06007390 RID: 29584 RVA: 0x0002AF6E File Offset: 0x0002916E
		public bool HasTurn { get; set; }

		// Token: 0x170013C4 RID: 5060
		// (get) Token: 0x06007391 RID: 29585 RVA: 0x0002AF77 File Offset: 0x00029177
		// (set) Token: 0x06007392 RID: 29586 RVA: 0x0002AF7F File Offset: 0x0002917F
		public bool CanFrost { get; set; }

		// Token: 0x170013C5 RID: 5061
		// (get) Token: 0x06007393 RID: 29587 RVA: 0x0002AF88 File Offset: 0x00029188
		// (set) Token: 0x06007394 RID: 29588 RVA: 0x0002AF90 File Offset: 0x00029190
		public bool CanHeal { get; set; }

		// Token: 0x170013C6 RID: 5062
		// (get) Token: 0x06007395 RID: 29589 RVA: 0x0002AF99 File Offset: 0x00029199
		// (set) Token: 0x06007396 RID: 29590 RVA: 0x0002AFA1 File Offset: 0x000291A1
		public bool IsGoal { get; set; }

		// Token: 0x170013C7 RID: 5063
		// (get) Token: 0x06007397 RID: 29591 RVA: 0x0002AFAA File Offset: 0x000291AA
		// (set) Token: 0x06007398 RID: 29592 RVA: 0x0002AFB2 File Offset: 0x000291B2
		public bool IsFly { get; set; }

		// Token: 0x170013C8 RID: 5064
		// (get) Token: 0x06007399 RID: 29593 RVA: 0x0002AFBB File Offset: 0x000291BB
		// (set) Token: 0x0600739A RID: 29594 RVA: 0x0002AFC3 File Offset: 0x000291C3
		public byte isBotom { get; set; }

		// Token: 0x170013C9 RID: 5065
		// (get) Token: 0x0600739B RID: 29595 RVA: 0x0002AFCC File Offset: 0x000291CC
		// (set) Token: 0x0600739C RID: 29596 RVA: 0x0002AFD4 File Offset: 0x000291D4
		public bool isShowBlood { get; set; }

		// Token: 0x170013CA RID: 5066
		// (get) Token: 0x0600739D RID: 29597 RVA: 0x0002AFDD File Offset: 0x000291DD
		// (set) Token: 0x0600739E RID: 29598 RVA: 0x0002AFE5 File Offset: 0x000291E5
		public bool isShowSmallMapPoint { get; set; }

		// Token: 0x170013CB RID: 5067
		// (get) Token: 0x0600739F RID: 29599 RVA: 0x0002AFEE File Offset: 0x000291EE
		// (set) Token: 0x060073A0 RID: 29600 RVA: 0x0002AFF6 File Offset: 0x000291F6
		public int ReduceBloodStart { get; set; }

		// Token: 0x170013CC RID: 5068
		// (get) Token: 0x060073A1 RID: 29601 RVA: 0x0002AFFF File Offset: 0x000291FF
		// (set) Token: 0x060073A2 RID: 29602 RVA: 0x0002B007 File Offset: 0x00029207
		public bool CanTakeDamage { get; set; }

		// Token: 0x170013CD RID: 5069
		// (get) Token: 0x060073A3 RID: 29603 RVA: 0x0002B010 File Offset: 0x00029210
		// (set) Token: 0x060073A4 RID: 29604 RVA: 0x0002B018 File Offset: 0x00029218
		public bool DamageForzen { get; set; }

		// Token: 0x170013CE RID: 5070
		// (get) Token: 0x060073A5 RID: 29605 RVA: 0x0002B021 File Offset: 0x00029221
		// (set) Token: 0x060073A6 RID: 29606 RVA: 0x0002B029 File Offset: 0x00029229
		public bool CompleteStep { get; set; }

		// Token: 0x170013CF RID: 5071
		// (get) Token: 0x060073A7 RID: 29607 RVA: 0x0002B032 File Offset: 0x00029232
		// (set) Token: 0x060073A8 RID: 29608 RVA: 0x0002B03A File Offset: 0x0002923A
		public int MaxStepMove { get; set; }

		// Token: 0x170013D0 RID: 5072
		// (get) Token: 0x060073A9 RID: 29609 RVA: 0x0002B043 File Offset: 0x00029243
		// (set) Token: 0x060073AA RID: 29610 RVA: 0x0002B04B File Offset: 0x0002924B
		public int FirstStepMove { get; set; }

		// Token: 0x170013D1 RID: 5073
		// (get) Token: 0x060073AB RID: 29611 RVA: 0x0002B054 File Offset: 0x00029254
		// (set) Token: 0x060073AC RID: 29612 RVA: 0x0002B05C File Offset: 0x0002925C
		public bool CancelGuard { get; set; }

		// Token: 0x170013D2 RID: 5074
		// (get) Token: 0x060073AD RID: 29613 RVA: 0x0002B065 File Offset: 0x00029265
		// (set) Token: 0x060073AE RID: 29614 RVA: 0x0002B06D File Offset: 0x0002926D
		public int MinBlood { get; set; }

		// Token: 0x170013D3 RID: 5075
		// (get) Token: 0x060073AF RID: 29615 RVA: 0x0002B076 File Offset: 0x00029276
		// (set) Token: 0x060073B0 RID: 29616 RVA: 0x0002B07E File Offset: 0x0002927E
		public int BallCanDamage { get; set; }

		// Token: 0x170013D4 RID: 5076
		// (get) Token: 0x060073B1 RID: 29617 RVA: 0x0002B087 File Offset: 0x00029287
		// (set) Token: 0x060073B2 RID: 29618 RVA: 0x0002B08F File Offset: 0x0002928F
		public bool IsHelperVortex { get; set; }
	}
}
