﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E5B RID: 3675
	public class CE1048 : BasePetEffect
	{
		// Token: 0x06007FB0 RID: 32688 RVA: 0x002A6190 File Offset: 0x002A4390
		public CE1048(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1048, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FB1 RID: 32689 RVA: 0x002A620C File Offset: 0x002A440C
		public override bool Start(Living living)
		{
			CE1048 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1048) as CE1048;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FB2 RID: 32690 RVA: 0x002A6268 File Offset: 0x002A4468
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				player.Defence += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007FB3 RID: 32691 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FB4 RID: 32692 RVA: 0x002A62CC File Offset: 0x002A44CC
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007FB5 RID: 32693 RVA: 0x002A6300 File Offset: 0x002A4500
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Defence -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004EB8 RID: 20152
		private int m_type = 0;

		// Token: 0x04004EB9 RID: 20153
		private int m_count = 0;

		// Token: 0x04004EBA RID: 20154
		private int m_probability = 0;

		// Token: 0x04004EBB RID: 20155
		private int m_delay = 0;

		// Token: 0x04004EBC RID: 20156
		private int m_coldDown = 0;

		// Token: 0x04004EBD RID: 20157
		private int m_currentId;

		// Token: 0x04004EBE RID: 20158
		private int m_added = 0;
	}
}
