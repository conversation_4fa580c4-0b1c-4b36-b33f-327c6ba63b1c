﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D18 RID: 3352
	public class PetReduceTakeDamageEquip : AbstractPetEffect
	{
		// Token: 0x060078DA RID: 30938 RVA: 0x00288210 File Offset: 0x00286410
		public PetReduceTakeDamageEquip(int count, string elementID)
			: base(ePetEffectType.PetReduceTakeDamageEquip, elementID)
		{
			this.m_count = count;
			this.m_elementID = elementID;
			uint num = <PrivateImplementationDetails>.ComputeStringHash(elementID);
			if (num <= 2510240658U)
			{
				if (num <= 1629016629U)
				{
					if (num <= 1511573296U)
					{
						if (num != 1013321913U)
						{
							if (num != 1175237273U)
							{
								if (num != 1511573296U)
								{
									return;
								}
								if (!(elementID == "1444"))
								{
									return;
								}
								goto IL_04AE;
							}
							else
							{
								if (!(elementID == "3584"))
								{
									return;
								}
								goto IL_049D;
							}
						}
						else
						{
							if (!(elementID == "2965"))
							{
								return;
							}
							goto IL_04E1;
						}
					}
					else if (num <= 1545128534U)
					{
						if (num != 1528350915U)
						{
							if (num != 1545128534U)
							{
								return;
							}
							if (!(elementID == "1446"))
							{
								return;
							}
							goto IL_048C;
						}
						else
						{
							if (!(elementID == "1445"))
							{
								return;
							}
							goto IL_048C;
						}
					}
					else if (num != 1612239010U)
					{
						if (num != 1629016629U)
						{
							return;
						}
						if (!(elementID == "1443"))
						{
							return;
						}
						goto IL_048C;
					}
					else
					{
						if (!(elementID == "1442"))
						{
							return;
						}
						goto IL_04AE;
					}
				}
				else if (num <= 1833576554U)
				{
					if (num != 1728542172U)
					{
						if (num != 1745466886U)
						{
							if (num != 1833576554U)
							{
								return;
							}
							if (!(elementID == "1381"))
							{
								return;
							}
							goto IL_04BF;
						}
						else
						{
							if (!(elementID == "2645"))
							{
								return;
							}
							goto IL_048C;
						}
					}
					else
					{
						if (!(elementID == "2650"))
						{
							return;
						}
						goto IL_04AE;
					}
				}
				else if (num <= 1963575933U)
				{
					if (num != 1850354173U)
					{
						if (num != 1963575933U)
						{
							return;
						}
						if (!(elementID == "2648"))
						{
							return;
						}
						goto IL_049D;
					}
					else
					{
						if (!(elementID == "1380"))
						{
							return;
						}
						goto IL_049D;
					}
				}
				else if (num != 2426352563U)
				{
					if (num != 2510240658U)
					{
						return;
					}
					if (!(elementID == "1033"))
					{
						return;
					}
				}
				else
				{
					if (!(elementID == "1034"))
					{
						return;
					}
					goto IL_048C;
				}
			}
			else if (num <= 3688782455U)
			{
				if (num <= 2916240372U)
				{
					if (num != 2527018277U)
					{
						if (num != 2696531946U)
						{
							if (num != 2916240372U)
							{
								return;
							}
							if (!(elementID == "1769"))
							{
								return;
							}
							this.m_value = 3;
							this.m_percent = true;
							return;
						}
						else
						{
							if (!(elementID == "3174"))
							{
								return;
							}
							goto IL_049D;
						}
					}
					else
					{
						if (!(elementID == "1032"))
						{
							return;
						}
						goto IL_048C;
					}
				}
				else if (num <= 3450807746U)
				{
					if (num != 3434030127U)
					{
						if (num != 3450807746U)
						{
							return;
						}
						if (!(elementID == "3220"))
						{
							return;
						}
						goto IL_04E1;
					}
					else
					{
						if (!(elementID == "3223"))
						{
							return;
						}
						goto IL_04BF;
					}
				}
				else if (num != 3672004836U)
				{
					if (num != 3688782455U)
					{
						return;
					}
					if (!(elementID == "1526"))
					{
						return;
					}
					goto IL_048C;
				}
				else
				{
					if (!(elementID == "1527"))
					{
						return;
					}
					goto IL_04AE;
				}
			}
			else if (num <= 4004692543U)
			{
				if (num <= 3943930693U)
				{
					if (num != 3927153074U)
					{
						if (num != 3943930693U)
						{
							return;
						}
						if (!(elementID == "1429"))
						{
							return;
						}
						goto IL_04AE;
					}
					else
					{
						if (!(elementID == "1428"))
						{
							return;
						}
						goto IL_048C;
					}
				}
				else if (num != 3987914924U)
				{
					if (num != 4004692543U)
					{
						return;
					}
					if (!(elementID == "1283"))
					{
						return;
					}
					this.m_value = 50;
					this.m_percent = true;
					return;
				}
				else
				{
					if (!(elementID == "1282"))
					{
						return;
					}
					goto IL_04BF;
				}
			}
			else if (num <= 4140046328U)
			{
				if (num != 4038247781U)
				{
					if (num != 4140046328U)
					{
						return;
					}
					if (!(elementID == "1233"))
					{
						return;
					}
				}
				else
				{
					if (!(elementID == "1281"))
					{
						return;
					}
					goto IL_049D;
				}
			}
			else if (num != 4156823947U)
			{
				if (num != 4257489661U)
				{
					return;
				}
				if (!(elementID == "1234"))
				{
					return;
				}
				goto IL_048C;
			}
			else if (!(elementID == "1232"))
			{
				return;
			}
			this.m_value = 5;
			this.m_percent = true;
			return;
			IL_048C:
			this.m_value = 10;
			this.m_percent = true;
			return;
			IL_049D:
			this.m_value = 15;
			this.m_percent = true;
			return;
			IL_04AE:
			this.m_value = 20;
			this.m_percent = true;
			return;
			IL_04BF:
			this.m_value = 30;
			this.m_percent = true;
			return;
			IL_04E1:
			this.m_value = 30;
			this.m_percent = true;
		}

		// Token: 0x060078DB RID: 30939 RVA: 0x00288720 File Offset: 0x00286920
		public override bool Start(Living living)
		{
			PetReduceTakeDamageEquip petReduceTakeDamageEquip = living.PetEffectList.GetOfType(ePetEffectType.PetReduceTakeDamageEquip) as PetReduceTakeDamageEquip;
			bool flag = petReduceTakeDamageEquip != null;
			bool flag2;
			if (flag)
			{
				petReduceTakeDamageEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078DC RID: 30940 RVA: 0x0002CF60 File Offset: 0x0002B160
		public override void OnAttached(Living player)
		{
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
			player.BeginSelfTurn += this.player_SelfTurn;
		}

		// Token: 0x060078DD RID: 30941 RVA: 0x0002CF89 File Offset: 0x0002B189
		public override void OnRemoved(Living player)
		{
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
			player.BeginSelfTurn += this.player_SelfTurn;
		}

		// Token: 0x060078DE RID: 30942 RVA: 0x00288768 File Offset: 0x00286968
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.m_elementID == "1442" || this.m_elementID == "1443" || this.m_elementID == "1444" || this.m_elementID == "1525";
			if (flag)
			{
				bool percent = this.m_percent;
				if (percent)
				{
					damageAmount -= 500 + damageAmount * this.m_value / 100;
				}
				else
				{
					damageAmount -= 500 + this.m_value;
				}
			}
			else
			{
				bool percent2 = this.m_percent;
				if (percent2)
				{
					damageAmount -= damageAmount * this.m_value / 100;
				}
				else
				{
					damageAmount -= this.m_value;
				}
			}
			living.Game.sendShowPicSkil(living, base.Info, true);
		}

		// Token: 0x060078DF RID: 30943 RVA: 0x00288840 File Offset: 0x00286A40
		private void player_SelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.Info, false);
				this.Stop();
			}
		}

		// Token: 0x040046D6 RID: 18134
		private int m_count;

		// Token: 0x040046D7 RID: 18135
		private int m_value;

		// Token: 0x040046D8 RID: 18136
		private bool m_percent;

		// Token: 0x040046D9 RID: 18137
		private string m_elementID;
	}
}
