﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FE1 RID: 4065
	public class NecklaceMgr
	{
		// Token: 0x06008B1D RID: 35613 RVA: 0x002FAD14 File Offset: 0x002F8F14
		public static bool Init()
		{
			return NecklaceMgr.ReLoad();
		}

		// Token: 0x06008B1E RID: 35614 RVA: 0x002FAD2C File Offset: 0x002F8F2C
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, NecklaceStrengInfo> dictionary = NecklaceMgr.LoadStrengFromDb();
				Dictionary<int, NecklaceCastingInfo> dictionary2 = NecklaceMgr.LoadCastingFromDb();
				bool flag = dictionary.Values.Count > 0 && dictionary2.Values.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, NecklaceStrengInfo>>(ref NecklaceMgr.m_NecklaceStreng, dictionary);
					Interlocked.Exchange<Dictionary<int, NecklaceCastingInfo>>(ref NecklaceMgr.m_NecklaceCasting, dictionary2);
					return true;
				}
			}
			catch (Exception ex)
			{
				NecklaceMgr.log.Error("NecklaceMgr init error:", ex);
			}
			return false;
		}

		// Token: 0x06008B1F RID: 35615 RVA: 0x002FADB8 File Offset: 0x002F8FB8
		private static Dictionary<int, NecklaceStrengInfo> LoadStrengFromDb()
		{
			Dictionary<int, NecklaceStrengInfo> dictionary = new Dictionary<int, NecklaceStrengInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				NecklaceStrengInfo[] allNecklaceStreng = produceBussiness.GetAllNecklaceStreng();
				NecklaceStrengInfo[] array = allNecklaceStreng;
				NecklaceStrengInfo[] array2 = array;
				NecklaceStrengInfo[] array3 = array2;
				foreach (NecklaceStrengInfo necklaceStrengInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(necklaceStrengInfo.Level);
					if (flag)
					{
						dictionary.Add(necklaceStrengInfo.Level, necklaceStrengInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008B20 RID: 35616 RVA: 0x002FAE54 File Offset: 0x002F9054
		private static Dictionary<int, NecklaceCastingInfo> LoadCastingFromDb()
		{
			Dictionary<int, NecklaceCastingInfo> dictionary = new Dictionary<int, NecklaceCastingInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				NecklaceCastingInfo[] allNecklaceCasting = produceBussiness.GetAllNecklaceCasting();
				NecklaceCastingInfo[] array = allNecklaceCasting;
				NecklaceCastingInfo[] array2 = array;
				NecklaceCastingInfo[] array3 = array2;
				foreach (NecklaceCastingInfo necklaceCastingInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(necklaceCastingInfo.Level);
					if (flag)
					{
						dictionary.Add(necklaceCastingInfo.Level, necklaceCastingInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008B21 RID: 35617 RVA: 0x002FAEF0 File Offset: 0x002F90F0
		public static int GetNecklaceLevelByGP(int exp)
		{
			foreach (KeyValuePair<int, NecklaceStrengInfo> keyValuePair in NecklaceMgr.m_NecklaceStreng)
			{
				bool flag = keyValuePair.Value.NecklaceStrengthExp == exp;
				if (flag)
				{
					return keyValuePair.Key;
				}
			}
			return -1;
		}

		// Token: 0x06008B22 RID: 35618 RVA: 0x002FAF64 File Offset: 0x002F9164
		public static NecklaceStrengInfo FindStrengthenExpInfo(int id)
		{
			bool flag = NecklaceMgr.m_NecklaceStreng.ContainsKey(id);
			NecklaceStrengInfo necklaceStrengInfo;
			if (flag)
			{
				necklaceStrengInfo = NecklaceMgr.m_NecklaceStreng[id];
			}
			else
			{
				necklaceStrengInfo = null;
			}
			return necklaceStrengInfo;
		}

		// Token: 0x06008B23 RID: 35619 RVA: 0x002FAF98 File Offset: 0x002F9198
		public static NecklaceCastingInfo FindNecklaceCasting(int id)
		{
			bool flag = NecklaceMgr.m_NecklaceCasting.ContainsKey(id);
			NecklaceCastingInfo necklaceCastingInfo;
			if (flag)
			{
				necklaceCastingInfo = NecklaceMgr.m_NecklaceCasting[id];
			}
			else
			{
				necklaceCastingInfo = null;
			}
			return necklaceCastingInfo;
		}

		// Token: 0x06008B24 RID: 35620 RVA: 0x002FAFCC File Offset: 0x002F91CC
		public static int StrengMaxLevel()
		{
			return NecklaceMgr.m_NecklaceCasting.Count - 1;
		}

		// Token: 0x06008B25 RID: 35621 RVA: 0x002FAFCC File Offset: 0x002F91CC
		public static int CastingMaxLevel()
		{
			return NecklaceMgr.m_NecklaceCasting.Count - 1;
		}

		// Token: 0x06008B26 RID: 35622 RVA: 0x002FAFEC File Offset: 0x002F91EC
		public static void BuildNeckLaceProp(int level, ref int att, ref int def, ref int agi, ref int luck, ref int blood)
		{
			NecklaceCastingInfo necklaceCastingInfo = NecklaceMgr.FindNecklaceCasting(level);
			bool flag = necklaceCastingInfo != null;
			if (flag)
			{
				att += necklaceCastingInfo.Attack;
				def += necklaceCastingInfo.Defence;
				agi += necklaceCastingInfo.Agility;
				luck += necklaceCastingInfo.Luck;
				blood += necklaceCastingInfo.Blood;
			}
		}

		// Token: 0x0400552F RID: 21807
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005530 RID: 21808
		private static Dictionary<int, NecklaceStrengInfo> m_NecklaceStreng = new Dictionary<int, NecklaceStrengInfo>();

		// Token: 0x04005531 RID: 21809
		private static Dictionary<int, NecklaceCastingInfo> m_NecklaceCasting = new Dictionary<int, NecklaceCastingInfo>();
	}
}
