﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using Game.Base;
using Game.Base.Packets;
using Game.Server.GameObjects;
using Game.Server.Managers;
using Game.Server.Rooms;

namespace Game.Server.Battle
{
	// Token: 0x02000C43 RID: 3139
	public class BattleServer
	{
		// Token: 0x17001317 RID: 4887
		// (get) Token: 0x06006FBC RID: 28604 RVA: 0x0024B9DC File Offset: 0x00249BDC
		// (set) Token: 0x06006FBD RID: 28605 RVA: 0x00029F42 File Offset: 0x00028142
		public int RetryCount
		{
			get
			{
				return this.m_retryCount;
			}
			set
			{
				this.m_retryCount = value;
			}
		}

		// Token: 0x17001318 RID: 4888
		// (get) Token: 0x06006FBE RID: 28606 RVA: 0x0024B9F4 File Offset: 0x00249BF4
		// (set) Token: 0x06006FBF RID: 28607 RVA: 0x00029F4C File Offset: 0x0002814C
		public DateTime LastRetryTime
		{
			get
			{
				return this.m_lastRetryTime;
			}
			set
			{
				this.m_lastRetryTime = value;
			}
		}

		// Token: 0x17001319 RID: 4889
		// (get) Token: 0x06006FC0 RID: 28608 RVA: 0x00029F56 File Offset: 0x00028156
		public FightServerConnector Server
		{
			get
			{
				return this.m_server;
			}
		}

		// Token: 0x1700131A RID: 4890
		// (get) Token: 0x06006FC1 RID: 28609 RVA: 0x00029F56 File Offset: 0x00028156
		public FightServerConnector Connector
		{
			get
			{
				return this.m_server;
			}
		}

		// Token: 0x1700131B RID: 4891
		// (get) Token: 0x06006FC2 RID: 28610 RVA: 0x00029F5E File Offset: 0x0002815E
		public string LoginKey
		{
			get
			{
				return this.m_loginKey;
			}
		}

		// Token: 0x1700131C RID: 4892
		// (get) Token: 0x06006FC3 RID: 28611 RVA: 0x00029F66 File Offset: 0x00028166
		public int ServerId
		{
			get
			{
				return this.m_serverId;
			}
		}

		// Token: 0x1700131D RID: 4893
		// (get) Token: 0x06006FC4 RID: 28612 RVA: 0x00029F6E File Offset: 0x0002816E
		public bool IsActive
		{
			get
			{
				return this.m_server.IsConnected;
			}
		}

		// Token: 0x1700131E RID: 4894
		// (get) Token: 0x06006FC5 RID: 28613 RVA: 0x00029F7B File Offset: 0x0002817B
		public string Ip
		{
			get
			{
				return this.m_ip;
			}
		}

		// Token: 0x1700131F RID: 4895
		// (get) Token: 0x06006FC6 RID: 28614 RVA: 0x00029F83 File Offset: 0x00028183
		public int Port
		{
			get
			{
				return this.m_port;
			}
		}

		// Token: 0x140000AE RID: 174
		// (add) Token: 0x06006FC7 RID: 28615 RVA: 0x0024BA0C File Offset: 0x00249C0C
		// (remove) Token: 0x06006FC8 RID: 28616 RVA: 0x0024BA44 File Offset: 0x00249C44
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventHandler Disconnected;

		// Token: 0x06006FC9 RID: 28617 RVA: 0x0024BA7C File Offset: 0x00249C7C
		public BattleServer(int serverId, string ip, int port, string loginKey)
		{
			this.m_serverId = serverId;
			this.m_ip = ip;
			this.m_port = port;
			this.m_loginKey = loginKey;
			this.m_retryCount = 0;
			this.m_lastRetryTime = DateTime.Now;
			this.m_server = new FightServerConnector(this, ip, port, loginKey);
			this.m_rooms = new Dictionary<int, BaseRoom>();
			this.m_server.Disconnected += this.m_server_Disconnected;
			this.m_server.Connected += this.m_server_Connected;
		}

		// Token: 0x06006FCA RID: 28618 RVA: 0x0024BB0C File Offset: 0x00249D0C
		public BattleServer Clone()
		{
			return new BattleServer(this.m_serverId, this.m_ip, this.m_port, this.m_loginKey);
		}

		// Token: 0x06006FCB RID: 28619 RVA: 0x0024BB3C File Offset: 0x00249D3C
		public void Start()
		{
			bool flag = !this.m_server.Connect();
			if (flag)
			{
				ThreadPool.QueueUserWorkItem(new WaitCallback(this.InvokeDisconnect));
			}
		}

		// Token: 0x06006FCC RID: 28620 RVA: 0x00029F8B File Offset: 0x0002818B
		private void InvokeDisconnect(object state)
		{
			this.m_server_Disconnected(this.m_server);
		}

		// Token: 0x06006FCD RID: 28621 RVA: 0x00005683 File Offset: 0x00003883
		private void m_server_Connected(BaseClient client)
		{
		}

		// Token: 0x06006FCE RID: 28622 RVA: 0x0024BB70 File Offset: 0x00249D70
		private void m_server_Disconnected(BaseClient client)
		{
			this.RemoveAllRoom();
			bool flag = this.Disconnected != null;
			if (flag)
			{
				this.Disconnected(this, null);
			}
		}

		// Token: 0x06006FCF RID: 28623 RVA: 0x0024BBA4 File Offset: 0x00249DA4
		public void RemoveAllRoom()
		{
			BaseRoom[] array = null;
			Dictionary<int, BaseRoom> rooms = this.m_rooms;
			lock (rooms)
			{
				array = this.m_rooms.Values.ToArray<BaseRoom>();
				this.m_rooms.Clear();
			}
			BaseRoom[] array2 = array;
			BaseRoom[] array3 = array2;
			BaseRoom[] array4 = array3;
			foreach (BaseRoom baseRoom in array4)
			{
				bool flag2 = baseRoom != null;
				if (flag2)
				{
					baseRoom.RemoveAllPlayer();
					RoomMgr.StopProxyGame(baseRoom);
				}
			}
		}

		// Token: 0x06006FD0 RID: 28624 RVA: 0x0024BC4C File Offset: 0x00249E4C
		public BaseRoom FindRoom(int roomId)
		{
			BaseRoom baseRoom = null;
			Dictionary<int, BaseRoom> rooms = this.m_rooms;
			lock (rooms)
			{
				bool flag2 = this.m_rooms.ContainsKey(roomId);
				if (flag2)
				{
					baseRoom = this.m_rooms[roomId];
				}
			}
			return baseRoom;
		}

		// Token: 0x06006FD1 RID: 28625 RVA: 0x0024BCB4 File Offset: 0x00249EB4
		public bool AddRoom(BaseRoom room)
		{
			bool flag = false;
			BaseRoom baseRoom = null;
			Dictionary<int, BaseRoom> rooms = this.m_rooms;
			lock (rooms)
			{
				bool flag3 = this.m_rooms.ContainsKey(room.RoomId);
				if (flag3)
				{
					baseRoom = this.m_rooms[room.RoomId];
					this.m_rooms.Remove(room.RoomId);
				}
			}
			bool flag4 = baseRoom != null && baseRoom.Game != null;
			if (flag4)
			{
				baseRoom.Game.Stop();
			}
			Dictionary<int, BaseRoom> rooms2 = this.m_rooms;
			lock (rooms2)
			{
				bool flag6 = !this.m_rooms.ContainsKey(room.RoomId);
				if (flag6)
				{
					this.m_rooms.Add(room.RoomId, room);
					flag = true;
				}
			}
			bool flag7 = flag;
			if (flag7)
			{
				this.m_server.SendAddRoom(room);
			}
			return flag;
		}

		// Token: 0x06006FD2 RID: 28626 RVA: 0x0024BDD8 File Offset: 0x00249FD8
		public bool RemoveRoom(BaseRoom room)
		{
			bool flag = false;
			Dictionary<int, BaseRoom> rooms = this.m_rooms;
			lock (rooms)
			{
				flag = this.m_rooms.ContainsKey(room.RoomId);
			}
			bool flag3 = flag;
			if (flag3)
			{
				this.m_server.SendRemoveRoom(room);
			}
			return flag;
		}

		// Token: 0x06006FD3 RID: 28627 RVA: 0x0024BE48 File Offset: 0x0024A048
		public void RemoveRoomImp(int roomId)
		{
			BaseRoom baseRoom = null;
			Dictionary<int, BaseRoom> rooms = this.m_rooms;
			lock (rooms)
			{
				bool flag2 = this.m_rooms.ContainsKey(roomId);
				if (flag2)
				{
					baseRoom = this.m_rooms[roomId];
					this.m_rooms.Remove(roomId);
				}
			}
			bool flag3 = baseRoom != null;
			if (flag3)
			{
				bool flag4 = baseRoom.IsPlaying && baseRoom.Game == null;
				if (flag4)
				{
					RoomMgr.CancelPickup(this, baseRoom);
				}
				else
				{
					RoomMgr.StopProxyGame(baseRoom);
				}
			}
		}

		// Token: 0x06006FD4 RID: 28628 RVA: 0x0024BEF0 File Offset: 0x0024A0F0
		public void StartGame(int roomId, ProxyGame game)
		{
			BaseRoom baseRoom = this.FindRoom(roomId);
			bool flag = baseRoom != null;
			if (flag)
			{
				RoomMgr.StartProxyGame(baseRoom, game);
			}
		}

		// Token: 0x06006FD5 RID: 28629 RVA: 0x0024BF18 File Offset: 0x0024A118
		public void StopGame(int roomId, int gameId)
		{
			BaseRoom baseRoom = this.FindRoom(roomId);
			bool flag = baseRoom != null;
			if (flag)
			{
				RoomMgr.StopProxyGame(baseRoom);
				Dictionary<int, BaseRoom> rooms = this.m_rooms;
				lock (rooms)
				{
					this.m_rooms.Remove(roomId);
				}
			}
		}

		// Token: 0x06006FD6 RID: 28630 RVA: 0x0024BF80 File Offset: 0x0024A180
		public void SendToRoom(int roomId, GSPacketIn pkg, int exceptId, int exceptGameId)
		{
			BaseRoom baseRoom = this.FindRoom(roomId);
			bool flag = baseRoom == null;
			if (!flag)
			{
				bool flag2 = exceptId != 0;
				if (flag2)
				{
					GamePlayer playerById = WorldMgr.GetPlayerById(exceptId);
					bool flag3 = playerById != null;
					if (flag3)
					{
						bool flag4 = playerById.TempGameId == exceptGameId;
						if (flag4)
						{
							baseRoom.SendToAll(pkg, playerById);
						}
						else
						{
							baseRoom.SendToAll(pkg);
						}
					}
				}
				else
				{
					baseRoom.SendToAll(pkg);
				}
			}
		}

		// Token: 0x06006FD7 RID: 28631 RVA: 0x00029F9B File Offset: 0x0002819B
		public void SendToUser(int playerid, GSPacketIn pkg)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(playerid);
			if (playerById != null)
			{
				playerById.SendTCP(pkg);
			}
		}

		// Token: 0x06006FD8 RID: 28632 RVA: 0x0024BFF4 File Offset: 0x0024A1F4
		public void UpdatePlayerGameId(int playerid, int gamePlayerId)
		{
			GamePlayer playerById = WorldMgr.GetPlayerById(playerid);
			bool flag = playerById != null;
			if (flag)
			{
				playerById.GamePlayerId = gamePlayerId;
				playerById.TempGameId = gamePlayerId;
			}
		}

		// Token: 0x06006FD9 RID: 28633 RVA: 0x0024C024 File Offset: 0x0024A224
		public override string ToString()
		{
			return string.Format("ServerID:{0},Ip:{1},Port:{2},IsConnected:{3},RoomCount:{4}", new object[]
			{
				this.m_serverId,
				this.m_server.RemoteEP.Address,
				this.m_server.RemoteEP.Port,
				this.m_server.IsConnected,
				this.m_rooms.Count
			});
		}

		// Token: 0x04003C38 RID: 15416
		private int m_serverId;

		// Token: 0x04003C39 RID: 15417
		private FightServerConnector m_server;

		// Token: 0x04003C3A RID: 15418
		private int m_retryCount;

		// Token: 0x04003C3B RID: 15419
		private DateTime m_lastRetryTime;

		// Token: 0x04003C3C RID: 15420
		private Dictionary<int, BaseRoom> m_rooms;

		// Token: 0x04003C3D RID: 15421
		private string m_ip;

		// Token: 0x04003C3E RID: 15422
		private int m_port;

		// Token: 0x04003C3F RID: 15423
		private string m_loginKey;
	}
}
