﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D1B RID: 3355
	public class PetShowFireofResentmentEquip : BasePetEffect
	{
		// Token: 0x060078EB RID: 30955 RVA: 0x0002D086 File Offset: 0x0002B286
		public PetShowFireofResentmentEquip(int count, string elementID)
			: base(ePetEffectType.PetShowFireofResentmentEquip, elementID)
		{
			this.m_count = count;
		}

		// Token: 0x060078EC RID: 30956 RVA: 0x00288AF4 File Offset: 0x00286CF4
		public override bool Start(Living living)
		{
			PetShowFireofResentmentEquip petShowFireofResentmentEquip = living.PetEffectList.GetOfType(ePetEffectType.PetShowFireofResentmentEquip) as PetShowFireofResentmentEquip;
			bool flag = petShowFireofResentmentEquip != null;
			bool flag2;
			if (flag)
			{
				petShowFireofResentmentEquip.m_count += this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078ED RID: 30957 RVA: 0x0002D09D File Offset: 0x0002B29D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x060078EE RID: 30958 RVA: 0x0002D0B3 File Offset: 0x0002B2B3
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.sendShowPicSkil(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x060078EF RID: 30959 RVA: 0x00288B44 File Offset: 0x00286D44
		private void player_BeginSelfTurn(Living living)
		{
			living.Game.sendShowPicSkil(living, base.ElementInfo, false);
			this.m_count--;
			bool flag = base.ElementInfo.Pic != -1;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
			}
			bool flag2 = this.m_count < 0;
			if (flag2)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
			else
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
			}
		}

		// Token: 0x040046DC RID: 18140
		private int m_count;
	}
}
