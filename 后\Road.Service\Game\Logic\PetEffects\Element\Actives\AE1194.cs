﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DF4 RID: 3572
	public class AE1194 : BasePetEffect
	{
		// Token: 0x06007D74 RID: 32116 RVA: 0x0029C164 File Offset: 0x0029A364
		public AE1194(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1194, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D75 RID: 32117 RVA: 0x0029C1E4 File Offset: 0x0029A3E4
		public override bool Start(Living living)
		{
			AE1194 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1194) as AE1194;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D76 RID: 32118 RVA: 0x00030531 File Offset: 0x0002E731
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D77 RID: 32119 RVA: 0x00030547 File Offset: 0x0002E747
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D78 RID: 32120 RVA: 0x0029C244 File Offset: 0x0029A444
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1194(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004BE4 RID: 19428
		private int m_type = 0;

		// Token: 0x04004BE5 RID: 19429
		private int m_count = 0;

		// Token: 0x04004BE6 RID: 19430
		private int m_probability = 0;

		// Token: 0x04004BE7 RID: 19431
		private int m_delay = 0;

		// Token: 0x04004BE8 RID: 19432
		private int m_coldDown = 0;

		// Token: 0x04004BE9 RID: 19433
		private int m_currentId;

		// Token: 0x04004BEA RID: 19434
		private int m_added = 0;
	}
}
