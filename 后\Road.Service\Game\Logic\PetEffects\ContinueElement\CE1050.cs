﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E5D RID: 3677
	public class CE1050 : BasePetEffect
	{
		// Token: 0x06007FBA RID: 32698 RVA: 0x002A646C File Offset: 0x002A466C
		public CE1050(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1050, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FBB RID: 32699 RVA: 0x002A64E8 File Offset: 0x002A46E8
		public override bool Start(Living living)
		{
			CE1050 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1050) as CE1050;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FBC RID: 32700 RVA: 0x002A6544 File Offset: 0x002A4744
		protected override void OnAttachedToPlayer(Player player)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 500;
				player.BaseGuard += (double)this.m_added;
			}
		}

		// Token: 0x06007FBD RID: 32701 RVA: 0x00031B48 File Offset: 0x0002FD48
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BaseGuard -= (double)this.m_added;
		}

		// Token: 0x04004EC6 RID: 20166
		private int m_type = 0;

		// Token: 0x04004EC7 RID: 20167
		private int m_count = 0;

		// Token: 0x04004EC8 RID: 20168
		private int m_probability = 0;

		// Token: 0x04004EC9 RID: 20169
		private int m_delay = 0;

		// Token: 0x04004ECA RID: 20170
		private int m_coldDown = 0;

		// Token: 0x04004ECB RID: 20171
		private int m_currentId;

		// Token: 0x04004ECC RID: 20172
		private int m_added = 0;
	}
}
