﻿using System;
using System.Collections;
using System.Reflection;
using Game.Logic.Phy.Object;
using log4net;

namespace Game.Logic.PetEffects
{
	// Token: 0x02000D53 RID: 3411
	public class PetEffectList
	{
		// Token: 0x1700149B RID: 5275
		// (get) Token: 0x06007A24 RID: 31268 RVA: 0x0002E3F7 File Offset: 0x0002C5F7
		public ArrayList List
		{
			get
			{
				return this.m_effects;
			}
		}

		// Token: 0x06007A25 RID: 31269 RVA: 0x0002E3FF File Offset: 0x0002C5FF
		public PetEffectList(Living owner, int immunity)
		{
			this.m_owner = owner;
			this.m_effects = new ArrayList(5);
			this.m_immunity = immunity;
		}

		// Token: 0x06007A26 RID: 31270 RVA: 0x0028D93C File Offset: 0x0028BB3C
		public bool CanAddEffect(int id)
		{
			bool flag = id > 999 || id < 0;
			return flag || ((1 << id - 1) & this.m_immunity) == 0;
		}

		// Token: 0x06007A27 RID: 31271 RVA: 0x0028D978 File Offset: 0x0028BB78
		public virtual bool Add(AbstractPetEffect effect)
		{
			bool flag = this.CanAddEffect(effect.TypeValue);
			bool flag3;
			if (flag)
			{
				ArrayList effects = this.m_effects;
				lock (effects)
				{
					this.m_effects.Add(effect);
				}
				effect.OnAttached(this.m_owner);
				this.OnEffectsChanged(effect);
				flag3 = true;
			}
			else
			{
				flag3 = false;
			}
			return flag3;
		}

		// Token: 0x06007A28 RID: 31272 RVA: 0x0028D9F4 File Offset: 0x0028BBF4
		public virtual bool Pause(AbstractPetEffect effect)
		{
			ArrayList effects = this.m_effects;
			lock (effects)
			{
				int num = this.m_effects.IndexOf(effect);
				bool flag2 = num < 0;
				if (flag2)
				{
					return false;
				}
			}
			effect.OnPaused(this.m_owner);
			this.OnEffectsChanged(effect);
			return true;
		}

		// Token: 0x06007A29 RID: 31273 RVA: 0x0028DA6C File Offset: 0x0028BC6C
		public virtual bool Remove(AbstractPetEffect effect)
		{
			int num = -1;
			ArrayList effects = this.m_effects;
			lock (effects)
			{
				num = this.m_effects.IndexOf(effect);
				bool flag2 = num < 0;
				if (flag2)
				{
					return false;
				}
				this.m_effects.RemoveAt(num);
			}
			bool flag3 = num != -1;
			bool flag4;
			if (flag3)
			{
				effect.OnRemoved(this.m_owner);
				this.OnEffectsChanged(effect);
				flag4 = true;
			}
			else
			{
				flag4 = false;
			}
			return flag4;
		}

		// Token: 0x06007A2A RID: 31274 RVA: 0x0028DB04 File Offset: 0x0028BD04
		public virtual void OnEffectsChanged(AbstractPetEffect changedEffect)
		{
			bool flag = this.m_changesCount <= 0;
			if (flag)
			{
				this.UpdateChangedEffects();
			}
		}

		// Token: 0x06007A2B RID: 31275 RVA: 0x0002E423 File Offset: 0x0002C623
		public void BeginChanges()
		{
			this.m_changesCount += 1;
		}

		// Token: 0x06007A2C RID: 31276 RVA: 0x0028DB30 File Offset: 0x0028BD30
		public virtual void CommitChanges()
		{
			sbyte b = this.m_changesCount - 1;
			this.m_changesCount = b;
			bool flag = b < 0;
			if (flag)
			{
				bool isWarnEnabled = PetEffectList.log.IsWarnEnabled;
				if (isWarnEnabled)
				{
					PetEffectList.log.Warn("changes count is less than zero, forgot BeginChanges()?\n" + Environment.StackTrace);
				}
				this.m_changesCount = 0;
			}
			bool flag2 = this.m_changesCount == 0;
			if (flag2)
			{
				this.UpdateChangedEffects();
			}
		}

		// Token: 0x06007A2D RID: 31277 RVA: 0x00005683 File Offset: 0x00003883
		protected virtual void UpdateChangedEffects()
		{
		}

		// Token: 0x06007A2E RID: 31278 RVA: 0x0028DBA8 File Offset: 0x0028BDA8
		public virtual AbstractPetEffect GetOfType(ePetEffectType effectType)
		{
			ArrayList effects = this.m_effects;
			lock (effects)
			{
				foreach (object obj in this.m_effects)
				{
					AbstractPetEffect abstractPetEffect = (AbstractPetEffect)obj;
					bool flag2 = abstractPetEffect.Type == effectType;
					if (flag2)
					{
						return abstractPetEffect;
					}
				}
			}
			return null;
		}

		// Token: 0x06007A2F RID: 31279 RVA: 0x0028DC4C File Offset: 0x0028BE4C
		public virtual IList GetAllOfType(ePetEffectType effectType)
		{
			ArrayList arrayList = new ArrayList();
			ArrayList effects = this.m_effects;
			lock (effects)
			{
				foreach (object obj in this.m_effects)
				{
					AbstractPetEffect abstractPetEffect = (AbstractPetEffect)obj;
					bool flag2 = abstractPetEffect.GetType().Equals(effectType);
					if (flag2)
					{
						arrayList.Add(abstractPetEffect);
					}
				}
			}
			return arrayList;
		}

		// Token: 0x06007A30 RID: 31280 RVA: 0x0028DD04 File Offset: 0x0028BF04
		public void StopEffect(ePetEffectType effectType)
		{
			IList allOfType = this.GetAllOfType(effectType);
			this.BeginChanges();
			foreach (object obj in allOfType)
			{
				AbstractPetEffect abstractPetEffect = (AbstractPetEffect)obj;
				abstractPetEffect.Stop();
			}
			this.CommitChanges();
		}

		// Token: 0x06007A31 RID: 31281 RVA: 0x0028DD74 File Offset: 0x0028BF74
		public void StopAllEffect()
		{
			bool flag = this.m_effects.Count > 0;
			if (flag)
			{
				AbstractPetEffect[] array = new AbstractPetEffect[this.m_effects.Count];
				this.m_effects.CopyTo(array);
				AbstractPetEffect[] array2 = array;
				AbstractPetEffect[] array3 = array2;
				AbstractPetEffect[] array4 = array3;
				foreach (AbstractPetEffect abstractPetEffect in array4)
				{
					abstractPetEffect.Stop();
				}
				this.m_effects.Clear();
			}
		}

		// Token: 0x04004787 RID: 18311
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04004788 RID: 18312
		protected ArrayList m_effects;

		// Token: 0x04004789 RID: 18313
		protected readonly Living m_owner;

		// Token: 0x0400478A RID: 18314
		protected volatile sbyte m_changesCount;

		// Token: 0x0400478B RID: 18315
		protected int m_immunity;
	}
}
