﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D93 RID: 3475
	public class AE1020 : BasePetEffect
	{
		// Token: 0x06007B79 RID: 31609 RVA: 0x00293164 File Offset: 0x00291364
		public AE1020(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1020, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B7A RID: 31610 RVA: 0x002931E4 File Offset: 0x002913E4
		public override bool Start(Living living)
		{
			AE1020 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1020) as AE1020;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B7B RID: 31611 RVA: 0x0002F1D7 File Offset: 0x0002D3D7
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007B7C RID: 31612 RVA: 0x0002F1ED File Offset: 0x0002D3ED
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007B7D RID: 31613 RVA: 0x00293244 File Offset: 0x00291444
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1020(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x0400493F RID: 18751
		private int m_type = 0;

		// Token: 0x04004940 RID: 18752
		private int m_count = 0;

		// Token: 0x04004941 RID: 18753
		private int m_probability = 0;

		// Token: 0x04004942 RID: 18754
		private int m_delay = 0;

		// Token: 0x04004943 RID: 18755
		private int m_coldDown = 0;

		// Token: 0x04004944 RID: 18756
		private int m_currentId;

		// Token: 0x04004945 RID: 18757
		private int m_added = 0;
	}
}
