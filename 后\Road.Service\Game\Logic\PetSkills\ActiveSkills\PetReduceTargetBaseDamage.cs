﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D46 RID: 3398
	public class PetReduceTargetBaseDamage : BasePetEffect
	{
		// Token: 0x060079D4 RID: 31188 RVA: 0x0002DFAB File Offset: 0x0002C1AB
		public PetReduceTargetBaseDamage(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetReduceTargetBaseDamage, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x060079D5 RID: 31189 RVA: 0x0028CAB4 File Offset: 0x0028ACB4
		public override bool Start(Living living)
		{
			PetReduceTargetBaseDamage petReduceTargetBaseDamage = living.PetEffectList.GetOfType(ePetEffectType.PetReduceTargetBaseDamage) as PetReduceTargetBaseDamage;
			bool flag = petReduceTargetBaseDamage != null;
			bool flag2;
			if (flag)
			{
				petReduceTargetBaseDamage.m_probability = ((this.m_probability > petReduceTargetBaseDamage.m_probability) ? this.m_probability : petReduceTargetBaseDamage.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079D6 RID: 31190 RVA: 0x0002DFDC File Offset: 0x0002C1DC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
			player.AfterKillingLiving += this.player_AfterKillingLiving;
		}

		// Token: 0x060079D7 RID: 31191 RVA: 0x0002E005 File Offset: 0x0002C205
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
			player.AfterKillingLiving -= this.player_AfterKillingLiving;
		}

		// Token: 0x060079D8 RID: 31192 RVA: 0x0028CB14 File Offset: 0x0028AD14
		private void player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = this.IsTrigger && living != target;
			if (flag)
			{
				this.IsTrigger = false;
				target.Game.sendShowPicSkil(target, base.ElementInfo, true);
				target.AddPetEffect(new PetReduceBaseDamageEquip(this.m_count, base.Info.ID.ToString()), 0);
			}
		}

		// Token: 0x060079D9 RID: 31193 RVA: 0x0028CB7C File Offset: 0x0028AD7C
		private void player_PlayerBuffSkillPet(Player player)
		{
			this.IsTrigger = false;
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
				player.PetEffectTrigger = true;
			}
		}

		// Token: 0x04004762 RID: 18274
		private int m_count;

		// Token: 0x04004763 RID: 18275
		private int m_probability;

		// Token: 0x04004764 RID: 18276
		private int m_currentId;
	}
}
