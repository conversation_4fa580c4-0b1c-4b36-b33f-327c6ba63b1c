﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F22 RID: 3874
	public class EvilTribe5Effect : BaseCardEffect
	{
		// Token: 0x06008402 RID: 33794 RVA: 0x002B6540 File Offset: 0x002B4740
		public EvilTribe5Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.EvilTribe5, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008403 RID: 33795 RVA: 0x002B65B0 File Offset: 0x002B47B0
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.EvilTribe5) is EvilTribe5Effect;
			return flag || base.Start(living);
		}

		// Token: 0x06008404 RID: 33796 RVA: 0x00034974 File Offset: 0x00032B74
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.ChangeProperty;
			player.PlayerAfterReset += this.ChangeProperty1;
		}

		// Token: 0x06008405 RID: 33797 RVA: 0x0003499D File Offset: 0x00032B9D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.ChangeProperty;
			player.PlayerAfterReset -= this.ChangeProperty1;
		}

		// Token: 0x06008406 RID: 33798 RVA: 0x002B65E8 File Offset: 0x002B47E8
		private void ChangeProperty(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = living.Game is PVEGame && (living.Game as PVEGame).Info.ID == 3 && (target is SimpleNpc || target is SimpleBoss);
			if (flag)
			{
				damageAmount += (damageAmount + criticalAmount) * this.m_value;
			}
		}

		// Token: 0x06008407 RID: 33799 RVA: 0x002B6648 File Offset: 0x002B4848
		private void ChangeProperty1(Player player)
		{
			bool flag = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 3;
			if (flag)
			{
				player.Game.SendMessage(player.PlayerDetail, "您激活了邪神部落5件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活邪神部落5件套卡.", 3);
			}
		}

		// Token: 0x0400525C RID: 21084
		private int m_indexValue = 0;

		// Token: 0x0400525D RID: 21085
		private int m_value = 0;

		// Token: 0x0400525E RID: 21086
		private int m_added = 0;
	}
}
