﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D6E RID: 3438
	public class PE1192 : BasePetEffect
	{
		// Token: 0x06007ABD RID: 31421 RVA: 0x00290194 File Offset: 0x0028E394
		public PE1192(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1192, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007ABE RID: 31422 RVA: 0x00290214 File Offset: 0x0028E414
		public override bool Start(Living living)
		{
			PE1192 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1192) as PE1192;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007ABF RID: 31423 RVA: 0x0002EA20 File Offset: 0x0002CC20
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007AC0 RID: 31424 RVA: 0x00290274 File Offset: 0x0028E474
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = living.PetEffects.BonusAttack != 0;
			if (!flag)
			{
				this.m_added = 100;
				List<Player> allTeamPlayers = living.Game.GetAllTeamPlayers(living);
				foreach (Player player in allTeamPlayers)
				{
					player.Attack += (double)this.m_added;
					player.PetEffects.BonusAttack += this.m_added;
					player.Game.SendPetBuff(player, base.Info, true);
				}
			}
		}

		// Token: 0x06007AC1 RID: 31425 RVA: 0x0002EA36 File Offset: 0x0002CC36
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x04004842 RID: 18498
		private int m_type = 0;

		// Token: 0x04004843 RID: 18499
		private int m_count = 0;

		// Token: 0x04004844 RID: 18500
		private int m_probability = 0;

		// Token: 0x04004845 RID: 18501
		private int m_delay = 0;

		// Token: 0x04004846 RID: 18502
		private int m_coldDown = 0;

		// Token: 0x04004847 RID: 18503
		private int m_currentId;

		// Token: 0x04004848 RID: 18504
		private int m_added = 0;
	}
}
