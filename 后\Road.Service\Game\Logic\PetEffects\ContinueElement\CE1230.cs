﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EA6 RID: 3750
	public class CE1230 : BasePetEffect
	{
		// Token: 0x06008189 RID: 33161 RVA: 0x002AD4E0 File Offset: 0x002AB6E0
		public CE1230(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1230, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600818A RID: 33162 RVA: 0x002AD560 File Offset: 0x002AB760
		public override bool Start(Living living)
		{
			CE1230 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1230) as CE1230;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600818B RID: 33163 RVA: 0x00032C74 File Offset: 0x00030E74
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600818C RID: 33164 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600818D RID: 33165 RVA: 0x002AD5C0 File Offset: 0x002AB7C0
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600818E RID: 33166 RVA: 0x00032C9D File Offset: 0x00030E9D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x040050C7 RID: 20679
		private int m_type = 0;

		// Token: 0x040050C8 RID: 20680
		private int m_count = 0;

		// Token: 0x040050C9 RID: 20681
		private int m_probability = 0;

		// Token: 0x040050CA RID: 20682
		private int m_delay = 0;

		// Token: 0x040050CB RID: 20683
		private int m_coldDown = 0;

		// Token: 0x040050CC RID: 20684
		private int m_currentId;

		// Token: 0x040050CD RID: 20685
		private int m_added = 0;
	}
}
