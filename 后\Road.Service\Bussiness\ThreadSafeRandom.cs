﻿using System;

namespace Bussiness
{
	// Token: 0x02000FBA RID: 4026
	public class ThreadSafeRandom
	{
		// Token: 0x06008A41 RID: 35393 RVA: 0x002F6548 File Offset: 0x002F4748
		public static int NextStatic()
		{
			Random random = ThreadSafeRandom.randomStatic;
			int num;
			lock (random)
			{
				num = ThreadSafeRandom.randomStatic.Next();
			}
			return num;
		}

		// Token: 0x06008A42 RID: 35394 RVA: 0x002F6594 File Offset: 0x002F4794
		public static int NextStatic(int maxValue)
		{
			Random random = ThreadSafeRandom.randomStatic;
			int num;
			lock (random)
			{
				num = ThreadSafeRandom.randomStatic.Next(maxValue);
			}
			return num;
		}

		// Token: 0x06008A43 RID: 35395 RVA: 0x002F65E0 File Offset: 0x002F47E0
		public static int NextStatic(int minValue, int maxValue)
		{
			Random random = ThreadSafeRandom.randomStatic;
			int num;
			lock (random)
			{
				num = ThreadSafeRandom.randomStatic.Next(minValue, maxValue);
			}
			return num;
		}

		// Token: 0x06008A44 RID: 35396 RVA: 0x002F662C File Offset: 0x002F482C
		public static void NextStatic(byte[] keys)
		{
			Random random = ThreadSafeRandom.randomStatic;
			lock (random)
			{
				ThreadSafeRandom.randomStatic.NextBytes(keys);
			}
		}

		// Token: 0x06008A45 RID: 35397 RVA: 0x002F6678 File Offset: 0x002F4878
		public int Next()
		{
			Random random = this.random;
			int num;
			lock (random)
			{
				num = this.random.Next();
			}
			return num;
		}

		// Token: 0x06008A46 RID: 35398 RVA: 0x002F66C4 File Offset: 0x002F48C4
		public int Next(int maxValue)
		{
			Random random = this.random;
			int num;
			lock (random)
			{
				num = this.random.Next(maxValue);
			}
			return num;
		}

		// Token: 0x06008A47 RID: 35399 RVA: 0x002F6710 File Offset: 0x002F4910
		public int Next(int minValue, int maxValue)
		{
			Random random = this.random;
			int num;
			lock (random)
			{
				num = this.random.Next(minValue, maxValue);
			}
			return num;
		}

		// Token: 0x06008A48 RID: 35400 RVA: 0x002F6760 File Offset: 0x002F4960
		public int NextSmallValue(int min, int max)
		{
			int num = Math.Abs(this.Next(min, max) - max);
			bool flag = num > max;
			if (flag)
			{
				num = max;
			}
			else
			{
				bool flag2 = num < min;
				if (flag2)
				{
					num = min;
				}
			}
			return num;
		}

		// Token: 0x040054A0 RID: 21664
		private static Random randomStatic = new Random();

		// Token: 0x040054A1 RID: 21665
		private Random random = new Random();
	}
}
