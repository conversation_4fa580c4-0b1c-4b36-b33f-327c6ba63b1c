﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D5A RID: 3418
	public class PE1076 : BasePetEffect
	{
		// Token: 0x06007A56 RID: 31318 RVA: 0x0028E6C4 File Offset: 0x0028C8C4
		public PE1076(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1076, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A57 RID: 31319 RVA: 0x0028E744 File Offset: 0x0028C944
		public override bool Start(Living living)
		{
			PE1076 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1076) as PE1076;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A58 RID: 31320 RVA: 0x0002E617 File Offset: 0x0002C817
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerShoot += this.Player_PlayerShoot;
			player.AfterPlayerShooted += this.Player_AfterPlayerShooted;
		}

		// Token: 0x06007A59 RID: 31321 RVA: 0x0002E640 File Offset: 0x0002C840
		private void Player_AfterPlayerShooted(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
		}

		// Token: 0x06007A5A RID: 31322 RVA: 0x0028E7A4 File Offset: 0x0028C9A4
		private void Player_PlayerShoot(Player player)
		{
			this.m_added = 200;
			player.Game.SendPetBuff(player, base.ElementInfo, true);
			List<Living> list = player.Game.Map.FindAllNearestEnemy(player.X, player.Y, 100.0, player);
			foreach (Living living in list)
			{
				living.SyncAtTime = true;
				living.AddBlood(-this.m_added, 1);
				living.SyncAtTime = false;
				bool flag = living.Blood <= 0;
				if (flag)
				{
					living.Die();
					bool flag2 = player != null;
					if (flag2)
					{
						if (player != null)
						{
							player.PlayerDetail.OnKillingLiving(player.Game, 2, living.Id, living.IsLiving, this.m_added);
						}
					}
				}
			}
		}

		// Token: 0x06007A5B RID: 31323 RVA: 0x0002E657 File Offset: 0x0002C857
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerShoot -= this.Player_PlayerShoot;
			player.AfterPlayerShooted -= this.Player_AfterPlayerShooted;
		}

		// Token: 0x040047B6 RID: 18358
		private int m_type = 0;

		// Token: 0x040047B7 RID: 18359
		private int m_count = 0;

		// Token: 0x040047B8 RID: 18360
		private int m_probability = 0;

		// Token: 0x040047B9 RID: 18361
		private int m_delay = 0;

		// Token: 0x040047BA RID: 18362
		private int m_coldDown = 0;

		// Token: 0x040047BB RID: 18363
		private int m_currentId;

		// Token: 0x040047BC RID: 18364
		private int m_added = 0;
	}
}
