﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D75 RID: 3445
	public class PE1223 : BasePetEffect
	{
		// Token: 0x06007ADE RID: 31454 RVA: 0x00290A84 File Offset: 0x0028EC84
		public PE1223(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1223, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007ADF RID: 31455 RVA: 0x00290B04 File Offset: 0x0028ED04
		public override bool Start(Living living)
		{
			PE1223 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1223) as PE1223;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AE0 RID: 31456 RVA: 0x0002EAFC File Offset: 0x0002CCFC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
		}

		// Token: 0x06007AE1 RID: 31457 RVA: 0x0002EB12 File Offset: 0x0002CD12
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			((Player)living).AddPetMP(2);
		}

		// Token: 0x06007AE2 RID: 31458 RVA: 0x0002EB22 File Offset: 0x0002CD22
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x04004873 RID: 18547
		private int m_type = 0;

		// Token: 0x04004874 RID: 18548
		private int m_count = 0;

		// Token: 0x04004875 RID: 18549
		private int m_probability = 0;

		// Token: 0x04004876 RID: 18550
		private int m_delay = 0;

		// Token: 0x04004877 RID: 18551
		private int m_coldDown = 0;

		// Token: 0x04004878 RID: 18552
		private int m_currentId;

		// Token: 0x04004879 RID: 18553
		private int m_added = 0;
	}
}
