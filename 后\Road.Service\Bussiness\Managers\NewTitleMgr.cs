﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FE2 RID: 4066
	public class NewTitleMgr
	{
		// Token: 0x06008B29 RID: 35625 RVA: 0x002FB048 File Offset: 0x002F9248
		public static bool ReLoad()
		{
			try
			{
				NewTitleInfo[] array = NewTitleMgr.LoadNewTitleDb();
				Dictionary<int, NewTitleInfo> dictionary = NewTitleMgr.LoadNewTitles(array);
				bool flag = array.Length != 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, NewTitleInfo>>(ref NewTitleMgr.m_NewTitles, dictionary);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = NewTitleMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					NewTitleMgr.log.Error("ReLoad NewTitle", ex);
				}
				return false;
			}
			return true;
		}

		// Token: 0x06008B2A RID: 35626 RVA: 0x002FB0C0 File Offset: 0x002F92C0
		public static bool Init()
		{
			return NewTitleMgr.ReLoad();
		}

		// Token: 0x06008B2B RID: 35627 RVA: 0x002FB0D8 File Offset: 0x002F92D8
		public static NewTitleInfo[] LoadNewTitleDb()
		{
			NewTitleInfo[] allNewTitle;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				allNewTitle = produceBussiness.GetAllNewTitle();
			}
			return allNewTitle;
		}

		// Token: 0x06008B2C RID: 35628 RVA: 0x002FB114 File Offset: 0x002F9314
		public static Dictionary<int, NewTitleInfo> LoadNewTitles(NewTitleInfo[] NewTitle)
		{
			Dictionary<int, NewTitleInfo> dictionary = new Dictionary<int, NewTitleInfo>();
			foreach (NewTitleInfo newTitleInfo in NewTitle)
			{
				bool flag = !dictionary.Keys.Contains(newTitleInfo.ID);
				if (flag)
				{
					dictionary.Add(newTitleInfo.ID, newTitleInfo);
				}
			}
			return dictionary;
		}

		// Token: 0x06008B2D RID: 35629 RVA: 0x002FB170 File Offset: 0x002F9370
		public static NewTitleInfo FindNewTitle(int ID)
		{
			NewTitleMgr.m_clientLocker.AcquireWriterLock(-1);
			try
			{
				bool flag = NewTitleMgr.m_NewTitles.ContainsKey(ID);
				if (flag)
				{
					return NewTitleMgr.m_NewTitles[ID];
				}
			}
			finally
			{
				NewTitleMgr.m_clientLocker.ReleaseWriterLock();
			}
			return null;
		}

		// Token: 0x06008B2E RID: 35630 RVA: 0x002FB1D0 File Offset: 0x002F93D0
		public static NewTitleInfo FindNewTitleByName(string Name)
		{
			NewTitleMgr.m_clientLocker.AcquireWriterLock(-1);
			try
			{
				foreach (NewTitleInfo newTitleInfo in NewTitleMgr.m_NewTitles.Values)
				{
					bool flag = newTitleInfo.Name.ToLower() == Name.ToLower();
					if (flag)
					{
						return newTitleInfo;
					}
				}
			}
			finally
			{
				NewTitleMgr.m_clientLocker.ReleaseWriterLock();
			}
			return null;
		}

		// Token: 0x04005532 RID: 21810
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005533 RID: 21811
		private static Dictionary<int, NewTitleInfo> m_NewTitles = new Dictionary<int, NewTitleInfo>();

		// Token: 0x04005534 RID: 21812
		private static Random random = new Random();

		// Token: 0x04005535 RID: 21813
		private static ReaderWriterLock m_clientLocker = new ReaderWriterLock();
	}
}
