﻿using System;
using System.Text;
using System.Xml;
using System.Xml.Linq;

namespace Bussiness
{
	// Token: 0x02000FBB RID: 4027
	public static class XmlExtends
	{
		// Token: 0x06008A4B RID: 35403 RVA: 0x002F67A0 File Offset: 0x002F49A0
		public static string ToString(this XElement node, bool check)
		{
			StringBuilder stringBuilder = new StringBuilder();
			using (XmlWriter xmlWriter = XmlWriter.Create(stringBuilder, new XmlWriterSettings
			{
				CheckCharacters = check,
				OmitXmlDeclaration = true,
				Indent = true
			}))
			{
				node.WriteTo(xmlWriter);
			}
			return stringBuilder.ToString();
		}
	}
}
