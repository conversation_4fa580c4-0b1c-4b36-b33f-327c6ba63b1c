﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EEC RID: 3820
	public class IceFronzeEquipEffect : BasePlayerEffect
	{
		// Token: 0x06008337 RID: 33591 RVA: 0x00034181 File Offset: 0x00032381
		public IceFronzeEquipEffect(int count, int probability)
			: base(eEffectType.IceFronzeEquipEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x06008338 RID: 33592 RVA: 0x002B3330 File Offset: 0x002B1530
		public override bool Start(Living living)
		{
			IceFronzeEquipEffect iceFronzeEquipEffect = living.EffectList.GetOfType(eEffectType.IceFronzeEquipEffect) as IceFronzeEquipEffect;
			bool flag = iceFronzeEquipEffect != null;
			bool flag2;
			if (flag)
			{
				iceFronzeEquipEffect.m_probability = ((this.m_probability > iceFronzeEquipEffect.m_probability) ? this.m_probability : iceFronzeEquipEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008339 RID: 33593 RVA: 0x000341A9 File Offset: 0x000323A9
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.ChangeProperty;
		}

		// Token: 0x0600833A RID: 33594 RVA: 0x000341BF File Offset: 0x000323BF
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.ChangeProperty;
		}

		// Token: 0x0600833B RID: 33595 RVA: 0x002B338C File Offset: 0x002B158C
		private void ChangeProperty(Player player, int ball)
		{
			bool flag = player.CurrentBall.ID != 1 && player.CurrentBall.ID != 3 && player.CurrentBall.ID != 5 && AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				player.AttackEffectTrigger = true;
				player.WillIceForonze = true;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("IceFronzeEquipEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x04005205 RID: 20997
		private int m_count = 0;

		// Token: 0x04005206 RID: 20998
		private int m_probability = 0;
	}
}
