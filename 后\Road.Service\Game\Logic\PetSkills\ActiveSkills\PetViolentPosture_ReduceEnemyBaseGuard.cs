﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D4E RID: 3406
	public class PetViolentPosture_ReduceEnemyBaseGuard : BasePetEffect
	{
		// Token: 0x060079FE RID: 31230 RVA: 0x0002E24F File Offset: 0x0002C44F
		public PetViolentPosture_ReduceEnemyBaseGuard(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetViolentPosture_ReduceEnemyBaseGuard, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
			this.m_count = count;
		}

		// Token: 0x060079FF RID: 31231 RVA: 0x0028D328 File Offset: 0x0028B528
		public override bool Start(Living living)
		{
			PetViolentPosture_ReduceEnemyBaseGuard petViolentPosture_ReduceEnemyBaseGuard = living.PetEffectList.GetOfType(ePetEffectType.PetViolentPosture_ReduceEnemyBaseGuard) as PetViolentPosture_ReduceEnemyBaseGuard;
			bool flag = petViolentPosture_ReduceEnemyBaseGuard != null;
			bool flag2;
			if (flag)
			{
				petViolentPosture_ReduceEnemyBaseGuard.m_probability = ((this.m_probability > petViolentPosture_ReduceEnemyBaseGuard.m_probability) ? this.m_probability : petViolentPosture_ReduceEnemyBaseGuard.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A00 RID: 31232 RVA: 0x0002E280 File Offset: 0x0002C480
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
		}

		// Token: 0x06007A01 RID: 31233 RVA: 0x0002E296 File Offset: 0x0002C496
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
		}

		// Token: 0x06007A02 RID: 31234 RVA: 0x0028D388 File Offset: 0x0028B588
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				bool flag2 = player.Game.IsPVE();
				if (flag2)
				{
					foreach (Player player2 in player.Game.GetAllPlayersSameTeam(player))
					{
						player2.AddEffect(new PetReduceBaseGuardEquip(1, base.Info.ID.ToString()), 0);
						player2.Game.SendPlayerPicture(player2, 29, true);
						player.Game.sendShowPicSkil(player, base.ElementInfo, true);
					}
				}
				else
				{
					player.AddEffect(new PetReduceBaseGuardEquip(0, base.Info.ID.ToString()), 0);
					player.Game.SendPlayerPicture(player, 29, true);
					player.Game.sendShowPicSkil(player, base.ElementInfo, true);
				}
			}
		}

		// Token: 0x04004777 RID: 18295
		private int m_probability;

		// Token: 0x04004778 RID: 18296
		private int m_currentId;

		// Token: 0x04004779 RID: 18297
		private int m_count;
	}
}
