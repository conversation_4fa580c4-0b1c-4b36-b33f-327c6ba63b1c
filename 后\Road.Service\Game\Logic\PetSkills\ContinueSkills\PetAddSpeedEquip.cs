﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D05 RID: 3333
	public class PetAddSpeedEquip : AbstractPetEffect
	{
		// Token: 0x06007876 RID: 30838 RVA: 0x002864C0 File Offset: 0x002846C0
		public PetAddSpeedEquip(int count, string elementID)
			: base(ePetEffectType.PetAddSpeedEquip, elementID)
		{
			this.m_count = count;
			if (!(elementID == "1577"))
			{
				if (!(elementID == "1613"))
				{
					if (elementID == "1618")
					{
						this.m_value = 60.0;
						this.m_percent = true;
					}
				}
				else
				{
					this.m_value = 40.0;
					this.m_percent = true;
				}
			}
			else
			{
				this.m_value = 50.0;
				this.m_percent = true;
			}
		}

		// Token: 0x06007877 RID: 30839 RVA: 0x00286558 File Offset: 0x00284758
		public override bool Start(Living living)
		{
			PetAddSpeedEquip petAddSpeedEquip = living.PetEffectList.GetOfType(ePetEffectType.PetAddSpeedEquip) as PetAddSpeedEquip;
			bool flag = petAddSpeedEquip != null;
			bool flag2;
			if (flag)
			{
				petAddSpeedEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007878 RID: 30840 RVA: 0x002865A0 File Offset: 0x002847A0
		public override void OnAttached(Living living)
		{
			bool flag = this.m_added == 0.0;
			if (flag)
			{
				bool percent = this.m_percent;
				if (percent)
				{
					this.m_added = living.Speed * this.m_value / 100.0;
				}
				else
				{
					this.m_added = this.m_value;
				}
				living.Speed += this.m_added;
			}
			living.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x06007879 RID: 30841 RVA: 0x0002CA49 File Offset: 0x0002AC49
		public override void OnRemoved(Living living)
		{
			living.Speed -= this.m_added;
			this.m_added = 0.0;
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x0600787A RID: 30842 RVA: 0x00286624 File Offset: 0x00284824
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count <= 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
		}

		// Token: 0x040046A0 RID: 18080
		private int m_count;

		// Token: 0x040046A1 RID: 18081
		private double m_value;

		// Token: 0x040046A2 RID: 18082
		private bool m_percent;

		// Token: 0x040046A3 RID: 18083
		private double m_added;
	}
}
