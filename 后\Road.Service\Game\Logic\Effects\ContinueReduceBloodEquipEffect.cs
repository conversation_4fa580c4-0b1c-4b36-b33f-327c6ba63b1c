﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EE6 RID: 3814
	public class ContinueReduceBloodEquipEffect : BasePlayerEffect
	{
		// Token: 0x0600830D RID: 33549 RVA: 0x00033F31 File Offset: 0x00032131
		public ContinueReduceBloodEquipEffect(int count, int probability)
			: base(eEffectType.ContinueReduceBloodEquipEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x0600830E RID: 33550 RVA: 0x002B29B8 File Offset: 0x002B0BB8
		public override bool Start(Living living)
		{
			ContinueReduceBloodEquipEffect continueReduceBloodEquipEffect = living.EffectList.GetOfType(eEffectType.ContinueReduceBloodEquipEffect) as ContinueReduceBloodEquipEffect;
			bool flag = continueReduceBloodEquipEffect != null;
			bool flag2;
			if (flag)
			{
				continueReduceBloodEquipEffect.m_probability = ((this.m_probability > continueReduceBloodEquipEffect.m_probability) ? this.m_probability : continueReduceBloodEquipEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600830F RID: 33551 RVA: 0x00033F59 File Offset: 0x00032159
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.ChangeProperty;
			player.AfterKillingLiving += this.player_AfterKillingLiving;
		}

		// Token: 0x06008310 RID: 33552 RVA: 0x002B2A14 File Offset: 0x002B0C14
		protected void player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				target.AddEffect(new ContinueReduceBloodEffect(this.m_count, 2, living as Player), 0);
			}
		}

		// Token: 0x06008311 RID: 33553 RVA: 0x00033F82 File Offset: 0x00032182
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.ChangeProperty;
			player.AfterKillingLiving -= this.player_AfterKillingLiving;
		}

		// Token: 0x06008312 RID: 33554 RVA: 0x002B2A48 File Offset: 0x002B0C48
		private void ChangeProperty(Player player, int ball)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				this.IsTrigger = true;
				player.AttackEffectTrigger = true;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("ContinueReduceBloodEquipEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051F7 RID: 20983
		private int m_count = 0;

		// Token: 0x040051F8 RID: 20984
		private int m_probability = 0;
	}
}
