﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using SqlDataProvider.Data;

namespace Bussiness
{
	// Token: 0x02000FA0 RID: 4000
	public class AreaBussiness : CrossBussiness
	{
		// Token: 0x0600880C RID: 34828 RVA: 0x002C9B64 File Offset: 0x002C7D64
		public AreaConfigInfo[] GetAllAreaConfig()
		{
			List<AreaConfigInfo> list = new List<AreaConfigInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_AreaConfig_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitAreaConfigInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = CrossBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					CrossBussiness.log.Error("InitAreaConfigInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x0600880D RID: 34829 RVA: 0x002C9C20 File Offset: 0x002C7E20
		public AreaConfigInfo InitAreaConfigInfo(SqlDataReader dr)
		{
			return new AreaConfigInfo
			{
				AreaID = (int)dr["AreaID"],
				AreaServer = ((dr["AreaServer"] == null) ? "" : dr["AreaServer"].ToString()),
				AreaName = ((dr["AreaName"] == null) ? "" : dr["AreaName"].ToString()),
				DataSource = ((dr["DataSource"] == null) ? "" : dr["DataSource"].ToString()),
				Catalog = ((dr["Catalog"] == null) ? "" : dr["Catalog"].ToString()),
				UserID = ((dr["UserID"] == null) ? "" : dr["UserID"].ToString()),
				Password = ((dr["Password"] == null) ? "" : dr["Password"].ToString()),
				RequestUrl = ((dr["RequestUrl"] == null) ? "" : dr["RequestUrl"].ToString()),
				CrossChatAllow = (bool)dr["CrossChatAllow"],
				CrossPrivateChat = (bool)dr["CrossPrivateChat"],
				Version = ((dr["Version"] == null) ? "" : dr["Version"].ToString())
			};
		}
	}
}
