﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using Bussiness;
using Game.Base.Packets;
using Game.Logic.Actions;
using Game.Logic.Phy.Maps;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000CAD RID: 3245
	public class PVPGame : BaseGame
	{
		// Token: 0x17001405 RID: 5125
		// (get) Token: 0x060074C2 RID: 29890 RVA: 0x0002B34E File Offset: 0x0002954E
		public Player CurrentPlayer
		{
			get
			{
				return this.m_currentLiving as Player;
			}
		}

		// Token: 0x060074C3 RID: 29891 RVA: 0x0026B58C File Offset: 0x0026978C
		public PVPGame(int id, int roomId, List<IGamePlayer> red, List<IGamePlayer> blue, Map map, eRoomType roomType, eGameType gameType, int timeType)
			: base(id, roomId, map, roomType, gameType, timeType)
		{
			this.m_redTeam = new List<Player>();
			this.m_blueTeam = new List<Player>();
			StringBuilder stringBuilder = new StringBuilder();
			this.m_redAvgLevel = 0f;
			this.m_redTeamIndex = 1;
			foreach (IGamePlayer gamePlayer in red)
			{
				IGamePlayer gamePlayer2 = gamePlayer;
				int num = this.PhysicalId;
				this.PhysicalId = num + 1;
				Player player = new Player(gamePlayer2, num, this, this.m_redTeamIndex, gamePlayer.PlayerCharacter.Blood);
				stringBuilder.Append(gamePlayer.PlayerCharacter.ID).Append(",");
				player.Reset();
				player.Direction = ((this.m_random.Next(0, 1) == 0) ? 1 : (-1));
				base.AddPlayer(player);
				this.m_redTeam.Add(player);
				this.m_redAvgLevel += (float)gamePlayer.PlayerCharacter.Grade;
				bool flag = !this.FrozenWind && gamePlayer.PlayerCharacter.Grade <= 9;
				if (flag)
				{
					this.FrozenWind = true;
				}
			}
			this.m_redAvgLevel /= (float)this.m_redTeam.Count;
			this.teamAStr = stringBuilder.ToString();
			StringBuilder stringBuilder2 = new StringBuilder();
			this.m_blueAvgLevel = 0f;
			this.m_blueTeamIndex = 2;
			foreach (IGamePlayer gamePlayer3 in blue)
			{
				IGamePlayer gamePlayer4 = gamePlayer3;
				int num = this.PhysicalId;
				this.PhysicalId = num + 1;
				Player player2 = new Player(gamePlayer4, num, this, this.m_blueTeamIndex, gamePlayer3.PlayerCharacter.Blood);
				stringBuilder2.Append(gamePlayer3.PlayerCharacter.ID).Append(",");
				player2.Reset();
				player2.Direction = ((this.m_random.Next(0, 1) == 0) ? 1 : (-1));
				base.AddPlayer(player2);
				this.m_blueTeam.Add(player2);
				this.m_blueAvgLevel += (float)gamePlayer3.PlayerCharacter.Grade;
				bool flag2 = !this.FrozenWind && gamePlayer3.PlayerCharacter.Grade <= 9;
				if (flag2)
				{
					this.FrozenWind = true;
				}
			}
			this.m_blueAvgLevel /= (float)blue.Count;
			this.teamBStr = stringBuilder2.ToString();
			this.BeginPlayerCount = this.m_redTeam.Count + this.m_blueTeam.Count;
			this.beginTime = DateTime.Now;
		}

		// Token: 0x060074C4 RID: 29892 RVA: 0x0026B880 File Offset: 0x00269A80
		public void Prepare()
		{
			bool flag = base.GameState == eGameState.Inited;
			if (flag)
			{
				base.SendCreateGame();
				this.m_gameState = eGameState.Prepared;
				this.CheckState(0);
			}
		}

		// Token: 0x060074C5 RID: 29893 RVA: 0x0026B8B4 File Offset: 0x00269AB4
		internal void SendTempStyle()
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(134);
			List<Player> allFightPlayers = base.GetAllFightPlayers();
			gspacketIn.WriteInt(allFightPlayers.Count);
			foreach (Player player in allFightPlayers)
			{
				IGamePlayer playerDetail = player.PlayerDetail;
				string style = playerDetail.PlayerCharacter.Style;
				gspacketIn.WriteString(style);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Hide);
				gspacketIn.WriteBoolean(playerDetail.PlayerCharacter.Sex);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Skin);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Colors);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ID);
			}
		}

		// Token: 0x060074C6 RID: 29894 RVA: 0x0026B9AC File Offset: 0x00269BAC
		public void StartLoading()
		{
			bool flag = base.GameState == eGameState.Prepared;
			if (flag)
			{
				base.ClearWaitTimer();
				base.SendStartLoading(60);
				base.VaneLoading(null);
				this.SendTempStyle();
				base.AddAction(new WaitPlayerLoadingAction(this, 61000));
				this.m_gameState = eGameState.Loading;
			}
		}

		// Token: 0x060074C7 RID: 29895 RVA: 0x0026BA04 File Offset: 0x00269C04
		public void StartGame()
		{
			bool flag = base.GameState != eGameState.Loading;
			if (!flag)
			{
				this.m_gameState = eGameState.Playing;
				base.ClearWaitTimer();
				base.SendSyncLifeTime();
				List<Player> allFightPlayers = base.GetAllFightPlayers();
				MapPoint mapRandomPos = MapMgr.GetMapRandomPos(this.m_map.Info.ID);
				GSPacketIn gspacketIn = new GSPacketIn(91);
				gspacketIn.WriteByte(99);
				gspacketIn.WriteInt(allFightPlayers.Count);
				foreach (Player player in allFightPlayers)
				{
					player.Reset();
					Point playerPoint = base.GetPlayerPoint(mapRandomPos, player.Team);
					player.SetXY(playerPoint);
					this.m_map.AddPhysical(player);
					player.StartMoving();
					player.StartGame();
					player.OnGameStarted();
					gspacketIn.WriteInt(player.Id);
					gspacketIn.WriteInt(player.X);
					gspacketIn.WriteInt(player.Y);
					gspacketIn.WriteInt(player.Direction);
					gspacketIn.WriteInt(player.MaxBlood);
					gspacketIn.WriteInt(player.Team);
					gspacketIn.WriteInt(player.Weapon.RefineryLevel);
					gspacketIn.WriteInt(player.PowerRatio);
					gspacketIn.WriteInt(player.Dander);
					gspacketIn.WriteInt(player.PlayerDetail.FightBuffs.Count);
					foreach (UserBufferInfo userBufferInfo in player.PlayerDetail.FightBuffs)
					{
						gspacketIn.WriteInt(userBufferInfo.Type);
						gspacketIn.WriteInt(userBufferInfo.Value);
					}
				}
				this.SendToAll(gspacketIn);
				base.WaitTime(allFightPlayers.Count * 1000);
				base.OnGameStarted();
			}
		}

		// Token: 0x060074C8 RID: 29896 RVA: 0x0026BC40 File Offset: 0x00269E40
		public void NextTurn()
		{
			bool flag = base.GameState != eGameState.Playing;
			if (!flag)
			{
				base.ClearWaitTimer();
				base.ClearDiedPhysicals();
				base.CheckBox();
				int turnIndex = base.m_turnIndex;
				base.m_turnIndex = turnIndex + 1;
				List<SimpleBox> list = base.CreateBox();
				List<Physics> allPhysicalSafe = this.m_map.GetAllPhysicalSafe();
				foreach (Physics physics in allPhysicalSafe)
				{
					physics.PrepareNewTurn();
				}
				this.LastTurnLiving = this.m_currentLiving;
				this.m_currentLiving = base.FindNextTurnedLiving(true);
				this.m_currentLiving.VaneOpen = true;
				bool vaneOpen = this.m_currentLiving.VaneOpen;
				if (vaneOpen)
				{
					base.UpdateWind(base.GetNextWind(), true);
				}
				bool flag2 = this.m_currentLiving != this.LastTurnLiving && this.LastTurnLiving is Player && (this.LastTurnLiving as Player).IsAddTurn;
				if (flag2)
				{
					this.m_currentLiving = this.LastTurnLiving;
					(this.LastTurnLiving as Player).IsAddTurn = false;
				}
				this.MinusDelays(this.m_currentLiving.Delay);
				this.m_currentLiving.PrepareSelfTurn();
				bool flag3 = !base.CurrentLiving.IsFrost && this.m_currentLiving.IsLiving;
				if (flag3)
				{
					this.m_currentLiving.StartAttacking();
					base.SendGameNextTurn(this.m_currentLiving, this, list);
					bool isAttacking = this.m_currentLiving.IsAttacking;
					if (isAttacking)
					{
						base.AddAction(new WaitLivingAttackingAction(this.m_currentLiving, base.m_turnIndex, (base.getTurnTime() + 20) * 1000));
					}
				}
				base.OnBeginNewTurn();
			}
		}

		// Token: 0x060074C9 RID: 29897 RVA: 0x00268EB8 File Offset: 0x002670B8
		public override bool TakeCard(Player player, bool isSysTake)
		{
			int num = 0;
			for (int i = 0; i < this.Cards.Length; i++)
			{
				bool flag = this.Cards[i] == 0;
				if (flag)
				{
					num = i;
					break;
				}
			}
			return this.TakeCard(player, num, isSysTake);
		}

		// Token: 0x060074CA RID: 29898 RVA: 0x0026BE1C File Offset: 0x0026A01C
		public override bool TakeCard(Player player, int index, bool isSysTake)
		{
			bool flag = player.CanTakeOut == 0;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = !player.IsActive || index < 0 || index > this.Cards.Length || player.FinishTakeCard || this.Cards[index] > 0;
				if (flag3)
				{
					flag2 = false;
				}
				else
				{
					List<ItemInfo> list = null;
					int num = 0;
					int num2 = 0;
					int num3 = 0;
					int num4 = 0;
					int num5 = 0;
					bool flag4 = DropInventory.CardDrop(base.RoomType, ref list) && list != null;
					if (flag4)
					{
						foreach (ItemInfo itemInfo in list)
						{
							num = itemInfo.Template.TemplateID;
							ItemInfo itemInfo2 = ItemInfo.FindSpecialItemInfo(itemInfo, ref num3, ref num4, ref num5);
							bool flag5 = itemInfo2 != null && num > 0;
							if (flag5)
							{
								player.PlayerDetail.AddTemplate(itemInfo2, eBageType.TempBag, itemInfo.Count);
							}
						}
					}
					player.FinishTakeCard = true;
					this.Cards[index] = 1;
					int num6 = num;
					int num7 = num6;
					if (num7 <= -200)
					{
						if (num7 != -300)
						{
							if (num7 == -200)
							{
								num2 = num4;
							}
						}
						else
						{
							num2 = num5;
						}
					}
					else if (num7 != -100)
					{
						if (num7 == 0)
						{
							num = -100;
							num3 = 100;
							num2 = 2000;
						}
					}
					else
					{
						num2 = num3;
					}
					player.PlayerDetail.AddGold(num3);
					player.PlayerDetail.AddMoney(num4);
					player.PlayerDetail.AddGiftToken(num5);
					base.SendGamePlayerTakeCard(player, index, num, num2, isSysTake);
					flag2 = true;
				}
			}
			return flag2;
		}

		// Token: 0x060074CB RID: 29899 RVA: 0x0026BFCC File Offset: 0x0026A1CC
		private int GetWinTeam(List<Player> players)
		{
			int num = -1;
			bool flag = this.m_roomType == eRoomType.Score || this.m_roomType == eRoomType.Rank;
			if (flag)
			{
				bool flag2 = false;
				int num2 = 0;
				int num3 = 0;
				int num4 = -1;
				foreach (Player player in players)
				{
					bool flag3 = !player.IsLiving;
					if (flag3)
					{
						flag2 = true;
						break;
					}
					bool flag4 = player.Team == 1;
					if (flag4)
					{
						num2 += player.TotalDamagePlayer;
					}
					else
					{
						num3 += player.TotalDamagePlayer;
						num4 = player.Team;
					}
				}
				bool flag5 = !flag2;
				if (flag5)
				{
					return (num2 > num3) ? 1 : num4;
				}
			}
			foreach (Player player2 in players)
			{
				bool isLiving = player2.IsLiving;
				if (isLiving)
				{
					num = player2.Team;
					break;
				}
			}
			bool flag6 = num == -1;
			if (flag6)
			{
				bool flag7 = this.CurrentPlayer != null;
				if (flag7)
				{
					return this.CurrentPlayer.Team;
				}
				bool flag8 = base.CurrentLiving != null;
				if (flag8)
				{
					num = base.CurrentLiving.Team;
				}
			}
			return num;
		}

		// Token: 0x060074CC RID: 29900 RVA: 0x0026C14C File Offset: 0x0026A34C
		public void GameOver()
		{
			bool flag = base.GameState != eGameState.Playing;
			if (!flag)
			{
				this.m_gameState = eGameState.GameOver;
				base.ClearWaitTimer();
				this.CurrentTurnTotalDamage = 0;
				List<Player> allFightPlayers = base.GetAllFightPlayers();
				int winTeam = this.GetWinTeam(allFightPlayers);
				int num = 0;
				int num2 = 0;
				GSPacketIn gspacketIn = new GSPacketIn(91);
				gspacketIn.WriteByte(100);
				gspacketIn.WriteInt(base.PlayerCount);
				foreach (Player player in allFightPlayers)
				{
					bool flag2 = player.TotalHurt > 0;
					if (flag2)
					{
						bool flag3 = player.Team == 1;
						if (flag3)
						{
							num2 = 1;
						}
						else
						{
							num = 1;
						}
					}
					player.LoadingProcess = 0;
					bool currentIsHitTarget = player.CurrentIsHitTarget;
					if (currentIsHitTarget)
					{
						player.TotalHitTargetCount++;
					}
					player.TotalShootCount = ((player.TotalShootCount == 0) ? 1 : player.TotalShootCount);
					bool flag4 = player.TotalShootCount < player.TotalHitTargetCount;
					if (flag4)
					{
						player.TotalShootCount = player.TotalHitTargetCount;
					}
					bool isAutoBot = player.PlayerDetail.PlayerCharacter.IsAutoBot;
					if (isAutoBot)
					{
						player.CanTakeOut = 0;
					}
					player.CanTakeOut = ((player.Team == 1) ? num2 : num);
					float num3 = ((player.Team == 1) ? this.m_blueAvgLevel : this.m_redAvgLevel);
					float num4 = (float)((player.Team == 1) ? this.m_blueTeam.Count : this.m_redTeam.Count);
					float num5 = (float)((player.Team == winTeam) ? 2 : 0);
					double num6 = 0.0;
					bool flag5 = num3 - (float)player.PlayerDetail.PlayerCharacter.Grade >= 5f && this.TotalHurt > 0;
					if (flag5)
					{
						base.SendMessage(player.PlayerDetail, LanguageMgr.GetTranslation("GetGPreward", Array.Empty<object>()), null, 2);
						num6 = 200.0;
					}
					int num7 = (int)((player.Team == 1) ? ((float)this.m_blueTeam.Count * this.m_blueAvgLevel * 300f) : (this.m_redAvgLevel * (float)this.m_redTeam.Count * 300f));
					int num8 = ((player.TotalHurt > num7) ? num7 : player.TotalHurt);
					double num9 = Math.Ceiling((double)num8 * 0.01);
					double num10 = Math.Ceiling((double)player.TotalKill * 0.5);
					double num11 = Math.Ceiling((double)(num5 + (float)(player.TotalHitTargetCount / player.TotalShootCount * 2)));
					double num12 = 0.0;
					double num13 = Math.Ceiling((num9 + num10 + num11 + num12) * (double)num3 * (0.9 + (double)(num4 - 1f) * 0.3));
					double num14 = ((player.PlayerDetail.PlayerCharacter.TypeVIP > 0) ? Math.Ceiling(num13 * GameProperties.VIPGpBonusRatePerLevel) : 0.0);
					double num15 = ((this.m_gameType == eGameType.Guild || this.m_gameType == eGameType.GuildLeage) ? Math.Ceiling(num13 * GameProperties.ConsortiaFightRate) : 0.0);
					double num16 = Math.Ceiling(num13 * player.PlayerDetail.GPSpouseTeam);
					double num17 = Math.Ceiling(num13 * (double)player.PlayerDetail.GMExperienceRate);
					double num18 = Math.Ceiling(num13 * player.PlayerDetail.GPApprenticeOnline);
					double num19 = Math.Ceiling(num13 * player.PlayerDetail.GPApprenticeTeam);
					double num20 = Math.Ceiling(num13 * player.PlayerDetail.GPPlusRate);
					double num21 = 0.0;
					double num22 = ((player.FightBuffers.ConsortionAddPercentGoldOrGP > 0) ? Math.Ceiling(num13 * (double)player.FightBuffers.ConsortionAddPercentGoldOrGP / 100.0) : 0.0);
					double num23 = 0.0;
					double num24 = num13 + num14 + num15 + num16 + num17 + num18 + num19 + num20 + num21 + num22 + num23 + num6;
					int num25 = 0;
					bool flag6 = base.GameType == eGameType.Guild || base.GameType == eGameType.GuildLeage;
					if (flag6)
					{
						num25 = ((player.Team != winTeam) ? ((int)((double)num4 * 0.5)) : ((int)num4));
					}
					double num26 = (double)(player.GainOffer + num25);
					double num27 = Math.Ceiling(num26 * player.PlayerDetail.OfferPlusRate);
					double num28 = Math.Ceiling(num26 * GameProperties.VIPOfferDecreaseRate);
					double num29 = Math.Ceiling(num26 * (double)player.PlayerDetail.GMOfferRate);
					double num30 = ((player.FightBuffers.ConsortionAddOfferRate > 0) ? Math.Ceiling(num26 * (double)player.FightBuffers.ConsortionAddOfferRate / 100.0) : 0.0);
					double num31 = ((this.m_gameType == eGameType.Guild || this.m_gameType == eGameType.GuildLeage) ? Math.Ceiling(num26 * GameProperties.ConsortiaFightRate) : 0.0);
					double num32 = 0.0;
					double num33 = num26 + num27 + num28 + num29 + num30 + num31 + num32;
					bool flag7 = player.Team == winTeam;
					int num34 = this.CalculateLeageScore(player, winTeam);
					bool flag8 = base.GameType == eGameType.Leage || base.GameType == eGameType.GuildLeage;
					if (flag8)
					{
						player.PlayerDetail.AddLeageScore(flag7, num34);
					}
					bool flag9 = base.RoomType == eRoomType.Score;
					if (flag9)
					{
						bool flag10 = flag7;
						if (flag10)
						{
							player.PlayerDetail.AddEliteScore(10);
							player.PlayerDetail.SendMessage(LanguageMgr.GetTranslation("GameServer.EliteGame.Match.Success", Array.Empty<object>()));
						}
						else
						{
							player.PlayerDetail.RemoveEliteScore(10);
							player.PlayerDetail.SendMessage(LanguageMgr.GetTranslation("GameServer.EliteGame.Match.Fail", Array.Empty<object>()));
						}
					}
					bool flag11 = base.RoomType == eRoomType.Rank;
					if (flag11)
					{
						bool flag12 = flag7;
						if (flag12)
						{
							player.PlayerDetail.SendWinEliteChampion(player.Blood, flag7 ? 1 : 0);
							player.PlayerDetail.SendMessage(LanguageMgr.GetTranslation("GameServer.EliteGame.ChampionMatch.Win", Array.Empty<object>()));
						}
						else
						{
							player.PlayerDetail.SendMessage(LanguageMgr.GetTranslation("GameServer.EliteGame.ChampionMatch.Lost", Array.Empty<object>()));
						}
					}
					int num35 = this.m_random.Next(100, 200);
					bool flag13 = base.RoomType == eRoomType.Match && flag7;
					if (flag13)
					{
						player.PlayerDetail.AddMoney(num35);
						player.PlayerDetail.SendMessage("竞技获胜，您获得了" + num35.ToString() + "点券！");
					}
					player.PlayerDetail.AddGpDirect((int)num24);
					player.PlayerDetail.AddOfferDirect((int)num33);
					gspacketIn.WriteInt(player.Id);
					gspacketIn.WriteBoolean(player.Team == winTeam);
					gspacketIn.WriteInt(player.PlayerDetail.PlayerCharacter.Grade);
					gspacketIn.WriteInt(player.PlayerDetail.PlayerCharacter.GP);
					gspacketIn.WriteInt((int)num9);
					gspacketIn.WriteInt((int)num10);
					gspacketIn.WriteInt((int)num11);
					gspacketIn.WriteInt((int)num12);
					gspacketIn.WriteInt((int)num14);
					gspacketIn.WriteInt((int)num15);
					gspacketIn.WriteInt((int)num16);
					gspacketIn.WriteInt((int)num17);
					gspacketIn.WriteInt((int)num18);
					gspacketIn.WriteInt((int)num19);
					gspacketIn.WriteInt((int)num20);
					gspacketIn.WriteInt((int)num21);
					gspacketIn.WriteInt((int)num22);
					gspacketIn.WriteInt((int)num23);
					gspacketIn.WriteInt((int)num24);
					gspacketIn.WriteInt((int)num26);
					gspacketIn.WriteInt((int)num27);
					gspacketIn.WriteInt((int)num28);
					gspacketIn.WriteInt((int)num29);
					gspacketIn.WriteInt((int)num30);
					gspacketIn.WriteInt((int)num31);
					gspacketIn.WriteInt((int)num32);
					gspacketIn.WriteInt((int)num33);
					gspacketIn.WriteInt(player.CanTakeOut);
				}
				int num36 = this.CalculateGuildMatchResult(allFightPlayers, winTeam);
				gspacketIn.WriteInt(num36);
				this.SendToAll(gspacketIn);
				foreach (Player player2 in allFightPlayers)
				{
					player2.PlayerDetail.OnGameOver(this, player2.Team == winTeam, player2.GainGP, player2.Blood, base.CountPlayersTeam(player2.Team), this.BeginPlayerCount, player2.PlayerDetail.GPApprenticeTeam > 0.0, player2.PlayerDetail.GPSpouseTeam > 0.0, false);
				}
				base.OnGameOverLog(base.RoomId, base.RoomType, base.GameType, 0, this.beginTime, DateTime.Now, this.BeginPlayerCount, base.Map.Info.ID, this.teamAStr, this.teamBStr, "", winTeam, this.BossWarField);
				base.WaitTime(20000);
				base.OnGameOverred();
			}
		}

		// Token: 0x060074CD RID: 29901 RVA: 0x0026CAFC File Offset: 0x0026ACFC
		public override void Stop()
		{
			bool flag = base.GameState != eGameState.GameOver;
			if (!flag)
			{
				this.m_gameState = eGameState.Stopped;
				this.SendTempStyle();
				List<Player> allFightPlayers = base.GetAllFightPlayers();
				foreach (Player player in allFightPlayers)
				{
					bool flag2 = player.IsActive && !player.FinishTakeCard;
					if (flag2)
					{
						int canTakeOut = player.CanTakeOut;
						for (int i = 0; i < canTakeOut; i++)
						{
							this.TakeCard(player, true);
						}
					}
				}
				Dictionary<int, Player> players = this.m_players;
				lock (players)
				{
					this.m_players.Clear();
				}
				base.Stop();
			}
		}

		// Token: 0x060074CE RID: 29902 RVA: 0x0026CC00 File Offset: 0x0026AE00
		public Player[] GetSameTeamPlayer(Player player)
		{
			List<Player> list = new List<Player>();
			foreach (Player player2 in base.GetAllFightPlayers())
			{
				bool flag = player2 != player && player2.Team == player.Team;
				if (flag)
				{
					list.Add(player2);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060074CF RID: 29903 RVA: 0x0026CC88 File Offset: 0x0026AE88
		public int GainCoupleGp(Player player, int gp)
		{
			Player[] sameTeamPlayer = this.GetSameTeamPlayer(player);
			Player[] array = sameTeamPlayer;
			Player[] array2 = array;
			foreach (Player player2 in array2)
			{
				bool flag = player2.PlayerDetail.PlayerCharacter.SpouseID == player.PlayerDetail.PlayerCharacter.SpouseID;
				if (flag)
				{
					return (int)((double)gp * 1.2);
				}
			}
			return gp;
		}

		// Token: 0x060074D0 RID: 29904 RVA: 0x0026CD04 File Offset: 0x0026AF04
		private int CalculateLeageScore(Player player, int winTeam)
		{
			int num = 1;
			bool flag = this.m_roomType == eRoomType.Match;
			if (flag)
			{
				num = ((player.Team != winTeam) ? base.Random.Next(50, 100) : base.Random.Next(150, 300));
			}
			return (num < 1) ? 1 : num;
		}

		// Token: 0x060074D1 RID: 29905 RVA: 0x0026CD60 File Offset: 0x0026AF60
		private int CalculateGuildMatchResult(List<Player> players, int winTeam)
		{
			bool flag = base.RoomType == eRoomType.Match && (base.GameType == eGameType.Guild || base.GameType == eGameType.GuildLeage);
			if (flag)
			{
				StringBuilder stringBuilder = new StringBuilder(LanguageMgr.GetTranslation("Game.Server.SceneGames.OnStopping.Msg5", Array.Empty<object>()));
				StringBuilder stringBuilder2 = new StringBuilder(LanguageMgr.GetTranslation("Game.Server.SceneGames.OnStopping.Msg5", Array.Empty<object>()));
				IGamePlayer gamePlayer = null;
				IGamePlayer gamePlayer2 = null;
				int num = 0;
				foreach (Player player in players)
				{
					bool flag2 = player.Team == winTeam;
					if (flag2)
					{
						stringBuilder.Append("[" + player.PlayerDetail.PlayerCharacter.NickName + "]");
						gamePlayer = player.PlayerDetail;
					}
					else
					{
						stringBuilder2.Append(player.PlayerDetail.PlayerCharacter.NickName ?? "");
						gamePlayer2 = player.PlayerDetail;
						num++;
					}
				}
				bool flag3 = gamePlayer2 != null;
				if (flag3)
				{
					stringBuilder.Append(LanguageMgr.GetTranslation("Game.Server.SceneGames.OnStopping.Msg1", Array.Empty<object>()) + gamePlayer2.PlayerCharacter.ConsortiaName + LanguageMgr.GetTranslation("Game.Server.SceneGames.OnStopping.Msg2", Array.Empty<object>()));
					stringBuilder2.Append(LanguageMgr.GetTranslation("Game.Server.SceneGames.OnStopping.Msg3", Array.Empty<object>()) + gamePlayer.PlayerCharacter.ConsortiaName + LanguageMgr.GetTranslation("Game.Server.SceneGames.OnStopping.Msg4", Array.Empty<object>()));
					int num2 = (num + this.TotalHurt / 2000) * (int)gamePlayer.GMRichesRate;
					gamePlayer.ConsortiaFight(gamePlayer.PlayerCharacter.ConsortiaID, gamePlayer2.PlayerCharacter.ConsortiaID, base.Players, base.RoomType, base.GameType, this.TotalHurt, players.Count);
					bool flag4 = gamePlayer.ServerID != gamePlayer2.ServerID;
					if (flag4)
					{
						gamePlayer2.ConsortiaFight(gamePlayer.PlayerCharacter.ConsortiaID, gamePlayer2.PlayerCharacter.ConsortiaID, base.Players, base.RoomType, base.GameType, this.TotalHurt, players.Count);
					}
					gamePlayer.SendConsortiaFight(gamePlayer.PlayerCharacter.ConsortiaID, num2, stringBuilder.ToString());
					foreach (Player player2 in players)
					{
						bool flag5 = player2.Team == winTeam;
						if (flag5)
						{
							player2.PlayerDetail.AddRobRiches(num2);
						}
					}
					return num2;
				}
			}
			return 0;
		}

		// Token: 0x060074D2 RID: 29906 RVA: 0x0026D030 File Offset: 0x0026B230
		public bool CanGameOver()
		{
			bool flag = base.RoomType == eRoomType.Score && this.beginTime.AddSeconds(300.0) <= DateTime.Now;
			bool flag2;
			if (flag)
			{
				flag2 = true;
			}
			else
			{
				bool flag3 = base.RoomType == eRoomType.Rank && this.beginTime.AddSeconds(600.0) <= DateTime.Now;
				if (flag3)
				{
					flag2 = true;
				}
				else
				{
					bool flag4 = true;
					bool flag5 = true;
					foreach (Player player in this.m_redTeam)
					{
						bool isLiving = player.IsLiving;
						if (isLiving)
						{
							flag4 = false;
							break;
						}
					}
					foreach (Player player2 in this.m_blueTeam)
					{
						bool isLiving2 = player2.IsLiving;
						if (isLiving2)
						{
							flag5 = false;
							break;
						}
					}
					flag2 = flag4 || flag5;
				}
			}
			return flag2;
		}

		// Token: 0x060074D3 RID: 29907 RVA: 0x0026D164 File Offset: 0x0026B364
		public override Player RemovePlayer(IGamePlayer gp, bool IsKick)
		{
			Player player = base.RemovePlayer(gp, IsKick);
			bool flag = base.RoomType == eRoomType.Score && player == null;
			if (flag)
			{
				using (PlayerBussiness playerBussiness = new PlayerBussiness())
				{
					PlayerInfo userSingleByUserID = playerBussiness.GetUserSingleByUserID(gp.PlayerCharacter.ID);
					bool flag2 = userSingleByUserID.EliteScore >= 50;
					if (flag2)
					{
						userSingleByUserID.EliteScore -= 50;
						playerBussiness.UpdatePlayer(userSingleByUserID);
					}
				}
			}
			bool flag3 = base.RoomType == eRoomType.Rank;
			if (flag3)
			{
				List<Player> allEnemyPlayers = base.GetAllEnemyPlayers(player);
				bool flag4 = allEnemyPlayers.Count == 1 && allEnemyPlayers[0].IsLiving && player.IsLiving;
				if (flag4)
				{
					bool flag5 = allEnemyPlayers[0].TotalDamageForMatch > player.TotalDamageForMatch;
					if (flag5)
					{
						allEnemyPlayers[0].PlayerDetail.SendMessage(string.Format("玩家: {0}, 战斗中造成伤害: {1} |胜利| 玩家: {2}, 战斗中造成伤害: {3}", new object[]
						{
							allEnemyPlayers[0].PlayerDetail.PlayerCharacter.NickName,
							allEnemyPlayers[0].TotalDamageForMatch,
							player.PlayerDetail.PlayerCharacter.NickName,
							player.TotalDamageForMatch
						}));
						allEnemyPlayers[0].PlayerDetail.SendWinEliteChampion(allEnemyPlayers[0].Blood, 1);
					}
					else
					{
						player.PlayerDetail.SendMessage(string.Format("玩家: {0}, 战斗中造成伤害: {1} |胜利| 玩家: {2}, 战斗中造成伤害: {3} 胜利. ", new object[]
						{
							player.PlayerDetail.PlayerCharacter.NickName,
							player.TotalDamageForMatch,
							allEnemyPlayers[0].PlayerDetail.PlayerCharacter.NickName,
							allEnemyPlayers[0].TotalDamageForMatch
						}));
						player.PlayerDetail.SendWinEliteChampion(player.Blood, 1);
					}
				}
			}
			bool flag6 = player != null && player.IsLiving && base.GameState != eGameState.Loading;
			if (flag6)
			{
				gp.RemoveGP(gp.PlayerCharacter.Grade * 12);
				string text = null;
				string text2 = null;
				bool flag7 = base.RoomType == eRoomType.Match;
				if (flag7)
				{
					bool flag8 = base.GameType == eGameType.Guild || base.GameType == eGameType.GuildLeage;
					if (flag8)
					{
						text = LanguageMgr.GetTranslation("AbstractPacketLib.SendGamePlayerLeave.Msg6", new object[]
						{
							gp.PlayerCharacter.Grade * 12,
							15
						});
						gp.RemoveOffer(15);
						text2 = LanguageMgr.GetTranslation("AbstractPacketLib.SendGamePlayerLeave.Msg7", new object[]
						{
							gp.PlayerCharacter.NickName,
							gp.PlayerCharacter.Grade * 12,
							15
						});
					}
					else
					{
						bool flag9 = base.GameType == eGameType.Free || base.GameType == eGameType.Leage;
						if (flag9)
						{
							text = LanguageMgr.GetTranslation("AbstractPacketLib.SendGamePlayerLeave.Msg6", new object[]
							{
								gp.PlayerCharacter.Grade * 12,
								5
							});
							gp.RemoveOffer(5);
							text2 = LanguageMgr.GetTranslation("AbstractPacketLib.SendGamePlayerLeave.Msg7", new object[]
							{
								gp.PlayerCharacter.NickName,
								gp.PlayerCharacter.Grade * 12,
								5
							});
						}
					}
				}
				else
				{
					text = LanguageMgr.GetTranslation("AbstractPacketLib.SendGamePlayerLeave.Msg4", new object[] { gp.PlayerCharacter.Grade * 12 });
					text2 = LanguageMgr.GetTranslation("AbstractPacketLib.SendGamePlayerLeave.Msg5", new object[]
					{
						gp.PlayerCharacter.NickName,
						gp.PlayerCharacter.Grade * 12
					});
				}
				bool flag10 = !string.IsNullOrEmpty(text) && !string.IsNullOrEmpty(text2);
				if (flag10)
				{
					base.SendMessage(gp, text, text2, 3);
				}
				bool flag11 = base.GetSameTeam() && base.CurrentLiving != null;
				if (flag11)
				{
					base.CurrentLiving.StopAttacking();
					this.CheckState(0);
				}
			}
			return player;
		}

		// Token: 0x060074D4 RID: 29908 RVA: 0x0002B525 File Offset: 0x00029725
		public override void CheckState(int delay)
		{
			base.AddAction(new CheckPVPGameStateAction(delay));
		}

		// Token: 0x04004449 RID: 17481
		private List<Player> m_redTeam;

		// Token: 0x0400444A RID: 17482
		private float m_redAvgLevel;

		// Token: 0x0400444B RID: 17483
		private List<Player> m_blueTeam;

		// Token: 0x0400444C RID: 17484
		private float m_blueAvgLevel;

		// Token: 0x0400444D RID: 17485
		private int BeginPlayerCount;

		// Token: 0x0400444E RID: 17486
		private int m_redTeamIndex;

		// Token: 0x0400444F RID: 17487
		private int m_blueTeamIndex;

		// Token: 0x04004450 RID: 17488
		private string teamAStr;

		// Token: 0x04004451 RID: 17489
		private string teamBStr;

		// Token: 0x04004452 RID: 17490
		private DateTime beginTime;
	}
}
