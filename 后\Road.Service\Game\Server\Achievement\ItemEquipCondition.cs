﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C6D RID: 3181
	public class ItemEquipCondition : BaseCondition
	{
		// Token: 0x060070AA RID: 28842 RVA: 0x0002A64E File Offset: 0x0002884E
		public ItemEquipCondition(BaseAchievement quest, AchievementConditionInfo info, int value, int templateid)
			: base(quest, info, value)
		{
			this.m_item = templateid;
		}

		// Token: 0x060070AB RID: 28843 RVA: 0x0002A663 File Offset: 0x00028863
		public override void AddTrigger(GamePlayer player)
		{
			player.NewGearEvent += this.player_PlayerNewGearEventHandle;
		}

		// Token: 0x060070AC RID: 28844 RVA: 0x00250410 File Offset: 0x0024E610
		private void player_PlayerNewGearEventHandle(ItemInfo item)
		{
			bool flag = item.TemplateID == this.m_item;
			if (flag)
			{
				int value = base.Value;
				base.Value = value + 1;
			}
		}

		// Token: 0x060070AD RID: 28845 RVA: 0x0002A679 File Offset: 0x00028879
		public override void RemoveTrigger(GamePlayer player)
		{
			player.NewGearEvent -= this.player_PlayerNewGearEventHandle;
		}

		// Token: 0x060070AE RID: 28846 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}

		// Token: 0x04003C62 RID: 15458
		private int m_item;
	}
}
