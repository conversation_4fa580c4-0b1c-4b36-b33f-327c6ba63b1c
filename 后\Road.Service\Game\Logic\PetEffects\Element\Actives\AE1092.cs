﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DC0 RID: 3520
	public class AE1092 : BasePetEffect
	{
		// Token: 0x06007C63 RID: 31843 RVA: 0x002973E4 File Offset: 0x002955E4
		public AE1092(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1092, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C64 RID: 31844 RVA: 0x00297464 File Offset: 0x00295664
		public override bool Start(Living living)
		{
			AE1092 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1092) as AE1092;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C65 RID: 31845 RVA: 0x0002FA2C File Offset: 0x0002DC2C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C66 RID: 31846 RVA: 0x0002FA42 File Offset: 0x0002DC42
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C67 RID: 31847 RVA: 0x002974C4 File Offset: 0x002956C4
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddPetEffect(new CE1092(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004A7A RID: 19066
		private int m_type = 0;

		// Token: 0x04004A7B RID: 19067
		private int m_count = 0;

		// Token: 0x04004A7C RID: 19068
		private int m_probability = 0;

		// Token: 0x04004A7D RID: 19069
		private int m_delay = 0;

		// Token: 0x04004A7E RID: 19070
		private int m_coldDown = 0;

		// Token: 0x04004A7F RID: 19071
		private int m_currentId;

		// Token: 0x04004A80 RID: 19072
		private int m_added = 0;
	}
}
