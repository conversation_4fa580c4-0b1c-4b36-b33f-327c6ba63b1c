﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E82 RID: 3714
	public class CE1176 : BasePetEffect
	{
		// Token: 0x060080A1 RID: 32929 RVA: 0x002A9B5C File Offset: 0x002A7D5C
		public CE1176(int count, int probability, int type, int skillId, int delay, string elementID, Living source)
			: base(ePetEffectType.CE1176, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
			this.m_source = source;
		}

		// Token: 0x060080A2 RID: 32930 RVA: 0x002A9BE4 File Offset: 0x002A7DE4
		public override bool Start(Living living)
		{
			CE1176 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1176) as CE1176;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080A3 RID: 32931 RVA: 0x000324EA File Offset: 0x000306EA
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060080A4 RID: 32932 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060080A5 RID: 32933 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginNextTurn(Living living)
		{
		}

		// Token: 0x060080A6 RID: 32934 RVA: 0x002A9C44 File Offset: 0x002A7E44
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 800;
				living.AddBlood(-this.m_added, 1);
				bool flag2 = living.Blood <= 0;
				if (flag2)
				{
					living.Die();
					bool flag3 = this.m_source != null && this.m_source is Player;
					if (flag3)
					{
						(this.m_source as Player).PlayerDetail.OnKillingLiving(this.m_source.Game, 2, living.Id, living.IsLiving, this.m_added);
					}
				}
			}
		}

		// Token: 0x060080A7 RID: 32935 RVA: 0x00032526 File Offset: 0x00030726
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004FC9 RID: 20425
		private int m_type = 0;

		// Token: 0x04004FCA RID: 20426
		private int m_count = 0;

		// Token: 0x04004FCB RID: 20427
		private int m_probability = 0;

		// Token: 0x04004FCC RID: 20428
		private int m_delay = 0;

		// Token: 0x04004FCD RID: 20429
		private int m_coldDown = 0;

		// Token: 0x04004FCE RID: 20430
		private int m_currentId;

		// Token: 0x04004FCF RID: 20431
		private int m_added = 0;

		// Token: 0x04004FD0 RID: 20432
		private Living m_source;
	}
}
