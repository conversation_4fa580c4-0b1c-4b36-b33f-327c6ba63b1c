﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E3E RID: 3646
	public class PE3174 : BasePetEffect
	{
		// Token: 0x06007F00 RID: 32512 RVA: 0x002A3508 File Offset: 0x002A1708
		public PE3174(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE3174, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F01 RID: 32513 RVA: 0x002A3584 File Offset: 0x002A1784
		public override bool Start(Living living)
		{
			PE3174 pe = living.PetEffectList.GetOfType(ePetEffectType.PE3174) as PE3174;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F02 RID: 32514 RVA: 0x0003155A File Offset: 0x0002F75A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
		}

		// Token: 0x06007F03 RID: 32515 RVA: 0x00031583 File Offset: 0x0002F783
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
		}

		// Token: 0x06007F04 RID: 32516 RVA: 0x002A35E0 File Offset: 0x002A17E0
		private void Player_BeginSelfTurn(Living living)
		{
			bool flag = living.PetEffects.ReduceDamage == 0;
			if (flag)
			{
				living.Game.SendPetBuff(living, base.Info, true);
				living.PetEffects.ReduceDamage = 15;
			}
		}

		// Token: 0x06007F05 RID: 32517 RVA: 0x002A3624 File Offset: 0x002A1824
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = damageAmount > 0;
			if (flag)
			{
				damageAmount -= damageAmount * 15 / 100;
			}
		}

		// Token: 0x04004DED RID: 19949
		private int m_type = 0;

		// Token: 0x04004DEE RID: 19950
		private int m_count = 0;

		// Token: 0x04004DEF RID: 19951
		private int m_probability = 0;

		// Token: 0x04004DF0 RID: 19952
		private int m_delay = 0;

		// Token: 0x04004DF1 RID: 19953
		private int m_coldDown = 0;

		// Token: 0x04004DF2 RID: 19954
		private int m_currentId;

		// Token: 0x04004DF3 RID: 19955
		private int m_added = 0;
	}
}
