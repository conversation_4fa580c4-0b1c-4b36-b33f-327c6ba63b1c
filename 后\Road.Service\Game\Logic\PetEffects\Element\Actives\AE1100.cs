﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DC8 RID: 3528
	public class AE1100 : BasePetEffect
	{
		// Token: 0x06007C8B RID: 31883 RVA: 0x00298094 File Offset: 0x00296294
		public AE1100(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1100, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C8C RID: 31884 RVA: 0x00298114 File Offset: 0x00296314
		public override bool Start(Living living)
		{
			AE1100 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1100) as AE1100;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C8D RID: 31885 RVA: 0x0002FB8C File Offset: 0x0002DD8C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C8E RID: 31886 RVA: 0x0002FBA2 File Offset: 0x0002DDA2
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C8F RID: 31887 RVA: 0x00298174 File Offset: 0x00296374
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.AddPetEffect(new CE1100(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004AB2 RID: 19122
		private int m_type = 0;

		// Token: 0x04004AB3 RID: 19123
		private int m_count = 0;

		// Token: 0x04004AB4 RID: 19124
		private int m_probability = 0;

		// Token: 0x04004AB5 RID: 19125
		private int m_delay = 0;

		// Token: 0x04004AB6 RID: 19126
		private int m_coldDown = 0;

		// Token: 0x04004AB7 RID: 19127
		private int m_currentId;

		// Token: 0x04004AB8 RID: 19128
		private int m_added = 0;
	}
}
