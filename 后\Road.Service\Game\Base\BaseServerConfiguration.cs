﻿using System;
using System.IO;
using System.Net;
using Game.Base.Config;

namespace Game.Base
{
	// Token: 0x02000F70 RID: 3952
	public class BaseServerConfiguration
	{
		// Token: 0x170014BC RID: 5308
		// (get) Token: 0x06008574 RID: 34164 RVA: 0x002BB504 File Offset: 0x002B9704
		// (set) Token: 0x06008575 RID: 34165 RVA: 0x0003597E File Offset: 0x00033B7E
		public ushort Port
		{
			get
			{
				return this._port;
			}
			set
			{
				this._port = value;
			}
		}

		// Token: 0x170014BD RID: 5309
		// (get) Token: 0x06008576 RID: 34166 RVA: 0x002BB51C File Offset: 0x002B971C
		// (set) Token: 0x06008577 RID: 34167 RVA: 0x00035988 File Offset: 0x00033B88
		public IPAddress Ip
		{
			get
			{
				return this._ip;
			}
			set
			{
				this._ip = value;
			}
		}

		// Token: 0x06008578 RID: 34168 RVA: 0x002BB534 File Offset: 0x002B9734
		protected virtual void LoadFromConfig(ConfigElement root)
		{
			string @string = root["Server"]["IP"].GetString("any");
			bool flag = @string == "any";
			if (flag)
			{
				this._ip = IPAddress.Any;
			}
			else
			{
				this._ip = IPAddress.Parse(@string);
			}
			this._port = (ushort)root["Server"]["Port"].GetInt((int)this._port);
		}

		// Token: 0x06008579 RID: 34169 RVA: 0x002BB5B8 File Offset: 0x002B97B8
		public void LoadFromXMLFile(FileInfo configFile)
		{
			XMLConfigFile xmlconfigFile = XMLConfigFile.ParseXMLFile(configFile);
			this.LoadFromConfig(xmlconfigFile);
		}

		// Token: 0x0600857A RID: 34170 RVA: 0x002BB5D8 File Offset: 0x002B97D8
		protected virtual void SaveToConfig(ConfigElement root)
		{
			root["Server"]["Port"].Set(this._port);
			root["Server"]["IP"].Set(this._ip);
		}

		// Token: 0x0600857B RID: 34171 RVA: 0x002BB630 File Offset: 0x002B9830
		public void SaveToXMLFile(FileInfo configFile)
		{
			bool flag = configFile == null;
			if (flag)
			{
				throw new ArgumentNullException("configFile");
			}
			XMLConfigFile xmlconfigFile = new XMLConfigFile();
			this.SaveToConfig(xmlconfigFile);
			xmlconfigFile.Save(configFile);
		}

		// Token: 0x0600857C RID: 34172 RVA: 0x00035992 File Offset: 0x00033B92
		public BaseServerConfiguration()
		{
			this._port = 7000;
			this._ip = IPAddress.Any;
		}

		// Token: 0x0400535F RID: 21343
		protected ushort _port;

		// Token: 0x04005360 RID: 21344
		protected IPAddress _ip;
	}
}
