﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Reflection;
using log4net;
using SqlDataProvider.BaseClass;

namespace Bussiness
{
	// Token: 0x02000FA2 RID: 4002
	public class BaseBussiness : IDisposable
	{
		// Token: 0x06008813 RID: 34835 RVA: 0x00036076 File Offset: 0x00034276
		public BaseBussiness()
		{
			this.db = new Sql_DbObject("AppConfig", "conString");
		}

		// Token: 0x06008814 RID: 34836 RVA: 0x002CA004 File Offset: 0x002C8204
		public DataTable GetPage(string queryStr, string queryWhere, int pageCurrent, int pageSize, string fdShow, string fdOreder, string fdKey, ref int total)
		{
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@QueryStr", queryStr),
					new SqlParameter("@QueryWhere", queryWhere),
					new SqlParameter("@PageSize", pageSize),
					new SqlParameter("@PageCurrent", pageCurrent),
					new SqlParameter("@FdShow", fdShow),
					new SqlParameter("@FdOrder", fdOreder),
					new SqlParameter("@FdKey", fdKey),
					new SqlParameter("@TotalRow", total)
				};
				array[7].Direction = ParameterDirection.Output;
				DataTable dataTable = this.db.GetDataTable(queryStr, "SP_CustomPage", array, 120);
				total = (int)array[7].Value;
				return dataTable;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error(string.Format("Custome Page queryStr@{0},queryWhere@{1},pageCurrent@{2},pageSize@{3},fdShow@{4},fdOrder@{5},fdKey@{6}", new object[] { queryStr, queryWhere, pageCurrent, pageSize, fdShow, fdOreder, fdKey }), ex);
				}
			}
			finally
			{
			}
			return new DataTable(queryStr);
		}

		// Token: 0x06008815 RID: 34837 RVA: 0x00036095 File Offset: 0x00034295
		public void Dispose()
		{
			this.db.Dispose();
			GC.SuppressFinalize(this);
		}

		// Token: 0x040053C8 RID: 21448
		protected static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040053C9 RID: 21449
		protected Sql_DbObject db;
	}
}
