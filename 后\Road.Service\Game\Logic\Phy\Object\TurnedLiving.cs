﻿using System;

namespace Game.Logic.Phy.Object
{
	// Token: 0x02000CE2 RID: 3298
	public class TurnedLiving : Living
	{
		// Token: 0x17001483 RID: 5251
		// (get) Token: 0x06007784 RID: 30596 RVA: 0x0028113C File Offset: 0x0027F33C
		// (set) Token: 0x06007785 RID: 30597 RVA: 0x0002C151 File Offset: 0x0002A351
		public int Delay
		{
			get
			{
				return this.m_delay;
			}
			set
			{
				this.m_delay = value;
			}
		}

		// Token: 0x17001484 RID: 5252
		// (get) Token: 0x06007786 RID: 30598 RVA: 0x00281154 File Offset: 0x0027F354
		// (set) Token: 0x06007787 RID: 30599 RVA: 0x0002C15B File Offset: 0x0002A35B
		public int MaxPsychic
		{
			get
			{
				return this.m_maxPsychic;
			}
			set
			{
				this.m_maxPsychic = value;
			}
		}

		// Token: 0x17001485 RID: 5253
		// (get) Token: 0x06007788 RID: 30600 RVA: 0x0028116C File Offset: 0x0027F36C
		// (set) Token: 0x06007789 RID: 30601 RVA: 0x0002C165 File Offset: 0x0002A365
		public int psychic
		{
			get
			{
				return this.m_psychic;
			}
			set
			{
				this.m_psychic = value;
			}
		}

		// Token: 0x17001486 RID: 5254
		// (get) Token: 0x0600778A RID: 30602 RVA: 0x00281184 File Offset: 0x0027F384
		// (set) Token: 0x0600778B RID: 30603 RVA: 0x0002C16F File Offset: 0x0002A36F
		public int PetMaxMP
		{
			get
			{
				return this.m_petMaxMP;
			}
			set
			{
				this.m_petMaxMP = value;
			}
		}

		// Token: 0x17001487 RID: 5255
		// (get) Token: 0x0600778C RID: 30604 RVA: 0x0028119C File Offset: 0x0027F39C
		// (set) Token: 0x0600778D RID: 30605 RVA: 0x0002C179 File Offset: 0x0002A379
		public int PetMP
		{
			get
			{
				return this.m_petMP;
			}
			set
			{
				this.m_petMP = value;
			}
		}

		// Token: 0x17001488 RID: 5256
		// (get) Token: 0x0600778E RID: 30606 RVA: 0x002811B4 File Offset: 0x0027F3B4
		// (set) Token: 0x0600778F RID: 30607 RVA: 0x0002C183 File Offset: 0x0002A383
		public int PetMaxFlag
		{
			get
			{
				return this.m_petMaxFlag;
			}
			set
			{
				this.m_petMaxFlag = value;
			}
		}

		// Token: 0x17001489 RID: 5257
		// (get) Token: 0x06007790 RID: 30608 RVA: 0x002811CC File Offset: 0x0027F3CC
		// (set) Token: 0x06007791 RID: 30609 RVA: 0x0002C18D File Offset: 0x0002A38D
		public int PetFlag
		{
			get
			{
				return this.m_petFlag;
			}
			set
			{
				this.m_petFlag = value;
			}
		}

		// Token: 0x1700148A RID: 5258
		// (get) Token: 0x06007792 RID: 30610 RVA: 0x002811E4 File Offset: 0x0027F3E4
		// (set) Token: 0x06007793 RID: 30611 RVA: 0x0002C197 File Offset: 0x0002A397
		public int Dander
		{
			get
			{
				return this.m_dander;
			}
			set
			{
				this.m_dander = value;
			}
		}

		// Token: 0x06007794 RID: 30612 RVA: 0x0002C1A1 File Offset: 0x0002A3A1
		public void ReduceDelay30()
		{
			this.m_delay -= this.m_delay * 30 / 100;
		}

		// Token: 0x06007795 RID: 30613 RVA: 0x002811FC File Offset: 0x0027F3FC
		public TurnedLiving(int id, BaseGame game, int team, string name, string modelId, int maxBlood, int immunity, int direction)
			: base(id, game, team, name, modelId, maxBlood, immunity, direction)
		{
		}

		// Token: 0x06007796 RID: 30614 RVA: 0x0002C1BD File Offset: 0x0002A3BD
		public override void Reset()
		{
			base.Reset();
		}

		// Token: 0x06007797 RID: 30615 RVA: 0x00281250 File Offset: 0x0027F450
		public void AddDelay(int value)
		{
			bool flag = base.Game is PVEGame;
			if (flag)
			{
				this.m_delay = ((PVEGame)base.Game).MissionInfo.IncrementDelay;
			}
			else
			{
				this.m_delay += value;
			}
		}

		// Token: 0x06007798 RID: 30616 RVA: 0x0002C1C7 File Offset: 0x0002A3C7
		public void ReduceDelay(int value)
		{
			this.m_delay -= value;
		}

		// Token: 0x06007799 RID: 30617 RVA: 0x0002C1D8 File Offset: 0x0002A3D8
		public override void PrepareSelfTurn()
		{
			base.PrepareSelfTurn();
		}

		// Token: 0x0600779A RID: 30618 RVA: 0x002812A0 File Offset: 0x0027F4A0
		public void AddPetMP(int value)
		{
			bool flag = value <= 0;
			if (!flag)
			{
				bool flag2 = base.IsLiving && this.PetMP < this.PetMaxMP;
				if (flag2)
				{
					this.m_petMP += value;
					bool flag3 = this.m_petMP > this.PetMaxMP;
					if (flag3)
					{
						this.m_petMP = this.PetMaxMP;
					}
				}
				else
				{
					this.m_petMP = this.PetMaxMP;
				}
			}
		}

		// Token: 0x0600779B RID: 30619 RVA: 0x0028131C File Offset: 0x0027F51C
		public void RemovePetMP(int value)
		{
			bool flag = value > 0 && base.IsLiving && this.PetMP > 0;
			if (flag)
			{
				this.m_petMP -= value;
				bool flag2 = this.m_petMP < 0;
				if (flag2)
				{
					this.m_petMP = 0;
				}
			}
		}

		// Token: 0x0600779C RID: 30620 RVA: 0x0028136C File Offset: 0x0027F56C
		public void AddPetFlag(int value)
		{
			bool flag = value <= 0;
			if (!flag)
			{
				bool flag2 = base.IsLiving && this.PetFlag < this.PetMaxFlag;
				if (flag2)
				{
					this.m_petFlag += value;
					bool flag3 = this.m_petFlag > this.PetMaxFlag;
					if (flag3)
					{
						this.m_petFlag = this.PetMaxFlag;
					}
				}
				else
				{
					this.m_petFlag = this.PetMaxFlag;
				}
			}
		}

		// Token: 0x0600779D RID: 30621 RVA: 0x002813E8 File Offset: 0x0027F5E8
		public void RemovePetFlag(int value)
		{
			bool flag = value > 0 && base.IsLiving && this.PetFlag > 0;
			if (flag)
			{
				this.m_petFlag -= value;
				bool flag2 = this.m_petFlag < 0;
				if (flag2)
				{
					this.m_petFlag = 0;
				}
			}
		}

		// Token: 0x0600779E RID: 30622 RVA: 0x00281438 File Offset: 0x0027F638
		public void AddDander(int value)
		{
			bool flag = value > 0 && base.IsLiving;
			if (flag)
			{
				this.SetDander(this.m_dander + value);
			}
		}

		// Token: 0x0600779F RID: 30623 RVA: 0x00281468 File Offset: 0x0027F668
		public void SetDander(int value)
		{
			this.m_dander = Math.Min(value, 200);
			bool syncAtTime = base.SyncAtTime;
			if (syncAtTime)
			{
				this.m_game.SendGameUpdateDander(this);
			}
		}

		// Token: 0x060077A0 RID: 30624 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void StartGame()
		{
		}

		// Token: 0x060077A1 RID: 30625 RVA: 0x002814A0 File Offset: 0x0027F6A0
		public virtual void Skip(int spendTime)
		{
			bool isAttacking = base.IsAttacking;
			if (isAttacking)
			{
				this.StopAttacking();
				this.m_game.CheckState(0);
			}
		}

		// Token: 0x04004608 RID: 17928
		protected int m_delay;

		// Token: 0x04004609 RID: 17929
		public int DefaultDelay;

		// Token: 0x0400460A RID: 17930
		private int m_maxPsychic = 999;

		// Token: 0x0400460B RID: 17931
		private int m_psychic = 0;

		// Token: 0x0400460C RID: 17932
		private int m_petMaxMP = 100;

		// Token: 0x0400460D RID: 17933
		private int m_petMP = 10;

		// Token: 0x0400460E RID: 17934
		private int m_petMaxFlag = 4;

		// Token: 0x0400460F RID: 17935
		private int m_petFlag = 10;

		// Token: 0x04004610 RID: 17936
		private int m_dander;
	}
}
