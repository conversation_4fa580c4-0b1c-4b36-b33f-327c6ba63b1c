﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F51 RID: 3921
	public class LivingBoltMoveAction : BaseAction
	{
		// Token: 0x060084FA RID: 34042 RVA: 0x00035302 File Offset: 0x00033502
		public LivingBoltMoveAction(Living living, int x, int y, string action, int delay, int finishTime)
			: base(delay, finishTime)
		{
			this.m_living = living;
			this.m_x = x;
			this.m_y = y;
			this.m_action = action;
		}

		// Token: 0x060084FB RID: 34043 RVA: 0x002B96E0 File Offset: 0x002B78E0
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			this.m_living.SetXY(this.m_x, this.m_y);
			game.SendLivingBoltMove(this.m_living, this.m_x, this.m_y, this.m_action);
			base.Finish(tick);
		}

		// Token: 0x040052E3 RID: 21219
		private Living m_living;

		// Token: 0x040052E4 RID: 21220
		private int m_x;

		// Token: 0x040052E5 RID: 21221
		private int m_y;

		// Token: 0x040052E6 RID: 21222
		private string m_action;
	}
}
