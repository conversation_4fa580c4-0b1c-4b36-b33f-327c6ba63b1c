﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D19 RID: 3353
	public class PetShareTakeDamageToTeamEquip : BasePetEffect
	{
		// Token: 0x060078E0 RID: 30944 RVA: 0x0002CFB2 File Offset: 0x0002B1B2
		public PetShareTakeDamageToTeamEquip(int count, string elementID)
			: base(ePetEffectType.PetShareTakeDamageToTeamEquip, elementID)
		{
			this.m_count = count;
		}

		// Token: 0x060078E1 RID: 30945 RVA: 0x00288888 File Offset: 0x00286A88
		public override bool Start(Living living)
		{
			PetShareTakeDamageToTeamEquip petShareTakeDamageToTeamEquip = living.PetEffectList.GetOfType(ePetEffectType.PetShareTakeDamageToTeamEquip) as PetShareTakeDamageToTeamEquip;
			bool flag = petShareTakeDamageToTeamEquip != null;
			bool flag2;
			if (flag)
			{
				petShareTakeDamageToTeamEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078E2 RID: 30946 RVA: 0x0002CFC9 File Offset: 0x0002B1C9
		protected override void OnAttachedToPlayer(Player player)
		{
			player.Game.sendShowPicSkil(player, base.Info, true);
			player.BeginSelfTurn += this.beginSelfTurn;
			player.TakePlayerDamage += this.takeDamage;
		}

		// Token: 0x060078E3 RID: 30947 RVA: 0x0002D006 File Offset: 0x0002B206
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.beginSelfTurn;
			player.TakePlayerDamage -= this.takeDamage;
		}

		// Token: 0x060078E4 RID: 30948 RVA: 0x002888D0 File Offset: 0x00286AD0
		private void takeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			List<Player> allTeamPlayers = living.Game.GetAllTeamPlayers(living);
			int num = damageAmount / allTeamPlayers.Count;
			int num2 = criticalAmount / allTeamPlayers.Count;
			foreach (Player player in allTeamPlayers)
			{
				bool flag = player == living;
				if (!flag)
				{
					player.SyncAtTime = true;
					player.RemoveBlood(num + num2);
					player.SyncAtTime = false;
					bool flag2 = player.Blood <= 0;
					if (flag2)
					{
						player.Die();
						bool flag3 = source != null && source is Player;
						if (flag3)
						{
							(source as Player).OnAfterKillingLiving(player, num + num2, 0);
						}
					}
				}
			}
			damageAmount = num;
			criticalAmount = num2;
		}

		// Token: 0x060078E5 RID: 30949 RVA: 0x002889BC File Offset: 0x00286BBC
		private void beginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.Info, false);
				this.Stop();
			}
		}

		// Token: 0x040046DA RID: 18138
		private int m_count;
	}
}
