﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Reflection;
using Game.Base.Packets;
using Game.Logic.Actions;
using Game.Logic.Phy.Maps;
using Game.Logic.Phy.Object;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000C85 RID: 3205
	public class BaseGame : AbstractGame
	{
		// Token: 0x17001336 RID: 4918
		// (get) Token: 0x0600716A RID: 29034 RVA: 0x0002A8E5 File Offset: 0x00028AE5
		public int RoomId
		{
			get
			{
				return this.m_roomId;
			}
		}

		// Token: 0x17001337 RID: 4919
		// (get) Token: 0x0600716B RID: 29035 RVA: 0x0002A8ED File Offset: 0x00028AED
		public Dictionary<int, Player> Players
		{
			get
			{
				return this.m_players;
			}
		}

		// Token: 0x17001338 RID: 4920
		// (get) Token: 0x0600716C RID: 29036 RVA: 0x0025BE8C File Offset: 0x0025A08C
		public int PlayerCount
		{
			get
			{
				Dictionary<int, Player> players = this.m_players;
				int count;
				lock (players)
				{
					count = this.m_players.Count;
				}
				return count;
			}
		}

		// Token: 0x0600716D RID: 29037 RVA: 0x0025BED8 File Offset: 0x0025A0D8
		public Player FindPlayerByChouHen()
		{
			List<Player> list = new List<Player>();
			List<double> list2 = new List<double>();
			Player player = null;
			foreach (Player player2 in this.m_players.Values)
			{
				bool isLiving = player2.IsLiving;
				if (isLiving)
				{
					list.Add(player2);
					list2.Add(player2.chouhen);
				}
			}
			bool flag = list.Count > 0;
			if (flag)
			{
				foreach (Player player3 in list)
				{
					bool flag2 = player3.chouhen == list2.Max();
					if (flag2)
					{
						player = player3;
					}
				}
			}
			return player;
		}

		// Token: 0x17001339 RID: 4921
		// (get) Token: 0x0600716E RID: 29038 RVA: 0x0025BFD4 File Offset: 0x0025A1D4
		// (set) Token: 0x0600716F RID: 29039 RVA: 0x0002A8F5 File Offset: 0x00028AF5
		public int TurnIndex
		{
			get
			{
				return this.m_turnIndex;
			}
			set
			{
				this.m_turnIndex = value;
			}
		}

		// Token: 0x1700133A RID: 4922
		// (get) Token: 0x06007170 RID: 29040 RVA: 0x0025BFEC File Offset: 0x0025A1EC
		// (set) Token: 0x06007171 RID: 29041 RVA: 0x0002A900 File Offset: 0x00028B00
		protected int m_turnIndex
		{
			get
			{
				return this.turnIndex;
			}
			set
			{
				this.turnIndex = value;
			}
		}

		// Token: 0x1700133B RID: 4923
		// (get) Token: 0x06007172 RID: 29042 RVA: 0x0002A90A File Offset: 0x00028B0A
		public eGameState GameState
		{
			get
			{
				return this.m_gameState;
			}
		}

		// Token: 0x1700133C RID: 4924
		// (get) Token: 0x06007173 RID: 29043 RVA: 0x0002A912 File Offset: 0x00028B12
		public eGameState GameSecondState
		{
			get
			{
				return this.m_gameSecondState;
			}
		}

		// Token: 0x1700133D RID: 4925
		// (get) Token: 0x06007174 RID: 29044 RVA: 0x0002A91A File Offset: 0x00028B1A
		public float Wind
		{
			get
			{
				return this.m_map.wind;
			}
		}

		// Token: 0x1700133E RID: 4926
		// (get) Token: 0x06007175 RID: 29045 RVA: 0x0002A927 File Offset: 0x00028B27
		public Map Map
		{
			get
			{
				return this.m_map;
			}
		}

		// Token: 0x1700133F RID: 4927
		// (get) Token: 0x06007176 RID: 29046 RVA: 0x0002A92F File Offset: 0x00028B2F
		public List<TurnedLiving> TurnQueue
		{
			get
			{
				return this.m_turnQueue;
			}
		}

		// Token: 0x17001340 RID: 4928
		// (get) Token: 0x06007177 RID: 29047 RVA: 0x0002A937 File Offset: 0x00028B37
		public bool HasPlayer
		{
			get
			{
				return this.m_players.Count > 0;
			}
		}

		// Token: 0x17001341 RID: 4929
		// (get) Token: 0x06007178 RID: 29048 RVA: 0x0002A947 File Offset: 0x00028B47
		public Random Random
		{
			get
			{
				return this.m_random;
			}
		}

		// Token: 0x17001342 RID: 4930
		// (get) Token: 0x06007179 RID: 29049 RVA: 0x0002A94F File Offset: 0x00028B4F
		public TurnedLiving CurrentLiving
		{
			get
			{
				return this.m_currentLiving;
			}
		}

		// Token: 0x17001343 RID: 4931
		// (get) Token: 0x0600717A RID: 29050 RVA: 0x0002A957 File Offset: 0x00028B57
		public int LifeTime
		{
			get
			{
				return this.m_lifeTime;
			}
		}

		// Token: 0x140000B1 RID: 177
		// (add) Token: 0x0600717B RID: 29051 RVA: 0x0025C004 File Offset: 0x0025A204
		// (remove) Token: 0x0600717C RID: 29052 RVA: 0x0025C03C File Offset: 0x0025A23C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event GameEventHandle GameOverred;

		// Token: 0x140000B2 RID: 178
		// (add) Token: 0x0600717D RID: 29053 RVA: 0x0025C074 File Offset: 0x0025A274
		// (remove) Token: 0x0600717E RID: 29054 RVA: 0x0025C0AC File Offset: 0x0025A2AC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event GameEventHandle BeginNewTurn;

		// Token: 0x140000B3 RID: 179
		// (add) Token: 0x0600717F RID: 29055 RVA: 0x0025C0E4 File Offset: 0x0025A2E4
		// (remove) Token: 0x06007180 RID: 29056 RVA: 0x0025C11C File Offset: 0x0025A31C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event BaseGame.GameOverLogEventHandle GameOverLog;

		// Token: 0x140000B4 RID: 180
		// (add) Token: 0x06007181 RID: 29057 RVA: 0x0025C154 File Offset: 0x0025A354
		// (remove) Token: 0x06007182 RID: 29058 RVA: 0x0025C18C File Offset: 0x0025A38C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event BaseGame.GameNpcDieEventHandle GameNpcDie;

		// Token: 0x06007183 RID: 29059 RVA: 0x0025C1C4 File Offset: 0x0025A3C4
		public void MessageGame(Living living_0, string string_0)
		{
			GSPacketIn gspacketIn = new GSPacketIn(3);
			gspacketIn.WriteInt(3);
			gspacketIn.WriteString(string_0);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007184 RID: 29060 RVA: 0x0025C1F4 File Offset: 0x0025A3F4
		public Player[] FindAllPlayerCANmove()
		{
			List<Player> list = new List<Player>();
			foreach (Player player in this.m_players.Values)
			{
				bool canMove = player.CanMove;
				if (canMove)
				{
					list.Add(player);
				}
			}
			return list.ToArray();
		}

		// Token: 0x06007185 RID: 29061 RVA: 0x0025C270 File Offset: 0x0025A470
		public Player FindPlayerByBlood()
		{
			List<Player> list = new List<Player>();
			List<int> list2 = new List<int>();
			Player player = null;
			foreach (Player player2 in this.m_players.Values)
			{
				bool isLiving = player2.IsLiving;
				if (isLiving)
				{
					list.Add(player2);
					list2.Add(player2.Blood);
				}
			}
			bool flag = list.Count > 0;
			if (flag)
			{
				foreach (Player player3 in list)
				{
					bool flag2 = player3.Blood == list2.Max();
					if (flag2)
					{
						player = player3;
					}
				}
			}
			return player;
		}

		// Token: 0x06007186 RID: 29062 RVA: 0x0025C36C File Offset: 0x0025A56C
		public List<SimpleBoss> FindAllBossByID2(int ID)
		{
			List<SimpleBoss> list = new List<SimpleBoss>();
			foreach (SimpleBoss simpleBoss in this.m_Bosses)
			{
				bool flag = simpleBoss.NpcInfo.ID == ID;
				if (flag)
				{
					list.Add(simpleBoss);
				}
			}
			return list;
		}

		// Token: 0x06007187 RID: 29063 RVA: 0x0025C3E8 File Offset: 0x0025A5E8
		internal void sendShowPicSkil(Living player, PetSkillElementInfo info, bool isActive)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(145);
			gspacketIn.WriteInt(info.ID);
			gspacketIn.WriteString(info.Name);
			gspacketIn.WriteString(info.Description);
			gspacketIn.WriteString(info.Pic.ToString());
			gspacketIn.WriteString(info.EffectPic);
			gspacketIn.WriteBoolean(isActive);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007188 RID: 29064 RVA: 0x0025C478 File Offset: 0x0025A678
		internal void UpdateEnergy(Player player, int energycost)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.WriteByte(191);
			gspacketIn.WriteInt(energycost);
			player.PlayerDetail.SendTCP(gspacketIn);
		}

		// Token: 0x06007189 RID: 29065 RVA: 0x0025C4B8 File Offset: 0x0025A6B8
		public void UpdateWind2(float wind, bool sendToClient)
		{
			this.m_map.wind = wind;
			if (sendToClient)
			{
				this.SendGameUpdateWind(wind);
			}
		}

		// Token: 0x0600718A RID: 29066 RVA: 0x0025C4E4 File Offset: 0x0025A6E4
		public void UpdateBlood(Living living, int type, int value, int embleType = 0)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id)
			{
				Parameter1 = living.Id
			};
			gspacketIn.WriteByte(11);
			gspacketIn.WriteByte((byte)type);
			gspacketIn.WriteLong((long)living.Blood);
			gspacketIn.WriteInt(value);
			gspacketIn.WriteInt(embleType);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600718B RID: 29067 RVA: 0x0025C548 File Offset: 0x0025A748
		public BaseGame(int id, int roomId, Map map, eRoomType roomType, eGameType gameType, int timeType)
			: base(id, roomType, gameType, timeType)
		{
			this.m_roomId = roomId;
			this.m_players = new Dictionary<int, Player>();
			this.m_turnQueue = new List<TurnedLiving>();
			this.m_livings = new List<Living>();
			this.m_decklivings = new List<Living>();
			this.m_random = new Random();
			this.m_logStartIps = new Dictionary<int, string>();
			this.m_map = map;
			this.m_actions = new ArrayList();
			this.PhysicalId = 0;
			this.BossWarField = "";
			this.FrozenWind = false;
			this.m_tempBox = new List<SimpleBox>();
			this.m_tempPoints = new List<Point>();
			this.m_tempGhostPoints = new List<Point>();
			bool flag = roomType == eRoomType.Dungeon || roomType == eRoomType.Boss;
			if (flag)
			{
				this.Cards = new int[121];
			}
			else
			{
				this.Cards = new int[8];
			}
			this.VortexBombKillCount = 0;
			this.m_gameState = eGameState.Inited;
		}

		// Token: 0x0600718C RID: 29068 RVA: 0x0002A95F File Offset: 0x00028B5F
		public void SetWind(int wind)
		{
			this.m_map.wind = (float)wind;
		}

		// Token: 0x0600718D RID: 29069 RVA: 0x0025C690 File Offset: 0x0025A890
		public bool SetMap(int mapId)
		{
			bool flag = this.GameState == eGameState.Playing;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				Map map = MapMgr.CloneMap(mapId);
				bool flag3 = map != null;
				if (flag3)
				{
					this.m_map = map;
					flag2 = true;
				}
				else
				{
					flag2 = false;
				}
			}
			return flag2;
		}

		// Token: 0x0600718E RID: 29070 RVA: 0x0025C6D0 File Offset: 0x0025A8D0
		public int GetTurnWaitTime()
		{
			return this.m_timeType;
		}

		// Token: 0x0600718F RID: 29071 RVA: 0x0025C6E8 File Offset: 0x0025A8E8
		protected void AddLogIp(int id, string ip)
		{
			Dictionary<int, string> logStartIps = this.m_logStartIps;
			lock (logStartIps)
			{
				bool flag2 = !this.m_logStartIps.ContainsKey(id);
				if (flag2)
				{
					this.m_logStartIps.Add(id, ip);
				}
			}
		}

		// Token: 0x06007190 RID: 29072 RVA: 0x0025C74C File Offset: 0x0025A94C
		protected void AddPlayer(Player fp)
		{
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				this.m_players.Add(fp.Id, fp);
				bool flag2 = fp.Weapon != null;
				if (flag2)
				{
					this.m_turnQueue.Add(fp);
				}
			}
		}

		// Token: 0x06007191 RID: 29073 RVA: 0x0025C7BC File Offset: 0x0025A9BC
		public bool IsPVE()
		{
			eRoomType roomType = base.RoomType;
			eRoomType eRoomType = roomType;
			return eRoomType - eRoomType.Boss <= 1 || eRoomType == eRoomType.Academy;
		}

		// Token: 0x06007192 RID: 29074 RVA: 0x0025C7F0 File Offset: 0x0025A9F0
		public virtual void AddLiving(Living living)
		{
			this.m_map.AddPhysical(living);
			bool flag = living is Player;
			if (flag)
			{
				Player player = living as Player;
				bool flag2 = player.Weapon == null;
				if (flag2)
				{
					return;
				}
			}
			bool flag3 = living is TurnedLiving;
			if (flag3)
			{
				this.m_turnQueue.Add(living as TurnedLiving);
			}
			else
			{
				bool flag4 = ((SimpleNpc)living).Type != eLivingType.SimpleNpcDeck;
				if (flag4)
				{
					this.m_livings.Add(living);
				}
				else
				{
					this.m_decklivings.Add(living);
				}
			}
			bool flag5 = !(living is Player);
			if (flag5)
			{
				this.SendAddLiving(living);
			}
		}

		// Token: 0x06007193 RID: 29075 RVA: 0x0002A970 File Offset: 0x00028B70
		public virtual void AddGhostBoxObj(PhysicalObj phy)
		{
			this.m_map.AddPhysical(phy);
			phy.SetGame(this);
		}

		// Token: 0x06007194 RID: 29076 RVA: 0x0025C8A8 File Offset: 0x0025AAA8
		public virtual void AddPhysicalObj(PhysicalObj phy, bool sendToClient)
		{
			this.m_map.AddPhysical(phy);
			phy.SetGame(this);
			if (sendToClient)
			{
				this.SendAddPhysicalObj(phy);
			}
		}

		// Token: 0x06007195 RID: 29077 RVA: 0x0025C8DC File Offset: 0x0025AADC
		public virtual void AddPhysicalTip(PhysicalObj phy, bool sendToClient)
		{
			this.m_map.AddPhysical(phy);
			phy.SetGame(this);
			if (sendToClient)
			{
				this.SendAddPhysicalTip(phy);
			}
		}

		// Token: 0x06007196 RID: 29078 RVA: 0x0025C910 File Offset: 0x0025AB10
		public override Player RemovePlayer(IGamePlayer gp, bool IsKick)
		{
			Player player = null;
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player2 in this.m_players.Values)
				{
					bool flag2 = player2.PlayerDetail == gp;
					if (flag2)
					{
						player = player2;
						this.m_players.Remove(player2.Id);
						break;
					}
				}
			}
			bool flag3 = player != null;
			if (flag3)
			{
				this.AddAction(new RemovePlayerAction(player));
			}
			return player;
		}

		// Token: 0x06007197 RID: 29079 RVA: 0x0025C9E0 File Offset: 0x0025ABE0
		public void RemovePhysicalObj(PhysicalObj phy, bool sendToClient)
		{
			this.m_map.RemovePhysical(phy);
			phy.SetGame(null);
			if (sendToClient)
			{
				this.SendRemovePhysicalObj(phy);
			}
		}

		// Token: 0x06007198 RID: 29080 RVA: 0x0025CA14 File Offset: 0x0025AC14
		public void RemoveLiving(int id)
		{
			foreach (Living living in this.m_livings)
			{
				bool flag = living.Id == id;
				if (flag)
				{
					this.m_map.RemovePhysical(living);
					bool flag2 = living is TurnedLiving;
					if (flag2)
					{
						this.m_turnQueue.Remove(living as TurnedLiving);
					}
					else
					{
						this.m_livings.Remove(living);
					}
				}
			}
			this.SendRemoveLiving(id);
		}

		// Token: 0x06007199 RID: 29081 RVA: 0x0002A988 File Offset: 0x00028B88
		public void RemoveLivings(int id)
		{
			this.SendRemoveLiving(id);
		}

		// Token: 0x0600719A RID: 29082 RVA: 0x0025CABC File Offset: 0x0025ACBC
		public void RemoveLiving(Living living, bool sendToClient)
		{
			this.m_map.RemovePhysical(living);
			if (sendToClient)
			{
				this.SendRemoveLiving(living.Id);
			}
		}

		// Token: 0x0600719B RID: 29083 RVA: 0x0025CAEC File Offset: 0x0025ACEC
		public List<Living> GetLivedLivings()
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool isLiving = living.IsLiving;
				if (isLiving)
				{
					list.Add(living);
				}
			}
			return list;
		}

		// Token: 0x0600719C RID: 29084 RVA: 0x0025CB60 File Offset: 0x0025AD60
		public List<Living> GetLivedLivingsHadTurn()
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living.IsLiving && living is SimpleNpc && living.Config.HasTurn;
				if (flag)
				{
					list.Add(living);
				}
			}
			return list;
		}

		// Token: 0x0600719D RID: 29085 RVA: 0x0025CBEC File Offset: 0x0025ADEC
		public void ClearAllNpc()
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living is SimpleNpc;
				if (flag)
				{
					list.Add(living);
				}
			}
			foreach (Living living2 in list)
			{
				this.m_livings.Remove(living2);
				living2.Dispose();
				this.SendRemoveLiving(living2.Id);
			}
			List<Physics> allPhysicalSafe = this.m_map.GetAllPhysicalSafe();
			foreach (Physics physics in allPhysicalSafe)
			{
				bool flag2 = physics is SimpleNpc;
				if (flag2)
				{
					this.m_map.RemovePhysical(physics);
				}
			}
		}

		// Token: 0x0600719E RID: 29086 RVA: 0x0025CD24 File Offset: 0x0025AF24
		public void ClearDiedPhysicals()
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool flag = !living.IsLiving;
				if (flag)
				{
					list.Add(living);
				}
			}
			foreach (Living living2 in list)
			{
				this.m_livings.Remove(living2);
				living2.Dispose();
			}
			List<Living> list2 = new List<Living>();
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag2 = !turnedLiving.IsLiving;
				if (flag2)
				{
					list2.Add(turnedLiving);
				}
			}
			foreach (Living living3 in list2)
			{
				TurnedLiving turnedLiving2 = (TurnedLiving)living3;
				this.m_turnQueue.Remove(turnedLiving2);
			}
			List<Physics> allPhysicalSafe = this.m_map.GetAllPhysicalSafe();
			foreach (Physics physics in allPhysicalSafe)
			{
				bool flag3 = !physics.IsLiving && !(physics is Player);
				if (flag3)
				{
					this.m_map.RemovePhysical(physics);
				}
			}
		}

		// Token: 0x0600719F RID: 29087 RVA: 0x0025CF14 File Offset: 0x0025B114
		public int CountPlayersTeam(int team)
		{
			int num = 0;
			Player[] allPlayers = this.GetAllPlayers();
			for (int i = 0; i < allPlayers.Length; i++)
			{
				bool flag = allPlayers[i].Team == team;
				if (flag)
				{
					num++;
				}
			}
			return num;
		}

		// Token: 0x060071A0 RID: 29088 RVA: 0x0025CF60 File Offset: 0x0025B160
		public bool IsAllComplete()
		{
			List<Player> allFightPlayers = this.GetAllFightPlayers();
			foreach (Player player in allFightPlayers)
			{
				bool flag = player.LoadingProcess < 100;
				if (flag)
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x060071A1 RID: 29089 RVA: 0x0025CFD0 File Offset: 0x0025B1D0
		public bool IsSpecialPVE()
		{
			eRoomType roomType = base.RoomType;
			eRoomType eRoomType = roomType;
			return eRoomType == eRoomType.FightLib || eRoomType == eRoomType.Freshman;
		}

		// Token: 0x060071A2 RID: 29090 RVA: 0x0025D004 File Offset: 0x0025B204
		public Player FindPlayer(int id)
		{
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				bool flag2 = this.m_players.ContainsKey(id);
				if (flag2)
				{
					return this.m_players[id];
				}
			}
			return null;
		}

		// Token: 0x060071A3 RID: 29091 RVA: 0x0025D068 File Offset: 0x0025B268
		public TurnedLiving FindNextTurnedLiving(bool isPvP)
		{
			bool flag = this.m_turnQueue.Count == 0;
			TurnedLiving turnedLiving;
			if (flag)
			{
				turnedLiving = null;
			}
			else
			{
				TurnedLiving turnedLiving2 = null;
				bool flag2 = this.TurnIndex == 1 && isPvP;
				if (flag2)
				{
					double num = -1.0;
					for (int i = 0; i < this.m_turnQueue.Count; i++)
					{
						bool flag3 = (!(this.m_turnQueue[i] is Player) || !(this.m_turnQueue[i] as Player).PlayerDetail.PlayerCharacter.isViewer) && this.m_turnQueue[i].IsLiving && this.m_turnQueue[i] is Player && (this.m_turnQueue[i] as Player).Agility > num;
						if (flag3)
						{
							turnedLiving2 = this.m_turnQueue[i];
							num = (this.m_turnQueue[i] as Player).Agility;
						}
					}
				}
				bool flag4 = turnedLiving2 == null;
				if (flag4)
				{
					int num2 = this.m_random.Next(this.m_turnQueue.Count - 1);
					turnedLiving2 = this.m_turnQueue[num2];
					int num3 = turnedLiving2.Delay;
					for (int j = 0; j < this.m_turnQueue.Count; j++)
					{
						bool flag5 = (!(this.m_turnQueue[j] is Player) || !(this.m_turnQueue[j] as Player).PlayerDetail.PlayerCharacter.isViewer) && (this.m_turnQueue[j].Config.HasTurn || this.m_turnQueue[j] is Player) && this.m_turnQueue[j].IsLiving && this.m_turnQueue[j].Delay < num3;
						if (flag5)
						{
							num3 = this.m_turnQueue[j].Delay;
							turnedLiving2 = this.m_turnQueue[j];
						}
					}
				}
				turnedLiving2.TurnNum++;
				turnedLiving = turnedLiving2;
			}
			return turnedLiving;
		}

		// Token: 0x060071A4 RID: 29092 RVA: 0x0025D2BC File Offset: 0x0025B4BC
		public TurnedLiving[] GetNextAllTurnedLiving()
		{
			bool flag = this.m_turnQueue.Count == 0;
			TurnedLiving[] array;
			if (flag)
			{
				array = null;
			}
			else
			{
				List<TurnedLiving> list = new List<TurnedLiving>();
				for (int i = 0; i < this.m_turnQueue.Count; i++)
				{
					bool flag2 = this.m_turnQueue[i].IsLiving && !this.m_turnQueue[i].IsFrost && !this.m_turnQueue[i].IsAttacking && this.m_turnQueue[i] is Player;
					if (flag2)
					{
						this.m_turnQueue[i].TurnNum++;
						list.Add(this.m_turnQueue[i]);
					}
				}
				array = list.ToArray();
			}
			return array;
		}

		// Token: 0x060071A5 RID: 29093 RVA: 0x0025D39C File Offset: 0x0025B59C
		public virtual void MinusDelays(int lowestDelay)
		{
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				turnedLiving.Delay -= lowestDelay;
			}
		}

		// Token: 0x060071A6 RID: 29094 RVA: 0x0025D3FC File Offset: 0x0025B5FC
		public SimpleNpc FindNpc(int npcId)
		{
			SimpleNpc simpleNpc = null;
			foreach (Living living in this.m_livings)
			{
				bool flag = living is SimpleNpc && (living as SimpleNpc).NpcInfo.ID == npcId && living.IsLiving;
				if (flag)
				{
					simpleNpc = living as SimpleNpc;
					return simpleNpc;
				}
			}
			return simpleNpc;
		}

		// Token: 0x060071A7 RID: 29095 RVA: 0x0025D48C File Offset: 0x0025B68C
		public SimpleNpc FindRandomNpc()
		{
			SimpleNpc[] array = this.FindAllNpcLiving();
			int num = this.Random.Next(0, array.Length - 1);
			return array[num];
		}

		// Token: 0x060071A8 RID: 29096 RVA: 0x0025D4BC File Offset: 0x0025B6BC
		public SimpleBoss[] FindAllBoss()
		{
			List<SimpleBoss> list = new List<SimpleBoss>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living is SimpleBoss;
				if (flag)
				{
					list.Add(living as SimpleBoss);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060071A9 RID: 29097 RVA: 0x0025D53C File Offset: 0x0025B73C
		public SimpleBoss FindBoss(int bossID)
		{
			SimpleBoss simpleBoss = null;
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving is SimpleBoss && (turnedLiving as SimpleBoss).NpcInfo.ID == bossID;
				if (flag)
				{
					simpleBoss = turnedLiving as SimpleBoss;
					return simpleBoss;
				}
			}
			return simpleBoss;
		}

		// Token: 0x060071AA RID: 29098 RVA: 0x0025D5C8 File Offset: 0x0025B7C8
		public SimpleBoss FindBossWithID(int id)
		{
			SimpleBoss simpleBoss = null;
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving is SimpleBoss && turnedLiving.IsLiving && (turnedLiving as SimpleBoss).NpcInfo.ID == id;
				if (flag)
				{
					simpleBoss = turnedLiving as SimpleBoss;
				}
			}
			return simpleBoss;
		}

		// Token: 0x060071AB RID: 29099 RVA: 0x0025D658 File Offset: 0x0025B858
		public List<Living> FindAllTurnBossLiving()
		{
			List<Living> list = new List<Living>();
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving is SimpleBoss && turnedLiving.IsLiving;
				if (flag)
				{
					list.Add(turnedLiving);
				}
			}
			return list;
		}

		// Token: 0x060071AC RID: 29100 RVA: 0x0025D6D8 File Offset: 0x0025B8D8
		public List<SimpleBoss> FindAllTurnBoss()
		{
			List<SimpleBoss> list = new List<SimpleBoss>();
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving is SimpleBoss;
				if (flag)
				{
					list.Add(turnedLiving as SimpleBoss);
				}
			}
			return list;
		}

		// Token: 0x060071AD RID: 29101 RVA: 0x0025D754 File Offset: 0x0025B954
		public SimpleBoss[] FindLivingTurnBossWithID(int id)
		{
			List<SimpleBoss> list = new List<SimpleBoss>();
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving is SimpleBoss && turnedLiving.IsLiving && (turnedLiving as SimpleBoss).NpcInfo.ID == id;
				if (flag)
				{
					list.Add(turnedLiving as SimpleBoss);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060071AE RID: 29102 RVA: 0x0025D7F0 File Offset: 0x0025B9F0
		public SimpleBoss FindSingleSimpleBossID(int id)
		{
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving is SimpleBoss && (turnedLiving as SimpleBoss).NpcInfo.ID == id;
				if (flag)
				{
					return turnedLiving as SimpleBoss;
				}
			}
			return null;
		}

		// Token: 0x060071AF RID: 29103 RVA: 0x0025D874 File Offset: 0x0025BA74
		public SimpleNpc[] FindAllNpc()
		{
			List<SimpleNpc> list = new List<SimpleNpc>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living is SimpleNpc;
				if (flag)
				{
					list.Add(living as SimpleNpc);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060071B0 RID: 29104 RVA: 0x0025D8F4 File Offset: 0x0025BAF4
		public SimpleNpc[] FindAllNpcLiving()
		{
			List<SimpleNpc> list = new List<SimpleNpc>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living is SimpleNpc && living.IsLiving;
				if (flag)
				{
					list.Add(living as SimpleNpc);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060071B1 RID: 29105 RVA: 0x0025D97C File Offset: 0x0025BB7C
		public SimpleNpc[] GetNPCLivingWithID(int id)
		{
			List<SimpleNpc> list = new List<SimpleNpc>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living is SimpleNpc && living.IsLiving && (living as SimpleNpc).NpcInfo.ID == id;
				if (flag)
				{
					list.Add(living as SimpleNpc);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060071B2 RID: 29106 RVA: 0x0025DA18 File Offset: 0x0025BC18
		public List<Living> GetLivedNpcs(int npcId)
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living.IsLiving && living is SimpleNpc && (living as SimpleNpc).NpcInfo.ID == npcId;
				if (flag)
				{
					list.Add(living);
				}
			}
			return list;
		}

		// Token: 0x060071B3 RID: 29107 RVA: 0x0025DAAC File Offset: 0x0025BCAC
		public int GetHighDelayTurn()
		{
			new List<Living>();
			int num = int.MinValue;
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving != null && turnedLiving.Delay > num;
				if (flag)
				{
					num = turnedLiving.Delay;
				}
			}
			return num;
		}

		// Token: 0x060071B4 RID: 29108 RVA: 0x0025DB2C File Offset: 0x0025BD2C
		public int GetLowDelayTurn()
		{
			new List<Living>();
			int num = int.MaxValue;
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving != null && turnedLiving.Delay < num;
				if (flag)
				{
					num = turnedLiving.Delay;
				}
			}
			return num;
		}

		// Token: 0x060071B5 RID: 29109 RVA: 0x0025DBAC File Offset: 0x0025BDAC
		public void ClearAllChild()
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living.IsLiving && living is SimpleNpc;
				if (flag)
				{
					list.Add(living);
				}
			}
			foreach (Living living2 in list)
			{
				this.m_livings.Remove(living2);
				living2.Dispose();
				this.RemoveLiving(living2.Id);
			}
		}

		// Token: 0x060071B6 RID: 29110 RVA: 0x0025DC88 File Offset: 0x0025BE88
		public void ClearAllChildByID(int ID)
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				SimpleNpc simpleNpc = (SimpleNpc)living;
				bool flag = simpleNpc.IsLiving && simpleNpc != null && simpleNpc.NpcInfo.ID == ID;
				if (flag)
				{
					list.Add(simpleNpc);
				}
			}
			foreach (Living living2 in list)
			{
				this.m_livings.Remove(living2);
				living2.Dispose();
				this.RemoveLiving(living2.Id);
			}
		}

		// Token: 0x060071B7 RID: 29111 RVA: 0x0025DD70 File Offset: 0x0025BF70
		public void ClearAllChildByIDs(int[] ID)
		{
			List<Living> list = new List<Living>();
			for (int i = 0; i < ID.Length; i++)
			{
				foreach (Living living in this.m_livings)
				{
					SimpleNpc simpleNpc = (SimpleNpc)living;
					bool flag = simpleNpc.IsLiving && simpleNpc != null && simpleNpc.NpcInfo.ID == ID[i];
					if (flag)
					{
						list.Add(simpleNpc);
					}
				}
			}
			foreach (Living living2 in list)
			{
				this.m_livings.Remove(living2);
				living2.Dispose();
				this.RemoveLiving(living2.Id);
			}
		}

		// Token: 0x060071B8 RID: 29112 RVA: 0x0025DE70 File Offset: 0x0025C070
		public List<Player> GetAllLivingPlayersByProperties(int prop)
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = player.IsLiving && (int)player.Properties1 == 2;
					if (flag2)
					{
						list.Add(player);
					}
				}
			}
			return list;
		}

		// Token: 0x060071B9 RID: 29113 RVA: 0x0025DF30 File Offset: 0x0025C130
		public float GetNextWind()
		{
			bool frozenWind = this.FrozenWind;
			float num;
			if (frozenWind)
			{
				num = 0f;
			}
			else
			{
				int num2 = (int)(this.Wind * 10f);
				bool flag = num2 > this.m_nextWind;
				int num3;
				if (flag)
				{
					num3 = num2 - this.m_random.Next(11);
					bool flag2 = num2 <= this.m_nextWind;
					if (flag2)
					{
						this.m_nextWind = this.m_random.Next(-40, 40);
					}
				}
				else
				{
					num3 = num2 + this.m_random.Next(11);
					bool flag3 = num2 >= this.m_nextWind;
					if (flag3)
					{
						this.m_nextWind = this.m_random.Next(-40, 40);
					}
				}
				num = (float)num3 / 10f;
			}
			return num;
		}

		// Token: 0x060071BA RID: 29114 RVA: 0x0025DFF8 File Offset: 0x0025C1F8
		public void UpdateWind(float wind, bool sendToClient)
		{
			bool flag = this.m_map.wind != wind;
			if (flag)
			{
				this.m_map.wind = wind;
				if (sendToClient)
				{
					this.SendGameUpdateWind(wind);
				}
			}
		}

		// Token: 0x060071BB RID: 29115 RVA: 0x0025E03C File Offset: 0x0025C23C
		public int GetDiedPlayerCount()
		{
			int num = 0;
			foreach (Player player in this.m_players.Values)
			{
				bool flag = player.IsActive && !player.IsLiving;
				if (flag)
				{
					num++;
				}
			}
			return num;
		}

		// Token: 0x060071BC RID: 29116 RVA: 0x0025E0BC File Offset: 0x0025C2BC
		public int GetDiedNPCCount()
		{
			int num = 0;
			SimpleNpc[] array = this.FindAllNpc();
			SimpleNpc[] array2 = array;
			SimpleNpc[] array3 = array2;
			foreach (SimpleNpc simpleNpc in array3)
			{
				bool flag = !simpleNpc.IsLiving;
				if (flag)
				{
					num++;
				}
			}
			return num;
		}

		// Token: 0x060071BD RID: 29117 RVA: 0x0025E114 File Offset: 0x0025C314
		public int GetDiedBossCount()
		{
			int num = 0;
			SimpleBoss[] array = this.FindAllBoss();
			SimpleBoss[] array2 = array;
			SimpleBoss[] array3 = array2;
			foreach (SimpleBoss simpleBoss in array3)
			{
				bool flag = !simpleBoss.IsLiving;
				if (flag)
				{
					num++;
				}
			}
			return num;
		}

		// Token: 0x060071BE RID: 29118 RVA: 0x0025E16C File Offset: 0x0025C36C
		public int GetDiedCount()
		{
			return this.GetDiedNPCCount() + this.GetDiedBossCount();
		}

		// Token: 0x060071BF RID: 29119 RVA: 0x0025E18C File Offset: 0x0025C38C
		public Point GetPlayerPoint(int team)
		{
			MapPoint pvemapRandomPos = MapMgr.GetPVEMapRandomPos(this.m_map.Info.ID);
			return this.GetPlayerPoint(pvemapRandomPos, team);
		}

		// Token: 0x060071C0 RID: 29120 RVA: 0x0025E1BC File Offset: 0x0025C3BC
		protected Point GetPlayerPoint(MapPoint mapPos, int team)
		{
			List<Point> list = mapPos.PosX;
			switch (team)
			{
			case 2:
			case 4:
			case 6:
				list = mapPos.PosX1;
				break;
			}
			int num = this.m_random.Next(list.Count);
			Point point = list[num];
			list.Remove(point);
			return point;
		}

		// Token: 0x060071C1 RID: 29121 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void CheckState(int delay)
		{
		}

		// Token: 0x060071C2 RID: 29122 RVA: 0x0025E228 File Offset: 0x0025C428
		public override void ProcessData(GSPacketIn packet)
		{
			bool flag = this.m_players.ContainsKey(packet.Parameter1);
			if (flag)
			{
				Player player = this.m_players[packet.Parameter1];
				this.AddAction(new ProcessPacketAction(player, packet));
			}
		}

		// Token: 0x060071C3 RID: 29123 RVA: 0x0025E270 File Offset: 0x0025C470
		public Player FindPlayerWithId(int id)
		{
			Dictionary<int, Player> players = this.m_players;
			Player player2;
			lock (players)
			{
				bool flag2 = this.m_players.Count > 0;
				if (flag2)
				{
					foreach (Player player in this.m_players.Values)
					{
						bool flag3 = player.IsLiving && player.Id == id;
						if (flag3)
						{
							return player;
						}
					}
				}
				player2 = null;
			}
			return player2;
		}

		// Token: 0x060071C4 RID: 29124 RVA: 0x0025E330 File Offset: 0x0025C530
		public List<Player> FindRangePlayers(int minX, int maxX)
		{
			Dictionary<int, Player> players = this.m_players;
			List<Player> list2;
			lock (players)
			{
				List<Player> list = new List<Player>();
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = player.IsLiving && player.X >= minX && player.X <= maxX;
					if (flag2)
					{
						list.Add(player);
					}
				}
				list2 = list;
			}
			return list2;
		}

		// Token: 0x060071C5 RID: 29125 RVA: 0x0025E3F4 File Offset: 0x0025C5F4
		public string ListPlayersName()
		{
			List<string> list = new List<string>();
			List<Player> allLivingPlayers = this.GetAllLivingPlayers();
			foreach (Player player in allLivingPlayers)
			{
				list.Add(player.PlayerDetail.PlayerCharacter.NickName);
			}
			return string.Join(",", list);
		}

		// Token: 0x060071C6 RID: 29126 RVA: 0x0025E478 File Offset: 0x0025C678
		public List<Player> GetAllEnemyPlayers(Living living)
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			List<Player> list2;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = player.Team != living.Team;
					if (flag2)
					{
						list.Add(player);
					}
				}
				list2 = list;
			}
			return list2;
		}

		// Token: 0x060071C7 RID: 29127 RVA: 0x0025E52C File Offset: 0x0025C72C
		public Player FindNearestPlayer(int x, int y)
		{
			double num = double.MaxValue;
			Player player = null;
			foreach (Player player2 in this.m_players.Values)
			{
				bool isLiving = player2.IsLiving;
				if (isLiving)
				{
					double num2 = player2.Distance(x, y);
					bool flag = num2 < num;
					if (flag)
					{
						num = num2;
						player = player2;
					}
				}
			}
			return player;
		}

		// Token: 0x060071C8 RID: 29128 RVA: 0x0025E5C0 File Offset: 0x0025C7C0
		public Player FindFarPlayer(int x, int y)
		{
			Dictionary<int, Player> players = this.m_players;
			Player player3;
			lock (players)
			{
				double num = double.MinValue;
				Player player = null;
				foreach (Player player2 in this.m_players.Values)
				{
					bool isLiving = player2.IsLiving;
					if (isLiving)
					{
						double num2 = player2.Distance(x, y);
						bool flag2 = num2 > num;
						if (flag2)
						{
							num = num2;
							player = player2;
						}
					}
				}
				player3 = player;
			}
			return player3;
		}

		// Token: 0x060071C9 RID: 29129 RVA: 0x0025E684 File Offset: 0x0025C884
		public int FindBombPlayerX(int blowArea)
		{
			Dictionary<int, int> dictionary = new Dictionary<int, int>();
			Dictionary<int, int> dictionary2 = new Dictionary<int, int>();
			List<int> list = new List<int>();
			List<Player> allFightPlayers = this.GetAllFightPlayers();
			int num = 0;
			foreach (Player player in allFightPlayers)
			{
				bool flag = !player.IsLiving;
				if (!flag)
				{
					for (int i = 0; i < 10; i++)
					{
						int num2;
						do
						{
							num2 = this.Random.Next(player.X - blowArea, player.X + blowArea);
						}
						while (dictionary.ContainsKey(num2));
						dictionary.Add(num2, 0);
					}
				}
			}
			foreach (int num3 in dictionary.Keys)
			{
				foreach (Player player2 in allFightPlayers)
				{
					bool flag2 = player2.X > num3 - blowArea && player2.X < num3 + blowArea;
					if (flag2)
					{
						bool flag3 = dictionary2.ContainsKey(num3);
						if (flag3)
						{
							Dictionary<int, int> dictionary3 = dictionary2;
							int num4 = num3;
							int num5 = dictionary3[num4];
							dictionary3[num4] = num5 + 1;
						}
						else
						{
							dictionary2.Add(num3, 1);
						}
					}
				}
			}
			foreach (int num6 in dictionary2.Values)
			{
				bool flag4 = num6 > num;
				if (flag4)
				{
					num = num6;
				}
			}
			foreach (int num7 in dictionary2.Keys)
			{
				bool flag5 = dictionary2[num7] == num;
				if (flag5)
				{
					list.Add(num7);
				}
			}
			int num8 = this.Random.Next(0, list.Count);
			return list[num8];
		}

		// Token: 0x060071CA RID: 29130 RVA: 0x0025E908 File Offset: 0x0025CB08
		public Living FindNearestHelper(int x, int y)
		{
			double num = double.MaxValue;
			Living living = null;
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving.IsLiving && (turnedLiving is Player || turnedLiving.Config.IsHelper);
				if (flag)
				{
					double num2 = turnedLiving.Distance(x, y);
					bool flag2 = num2 < num;
					if (flag2)
					{
						num = num2;
						living = turnedLiving;
					}
				}
			}
			return living;
		}

		// Token: 0x060071CB RID: 29131 RVA: 0x0025E9B4 File Offset: 0x0025CBB4
		public SimpleNpc FindHealthyHelper()
		{
			SimpleNpc simpleNpc = null;
			foreach (Living living in this.m_livings)
			{
				SimpleNpc simpleNpc2 = (SimpleNpc)living;
				bool flag = simpleNpc2.Config.IsHelper && !simpleNpc2.Config.CanHeal;
				if (flag)
				{
					simpleNpc = simpleNpc2;
				}
			}
			return simpleNpc;
		}

		// Token: 0x060071CC RID: 29132 RVA: 0x0025EA3C File Offset: 0x0025CC3C
		public SimpleNpc FindNearestAdverseNpc(int x, int y, int camp)
		{
			double num = double.MaxValue;
			SimpleNpc simpleNpc = null;
			foreach (Living living in this.m_livings)
			{
				SimpleNpc simpleNpc2 = (SimpleNpc)living;
				bool flag = simpleNpc2.IsLiving && simpleNpc2.NpcInfo.Camp != camp;
				if (flag)
				{
					double num2 = simpleNpc2.Distance(x, y);
					bool flag2 = num2 < num;
					if (flag2)
					{
						num = num2;
						simpleNpc = simpleNpc2;
					}
				}
			}
			foreach (Living living2 in this.m_decklivings)
			{
				SimpleNpc simpleNpc3 = (SimpleNpc)living2;
				bool flag3 = simpleNpc3.IsLiving && simpleNpc3.NpcInfo.Camp != camp;
				if (flag3)
				{
					double num3 = simpleNpc3.Distance(x, y);
					bool flag4 = num3 < num;
					if (flag4)
					{
						num = num3;
						simpleNpc = simpleNpc3;
					}
				}
			}
			return simpleNpc;
		}

		// Token: 0x060071CD RID: 29133 RVA: 0x0025EB7C File Offset: 0x0025CD7C
		public Player FindRandomPlayer(Living living)
		{
			List<Player> list = new List<Player>();
			Player player = null;
			string text = "这里一个组合";
			List<Player> list2 = list;
			Console.WriteLine(text + ((list2 != null) ? list2.ToString() : null));
			foreach (Player player2 in this.m_players.Values)
			{
				Console.WriteLine("这里进入foreach");
				bool flag = player2.IsLiving && player2.Team == living.Team;
				if (flag)
				{
					Console.WriteLine("这里进入if");
					Console.WriteLine("value.IsLiving：" + player2.IsLiving.ToString());
					Console.WriteLine("value.Team：" + player2.Team.ToString());
					Console.WriteLine("living.Team：" + living.Team.ToString());
					list.Add(player2);
				}
			}
			bool flag2 = list.Count > 0;
			if (flag2)
			{
				Console.WriteLine("这里进入if 》 0");
				int num = this.Random.Next(0, list.Count);
				Console.WriteLine("index概率取0-Count" + list.Count.ToString());
				player = list[num];
				string text2 = "result";
				Player player3 = player;
				Console.WriteLine(text2 + ((player3 != null) ? player3.ToString() : null));
			}
			return player;
		}

		// Token: 0x060071CE RID: 29134 RVA: 0x0025ED18 File Offset: 0x0025CF18
		public Player FindRandomPlayer()
		{
			List<Player> list = new List<Player>();
			Player player = null;
			foreach (Player player2 in this.m_players.Values)
			{
				bool isLiving = player2.IsLiving;
				if (isLiving)
				{
					list.Add(player2);
				}
			}
			bool flag = list.Count > 0;
			if (flag)
			{
				int num = this.Random.Next(0, list.Count);
				player = list[num];
			}
			return player;
		}

		// Token: 0x060071CF RID: 29135 RVA: 0x0025EDC4 File Offset: 0x0025CFC4
		public Player[] FindRandomPlayer(int max)
		{
			List<Player> list = new List<Player>();
			bool flag = this.m_players.Count > 0;
			if (flag)
			{
				List<Player> list2 = new List<Player>();
				foreach (Player player in this.m_players.Values)
				{
					bool isLiving = player.IsLiving;
					if (isLiving)
					{
						list2.Add(player);
					}
				}
				for (int i = 0; i < max; i++)
				{
					int num = this.Random.Next(0, list2.Count);
					list.Add(list2[num]);
					list2.RemoveAt(num);
					bool flag2 = list2.Count <= 0;
					if (flag2)
					{
						break;
					}
				}
			}
			return list.ToArray();
		}

		// Token: 0x060071D0 RID: 29136 RVA: 0x0025EEBC File Offset: 0x0025D0BC
		public int FindlivingbyDir(Living npc)
		{
			int num = 0;
			int num2 = 0;
			foreach (Player player in this.m_players.Values)
			{
				bool isLiving = player.IsLiving;
				if (isLiving)
				{
					bool flag = player.X > npc.X;
					if (flag)
					{
						num2++;
					}
					else
					{
						num++;
					}
				}
			}
			bool flag2 = num2 > num;
			int num3;
			if (flag2)
			{
				num3 = 1;
			}
			else
			{
				bool flag3 = num2 < num;
				if (flag3)
				{
					num3 = -1;
				}
				else
				{
					num3 = -npc.Direction;
				}
			}
			return num3;
		}

		// Token: 0x060071D1 RID: 29137 RVA: 0x0025EF74 File Offset: 0x0025D174
		public PhysicalObj[] FindPhysicalObjByName(string name)
		{
			List<PhysicalObj> list = new List<PhysicalObj>();
			foreach (PhysicalObj physicalObj in this.m_map.GetAllPhysicalObjSafe())
			{
				bool flag = physicalObj.Name == name;
				if (flag)
				{
					list.Add(physicalObj);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060071D2 RID: 29138 RVA: 0x0025EFF8 File Offset: 0x0025D1F8
		public PhysicalObj[] FindPhysicalObjByName(string name, bool CanPenetrate)
		{
			List<PhysicalObj> list = new List<PhysicalObj>();
			foreach (PhysicalObj physicalObj in this.m_map.GetAllPhysicalObjSafe())
			{
				bool flag = physicalObj.Name == name && physicalObj.CanPenetrate == CanPenetrate;
				if (flag)
				{
					list.Add(physicalObj);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060071D3 RID: 29139 RVA: 0x0025F088 File Offset: 0x0025D288
		public Player GetFrostPlayerRadom()
		{
			List<Player> allFightPlayers = this.GetAllFightPlayers();
			List<Player> list = new List<Player>();
			foreach (Player player in allFightPlayers)
			{
				bool isFrost = player.IsFrost;
				if (isFrost)
				{
					list.Add(player);
				}
			}
			bool flag = list.Count > 0;
			Player player2;
			if (flag)
			{
				int num = this.Random.Next(0, list.Count);
				player2 = list.ElementAt(num);
			}
			else
			{
				player2 = null;
			}
			return player2;
		}

		// Token: 0x060071D4 RID: 29140 RVA: 0x0025F130 File Offset: 0x0025D330
		public List<Player> GetAllTeamPlayers(Living living)
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = player.Team == living.Team;
					if (flag2)
					{
						list.Add(player);
					}
				}
			}
			return list;
		}

		// Token: 0x060071D5 RID: 29141 RVA: 0x0025F1E4 File Offset: 0x0025D3E4
		public List<Living> GetBossLivings()
		{
			List<Living> list = new List<Living>();
			bool flag = this.m_Bosses == null;
			List<Living> list2;
			if (flag)
			{
				list2 = list;
			}
			else
			{
				foreach (SimpleBoss simpleBoss in this.m_Bosses)
				{
					bool flag2 = simpleBoss != null && simpleBoss.IsLiving;
					if (flag2)
					{
						list.Add(simpleBoss);
					}
				}
				list2 = list;
			}
			return list2;
		}

		// Token: 0x060071D6 RID: 29142 RVA: 0x0025F274 File Offset: 0x0025D474
		public List<Living> FindAppointDeGreeNpc(int degree)
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living.IsLiving && living.Degree == degree;
				if (flag)
				{
					list.Add(living);
				}
			}
			foreach (Living living2 in this.m_decklivings)
			{
				bool flag2 = living2.IsLiving && living2.Degree == degree;
				if (flag2)
				{
					list.Add(living2);
				}
			}
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag3 = turnedLiving.IsLiving && turnedLiving.Degree == degree;
				if (flag3)
				{
					list.Add(turnedLiving);
				}
			}
			return list;
		}

		// Token: 0x060071D7 RID: 29143 RVA: 0x00068C5C File Offset: 0x00066E5C
		public virtual bool TakeCard(Player player, bool isSysTake)
		{
			return false;
		}

		// Token: 0x060071D8 RID: 29144 RVA: 0x00068C5C File Offset: 0x00066E5C
		public virtual bool TakeCard(Player player, int index, bool isSysTake)
		{
			return false;
		}

		// Token: 0x060071D9 RID: 29145 RVA: 0x0002A993 File Offset: 0x00028B93
		public override void Pause(int time)
		{
			this.m_passTick = Math.Max(this.m_passTick, TickHelper.GetTickCount() + (long)time);
		}

		// Token: 0x060071DA RID: 29146 RVA: 0x0002A9AF File Offset: 0x00028BAF
		public override void Resume()
		{
			this.m_passTick = 0L;
		}

		// Token: 0x060071DB RID: 29147 RVA: 0x0025F3C4 File Offset: 0x0025D5C4
		public void AddAction(IAction action)
		{
			ArrayList actions = this.m_actions;
			lock (actions)
			{
				this.m_actions.Add(action);
			}
		}

		// Token: 0x060071DC RID: 29148 RVA: 0x0025F410 File Offset: 0x0025D610
		public void AddAction(ArrayList actions)
		{
			ArrayList actions2 = this.m_actions;
			lock (actions2)
			{
				this.m_actions.AddRange(actions);
			}
		}

		// Token: 0x060071DD RID: 29149 RVA: 0x0002A9BA File Offset: 0x00028BBA
		public void ClearWaitTimer()
		{
			this.m_waitTimer = 0L;
		}

		// Token: 0x060071DE RID: 29150 RVA: 0x0002A9C5 File Offset: 0x00028BC5
		public void WaitTime(int delay)
		{
			this.m_waitTimer = Math.Max(this.m_waitTimer, TickHelper.GetTickCount() + (long)delay);
			this.m_lastWaitTimer = this.m_waitTimer;
		}

		// Token: 0x060071DF RID: 29151 RVA: 0x0025F45C File Offset: 0x0025D65C
		public long GetWaitTimer()
		{
			return this.m_waitTimer;
		}

		// Token: 0x060071E0 RID: 29152 RVA: 0x0025F474 File Offset: 0x0025D674
		public int GetWaitTimerLeft()
		{
			bool flag = this.m_lastWaitTimer <= 0L;
			int num;
			if (flag)
			{
				num = 0;
			}
			else
			{
				long num2 = ((TickHelper.GetTickCount() > this.m_lastWaitTimer) ? (TickHelper.GetTickCount() - this.m_lastWaitTimer) : (this.m_lastWaitTimer - TickHelper.GetTickCount()));
				bool flag2 = num2 > 10000L;
				if (flag2)
				{
					num = 1000;
				}
				else
				{
					num = (int)num2;
				}
			}
			return num;
		}

		// Token: 0x060071E1 RID: 29153 RVA: 0x0025F4DC File Offset: 0x0025D6DC
		public void Update(long tick)
		{
			bool flag = this.m_passTick >= tick;
			if (!flag)
			{
				this.m_lifeTime++;
				ArrayList actions = this.m_actions;
				ArrayList arrayList;
				lock (actions)
				{
					arrayList = (ArrayList)this.m_actions.Clone();
					this.m_actions.Clear();
				}
				bool flag3 = arrayList == null || this.GameState == eGameState.Stopped;
				if (!flag3)
				{
					this.CurrentActionCount = arrayList.Count;
					bool flag4 = arrayList.Count > 0;
					if (flag4)
					{
						ArrayList arrayList2 = new ArrayList();
						foreach (object obj in arrayList)
						{
							IAction action = (IAction)obj;
							try
							{
								action.Execute(this, tick);
								bool flag5 = !action.IsFinished(this, tick);
								if (flag5)
								{
									arrayList2.Add(action);
								}
							}
							catch (Exception ex)
							{
								BaseGame.log.Error("Map update error:", ex);
							}
						}
						this.AddAction(arrayList2);
					}
					else
					{
						bool flag6 = this.m_waitTimer < tick;
						if (flag6)
						{
							this.CheckState(0);
						}
					}
				}
			}
		}

		// Token: 0x060071E2 RID: 29154 RVA: 0x0025F65C File Offset: 0x0025D85C
		public List<Player> GetAllTarget(Living living)
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = player.IsLiving && player.Id != living.Id;
					if (flag2)
					{
						list.Add(player);
					}
				}
			}
			return list;
		}

		// Token: 0x060071E3 RID: 29155 RVA: 0x0025F71C File Offset: 0x0025D91C
		public List<Player> GetAllFightPlayers()
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				list.AddRange(this.m_players.Values);
			}
			return list;
		}

		// Token: 0x060071E4 RID: 29156 RVA: 0x0025F778 File Offset: 0x0025D978
		public List<Player> GetAllFightingPlayers()
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = !player.PlayerDetail.PlayerCharacter.isViewer;
					if (flag2)
					{
						list.Add(player);
					}
				}
			}
			return list;
		}

		// Token: 0x060071E5 RID: 29157 RVA: 0x0025F830 File Offset: 0x0025DA30
		public List<Player> GetAllLivingPlayers()
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool isLiving = player.IsLiving;
					if (isLiving)
					{
						list.Add(player);
					}
				}
			}
			return list;
		}

		// Token: 0x060071E6 RID: 29158 RVA: 0x0025F8DC File Offset: 0x0025DADC
		public List<Player> GetAllLivingPlayerOnRange(int minX, int maxX)
		{
			List<Player> list = new List<Player>();
			foreach (Player player in this.GetAllLivingPlayers())
			{
				bool flag = player.X >= minX && player.X <= maxX;
				if (flag)
				{
					list.Add(player);
				}
			}
			return list;
		}

		// Token: 0x060071E7 RID: 29159 RVA: 0x0025F960 File Offset: 0x0025DB60
		public bool GetSameTeam()
		{
			bool flag = false;
			Player[] allPlayers = this.GetAllPlayers();
			Player[] array = allPlayers;
			Player[] array2 = array;
			Player[] array3 = array2;
			foreach (Player player in array3)
			{
				bool flag2 = player.Team == allPlayers[0].Team;
				if (!flag2)
				{
					flag = false;
					break;
				}
				flag = true;
			}
			return flag;
		}

		// Token: 0x060071E8 RID: 29160 RVA: 0x0025F9C8 File Offset: 0x0025DBC8
		public Player[] GetAllPlayers()
		{
			return this.GetAllFightPlayers().ToArray();
		}

		// Token: 0x060071E9 RID: 29161 RVA: 0x0025F9E8 File Offset: 0x0025DBE8
		public Player GetPlayer(IGamePlayer gp)
		{
			Player player = null;
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player2 in this.m_players.Values)
				{
					bool flag2 = player2.PlayerDetail == gp;
					if (flag2)
					{
						player = player2;
						break;
					}
				}
			}
			return player;
		}

		// Token: 0x060071EA RID: 29162 RVA: 0x0025FA8C File Offset: 0x0025DC8C
		public int GetPlayerCount()
		{
			return this.GetAllFightPlayers().Count;
		}

		// Token: 0x060071EB RID: 29163 RVA: 0x0002A9ED File Offset: 0x00028BED
		public virtual void SendToAll(GSPacketIn pkg)
		{
			this.SendToAll(pkg, null);
		}

		// Token: 0x060071EC RID: 29164 RVA: 0x0025FAAC File Offset: 0x0025DCAC
		public virtual void SendToAll(GSPacketIn pkg, IGamePlayer except)
		{
			bool flag = pkg.Parameter2 == 0;
			if (flag)
			{
				pkg.Parameter2 = this.LifeTime;
			}
			List<Player> allFightPlayers = this.GetAllFightPlayers();
			foreach (Player player in allFightPlayers)
			{
				bool flag2 = player.IsActive && player.PlayerDetail != except;
				if (flag2)
				{
					player.PlayerDetail.SendTCP(pkg);
				}
			}
		}

		// Token: 0x060071ED RID: 29165 RVA: 0x0002A9F9 File Offset: 0x00028BF9
		public virtual void SendToTeam(GSPacketIn pkg, int team)
		{
			this.SendToTeam(pkg, team, null);
		}

		// Token: 0x060071EE RID: 29166 RVA: 0x0025FB48 File Offset: 0x0025DD48
		public virtual void SendToTeam(GSPacketIn pkg, int team, IGamePlayer except)
		{
			List<Player> allFightPlayers = this.GetAllFightPlayers();
			foreach (Player player in allFightPlayers)
			{
				bool flag = player.IsActive && player.PlayerDetail != except && player.Team == team;
				if (flag)
				{
					player.PlayerDetail.SendTCP(pkg);
				}
			}
		}

		// Token: 0x060071EF RID: 29167 RVA: 0x0025FBCC File Offset: 0x0025DDCC
		public int getTurnTime()
		{
			int timeType = this.m_timeType;
			if (!true)
			{
			}
			int num;
			switch (timeType)
			{
			case 1:
				num = 8;
				break;
			case 2:
				num = 10;
				break;
			case 3:
				num = 12;
				break;
			case 4:
				num = 16;
				break;
			case 5:
				num = 21;
				break;
			case 6:
				num = 31;
				break;
			default:
				num = -1;
				break;
			}
			if (!true)
			{
			}
			return num;
		}

		// Token: 0x060071F0 RID: 29168 RVA: 0x0002AA06 File Offset: 0x00028C06
		public void AddTempPoint(int x, int y)
		{
			this.m_tempPoints.Add(new Point(x, y));
		}

		// Token: 0x060071F1 RID: 29169 RVA: 0x0002AA1C File Offset: 0x00028C1C
		public void AddTempGhostPoint(int x, int y)
		{
			this.m_tempGhostPoints.Add(new Point(x, y));
		}

		// Token: 0x060071F2 RID: 29170 RVA: 0x0025FC50 File Offset: 0x0025DE50
		public SimpleBox AddBox(ItemInfo item, Point pos, bool sendToClient)
		{
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			SimpleBox simpleBox = new SimpleBox(physicalId, "1", item, 1);
			simpleBox.SetXY(pos);
			this.AddPhysicalObj(simpleBox, sendToClient);
			return this.AddBox(simpleBox, sendToClient);
		}

		// Token: 0x060071F3 RID: 29171 RVA: 0x0025FC9C File Offset: 0x0025DE9C
		public SimpleBox AddBox(SimpleBox box, bool sendToClient)
		{
			this.m_tempBox.Add(box);
			this.AddPhysicalObj(box, sendToClient);
			return box;
		}

		// Token: 0x060071F4 RID: 29172 RVA: 0x0025FCC8 File Offset: 0x0025DEC8
		public SimpleBox AddGhostBox(Point pos, int type)
		{
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			SimpleBox simpleBox = new SimpleBox(physicalId, "1", null, type);
			simpleBox.SetXY(pos);
			return this.AddGhostBox(simpleBox);
		}

		// Token: 0x060071F5 RID: 29173 RVA: 0x0025FD08 File Offset: 0x0025DF08
		public SimpleBox AddGhostBox(SimpleBox box)
		{
			this.m_tempBox.Add(box);
			this.AddGhostBoxObj(box);
			return box;
		}

		// Token: 0x060071F6 RID: 29174 RVA: 0x0025FD30 File Offset: 0x0025DF30
		public void CheckBox()
		{
			List<SimpleBox> list = new List<SimpleBox>();
			foreach (SimpleBox simpleBox in this.m_tempBox)
			{
				bool flag = !simpleBox.IsLiving;
				if (flag)
				{
					list.Add(simpleBox);
				}
			}
			foreach (SimpleBox simpleBox2 in list)
			{
				this.m_tempBox.Remove(simpleBox2);
				this.RemovePhysicalObj(simpleBox2, true);
			}
		}

		// Token: 0x060071F7 RID: 29175 RVA: 0x0025FDF4 File Offset: 0x0025DFF4
		public int CheckGhostBox()
		{
			List<SimpleBox> list = new List<SimpleBox>();
			foreach (SimpleBox simpleBox in this.m_tempBox)
			{
				bool flag = simpleBox.Type > 1;
				if (flag)
				{
					list.Add(simpleBox);
				}
			}
			return list.Count<SimpleBox>();
		}

		// Token: 0x060071F8 RID: 29176 RVA: 0x0025FE70 File Offset: 0x0025E070
		public void CreateGhostPoints()
		{
			int backroundHeight = this.m_map.Info.BackroundHeight;
			int backroundWidht = this.m_map.Info.BackroundWidht;
			Point point = new Point(backroundWidht / 2, backroundHeight / 2);
			int num = 180;
			this.m_tempGhostPoints = this.DrawCirclePoints(backroundHeight, 30, (double)(backroundHeight - num), point);
		}

		// Token: 0x060071F9 RID: 29177 RVA: 0x0025FEC8 File Offset: 0x0025E0C8
		public List<Point> DrawCirclePoints(int points, int dis, double radius, Point center)
		{
			List<Point> list = new List<Point>();
			double num = 6.283185307179586 / (double)points;
			for (double num2 = radius; num2 > (double)dis; num2 -= (double)dis)
			{
				for (int i = 0; i < points; i++)
				{
					double num3 = num * (double)i;
					int num4 = (int)((double)center.X + num2 * Math.Cos(num3));
					int num5 = (int)((double)center.Y + num2 * Math.Sin(num3));
					Point point = new Point(num4, num5);
					list.Add(point);
				}
			}
			return list;
		}

		// Token: 0x060071FA RID: 29178 RVA: 0x0025FF60 File Offset: 0x0025E160
		public List<SimpleBox> CreateBox()
		{
			int num = this.m_players.Count + 2;
			int num2 = 0;
			List<ItemInfo> list = null;
			List<SimpleBox> list2 = new List<SimpleBox>();
			bool flag = this.CurrentTurnTotalDamage > 0;
			if (flag)
			{
				num2 = this.m_random.Next(1, 3);
				bool flag2 = this.m_tempBox.Count + num2 > num;
				if (flag2)
				{
					num2 = num - this.m_tempBox.Count;
				}
				bool flag3 = num2 > 0;
				if (flag3)
				{
					DropInventory.BoxDrop(this.m_roomType, ref list);
				}
			}
			int diedPlayerCount = this.GetDiedPlayerCount();
			int num3 = 0;
			bool flag4 = diedPlayerCount > 0;
			if (flag4)
			{
				num3 = this.m_random.Next(diedPlayerCount);
				bool flag5 = this.m_tempGhostPoints.Count < num;
				if (flag5)
				{
					this.CreateGhostPoints();
				}
				for (int i = 0; i < this.m_tempGhostPoints.Count; i++)
				{
					int num4 = this.m_random.Next(this.m_tempGhostPoints.Count);
					Point point = this.m_tempGhostPoints[num4];
					this.m_tempGhostPoints[num4] = this.m_tempGhostPoints[i];
					this.m_tempGhostPoints[i] = point;
				}
				int num5 = diedPlayerCount + num - this.CheckGhostBox();
				bool flag6 = this.m_tempGhostPoints.Count > num5;
				if (flag6)
				{
					int[] array = new int[] { 2, 3 };
					for (int j = 0; j < num5; j++)
					{
						int num6 = this.m_random.Next(array.Length);
						int num7 = this.m_random.Next(this.m_tempGhostPoints.Count);
						list2.Add(this.AddGhostBox(this.m_tempGhostPoints[num7], array[num6]));
					}
				}
			}
			bool flag7 = this.m_tempBox.Count + num2 + num3 > num;
			if (flag7)
			{
				num3 = num - this.m_tempBox.Count - num2;
			}
			bool flag8 = num3 > 0;
			if (flag8)
			{
			}
			bool flag9 = list != null;
			if (flag9)
			{
				for (int k = 0; k < this.m_tempPoints.Count; k++)
				{
					int num8 = this.m_random.Next(this.m_tempPoints.Count);
					Point point2 = this.m_tempPoints[num8];
					this.m_tempPoints[num8] = this.m_tempPoints[k];
					this.m_tempPoints[k] = point2;
				}
				int num9 = Math.Min(list.Count, this.m_tempPoints.Count);
				for (int l = 0; l < num9; l++)
				{
					list2.Add(this.AddBox(list[l], this.m_tempPoints[l], false));
				}
			}
			this.m_tempPoints.Clear();
			this.m_tempGhostPoints.Clear();
			return list2;
		}

		// Token: 0x060071FB RID: 29179 RVA: 0x00260264 File Offset: 0x0025E464
		public void AddLoadingFile(int type, string file, string className)
		{
			bool flag = file != null && className != null;
			if (flag)
			{
				this.m_loadingFiles.Add(new LoadingFileInfo(type, file, className));
			}
		}

		// Token: 0x060071FC RID: 29180 RVA: 0x0002AA32 File Offset: 0x00028C32
		public void ClearLoadingFiles()
		{
			this.m_loadingFiles.Clear();
		}

		// Token: 0x060071FD RID: 29181 RVA: 0x00005683 File Offset: 0x00003883
		public void AfterUseItem(ItemInfo item)
		{
		}

		// Token: 0x060071FE RID: 29182 RVA: 0x00260298 File Offset: 0x0025E498
		public byte GetVane(int Wind, int param)
		{
			int num = Math.Abs(Wind);
			if (!true)
			{
			}
			byte b;
			if (param != 1)
			{
				if (param != 3)
				{
					b = 0;
				}
				else
				{
					b = WindMgr.GetWindID(num, 3);
				}
			}
			else
			{
				b = WindMgr.GetWindID(num, 1);
			}
			if (!true)
			{
			}
			return b;
		}

		// Token: 0x060071FF RID: 29183 RVA: 0x002602F8 File Offset: 0x0025E4F8
		public void VaneLoading(Player p)
		{
			List<WindInfo> wind = WindMgr.GetWind();
			foreach (WindInfo windInfo in wind)
			{
				this.SendGameWindPic(p, (byte)windInfo.WindID, windInfo.WindPic);
			}
		}

		// Token: 0x06007200 RID: 29184 RVA: 0x0002AA41 File Offset: 0x00028C41
		internal void SendCreateGame()
		{
			this.SendCreateGame(null);
		}

		// Token: 0x06007201 RID: 29185 RVA: 0x00260360 File Offset: 0x0025E560
		internal void SendCreateGame(Player player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(101);
			gspacketIn.WriteInt((int)((byte)this.m_roomType));
			gspacketIn.WriteInt((int)((byte)this.m_gameType));
			gspacketIn.WriteInt(this.m_timeType);
			List<Player> allFightPlayers = this.GetAllFightPlayers();
			gspacketIn.WriteInt(allFightPlayers.Count);
			foreach (Player player2 in allFightPlayers)
			{
				IGamePlayer playerDetail = player2.PlayerDetail;
				gspacketIn.WriteInt(playerDetail.AreaID);
				gspacketIn.WriteString(playerDetail.AreaName);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ID);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.NickName);
				gspacketIn.WriteBoolean(playerDetail.PlayerCharacter.isViewer);
				gspacketIn.WriteByte(playerDetail.PlayerCharacter.TypeVIP);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.VIPLevel);
				gspacketIn.WriteBoolean(playerDetail.PlayerCharacter.Sex);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Hide);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Style);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Colors);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Skin);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Grade);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Repute);
				bool flag = playerDetail.MainWeapon.GoldValidDate();
				if (flag)
				{
					gspacketIn.WriteInt(playerDetail.MainWeapon.GoldEquip.TemplateID);
				}
				else
				{
					gspacketIn.WriteInt(playerDetail.MainWeapon.TemplateID);
				}
				gspacketIn.WriteInt(playerDetail.MainWeapon.RefineryLevel);
				gspacketIn.WriteString(playerDetail.MainWeapon.Template.Pic);
				gspacketIn.WriteDateTime(DateTime.Now);
				gspacketIn.WriteInt((playerDetail.SecondWeapon != null) ? playerDetail.SecondWeapon.TemplateID : 0);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Nimbus);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ConsortiaID);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.ConsortiaName);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.badgeID);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ConsortiaLevel);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ConsortiaRepute);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Win);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Total);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.FightPower);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ApprenticeshipState);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.MasterID);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.MasterOrApprentices);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.AchievementPoint);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Honor);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Offer);
				gspacketIn.WriteBoolean(playerDetail.PlayerCharacter.DailyLeagueFirst);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.DailyLeagueLastScore);
				gspacketIn.WriteBoolean(playerDetail.PlayerCharacter.IsMarried);
				bool isMarried = playerDetail.PlayerCharacter.IsMarried;
				if (isMarried)
				{
					gspacketIn.WriteInt(playerDetail.PlayerCharacter.SpouseID);
					gspacketIn.WriteString(playerDetail.PlayerCharacter.SpouseName);
				}
				gspacketIn.WriteInt((int)playerDetail.GMExperienceRate);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt((int)playerDetail.GMOfferRate);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt((int)playerDetail.GMRichesRate);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt(player2.Team);
				gspacketIn.WriteInt(player2.Id);
				gspacketIn.WriteInt(player2.MaxBlood);
				bool flag2 = player2.UserPet == null;
				if (flag2)
				{
					gspacketIn.WriteInt(0);
				}
				else
				{
					gspacketIn.WriteInt(1);
					gspacketIn.WriteInt(player2.UserPet.Place);
					gspacketIn.WriteInt(player2.UserPet.TemplateID);
					gspacketIn.WriteInt(player2.UserPet.ID);
					gspacketIn.WriteString(player2.UserPet.Name);
					gspacketIn.WriteInt(player2.UserPet.UserID);
					gspacketIn.WriteInt(player2.UserPet.Level);
					string[] array = player2.UserPet.SkillEquip.Split(new char[] { '|' });
					gspacketIn.WriteInt(array.Length);
					string[] array2 = array;
					string[] array3 = array2;
					string[] array4 = array3;
					foreach (string text in array4)
					{
						gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[1]));
						gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[0]));
					}
				}
			}
			bool flag3 = player == null;
			if (flag3)
			{
				this.SendToAll(gspacketIn);
			}
			else
			{
				player.PlayerDetail.SendTCP(gspacketIn);
			}
		}

		// Token: 0x06007202 RID: 29186 RVA: 0x002608F4 File Offset: 0x0025EAF4
		internal void SendOpenSelectLeaderWindow(int maxTime)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(102);
			gspacketIn.WriteInt(maxTime);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007203 RID: 29187 RVA: 0x00260924 File Offset: 0x0025EB24
		internal void SendSkipNext(Player player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(12);
			player.PlayerDetail.SendTCP(gspacketIn);
		}

		// Token: 0x06007204 RID: 29188 RVA: 0x00260950 File Offset: 0x0025EB50
		internal void SendStartLoading(int maxTime)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(103);
			gspacketIn.WriteInt(maxTime);
			gspacketIn.WriteInt(this.m_map.Info.ID);
			gspacketIn.WriteInt(this.m_loadingFiles.Count);
			foreach (LoadingFileInfo loadingFileInfo in this.m_loadingFiles)
			{
				gspacketIn.WriteInt(loadingFileInfo.Type);
				gspacketIn.WriteString(loadingFileInfo.Path);
				gspacketIn.WriteString(loadingFileInfo.ClassName);
			}
			bool flag = this.IsSpecialPVE();
			if (flag)
			{
				gspacketIn.WriteInt(0);
			}
			else
			{
				GameNeedPetSkillInfo[] gameNeedPetSkill = PetMgr.GetGameNeedPetSkill();
				gspacketIn.WriteInt(gameNeedPetSkill.Length);
				GameNeedPetSkillInfo[] array = gameNeedPetSkill;
				GameNeedPetSkillInfo[] array2 = array;
				GameNeedPetSkillInfo[] array3 = array2;
				foreach (GameNeedPetSkillInfo gameNeedPetSkillInfo in array3)
				{
					gspacketIn.WriteString(gameNeedPetSkillInfo.Pic.ToString());
					gspacketIn.WriteString(gameNeedPetSkillInfo.EffectPic);
				}
			}
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007205 RID: 29189 RVA: 0x00260A90 File Offset: 0x0025EC90
		internal void SendAddPhysicalObj(PhysicalObj obj)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(48);
			gspacketIn.WriteInt(obj.Id);
			gspacketIn.WriteInt(obj.Type);
			gspacketIn.WriteInt(obj.X);
			gspacketIn.WriteInt(obj.Y);
			gspacketIn.WriteString(obj.Model);
			gspacketIn.WriteString(obj.CurrentAction);
			gspacketIn.WriteInt(obj.Scale);
			gspacketIn.WriteInt(obj.Scale);
			gspacketIn.WriteInt(obj.Rotation);
			gspacketIn.WriteInt(obj.phyBringToFront);
			gspacketIn.WriteInt(obj.typeEffect);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007206 RID: 29190 RVA: 0x00260B48 File Offset: 0x0025ED48
		internal void SendAddPhysicalTip(PhysicalObj obj)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(68);
			gspacketIn.WriteInt(obj.Id);
			gspacketIn.WriteInt(obj.Type);
			gspacketIn.WriteInt(obj.X);
			gspacketIn.WriteInt(obj.Y);
			gspacketIn.WriteString(obj.Model);
			gspacketIn.WriteString(obj.CurrentAction);
			gspacketIn.WriteInt(obj.Scale);
			gspacketIn.WriteInt(obj.Rotation);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007207 RID: 29191 RVA: 0x0002AA4C File Offset: 0x00028C4C
		internal void SendPhysicalObjFocus(Physics obj, int type)
		{
			this.SendPhysicalObjFocus(obj.X, obj.Y, type);
		}

		// Token: 0x06007208 RID: 29192 RVA: 0x00260BD8 File Offset: 0x0025EDD8
		internal void SendPhysicalObjFocus(int x, int y, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(62);
			gspacketIn.WriteInt(type);
			gspacketIn.WriteInt(x);
			gspacketIn.WriteInt(y);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007209 RID: 29193 RVA: 0x00260C18 File Offset: 0x0025EE18
		internal void SendPhysicalObjPlayAction(PhysicalObj obj)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(66);
			gspacketIn.WriteInt(obj.Id);
			gspacketIn.WriteString(obj.CurrentAction);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600720A RID: 29194 RVA: 0x00260C5C File Offset: 0x0025EE5C
		internal void SendRemovePhysicalObj(PhysicalObj obj)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(53);
			gspacketIn.WriteInt(obj.Id);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600720B RID: 29195 RVA: 0x00260C90 File Offset: 0x0025EE90
		internal void SendRemoveLiving(int id)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(53);
			gspacketIn.WriteInt(id);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600720C RID: 29196 RVA: 0x00260CC0 File Offset: 0x0025EEC0
		internal void SendLivingBoltMove(Living living, int toX, int toY, string action)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(72);
			gspacketIn.WriteInt(toX);
			gspacketIn.WriteInt(toY);
			gspacketIn.WriteString((!string.IsNullOrEmpty(action)) ? action : "");
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600720D RID: 29197 RVA: 0x00260D24 File Offset: 0x0025EF24
		internal void SendAddLiving(Living living)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(64);
			gspacketIn.WriteByte((byte)living.Type);
			gspacketIn.WriteInt(living.Id);
			gspacketIn.WriteString(living.Name);
			gspacketIn.WriteString(living.ModelId);
			gspacketIn.WriteString(living.ActionStr);
			gspacketIn.WriteInt(living.X);
			gspacketIn.WriteInt(living.Y);
			gspacketIn.WriteInt(living.Blood);
			gspacketIn.WriteInt(living.MaxBlood);
			gspacketIn.WriteInt(living.Team);
			gspacketIn.WriteByte((byte)living.Direction);
			gspacketIn.WriteByte(living.Config.isBotom);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600720E RID: 29198 RVA: 0x00260DFC File Offset: 0x0025EFFC
		internal void SendPlayerMove(Player player, int type, int x, int y, byte dir, bool isLiving)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(9);
			gspacketIn.WriteByte((byte)type);
			gspacketIn.WriteInt(x);
			gspacketIn.WriteInt(y);
			gspacketIn.WriteByte(dir);
			gspacketIn.WriteBoolean(isLiving);
			bool flag = type == 2;
			if (flag)
			{
				gspacketIn.WriteInt(this.m_tempBox.Count);
				foreach (SimpleBox simpleBox in this.m_tempBox)
				{
					gspacketIn.WriteInt(simpleBox.X);
					gspacketIn.WriteInt(simpleBox.Y);
				}
			}
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600720F RID: 29199 RVA: 0x00260EDC File Offset: 0x0025F0DC
		internal void SendLivingMoveTo(Living living, int fromX, int fromY, int toX, int toY, string action, int speed, string sAction)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(55);
			gspacketIn.WriteInt(fromX);
			gspacketIn.WriteInt(fromY);
			gspacketIn.WriteInt(toX);
			gspacketIn.WriteInt(toY);
			gspacketIn.WriteInt(speed);
			gspacketIn.WriteString((!string.IsNullOrEmpty(action)) ? action : "");
			gspacketIn.WriteString(sAction);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007210 RID: 29200 RVA: 0x00260F64 File Offset: 0x0025F164
		internal void SendLivingSay(Living living, string msg, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(59);
			gspacketIn.WriteString(msg);
			gspacketIn.WriteInt(type);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007211 RID: 29201 RVA: 0x00260FB0 File Offset: 0x0025F1B0
		internal void SendLivingFall(Living living, int toX, int toY, int speed, string action, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(56);
			gspacketIn.WriteInt(toX);
			gspacketIn.WriteInt(toY);
			gspacketIn.WriteInt(speed);
			gspacketIn.WriteString((!string.IsNullOrEmpty(action)) ? action : "");
			gspacketIn.WriteInt(type);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007212 RID: 29202 RVA: 0x00261028 File Offset: 0x0025F228
		internal void SendLivingJump(Living living, int toX, int toY, int speed, string action, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(57);
			gspacketIn.WriteInt(toX);
			gspacketIn.WriteInt(toY);
			gspacketIn.WriteInt(speed);
			gspacketIn.WriteString((!string.IsNullOrEmpty(action)) ? action : "");
			gspacketIn.WriteInt(type);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007213 RID: 29203 RVA: 0x002610A0 File Offset: 0x0025F2A0
		internal void SendLivingBeat(Living living, Living target, int totalDemageAmount, string action, int livingCount, int attackEffect)
		{
			int num = 0;
			bool flag = target is Player;
			if (flag)
			{
				Player player = target as Player;
				num = player.Dander;
			}
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(58);
			gspacketIn.WriteString((!string.IsNullOrEmpty(action)) ? action : "");
			gspacketIn.WriteInt(livingCount);
			for (int i = 1; i <= livingCount; i++)
			{
				gspacketIn.WriteInt(target.Id);
				gspacketIn.WriteInt(totalDemageAmount);
				gspacketIn.WriteInt(target.Blood);
				gspacketIn.WriteInt(num);
				gspacketIn.WriteInt(attackEffect);
			}
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007214 RID: 29204 RVA: 0x0026116C File Offset: 0x0025F36C
		internal void SendLivingPlayMovie(Living living, string action)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(60);
			gspacketIn.WriteString(action);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007215 RID: 29205 RVA: 0x002611B0 File Offset: 0x0025F3B0
		internal void SendPlayerInfoInGame(Player living)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.Parameter2 = this.LifeTime;
			gspacketIn.WriteByte(120);
			gspacketIn.WriteInt(living.PlayerDetail.AreaID);
			gspacketIn.WriteInt(living.PlayerDetail.PlayerCharacter.ID);
			gspacketIn.WriteInt(living.Team);
			gspacketIn.WriteInt(living.Id);
			gspacketIn.WriteInt(living.MaxBlood);
			gspacketIn.WriteBoolean(living.Ready);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007216 RID: 29206 RVA: 0x00261244 File Offset: 0x0025F444
		internal void SendGameUpdateHealth(Living player, int type, int value)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(11);
			gspacketIn.WriteByte((byte)type);
			gspacketIn.WriteInt(player.Blood);
			gspacketIn.WriteInt(value);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007217 RID: 29207 RVA: 0x0026129C File Offset: 0x0025F49C
		internal void SendGameUpdateDander(TurnedLiving player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(14);
			gspacketIn.WriteInt(player.Dander);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007218 RID: 29208 RVA: 0x002612E4 File Offset: 0x0025F4E4
		internal void SendGameUpdateFrozenState(Living player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(33);
			gspacketIn.WriteBoolean(player.IsFrost);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007219 RID: 29209 RVA: 0x0026132C File Offset: 0x0025F52C
		internal void SendGameUpdateNoHoleState(Living player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(82);
			gspacketIn.WriteBoolean(player.IsNoHole);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600721A RID: 29210 RVA: 0x00261374 File Offset: 0x0025F574
		internal void SendGameUpdateHideState(Living player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(35);
			gspacketIn.WriteBoolean(player.IsHide);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600721B RID: 29211 RVA: 0x002613BC File Offset: 0x0025F5BC
		internal void SendGameUpdateSealState(Living player, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(18);
			gspacketIn.WriteByte((byte)type);
			gspacketIn.WriteBoolean(player.GetSealState());
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600721C RID: 29212 RVA: 0x0000F4C1 File Offset: 0x0000D6C1
		internal void SendlivingBoltmove(Player player, int x, int y)
		{
			throw new NotImplementedException();
		}

		// Token: 0x0600721D RID: 29213 RVA: 0x0026140C File Offset: 0x0025F60C
		public void AddPlayerSadowHandler(Player living_0, int X, int Y, int IdObj)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living_0.Id);
			gspacketIn.Parameter1 = living_0.Id;
			gspacketIn.WriteByte(157);
			gspacketIn.WriteByte(18);
			gspacketIn.WriteInt(IdObj);
			gspacketIn.WriteInt(X);
			gspacketIn.WriteInt(living_0.Y);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteBoolean(false);
			gspacketIn.WriteBoolean(false);
			gspacketIn.WriteInt(living_0.Id);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600721E RID: 29214 RVA: 0x0025F130 File Offset: 0x0025D330
		public List<Player> GetAllPlayersSameTeam(Living living)
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = player.Team == living.Team;
					if (flag2)
					{
						list.Add(player);
					}
				}
			}
			return list;
		}

		// Token: 0x0600721F RID: 29215 RVA: 0x002614B0 File Offset: 0x0025F6B0
		public void removePhysicObject(Player living_0)
		{
			foreach (int num in living_0.ListObject)
			{
				GSPacketIn gspacketIn = new GSPacketIn(91, living_0.Id)
				{
					Parameter1 = living_0.Id
				};
				gspacketIn.WriteByte(53);
				gspacketIn.WriteInt(num);
				this.SendToAll(gspacketIn);
			}
			living_0.ListObject = new List<int>();
		}

		// Token: 0x06007220 RID: 29216 RVA: 0x00261540 File Offset: 0x0025F740
		internal void SendGameUpdateShootCount(Player player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.WriteByte(46);
			gspacketIn.WriteByte((byte)player.ShootCount);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007221 RID: 29217 RVA: 0x0026157C File Offset: 0x0025F77C
		internal void SendGameUpdateBall(Player player, bool Special)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(20);
			gspacketIn.WriteBoolean(Special);
			gspacketIn.WriteInt(player.CurrentBall.ID);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007222 RID: 29218 RVA: 0x002615D0 File Offset: 0x0025F7D0
		internal void SendGamePickBox(Living player, int index, int arkType, string goods)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.WriteByte(49);
			gspacketIn.WriteByte((byte)index);
			gspacketIn.WriteByte((byte)arkType);
			gspacketIn.WriteString(goods);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007223 RID: 29219 RVA: 0x00261618 File Offset: 0x0025F818
		internal void SendGameUpdateWind(float wind)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(38);
			int num = (int)(wind * 10f);
			gspacketIn.WriteInt(num);
			gspacketIn.WriteBoolean(num > 0);
			gspacketIn.WriteByte(this.GetVane(num, 1));
			gspacketIn.WriteByte(this.GetVane(num, 2));
			gspacketIn.WriteByte(this.GetVane(num, 3));
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007224 RID: 29220 RVA: 0x00261688 File Offset: 0x0025F888
		internal void SendGameWindPic(Player p, byte windId, byte[] windpic)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(241);
			gspacketIn.WriteByte(windId);
			gspacketIn.Write(windpic);
			bool flag = p != null;
			if (flag)
			{
				p.PlayerDetail.SendTCP(gspacketIn);
			}
			else
			{
				this.SendToAll(gspacketIn);
			}
		}

		// Token: 0x06007225 RID: 29221 RVA: 0x002616E0 File Offset: 0x0025F8E0
		internal void SendUseDeputyWeapon(Player player, int ResCount)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(84);
			gspacketIn.WriteInt(ResCount);
			player.PlayerDetail.SendTCP(gspacketIn);
		}

		// Token: 0x06007226 RID: 29222 RVA: 0x0002AA63 File Offset: 0x00028C63
		internal void SendPlayerUseProp(Player player, int type, int place, int templateID)
		{
			this.SendPlayerUseProp(player, type, place, templateID, player);
		}

		// Token: 0x06007227 RID: 29223 RVA: 0x00261728 File Offset: 0x0025F928
		internal void SendPlayerUseProp(Living player, int type, int place, int templateID, Player p)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(32);
			gspacketIn.WriteByte((byte)type);
			gspacketIn.WriteInt(place);
			gspacketIn.WriteInt(templateID);
			gspacketIn.WriteInt(p.Id);
			gspacketIn.WriteBoolean(templateID == 10017);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007228 RID: 29224 RVA: 0x0026179C File Offset: 0x0025F99C
		internal void SendGamePlayerTakeCard(Player player, int index, int templateID, int count, bool isSysTake)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(98);
			gspacketIn.WriteBoolean(isSysTake);
			gspacketIn.WriteByte((byte)index);
			gspacketIn.WriteInt(templateID);
			gspacketIn.WriteInt(count);
			gspacketIn.WriteBoolean(player.PlayerDetail.PlayerCharacter.TypeVIP > 0 && player.HasPaymentTakeCard);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007229 RID: 29225 RVA: 0x0026181C File Offset: 0x0025FA1C
		internal void SendGameNextTurn(Living living, BaseGame game, List<SimpleBox> newBoxes)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(6);
			int num = (int)(this.m_map.wind * 10f);
			gspacketIn.WriteBoolean(num > 0);
			gspacketIn.WriteByte(this.GetVane(num, 1));
			gspacketIn.WriteByte(this.GetVane(num, 2));
			gspacketIn.WriteByte(this.GetVane(num, 3));
			gspacketIn.WriteBoolean(living.IsHide);
			gspacketIn.WriteInt(this.getTurnTime());
			gspacketIn.WriteInt(newBoxes.Count);
			foreach (SimpleBox simpleBox in newBoxes)
			{
				gspacketIn.WriteInt(simpleBox.Id);
				gspacketIn.WriteInt(simpleBox.X);
				gspacketIn.WriteInt(simpleBox.Y);
				gspacketIn.WriteInt(simpleBox.Type);
			}
			bool flag = living is TurnedLiving;
			if (flag)
			{
				List<Player> allFightPlayers = game.GetAllFightPlayers();
				gspacketIn.WriteInt(allFightPlayers.Count);
				foreach (Player player in allFightPlayers)
				{
					gspacketIn.WriteInt(player.Id);
					gspacketIn.WriteBoolean(player.IsLiving);
					gspacketIn.WriteInt(player.X);
					gspacketIn.WriteInt(player.Y);
					gspacketIn.WriteInt(player.Blood);
					gspacketIn.WriteBoolean(player.IsNoHole);
					gspacketIn.WriteInt(player.Energy);
					gspacketIn.WriteInt(player.psychic);
					gspacketIn.WriteInt(player.Dander);
					bool flag2 = player.UserPet == null;
					if (flag2)
					{
						gspacketIn.WriteInt(0);
						gspacketIn.WriteInt(0);
						gspacketIn.WriteInt(0);
					}
					else
					{
						gspacketIn.WriteInt(player.PetMaxMP);
						gspacketIn.WriteInt(player.PetMP);
						gspacketIn.WriteInt(player.PetFlag);
					}
					gspacketIn.WriteInt(player.ShootCount);
				}
				gspacketIn.WriteInt(game.TurnIndex);
			}
			gspacketIn.WriteBoolean(false);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600722A RID: 29226 RVA: 0x00261AB8 File Offset: 0x0025FCB8
		internal void SendLivingUpdateDirection(Living living)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(7);
			gspacketIn.WriteInt(living.Direction);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600722B RID: 29227 RVA: 0x00261AF8 File Offset: 0x0025FCF8
		internal void SendLivingUpdateAngryState(Living living)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(118);
			gspacketIn.WriteInt(living.State);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600722C RID: 29228 RVA: 0x00261B3C File Offset: 0x0025FD3C
		internal void SendEquipEffect(Living player, string buffer)
		{
			GSPacketIn gspacketIn = new GSPacketIn(3);
			gspacketIn.WriteInt(0);
			gspacketIn.WriteString(buffer);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600722D RID: 29229 RVA: 0x00261B6C File Offset: 0x0025FD6C
		internal void SendMessage(IGamePlayer player, string msg, string msg1, int type)
		{
			bool flag = msg != null;
			if (flag)
			{
				GSPacketIn gspacketIn = new GSPacketIn(3);
				gspacketIn.WriteInt(type);
				gspacketIn.WriteString(msg);
				player.SendTCP(gspacketIn);
			}
			bool flag2 = msg1 != null;
			if (flag2)
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(3);
				gspacketIn2.WriteInt(type);
				gspacketIn2.WriteString(msg1);
				this.SendToAll(gspacketIn2, player);
			}
		}

		// Token: 0x0600722E RID: 29230 RVA: 0x00261BD0 File Offset: 0x0025FDD0
		internal void SendFightAchievement(Living living, int achievID, int dis, int delay)
		{
			bool flag = living.Game.RoomType == eRoomType.Match || living.Game.RoomType == eRoomType.Freedom || living.Game.RoomType == eRoomType.Score || living.Game.RoomType == eRoomType.Rank;
			if (flag)
			{
				GSPacketIn gspacketIn = new GSPacketIn(91);
				gspacketIn.WriteByte(238);
				gspacketIn.WriteInt(achievID);
				gspacketIn.WriteInt(dis);
				gspacketIn.WriteInt(delay);
				this.SendToAll(gspacketIn);
			}
		}

		// Token: 0x0600722F RID: 29231 RVA: 0x00261C58 File Offset: 0x0025FE58
		internal void SendPlayerPicture(Living living, int type, bool state)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(128);
			gspacketIn.WriteInt(type);
			gspacketIn.WriteBoolean(state);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007230 RID: 29232 RVA: 0x00261CA0 File Offset: 0x0025FEA0
		internal void SendPlayerRemove(Player player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94, player.PlayerDetail.PlayerCharacter.ID);
			gspacketIn.WriteByte(5);
			gspacketIn.WriteInt(player.PlayerDetail.AreaID);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007231 RID: 29233 RVA: 0x00261CE8 File Offset: 0x0025FEE8
		internal void SendAttackEffect(Living player, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(129);
			gspacketIn.WriteBoolean(true);
			gspacketIn.WriteInt(type);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007232 RID: 29234 RVA: 0x00261D30 File Offset: 0x0025FF30
		internal void SendSyncLifeTime()
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(131);
			gspacketIn.WriteInt(this.m_lifeTime);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007233 RID: 29235 RVA: 0x00261D68 File Offset: 0x0025FF68
		internal void SendGamePlayerProperty(Living living, string type, string state)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(41);
			gspacketIn.WriteString(type);
			gspacketIn.WriteString(state);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007234 RID: 29236 RVA: 0x00261DB4 File Offset: 0x0025FFB4
		internal void SendCurrentPlayerProperty(Living living, string type, string state)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(41);
			gspacketIn.WriteString(type);
			gspacketIn.WriteString(state);
			bool flag = living is Player;
			if (flag)
			{
				((Player)living).PlayerDetail.SendTCP(gspacketIn);
			}
		}

		// Token: 0x06007235 RID: 29237 RVA: 0x00261E18 File Offset: 0x00260018
		public void SendLivingShowBlood(Living player, int isShow)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.WriteByte(80);
			gspacketIn.WriteInt(player.Id);
			gspacketIn.WriteInt(isShow);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007236 RID: 29238 RVA: 0x00261E5C File Offset: 0x0026005C
		internal void SendLivingTurnRotation(Living player, int rotation, int speed, string endPlay)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(85);
			gspacketIn.WriteInt(rotation);
			gspacketIn.WriteInt(speed);
			gspacketIn.WriteString(endPlay);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007237 RID: 29239 RVA: 0x00261EB0 File Offset: 0x002600B0
		internal void SendLivingActionMapping(int id, string source, string value)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, id);
			gspacketIn.Parameter1 = id;
			gspacketIn.WriteByte(223);
			gspacketIn.WriteInt(id);
			gspacketIn.WriteString(source);
			gspacketIn.WriteString(value);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007238 RID: 29240 RVA: 0x00261EFC File Offset: 0x002600FC
		internal void SendRoundOneEnd(Living player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(4);
			((Player)player).PlayerDetail.SendTCP(gspacketIn);
		}

		// Token: 0x06007239 RID: 29241 RVA: 0x00261F2C File Offset: 0x0026012C
		internal void UpdateMaxBlood(Living player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.WriteByte(164);
			gspacketIn.WriteInt(player.Id);
			gspacketIn.WriteInt(player.MaxBlood);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600723A RID: 29242 RVA: 0x00261F78 File Offset: 0x00260178
		internal void SendTempStyle(Player living, string style, bool isTemp)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(134);
			gspacketIn.WriteInt(1);
			bool flag = !isTemp;
			if (flag)
			{
				gspacketIn.WriteString(living.PlayerDetail.PlayerCharacter.Style);
				gspacketIn.WriteInt(living.PlayerDetail.PlayerCharacter.Hide);
			}
			else
			{
				gspacketIn.WriteString(style);
				gspacketIn.WriteInt(1111111111);
			}
			gspacketIn.WriteBoolean(living.PlayerDetail.PlayerCharacter.Sex);
			gspacketIn.WriteString(living.PlayerDetail.PlayerCharacter.Skin);
			gspacketIn.WriteString(living.PlayerDetail.PlayerCharacter.Colors);
			gspacketIn.WriteInt(living.PlayerDetail.PlayerCharacter.ID);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600723B RID: 29243 RVA: 0x0002AA73 File Offset: 0x00028C73
		internal void SendPetUseKill(Player player)
		{
			this.SendPetUseKill(player, player.PetEffects.CurrentUseSkill, player.PetEffects.CurrentUseSkill != 0);
		}

		// Token: 0x0600723C RID: 29244 RVA: 0x00262058 File Offset: 0x00260258
		internal void SendPetUseKill(Player player, int skillId, bool isUse)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(144);
			gspacketIn.WriteInt(skillId);
			gspacketIn.WriteBoolean(isUse);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600723D RID: 29245 RVA: 0x002620A8 File Offset: 0x002602A8
		internal void SendPetBuff(Living player, PetSkillElementInfo info, bool isActive)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.WriteByte(145);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteInt(info.ID);
			gspacketIn.WriteString(info.Name);
			gspacketIn.WriteString(info.Description);
			gspacketIn.WriteString(info.Pic.ToString());
			gspacketIn.WriteString(info.EffectPic);
			gspacketIn.WriteBoolean(isActive);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600723E RID: 29246 RVA: 0x00262138 File Offset: 0x00260338
		public void SendQuizWindow(int QuizID, int ArightResult, int NeedArightResult, int MaxQuizSize, int TimeOut, string Caption, string QuizStr, string ResultStrFirst, string ResultStrSecond, string ResultStrThird)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(24);
			gspacketIn.WriteBoolean(true);
			gspacketIn.WriteInt(QuizID);
			gspacketIn.WriteInt(ArightResult);
			gspacketIn.WriteInt(NeedArightResult);
			gspacketIn.WriteInt(MaxQuizSize);
			gspacketIn.WriteInt(TimeOut);
			gspacketIn.WriteString(Caption);
			gspacketIn.WriteString(QuizStr);
			gspacketIn.WriteString(ResultStrFirst);
			gspacketIn.WriteString(ResultStrSecond);
			gspacketIn.WriteString(ResultStrThird);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600723F RID: 29247 RVA: 0x002621C0 File Offset: 0x002603C0
		public void SendCloseQuizWindow()
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(24);
			gspacketIn.WriteBoolean(false);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007240 RID: 29248 RVA: 0x002621F0 File Offset: 0x002603F0
		internal void SendLockFocus(bool IsLock)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(69);
			gspacketIn.WriteBoolean(IsLock);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007241 RID: 29249 RVA: 0x00262220 File Offset: 0x00260420
		public void ShowBloodItem(int id)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(69);
			gspacketIn.WriteInt(id);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007242 RID: 29250 RVA: 0x00262250 File Offset: 0x00260450
		public void SendShowBloodItem(int livingId)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(73);
			gspacketIn.WriteInt(livingId);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007243 RID: 29251 RVA: 0x00262280 File Offset: 0x00260480
		public void LivingChangeAngle(Living living, int Speed, int Angle, string endPlay)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91)
			{
				Parameter1 = living.Id
			};
			gspacketIn.WriteByte(85);
			gspacketIn.WriteInt(Angle);
			gspacketIn.WriteInt(Speed);
			gspacketIn.WriteString(endPlay);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06007244 RID: 29252 RVA: 0x002622D0 File Offset: 0x002604D0
		protected void OnGameOverred()
		{
			bool flag = this.GameOverred != null;
			if (flag)
			{
				this.GameOverred(this);
			}
		}

		// Token: 0x06007245 RID: 29253 RVA: 0x002622FC File Offset: 0x002604FC
		protected void OnBeginNewTurn()
		{
			bool flag = this.BeginNewTurn != null;
			if (flag)
			{
				this.BeginNewTurn(this);
			}
		}

		// Token: 0x06007246 RID: 29254 RVA: 0x00262328 File Offset: 0x00260528
		public void OnGameOverLog(int _roomId, eRoomType _roomType, eGameType _fightType, int _changeTeam, DateTime _playBegin, DateTime _playEnd, int _userCount, int _mapId, string _teamA, string _teamB, string _playResult, int _winTeam, string BossWar)
		{
			bool flag = this.GameOverLog != null;
			if (flag)
			{
				this.GameOverLog(_roomId, _roomType, _fightType, _changeTeam, _playBegin, _playEnd, _userCount, _mapId, _teamA, _teamB, _playResult, _winTeam, this.BossWarField);
			}
		}

		// Token: 0x06007247 RID: 29255 RVA: 0x0026236C File Offset: 0x0026056C
		public void OnGameNpcDie(int Id)
		{
			bool flag = this.GameNpcDie != null;
			if (flag)
			{
				this.GameNpcDie(Id);
			}
		}

		// Token: 0x06007248 RID: 29256 RVA: 0x00262398 File Offset: 0x00260598
		public override string ToString()
		{
			return string.Format("Id:{0},player:{1},state:{2},current:{3},turnIndex:{4},actions:{5}", new object[]
			{
				base.Id,
				this.PlayerCount,
				this.GameState,
				this.CurrentLiving,
				this.m_turnIndex,
				this.m_actions.Count
			});
		}

		// Token: 0x04003DB0 RID: 15792
		public static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04003DB1 RID: 15793
		public readonly int[] EquipPlace = new int[]
		{
			1, 2, 3, 4, 5, 6, 11, 13, 14, 15,
			16, 17, 18, 19, 20
		};

		// Token: 0x04003DB2 RID: 15794
		protected int turnIndex;

		// Token: 0x04003DB3 RID: 15795
		protected int m_nextPlayerId;

		// Token: 0x04003DB4 RID: 15796
		protected int m_nextWind;

		// Token: 0x04003DB5 RID: 15797
		protected int m_nextWindRate;

		// Token: 0x04003DB6 RID: 15798
		protected eGameState m_gameState;

		// Token: 0x04003DB7 RID: 15799
		protected eGameState m_gameSecondState;

		// Token: 0x04003DB8 RID: 15800
		protected Map m_map;

		// Token: 0x04003DB9 RID: 15801
		protected Dictionary<int, Player> m_players;

		// Token: 0x04003DBA RID: 15802
		protected List<Living> m_livings;

		// Token: 0x04003DBB RID: 15803
		protected List<Living> m_decklivings;

		// Token: 0x04003DBC RID: 15804
		protected Random m_random;

		// Token: 0x04003DBD RID: 15805
		protected TurnedLiving m_currentLiving;

		// Token: 0x04003DBE RID: 15806
		public TurnedLiving LastTurnLiving;

		// Token: 0x04003DBF RID: 15807
		public int PhysicalId;

		// Token: 0x04003DC0 RID: 15808
		public int CurrentTurnTotalDamage;

		// Token: 0x04003DC1 RID: 15809
		public int TotalHurt;

		// Token: 0x04003DC2 RID: 15810
		public int ConsortiaAlly;

		// Token: 0x04003DC3 RID: 15811
		public int RichesRate;

		// Token: 0x04003DC4 RID: 15812
		public string BossWarField;

		// Token: 0x04003DC5 RID: 15813
		private ArrayList m_actions;

		// Token: 0x04003DC6 RID: 15814
		private List<TurnedLiving> m_turnQueue;

		// Token: 0x04003DC7 RID: 15815
		protected Dictionary<int, string> m_logStartIps;

		// Token: 0x04003DC8 RID: 15816
		private int m_roomId;

		// Token: 0x04003DC9 RID: 15817
		public bool FrozenWind;

		// Token: 0x04003DCA RID: 15818
		public int VortexBombKillCount = 0;

		// Token: 0x04003DCB RID: 15819
		public int[] Cards;

		// Token: 0x04003DCC RID: 15820
		private int m_lifeTime = 0;

		// Token: 0x04003DCD RID: 15821
		private long m_waitTimer = 0L;

		// Token: 0x04003DCE RID: 15822
		private long m_lastWaitTimer = 0L;

		// Token: 0x04003DCF RID: 15823
		private long m_passTick = 0L;

		// Token: 0x04003DD0 RID: 15824
		public int CurrentActionCount = 0;

		// Token: 0x04003DD1 RID: 15825
		public int loadBossID = 0;

		// Token: 0x04003DD2 RID: 15826
		private List<SimpleBox> m_tempBox;

		// Token: 0x04003DD3 RID: 15827
		private List<Point> m_tempPoints;

		// Token: 0x04003DD4 RID: 15828
		private List<Point> m_tempGhostPoints;

		// Token: 0x04003DD5 RID: 15829
		private List<LoadingFileInfo> m_loadingFiles = new List<LoadingFileInfo>();

		// Token: 0x04003DD6 RID: 15830
		public int TotalCostMoney;

		// Token: 0x04003DD7 RID: 15831
		public int TotalCostGold;

		// Token: 0x04003DD8 RID: 15832
		protected List<SimpleBoss> m_Bosses;

		// Token: 0x02000C86 RID: 3206
		// (Invoke) Token: 0x0600724B RID: 29259
		public delegate void GameOverLogEventHandle(int roomId, eRoomType roomType, eGameType fightType, int changeTeam, DateTime playBegin, DateTime playEnd, int userCount, int mapId, string teamA, string teamB, string playResult, int winTeam, string BossWar);

		// Token: 0x02000C87 RID: 3207
		// (Invoke) Token: 0x0600724F RID: 29263
		public delegate void GameNpcDieEventHandle(int NpcId);
	}
}
