﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D69 RID: 3433
	public class PE1164 : BasePetEffect
	{
		// Token: 0x06007AA4 RID: 31396 RVA: 0x0028FB8C File Offset: 0x0028DD8C
		public PE1164(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1164, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AA5 RID: 31397 RVA: 0x0028FC0C File Offset: 0x0028DE0C
		public override bool Start(Living living)
		{
			PE1164 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1164) as PE1164;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AA6 RID: 31398 RVA: 0x0002E944 File Offset: 0x0002CB44
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007AA7 RID: 31399 RVA: 0x0028FC6C File Offset: 0x0028DE6C
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				living.Game.SendPetBuff(living, base.ElementInfo, true);
				living.Attack += (double)this.m_added;
			}
		}

		// Token: 0x06007AA8 RID: 31400 RVA: 0x0002E95A File Offset: 0x0002CB5A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x0400481F RID: 18463
		private int m_type = 0;

		// Token: 0x04004820 RID: 18464
		private int m_count = 0;

		// Token: 0x04004821 RID: 18465
		private int m_probability = 0;

		// Token: 0x04004822 RID: 18466
		private int m_delay = 0;

		// Token: 0x04004823 RID: 18467
		private int m_coldDown = 0;

		// Token: 0x04004824 RID: 18468
		private int m_currentId;

		// Token: 0x04004825 RID: 18469
		private int m_added = 0;
	}
}
