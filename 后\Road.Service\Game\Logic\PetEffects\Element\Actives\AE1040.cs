﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DA2 RID: 3490
	public class AE1040 : BasePetEffect
	{
		// Token: 0x06007BCA RID: 31690 RVA: 0x0029481C File Offset: 0x00292A1C
		public AE1040(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1040, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BCB RID: 31691 RVA: 0x0029489C File Offset: 0x00292A9C
		public override bool Start(Living living)
		{
			AE1040 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1040) as AE1040;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BCC RID: 31692 RVA: 0x0002F54F File Offset: 0x0002D74F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BCD RID: 31693 RVA: 0x0002F565 File Offset: 0x0002D765
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BCE RID: 31694 RVA: 0x002948FC File Offset: 0x00292AFC
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1040(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x040049A8 RID: 18856
		private int m_type = 0;

		// Token: 0x040049A9 RID: 18857
		private int m_count = 0;

		// Token: 0x040049AA RID: 18858
		private int m_probability = 0;

		// Token: 0x040049AB RID: 18859
		private int m_delay = 0;

		// Token: 0x040049AC RID: 18860
		private int m_coldDown = 0;

		// Token: 0x040049AD RID: 18861
		private int m_currentId;

		// Token: 0x040049AE RID: 18862
		private int m_added = 0;
	}
}
