﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D3E RID: 3390
	public class PetMakeDamagePercent : BasePetEffect
	{
		// Token: 0x060079A8 RID: 31144 RVA: 0x0028BD50 File Offset: 0x00289F50
		public PetMakeDamagePercent(int probability, int skillId, string elementID)
			: base(ePetEffectType.PetMakeDamagePercent, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
			bool flag = elementID == null;
			if (!flag)
			{
				int length = elementID.Length;
				bool flag2 = length != 4;
				if (!flag2)
				{
					switch (elementID[3])
					{
					case '0':
					{
						bool flag3 = !(elementID == "2960");
						if (flag3)
						{
							bool flag4 = elementID == "1550";
							if (flag4)
							{
								this.m_value = 220;
							}
						}
						else
						{
							this.m_value = 210;
						}
						break;
					}
					case '2':
					{
						bool flag5 = !(elementID == "4252");
						if (flag5)
						{
							bool flag6 = elementID == "3212";
							if (flag6)
							{
								this.m_value = 150;
							}
						}
						else
						{
							this.m_value = 180;
						}
						break;
					}
					case '4':
					{
						bool flag7 = elementID == "3214";
						if (flag7)
						{
							this.m_value = 180;
						}
						break;
					}
					case '5':
					{
						bool flag8 = elementID == "3215";
						if (flag8)
						{
							this.m_value = 220;
						}
						break;
					}
					case '8':
					{
						bool flag9 = !(elementID == "2958");
						if (flag9)
						{
							bool flag10 = elementID == "1548";
							if (flag10)
							{
								this.m_value = 150;
							}
						}
						else
						{
							this.m_value = 150;
						}
						break;
					}
					case '9':
					{
						bool flag11 = !(elementID == "2959");
						if (flag11)
						{
							bool flag12 = elementID == "1549";
							if (flag12)
							{
								this.m_value = 180;
							}
						}
						else
						{
							this.m_value = 180;
						}
						break;
					}
					}
				}
			}
		}

		// Token: 0x060079A9 RID: 31145 RVA: 0x0028BF5C File Offset: 0x0028A15C
		public override bool Start(Living living)
		{
			PetMakeDamagePercent petMakeDamagePercent = living.PetEffectList.GetOfType(ePetEffectType.PetMakeDamagePercent) as PetMakeDamagePercent;
			bool flag = petMakeDamagePercent != null;
			bool flag2;
			if (flag)
			{
				petMakeDamagePercent.m_probability = ((this.m_probability > petMakeDamagePercent.m_probability) ? this.m_probability : petMakeDamagePercent.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079AA RID: 31146 RVA: 0x0002DCF9 File Offset: 0x0002BEF9
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
			player.PlayerSkip += this.skip;
		}

		// Token: 0x060079AB RID: 31147 RVA: 0x0002DD35 File Offset: 0x0002BF35
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
			player.PlayerSkip -= this.skip;
		}

		// Token: 0x060079AC RID: 31148 RVA: 0x0028BFBC File Offset: 0x0028A1BC
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus += (float)(this.m_value / 100);
			}
			player.MustCrit = true;
		}

		// Token: 0x060079AD RID: 31149 RVA: 0x0028C004 File Offset: 0x0028A204
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_value / 100);
			}
			player.MustCrit = false;
		}

		// Token: 0x060079AE RID: 31150 RVA: 0x0002DD71 File Offset: 0x0002BF71
		private void skip(Player player)
		{
			player.MustCrit = false;
		}

		// Token: 0x04004748 RID: 18248
		private int m_probability = 0;

		// Token: 0x04004749 RID: 18249
		private int m_currentId;

		// Token: 0x0400474A RID: 18250
		private int m_value = 0;
	}
}
