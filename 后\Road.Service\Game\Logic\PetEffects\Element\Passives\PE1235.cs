﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D76 RID: 3446
	public class PE1235 : BasePetEffect
	{
		// Token: 0x06007AE3 RID: 31459 RVA: 0x00290B64 File Offset: 0x0028ED64
		public PE1235(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1235, elementID)
		{
			this.m_count = count;
			this.m_coldDown = 3;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AE4 RID: 31460 RVA: 0x00290BE4 File Offset: 0x0028EDE4
		public override bool Start(Living living)
		{
			PE1235 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1235) as PE1235;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AE5 RID: 31461 RVA: 0x0002EB38 File Offset: 0x0002CD38
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKilledByLiving += this.player_afterKilledByLiving;
		}

		// Token: 0x06007AE6 RID: 31462 RVA: 0x0002EB4E File Offset: 0x0002CD4E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.player_afterKilledByLiving;
		}

		// Token: 0x06007AE7 RID: 31463 RVA: 0x00290C44 File Offset: 0x0028EE44
		private void player_afterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = living.PetEffects.ReboundDamage <= 0;
			if (!flag)
			{
				target.Game.SendPetBuff(target, base.ElementInfo, true);
				bool flag2 = target.Blood < 0;
				if (flag2)
				{
					target.Die();
					bool flag3 = living != null && living is Player;
					if (flag3)
					{
						(living as Player).PlayerDetail.OnKillingLiving(living.Game, 2, living.Id, living.IsLiving, target.PetEffects.ReboundDamage);
					}
				}
				else
				{
					target.AddPetEffect(new CE1235(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
				living.PetEffects.ReboundDamage = 0;
			}
		}

		// Token: 0x0400487A RID: 18554
		private int m_type = 0;

		// Token: 0x0400487B RID: 18555
		private int m_count = 0;

		// Token: 0x0400487C RID: 18556
		private int m_probability = 0;

		// Token: 0x0400487D RID: 18557
		private int m_delay = 0;

		// Token: 0x0400487E RID: 18558
		private int m_coldDown = 0;

		// Token: 0x0400487F RID: 18559
		private int m_currentId;

		// Token: 0x04004880 RID: 18560
		private int m_added = 0;
	}
}
