﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D14 RID: 3348
	public class PetReduceEnergyEquip : AbstractPetEffect
	{
		// Token: 0x060078C5 RID: 30917 RVA: 0x00287B9C File Offset: 0x00285D9C
		public PetReduceEnergyEquip(int count, string elementID)
			: base(ePetEffectType.PetReduceEnergyEquip, elementID)
		{
			this.m_count = count;
			if (!(elementID == "1150"))
			{
				if (!(elementID == "1151"))
				{
					if (!(elementID == "1152"))
					{
						if (elementID == "1741")
						{
							this.m_value = 50;
							this.m_percent = true;
						}
					}
					else
					{
						this.m_value = 30;
						this.m_percent = true;
					}
				}
				else
				{
					this.m_value = 25;
					this.m_percent = true;
				}
			}
			else
			{
				this.m_value = 20;
				this.m_percent = true;
			}
		}

		// Token: 0x060078C6 RID: 30918 RVA: 0x00287C3C File Offset: 0x00285E3C
		public override bool Start(Living living)
		{
			PetReduceEnergyEquip petReduceEnergyEquip = living.PetEffectList.GetOfType(ePetEffectType.PetReduceEnergyEquip) as PetReduceEnergyEquip;
			bool flag = petReduceEnergyEquip != null;
			bool flag2;
			if (flag)
			{
				petReduceEnergyEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078C7 RID: 30919 RVA: 0x0002CEA4 File Offset: 0x0002B0A4
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x060078C8 RID: 30920 RVA: 0x0002CEBA File Offset: 0x0002B0BA
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x060078C9 RID: 30921 RVA: 0x00287C84 File Offset: 0x00285E84
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
			else
			{
				bool flag2 = living is Player;
				if (flag2)
				{
					bool percent = this.m_percent;
					if (percent)
					{
						(living as Player).ReduceEnergy((living as Player).Energy * this.m_value / 100);
					}
					else
					{
						(living as Player).ReduceEnergy(this.m_value);
					}
				}
			}
		}

		// Token: 0x040046C8 RID: 18120
		private int m_count;

		// Token: 0x040046C9 RID: 18121
		private int m_value;

		// Token: 0x040046CA RID: 18122
		private bool m_percent;
	}
}
