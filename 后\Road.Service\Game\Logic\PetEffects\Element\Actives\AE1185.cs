﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DF0 RID: 3568
	public class AE1185 : BasePetEffect
	{
		// Token: 0x06007D5F RID: 32095 RVA: 0x0029BBB4 File Offset: 0x00299DB4
		public AE1185(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1185, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D60 RID: 32096 RVA: 0x0029BC34 File Offset: 0x00299E34
		public override bool Start(Living living)
		{
			AE1185 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1185) as AE1185;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D61 RID: 32097 RVA: 0x0003046E File Offset: 0x0002E66E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D62 RID: 32098 RVA: 0x00030484 File Offset: 0x0002E684
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D63 RID: 32099 RVA: 0x0029BC94 File Offset: 0x00299E94
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1185(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004BC8 RID: 19400
		private int m_type = 0;

		// Token: 0x04004BC9 RID: 19401
		private int m_count = 0;

		// Token: 0x04004BCA RID: 19402
		private int m_probability = 0;

		// Token: 0x04004BCB RID: 19403
		private int m_delay = 0;

		// Token: 0x04004BCC RID: 19404
		private int m_coldDown = 0;

		// Token: 0x04004BCD RID: 19405
		private int m_currentId;

		// Token: 0x04004BCE RID: 19406
		private int m_added = 0;
	}
}
