﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DE5 RID: 3557
	public class AE1174 : BasePetEffect
	{
		// Token: 0x06007D28 RID: 32040 RVA: 0x0029A9AC File Offset: 0x00298BAC
		public AE1174(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1174, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D29 RID: 32041 RVA: 0x0029AA2C File Offset: 0x00298C2C
		public override bool Start(Living living)
		{
			AE1174 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1174) as AE1174;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D2A RID: 32042 RVA: 0x0003028A File Offset: 0x0002E48A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D2B RID: 32043 RVA: 0x000302A0 File Offset: 0x0002E4A0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D2C RID: 32044 RVA: 0x0029AA8C File Offset: 0x00298C8C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1174(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004B7B RID: 19323
		private int m_type = 0;

		// Token: 0x04004B7C RID: 19324
		private int m_count = 0;

		// Token: 0x04004B7D RID: 19325
		private int m_probability = 0;

		// Token: 0x04004B7E RID: 19326
		private int m_delay = 0;

		// Token: 0x04004B7F RID: 19327
		private int m_coldDown = 0;

		// Token: 0x04004B80 RID: 19328
		private int m_currentId;

		// Token: 0x04004B81 RID: 19329
		private int m_added = 0;
	}
}
