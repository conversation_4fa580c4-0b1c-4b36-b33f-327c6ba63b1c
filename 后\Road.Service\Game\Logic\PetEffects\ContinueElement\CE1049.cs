﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E5C RID: 3676
	public class CE1049 : BasePetEffect
	{
		// Token: 0x06007FB6 RID: 32694 RVA: 0x002A6350 File Offset: 0x002A4550
		public CE1049(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1049, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FB7 RID: 32695 RVA: 0x002A63D0 File Offset: 0x002A45D0
		public override bool Start(Living living)
		{
			CE1049 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1049) as CE1049;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FB8 RID: 32696 RVA: 0x002A6430 File Offset: 0x002A4630
		protected override void OnAttachedToPlayer(Player player)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 200;
				player.BaseGuard += (double)this.m_added;
			}
		}

		// Token: 0x06007FB9 RID: 32697 RVA: 0x00031B1D File Offset: 0x0002FD1D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BaseGuard -= (double)this.m_added;
		}

		// Token: 0x04004EBF RID: 20159
		private int m_type = 0;

		// Token: 0x04004EC0 RID: 20160
		private int m_count = 0;

		// Token: 0x04004EC1 RID: 20161
		private int m_probability = 0;

		// Token: 0x04004EC2 RID: 20162
		private int m_delay = 0;

		// Token: 0x04004EC3 RID: 20163
		private int m_coldDown = 0;

		// Token: 0x04004EC4 RID: 20164
		private int m_currentId;

		// Token: 0x04004EC5 RID: 20165
		private int m_added = 0;
	}
}
