﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FE0 RID: 4064
	public class MarkMgr
	{
		// Token: 0x06008B18 RID: 35608 RVA: 0x002FA7BC File Offset: 0x002F89BC
		public static bool Init()
		{
			bool flag;
			try
			{
				MarkMgr.rand = new Random();
				flag = MarkMgr.ReLoad();
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = MarkMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					MarkMgr.log.Error("MarkMgr", ex);
				}
				flag = false;
			}
			return flag;
		}

		// Token: 0x06008B19 RID: 35609 RVA: 0x002FA818 File Offset: 0x002F8A18
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, DebrisPropertyConfig> dictionary = new Dictionary<int, DebrisPropertyConfig>();
				Dictionary<int, EngraveDebrisInfo> dictionary2 = new Dictionary<int, EngraveDebrisInfo>();
				Dictionary<int, EngraveFocusDebrisInfo> dictionary3 = new Dictionary<int, EngraveFocusDebrisInfo>();
				Dictionary<int, EngraveFocusElement> dictionary4 = new Dictionary<int, EngraveFocusElement>();
				Dictionary<int, EngraveRefineryConfigInfo> dictionary5 = new Dictionary<int, EngraveRefineryConfigInfo>();
				Dictionary<int, EngraveSetElementInfo> dictionary6 = new Dictionary<int, EngraveSetElementInfo>();
				Dictionary<int, EngraveSetInfo> dictionary7 = new Dictionary<int, EngraveSetInfo>();
				Dictionary<int, EngraveTemperConfigInfo> dictionary8 = new Dictionary<int, EngraveTemperConfigInfo>();
				Dictionary<int, TSSigilProValueLimitTemp> dictionary9 = new Dictionary<int, TSSigilProValueLimitTemp>();
				Dictionary<int, TSSigilSkillTemplate> dictionary10 = new Dictionary<int, TSSigilSkillTemplate>();
				bool flag = MarkMgr.LoadMarkFromDb(dictionary, dictionary2, dictionary3, dictionary4, dictionary5, dictionary6, dictionary7, dictionary8, dictionary9, dictionary10);
				if (flag)
				{
					try
					{
						MarkMgr.m_DebrisPropertys = dictionary;
						MarkMgr.m_Debris = dictionary2;
						MarkMgr.m_FocusDebris = dictionary3;
						MarkMgr.m_FocusElements = dictionary4;
						MarkMgr.m_Refinerys = dictionary5;
						MarkMgr.m_SetElements = dictionary6;
						MarkMgr.m_Sets = dictionary7;
						MarkMgr.m_Tempers = dictionary8;
						MarkMgr.m_SigilPros = dictionary9;
						MarkMgr.m_SigilSkills = dictionary10;
						return true;
					}
					catch
					{
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = MarkMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					MarkMgr.log.Error("MarkMgr", ex);
				}
			}
			return false;
		}

		// Token: 0x06008B1A RID: 35610 RVA: 0x002FA924 File Offset: 0x002F8B24
		private static bool LoadMarkFromDb(Dictionary<int, DebrisPropertyConfig> PropertyConfig, Dictionary<int, EngraveDebrisInfo> Debris, Dictionary<int, EngraveFocusDebrisInfo> FocusDebris, Dictionary<int, EngraveFocusElement> FocusElement, Dictionary<int, EngraveRefineryConfigInfo> RefineryConfig, Dictionary<int, EngraveSetElementInfo> SetElement, Dictionary<int, EngraveSetInfo> Set, Dictionary<int, EngraveTemperConfigInfo> TemperConfig, Dictionary<int, TSSigilProValueLimitTemp> SigilPro, Dictionary<int, TSSigilSkillTemplate> SigilSkill)
		{
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				DebrisPropertyConfig[] allDebrisPropertyConfig = produceBussiness.GetAllDebrisPropertyConfig();
				EngraveDebrisInfo[] allEngraveDebris = produceBussiness.GetAllEngraveDebris();
				EngraveFocusDebrisInfo[] array = produceBussiness.EngraveFocusDebris();
				EngraveFocusElement[] allEngraveFocusElement = produceBussiness.GetAllEngraveFocusElement();
				EngraveRefineryConfigInfo[] allEngraveRefineryConfig = produceBussiness.GetAllEngraveRefineryConfig();
				EngraveSetElementInfo[] allEngraveSetElement = produceBussiness.GetAllEngraveSetElement();
				EngraveSetInfo[] allEngraveSet = produceBussiness.GetAllEngraveSet();
				EngraveTemperConfigInfo[] allEngraveTemperConfig = produceBussiness.GetAllEngraveTemperConfig();
				TSSigilProValueLimitTemp[] allSigilProValueLimitTemp = produceBussiness.GetAllSigilProValueLimitTemp();
				TSSigilSkillTemplate[] allSigilSkillTemplate = produceBussiness.GetAllSigilSkillTemplate();
				DebrisPropertyConfig[] array2 = allDebrisPropertyConfig;
				DebrisPropertyConfig[] array3 = array2;
				DebrisPropertyConfig[] array4 = array3;
				foreach (DebrisPropertyConfig debrisPropertyConfig in array4)
				{
					bool flag = !PropertyConfig.ContainsKey(debrisPropertyConfig.Id);
					if (flag)
					{
						PropertyConfig.Add(debrisPropertyConfig.Id, debrisPropertyConfig);
					}
				}
				EngraveDebrisInfo[] array6 = allEngraveDebris;
				EngraveDebrisInfo[] array7 = array6;
				EngraveDebrisInfo[] array8 = array7;
				foreach (EngraveDebrisInfo engraveDebrisInfo in array8)
				{
					bool flag2 = !Debris.ContainsKey(engraveDebrisInfo.Id);
					if (flag2)
					{
						Debris.Add(engraveDebrisInfo.Id, engraveDebrisInfo);
					}
				}
				EngraveFocusDebrisInfo[] array10 = array;
				EngraveFocusDebrisInfo[] array11 = array10;
				EngraveFocusDebrisInfo[] array12 = array11;
				foreach (EngraveFocusDebrisInfo engraveFocusDebrisInfo in array12)
				{
					bool flag3 = !FocusDebris.ContainsKey(engraveFocusDebrisInfo.ItemID);
					if (flag3)
					{
						FocusDebris.Add(engraveFocusDebrisInfo.ItemID, engraveFocusDebrisInfo);
					}
				}
				EngraveFocusElement[] array14 = allEngraveFocusElement;
				EngraveFocusElement[] array15 = array14;
				EngraveFocusElement[] array16 = array15;
				foreach (EngraveFocusElement engraveFocusElement in array16)
				{
					bool flag4 = !FocusElement.ContainsKey(engraveFocusElement.FocusId);
					if (flag4)
					{
						FocusElement.Add(engraveFocusElement.FocusId, engraveFocusElement);
					}
				}
				EngraveRefineryConfigInfo[] array18 = allEngraveRefineryConfig;
				EngraveRefineryConfigInfo[] array19 = array18;
				EngraveRefineryConfigInfo[] array20 = array19;
				foreach (EngraveRefineryConfigInfo engraveRefineryConfigInfo in array20)
				{
					bool flag5 = !RefineryConfig.ContainsKey(engraveRefineryConfigInfo.Id);
					if (flag5)
					{
						RefineryConfig.Add(engraveRefineryConfigInfo.Id, engraveRefineryConfigInfo);
					}
				}
				EngraveSetElementInfo[] array22 = allEngraveSetElement;
				EngraveSetElementInfo[] array23 = array22;
				EngraveSetElementInfo[] array24 = array23;
				foreach (EngraveSetElementInfo engraveSetElementInfo in array24)
				{
					bool flag6 = !SetElement.ContainsKey(engraveSetElementInfo.Id);
					if (flag6)
					{
						SetElement.Add(engraveSetElementInfo.Id, engraveSetElementInfo);
					}
				}
				EngraveSetInfo[] array26 = allEngraveSet;
				EngraveSetInfo[] array27 = array26;
				EngraveSetInfo[] array28 = array27;
				foreach (EngraveSetInfo engraveSetInfo in array28)
				{
					bool flag7 = !Set.ContainsKey(engraveSetInfo.SetId);
					if (flag7)
					{
						Set.Add(engraveSetInfo.SetId, engraveSetInfo);
					}
				}
				EngraveTemperConfigInfo[] array30 = allEngraveTemperConfig;
				EngraveTemperConfigInfo[] array31 = array30;
				EngraveTemperConfigInfo[] array32 = array31;
				foreach (EngraveTemperConfigInfo engraveTemperConfigInfo in array32)
				{
					bool flag8 = !TemperConfig.ContainsKey(engraveTemperConfigInfo.Level);
					if (flag8)
					{
						TemperConfig.Add(engraveTemperConfigInfo.Level, engraveTemperConfigInfo);
					}
				}
				TSSigilProValueLimitTemp[] array34 = allSigilProValueLimitTemp;
				TSSigilProValueLimitTemp[] array35 = array34;
				TSSigilProValueLimitTemp[] array36 = array35;
				foreach (TSSigilProValueLimitTemp tssigilProValueLimitTemp in array36)
				{
					bool flag9 = !SigilPro.ContainsKey(tssigilProValueLimitTemp.Id);
					if (flag9)
					{
						SigilPro.Add(tssigilProValueLimitTemp.Id, tssigilProValueLimitTemp);
					}
				}
				TSSigilSkillTemplate[] array38 = allSigilSkillTemplate;
				TSSigilSkillTemplate[] array39 = array38;
				TSSigilSkillTemplate[] array40 = array39;
				foreach (TSSigilSkillTemplate tssigilSkillTemplate in array40)
				{
					bool flag10 = !SigilSkill.ContainsKey(tssigilSkillTemplate.SkillID);
					if (flag10)
					{
						SigilSkill.Add(tssigilSkillTemplate.SkillID, tssigilSkillTemplate);
					}
				}
			}
			return true;
		}

		// Token: 0x04005522 RID: 21794
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005523 RID: 21795
		private static Dictionary<int, DebrisPropertyConfig> m_DebrisPropertys;

		// Token: 0x04005524 RID: 21796
		private static Dictionary<int, EngraveDebrisInfo> m_Debris;

		// Token: 0x04005525 RID: 21797
		private static Dictionary<int, EngraveFocusDebrisInfo> m_FocusDebris;

		// Token: 0x04005526 RID: 21798
		private static Dictionary<int, EngraveFocusElement> m_FocusElements;

		// Token: 0x04005527 RID: 21799
		private static Dictionary<int, EngraveRefineryConfigInfo> m_Refinerys;

		// Token: 0x04005528 RID: 21800
		private static Dictionary<int, EngraveSetElementInfo> m_SetElements;

		// Token: 0x04005529 RID: 21801
		private static Dictionary<int, EngraveSetInfo> m_Sets;

		// Token: 0x0400552A RID: 21802
		private static Dictionary<int, EngraveTemperConfigInfo> m_Tempers;

		// Token: 0x0400552B RID: 21803
		private static Dictionary<int, TSSigilProValueLimitTemp> m_SigilPros;

		// Token: 0x0400552C RID: 21804
		private static Dictionary<int, TSSigilSkillTemplate> m_SigilSkills;

		// Token: 0x0400552D RID: 21805
		private static ReaderWriterLock m_clientLocker = new ReaderWriterLock();

		// Token: 0x0400552E RID: 21806
		private static Random rand;
	}
}
