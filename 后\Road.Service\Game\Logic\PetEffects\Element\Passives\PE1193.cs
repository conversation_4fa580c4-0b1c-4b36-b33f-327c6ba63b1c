﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D6F RID: 3439
	public class PE1193 : BasePetEffect
	{
		// Token: 0x06007AC2 RID: 31426 RVA: 0x00290330 File Offset: 0x0028E530
		public PE1193(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1193, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AC3 RID: 31427 RVA: 0x002903B0 File Offset: 0x0028E5B0
		public override bool Start(Living living)
		{
			PE1193 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1193) as PE1193;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AC4 RID: 31428 RVA: 0x0002EA4C File Offset: 0x0002CC4C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007AC5 RID: 31429 RVA: 0x00290410 File Offset: 0x0028E610
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = living.PetEffects.BonusAttack != 0;
			if (!flag)
			{
				this.m_added = 300;
				List<Player> allTeamPlayers = living.Game.GetAllTeamPlayers(living);
				foreach (Player player in allTeamPlayers)
				{
					player.Attack += (double)this.m_added;
					player.PetEffects.BonusAttack += this.m_added;
					player.Game.SendPetBuff(player, base.Info, true);
				}
			}
		}

		// Token: 0x06007AC6 RID: 31430 RVA: 0x0002EA62 File Offset: 0x0002CC62
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x04004849 RID: 18505
		private int m_type = 0;

		// Token: 0x0400484A RID: 18506
		private int m_count = 0;

		// Token: 0x0400484B RID: 18507
		private int m_probability = 0;

		// Token: 0x0400484C RID: 18508
		private int m_delay = 0;

		// Token: 0x0400484D RID: 18509
		private int m_coldDown = 0;

		// Token: 0x0400484E RID: 18510
		private int m_currentId;

		// Token: 0x0400484F RID: 18511
		private int m_added = 0;
	}
}
