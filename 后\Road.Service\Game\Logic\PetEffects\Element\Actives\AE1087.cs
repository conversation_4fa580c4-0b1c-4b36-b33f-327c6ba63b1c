﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DBC RID: 3516
	public class AE1087 : BasePetEffect
	{
		// Token: 0x06007C4D RID: 31821 RVA: 0x00296D00 File Offset: 0x00294F00
		public AE1087(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1087, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C4E RID: 31822 RVA: 0x00296D80 File Offset: 0x00294F80
		public override bool Start(Living living)
		{
			AE1087 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1087) as AE1087;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C4F RID: 31823 RVA: 0x0002F982 File Offset: 0x0002DB82
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C50 RID: 31824 RVA: 0x00296DE0 File Offset: 0x00294FE0
		protected override void OnPausedOnPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Defence += (double)this.m_added;
			this.IsTrigger = false;
		}

		// Token: 0x06007C51 RID: 31825 RVA: 0x00296E30 File Offset: 0x00295030
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && !this.IsTrigger;
			if (flag)
			{
				this.IsTrigger = true;
				this.m_added = 1000;
				player.Defence -= (double)this.m_added;
				player.Game.SendPetBuff(player, base.ElementInfo, true);
			}
		}

		// Token: 0x04004A5E RID: 19038
		private int m_type = 0;

		// Token: 0x04004A5F RID: 19039
		private int m_count = 0;

		// Token: 0x04004A60 RID: 19040
		private int m_probability = 0;

		// Token: 0x04004A61 RID: 19041
		private int m_delay = 0;

		// Token: 0x04004A62 RID: 19042
		private int m_coldDown = 0;

		// Token: 0x04004A63 RID: 19043
		private int m_currentId;

		// Token: 0x04004A64 RID: 19044
		private int m_added = 0;
	}
}
