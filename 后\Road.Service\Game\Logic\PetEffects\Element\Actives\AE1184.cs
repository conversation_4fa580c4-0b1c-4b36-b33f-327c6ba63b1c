﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DEF RID: 3567
	public class AE1184 : BasePetEffect
	{
		// Token: 0x06007D5A RID: 32090 RVA: 0x0029BA60 File Offset: 0x00299C60
		public AE1184(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1184, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D5B RID: 32091 RVA: 0x0029BAE0 File Offset: 0x00299CE0
		public override bool Start(Living living)
		{
			AE1184 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1184) as AE1184;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D5C RID: 32092 RVA: 0x00030442 File Offset: 0x0002E642
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D5D RID: 32093 RVA: 0x00030458 File Offset: 0x0002E658
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D5E RID: 32094 RVA: 0x0029BB40 File Offset: 0x00299D40
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1184(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004BC1 RID: 19393
		private int m_type = 0;

		// Token: 0x04004BC2 RID: 19394
		private int m_count = 0;

		// Token: 0x04004BC3 RID: 19395
		private int m_probability = 0;

		// Token: 0x04004BC4 RID: 19396
		private int m_delay = 0;

		// Token: 0x04004BC5 RID: 19397
		private int m_coldDown = 0;

		// Token: 0x04004BC6 RID: 19398
		private int m_currentId;

		// Token: 0x04004BC7 RID: 19399
		private int m_added = 0;
	}
}
