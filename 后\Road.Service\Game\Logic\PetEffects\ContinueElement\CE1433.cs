﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EBF RID: 3775
	public class CE1433 : BasePetEffect
	{
		// Token: 0x0600822B RID: 33323 RVA: 0x002AFA68 File Offset: 0x002ADC68
		public CE1433(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1433, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600822C RID: 33324 RVA: 0x002AFAE8 File Offset: 0x002ADCE8
		public override bool Start(Living living)
		{
			CE1433 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1433) as CE1433;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600822D RID: 33325 RVA: 0x000332E6 File Offset: 0x000314E6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600822E RID: 33326 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600822F RID: 33327 RVA: 0x002AFB48 File Offset: 0x002ADD48
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008230 RID: 33328 RVA: 0x002AFB7C File Offset: 0x002ADD7C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PetEffects.CritRate = 0;
			player.BaseDamage -= player.BaseDamage * 20.0 / 100.0;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005178 RID: 20856
		private int m_type = 0;

		// Token: 0x04005179 RID: 20857
		private int m_count = 0;

		// Token: 0x0400517A RID: 20858
		private int m_probability = 0;

		// Token: 0x0400517B RID: 20859
		private int m_delay = 0;

		// Token: 0x0400517C RID: 20860
		private int m_coldDown = 0;

		// Token: 0x0400517D RID: 20861
		private int m_currentId;

		// Token: 0x0400517E RID: 20862
		private int m_added = 0;
	}
}
