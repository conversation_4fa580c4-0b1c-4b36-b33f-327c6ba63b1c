﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E89 RID: 3721
	public class CE1183 : BasePetEffect
	{
		// Token: 0x1700149E RID: 5278
		// (get) Token: 0x060080CF RID: 32975 RVA: 0x000325EC File Offset: 0x000307EC
		public int Count
		{
			get
			{
				return this.m_count;
			}
		}

		// Token: 0x060080D0 RID: 32976 RVA: 0x002AA7AC File Offset: 0x002A89AC
		public CE1183(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1183, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060080D1 RID: 32977 RVA: 0x002AA82C File Offset: 0x002A8A2C
		public override bool Start(Living living)
		{
			CE1183 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1183) as CE1183;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080D2 RID: 32978 RVA: 0x002AA88C File Offset: 0x002A8A8C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				player.BaseGuard += (double)this.m_added;
				player.Game.SendPetBuff(player, base.Info, true);
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060080D3 RID: 32979 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060080D4 RID: 32980 RVA: 0x002AA904 File Offset: 0x002A8B04
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060080D5 RID: 32981 RVA: 0x002AA938 File Offset: 0x002A8B38
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BaseGuard -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004FFC RID: 20476
		private int m_type = 0;

		// Token: 0x04004FFD RID: 20477
		private int m_count = 0;

		// Token: 0x04004FFE RID: 20478
		private int m_probability = 0;

		// Token: 0x04004FFF RID: 20479
		private int m_delay = 0;

		// Token: 0x04005000 RID: 20480
		private int m_coldDown = 0;

		// Token: 0x04005001 RID: 20481
		private int m_currentId;

		// Token: 0x04005002 RID: 20482
		private int m_added = 0;
	}
}
