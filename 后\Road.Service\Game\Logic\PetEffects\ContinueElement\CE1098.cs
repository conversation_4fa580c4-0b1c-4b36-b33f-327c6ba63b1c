﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E68 RID: 3688
	public class CE1098 : BasePetEffect
	{
		// Token: 0x06007FFC RID: 32764 RVA: 0x002A76B4 File Offset: 0x002A58B4
		public CE1098(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1098, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FFD RID: 32765 RVA: 0x002A7734 File Offset: 0x002A5934
		public override bool Start(Living living)
		{
			CE1098 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1098) as CE1098;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FFE RID: 32766 RVA: 0x002A7794 File Offset: 0x002A5994
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.Lucky += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007FFF RID: 32767 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008000 RID: 32768 RVA: 0x002A77F4 File Offset: 0x002A59F4
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008001 RID: 32769 RVA: 0x00031D57 File Offset: 0x0002FF57
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Lucky -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F13 RID: 20243
		private int m_type = 0;

		// Token: 0x04004F14 RID: 20244
		private int m_count = 0;

		// Token: 0x04004F15 RID: 20245
		private int m_probability = 0;

		// Token: 0x04004F16 RID: 20246
		private int m_delay = 0;

		// Token: 0x04004F17 RID: 20247
		private int m_coldDown = 0;

		// Token: 0x04004F18 RID: 20248
		private int m_currentId;

		// Token: 0x04004F19 RID: 20249
		private int m_added = 0;
	}
}
