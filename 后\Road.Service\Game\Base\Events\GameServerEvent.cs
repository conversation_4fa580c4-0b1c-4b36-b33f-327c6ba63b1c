﻿using System;

namespace Game.Base.Events
{
	// Token: 0x02000F8B RID: 3979
	public class GameServerEvent : RoadEvent
	{
		// Token: 0x0600878A RID: 34698 RVA: 0x00035EA3 File Offset: 0x000340A3
		protected GameServerEvent(string name)
			: base(name)
		{
		}

		// Token: 0x040053B1 RID: 21425
		public static readonly GameServerEvent Started = new GameServerEvent("Server.Started");

		// Token: 0x040053B2 RID: 21426
		public static readonly GameServerEvent Stopped = new GameServerEvent("Server.Stopped");

		// Token: 0x040053B3 RID: 21427
		public static readonly GameServerEvent WorldSave = new GameServerEvent("Server.WorldSave");
	}
}
