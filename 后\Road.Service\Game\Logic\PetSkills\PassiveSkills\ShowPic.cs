﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CFB RID: 3323
	public class ShowPic : BasePetEffect
	{
		// Token: 0x06007840 RID: 30784 RVA: 0x0002C84C File Offset: 0x0002AA4C
		public ShowPic(string elementID)
			: base(ePetEffectType.ShowPic, elementID)
		{
		}

		// Token: 0x06007841 RID: 30785 RVA: 0x00284F38 File Offset: 0x00283138
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.ShowPic) is ShowPic;
			return flag || base.Start(living);
		}

		// Token: 0x06007842 RID: 30786 RVA: 0x0002C863 File Offset: 0x0002AA63
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x06007843 RID: 30787 RVA: 0x0002C879 File Offset: 0x0002AA79
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x06007844 RID: 30788 RVA: 0x00284F74 File Offset: 0x00283174
		private void player_BeginSelfTurn(Living living)
		{
			bool flag = this.count == 0;
			if (flag)
			{
				this.count = 1;
				living.Game.sendShowPicSkil(living, base.Info, true);
			}
		}

		// Token: 0x04004680 RID: 18048
		private int count = 0;
	}
}
