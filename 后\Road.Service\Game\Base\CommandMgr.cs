﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using System.Text;
using Game.Base.Events;
using Game.Server.Managers;
using log4net;

namespace Game.Base
{
	// Token: 0x02000F74 RID: 3956
	public class CommandMgr
	{
		// Token: 0x170014C6 RID: 5318
		// (get) Token: 0x06008595 RID: 34197 RVA: 0x002BB810 File Offset: 0x002B9A10
		// (set) Token: 0x06008596 RID: 34198 RVA: 0x00035A85 File Offset: 0x00033C85
		public static string[] DisableCommands
		{
			get
			{
				return CommandMgr.m_disabledarray;
			}
			set
			{
				CommandMgr.m_disabledarray = ((value == null) ? new string[0] : value);
			}
		}

		// Token: 0x06008597 RID: 34199 RVA: 0x002BB828 File Offset: 0x002B9A28
		public static GameCommand GetCommand(string cmd)
		{
			return CommandMgr.m_cmds[cmd] as GameCommand;
		}

		// Token: 0x06008598 RID: 34200 RVA: 0x002BB84C File Offset: 0x002B9A4C
		public static GameCommand GuessCommand(string cmd)
		{
			GameCommand gameCommand = CommandMgr.GetCommand(cmd);
			bool flag = gameCommand != null;
			GameCommand gameCommand2;
			if (flag)
			{
				gameCommand2 = gameCommand;
			}
			else
			{
				string text = cmd.ToLower();
				IDictionaryEnumerator enumerator = CommandMgr.m_cmds.GetEnumerator();
				while (enumerator.MoveNext())
				{
					GameCommand gameCommand3 = enumerator.Value as GameCommand;
					string text2 = enumerator.Key as string;
					bool flag2 = gameCommand3 == null || !text2.ToLower().StartsWith(text);
					if (!flag2)
					{
						gameCommand = gameCommand3;
						break;
					}
				}
				gameCommand2 = gameCommand;
			}
			return gameCommand2;
		}

		// Token: 0x06008599 RID: 34201 RVA: 0x002BB8D8 File Offset: 0x002B9AD8
		public static string[] GetCommandList(ePrivLevel plvl, bool addDesc)
		{
			IDictionaryEnumerator enumerator = CommandMgr.m_cmds.GetEnumerator();
			ArrayList arrayList = new ArrayList();
			while (enumerator.MoveNext())
			{
				GameCommand gameCommand = enumerator.Value as GameCommand;
				string text = enumerator.Key as string;
				bool flag = gameCommand == null || text == null;
				if (!flag)
				{
					bool flag2 = text[0] == '&';
					if (flag2)
					{
						text = "/" + text.Remove(0, 1);
					}
					bool flag3 = plvl >= (ePrivLevel)gameCommand.m_lvl;
					if (flag3)
					{
						if (addDesc)
						{
							arrayList.Add(text + " - " + gameCommand.m_desc);
						}
						else
						{
							arrayList.Add(gameCommand.m_cmd);
						}
					}
				}
			}
			return (string[])arrayList.ToArray(typeof(string));
		}

		// Token: 0x0600859A RID: 34202 RVA: 0x00035A99 File Offset: 0x00033C99
		[ScriptLoadedEvent]
		public static void OnScriptCompiled(RoadEvent ev, object sender, EventArgs args)
		{
			CommandMgr.LoadCommands();
		}

		// Token: 0x0600859B RID: 34203 RVA: 0x002BB9C0 File Offset: 0x002B9BC0
		public static bool LoadCommands()
		{
			CommandMgr.m_cmds.Clear();
			ArrayList arrayList = new ArrayList(ScriptMgr.Scripts);
			foreach (object obj in arrayList)
			{
				Assembly assembly = (Assembly)obj;
				bool isDebugEnabled = CommandMgr.log.IsDebugEnabled;
				if (isDebugEnabled)
				{
					ILog log = CommandMgr.log;
					string text = "ScriptMgr: Searching for commands in ";
					AssemblyName name = assembly.GetName();
					log.Debug(text + ((name != null) ? name.ToString() : null));
				}
				Type[] types = assembly.GetTypes();
				Type[] array = types;
				Type[] array2 = array;
				foreach (Type type in array2)
				{
					bool flag = !type.IsClass || type.GetInterface("Game.Base.ICommandHandler") == null;
					if (!flag)
					{
						try
						{
							object[] customAttributes = type.GetCustomAttributes(typeof(CmdAttribute), false);
							foreach (CmdAttribute cmdAttribute in customAttributes)
							{
								bool flag2 = false;
								string[] disabledarray = CommandMgr.m_disabledarray;
								string[] array5 = disabledarray;
								string[] array6 = array5;
								foreach (string text2 in array6)
								{
									bool flag3 = cmdAttribute.Cmd.Replace('&', '/') == text2;
									if (flag3)
									{
										flag2 = true;
										CommandMgr.log.Info("Will not load command " + cmdAttribute.Cmd + " as it is disabled in game properties");
										break;
									}
								}
								bool flag4 = flag2;
								if (!flag4)
								{
									bool flag5 = CommandMgr.m_cmds.ContainsKey(cmdAttribute.Cmd);
									if (flag5)
									{
										ILog log2 = CommandMgr.log;
										string cmd = cmdAttribute.Cmd;
										string text3 = " from ";
										AssemblyName name2 = assembly.GetName();
										log2.Info(cmd + text3 + ((name2 != null) ? name2.ToString() : null) + " has been suppressed, a command of that type already exists!");
									}
									else
									{
										bool isDebugEnabled2 = CommandMgr.log.IsDebugEnabled;
										if (isDebugEnabled2)
										{
											CommandMgr.log.Debug("Load: " + cmdAttribute.Cmd + "," + cmdAttribute.Description);
										}
										GameCommand gameCommand = new GameCommand();
										gameCommand.m_usage = cmdAttribute.Usage;
										gameCommand.m_cmd = cmdAttribute.Cmd;
										gameCommand.m_lvl = cmdAttribute.Level;
										gameCommand.m_desc = cmdAttribute.Description;
										gameCommand.m_cmdHandler = (ICommandHandler)Activator.CreateInstance(type);
										CommandMgr.m_cmds.Add(cmdAttribute.Cmd, gameCommand);
										bool flag6 = cmdAttribute.Aliases != null;
										if (flag6)
										{
											string[] aliases = cmdAttribute.Aliases;
											string[] array8 = aliases;
											string[] array9 = array8;
											foreach (string text4 in array9)
											{
												CommandMgr.m_cmds.Add(text4, gameCommand);
											}
										}
									}
								}
							}
						}
						catch (Exception ex)
						{
							bool isErrorEnabled = CommandMgr.log.IsErrorEnabled;
							if (isErrorEnabled)
							{
								CommandMgr.log.Error("LoadCommands", ex);
							}
						}
					}
				}
			}
			CommandMgr.log.Info("Loaded " + CommandMgr.m_cmds.Count.ToString() + " commands!");
			return true;
		}

		// Token: 0x0600859C RID: 34204 RVA: 0x002BBD60 File Offset: 0x002B9F60
		public static void DisplaySyntax(BaseClient client)
		{
			client.DisplayMessage("Commands list:");
			string[] commandList = CommandMgr.GetCommandList(ePrivLevel.Admin, true);
			string[] array = commandList;
			string[] array2 = array;
			foreach (string text in array2)
			{
				client.DisplayMessage("         " + text);
			}
		}

		// Token: 0x0600859D RID: 34205 RVA: 0x002BBDB8 File Offset: 0x002B9FB8
		public static bool HandleCommandNoPlvl(BaseClient client, string cmdLine)
		{
			try
			{
				string[] array = CommandMgr.ParseCmdLine(cmdLine);
				GameCommand gameCommand = CommandMgr.GuessCommand(array[0]);
				bool flag = gameCommand == null;
				if (flag)
				{
					return false;
				}
				CommandMgr.ExecuteCommand(client, gameCommand, array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = CommandMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					CommandMgr.log.Error("HandleCommandNoPlvl", ex);
				}
			}
			return true;
		}

		// Token: 0x0600859E RID: 34206 RVA: 0x002BBE30 File Offset: 0x002BA030
		private static bool ExecuteCommand(BaseClient client, GameCommand myCommand, string[] pars)
		{
			pars[0] = myCommand.m_cmd;
			return myCommand.m_cmdHandler.OnCommand(client, pars);
		}

		// Token: 0x0600859F RID: 34207 RVA: 0x002BBE58 File Offset: 0x002BA058
		private static string[] ParseCmdLine(string cmdLine)
		{
			bool flag = cmdLine == null;
			if (flag)
			{
				throw new ArgumentNullException("cmdLine");
			}
			List<string> list = new List<string>();
			int num = 0;
			StringBuilder stringBuilder = new StringBuilder(cmdLine.Length >> 1);
			for (int i = 0; i < cmdLine.Length; i++)
			{
				char c = cmdLine[i];
				switch (num)
				{
				case 0:
				{
					bool flag2 = c != ' ';
					if (flag2)
					{
						stringBuilder.Length = 0;
						bool flag3 = c == '"';
						if (flag3)
						{
							num = 2;
						}
						else
						{
							num = 1;
							i--;
						}
					}
					break;
				}
				case 1:
				{
					bool flag4 = c == ' ';
					if (flag4)
					{
						list.Add(stringBuilder.ToString());
						num = 0;
					}
					stringBuilder.Append(c);
					break;
				}
				case 2:
				{
					bool flag5 = c == '"';
					if (flag5)
					{
						list.Add(stringBuilder.ToString());
						num = 0;
					}
					stringBuilder.Append(c);
					break;
				}
				}
			}
			bool flag6 = num != 0;
			if (flag6)
			{
				list.Add(stringBuilder.ToString());
			}
			string[] array = new string[list.Count];
			list.CopyTo(array);
			return array;
		}

		// Token: 0x0400536A RID: 21354
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x0400536B RID: 21355
		private static Hashtable m_cmds = new Hashtable(StringComparer.InvariantCultureIgnoreCase);

		// Token: 0x0400536C RID: 21356
		private static string[] m_disabledarray = new string[0];
	}
}
