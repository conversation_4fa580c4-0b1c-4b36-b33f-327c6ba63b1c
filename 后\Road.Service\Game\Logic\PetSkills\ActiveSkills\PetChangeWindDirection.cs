﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D33 RID: 3379
	public class PetChangeWindDirection : BasePetEffect
	{
		// Token: 0x0600796C RID: 31084 RVA: 0x0002D93D File Offset: 0x0002BB3D
		public PetChangeWindDirection(int skillId, string elementID)
			: base(ePetEffectType.PetChangeWindDirection, elementID)
		{
			this.m_skillId = skillId;
		}

		// Token: 0x0600796D RID: 31085 RVA: 0x0028A44C File Offset: 0x0028864C
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetChangeWindDirection) is PetChangeWindDirection;
			return flag || base.Start(living);
		}

		// Token: 0x0600796E RID: 31086 RVA: 0x0002D954 File Offset: 0x0002BB54
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
		}

		// Token: 0x0600796F RID: 31087 RVA: 0x0002D96A File Offset: 0x0002BB6A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
		}

		// Token: 0x06007970 RID: 31088 RVA: 0x0028A488 File Offset: 0x00288688
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_skillId;
			if (flag)
			{
				player.PetEffectTrigger = true;
				player.Game.sendShowPicSkil(player, base.Info, true);
				player.Game.UpdateWind(player.Game.Wind * -1f, true);
			}
		}

		// Token: 0x04004723 RID: 18211
		private int m_skillId;
	}
}
