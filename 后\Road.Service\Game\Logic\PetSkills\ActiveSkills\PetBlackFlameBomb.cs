﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D2E RID: 3374
	public class PetBlackFlameBomb : BasePetEffect
	{
		// Token: 0x06007951 RID: 31057 RVA: 0x0002D77E File Offset: 0x0002B97E
		public PetBlackFlameBomb(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetBlackFlameBomb, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x06007952 RID: 31058 RVA: 0x00289CDC File Offset: 0x00287EDC
		public override bool Start(Living living)
		{
			PetBlackFlameBomb petBlackFlameBomb = living.PetEffectList.GetOfType(ePetEffectType.PetBlackFlameBomb) as PetBlackFlameBomb;
			bool flag = petBlackFlameBomb != null;
			bool flag2;
			if (flag)
			{
				petBlackFlameBomb.m_probability = ((this.m_probability > petBlackFlameBomb.m_probability) ? this.m_probability : petBlackFlameBomb.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007953 RID: 31059 RVA: 0x0002D7AF File Offset: 0x0002B9AF
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerShoot);
		}

		// Token: 0x06007954 RID: 31060 RVA: 0x0002D7C5 File Offset: 0x0002B9C5
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerShoot);
		}

		// Token: 0x06007955 RID: 31061 RVA: 0x00289D3C File Offset: 0x00287F3C
		private void player_PlayerShoot(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				living.AddPetEffect(new PetBlackFlameBombEquip(this.m_count, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004712 RID: 18194
		private int m_count;

		// Token: 0x04004713 RID: 18195
		private int m_probability;

		// Token: 0x04004714 RID: 18196
		private int m_currentId;
	}
}
