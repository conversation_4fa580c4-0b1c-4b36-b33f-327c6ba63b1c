﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{4896D65B-2F8C-497C-A898-6C19AB0B2FB3}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>System</RootNamespace>
    <AssemblyName>EntityFramework</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoStdLib>true</NoStdLib>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoStdLib>true</NoStdLib>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ComponentModel\DataAnnotations\Schema\IndexAttribute.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\BasicCommandTreeVisitor.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\BasicExpressionVisitor.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbAggregate.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbAndExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbApplyExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbArithmeticExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbBinaryExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbCaseExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbCastExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbCommandTree.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbCommandTreeKind.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbComparisonExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbConstantExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbCrossJoinExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbDeleteCommandTree.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbDerefExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbDistinctExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbElementExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbEntityRefExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbExceptExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbExpressionBinding.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbExpressionKind.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbExpressionKindHelper.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbExpressionRebinder.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbExpressionVisitor.2.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbExpressionVisitor.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbFilterExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbFunctionAggregate.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbFunctionCommandTree.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbFunctionExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbGroupAggregate.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbGroupByExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbGroupExpressionBinding.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbInExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbInsertCommandTree.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbIntersectExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbIsEmptyExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbIsNullExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbIsOfExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbJoinExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbLambda.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbLambdaExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbLikeExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbLimitExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbModificationClause.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbModificationCommandTree.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbNewInstanceExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbNotExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbNullExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbOfTypeExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbOrExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbParameterReferenceExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbProjectExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbPropertyExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbQuantifierExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbQueryCommandTree.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbRefExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbRefKeyExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbRelatedEntityRef.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbRelationshipNavigationExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbScanExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbSetClause.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbSkipExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbSortClause.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbSortExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbTreatExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbUnaryExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbUnionAllExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbUpdateCommandTree.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DbVariableReferenceExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\DefaultExpressionVisitor.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\ExpressionBuilder\DbExpressionBuilder.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\ExpressionBuilder\EdmFunctions.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\ExpressionBuilder\Hierarchy\HierarchyIdEdmFunctions.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\ExpressionBuilder\Internal\ArgumentValidation.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\ExpressionBuilder\Internal\EnumerableValidator.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\ExpressionBuilder\Row.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\ExpressionBuilder\Spatial\SpatialEdmFunctions.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\Internal\DbExpressionList.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\Internal\DbExpressionRule.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\Internal\DbExpressionRuleProcessingVisitor.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\Internal\DbExpressionValidator.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\Internal\ExpressionDumper.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\Internal\ExpressionKeyGen.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\Internal\ExpressionPrinter.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\Internal\ParameterRetriever.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\Internal\PatternMatchRule.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\Internal\PatternMatchRuleProcessor.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\Internal\Patterns.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\Internal\ViewSimplifier.cs" />
    <Compile Include="Data\Entity\Core\Common\CommandTrees\Internal\XmlExpressionDumper.cs" />
    <Compile Include="Data\Entity\Core\Common\DataRecordInfo.cs" />
    <Compile Include="Data\Entity\Core\Common\DbCommandDefinition.cs" />
    <Compile Include="Data\Entity\Core\Common\DbProviderManifest.cs" />
    <Compile Include="Data\Entity\Core\Common\DbProviderServices.cs" />
    <Compile Include="Data\Entity\Core\Common\DbXmlEnabledProviderManifest.cs" />
    <Compile Include="Data\Entity\Core\Common\EntityRecordInfo.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\AliasedExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\ApplyClauseItem.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\ApplyKind.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\BuiltInExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\BuiltInKind.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\CaseExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\CollectionTypeDefinition.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\Command.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\CreateRefExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\DerefExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\DistinctKind.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\DotExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\FromClause.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\FromClauseItem.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\FromClauseItemKind.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\FunctionDefinition.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\GroupAggregateExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\GroupByClause.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\GroupPartitionExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\HavingClause.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\Identifier.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\JoinClauseItem.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\JoinKind.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\KeyExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\Literal.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\LiteralKind.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\MethodExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\MultisetConstructorExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\NamespaceImport.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\Node.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\NodeList.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\OrderByClause.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\OrderByClauseItem.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\OrderKind.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\ParenExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\PropDefinition.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\QueryExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\QueryParameter.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\QueryStatement.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\RefExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\RefTypeDefinition.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\RelshipNavigationExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\RowConstructorExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\RowTypeDefinition.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\SelectClause.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\SelectKind.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\Statement.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\AST\WhenThenExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\CqlErrorHelper.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\CqlLexer.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\CqlParser.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\CqlQuery.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\Disposer.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\EntityContainerExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\EntitySqlParser.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\ErrorContext.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\ExpressionResolution.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\ExpressionResolutionClass.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\FreeVariableScopeEntry.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\FunctionAggregateInfo.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\FunctionDefinition.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\FunctionOverloadResolver.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\GroupAggregateInfo.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\GroupAggregateKind.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\GroupKeyAggregateInfo.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\GroupKeyDefinitionScopeEntry.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\GroupPartitionInfo.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\IGetAlternativeName.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\IGroupExpressionExtendedInfo.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\InlineFunctionGroup.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\InlineFunctionInfo.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\InvalidGroupInputRefScopeEntry.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\MetadataEnumMember.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\MetadataFunctionGroup.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\MetadataMember.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\MetadataMemberClass.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\MetadataNamespace.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\MetadataType.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\Pair.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\ParseResult.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\ParserOptions.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\ProjectionItemDefinitionScopeEntry.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\Scope.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\ScopeEntry.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\ScopeEntryKind.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\ScopeManager.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\ScopeRegion.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\SemanticAnalyzer.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\SemanticResolver.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\SourceScopeEntry.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\TypeResolver.cs" />
    <Compile Include="Data\Entity\Core\Common\EntitySql\ValueExpression.cs" />
    <Compile Include="Data\Entity\Core\Common\FieldMetadata.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\DbTypeMap.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\CodeGenEmitter.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\CollectionTranslatorResult.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\ColumnMapKeyBuilder.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\CompensatingCollection.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\Coordinator.2.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\Coordinator.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\CoordinatorFactory.2.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\CoordinatorFactory.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\CoordinatorScratchpad.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\RecordState.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\RecordStateFactory.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\RecordStateScratchpad.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\Shaper.2.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\Shaper.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\ShaperFactory.2.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\ShaperFactory.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\Translator.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\TranslatorArg.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\Materialization\TranslatorResult.cs" />
    <Compile Include="Data\Entity\Core\Common\Internal\MultipartIdentifier.cs" />
    <Compile Include="Data\Entity\Core\Common\QueryCache\CompiledQueryCacheEntry.cs" />
    <Compile Include="Data\Entity\Core\Common\QueryCache\CompiledQueryCacheKey.cs" />
    <Compile Include="Data\Entity\Core\Common\QueryCache\EntityClientCacheKey.cs" />
    <Compile Include="Data\Entity\Core\Common\QueryCache\EntitySqlQueryCacheKey.cs" />
    <Compile Include="Data\Entity\Core\Common\QueryCache\LinqQueryCacheKey.cs" />
    <Compile Include="Data\Entity\Core\Common\QueryCache\QueryCacheEntry.cs" />
    <Compile Include="Data\Entity\Core\Common\QueryCache\QueryCacheKey.cs" />
    <Compile Include="Data\Entity\Core\Common\QueryCache\QueryCacheManager.cs" />
    <Compile Include="Data\Entity\Core\Common\QueryCache\ShaperFactoryQueryCacheKey.cs" />
    <Compile Include="Data\Entity\Core\Common\TypeHelpers.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\AliasGenerator.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\AndExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\BasicVisitor.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\BooleanExpressionTermRewriter.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\BoolExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\Clause.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\CnfClause.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\CnfSentence.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\ConversionContext.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\Converter.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\DnfClause.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\DnfSentence.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\DomainConstraint.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\DomainConstraintConversionContext.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\DomainVariable.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\ExprType.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\FalseExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\GenericConversionContext.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\IdentifierService.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\KnowledgeBase.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\LeafVisitor.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\Literal.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\LiteralVertexPair.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\NegationPusher.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\NormalFormNode.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\NotExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\OrExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\Sentence.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\Simplifier.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\Solver.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\TermCounter.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\TermExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\ToDecisionDiagramConverter.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\TreeExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\Triple.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\TrueExpr.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\Vertex.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Boolean\Visitor.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\ByValueComparer.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\ByValueEqualityComparer.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\CommandHelper.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\DisposableCollectionWrapper.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Helpers.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\InternalBase.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\KeyToListMap.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Memoizer.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\MetadataHelper.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\ModifiableIteratorCollection.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Pair.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\Set.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\StringUtil.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\TrailingSpaceComparer.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\TrailingSpaceStringComparer.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\TreeNode.cs" />
    <Compile Include="Data\Entity\Core\Common\Utils\TreePrinter.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\EntityCommand.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\EntityConnection.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\EntityConnectionStringBuilder.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\EntityDataReader.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\EntityParameter.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\EntityParameterCollection.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\EntityProviderFactory.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\EntityTransaction.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\Internal\DbConnectionOptions.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\Internal\EntityAdapter.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\Internal\EntityCommandDefinition.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\Internal\EntityProviderServices.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\Internal\IEntityAdapter.cs" />
    <Compile Include="Data\Entity\Core\EntityClient\NameValuePair.cs" />
    <Compile Include="Data\Entity\Core\EntityCommandCompilationException.cs" />
    <Compile Include="Data\Entity\Core\EntityCommandExecutionException.cs" />
    <Compile Include="Data\Entity\Core\EntityException.cs" />
    <Compile Include="Data\Entity\Core\EntityKey.cs" />
    <Compile Include="Data\Entity\Core\EntityKeyMember.cs" />
    <Compile Include="Data\Entity\Core\EntityResCategoryAttribute.cs" />
    <Compile Include="Data\Entity\Core\EntityResDescriptionAttribute.cs" />
    <Compile Include="Data\Entity\Core\EntitySqlException.cs" />
    <Compile Include="Data\Entity\Core\EntityUtil.cs" />
    <Compile Include="Data\Entity\Core\FieldNameLookup.cs" />
    <Compile Include="Data\Entity\Core\IEntityStateEntry.cs" />
    <Compile Include="Data\Entity\Core\IEntityStateManager.cs" />
    <Compile Include="Data\Entity\Core\IExtendedDataRecord.cs" />
    <Compile Include="Data\Entity\Core\InternalMappingException.cs" />
    <Compile Include="Data\Entity\Core\InvalidCommandTreeException.cs" />
    <Compile Include="Data\Entity\Core\MappingException.cs" />
    <Compile Include="Data\Entity\Core\Mapping\AssociationSetMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\AssociationSetModificationFunctionMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\AssociationTypeMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\BaseMetadataMappingVisitor.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ColumnMappingBuilder.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ComplexPropertyMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ComplexTypeMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\CompressingHashBuilder.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ConditionPropertyMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\DefaultObjectMappingItemCollection.cs" />
    <Compile Include="Data\Entity\Core\Mapping\EndPropertyMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\EntityContainerMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\EntitySetBaseMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\EntitySetMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\EntityTypeMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\EntityTypeModificationFunctionMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\EntityViewContainer.cs" />
    <Compile Include="Data\Entity\Core\Mapping\EntityViewGenerationAttribute.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportComplexTypeMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportEntityTypeMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportEntityTypeMappingCondition.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportEntityTypeMappingConditionIsNull.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportEntityTypeMappingConditionValue.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportMappingComposable.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportMappingComposableHelper.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportMappingNonComposable.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportNormalizedEntityTypeMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportResultMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportReturnTypeEntityTypeColumnsRenameBuilder.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportReturnTypePropertyMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportReturnTypeScalarPropertyMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportReturnTypeStructuralTypeColumn.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportReturnTypeStructuralTypeColumnRenameMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportStructuralTypeMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\FunctionImportStructuralTypeMappingKB.cs" />
    <Compile Include="Data\Entity\Core\Mapping\InputForComputingCellGroups.cs" />
    <Compile Include="Data\Entity\Core\Mapping\IsNullConditionMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\LineInfo.cs" />
    <Compile Include="Data\Entity\Core\Mapping\MappingBase.cs" />
    <Compile Include="Data\Entity\Core\Mapping\MappingErrorCode.cs" />
    <Compile Include="Data\Entity\Core\Mapping\MappingFragment.cs" />
    <Compile Include="Data\Entity\Core\Mapping\MappingItem.cs" />
    <Compile Include="Data\Entity\Core\Mapping\MappingItemCollection.cs" />
    <Compile Include="Data\Entity\Core\Mapping\MappingItemLoader.cs" />
    <Compile Include="Data\Entity\Core\Mapping\MemberMappingKind.cs" />
    <Compile Include="Data\Entity\Core\Mapping\MetadataMappingHasherVisitor.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ModificationFunctionMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ModificationFunctionMemberPath.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ModificationFunctionParameterBinding.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ModificationFunctionResultBinding.cs" />
    <Compile Include="Data\Entity\Core\Mapping\MslConstructs.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ObjectAssociationEndMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ObjectComplexPropertyMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ObjectMemberMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ObjectMslConstructs.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ObjectNavigationPropertyMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ObjectPropertyMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ObjectTypeMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\OutputFromComputeCellGroups.cs" />
    <Compile Include="Data\Entity\Core\Mapping\PropertyMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ScalarPropertyMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\StorageMappingItemCollection.cs" />
    <Compile Include="Data\Entity\Core\Mapping\StringHashBuilder.cs" />
    <Compile Include="Data\Entity\Core\Mapping\StructuralTypeMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\TypeMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\AssociationSetMetadata.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\ChangeNode.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\CompositeKey.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\DynamicUpdateCommand.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\ExtractedStateEntry.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\ExtractorMetadata.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\FunctionUpdateCommand.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\Graph.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\KeyManager.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\ModificationFunctionMappingTranslator.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\ModificationOperator.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\ModifiedPropertiesBehavior.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\Propagator.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\PropagatorFlags.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\PropagatorResult.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\RecordConverter.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\SourceInterpreter.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\TableChangeProcessor.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\UndirectedGraph.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\UpdateCommand.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\UpdateCommandKind.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\UpdateCommandOrderer.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\UpdateCompiler.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\UpdateExpressionVisitor.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\UpdateTranslator.cs" />
    <Compile Include="Data\Entity\Core\Mapping\Update\Internal\ViewLoader.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ValueCondition.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ValueConditionMapping.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\BasicViewGenerator.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CellCreator.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CellGroupValidator.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CellPartitioner.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CellTreeSimplifier.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\ConfigViewGenerator.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CqlGeneration\BooleanProjectedSlot.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CqlGeneration\CaseCqlBlock.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CqlGeneration\CqlBlock.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CqlGeneration\CqlWriter.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CqlGeneration\ExtentCqlBlock.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CqlGeneration\JoinCqlBlock.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CqlGeneration\QualifiedSlot.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CqlGeneration\SlotInfo.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CqlGeneration\UnionCqlBlock.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\CqlGenerator.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\DiscriminatorMap.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\GeneratedView.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\PerfType.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\DefaultTileProcessor.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\FragmentQuery.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\FragmentQueryKB.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\FragmentQueryKBChaseSupport.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\FragmentQueryProcessor.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\ITileQuery.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\QueryRewriter.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\RewritingPass.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\RewritingProcessor.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\RewritingSimplifier.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\Tile.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\TileBinaryOperator.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\TileNamed.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\TileOpKind.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\TileProcessor.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\QueryRewriting\TileQueryProcessor.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\BoolExpression.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\BoolLiteral.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\CaseStatement.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\CaseStatementProjectedSlot.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\Cell.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\CellIdBoolean.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\CellLabel.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\CellQuery.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\CellTreeNode.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\CellTreeOpType.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\Constant.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\ConstantProjectedSlot.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\CqlIdentifiers.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\Domain.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\ErrorLog.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\ExtentKey.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\LeafCellTreeNode.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\LeftCellWrapper.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\MemberDomainMap.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\MemberMaps.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\MemberPath.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\MemberProjectedSlot.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\MemberProjectionIndex.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\MemberRestriction.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\NegatedConstant.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\OpCellTreeNode.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\ProjectedSlot.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\QualifiedCellIdBoolean.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\RoleBoolean.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\ScalarConstant.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\ScalarRestriction.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\TrueFalseLiteral.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\TypeConstant.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\TypeRestriction.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\ViewTarget.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Structures\WithRelationship.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Utils\ExceptionHelpers.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Utils\ExternalCalls.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Utils\ViewGenErrorCode.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\BasicCellRelation.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\BasicKeyConstraint.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\CellRelation.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\ConditionComparer.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\ConstraintBase.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\ErrorPatternMatcher.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\ForeignConstraint.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\KeyConstraint.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\LCWComparer.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\RewritingValidator.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\SchemaConstraints.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\ViewCellRelation.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\ViewCellSlot.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\Validation\ViewKeyConstraint.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\ViewgenContext.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\ViewGenerator.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\ViewgenGatekeeper.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\ViewGenMode.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\ViewGenResults.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewGeneration\ViewGenTraceLevel.cs" />
    <Compile Include="Data\Entity\Core\Mapping\ViewValidator.cs" />
    <Compile Include="Data\Entity\Core\MetadataException.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\AspProxy.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\AssemblyCache.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\AssemblyCacheEntry.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\AssociationEndMember.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\AssociationSet.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\AssociationSetEnd.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\AssociationType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\BuiltInTypeKind.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\CacheForPrimitiveTypes.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ClrComplexType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ClrEntityType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ClrEnumType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ClrPerspective.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\CodeFirstOSpaceLoader.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\CodeFirstOSpaceTypeFactory.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\CollectionKind.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\CollectionType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ComplexType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ConcurrencyMode.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\Converter.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\CsdlSerializer.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\CustomAssemblyResolver.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\DataModelErrorEventArgs.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\DataModelValidationRule.2.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\DataModelValidationRule.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\DataModelValidationRuleSet.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\DataModelValidator.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\DataSpace.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\DbDatabaseMapping.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\DbModelExtensions.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\DefaultAssemblyResolver.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\Documentation.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmConstants.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmError.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmFunction.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmFunctionPayload.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmItemCollection.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmItemError.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmMember.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmModel.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmModelRuleSet.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmModelSemanticValidationRules.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmModelSyntacticValidationRules.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmModelValidationContext.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmModelValidationRule.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmModelValidationVisitor.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmProperty.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmSchemaError.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmSchemaErrorSeverity.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmSerializationVisitor.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmValidator.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EdmXmlSchemaWriter.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EntityContainer.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EntitySet.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EntitySetBase.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EntitySetBaseCollection.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EntityType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EntityTypeBase.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EnumMember.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\EnumType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ExpensiveOSpaceLoader.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\Facet.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\FacetDescription.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\FacetValueContainer.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\FacetValues.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\FilteredReadOnlyMetadataCollection.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ForeignKeyBuilder.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\FunctionParameter.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\GlobalItem.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\Helper.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\IBaseList.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\IEdmModelAdapter.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ImmutableAssemblyCacheEntry.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\INamedDataModelItem.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ItemCollection.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\KnownAssembliesSet.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\KnownAssemblyEntry.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\LoadMessageLogger.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\LockedAssemblyCache.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MappingMetadataHelper.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MemberCollection.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataArtifactAssemblyResolver.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataArtifactLoader.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataArtifactLoaderComposite.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataArtifactLoaderCompositeFile.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataArtifactLoaderCompositeResource.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataArtifactLoaderFile.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataArtifactLoaderResource.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataArtifactLoaderXmlReaderWrapper.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataAssemblyHelper.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataCache.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataCollection.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataItem.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataItemHelper.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataOptimization.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataProperty.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataPropertyAttribute.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataPropertyCollection.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataPropertyValue.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MetadataWorkspace.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ModelPerspective.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MslSerializer.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MslXmlSchemaWriter.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\MutableAssemblyCacheEntry.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\NavigationProperty.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\NavigationPropertyAccessor.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ObjectItemAssemblyLoader.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ObjectItemAttributeAssemblyLoader.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ObjectItemCachedAssemblyLoader.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ObjectItemCollection.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ObjectItemConventionAssemblyLoader.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ObjectItemLoadingSessionData.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ObjectItemNoOpAssemblyLoader.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\OcAssemblyCache.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\OperationAction.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\OSpaceTypeFactory.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ParameterMode.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ParameterTypeSemantics.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\Perspective.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\PrimitiveType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\PrimitiveTypeKind.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\PropertyKind.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\Provider\ClrProviderManifest.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\Provider\EdmProviderManifest.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\Provider\EdmProviderManifestFunctionBuilder.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\Provider\EdmProviderManifestHierarchyIdFunctions.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\Provider\EdmProviderManifestSpatialFunctions.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ReadOnlyMetadataCollection.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ReferentialConstraint.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\RefType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\RelationshipEndMember.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\RelationshipMultiplicity.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\RelationshipMultiplicityConverter.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\RelationshipSet.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\RelationshipType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\RowType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\SafeLink.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\SafeLinkCollection.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\SimpleType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\SsdlSerializer.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\StoreGeneratedPattern.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\StoreItemCollection.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\StructuralType.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\TargetPerspective.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\TypeSemantics.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\TypeUsage.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\Util.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ValidationErrorEventArgs.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\ValidationSeverity.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\XmlConstants.cs" />
    <Compile Include="Data\Entity\Core\Metadata\Edm\XmlSchemaWriter.cs" />
    <Compile Include="Data\Entity\Core\ObjectNotFoundException.cs" />
    <Compile Include="Data\Entity\Core\Objects\CompiledQuery.cs" />
    <Compile Include="Data\Entity\Core\Objects\CurrentValueRecord.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\ComplexObject.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EdmComplexPropertyAttribute.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EdmComplexTypeAttribute.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EdmEntityTypeAttribute.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EdmEnumTypeAttribute.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EdmFunctionAttribute.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EdmPropertyAttribute.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EdmRelationshipAttribute.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EdmRelationshipNavigationPropertyAttribute.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EdmScalarPropertyAttribute.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EdmSchemaAttribute.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EdmTypeAttribute.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EntityCollection.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EntityObject.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EntityReference.2.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\EntityReference.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\IEntityChangeTracker.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\IEntityWithChangeTracker.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\IEntityWithKey.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\IEntityWithRelationships.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\IRelatedEnd.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\IRelationshipFixer.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\RelatedEnd.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\RelationshipFixer.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\RelationshipKind.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\RelationshipManager.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\RelationshipNavigation.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataClasses\StructuralObject.cs" />
    <Compile Include="Data\Entity\Core\Objects\DataRecordObjectView.cs" />
    <Compile Include="Data\Entity\Core\Objects\DbUpdatableDataRecord.cs" />
    <Compile Include="Data\Entity\Core\Objects\DelegateFactory.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\Binding.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\BindingContext.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\CompiledELinqQueryState.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\ELinqQueryState.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\ExpressionConverter.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\Funcletizer.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\InitializerMetadata.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\InitializerMetadataKind.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\LinqExpressionNormalizer.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\ObjectQueryProvider.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\QueryParameterExpression.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\ReflectionUtil.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\SequenceMethod.cs" />
    <Compile Include="Data\Entity\Core\Objects\ELinq\TypeSystem.cs" />
    <Compile Include="Data\Entity\Core\Objects\EntityEntry.cs" />
    <Compile Include="Data\Entity\Core\Objects\EntityFunctions.cs" />
    <Compile Include="Data\Entity\Core\Objects\EntitySetQualifiedType.cs" />
    <Compile Include="Data\Entity\Core\Objects\EntitySqlQueryState.cs" />
    <Compile Include="Data\Entity\Core\Objects\ExecutionOptions.cs" />
    <Compile Include="Data\Entity\Core\Objects\FieldDescriptor.cs" />
    <Compile Include="Data\Entity\Core\Objects\IntBox.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\BaseEntityWrapper.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\BaseProxyImplementor.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\BufferedDataReader.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\BufferedDataRecord.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\ComplexTypeMaterializer.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\DataContractImplementor.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\EntityProxyFactory.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\EntityProxyMemberInfo.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\EntityProxyTypeInfo.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\EntitySqlQueryBuilder.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\EntityWithChangeTrackerStrategy.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\EntityWithKeyStrategy.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\EntityWrapper.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\EntityWrapperFactory.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\EntityWrapperWithoutRelationships.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\EntityWrapperWithRelationships.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\ForeignKeyFactory.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\IChangeTrackingStrategy.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\IEntityKeyStrategy.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\IEntityWrapper.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\IPocoImplementor.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\IPropertyAccessorStrategy.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\LazyLoadBehavior.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\LazyLoadImplementor.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\LightweightEntityWrapper.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\NullEntityWrapper.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\ObjectFullSpanRewriter.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\ObjectQueryExecutionPlan.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\ObjectQueryExecutionPlanFactory.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\ObjectQueryState.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\ObjectSpanRewriter.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\PocoEntityKeyStrategy.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\PocoPropertyAccessorStrategy.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\SerializableImplementor.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\ShapedBufferedDataRecord.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\ShapelessBufferedDataRecord.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\SnapshotChangeTrackingStrategy.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\SpanIndex.cs" />
    <Compile Include="Data\Entity\Core\Objects\Internal\TransactionManager.cs" />
    <Compile Include="Data\Entity\Core\Objects\IObjectSet.cs" />
    <Compile Include="Data\Entity\Core\Objects\IObjectView.cs" />
    <Compile Include="Data\Entity\Core\Objects\IObjectViewData.cs" />
    <Compile Include="Data\Entity\Core\Objects\MaterializedDataRecord.cs" />
    <Compile Include="Data\Entity\Core\Objects\MergeOption.cs" />
    <Compile Include="Data\Entity\Core\Objects\NextResultGenerator.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectContext.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectContextOptions.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectMaterializedEventArgs.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectMaterializedEventHandler.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectParameter.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectParameterCollection.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectQuery.2.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectQuery.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectResult.2.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectResult.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectSet.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectStateEntry.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectStateEntryDbDataRecord.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectStateEntryDbUpdatableDataRecord.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectStateEntryOriginalDbUpdatableDataRecord_Internal.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectStateEntryOriginalDbUpdatableDataRecord_Public.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectStateManager.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectStateValueRecord.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectView.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectViewEntityCollectionData.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectViewFactory.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectViewListener.cs" />
    <Compile Include="Data\Entity\Core\Objects\ObjectViewQueryResultData.cs" />
    <Compile Include="Data\Entity\Core\Objects\OriginalValueRecord.cs" />
    <Compile Include="Data\Entity\Core\Objects\ProxyDataContractResolver.cs" />
    <Compile Include="Data\Entity\Core\Objects\RefreshMode.cs" />
    <Compile Include="Data\Entity\Core\Objects\RelationshipEntry.cs" />
    <Compile Include="Data\Entity\Core\Objects\RelationshipWrapper.cs" />
    <Compile Include="Data\Entity\Core\Objects\SaveOptions.cs" />
    <Compile Include="Data\Entity\Core\Objects\Span.cs" />
    <Compile Include="Data\Entity\Core\Objects\StateManagerMemberMetadata.cs" />
    <Compile Include="Data\Entity\Core\Objects\StateManagerTypeMetadata.cs" />
    <Compile Include="Data\Entity\Core\Objects\StateManagerValue.cs" />
    <Compile Include="Data\Entity\Core\OptimisticConcurrencyException.cs" />
    <Compile Include="Data\Entity\Core\PropertyConstraintException.cs" />
    <Compile Include="Data\Entity\Core\ProviderIncompatibleException.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\AggregateOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\AncillaryOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ApplyBaseOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ArithmeticOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\BasicOpVisitor.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\BasicOpVisitorOfNode.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\BasicOpVisitorOfT.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\BitVec.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\CaseOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\CastOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\CollectionColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\CollectionInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\CollectOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ColumnMapCopier.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ColumnMapFactory.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ColumnMapVisitor.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ColumnMapVisitorWithResults.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ColumnMD.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ColumnVar.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\Command.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ComparisonOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ComplexTypeColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ComputedVar.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ConditionalOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ConstantBaseOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ConstantOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ConstantPredicateOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ConstrainedSortOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\CrossApplyOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\CrossJoinOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\DerefOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\DiscriminatedCollectionColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\DiscriminatedEntityIdentity.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\DiscriminatedNewEntityOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\DistinctOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\Dump.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ElementOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\EntityColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\EntityIdentity.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ExceptOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ExistsOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ExplicitDiscriminatorMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ExtendedNodeInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\FilterOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\FullOuterJoinOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\FunctionOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\GetEntityRefOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\GetRefKeyOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\GroupByBaseOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\GroupByIntoOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\GroupByOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\InnerJoinOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\InternalConstantOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\IntersectOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\IsOfOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\JoinBaseOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\KeyVec.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\LeafOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\LeftOuterJoinOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\LikeOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\MultipleDiscriminatorPolymorphicColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\MultiStreamNestOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\NavigateOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\NestBaseOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\NewEntityBaseOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\NewEntityOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\NewInstanceOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\NewMultisetOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\NewRecordOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\Node.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\NodeCounter.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\NodeInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\NodeInfoVisitor.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\NullOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\NullSentinelOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\Op.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\OpCopier.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\OpDelegate.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\OpType.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\OuterApplyOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ParameterVar.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\PatternMatchRule.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\PhysicalOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\PhysicalProjectOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ProjectOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\PropertyOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\RecordColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\RefColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\RefOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\RelOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\RelProperty.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\RelPropertyHelper.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\RelPropertyOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\RowCount.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\Rule.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\RulePatternOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\RuleProcessingContext.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\RuleProcessor.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ScalarColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ScalarOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ScanTableBaseOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ScanTableOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\ScanViewOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SetOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SetOpVar.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SimpleCollectionColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SimpleColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SimpleEntityIdentity.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SimplePolymorphicColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SimpleRule.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SingleRowOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SingleRowTableOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SingleStreamNestOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SoftCastOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SortBaseOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SortKey.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SortOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\StructuredColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\SubTreeId.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\Table.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\TableMD.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\TreatOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\TypedColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\UnionAllOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\UnnestOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\Var.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\VarDefListOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\VarDefOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\VarList.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\VarMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\VarRefColumnMap.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\VarRefOp.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\VarType.cs" />
    <Compile Include="Data\Entity\Core\Query\InternalTrees\VarVec.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\AggregatePushdown.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\AggregatePushdownUtil.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\AllPropertyRef.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ApplyOpRules.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\AugmentedJoinNode.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\AugmentedNode.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\AugmentedTableNode.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\CodeGen.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\CollectionVarInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ColumnMapProcessor.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ColumnMapTranslator.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ColumnMapTranslatorTranslationDelegate.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ConstrainedSortOpRules.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ConstraintManager.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\CTreeGenerator.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\DiscriminatorMapInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\DistinctOpRules.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\EntitySetIdPropertyRef.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ExtentPair.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\FilterOpRules.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ForeignKeyConstraint.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\GroupAggregateRefComputingVisitor.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\GroupAggregateVarComputationTranslator.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\GroupAggregateVarInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\GroupAggregateVarInfoManager.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\GroupAggregateVarRefInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\GroupByOpRules.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ITreeGenerator.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\JoinEdge.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\JoinElimination.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\JoinGraph.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\JoinKind.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\JoinOpRules.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\KeyPullup.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\NestedPropertyRef.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\NestPullup.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\NominalTypeEliminator.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\Normalizer.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\NullSemantics.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\NullSentinelPropertyRef.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\OpCopierTrackingCollectionVars.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\PlanCompiler.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\PlanCompilerPhase.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\PlanCompilerUtil.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\Predicate.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\PreProcessor.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\PrimitiveTypeVarInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ProjectionPruner.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ProjectOpRules.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\PropertyPushdownHelper.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\PropertyRef.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\PropertyRefList.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ProviderCommandInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ProviderCommandInfoUtils.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\RelPropertyRef.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\RootTypeInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\ScalarOpRules.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\SetOpRules.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\SimplePropertyRef.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\SingleRowOpRules.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\SortOpRules.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\SortRemover.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\StructuredTypeInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\StructuredTypeNullabilityAnalyzer.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\StructuredVarInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\SubqueryTrackingVisitor.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\TransformationRules.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\TransformationRulesContext.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\TransformationRulesGroup.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\TryGetValue.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\TypeIdKind.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\TypeIdPropertyRef.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\TypeInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\TypeUsageEqualityComparer.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\TypeUtils.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\VarInfo.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\VarInfoKind.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\VarInfoMap.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\VarRefManager.cs" />
    <Compile Include="Data\Entity\Core\Query\PlanCompiler\VarRemapper.cs" />
    <Compile Include="Data\Entity\Core\Query\ResultAssembly\BridgeDataReader.cs" />
    <Compile Include="Data\Entity\Core\Query\ResultAssembly\BridgeDataReaderFactory.cs" />
    <Compile Include="Data\Entity\Core\Query\ResultAssembly\BridgeDataRecord.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\Action.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\AddErrorKind.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\AliasResolver.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\AttributeValueNotification.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\BooleanFacetDescriptionElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\ByteFacetDescriptionElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\CollectionTypeElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\DocumentationElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\EntityContainer.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\EntityContainerAssociationSet.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\EntityContainerAssociationSetEnd.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\EntityContainerEntitySet.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\EntityContainerEntitySetDefiningQuery.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\EntityContainerRelationshipSet.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\EntityContainerRelationshipSetEnd.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\EntityKeyElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\ErrorCode.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\FacetDescriptionElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\FacetEnabledSchemaElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\FilteredSchemaElementLookUpTable.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\Function.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\FunctionCommandText.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\FunctionImportElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\IntegerFacetDescriptionElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\IRelationship.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\IRelationshipEnd.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\ISchemaElementLookUpTable.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\ModelFunction.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\ModelFunctionTypeElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\NavigationProperty.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\OnOperation.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\Operation.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\Parameter.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\PrimitiveSchema.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\Property.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\PropertyRefElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\ProviderManifestNeeded.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\ReferenceTypeElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\ReferentialConstraint.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\ReferentialConstraintRoleElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\Relationship.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\RelationshipEnd.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\RelationshipEndCollection.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\ReturnType.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\ReturnValue.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\RowTypeElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\RowTypePropertyElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\ScalarType.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\Schema.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\SchemaComplexType.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\SchemaDataModelOption.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\SchemaElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\SchemaElementLookUpTable.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\SchemaElementLookUpTableEnumerator.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\SchemaEntityType.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\SchemaEnumMember.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\SchemaEnumType.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\SchemaManager.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\SchemaType.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\SridFacetDescriptionElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\StructuredProperty.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\StructuredType.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\TextElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\TypeElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\TypeModifier.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\TypeRefElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\TypeUsageBuilder.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\UsingElement.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\Utils.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\ValidationHelper.cs" />
    <Compile Include="Data\Entity\Core\SchemaObjectModel\XmlSchemaResource.cs" />
    <Compile Include="Data\Entity\Core\UpdateException.cs" />
    <Compile Include="Data\Entity\CreateDatabaseIfNotExists.cs" />
    <Compile Include="Data\Entity\Database.cs" />
    <Compile Include="Data\Entity\DbConfiguration.cs" />
    <Compile Include="Data\Entity\DbConfigurationTypeAttribute.cs" />
    <Compile Include="Data\Entity\DbContext.cs" />
    <Compile Include="Data\Entity\DbContextTransaction.cs" />
    <Compile Include="Data\Entity\DbFunctionAttribute.cs" />
    <Compile Include="Data\Entity\DbFunctions.cs" />
    <Compile Include="Data\Entity\DbModelBuilder.cs" />
    <Compile Include="Data\Entity\DbModelBuilderVersion.cs" />
    <Compile Include="Data\Entity\DbModelBuilderVersionAttribute.cs" />
    <Compile Include="Data\Entity\DbSet.2.cs" />
    <Compile Include="Data\Entity\DbSet.cs" />
    <Compile Include="Data\Entity\DropCreateDatabaseAlways.cs" />
    <Compile Include="Data\Entity\DropCreateDatabaseIfModelChanges.cs" />
    <Compile Include="Data\Entity\Edm\EdmModelVisitor.cs" />
    <Compile Include="Data\Entity\EntityState.cs" />
    <Compile Include="Data\Entity\Hierarchy\DbHierarchyServices.cs" />
    <Compile Include="Data\Entity\Hierarchy\HierarchyId.cs" />
    <Compile Include="Data\Entity\IDatabaseInitializer.cs" />
    <Compile Include="Data\Entity\IDbSet.cs" />
    <Compile Include="Data\Entity\Infrastructure\Annotations\AnnotationCodeGenerator.cs" />
    <Compile Include="Data\Entity\Infrastructure\Annotations\AnnotationValues.cs" />
    <Compile Include="Data\Entity\Infrastructure\Annotations\CompatibilityResult.cs" />
    <Compile Include="Data\Entity\Infrastructure\Annotations\IMergeableAnnotation.cs" />
    <Compile Include="Data\Entity\Infrastructure\Annotations\IndexAnnotation.cs" />
    <Compile Include="Data\Entity\Infrastructure\Annotations\IndexAnnotationSerializer.cs" />
    <Compile Include="Data\Entity\Infrastructure\Annotations\IndexAttributeExtensions.cs" />
    <Compile Include="Data\Entity\Infrastructure\CommitFailedException.cs" />
    <Compile Include="Data\Entity\Infrastructure\CommitFailureHandler.cs" />
    <Compile Include="Data\Entity\Infrastructure\ConsolidatedIndex.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbChangeTracker.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbCollectionEntry.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbCollectionEntry.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbCompiledModel.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbComplexPropertyEntry.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbComplexPropertyEntry.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbConnectionInfo.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbConnectionStringOrigin.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbContextConfiguration.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbContextInfo.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbEntityEntry.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbEntityEntry.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbExecutionStrategy.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbMemberEntry.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbMemberEntry.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbModel.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbModelStore.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbPropertyEntry.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbPropertyEntry.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbPropertyValues.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbProviderInfo.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbQuery.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbQuery.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbRawSqlQuery.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbRawSqlQuery.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbReferenceEntry.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbReferenceEntry.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbSqlQuery.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbSqlQuery.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbUpdateConcurrencyException.cs" />
    <Compile Include="Data\Entity\Infrastructure\DbUpdateException.cs" />
    <Compile Include="Data\Entity\Infrastructure\DefaultDbModelStore.cs" />
    <Compile Include="Data\Entity\Infrastructure\DefaultDbProviderFactoryResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DefaultExecutionStrategy.cs" />
    <Compile Include="Data\Entity\Infrastructure\DefaultManifestTokenResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DefaultTransactionHandler.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\AppConfigDependencyResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\CachingDependencyResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\ClrTypeAnnotationSerializer.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\CompositeResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\DatabaseInitializerResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\DbConfigurationFinder.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\DbConfigurationLoadedEventArgs.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\DbConfigurationLoader.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\DbConfigurationManager.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\DbDependencyResolverExtensions.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\DefaultExecutionStrategyResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\DefaultInvariantNameResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\DefaultProviderFactoryResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\DefaultProviderServicesResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\ExecutionStrategyResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\IDbDependencyResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\InternalConfiguration.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\InvariantNameResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\NamedDbProviderService.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\ProviderServicesFactory.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\ResolverChain.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\RootDependencyResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\SingletonDependencyResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\TransactionContextInitializerResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\TransactionHandlerResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\DependencyResolution\WrappingDependencyResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\Design\AppConfigReader.cs" />
    <Compile Include="Data\Entity\Infrastructure\Design\Executor.cs" />
    <Compile Include="Data\Entity\Infrastructure\Design\ForwardingProxy.cs" />
    <Compile Include="Data\Entity\Infrastructure\Design\HandlerBase.cs" />
    <Compile Include="Data\Entity\Infrastructure\Design\IReportHandler.cs" />
    <Compile Include="Data\Entity\Infrastructure\Design\IResultHandler.cs" />
    <Compile Include="Data\Entity\Infrastructure\Design\IResultHandler2.cs" />
    <Compile Include="Data\Entity\Infrastructure\Design\Reporter.cs" />
    <Compile Include="Data\Entity\Infrastructure\Design\ReportHandler.cs" />
    <Compile Include="Data\Entity\Infrastructure\Design\ResultHandler.cs" />
    <Compile Include="Data\Entity\Infrastructure\Design\WrappedReportHandler.cs" />
    <Compile Include="Data\Entity\Infrastructure\Design\WrappedResultHandler.cs" />
    <Compile Include="Data\Entity\Infrastructure\EdmMetadata.cs" />
    <Compile Include="Data\Entity\Infrastructure\EdmxReader.cs" />
    <Compile Include="Data\Entity\Infrastructure\EdmxWriter.cs" />
    <Compile Include="Data\Entity\Infrastructure\ExecutionStrategyKey.cs" />
    <Compile Include="Data\Entity\Infrastructure\IDbAsyncEnumerable.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\IDbAsyncEnumerable.cs" />
    <Compile Include="Data\Entity\Infrastructure\IDbAsyncEnumerableExtensions.cs" />
    <Compile Include="Data\Entity\Infrastructure\IDbAsyncEnumerator.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\IDbAsyncEnumerator.cs" />
    <Compile Include="Data\Entity\Infrastructure\IDbAsyncEnumeratorExtensions.cs" />
    <Compile Include="Data\Entity\Infrastructure\IDbAsyncQueryProvider.cs" />
    <Compile Include="Data\Entity\Infrastructure\IDbConnectionFactory.cs" />
    <Compile Include="Data\Entity\Infrastructure\IDbContextFactory.cs" />
    <Compile Include="Data\Entity\Infrastructure\IDbExecutionStrategy.cs" />
    <Compile Include="Data\Entity\Infrastructure\IDbModelCacheKey.cs" />
    <Compile Include="Data\Entity\Infrastructure\IDbModelCacheKeyProvider.cs" />
    <Compile Include="Data\Entity\Infrastructure\IDbProviderFactoryResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\IManifestTokenResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\IMetadataAnnotationSerializer.cs" />
    <Compile Include="Data\Entity\Infrastructure\IncludeMetadataConvention.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\BeginTransactionInterceptionContext.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\CancelableDbCommandDispatcher.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\CancelableEntityConnectionDispatcher.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DatabaseLogFormatter.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DatabaseLogger.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbCommandDispatcher.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbCommandInterceptionContext.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbCommandInterceptionContext.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbCommandInterceptor.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbCommandTreeDispatcher.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbCommandTreeInterceptionContext.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbConfigurationDispatcher.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbConfigurationInterceptionContext.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbConnectionDispatcher.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbConnectionInterceptionContext.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbConnectionInterceptionContext.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbConnectionPropertyInterceptionContext.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbDispatchers.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbInterception.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbInterceptionContext.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbTransactionDispatcher.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbTransactionInterceptionContext.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\DbTransactionInterceptionContext.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\EnlistTransactionInterceptionContext.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\ICancelableDbCommandInterceptor.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\ICancelableEntityConnectionInterceptor.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\IDbCommandInterceptor.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\IDbCommandTreeInterceptor.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\IDbConfigurationInterceptor.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\IDbConnectionInterceptor.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\IDbInterceptor.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\IDbMutableInterceptionContext.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\IDbMutableInterceptionContext.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\IDbTransactionInterceptor.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\InterceptionContextMutableData.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\InterceptionContextMutableData.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\InternalDispatcher.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\MutableInterceptionContext.2.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\MutableInterceptionContext.cs" />
    <Compile Include="Data\Entity\Infrastructure\Interception\PropertyInterceptionContext.cs" />
    <Compile Include="Data\Entity\Infrastructure\IObjectContextAdapter.cs" />
    <Compile Include="Data\Entity\Infrastructure\IProviderInvariantName.cs" />
    <Compile Include="Data\Entity\Infrastructure\LocalDbConnectionFactory.cs" />
    <Compile Include="Data\Entity\Infrastructure\MappingViews\DbMappingView.cs" />
    <Compile Include="Data\Entity\Infrastructure\MappingViews\DbMappingViewCache.cs" />
    <Compile Include="Data\Entity\Infrastructure\MappingViews\DbMappingViewCacheFactory.cs" />
    <Compile Include="Data\Entity\Infrastructure\MappingViews\DbMappingViewCacheTypeAttribute.cs" />
    <Compile Include="Data\Entity\Infrastructure\MappingViews\DefaultDbMappingViewCacheFactory.cs" />
    <Compile Include="Data\Entity\Infrastructure\ModelContainerConvention.cs" />
    <Compile Include="Data\Entity\Infrastructure\ModelNamespaceConvention.cs" />
    <Compile Include="Data\Entity\Infrastructure\Net40DefaultDbProviderFactoryResolver.cs" />
    <Compile Include="Data\Entity\Infrastructure\ObjectReferenceEqualityComparer.cs" />
    <Compile Include="Data\Entity\Infrastructure\Pluralization\BidirectionalDictionary.cs" />
    <Compile Include="Data\Entity\Infrastructure\Pluralization\CustomPluralizationEntry.cs" />
    <Compile Include="Data\Entity\Infrastructure\Pluralization\EnglishPluralizationService.cs" />
    <Compile Include="Data\Entity\Infrastructure\Pluralization\IPluralizationService.cs" />
    <Compile Include="Data\Entity\Infrastructure\Pluralization\PluralizationServiceUtil.cs" />
    <Compile Include="Data\Entity\Infrastructure\ProviderInvariantName.cs" />
    <Compile Include="Data\Entity\Infrastructure\ReplacementDbQueryWrapper.cs" />
    <Compile Include="Data\Entity\Infrastructure\RetryLimitExceededException.cs" />
    <Compile Include="Data\Entity\Infrastructure\SqlCeConnectionFactory.cs" />
    <Compile Include="Data\Entity\Infrastructure\SqlConnectionFactory.cs" />
    <Compile Include="Data\Entity\Infrastructure\SuppressDbSetInitializationAttribute.cs" />
    <Compile Include="Data\Entity\Infrastructure\TableExistenceChecker.cs" />
    <Compile Include="Data\Entity\Infrastructure\TransactionContext.cs" />
    <Compile Include="Data\Entity\Infrastructure\TransactionContextInitializer.cs" />
    <Compile Include="Data\Entity\Infrastructure\TransactionHandler.cs" />
    <Compile Include="Data\Entity\Infrastructure\TransactionRow.cs" />
    <Compile Include="Data\Entity\Infrastructure\UnintentionalCodeFirstException.cs" />
    <Compile Include="Data\Entity\Internal\AppConfig.cs" />
    <Compile Include="Data\Entity\Internal\ClonedObjectContext.cs" />
    <Compile Include="Data\Entity\Internal\ClonedPropertyValues.cs" />
    <Compile Include="Data\Entity\Internal\ClonedPropertyValuesItem.cs" />
    <Compile Include="Data\Entity\Internal\CodeFirstCachedMetadataWorkspace.cs" />
    <Compile Include="Data\Entity\Internal\CommandTracer.cs" />
    <Compile Include="Data\Entity\Internal\ConfigFile\ContextCollection.cs" />
    <Compile Include="Data\Entity\Internal\ConfigFile\ContextElement.cs" />
    <Compile Include="Data\Entity\Internal\ConfigFile\DatabaseInitializerElement.cs" />
    <Compile Include="Data\Entity\Internal\ConfigFile\DefaultConnectionFactoryElement.cs" />
    <Compile Include="Data\Entity\Internal\ConfigFile\EntityFrameworkSection.cs" />
    <Compile Include="Data\Entity\Internal\ConfigFile\InterceptorElement.cs" />
    <Compile Include="Data\Entity\Internal\ConfigFile\InterceptorsCollection.cs" />
    <Compile Include="Data\Entity\Internal\ConfigFile\ParameterCollection.cs" />
    <Compile Include="Data\Entity\Internal\ConfigFile\ParameterElement.cs" />
    <Compile Include="Data\Entity\Internal\ConfigFile\ProviderCollection.cs" />
    <Compile Include="Data\Entity\Internal\ConfigFile\ProviderElement.cs" />
    <Compile Include="Data\Entity\Internal\ConfigFile\QueryCacheElement.cs" />
    <Compile Include="Data\Entity\Internal\ContextConfig.cs" />
    <Compile Include="Data\Entity\Internal\DatabaseCreator.cs" />
    <Compile Include="Data\Entity\Internal\DatabaseExistenceState.cs" />
    <Compile Include="Data\Entity\Internal\DatabaseOperations.cs" />
    <Compile Include="Data\Entity\Internal\DatabaseTableChecker.cs" />
    <Compile Include="Data\Entity\Internal\DbContextTypesInitializersPair.cs" />
    <Compile Include="Data\Entity\Internal\DbDataRecordPropertyValues.cs" />
    <Compile Include="Data\Entity\Internal\DbDataRecordPropertyValuesItem.cs" />
    <Compile Include="Data\Entity\Internal\DbHelpers.cs" />
    <Compile Include="Data\Entity\Internal\DbLocalView.cs" />
    <Compile Include="Data\Entity\Internal\DbSetDiscoveryService.cs" />
    <Compile Include="Data\Entity\Internal\DefaultModelCacheKey.cs" />
    <Compile Include="Data\Entity\Internal\DefaultModelCacheKeyFactory.cs" />
    <Compile Include="Data\Entity\Internal\EagerInternalConnection.cs" />
    <Compile Include="Data\Entity\Internal\EagerInternalContext.cs" />
    <Compile Include="Data\Entity\Internal\EdmMetadataContext.cs" />
    <Compile Include="Data\Entity\Internal\EdmMetadataRepository.cs" />
    <Compile Include="Data\Entity\Internal\EntitySetTypePair.cs" />
    <Compile Include="Data\Entity\Internal\ICachedMetadataWorkspace.cs" />
    <Compile Include="Data\Entity\Internal\IDbEnumerator.cs" />
    <Compile Include="Data\Entity\Internal\IEntityStateEntry.cs" />
    <Compile Include="Data\Entity\Internal\IInternalConnection.cs" />
    <Compile Include="Data\Entity\Internal\InitializerConfig.cs" />
    <Compile Include="Data\Entity\Internal\InitializerLockPair.cs" />
    <Compile Include="Data\Entity\Internal\InterceptableDbCommand.cs" />
    <Compile Include="Data\Entity\Internal\InternalCollectionEntry.cs" />
    <Compile Include="Data\Entity\Internal\InternalConnection.cs" />
    <Compile Include="Data\Entity\Internal\InternalContext.cs" />
    <Compile Include="Data\Entity\Internal\InternalEntityEntry.cs" />
    <Compile Include="Data\Entity\Internal\InternalEntityPropertyEntry.cs" />
    <Compile Include="Data\Entity\Internal\InternalMemberEntry.cs" />
    <Compile Include="Data\Entity\Internal\InternalNavigationEntry.cs" />
    <Compile Include="Data\Entity\Internal\InternalNestedPropertyEntry.cs" />
    <Compile Include="Data\Entity\Internal\InternalPropertyEntry.cs" />
    <Compile Include="Data\Entity\Internal\InternalPropertyValues.cs" />
    <Compile Include="Data\Entity\Internal\InternalReferenceEntry.cs" />
    <Compile Include="Data\Entity\Internal\InternalSqlNonSetQuery.cs" />
    <Compile Include="Data\Entity\Internal\InternalSqlQuery.cs" />
    <Compile Include="Data\Entity\Internal\InternalSqlSetQuery.cs" />
    <Compile Include="Data\Entity\Internal\IPropertyValuesItem.cs" />
    <Compile Include="Data\Entity\Internal\LazyAsyncEnumerator.cs" />
    <Compile Include="Data\Entity\Internal\LazyEnumerator.cs" />
    <Compile Include="Data\Entity\Internal\LazyInternalConnection.cs" />
    <Compile Include="Data\Entity\Internal\LazyInternalContext.cs" />
    <Compile Include="Data\Entity\Internal\Linq\DbQueryProvider.cs" />
    <Compile Include="Data\Entity\Internal\Linq\DbQueryVisitor.cs" />
    <Compile Include="Data\Entity\Internal\Linq\IInternalQuery.2.cs" />
    <Compile Include="Data\Entity\Internal\Linq\IInternalQuery.cs" />
    <Compile Include="Data\Entity\Internal\Linq\IInternalQueryAdapter.cs" />
    <Compile Include="Data\Entity\Internal\Linq\IInternalSet.2.cs" />
    <Compile Include="Data\Entity\Internal\Linq\IInternalSet.cs" />
    <Compile Include="Data\Entity\Internal\Linq\IInternalSetAdapter.cs" />
    <Compile Include="Data\Entity\Internal\Linq\InternalDbQuery.cs" />
    <Compile Include="Data\Entity\Internal\Linq\InternalDbSet.cs" />
    <Compile Include="Data\Entity\Internal\Linq\InternalQuery.cs" />
    <Compile Include="Data\Entity\Internal\Linq\InternalSet.cs" />
    <Compile Include="Data\Entity\Internal\Linq\NonGenericDbQueryProvider.cs" />
    <Compile Include="Data\Entity\Internal\MemberEntryMetadata.cs" />
    <Compile Include="Data\Entity\Internal\MemberEntryType.cs" />
    <Compile Include="Data\Entity\Internal\MockingProxies\EntityConnectionProxy.cs" />
    <Compile Include="Data\Entity\Internal\MockingProxies\ObjectContextProxy.cs" />
    <Compile Include="Data\Entity\Internal\ModelCompatibilityChecker.cs" />
    <Compile Include="Data\Entity\Internal\ModelHashCalculator.cs" />
    <Compile Include="Data\Entity\Internal\NavigationEntryMetadata.cs" />
    <Compile Include="Data\Entity\Internal\ObjectContextTypeCache.cs" />
    <Compile Include="Data\Entity\Internal\ObservableBackedBindingList.cs" />
    <Compile Include="Data\Entity\Internal\PropertyEntryMetadata.cs" />
    <Compile Include="Data\Entity\Internal\QueryCacheConfig.cs" />
    <Compile Include="Data\Entity\Internal\ReadOnlySet.cs" />
    <Compile Include="Data\Entity\Internal\RepositoryBase.cs" />
    <Compile Include="Data\Entity\Internal\RetryAction.cs" />
    <Compile Include="Data\Entity\Internal\RetryLazy.cs" />
    <Compile Include="Data\Entity\Internal\SortableBindingList.cs" />
    <Compile Include="Data\Entity\Internal\StateEntryAdapter.cs" />
    <Compile Include="Data\Entity\Internal\ThrowingMonitor.cs" />
    <Compile Include="Data\Entity\Internal\Validation\ComplexPropertyValidator.cs" />
    <Compile Include="Data\Entity\Internal\Validation\ComplexTypeValidator.cs" />
    <Compile Include="Data\Entity\Internal\Validation\EntityValidationContext.cs" />
    <Compile Include="Data\Entity\Internal\Validation\EntityValidator.cs" />
    <Compile Include="Data\Entity\Internal\Validation\EntityValidatorBuilder.cs" />
    <Compile Include="Data\Entity\Internal\Validation\IValidator.cs" />
    <Compile Include="Data\Entity\Internal\Validation\PropertyValidator.cs" />
    <Compile Include="Data\Entity\Internal\Validation\TypeValidator.cs" />
    <Compile Include="Data\Entity\Internal\Validation\ValidatableObjectValidator.cs" />
    <Compile Include="Data\Entity\Internal\Validation\ValidationAttributeValidator.cs" />
    <Compile Include="Data\Entity\Internal\Validation\ValidationProvider.cs" />
    <Compile Include="Data\Entity\Internal\WrappedEntityKey.cs" />
    <Compile Include="Data\Entity\MigrateDatabaseToLatestVersion.cs" />
    <Compile Include="Data\Entity\Migrations\Builders\ColumnBuilder.cs" />
    <Compile Include="Data\Entity\Migrations\Builders\ParameterBuilder.cs" />
    <Compile Include="Data\Entity\Migrations\Builders\TableBuilder.cs" />
    <Compile Include="Data\Entity\Migrations\DbMigration.cs" />
    <Compile Include="Data\Entity\Migrations\DbMigrationsConfiguration.2.cs" />
    <Compile Include="Data\Entity\Migrations\DbMigrationsConfiguration.cs" />
    <Compile Include="Data\Entity\Migrations\DbMigrator.cs" />
    <Compile Include="Data\Entity\Migrations\DbSetMigrationsExtensions.cs" />
    <Compile Include="Data\Entity\Migrations\Design\CSharpMigrationCodeGenerator.cs" />
    <Compile Include="Data\Entity\Migrations\Design\MigrationCodeGenerator.cs" />
    <Compile Include="Data\Entity\Migrations\Design\MigrationScaffolder.cs" />
    <Compile Include="Data\Entity\Migrations\Design\ScaffoldedMigration.cs" />
    <Compile Include="Data\Entity\Migrations\Design\ToolingException.cs" />
    <Compile Include="Data\Entity\Migrations\Design\ToolingFacade.cs" />
    <Compile Include="Data\Entity\Migrations\Design\VisualBasicMigrationCodeGenerator.cs" />
    <Compile Include="Data\Entity\Migrations\Edm\EdmXNames.cs" />
    <Compile Include="Data\Entity\Migrations\Edm\ModelCompressor.cs" />
    <Compile Include="Data\Entity\Migrations\History\HistoryContext.cs" />
    <Compile Include="Data\Entity\Migrations\History\HistoryRepository.cs" />
    <Compile Include="Data\Entity\Migrations\History\HistoryRow.cs" />
    <Compile Include="Data\Entity\Migrations\History\LegacyHistoryContext.cs" />
    <Compile Include="Data\Entity\Migrations\History\LegacyHistoryRow.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\AutomaticDataLossException.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\AutomaticMigrationsDisabledException.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\DynamicToFunctionModificationCommandConverter.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\EdmModelDiffer.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\IDbMigration.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\IMigrationMetadata.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\MigrationAssembly.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\MigrationsException.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\MigrationsLogger.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\MigrationsPendingException.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\MigratorBase.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\MigratorLoggingDecorator.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\MigratorScriptingDecorator.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\ModificationCommandTreeGenerator.cs" />
    <Compile Include="Data\Entity\Migrations\Infrastructure\VersionedModel.cs" />
    <Compile Include="Data\Entity\Migrations\Model\AddColumnOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\AddForeignKeyOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\AddPrimaryKeyOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\AlterColumnOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\AlterProcedureOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\AlterTableOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\ColumnModel.cs" />
    <Compile Include="Data\Entity\Migrations\Model\CreateIndexOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\CreateProcedureOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\CreateTableOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\DropColumnOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\DropForeignKeyOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\DropIndexOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\DropPrimaryKeyOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\DropProcedureOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\DropTableOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\ForeignKeyOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\HistoryOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\IAnnotationTarget.cs" />
    <Compile Include="Data\Entity\Migrations\Model\IndexOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\MigrationOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\MoveProcedureOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\MoveTableOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\NotSupportedOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\ParameterModel.cs" />
    <Compile Include="Data\Entity\Migrations\Model\PrimaryKeyOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\ProcedureOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\PropertyModel.cs" />
    <Compile Include="Data\Entity\Migrations\Model\RenameColumnOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\RenameIndexOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\RenameProcedureOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\RenameTableOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\SqlOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Model\UpdateDatabaseOperation.cs" />
    <Compile Include="Data\Entity\Migrations\Sql\MigrationSqlGenerator.cs" />
    <Compile Include="Data\Entity\Migrations\Sql\MigrationStatement.cs" />
    <Compile Include="Data\Entity\Migrations\Utilities\ConfigurationFileUpdater.cs" />
    <Compile Include="Data\Entity\Migrations\Utilities\DatabaseCreator.cs" />
    <Compile Include="Data\Entity\Migrations\Utilities\EmptyContext.cs" />
    <Compile Include="Data\Entity\Migrations\Utilities\IndentedTextWriter.cs" />
    <Compile Include="Data\Entity\Migrations\Utilities\MigrationsConfigurationFinder.cs" />
    <Compile Include="Data\Entity\Migrations\Utilities\UtcNowGenerator.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\ComplexTypeConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\AssociationMappingConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\AssociationModificationStoredProcedureConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\BinaryPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\CascadableNavigationPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConfigurationBase.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConfigurationRegistrar.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConfigurationTypeActivator.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConfigurationTypeFilter.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConfigurationTypesFinder.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConventionDeleteModificationStoredProcedureConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConventionInsertModificationStoredProcedureConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConventionModificationStoredProcedureConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConventionModificationStoredProceduresConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConventionPrimitivePropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConventionsConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConventionsTypeActivator.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConventionsTypeFilter.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConventionsTypeFinder.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConventionTypeConfiguration.2.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConventionTypeConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ConventionUpdateModificationStoredProcedureConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\DateTimePropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\DecimalPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\DeleteModificationStoredProcedureConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\DependentNavigationPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\EntityMappingConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ForeignKeyAssociationMappingConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ForeignKeyNavigationPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\IndexConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\InsertModificationStoredProcedureConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\LengthColumnConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\LengthPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ManyNavigationPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ManyToManyAssociationMappingConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ManyToManyModificationStoredProcedureConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ManyToManyModificationStoredProceduresConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ManyToManyNavigationPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Mapping\AssociationMappingOperations.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Mapping\ColumnMapping.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Mapping\DatabaseOperations.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Mapping\EntityMappingConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Mapping\EntityMappingOperations.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Mapping\EntityMappingService.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Mapping\ForeignKeyPrimitiveOperations.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Mapping\PropertyMappingSpecification.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Mapping\SortedEntityTypeIndex.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Mapping\TableMapping.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Mapping\TableOperations.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Mapping\TablePrimitiveOperations.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ModelConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ModificationStoredProcedureConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ModificationStoredProcedureConfigurationBase.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ModificationStoredProceduresConfiguration.2.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ModificationStoredProceduresConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\NotNullConditionConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\OptionalNavigationPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\PrimaryKeyIndexConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\PrimitiveColumnConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\PrimitivePropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\Index\IndexConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\Navigation\ConstraintConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\Navigation\ConventionNavigationPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\Navigation\ForeignKeyConstraintConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\Navigation\IndependentConstraintConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\Navigation\NavigationPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\Primitive\BinaryPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\Primitive\DateTimePropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\Primitive\DecimalPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\Primitive\LengthPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\Primitive\OverridableConfigurationParts.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\Primitive\PrimitivePropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\Primitive\StringPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Properties\PropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\PropertyConventionConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\PropertyConventionWithHavingConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\PropertyMappingConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\RequiredNavigationPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\StringColumnConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\StringPropertyConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\StructuralTypeConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\TphColumnFixer.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\TypeConventionConfiguration.2.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\TypeConventionConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\TypeConventionWithHavingConfiguration.2.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\TypeConventionWithHavingConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Types\ComplexTypeConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Types\EntityTypeConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\Types\StructuralTypeConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\UpdateModificationStoredProcedureConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Configuration\ValueConditionConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\AssociationInverseDiscoveryConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\AttributeToColumnAnnotationConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\AttributeToTableAnnotationConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\ColumnAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\ColumnOrderingConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\ColumnOrderingConventionStrict.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\ComplexTypeAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\ComplexTypeDiscoveryConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\ConcurrencyCheckAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\Convention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\DatabaseGeneratedAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\DecimalPropertyConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\DeclaredPropertyOrderingConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\ForeignKeyAssociationMultiplicityConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\ForeignKeyDiscoveryConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\ForeignKeyIndexConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\ForeignKeyNavigationPropertyAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\ForeignKeyPrimitivePropertyAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\IConceptualModelConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\IConfigurationConvention.2.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\IConfigurationConvention.3.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\IConfigurationConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\IConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\IDbMappingConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\IdKeyDiscoveryConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\IndexAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\InversePropertyAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\IStoreModelConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\KeyAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\KeyDiscoveryConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\ManyToManyCascadeDeleteConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\MappingInheritedPropertiesSupportConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\MaxLengthAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\NavigationPropertyNameForeignKeyDiscoveryConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\NotMappedPropertyAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\NotMappedTypeAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\OneToManyCascadeDeleteConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\OneToOneConstraintIntroductionConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\PluralizingEntitySetNameConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\PluralizingTableNameConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\PrimaryKeyNameForeignKeyDiscoveryConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\PrimitivePropertyAttributeConfigurationConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\PropertyAttributeConfigurationConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\PropertyConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\PropertyConventionBase.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\PropertyConventionWithHaving.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\PropertyMaxLengthConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\RequiredNavigationPropertyAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\RequiredPrimitivePropertyAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\Sets\ConventionSet.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\Sets\V1ConventionSet.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\Sets\V2ConventionSet.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\SqlCePropertyMaxLengthConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\StoreGeneratedIdentityKeyConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\StringLengthAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\TableAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\TimestampAttributeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\TypeAttributeConfigurationConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\TypeConvention.2.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\TypeConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\TypeConventionBase.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\TypeConventionWithHaving.2.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\TypeConventionWithHaving.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\TypeConventionWithHavingBase.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Conventions\TypeNameForeignKeyDiscoveryConvention.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Design\PluralizationServices\StringBidirectionalDictionary.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\AssociationTypeExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\ColumnMappingBuilderExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\ComplexTypeExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\DataModelErrorEventArgsExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\DbDatabaseMappingExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\EdmMemberExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\EdmModelExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\EdmPropertyExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\EdmTypeExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\EntitySetExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\EntityTypeExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\EnumTypeExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\ForeignKeyBuilderExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\FunctionParameterExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\INamedDataModelItemExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\MetadataPropertyExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\NavigationPropertyExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\RelationshipEndMemberExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\RelationshipMultiplicityExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\Serialization\EdmxSerializer.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\Services\AssociationTypeMappingGenerator.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\Services\DatabaseMappingGenerator.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\Services\FunctionParameterMappingGenerator.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\Services\ModificationFunctionMappingGenerator.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\Services\PropertyMappingGenerator.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\Services\StructuralTypeMappingGenerator.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\Services\TableMappingGenerator.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\StorageAssociationSetMappingExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\StorageEntityTypeMappingExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Edm\StorageMappingFragmentExtensions.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\EntityTypeConfiguration.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Mappers\AttributeMapper.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Mappers\MappingContext.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Mappers\NavigationPropertyMapper.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Mappers\PropertyFilter.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Mappers\PropertyMapper.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Mappers\TypeMapper.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\ModelValidationException.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Utilities\AttributeProvider.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Utilities\EdmPropertyPath.cs" />
    <Compile Include="Data\Entity\ModelConfiguration\Utilities\PropertyPath.cs" />
    <Compile Include="Data\Entity\NullDatabaseInitializer.cs" />
    <Compile Include="Data\Entity\ObservableCollectionExtensions.cs" />
    <Compile Include="Data\Entity\QueryableExtensions.cs" />
    <Compile Include="Data\Entity\Resources\EntityRes.cs" />
    <Compile Include="Data\Entity\Resources\Error.cs" />
    <Compile Include="Data\Entity\Resources\Strings.cs" />
    <Compile Include="Data\Entity\Spatial\DbGeography.cs" />
    <Compile Include="Data\Entity\Spatial\DbGeographyWellKnownValue.cs" />
    <Compile Include="Data\Entity\Spatial\DbGeometry.cs" />
    <Compile Include="Data\Entity\Spatial\DbGeometryWellKnownValue.cs" />
    <Compile Include="Data\Entity\Spatial\DbSpatialDataReader.cs" />
    <Compile Include="Data\Entity\Spatial\DbSpatialServices.cs" />
    <Compile Include="Data\Entity\Spatial\DefaultSpatialServices.cs" />
    <Compile Include="Data\Entity\Spatial\SpatialHelpers.cs" />
    <Compile Include="Data\Entity\Spatial\SpatialServicesLoader.cs" />
    <Compile Include="Data\Entity\TransactionalBehavior.cs" />
    <Compile Include="Data\Entity\Utilities\AssemblyExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\BoolExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\ByteExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\Check.cs" />
    <Compile Include="Data\Entity\Utilities\DatabaseName.cs" />
    <Compile Include="Data\Entity\Utilities\DbConnectionExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\DbContextExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\DbModelBuilderVersionExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\DbModelExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\DbProviderFactoryExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\DbProviderInfoExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\DbProviderManifestExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\DbProviderServicesExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\DebugCheck.cs" />
    <Compile Include="Data\Entity\Utilities\DynamicEqualityComparer.cs" />
    <Compile Include="Data\Entity\Utilities\DynamicEqualityComparerLinqIntegration.cs" />
    <Compile Include="Data\Entity\Utilities\ExceptionExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\ExpressionExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\HashSetExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\IEnumerableExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\MemberInfoExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\PropertyInfoExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\ProviderRowFinder.cs" />
    <Compile Include="Data\Entity\Utilities\StringExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\TaskExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\TaskHelper.cs" />
    <Compile Include="Data\Entity\Utilities\TypeExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\TypeFinder.cs" />
    <Compile Include="Data\Entity\Utilities\ValidationContextExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\XContainerExtensions.cs" />
    <Compile Include="Data\Entity\Utilities\XDocumentExtensions.cs" />
    <Compile Include="Data\Entity\Validation\DbEntityValidationException.cs" />
    <Compile Include="Data\Entity\Validation\DbEntityValidationResult.cs" />
    <Compile Include="Data\Entity\Validation\DbUnexpectedValidationException.cs" />
    <Compile Include="Data\Entity\Validation\DbValidationError.cs" />
    <Compile Include="Linq\Expressions\EntityExpressionVisitor.cs" />
    <Compile Include="Linq\Expressions\Internal\Error.cs" />
    <Compile Include="Linq\Expressions\Internal\ReadOnlyCollectionExtensions.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Data\Entity\Properties\Resources.resources" />
    <EmbeddedResource Include="System.Data.Resources.AnnotationSchema.xsd" />
    <EmbeddedResource Include="System.Data.Resources.CodeGenerationSchema.xsd" />
    <EmbeddedResource Include="System.Data.Resources.CSDLSchema_1.xsd" />
    <EmbeddedResource Include="System.Data.Resources.CSDLSchema_1_1.xsd" />
    <EmbeddedResource Include="System.Data.Resources.CSDLSchema_2.xsd" />
    <EmbeddedResource Include="System.Data.Resources.CSDLSchema_3.xsd" />
    <EmbeddedResource Include="System.Data.Resources.CSMSL_1.xsd" />
    <EmbeddedResource Include="System.Data.Resources.CSMSL_2.xsd" />
    <EmbeddedResource Include="System.Data.Resources.CSMSL_3.xsd" />
    <EmbeddedResource Include="System.Data.Resources.DbProviderServices.ConceptualSchemaDef.2.csdl" />
    <EmbeddedResource Include="System.Data.Resources.DbProviderServices.ConceptualSchemaDef.csdl" />
    <EmbeddedResource Include="System.Data.Resources.EntityStoreSchemaGenerator.xsd" />
    <EmbeddedResource Include="System.Data.Resources.ProviderServices.ProviderManifest.xsd" />
    <EmbeddedResource Include="System.Data.Resources.SSDLSchema.xsd" />
    <EmbeddedResource Include="System.Data.Resources.SSDLSchema_2.xsd" />
    <EmbeddedResource Include="System.Data.Resources.SSDLSchema_3.xsd" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\mscorlib\mscorlib.csproj">
      <Project>{4896D65B-2F8C-497C-A898-6C19AB0B2FB7}</Project>
      <Name>mscorlib</Name>
    </ProjectReference>
    <ProjectReference Include="..\System.Configuration\System.Configuration.csproj">
      <Project>{4896D65B-2F8C-497C-A898-6C19AB0B2FB9}</Project>
      <Name>System.Configuration</Name>
    </ProjectReference>
    <ProjectReference Include="..\System\System.csproj">
      <Project>{4896D65B-2F8C-497C-A898-6C19AB0B2FBB}</Project>
      <Name>System</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>