﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using Bussiness;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000CA8 RID: 3240
	public class PetMgr
	{
		// Token: 0x0600740B RID: 29707 RVA: 0x00264934 File Offset: 0x00262B34
		public static bool Init()
		{
			bool flag;
			try
			{
				PetMgr.rand = new Random();
				flag = PetMgr.ReLoad();
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = PetMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					PetMgr.log.Error("PetMgr", ex);
				}
				flag = false;
			}
			return flag;
		}

		// Token: 0x0600740C RID: 29708 RVA: 0x00264990 File Offset: 0x00262B90
		public static bool ReLoad()
		{
			try
			{
				Dictionary<string, PetConfig> dictionary = new Dictionary<string, PetConfig>();
				Dictionary<int, PetLevel> dictionary2 = new Dictionary<int, PetLevel>();
				Dictionary<int, PetSkillElementInfo> dictionary3 = new Dictionary<int, PetSkillElementInfo>();
				Dictionary<int, PetSkillInfo> dictionary4 = new Dictionary<int, PetSkillInfo>();
				Dictionary<int, PetSkillTemplateInfo> dictionary5 = new Dictionary<int, PetSkillTemplateInfo>();
				Dictionary<int, PetTemplateInfo> dictionary6 = new Dictionary<int, PetTemplateInfo>();
				Dictionary<int, PetTemplateInfo> dictionary7 = new Dictionary<int, PetTemplateInfo>();
				Dictionary<int, PetExpItemPriceInfo> dictionary8 = new Dictionary<int, PetExpItemPriceInfo>();
				bool flag = PetMgr.LoadPetFromDb(dictionary, dictionary2, dictionary3, dictionary4, dictionary5, dictionary7, dictionary8);
				if (flag)
				{
					try
					{
						PetMgr.m_configs = dictionary;
						PetMgr.m_levels = dictionary2;
						PetMgr.m_skillElements = dictionary3;
						PetMgr.m_skills = dictionary4;
						PetMgr.m_skillTemplates = dictionary5;
						PetMgr.m_templateIds = dictionary7;
						PetMgr.m_expItemPrices = dictionary8;
						List<GameNeedPetSkillInfo> list = PetMgr.LoadGameNeedPetSkill();
						bool flag2 = list.Count > 0;
						if (flag2)
						{
							Interlocked.Exchange<List<GameNeedPetSkillInfo>>(ref PetMgr.m_gameNeedPetSkillInfos, list);
						}
						return true;
					}
					catch
					{
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = PetMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					PetMgr.log.Error("PetMgr", ex);
				}
			}
			return false;
		}

		// Token: 0x0600740D RID: 29709 RVA: 0x00264A98 File Offset: 0x00262C98
		public static GameNeedPetSkillInfo[] GetGameNeedPetSkill()
		{
			return PetMgr.m_gameNeedPetSkillInfos.ToArray();
		}

		// Token: 0x0600740E RID: 29710 RVA: 0x00264AB4 File Offset: 0x00262CB4
		public static List<GameNeedPetSkillInfo> LoadGameNeedPetSkill()
		{
			List<GameNeedPetSkillInfo> list = new List<GameNeedPetSkillInfo>();
			List<string> list2 = new List<string>();
			foreach (PetSkillInfo petSkillInfo in PetMgr.m_skills.Values)
			{
				string effectPic = petSkillInfo.EffectPic;
				bool flag = !string.IsNullOrEmpty(effectPic) && !list2.Contains(effectPic);
				if (flag)
				{
					list.Add(new GameNeedPetSkillInfo
					{
						Pic = petSkillInfo.Pic,
						EffectPic = petSkillInfo.EffectPic
					});
					list2.Add(effectPic);
				}
			}
			foreach (PetSkillElementInfo petSkillElementInfo in PetMgr.m_skillElements.Values)
			{
				string effectPic2 = petSkillElementInfo.EffectPic;
				bool flag2 = !string.IsNullOrEmpty(effectPic2) && !list2.Contains(effectPic2);
				if (flag2)
				{
					list.Add(new GameNeedPetSkillInfo
					{
						Pic = petSkillElementInfo.Pic,
						EffectPic = petSkillElementInfo.EffectPic
					});
					list2.Add(effectPic2);
				}
			}
			return list;
		}

		// Token: 0x0600740F RID: 29711 RVA: 0x00264C1C File Offset: 0x00262E1C
		private static bool LoadPetFromDb(Dictionary<string, PetConfig> Config, Dictionary<int, PetLevel> Level, Dictionary<int, PetSkillElementInfo> SkillElement, Dictionary<int, PetSkillInfo> Skill, Dictionary<int, PetSkillTemplateInfo> SkillTemplate, Dictionary<int, PetTemplateInfo> TemplateId, Dictionary<int, PetExpItemPriceInfo> PetExpItemPrice)
		{
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				PetConfig[] allPetConfig = produceBussiness.GetAllPetConfig();
				PetLevel[] allPetLevel = produceBussiness.GetAllPetLevel();
				PetSkillElementInfo[] allPetSkillElementInfo = produceBussiness.GetAllPetSkillElementInfo();
				PetSkillInfo[] allPetSkillInfo = produceBussiness.GetAllPetSkillInfo();
				PetSkillTemplateInfo[] allPetSkillTemplateInfo = produceBussiness.GetAllPetSkillTemplateInfo();
				PetTemplateInfo[] allPetTemplateInfo = produceBussiness.GetAllPetTemplateInfo();
				PetExpItemPriceInfo[] allPetExpItemPrice = produceBussiness.GetAllPetExpItemPrice();
				PetConfig[] array = allPetConfig;
				PetConfig[] array2 = array;
				PetConfig[] array3 = array2;
				foreach (PetConfig petConfig in array3)
				{
					bool flag = !Config.ContainsKey(petConfig.Name);
					if (flag)
					{
						Config.Add(petConfig.Name, petConfig);
					}
				}
				PetLevel[] array5 = allPetLevel;
				PetLevel[] array6 = array5;
				PetLevel[] array7 = array6;
				foreach (PetLevel petLevel in array7)
				{
					bool flag2 = !Level.ContainsKey(petLevel.Level);
					if (flag2)
					{
						Level.Add(petLevel.Level, petLevel);
					}
				}
				PetSkillElementInfo[] array9 = allPetSkillElementInfo;
				PetSkillElementInfo[] array10 = array9;
				PetSkillElementInfo[] array11 = array10;
				foreach (PetSkillElementInfo petSkillElementInfo in array11)
				{
					bool flag3 = !SkillElement.ContainsKey(petSkillElementInfo.ID);
					if (flag3)
					{
						SkillElement.Add(petSkillElementInfo.ID, petSkillElementInfo);
					}
				}
				PetSkillTemplateInfo[] array13 = allPetSkillTemplateInfo;
				PetSkillTemplateInfo[] array14 = array13;
				PetSkillTemplateInfo[] array15 = array14;
				foreach (PetSkillTemplateInfo petSkillTemplateInfo in array15)
				{
					bool flag4 = !SkillTemplate.ContainsKey(petSkillTemplateInfo.SkillID);
					if (flag4)
					{
						SkillTemplate.Add(petSkillTemplateInfo.SkillID, petSkillTemplateInfo);
					}
				}
				PetTemplateInfo[] array17 = allPetTemplateInfo;
				PetTemplateInfo[] array18 = array17;
				PetTemplateInfo[] array19 = array18;
				foreach (PetTemplateInfo petTemplateInfo in array19)
				{
					bool flag5 = !TemplateId.ContainsKey(petTemplateInfo.TemplateID);
					if (flag5)
					{
						TemplateId.Add(petTemplateInfo.TemplateID, petTemplateInfo);
					}
				}
				PetSkillInfo[] array21 = allPetSkillInfo;
				PetSkillInfo[] array22 = array21;
				PetSkillInfo[] array23 = array22;
				foreach (PetSkillInfo petSkillInfo in array23)
				{
					bool flag6 = !Skill.ContainsKey(petSkillInfo.ID);
					if (flag6)
					{
						Skill.Add(petSkillInfo.ID, petSkillInfo);
					}
				}
				PetExpItemPriceInfo[] array25 = allPetExpItemPrice;
				PetExpItemPriceInfo[] array26 = array25;
				PetExpItemPriceInfo[] array27 = array26;
				foreach (PetExpItemPriceInfo petExpItemPriceInfo in array27)
				{
					bool flag7 = !PetExpItemPrice.ContainsKey(petExpItemPriceInfo.Count);
					if (flag7)
					{
						PetExpItemPrice.Add(petExpItemPriceInfo.Count, petExpItemPriceInfo);
					}
				}
			}
			return true;
		}

		// Token: 0x06007410 RID: 29712 RVA: 0x00264EF0 File Offset: 0x002630F0
		public static PetConfig FindConfig(string key)
		{
			bool flag = PetMgr.m_configs == null;
			if (flag)
			{
				PetMgr.Init();
			}
			bool flag2 = PetMgr.m_configs.ContainsKey(key);
			PetConfig petConfig;
			if (flag2)
			{
				petConfig = PetMgr.m_configs[key];
			}
			else
			{
				petConfig = null;
			}
			return petConfig;
		}

		// Token: 0x06007411 RID: 29713 RVA: 0x00264F38 File Offset: 0x00263138
		public static PetLevel FindPetLevel(int level)
		{
			bool flag = PetMgr.m_levels == null;
			if (flag)
			{
				PetMgr.Init();
			}
			bool flag2 = PetMgr.m_levels.ContainsKey(level);
			PetLevel petLevel;
			if (flag2)
			{
				petLevel = PetMgr.m_levels[level];
			}
			else
			{
				petLevel = null;
			}
			return petLevel;
		}

		// Token: 0x06007412 RID: 29714 RVA: 0x00264F80 File Offset: 0x00263180
		public static PetSkillElementInfo FindPetSkillElement(int SkillID)
		{
			bool flag = PetMgr.m_skillElements == null;
			if (flag)
			{
				PetMgr.Init();
			}
			bool flag2 = PetMgr.m_skillElements.ContainsKey(SkillID);
			PetSkillElementInfo petSkillElementInfo;
			if (flag2)
			{
				petSkillElementInfo = PetMgr.m_skillElements[SkillID];
			}
			else
			{
				petSkillElementInfo = null;
			}
			return petSkillElementInfo;
		}

		// Token: 0x06007413 RID: 29715 RVA: 0x00264FC8 File Offset: 0x002631C8
		public static PetSkillInfo FindPetSkill(int SkillID)
		{
			bool flag = PetMgr.m_skills == null;
			if (flag)
			{
				PetMgr.Init();
			}
			bool flag2 = PetMgr.m_skills.ContainsKey(SkillID);
			PetSkillInfo petSkillInfo;
			if (flag2)
			{
				petSkillInfo = PetMgr.m_skills[SkillID];
			}
			else
			{
				petSkillInfo = null;
			}
			return petSkillInfo;
		}

		// Token: 0x06007414 RID: 29716 RVA: 0x00265010 File Offset: 0x00263210
		public static PetSkillInfo[] GetPetSkills()
		{
			List<PetSkillInfo> list = new List<PetSkillInfo>();
			bool flag = PetMgr.m_skills == null;
			if (flag)
			{
				PetMgr.Init();
			}
			foreach (PetSkillInfo petSkillInfo in PetMgr.m_skills.Values)
			{
				list.Add(petSkillInfo);
			}
			return list.ToArray();
		}

		// Token: 0x06007415 RID: 29717 RVA: 0x00265094 File Offset: 0x00263294
		public static PetSkillTemplateInfo GetPetSkillTemplate(int ID)
		{
			bool flag = PetMgr.m_skillTemplates == null;
			if (flag)
			{
				PetMgr.Init();
			}
			bool flag2 = PetMgr.m_skillTemplates.ContainsKey(ID);
			PetSkillTemplateInfo petSkillTemplateInfo;
			if (flag2)
			{
				petSkillTemplateInfo = PetMgr.m_skillTemplates[ID];
			}
			else
			{
				petSkillTemplateInfo = null;
			}
			return petSkillTemplateInfo;
		}

		// Token: 0x06007416 RID: 29718 RVA: 0x002650DC File Offset: 0x002632DC
		public static PetTemplateInfo FindPetTemplate(int TemplateID)
		{
			PetMgr.m_clientLocker.AcquireWriterLock(-1);
			try
			{
				bool flag = PetMgr.m_templateIds.ContainsKey(TemplateID);
				if (flag)
				{
					return PetMgr.m_templateIds[TemplateID];
				}
			}
			finally
			{
				PetMgr.m_clientLocker.ReleaseWriterLock();
			}
			return null;
		}

		// Token: 0x06007417 RID: 29719 RVA: 0x0026513C File Offset: 0x0026333C
		public static PetTemplateInfo FindPetTemplateByKind(int star, int kindId)
		{
			foreach (PetTemplateInfo petTemplateInfo in PetMgr.m_templateIds.Values)
			{
				bool flag = petTemplateInfo.KindID == kindId && star == petTemplateInfo.StarLevel;
				if (flag)
				{
					return petTemplateInfo;
				}
			}
			return null;
		}

		// Token: 0x06007418 RID: 29720 RVA: 0x002651B4 File Offset: 0x002633B4
		public static PetSkillTemplateInfo[] GetPetSkillByKindID(int KindID)
		{
			List<PetSkillTemplateInfo> list = new List<PetSkillTemplateInfo>();
			foreach (PetSkillTemplateInfo petSkillTemplateInfo in PetMgr.m_skillTemplates.Values)
			{
				bool flag = petSkillTemplateInfo.KindID == KindID;
				if (flag)
				{
					list.Add(petSkillTemplateInfo);
				}
			}
			return list.ToArray();
		}

		// Token: 0x06007419 RID: 29721 RVA: 0x00265234 File Offset: 0x00263434
		public static List<int> GetPetSkillByKindID(int KindID, int lv, int playerLevel)
		{
			List<int> list = new List<int>();
			List<string> list2 = new List<string>();
			int num = ((lv > playerLevel) ? playerLevel : lv);
			PetSkillTemplateInfo[] petSkillByKindID = PetMgr.GetPetSkillByKindID(KindID, num);
			for (int i = 1; i <= num; i++)
			{
				PetSkillTemplateInfo[] array = petSkillByKindID;
				PetSkillTemplateInfo[] array2 = array;
				PetSkillTemplateInfo[] array3 = array2;
				foreach (PetSkillTemplateInfo petSkillTemplateInfo in array3)
				{
					bool flag = petSkillTemplateInfo.MinLevel == i;
					if (flag)
					{
						string[] array5 = petSkillTemplateInfo.DeleteSkillIDs.Split(new char[] { ',' });
						string[] array6 = array5;
						string[] array7 = array6;
						string[] array8 = array7;
						foreach (string text in array8)
						{
							list2.Add(text);
						}
						list.Add(petSkillTemplateInfo.SkillID);
					}
				}
			}
			foreach (string text2 in list2)
			{
				bool flag2 = !string.IsNullOrEmpty(text2);
				if (flag2)
				{
					int num2 = int.Parse(text2);
					list.Remove(num2);
				}
			}
			list.Sort();
			return list;
		}

		// Token: 0x0600741A RID: 29722 RVA: 0x00265394 File Offset: 0x00263594
		public static PetSkillTemplateInfo[] GetPetSkillByKindID(int kindID, int level)
		{
			List<PetSkillTemplateInfo> list = new List<PetSkillTemplateInfo>();
			foreach (PetSkillTemplateInfo petSkillTemplateInfo in PetMgr.m_skillTemplates.Values)
			{
				bool flag = petSkillTemplateInfo.KindID == kindID && petSkillTemplateInfo.MinLevel <= level;
				if (flag)
				{
					list.Add(petSkillTemplateInfo);
				}
			}
			return list.ToArray();
		}

		// Token: 0x0600741B RID: 29723 RVA: 0x00265424 File Offset: 0x00263624
		public static List<UserPetInfo> CreateAdoptList(int userID, int playerLevel, int vipLv)
		{
			int num = Convert.ToInt32(PetMgr.FindConfig("AdoptCount").Value);
			List<UserPetInfo> list = new List<UserPetInfo>();
			List<PetTemplateInfo> list2 = null;
			int i = 0;
			while (i < num)
			{
				bool flag = DropInventory.GetPetDrop(613, 1, ref list2) && list2 != null;
				if (flag)
				{
					int num2 = PetMgr.rand.Next(list2.Count);
					UserPetInfo userPetInfo = PetMgr.CreatePet(list2[num2], userID, i, playerLevel, vipLv);
					userPetInfo.IsExit = true;
					list.Add(userPetInfo);
					i++;
				}
			}
			return list;
		}

		// Token: 0x0600741C RID: 29724 RVA: 0x002654C0 File Offset: 0x002636C0
		public static List<UserPetInfo> CreateFirstAdoptList(int userID, int playerLevel, int vipLv)
		{
			List<int> list = new List<int> { 100301, 110301, 120301, 130301 };
			List<UserPetInfo> list2 = new List<UserPetInfo>();
			for (int i = 0; i < list.Count; i++)
			{
				PetTemplateInfo petTemplateInfo = PetMgr.FindPetTemplate(list[i]);
				UserPetInfo userPetInfo = PetMgr.CreatePet(petTemplateInfo, userID, i, playerLevel, vipLv);
				userPetInfo.IsExit = true;
				list2.Add(userPetInfo);
			}
			return list2;
		}

		// Token: 0x0600741D RID: 29725 RVA: 0x00265558 File Offset: 0x00263758
		public static string ActiveEquipSkill(int Level, int vipLevel)
		{
			string[] array = new string[] { "0,0", "-1,1", "-1,2", "-1,3", "-1,4" };
			bool flag = Level >= 20 && Level < 30;
			if (flag)
			{
				array[1] = "0,1";
			}
			bool flag2 = Level >= 30 && Level < 50;
			if (flag2)
			{
				array[1] = "0,1";
				array[2] = "0,2";
			}
			bool flag3 = Level >= 50;
			if (flag3)
			{
				array[1] = "0,1";
				array[2] = "0,2";
				array[3] = "0,3";
			}
			bool flag4 = vipLevel >= 4;
			if (flag4)
			{
				array[4] = "0,4";
			}
			string text = array[0];
			for (int i = 1; i < array.Length; i++)
			{
				text = text + "|" + array[i];
			}
			return text;
		}

		// Token: 0x0600741E RID: 29726 RVA: 0x00265648 File Offset: 0x00263848
		public static int UpdateEvolution(int TemplateID, int lv, ref int evoluTime)
		{
			string text = TemplateID.ToString();
			int num = Convert.ToInt32(PetMgr.FindConfig("EvolutionLevel1").Value);
			int num2 = Convert.ToInt32(PetMgr.FindConfig("EvolutionLevel2").Value);
			evoluTime = 1;
			bool flag = !(text.Substring(text.Length - 1, 1) == "1");
			int num3;
			if (flag)
			{
				num3 = ((!(text.Substring(text.Length - 1, 1) == "2")) ? TemplateID : (TemplateID + 1));
			}
			else
			{
				bool flag2 = lv < num;
				if (flag2)
				{
					num3 = TemplateID;
				}
				else
				{
					bool flag3 = lv < num2;
					if (flag3)
					{
						num3 = TemplateID + 1;
					}
					else
					{
						num3 = TemplateID + 2;
						evoluTime = 2;
					}
				}
			}
			PetTemplateInfo petTemplateInfo = PetMgr.FindPetTemplate(num3);
			return (petTemplateInfo != null) ? petTemplateInfo.TemplateID : TemplateID;
		}

		// Token: 0x0600741F RID: 29727 RVA: 0x00265718 File Offset: 0x00263918
		public static string UpdateSkillPet(int Level, int TemplateID, int playerLevel)
		{
			string text = string.Empty;
			PetTemplateInfo petTemplateInfo = PetMgr.FindPetTemplate(TemplateID);
			bool flag = petTemplateInfo == null;
			string text2;
			if (flag)
			{
				PetMgr.log.Error("Pet not found: " + TemplateID.ToString());
				text2 = text;
			}
			else
			{
				List<int> petSkillByKindID = PetMgr.GetPetSkillByKindID(petTemplateInfo.KindID, Level, playerLevel);
				bool flag2 = petSkillByKindID.Count > 0;
				if (flag2)
				{
					text = petSkillByKindID[0].ToString() + ",0";
					for (int i = 1; i < petSkillByKindID.Count; i++)
					{
						text = string.Concat(new string[]
						{
							text,
							"|",
							petSkillByKindID[i].ToString(),
							",",
							i.ToString()
						});
					}
				}
				else
				{
					PetMgr.log.Error("Pet skill not found! info.KindID: " + petTemplateInfo.KindID.ToString());
				}
				text2 = text;
			}
			return text2;
		}

		// Token: 0x06007420 RID: 29728 RVA: 0x00265824 File Offset: 0x00263A24
		public static int GetLevel(int GP, int playerLevel)
		{
			bool flag = GP >= PetMgr.FindPetLevel(playerLevel).GP;
			int num;
			if (flag)
			{
				num = playerLevel;
			}
			else
			{
				for (int i = 1; i <= playerLevel; i++)
				{
					bool flag2 = GP < PetMgr.FindPetLevel(i).GP;
					if (flag2)
					{
						return (i - 1 == 0) ? 1 : (i - 1);
					}
				}
				num = 1;
			}
			return num;
		}

		// Token: 0x06007421 RID: 29729 RVA: 0x0026588C File Offset: 0x00263A8C
		public static int GetGP(int level, int playerLevel)
		{
			for (int i = 1; i <= playerLevel; i++)
			{
				bool flag = level == PetMgr.FindPetLevel(i).Level;
				if (flag)
				{
					return PetMgr.FindPetLevel(i).GP;
				}
			}
			return 0;
		}

		// Token: 0x06007422 RID: 29730 RVA: 0x002658D8 File Offset: 0x00263AD8
		public static void purificar(UserPetInfo petInfo, bool bld, bool att, bool def, bool agi, bool luck)
		{
			PetTemplateInfo petTemplateInfo = PetMgr.FindPetTemplate(petInfo.TemplateID);
			double num = (double)petTemplateInfo.StarLevel * 0.1;
			Random random = new Random();
			bool flag = !bld;
			if (flag)
			{
				petInfo.BloodGrow = random.Next(petTemplateInfo.HighBloodGrow * (random.Next(1, random.Next(2, 100)) / 100), petTemplateInfo.HighBloodGrow);
			}
			bool flag2 = !att;
			if (flag2)
			{
				petInfo.AttackGrow = random.Next(petTemplateInfo.HighAttackGrow * (random.Next(1, random.Next(2, 100)) / 100), petTemplateInfo.HighAttackGrow);
			}
			bool flag3 = !def;
			if (flag3)
			{
				petInfo.DefenceGrow = random.Next(petTemplateInfo.HighDefenceGrow * (random.Next(1, random.Next(2, 100)) / 100), petTemplateInfo.HighDefenceGrow);
			}
			bool flag4 = !agi;
			if (flag4)
			{
				petInfo.AgilityGrow = random.Next(petTemplateInfo.HighAgilityGrow * (random.Next(1, random.Next(2, 100)) / 100), petTemplateInfo.HighAgilityGrow);
			}
			bool flag5 = !luck;
			if (flag5)
			{
				petInfo.LuckGrow = random.Next(petTemplateInfo.HighLuckGrow * (random.Next(1, random.Next(2, 100)) / 100), petTemplateInfo.HighLuckGrow);
			}
		}

		// Token: 0x06007423 RID: 29731 RVA: 0x00265A28 File Offset: 0x00263C28
		public static UserPetInfo CreatePet(PetTemplateInfo info, int userID, int place, int playerLevel, int vipLv)
		{
			UserPetInfo userPetInfo = new UserPetInfo();
			double num = (double)info.StarLevel * 0.1;
			Random random = new Random();
			userPetInfo.BloodGrow = (int)Math.Ceiling((double)(random.Next((int)((double)info.HighBlood / (1.7 - num)), info.HighBlood - (int)((double)info.HighBlood / 17.1)) / 10));
			userPetInfo.AttackGrow = random.Next((int)((double)info.HighAttack / (1.7 - num)), info.HighAttack - (int)((double)info.HighAttack / 17.1));
			userPetInfo.DefenceGrow = random.Next((int)((double)info.HighDefence / (1.7 - num)), info.HighDefence - (int)((double)info.HighDefence / 17.1));
			userPetInfo.AgilityGrow = random.Next((int)((double)info.HighAgility / (1.7 - num)), info.HighAgility - (int)((double)info.HighAgility / 17.1));
			userPetInfo.LuckGrow = random.Next((int)((double)info.HighLuck / (1.7 - num)), info.HighLuck - (int)((double)info.HighLuck / 17.1));
			userPetInfo.ID = 0;
			userPetInfo.Hunger = 10000;
			userPetInfo.TemplateID = info.TemplateID;
			userPetInfo.Name = info.Name;
			userPetInfo.UserID = userID;
			userPetInfo.Place = place;
			userPetInfo.Level = 1;
			userPetInfo.BuildProp();
			userPetInfo.Skill = PetMgr.UpdateSkillPet(1, info.TemplateID, playerLevel);
			userPetInfo.SkillEquip = PetMgr.ActiveEquipSkill(1, vipLv);
			return userPetInfo;
		}

		// Token: 0x06007424 RID: 29732 RVA: 0x00265BF8 File Offset: 0x00263DF8
		public static UserPetInfo CreateNewPet(int vipLv)
		{
			string[] array = PetMgr.FindConfig("NewPet").Value.Split(new char[] { ',' });
			int num = PetMgr.rand.Next(array.Length);
			PetTemplateInfo petTemplateInfo = PetMgr.FindPetTemplate(Convert.ToInt32(array[num]));
			return PetMgr.CreatePet(petTemplateInfo, -1, -1, 60, vipLv);
		}

		// Token: 0x06007425 RID: 29733 RVA: 0x00265C54 File Offset: 0x00263E54
		public static void GetEvolutionPropArr(UserPetInfo _petInfo, PetTemplateInfo petTempleteInfo, ref double[] propArr, ref double[] growArr)
		{
			double[] array = new double[]
			{
				(double)(_petInfo.Blood * 100),
				(double)(_petInfo.Attack * 100),
				(double)(_petInfo.Defence * 100),
				(double)(_petInfo.Agility * 100),
				(double)(_petInfo.Luck * 100)
			};
			double[] array2 = new double[]
			{
				(double)_petInfo.BloodGrow,
				(double)_petInfo.AttackGrow,
				(double)_petInfo.DefenceGrow,
				(double)_petInfo.AgilityGrow,
				(double)_petInfo.LuckGrow
			};
			double[] array3 = new double[]
			{
				(double)petTempleteInfo.HighBlood,
				(double)petTempleteInfo.HighAttack,
				(double)petTempleteInfo.HighDefence,
				(double)petTempleteInfo.HighAgility,
				(double)petTempleteInfo.HighLuck
			};
			double[] array4 = new double[]
			{
				(double)petTempleteInfo.HighBloodGrow,
				(double)petTempleteInfo.HighAttackGrow,
				(double)petTempleteInfo.HighDefenceGrow,
				(double)petTempleteInfo.HighAgilityGrow,
				(double)petTempleteInfo.HighLuckGrow
			};
			double[] array5 = array3;
			double[] addedPropArr = PetMgr.GetAddedPropArr(1, array4);
			double[] array6 = array3;
			double[] addedPropArr2 = PetMgr.GetAddedPropArr(2, array4);
			double[] array7 = array3;
			double[] addedPropArr3 = PetMgr.GetAddedPropArr(3, array4);
			bool flag = _petInfo.Level < 30;
			if (flag)
			{
				for (int i = 0; i < array5.Length; i++)
				{
					array5[i] += (double)(_petInfo.Level - 1) * addedPropArr[i] - array[i];
					addedPropArr[i] -= array2[i];
					array5[i] = Math.Ceiling(array5[i] / 10.0) / 10.0;
					addedPropArr[i] = Math.Ceiling(addedPropArr[i] / 10.0) / 10.0;
				}
				propArr = array5;
				growArr = addedPropArr;
			}
			else
			{
				bool flag2 = _petInfo.Level < 50;
				if (flag2)
				{
					for (int j = 0; j < array6.Length; j++)
					{
						array6[j] += (double)(_petInfo.Level - 30) * addedPropArr2[j] + 29.0 * addedPropArr[j] - array[j];
						addedPropArr2[j] -= array2[j];
						array6[j] = Math.Ceiling(array6[j] / 10.0) / 10.0;
						addedPropArr2[j] = Math.Ceiling(addedPropArr2[j] / 10.0) / 10.0;
					}
					propArr = array6;
					growArr = addedPropArr2;
				}
				else
				{
					for (int k = 0; k < array7.Length; k++)
					{
						array7[k] += (double)(_petInfo.Level - 50) * addedPropArr3[k] + 20.0 * addedPropArr2[k] + 29.0 * addedPropArr[k] - array[k];
						addedPropArr3[k] -= array2[k];
						array7[k] = Math.Ceiling(array7[k] / 10.0) / 10.0;
						addedPropArr3[k] = Math.Ceiling(addedPropArr3[k] / 10.0) / 10.0;
					}
					propArr = array7;
					growArr = addedPropArr3;
				}
			}
		}

		// Token: 0x06007426 RID: 29734 RVA: 0x00265FC4 File Offset: 0x002641C4
		public static double[] GetAddedPropArr(int grade, double[] propArr)
		{
			double[] array = new double[5];
			array[0] = propArr[0] * Math.Pow(2.0, (double)(grade - 1));
			double[] array2 = array;
			for (int i = 1; i < 5; i++)
			{
				array2[i] = propArr[i] * Math.Pow(1.5, (double)(grade - 1));
			}
			return array2;
		}

		// Token: 0x06007427 RID: 29735 RVA: 0x00266024 File Offset: 0x00264224
		public static PetExpItemPriceInfo FindPetExpItemPrice(int count)
		{
			bool flag = PetMgr.m_expItemPrices == null;
			if (flag)
			{
				PetMgr.Init();
			}
			bool flag2 = PetMgr.m_expItemPrices.ContainsKey(count);
			PetExpItemPriceInfo petExpItemPriceInfo;
			if (flag2)
			{
				petExpItemPriceInfo = PetMgr.m_expItemPrices[count];
			}
			else
			{
				petExpItemPriceInfo = null;
			}
			return petExpItemPriceInfo;
		}

		// Token: 0x040043FF RID: 17407
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04004400 RID: 17408
		private static Dictionary<string, PetConfig> m_configs;

		// Token: 0x04004401 RID: 17409
		private static Dictionary<int, PetLevel> m_levels;

		// Token: 0x04004402 RID: 17410
		private static Dictionary<int, PetSkillElementInfo> m_skillElements;

		// Token: 0x04004403 RID: 17411
		private static Dictionary<int, PetSkillInfo> m_skills;

		// Token: 0x04004404 RID: 17412
		private static Dictionary<int, PetSkillTemplateInfo> m_skillTemplates;

		// Token: 0x04004405 RID: 17413
		private static Dictionary<int, PetTemplateInfo> m_templateIds;

		// Token: 0x04004406 RID: 17414
		private static Dictionary<int, PetExpItemPriceInfo> m_expItemPrices;

		// Token: 0x04004407 RID: 17415
		private static List<GameNeedPetSkillInfo> m_gameNeedPetSkillInfos = new List<GameNeedPetSkillInfo>();

		// Token: 0x04004408 RID: 17416
		private static ReaderWriterLock m_clientLocker = new ReaderWriterLock();

		// Token: 0x04004409 RID: 17417
		private static Random rand;
	}
}
