﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E1F RID: 3615
	public class AE1257 : BasePetEffect
	{
		// Token: 0x06007E53 RID: 32339 RVA: 0x0029FE0C File Offset: 0x0029E00C
		public AE1257(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1257, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E54 RID: 32340 RVA: 0x0029FE8C File Offset: 0x0029E08C
		public override bool Start(Living living)
		{
			AE1257 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1257) as AE1257;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E55 RID: 32341 RVA: 0x00030DCF File Offset: 0x0002EFCF
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E56 RID: 32342 RVA: 0x00030DE5 File Offset: 0x0002EFE5
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E57 RID: 32343 RVA: 0x0029FEEC File Offset: 0x0029E0EC
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddPetEffect(new CE1257(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004D11 RID: 19729
		private int m_type = 0;

		// Token: 0x04004D12 RID: 19730
		private int m_count = 0;

		// Token: 0x04004D13 RID: 19731
		private int m_probability = 0;

		// Token: 0x04004D14 RID: 19732
		private int m_delay = 0;

		// Token: 0x04004D15 RID: 19733
		private int m_coldDown = 0;

		// Token: 0x04004D16 RID: 19734
		private int m_currentId;

		// Token: 0x04004D17 RID: 19735
		private int m_added = 0;
	}
}
