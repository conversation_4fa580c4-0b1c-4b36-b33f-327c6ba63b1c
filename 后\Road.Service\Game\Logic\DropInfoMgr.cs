﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using log4net;

namespace Game.Logic
{
	// Token: 0x02000C88 RID: 3208
	public class DropInfoMgr
	{
		// Token: 0x06007252 RID: 29266 RVA: 0x00262410 File Offset: 0x00260610
		public static bool CanDrop(int templateId)
		{
			bool flag = DropInfoMgr.DropInfo == null;
			bool flag2;
			if (flag)
			{
				flag2 = true;
			}
			else
			{
				DropInfoMgr.m_lock.AcquireWriterLock(-1);
				try
				{
					bool flag3 = DropInfoMgr.DropInfo.ContainsKey(templateId);
					if (flag3)
					{
						MacroDropInfo macroDropInfo = DropInfoMgr.DropInfo[templateId];
						bool flag4 = macroDropInfo.DropCount < macroDropInfo.MaxDropCount || macroDropInfo.SelfDropCount >= macroDropInfo.DropCount;
						if (flag4)
						{
							MacroDropInfo macroDropInfo2 = macroDropInfo;
							int num = macroDropInfo2.SelfDropCount;
							macroDropInfo2.SelfDropCount = num + 1;
							MacroDropInfo macroDropInfo3 = macroDropInfo;
							num = macroDropInfo3.DropCount;
							macroDropInfo3.DropCount = num + 1;
							return true;
						}
						return false;
					}
				}
				catch (Exception ex)
				{
					bool isErrorEnabled = DropInfoMgr.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						DropInfoMgr.log.Error("DropInfoMgr CanDrop", ex);
					}
				}
				finally
				{
					DropInfoMgr.m_lock.ReleaseWriterLock();
				}
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x04003DDD RID: 15837
		protected static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04003DDE RID: 15838
		protected static ReaderWriterLock m_lock = new ReaderWriterLock();

		// Token: 0x04003DDF RID: 15839
		public static Dictionary<int, MacroDropInfo> DropInfo;
	}
}
