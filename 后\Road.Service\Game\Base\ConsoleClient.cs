﻿using System;

namespace Game.Base
{
	// Token: 0x02000F76 RID: 3958
	public class ConsoleClient : BaseClient
	{
		// Token: 0x060085A2 RID: 34210 RVA: 0x00035AD2 File Offset: 0x00033CD2
		public ConsoleClient()
			: base(null, null)
		{
		}

		// Token: 0x060085A3 RID: 34211 RVA: 0x0002B4BA File Offset: 0x000296BA
		public override void DisplayMessage(string msg)
		{
			Console.WriteLine(msg);
		}
	}
}
