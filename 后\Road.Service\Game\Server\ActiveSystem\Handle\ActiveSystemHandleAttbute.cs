﻿using System;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C4B RID: 3147
	internal class ActiveSystemHandleAttbute : Attribute
	{
		// Token: 0x17001322 RID: 4898
		// (get) Token: 0x06007014 RID: 28692 RVA: 0x0002A2CC File Offset: 0x000284CC
		// (set) Token: 0x06007015 RID: 28693 RVA: 0x0002A2D4 File Offset: 0x000284D4
		public byte Code { get; private set; }

		// Token: 0x06007016 RID: 28694 RVA: 0x0002A2DD File Offset: 0x000284DD
		public ActiveSystemHandleAttbute(byte code)
		{
			this.Code = code;
		}
	}
}
