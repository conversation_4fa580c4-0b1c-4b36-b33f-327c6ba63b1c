﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D73 RID: 3443
	public class PE1216 : BasePetEffect
	{
		// Token: 0x06007AD6 RID: 31446 RVA: 0x0029084C File Offset: 0x0028EA4C
		public PE1216(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1216, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AD7 RID: 31447 RVA: 0x002908CC File Offset: 0x0028EACC
		public override bool Start(Living living)
		{
			PE1216 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1216) as PE1216;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AD8 RID: 31448 RVA: 0x0029092C File Offset: 0x0028EB2C
		protected override void OnAttachedToPlayer(Player player)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 1500;
				player.PetEffects.AddMaxBloodValue = this.m_added;
			}
		}

		// Token: 0x06007AD9 RID: 31449 RVA: 0x00005683 File Offset: 0x00003883
		protected override void OnRemovedFromPlayer(Player player)
		{
		}

		// Token: 0x04004865 RID: 18533
		private int m_type = 0;

		// Token: 0x04004866 RID: 18534
		private int m_count = 0;

		// Token: 0x04004867 RID: 18535
		private int m_probability = 0;

		// Token: 0x04004868 RID: 18536
		private int m_delay = 0;

		// Token: 0x04004869 RID: 18537
		private int m_coldDown = 0;

		// Token: 0x0400486A RID: 18538
		private int m_currentId;

		// Token: 0x0400486B RID: 18539
		private int m_added = 0;
	}
}
