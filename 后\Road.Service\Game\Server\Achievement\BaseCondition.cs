﻿using System;
using System.Reflection;
using Game.Server.GameObjects;
using log4net;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C5F RID: 3167
	public class BaseCondition
	{
		// Token: 0x17001325 RID: 4901
		// (get) Token: 0x06007064 RID: 28772 RVA: 0x0002A3E4 File Offset: 0x000285E4
		public AchievementConditionInfo Info
		{
			get
			{
				return this.m_info;
			}
		}

		// Token: 0x17001326 RID: 4902
		// (get) Token: 0x06007065 RID: 28773 RVA: 0x0024F88C File Offset: 0x0024DA8C
		// (set) Token: 0x06007066 RID: 28774 RVA: 0x0024F8A4 File Offset: 0x0024DAA4
		public int Value
		{
			get
			{
				return this.m_value;
			}
			set
			{
				bool flag = this.m_value != value;
				if (flag)
				{
					this.m_value = value;
					this.m_list.Update();
				}
			}
		}

		// Token: 0x06007067 RID: 28775 RVA: 0x0002A3EC File Offset: 0x000285EC
		public BaseCondition(BaseAchievement ach, AchievementConditionInfo info, int value)
		{
			this.m_list = ach;
			this.m_info = info;
			this.m_value = value;
		}

		// Token: 0x06007068 RID: 28776 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void AddTrigger(GamePlayer player)
		{
		}

		// Token: 0x06007069 RID: 28777 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void RemoveTrigger(GamePlayer player)
		{
		}

		// Token: 0x0600706A RID: 28778 RVA: 0x00068C5C File Offset: 0x00066E5C
		public virtual bool IsCompleted(GamePlayer player)
		{
			return false;
		}

		// Token: 0x0600706B RID: 28779 RVA: 0x00069EBC File Offset: 0x000680BC
		public virtual bool Finish(GamePlayer player)
		{
			return true;
		}

		// Token: 0x0600706C RID: 28780 RVA: 0x0024F8D8 File Offset: 0x0024DAD8
		public static BaseCondition CreateCondition(BaseAchievement ach, AchievementConditionInfo info, int value)
		{
			int condictionType = info.CondictionType;
			if (!true)
			{
			}
			BaseCondition baseCondition;
			switch (condictionType)
			{
			case 1:
				baseCondition = new PropertisCharacterCondition(ach, info, value, "attack");
				goto IL_0730;
			case 2:
				baseCondition = new PropertisCharacterCondition(ach, info, value, "defence");
				goto IL_0730;
			case 3:
				baseCondition = new PropertisCharacterCondition(ach, info, value, "agility");
				goto IL_0730;
			case 4:
				baseCondition = new PropertisCharacterCondition(ach, info, value, "luck");
				goto IL_0730;
			case 5:
				baseCondition = new GameKillingBossCondition(ach, info, value, new int[] { 4009, 4109, 4209, 4309 });
				goto IL_0730;
			case 6:
				baseCondition = new GameOverPassCondition(ach, info, 6, value);
				goto IL_0730;
			case 7:
				baseCondition = new GameKillingBossCondition(ach, info, value, new int[] { 3317 });
				goto IL_0730;
			case 8:
				baseCondition = new GameKillingBossCondition(ach, info, value, new int[] { 4309 });
				goto IL_0730;
			case 9:
				baseCondition = new PropertisCharacterCondition(ach, info, value, "fightpower");
				goto IL_0730;
			case 10:
				baseCondition = new LevelUpgradeCondition(ach, info, value);
				goto IL_0730;
			case 11:
				baseCondition = new FightCompleteCondition(ach, info, value);
				goto IL_0730;
			case 13:
				baseCondition = new OnlineTimeCondition(ach, info, value);
				goto IL_0730;
			case 14:
				baseCondition = new FightMatchWinCondition(ach, info, value);
				goto IL_0730;
			case 15:
				baseCondition = new GuildFightWinCondition(ach, info, value);
				goto IL_0730;
			case 19:
				baseCondition = new FightKillPlayerCondition(ach, info, value);
				goto IL_0730;
			case 21:
				baseCondition = new QuestGreenFinishCondition(ach, info, value);
				goto IL_0730;
			case 22:
				baseCondition = new QuestDailyFinishCondition(ach, info, value);
				goto IL_0730;
			case 24:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 25:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 26:
				baseCondition = new GameKillingBossCondition(ach, info, value, new int[] { 2003, 2103 });
				goto IL_0730;
			case 27:
				baseCondition = new GameKillingBossCondition(ach, info, value, new int[] { 1006, 1106, 1206, 1306 });
				goto IL_0730;
			case 28:
				baseCondition = new GameOverPassCondition(ach, info, 5, value);
				goto IL_0730;
			case 29:
				baseCondition = new GameOverPassCondition(ach, info, 4, value);
				goto IL_0730;
			case 30:
				baseCondition = new GameOverPassCondition(ach, info, 3, value);
				goto IL_0730;
			case 31:
				baseCondition = new GameKillingBossCondition(ach, info, value, new int[] { 3017, 3117, 3217, 3317 });
				goto IL_0730;
			case 32:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 33:
				baseCondition = new HotSpingEnterCondition(ach, info, value);
				goto IL_0730;
			case 34:
				baseCondition = new UsingItemCondition(ach, info, 10020, value);
				goto IL_0730;
			case 35:
				baseCondition = new UsingItemCondition(ach, info, 10022, value);
				goto IL_0730;
			case 36:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 37:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 38:
				baseCondition = new GoldCollectionCondition(ach, info, value);
				goto IL_0730;
			case 39:
				baseCondition = new GiftTokenCollectionCondition(ach, info, value);
				goto IL_0730;
			case 40:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 41:
				baseCondition = new FightOneBloodIsWinCondition(ach, info, value);
				goto IL_0730;
			case 42:
				baseCondition = new ItemEquipCondition(ach, info, value, 17002);
				goto IL_0730;
			case 45:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 47:
				baseCondition = new UseBigBugleCondition(ach, info, value);
				goto IL_0730;
			case 48:
				baseCondition = new UseSmaillBugleCondition(ach, info, value);
				goto IL_0730;
			case 50:
				baseCondition = new FightAddOfferCondition(ach, info, value);
				goto IL_0730;
			case 51:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 52:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 53:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 54:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 55:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 56:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 59:
				baseCondition = new CompleteQuestGoodManCondtion(ach, info, value);
				goto IL_0730;
			case 60:
				baseCondition = new GameKillingBossCondition(ach, info, value, new int[] { 1308, 1208, 1108, 1008 });
				goto IL_0730;
			case 61:
				baseCondition = new GameKillingBossCondition(ach, info, value, new int[] { 1303 });
				goto IL_0730;
			case 62:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 64:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 65:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 66:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 67:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 68:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 69:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 70:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 71:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 72:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 73:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 74:
				baseCondition = new UserVipUpdateCondition(ach, info, value);
				goto IL_0730;
			case 75:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 76:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 77:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 78:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 79:
				baseCondition = new GameKillingBossCondition(ach, info, value, new int[] { 7031, 7131, 7231 });
				goto IL_0730;
			case 80:
				baseCondition = new GameOverPassCondition(ach, info, 2, value);
				goto IL_0730;
			case 81:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 82:
				baseCondition = new GameOverPassCondition(ach, info, 1, value);
				goto IL_0730;
			case 83:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 84:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 85:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 86:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 87:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 88:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 89:
				baseCondition = new GameKillingBossCondition(ach, info, value, new int[] { 5131, 5231, 5331 });
				goto IL_0730;
			case 90:
				baseCondition = new GameOverPassCondition(ach, info, 3, value);
				goto IL_0730;
			case 91:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 92:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 93:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 94:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			case 95:
				baseCondition = new DefaultCondition(ach, info, value);
				goto IL_0730;
			}
			baseCondition = new DefaultCondition(ach, info, value);
			IL_0730:
			if (!true)
			{
			}
			return baseCondition;
		}

		// Token: 0x04003C5C RID: 15452
		protected AchievementConditionInfo m_info;

		// Token: 0x04003C5D RID: 15453
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04003C5E RID: 15454
		private int m_value;

		// Token: 0x04003C5F RID: 15455
		private BaseAchievement m_list;
	}
}
