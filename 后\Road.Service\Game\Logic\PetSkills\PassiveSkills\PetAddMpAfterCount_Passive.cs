﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CF3 RID: 3315
	public class PetAddMpAfterCount_Passive : BasePetEffect
	{
		// Token: 0x06007819 RID: 30745 RVA: 0x002840AC File Offset: 0x002822AC
		public PetAddMpAfterCount_Passive(string elementID)
			: base(ePetEffectType.PetAddMpAfterCount_Passive, elementID)
		{
			if (!(elementID == "1748"))
			{
				if (!(elementID == "1781"))
				{
					if (elementID == "1815")
					{
						this.m_added = 20;
						this.m_value = 25;
					}
				}
				else
				{
					this.m_added = 30;
					this.m_value = 14;
				}
			}
			else
			{
				this.m_added = 20;
				this.m_value = 20;
			}
		}

		// Token: 0x0600781A RID: 30746 RVA: 0x0028412C File Offset: 0x0028232C
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetAddMpAfterCount_Passive) is PetAddMpAfterCount_Passive;
			return flag || base.Start(living);
		}

		// Token: 0x0600781B RID: 30747 RVA: 0x0002C635 File Offset: 0x0002A835
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
		}

		// Token: 0x0600781C RID: 30748 RVA: 0x0002C64B File Offset: 0x0002A84B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
		}

		// Token: 0x0600781D RID: 30749 RVA: 0x00284168 File Offset: 0x00282368
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageamount, ref int criticalamount)
		{
			this.m_countTotal++;
			bool flag = this.m_countTotal >= this.m_value;
			if (flag)
			{
				this.m_countTotal = 0;
				(living as Player).AddPetMP(this.m_added);
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				living.AddPetEffect(new PetShowFireOfHatredEquip(99, "1751"), 0);
			}
		}

		// Token: 0x04004665 RID: 18021
		private int m_added;

		// Token: 0x04004666 RID: 18022
		private int m_value;

		// Token: 0x04004667 RID: 18023
		private int m_countTotal;
	}
}
