﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EC6 RID: 3782
	public class CE8888 : BasePetEffect
	{
		// Token: 0x06008258 RID: 33368 RVA: 0x002B0650 File Offset: 0x002AE850
		public CE8888(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE8888, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008259 RID: 33369 RVA: 0x002B06D0 File Offset: 0x002AE8D0
		public override bool Start(Living living)
		{
			CE8888 ce = living.PetEffectList.GetOfType(ePetEffectType.CE8888) as CE8888;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600825A RID: 33370 RVA: 0x002B0730 File Offset: 0x002AE930
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				player.BaseDamage += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600825B RID: 33371 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600825C RID: 33372 RVA: 0x002B0794 File Offset: 0x002AE994
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600825D RID: 33373 RVA: 0x00033454 File Offset: 0x00031654
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BaseDamage -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x040051A8 RID: 20904
		private int m_type = 0;

		// Token: 0x040051A9 RID: 20905
		private int m_count = 0;

		// Token: 0x040051AA RID: 20906
		private int m_probability = 0;

		// Token: 0x040051AB RID: 20907
		private int m_delay = 0;

		// Token: 0x040051AC RID: 20908
		private int m_coldDown = 0;

		// Token: 0x040051AD RID: 20909
		private int m_currentId;

		// Token: 0x040051AE RID: 20910
		private int m_added = 0;
	}
}
