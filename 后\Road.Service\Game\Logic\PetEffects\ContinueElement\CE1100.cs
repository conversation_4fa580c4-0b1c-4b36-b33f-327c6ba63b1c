﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E6A RID: 3690
	public class CE1100 : BasePetEffect
	{
		// Token: 0x06008008 RID: 32776 RVA: 0x002A79A0 File Offset: 0x002A5BA0
		public CE1100(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1100, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008009 RID: 32777 RVA: 0x002A7A20 File Offset: 0x002A5C20
		public override bool Start(Living living)
		{
			CE1100 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1100) as CE1100;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600800A RID: 32778 RVA: 0x002A7A80 File Offset: 0x002A5C80
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 1000;
				player.MaxBlood += this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600800B RID: 32779 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600800C RID: 32780 RVA: 0x002A7AE4 File Offset: 0x002A5CE4
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600800D RID: 32781 RVA: 0x00031DB9 File Offset: 0x0002FFB9
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.MaxBlood -= this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F21 RID: 20257
		private int m_type = 0;

		// Token: 0x04004F22 RID: 20258
		private int m_count = 0;

		// Token: 0x04004F23 RID: 20259
		private int m_probability = 0;

		// Token: 0x04004F24 RID: 20260
		private int m_delay = 0;

		// Token: 0x04004F25 RID: 20261
		private int m_coldDown = 0;

		// Token: 0x04004F26 RID: 20262
		private int m_currentId;

		// Token: 0x04004F27 RID: 20263
		private int m_added = 0;
	}
}
