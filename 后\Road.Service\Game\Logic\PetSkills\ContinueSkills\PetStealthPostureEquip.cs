﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D1C RID: 3356
	public class PetStealthPostureEquip : BasePetEffect
	{
		// Token: 0x060078F0 RID: 30960 RVA: 0x0002D0DD File Offset: 0x0002B2DD
		public PetStealthPostureEquip(int count, string elementID)
			: base(ePetEffectType.PetStealthPostureEquip, elementID)
		{
			this.m_count = count;
		}

		// Token: 0x060078F1 RID: 30961 RVA: 0x00288BE4 File Offset: 0x00286DE4
		public override bool Start(Living living)
		{
			PetStealthPostureEquip petStealthPostureEquip = living.PetEffectList.GetOfType(ePetEffectType.PetStealthPostureEquip) as PetStealthPostureEquip;
			bool flag = petStealthPostureEquip != null;
			bool flag2;
			if (flag)
			{
				petStealthPostureEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078F2 RID: 30962 RVA: 0x0002D0F4 File Offset: 0x0002B2F4
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x060078F3 RID: 30963 RVA: 0x0002D10A File Offset: 0x0002B30A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x060078F4 RID: 30964 RVA: 0x00288C2C File Offset: 0x00286E2C
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x040046DD RID: 18141
		private int m_count;
	}
}
