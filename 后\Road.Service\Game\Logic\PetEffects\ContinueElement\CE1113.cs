﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E6C RID: 3692
	public class CE1113 : BasePetEffect
	{
		// Token: 0x06008014 RID: 32788 RVA: 0x002A7C88 File Offset: 0x002A5E88
		public CE1113(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1113, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008015 RID: 32789 RVA: 0x002A7D08 File Offset: 0x002A5F08
		public override bool Start(Living living)
		{
			CE1113 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1113) as CE1113;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008016 RID: 32790 RVA: 0x00031E15 File Offset: 0x00030015
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008017 RID: 32791 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008018 RID: 32792 RVA: 0x002A7D68 File Offset: 0x002A5F68
		private void Player_BeginNextTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008019 RID: 32793 RVA: 0x00031E3E File Offset: 0x0003003E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x04004F2F RID: 20271
		private int m_type = 0;

		// Token: 0x04004F30 RID: 20272
		private int m_count = 0;

		// Token: 0x04004F31 RID: 20273
		private int m_probability = 0;

		// Token: 0x04004F32 RID: 20274
		private int m_delay = 0;

		// Token: 0x04004F33 RID: 20275
		private int m_coldDown = 0;

		// Token: 0x04004F34 RID: 20276
		private int m_currentId;

		// Token: 0x04004F35 RID: 20277
		private int m_added = 0;
	}
}
