﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DF5 RID: 3573
	public class AE1195 : BasePetEffect
	{
		// Token: 0x06007D79 RID: 32121 RVA: 0x0029C2A4 File Offset: 0x0029A4A4
		public AE1195(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1195, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D7A RID: 32122 RVA: 0x0029C324 File Offset: 0x0029A524
		public override bool Start(Living living)
		{
			AE1195 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1195) as AE1195;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D7B RID: 32123 RVA: 0x0003055D File Offset: 0x0002E75D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D7C RID: 32124 RVA: 0x00030573 File Offset: 0x0002E773
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D7D RID: 32125 RVA: 0x0029C384 File Offset: 0x0029A584
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1195(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004BEB RID: 19435
		private int m_type = 0;

		// Token: 0x04004BEC RID: 19436
		private int m_count = 0;

		// Token: 0x04004BED RID: 19437
		private int m_probability = 0;

		// Token: 0x04004BEE RID: 19438
		private int m_delay = 0;

		// Token: 0x04004BEF RID: 19439
		private int m_coldDown = 0;

		// Token: 0x04004BF0 RID: 19440
		private int m_currentId;

		// Token: 0x04004BF1 RID: 19441
		private int m_added = 0;
	}
}
