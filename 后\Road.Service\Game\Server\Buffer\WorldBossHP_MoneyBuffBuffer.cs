﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Buffer
{
	// Token: 0x02000C40 RID: 3136
	public class WorldBossHP_MoneyBuffBuffer : AbstractBuffer
	{
		// Token: 0x06006FA6 RID: 28582 RVA: 0x00029E24 File Offset: 0x00028024
		public WorldBossHP_MoneyBuffBuffer(UserBufferInfo buffer)
			: base(buffer)
		{
		}

		// Token: 0x06006FA7 RID: 28583 RVA: 0x0024B084 File Offset: 0x00249284
		public override void Start(GamePlayer player)
		{
			WorldBossHP_MoneyBuffBuffer worldBossHP_MoneyBuffBuffer = player.BufferList.GetOfType(typeof(WorldBossHP_MoneyBuffBuffer)) as WorldBossHP_MoneyBuffBuffer;
			bool flag = worldBossHP_MoneyBuffBuffer != null;
			if (flag)
			{
				worldBossHP_MoneyBuffBuffer.Info.ValidDate = this.m_info.ValidDate;
				player.BufferList.UpdateBuffer(worldBossHP_MoneyBuffBuffer);
				for (int i = 0; i < player.FightBuffs.Count; i++)
				{
					bool flag2 = player.FightBuffs[i].Type == this.m_info.Type && player.FightBuffs[i].ValidCount < 20;
					if (flag2)
					{
						player.FightBuffs[i].Value += this.m_info.Value;
						UserBufferInfo userBufferInfo = player.FightBuffs[i];
						int validCount = userBufferInfo.ValidCount;
						userBufferInfo.ValidCount = validCount + 1;
						break;
					}
				}
			}
			else
			{
				base.Start(player);
				player.FightBuffs.Add(base.Info);
			}
		}

		// Token: 0x06006FA8 RID: 28584 RVA: 0x00029E2F File Offset: 0x0002802F
		public override void Stop()
		{
			this.m_player.FightBuffs.Remove(base.Info);
			base.Stop();
		}
	}
}
