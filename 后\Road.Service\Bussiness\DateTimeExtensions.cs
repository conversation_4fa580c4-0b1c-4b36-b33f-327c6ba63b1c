﻿using System;
using System.Globalization;

namespace Bussiness
{
	// Token: 0x02000FA8 RID: 4008
	public static class DateTimeExtensions
	{
		// Token: 0x06008865 RID: 34917 RVA: 0x002D1218 File Offset: 0x002CF418
		public static int GetWeekOfMonth(this DateTime time)
		{
			DateTime dateTime = new DateTime(time.Year, time.Month, 1);
			return time.GetWeekOfYear() - dateTime.GetWeekOfYear() + 1;
		}

		// Token: 0x06008866 RID: 34918 RVA: 0x002D1250 File Offset: 0x002CF450
		private static int WeekOfYear(this DateTime time)
		{
			return DateTimeExtensions.m_gc.GetWeekOfYear(time, CalendarWeekRule.FirstDay, DayOfWeek.Sunday);
		}

		// Token: 0x06008867 RID: 34919 RVA: 0x002D1270 File Offset: 0x002CF470
		public static int GetWeekOfYear(this DateTime time)
		{
			CultureInfo cultureInfo = new CultureInfo("zh-CN");
			Calendar calendar = cultureInfo.Calendar;
			CalendarWeekRule calendarWeekRule = cultureInfo.DateTimeFormat.CalendarWeekRule;
			DayOfWeek firstDayOfWeek = cultureInfo.DateTimeFormat.FirstDayOfWeek;
			return calendar.GetWeekOfYear(time, calendarWeekRule, firstDayOfWeek);
		}

		// Token: 0x06008868 RID: 34920 RVA: 0x002D12B8 File Offset: 0x002CF4B8
		public static DateTime GetStartNextMonth(this DateTime time, int totalMonth)
		{
			DateTime dateTime = time.AddMonths(totalMonth);
			return new DateTime(dateTime.Year, dateTime.Month, 1).Date;
		}

		// Token: 0x040053E2 RID: 21474
		private static GregorianCalendar m_gc = new GregorianCalendar();
	}
}
