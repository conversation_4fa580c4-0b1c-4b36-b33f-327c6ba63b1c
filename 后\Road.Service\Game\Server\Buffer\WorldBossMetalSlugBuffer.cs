﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Buffer
{
	// Token: 0x02000C41 RID: 3137
	public class WorldBossMetalSlugBuffer : AbstractBuffer
	{
		// Token: 0x06006FA9 RID: 28585 RVA: 0x00029E24 File Offset: 0x00028024
		public WorldBossMetalSlugBuffer(UserBufferInfo buffer)
			: base(buffer)
		{
		}

		// Token: 0x06006FAA RID: 28586 RVA: 0x0024B1A0 File Offset: 0x002493A0
		public override void Start(GamePlayer player)
		{
			WorldBossMetalSlugBuffer worldBossMetalSlugBuffer = player.BufferList.GetOfType(typeof(WorldBossMetalSlugBuffer)) as WorldBossMetalSlugBuffer;
			bool flag = worldBossMetalSlugBuffer != null;
			if (flag)
			{
				worldBossMetalSlugBuffer.Info.ValidDate = base.Info.ValidDate;
				player.BufferList.UpdateBuffer(worldBossMetalSlugBuffer);
				player.UpdateFightBuff(base.Info);
			}
			else
			{
				base.Start(player);
				player.FightBuffs.Add(base.Info);
			}
		}

		// Token: 0x06006FAB RID: 28587 RVA: 0x00029E2F File Offset: 0x0002802F
		public override void Stop()
		{
			this.m_player.FightBuffs.Remove(base.Info);
			base.Stop();
		}
	}
}
