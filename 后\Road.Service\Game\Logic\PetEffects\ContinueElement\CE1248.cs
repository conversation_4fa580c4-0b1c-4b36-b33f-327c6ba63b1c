﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EB0 RID: 3760
	public class CE1248 : BasePetEffect
	{
		// Token: 0x060081CA RID: 33226 RVA: 0x002AE354 File Offset: 0x002AC554
		public CE1248(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1248, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081CB RID: 33227 RVA: 0x002AE3D4 File Offset: 0x002AC5D4
		public override bool Start(Living living)
		{
			CE1248 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1248) as CE1248;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081CC RID: 33228 RVA: 0x00032F4F File Offset: 0x0003114F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081CD RID: 33229 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081CE RID: 33230 RVA: 0x002AE434 File Offset: 0x002AC634
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			living.Game.SendPetBuff(living, base.ElementInfo, true);
			this.m_added = 200;
			damageAmount -= this.m_added;
			bool flag = damageAmount < 0;
			if (flag)
			{
				damageAmount = 1;
			}
		}

		// Token: 0x060081CF RID: 33231 RVA: 0x002AE47C File Offset: 0x002AC67C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060081D0 RID: 33232 RVA: 0x00032F8B File Offset: 0x0003118B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
		}

		// Token: 0x0400510D RID: 20749
		private int m_type = 0;

		// Token: 0x0400510E RID: 20750
		private int m_count = 0;

		// Token: 0x0400510F RID: 20751
		private int m_probability = 0;

		// Token: 0x04005110 RID: 20752
		private int m_delay = 0;

		// Token: 0x04005111 RID: 20753
		private int m_coldDown = 0;

		// Token: 0x04005112 RID: 20754
		private int m_currentId;

		// Token: 0x04005113 RID: 20755
		private int m_added = 0;
	}
}
