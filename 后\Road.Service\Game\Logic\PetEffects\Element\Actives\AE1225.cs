﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E0B RID: 3595
	public class AE1225 : BasePetEffect
	{
		// Token: 0x06007DEC RID: 32236 RVA: 0x0029E3E0 File Offset: 0x0029C5E0
		public AE1225(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1225, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DED RID: 32237 RVA: 0x0029E460 File Offset: 0x0029C660
		public override bool Start(Living living)
		{
			AE1225 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1225) as AE1225;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DEE RID: 32238 RVA: 0x000309ED File Offset: 0x0002EBED
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DEF RID: 32239 RVA: 0x00030A03 File Offset: 0x0002EC03
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DF0 RID: 32240 RVA: 0x0029E4C0 File Offset: 0x0029C6C0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && !this.IsTrigger;
			if (flag)
			{
				this.m_added = 800;
				player.AddBlood(-this.m_added, 1);
				bool flag2 = player.Blood <= 0;
				if (flag2)
				{
					player.Die();
				}
				this.IsTrigger = true;
			}
		}

		// Token: 0x04004C85 RID: 19589
		private int m_type = 0;

		// Token: 0x04004C86 RID: 19590
		private int m_count = 0;

		// Token: 0x04004C87 RID: 19591
		private int m_probability = 0;

		// Token: 0x04004C88 RID: 19592
		private int m_delay = 0;

		// Token: 0x04004C89 RID: 19593
		private int m_coldDown = 0;

		// Token: 0x04004C8A RID: 19594
		private int m_currentId;

		// Token: 0x04004C8B RID: 19595
		private int m_added = 0;
	}
}
