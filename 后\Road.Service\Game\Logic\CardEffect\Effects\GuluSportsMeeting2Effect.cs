﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F2C RID: 3884
	public class GuluSportsMeeting2Effect : BaseCardEffect
	{
		// Token: 0x170014A9 RID: 5289
		// (get) Token: 0x0600843E RID: 33854 RVA: 0x00034CF4 File Offset: 0x00032EF4
		public int CureValue
		{
			get
			{
				return this.m_added;
			}
		}

		// Token: 0x0600843F RID: 33855 RVA: 0x002B7404 File Offset: 0x002B5604
		public GuluSportsMeeting2Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.GuluSportsMeeting2, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008440 RID: 33856 RVA: 0x002B7474 File Offset: 0x002B5674
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.GuluSportsMeeting2) is GuluSportsMeeting2Effect;
			return flag || base.Start(living);
		}

		// Token: 0x06008441 RID: 33857 RVA: 0x00034CFC File Offset: 0x00032EFC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerCure += this.ChangeProperty;
			player.PlayerAfterReset += this.ChangeProperty1;
		}

		// Token: 0x06008442 RID: 33858 RVA: 0x00034D25 File Offset: 0x00032F25
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerCure -= this.ChangeProperty;
			player.PlayerAfterReset -= this.ChangeProperty1;
		}

		// Token: 0x06008443 RID: 33859 RVA: 0x002B74AC File Offset: 0x002B56AC
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 6;
			if (flag2)
			{
				this.m_added = this.m_value;
				player.AddBlood(this.m_value);
			}
		}

		// Token: 0x06008444 RID: 33860 RVA: 0x002B7518 File Offset: 0x002B5718
		private void ChangeProperty1(Player player)
		{
			bool flag = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 6;
			if (flag)
			{
				player.Game.SendMessage(player.PlayerDetail, "您激活了啵咕运动会2件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活啵咕运动会2件套卡.", 3);
			}
		}

		// Token: 0x04005278 RID: 21112
		private int m_indexValue = 0;

		// Token: 0x04005279 RID: 21113
		private int m_value = 0;

		// Token: 0x0400527A RID: 21114
		private int m_added = 0;
	}
}
