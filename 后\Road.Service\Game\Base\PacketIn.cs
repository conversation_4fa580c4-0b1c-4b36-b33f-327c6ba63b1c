﻿using System;
using System.Text;

namespace Game.Base
{
	// Token: 0x02000F7B RID: 3963
	public class PacketIn
	{
		// Token: 0x170014C7 RID: 5319
		// (get) Token: 0x060085C2 RID: 34242 RVA: 0x00035ADE File Offset: 0x00033CDE
		public byte[] Buffer
		{
			get
			{
				return this.m_buffer;
			}
		}

		// Token: 0x170014C8 RID: 5320
		// (get) Token: 0x060085C3 RID: 34243 RVA: 0x00035AE6 File Offset: 0x00033CE6
		public int Length
		{
			get
			{
				return this.m_length;
			}
		}

		// Token: 0x170014C9 RID: 5321
		// (get) Token: 0x060085C4 RID: 34244 RVA: 0x002BC5C0 File Offset: 0x002BA7C0
		// (set) Token: 0x060085C5 RID: 34245 RVA: 0x00035AEE File Offset: 0x00033CEE
		public int Offset
		{
			get
			{
				return this.m_offset;
			}
			set
			{
				this.m_offset = value;
			}
		}

		// Token: 0x170014CA RID: 5322
		// (get) Token: 0x060085C6 RID: 34246 RVA: 0x00035AF8 File Offset: 0x00033CF8
		public int DataLeft
		{
			get
			{
				return this.m_length - this.m_offset;
			}
		}

		// Token: 0x060085C7 RID: 34247 RVA: 0x00035B07 File Offset: 0x00033D07
		public PacketIn(byte[] buf, int len)
		{
			this.m_buffer = buf;
			this.m_length = len;
			this.m_offset = 0;
		}

		// Token: 0x060085C8 RID: 34248 RVA: 0x00035B41 File Offset: 0x00033D41
		public void Skip(int num)
		{
			this.m_offset += num;
		}

		// Token: 0x060085C9 RID: 34249 RVA: 0x002BC5D8 File Offset: 0x002BA7D8
		public virtual bool ReadBoolean()
		{
			byte[] buffer = this.m_buffer;
			int offset = this.m_offset;
			this.m_offset = offset + 1;
			return buffer[offset] > 0;
		}

		// Token: 0x060085CA RID: 34250 RVA: 0x002BC608 File Offset: 0x002BA808
		public virtual byte ReadByte()
		{
			byte[] buffer = this.m_buffer;
			int offset = this.m_offset;
			this.m_offset = offset + 1;
			return buffer[offset];
		}

		// Token: 0x060085CB RID: 34251 RVA: 0x002BC634 File Offset: 0x002BA834
		public virtual short ReadShort()
		{
			byte b = this.ReadByte();
			byte b2 = this.ReadByte();
			return Marshal.ConvertToInt16(b, b2);
		}

		// Token: 0x060085CC RID: 34252 RVA: 0x002BC65C File Offset: 0x002BA85C
		public virtual short ReadShortLowEndian()
		{
			byte b = this.ReadByte();
			byte b2 = this.ReadByte();
			return Marshal.ConvertToInt16(b2, b);
		}

		// Token: 0x060085CD RID: 34253 RVA: 0x002BC684 File Offset: 0x002BA884
		public virtual int ReadInt()
		{
			byte b = this.ReadByte();
			byte b2 = this.ReadByte();
			byte b3 = this.ReadByte();
			byte b4 = this.ReadByte();
			return Marshal.ConvertToInt32(b, b2, b3, b4);
		}

		// Token: 0x060085CE RID: 34254 RVA: 0x002BC6C0 File Offset: 0x002BA8C0
		public virtual uint ReadUInt()
		{
			byte b = this.ReadByte();
			byte b2 = this.ReadByte();
			byte b3 = this.ReadByte();
			byte b4 = this.ReadByte();
			return Marshal.ConvertToUInt32(b, b2, b3, b4);
		}

		// Token: 0x060085CF RID: 34255 RVA: 0x002BC6FC File Offset: 0x002BA8FC
		public virtual long ReadLong()
		{
			int num = this.ReadInt();
			long num2 = this.ReadUnsignedInt();
			int num3 = 1;
			bool flag = num < 0;
			if (flag)
			{
				num3 = -1;
			}
			return (long)((double)num3 * (Math.Abs((double)num * Math.Pow(2.0, 32.0)) + (double)num2));
		}

		// Token: 0x060085D0 RID: 34256 RVA: 0x002BC754 File Offset: 0x002BA954
		public virtual long ReadUnsignedInt()
		{
			return (long)this.ReadInt() & (long)((ulong)(-1));
		}

		// Token: 0x060085D1 RID: 34257 RVA: 0x002BC770 File Offset: 0x002BA970
		public virtual float ReadFloat()
		{
			byte[] array = new byte[4];
			for (int i = 0; i < array.Length; i++)
			{
				array[i] = this.ReadByte();
			}
			return BitConverter.ToSingle(array, 0);
		}

		// Token: 0x060085D2 RID: 34258 RVA: 0x002BC7B0 File Offset: 0x002BA9B0
		public virtual double ReadDouble()
		{
			byte[] array = new byte[8];
			for (int i = 0; i < array.Length; i++)
			{
				array[i] = this.ReadByte();
			}
			return BitConverter.ToDouble(array, 0);
		}

		// Token: 0x060085D3 RID: 34259 RVA: 0x002BC7F0 File Offset: 0x002BA9F0
		public virtual string ReadString()
		{
			short num = this.ReadShort();
			string @string = Encoding.UTF8.GetString(this.m_buffer, this.m_offset, (int)num);
			this.m_offset += (int)num;
			return @string.Replace("\0", "");
		}

		// Token: 0x060085D4 RID: 34260 RVA: 0x002BC840 File Offset: 0x002BAA40
		public virtual byte[] ReadBytes(int maxLen)
		{
			byte[] array = new byte[maxLen];
			Array.Copy(this.m_buffer, this.m_offset, array, 0, maxLen);
			this.m_offset += maxLen;
			return array;
		}

		// Token: 0x060085D5 RID: 34261 RVA: 0x002BC880 File Offset: 0x002BAA80
		public virtual byte[] ReadBytes()
		{
			return this.ReadBytes(this.m_length - this.m_offset);
		}

		// Token: 0x060085D6 RID: 34262 RVA: 0x002BC8A8 File Offset: 0x002BAAA8
		public DateTime ReadDateTime()
		{
			return new DateTime((int)this.ReadShort(), (int)this.ReadByte(), (int)this.ReadByte(), (int)this.ReadByte(), (int)this.ReadByte(), (int)this.ReadByte());
		}

		// Token: 0x060085D7 RID: 34263 RVA: 0x002BC8E4 File Offset: 0x002BAAE4
		public virtual int CopyTo(byte[] dst, int dstOffset, int offset)
		{
			int num = ((this.m_length - offset < dst.Length - dstOffset) ? (this.m_length - offset) : (dst.Length - dstOffset));
			bool flag = num > 0;
			if (flag)
			{
				global::System.Buffer.BlockCopy(this.m_buffer, offset, dst, dstOffset, num);
			}
			return num;
		}

		// Token: 0x060085D8 RID: 34264 RVA: 0x002BC930 File Offset: 0x002BAB30
		public virtual int CopyTo(byte[] dst, int dstOffset, int offset, byte[] key, ref byte subkey)
		{
			int num = ((this.m_length - offset < dst.Length - dstOffset) ? (this.m_length - offset) : (dst.Length - dstOffset));
			bool flag = num > 0;
			if (flag)
			{
				byte[] array = new byte[num];
				for (int i = 0; i < num; i++)
				{
					bool flag2 = i + offset > 0;
					if (flag2)
					{
						bool flag3 = i > 0;
						if (flag3)
						{
							subkey = array[i - 1];
						}
						key[(i + offset) % 8] = (byte)((int)(key[(i + offset) % 8] + subkey) ^ (i + offset));
						subkey = (this.m_buffer[i + offset] ^ key[(i + offset) % 8]) + subkey;
						array[i] = subkey;
					}
					else
					{
						array[0] = this.m_buffer[0] ^ key[0];
					}
				}
				global::System.Buffer.BlockCopy(array, 0, dst, dstOffset, num);
			}
			return num;
		}

		// Token: 0x060085D9 RID: 34265 RVA: 0x002BCA0C File Offset: 0x002BAC0C
		public virtual int CopyFrom(byte[] src, int srcOffset, int offset, int count)
		{
			bool flag = count < this.m_buffer.Length && count - srcOffset < src.Length;
			int num;
			if (flag)
			{
				global::System.Buffer.BlockCopy(src, srcOffset, this.m_buffer, offset, count);
				num = count;
			}
			else
			{
				num = -1;
			}
			return num;
		}

		// Token: 0x060085DA RID: 34266 RVA: 0x002BCA54 File Offset: 0x002BAC54
		public virtual int[] CopyFrom(byte[] src, int srcOffset, int offset, int count, byte[] key)
		{
			int[] array = new int[count];
			for (int i = 0; i < count; i++)
			{
				this.m_buffer[i] = src[i];
			}
			bool flag = count <= this.m_buffer.Length && count - srcOffset <= src.Length;
			if (flag)
			{
				this.m_buffer[0] = src[srcOffset] ^ key[0];
				for (int j = 1; j < count; j++)
				{
					key[j % 8] = (byte)((int)(key[j % 8] + src[srcOffset + j - 1]) ^ j);
					this.m_buffer[j] = (src[srcOffset + j] - src[srcOffset + j - 1]) ^ key[j % 8];
				}
			}
			return array;
		}

		// Token: 0x060085DB RID: 34267 RVA: 0x002BCB18 File Offset: 0x002BAD18
		public virtual void WriteBoolean(bool val)
		{
			bool flag = this.m_offset == this.m_buffer.Length;
			if (flag)
			{
				byte[] buffer = this.m_buffer;
				this.m_buffer = new byte[this.m_buffer.Length * 2];
				Array.Copy(buffer, this.m_buffer, buffer.Length);
			}
			byte[] buffer2 = this.m_buffer;
			int offset = this.m_offset;
			this.m_offset = offset + 1;
			buffer2[offset] = (val ? 1 : 0);
			this.m_length = ((this.m_offset > this.m_length) ? this.m_offset : this.m_length);
		}

		// Token: 0x060085DC RID: 34268 RVA: 0x002BCBAC File Offset: 0x002BADAC
		public virtual void WriteByte(byte val)
		{
			bool flag = this.m_offset == this.m_buffer.Length;
			if (flag)
			{
				byte[] buffer = this.m_buffer;
				this.m_buffer = new byte[this.m_buffer.Length * 2];
				Array.Copy(buffer, this.m_buffer, buffer.Length);
			}
			byte[] buffer2 = this.m_buffer;
			int offset = this.m_offset;
			this.m_offset = offset + 1;
			buffer2[offset] = val;
			this.m_length = ((this.m_offset > this.m_length) ? this.m_offset : this.m_length);
		}

		// Token: 0x060085DD RID: 34269 RVA: 0x00035B52 File Offset: 0x00033D52
		public virtual void Write(byte[] src)
		{
			this.Write(src, 0, src.Length);
		}

		// Token: 0x060085DE RID: 34270 RVA: 0x002BCC38 File Offset: 0x002BAE38
		public virtual void Write(byte[] src, int offset, int len)
		{
			bool flag = this.m_offset + len >= this.m_buffer.Length;
			if (flag)
			{
				byte[] buffer = this.m_buffer;
				this.m_buffer = new byte[this.m_buffer.Length * 2];
				Array.Copy(buffer, this.m_buffer, buffer.Length);
				this.Write(src, offset, len);
			}
			else
			{
				Array.Copy(src, offset, this.m_buffer, this.m_offset, len);
				this.m_offset += len;
				this.m_length = ((this.m_offset > this.m_length) ? this.m_offset : this.m_length);
			}
		}

		// Token: 0x060085DF RID: 34271 RVA: 0x00035B61 File Offset: 0x00033D61
		public virtual void WriteShort(short val)
		{
			this.WriteByte((byte)(val >> 8));
			this.WriteByte((byte)(val & 255));
		}

		// Token: 0x060085E0 RID: 34272 RVA: 0x00035B7E File Offset: 0x00033D7E
		public virtual void WriteShortLowEndian(short val)
		{
			this.WriteByte((byte)(val & 255));
			this.WriteByte((byte)(val >> 8));
		}

		// Token: 0x060085E1 RID: 34273 RVA: 0x002BCCE0 File Offset: 0x002BAEE0
		public virtual void WriteInt(int val)
		{
			this.WriteByte((byte)(val >> 24));
			this.WriteByte((byte)((val >> 16) & 255));
			this.WriteByte((byte)((val & 65535) >> 8));
			this.WriteByte((byte)(val & 65535 & 255));
		}

		// Token: 0x060085E2 RID: 34274 RVA: 0x002BCD34 File Offset: 0x002BAF34
		public virtual void WriteUInt(uint val)
		{
			this.WriteByte((byte)(val >> 24));
			this.WriteByte((byte)((val >> 16) & 255U));
			this.WriteByte((byte)((val & 65535U) >> 8));
			this.WriteByte((byte)(val & 65535U & 255U));
		}

		// Token: 0x060085E3 RID: 34275 RVA: 0x002BCD88 File Offset: 0x002BAF88
		public virtual void WriteLong(long val)
		{
			uint num = (uint)val;
			int num2 = (int)Math.Floor((double)val / Math.Pow(2.0, 32.0));
			this.WriteInt(num2);
			this.WriteInt((int)num);
		}

		// Token: 0x060085E4 RID: 34276 RVA: 0x002BCDCC File Offset: 0x002BAFCC
		public virtual void WriteFloat(float val)
		{
			byte[] bytes = BitConverter.GetBytes(val);
			this.Write(bytes);
		}

		// Token: 0x060085E5 RID: 34277 RVA: 0x002BCDEC File Offset: 0x002BAFEC
		public virtual void WriteDouble(double val)
		{
			byte[] bytes = BitConverter.GetBytes(val);
			this.Write(bytes);
		}

		// Token: 0x060085E6 RID: 34278 RVA: 0x002BCE0C File Offset: 0x002BB00C
		public virtual void Fill(byte val, int num)
		{
			for (int i = 0; i < num; i++)
			{
				this.WriteByte(val);
			}
		}

		// Token: 0x060085E7 RID: 34279 RVA: 0x002BCE34 File Offset: 0x002BB034
		public virtual void WriteString(string str)
		{
			bool flag = !string.IsNullOrEmpty(str);
			if (flag)
			{
				byte[] bytes = Encoding.UTF8.GetBytes(str);
				this.WriteShort((short)(bytes.Length + 1));
				this.Write(bytes, 0, bytes.Length);
				this.WriteByte(0);
			}
			else
			{
				this.WriteShort(1);
				this.WriteByte(0);
			}
		}

		// Token: 0x060085E8 RID: 34280 RVA: 0x002BCE94 File Offset: 0x002BB094
		public virtual void WriteString(string str, int maxlen)
		{
			byte[] bytes = Encoding.UTF8.GetBytes(str);
			int num = ((bytes.Length < maxlen) ? bytes.Length : maxlen);
			this.WriteShort((short)num);
			this.Write(bytes, 0, num);
		}

		// Token: 0x060085E9 RID: 34281 RVA: 0x002BCED0 File Offset: 0x002BB0D0
		public void WriteDateTime(DateTime date)
		{
			this.WriteShort((short)date.Year);
			this.WriteByte((byte)date.Month);
			this.WriteByte((byte)date.Day);
			this.WriteByte((byte)date.Hour);
			this.WriteByte((byte)date.Minute);
			this.WriteByte((byte)date.Second);
		}

		// Token: 0x04005378 RID: 21368
		protected byte[] m_buffer;

		// Token: 0x04005379 RID: 21369
		protected int m_length;

		// Token: 0x0400537A RID: 21370
		protected int m_offset;

		// Token: 0x0400537B RID: 21371
		public volatile bool IsSended = true;

		// Token: 0x0400537C RID: 21372
		public volatile int MSended = 0;

		// Token: 0x0400537D RID: 21373
		public volatile int PacketNum = 0;
	}
}
