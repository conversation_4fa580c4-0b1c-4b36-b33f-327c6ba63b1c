﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D09 RID: 3337
	public class PetClonePostureEquip : AbstractPetEffect
	{
		// Token: 0x0600788A RID: 30858 RVA: 0x0002CB6C File Offset: 0x0002AD6C
		public PetClonePostureEquip(int count, string elementID)
			: base(ePetEffectType.PetClonePostureEquip, elementID)
		{
			this.m_count = count;
		}

		// Token: 0x0600788B RID: 30859 RVA: 0x0028698C File Offset: 0x00284B8C
		public override bool Start(Living living)
		{
			PetClonePostureEquip petClonePostureEquip = living.PetEffectList.GetOfType(ePetEffectType.PetClonePostureEquip) as PetClonePostureEquip;
			bool flag = petClonePostureEquip != null;
			bool flag2;
			if (flag)
			{
				petClonePostureEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600788C RID: 30860 RVA: 0x0002CB83 File Offset: 0x0002AD83
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x0600788D RID: 30861 RVA: 0x0002CB99 File Offset: 0x0002AD99
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x0600788E RID: 30862 RVA: 0x002869D4 File Offset: 0x00284BD4
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.removePhysicObject(living as Player);
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
		}

		// Token: 0x040046A9 RID: 18089
		private int m_count;
	}
}
