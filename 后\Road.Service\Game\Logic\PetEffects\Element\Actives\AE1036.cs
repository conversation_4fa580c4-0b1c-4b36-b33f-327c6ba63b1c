﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D9F RID: 3487
	public class AE1036 : BasePetEffect
	{
		// Token: 0x06007BB9 RID: 31673 RVA: 0x002943CC File Offset: 0x002925CC
		public AE1036(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1036, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BBA RID: 31674 RVA: 0x0029444C File Offset: 0x0029264C
		public override bool Start(Living living)
		{
			AE1036 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1036) as AE1036;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BBB RID: 31675 RVA: 0x0002F47F File Offset: 0x0002D67F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007BBC RID: 31676 RVA: 0x0002F4A8 File Offset: 0x0002D6A8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007BBD RID: 31677 RVA: 0x002944AC File Offset: 0x002926AC
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 120;
				player.CurrentDamagePlus += (float)(this.m_added / 100);
			}
		}

		// Token: 0x06007BBE RID: 31678 RVA: 0x002944F4 File Offset: 0x002926F4
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_added / 100);
				this.m_added = 0;
			}
		}

		// Token: 0x04004993 RID: 18835
		private int m_type = 0;

		// Token: 0x04004994 RID: 18836
		private int m_count = 0;

		// Token: 0x04004995 RID: 18837
		private int m_probability = 0;

		// Token: 0x04004996 RID: 18838
		private int m_delay = 0;

		// Token: 0x04004997 RID: 18839
		private int m_coldDown = 0;

		// Token: 0x04004998 RID: 18840
		private int m_currentId;

		// Token: 0x04004999 RID: 18841
		private int m_added = 0;
	}
}
