﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E63 RID: 3683
	public class CE1093 : BasePetEffect
	{
		// Token: 0x06007FDE RID: 32734 RVA: 0x002A6F14 File Offset: 0x002A5114
		public CE1093(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1093, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FDF RID: 32735 RVA: 0x002A6F94 File Offset: 0x002A5194
		public override bool Start(Living living)
		{
			CE1093 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1093) as CE1093;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FE0 RID: 32736 RVA: 0x002A6FF4 File Offset: 0x002A51F4
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				player.Attack += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007FE1 RID: 32737 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FE2 RID: 32738 RVA: 0x002A7058 File Offset: 0x002A5258
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007FE3 RID: 32739 RVA: 0x002A708C File Offset: 0x002A528C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Attack -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004EF0 RID: 20208
		private int m_type = 0;

		// Token: 0x04004EF1 RID: 20209
		private int m_count = 0;

		// Token: 0x04004EF2 RID: 20210
		private int m_probability = 0;

		// Token: 0x04004EF3 RID: 20211
		private int m_delay = 0;

		// Token: 0x04004EF4 RID: 20212
		private int m_coldDown = 0;

		// Token: 0x04004EF5 RID: 20213
		private int m_currentId;

		// Token: 0x04004EF6 RID: 20214
		private int m_added = 0;
	}
}
