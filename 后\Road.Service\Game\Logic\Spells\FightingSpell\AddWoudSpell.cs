﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CC1 RID: 3265
	[SpellAttibute(13)]
	public class AddWoudSpell : ISpellHandler
	{
		// Token: 0x06007504 RID: 29956 RVA: 0x0026E5EC File Offset: 0x0026C7EC
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.CurrentDamagePlus += (float)item.Property2 / 100f;
			}
			else
			{
				bool flag = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
				if (flag)
				{
					game.CurrentLiving.CurrentDamagePlus += (float)item.Property2 / 100f;
				}
			}
		}
	}
}
