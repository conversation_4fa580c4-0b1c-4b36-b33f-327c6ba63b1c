﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E54 RID: 3668
	public class CE1041 : BasePetEffect
	{
		// Token: 0x06007F86 RID: 32646 RVA: 0x002A56C8 File Offset: 0x002A38C8
		public CE1041(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1041, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F87 RID: 32647 RVA: 0x002A5748 File Offset: 0x002A3948
		public override bool Start(Living living)
		{
			CE1041 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1041) as CE1041;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F88 RID: 32648 RVA: 0x002A57A8 File Offset: 0x002A39A8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				player.Lucky += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F89 RID: 32649 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F8A RID: 32650 RVA: 0x002A580C File Offset: 0x002A3A0C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F8B RID: 32651 RVA: 0x002A5840 File Offset: 0x002A3A40
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Lucky -= (double)this.m_added;
			this.m_added = 0;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E87 RID: 20103
		private int m_type = 0;

		// Token: 0x04004E88 RID: 20104
		private int m_count = 0;

		// Token: 0x04004E89 RID: 20105
		private int m_probability = 0;

		// Token: 0x04004E8A RID: 20106
		private int m_delay = 0;

		// Token: 0x04004E8B RID: 20107
		private int m_coldDown = 0;

		// Token: 0x04004E8C RID: 20108
		private int m_currentId;

		// Token: 0x04004E8D RID: 20109
		private int m_added = 0;
	}
}
