﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D41 RID: 3393
	public class PetReduceEnemyBlood : BasePetEffect
	{
		// Token: 0x060079BA RID: 31162 RVA: 0x0028C464 File Offset: 0x0028A664
		public PetReduceEnemyBlood(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetReduceEnemyBlood, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_skillId = skillId;
			if (elementID == "1717" || elementID == "1718" || elementID == "1719" || elementID == "1801" || elementID == "1802" || elementID == "1803")
			{
				this.distanceRequired = 300;
			}
		}

		// Token: 0x060079BB RID: 31163 RVA: 0x0028C50C File Offset: 0x0028A70C
		public override bool Start(Living living)
		{
			PetReduceEnemyBlood petReduceEnemyBlood = living.PetEffectList.GetOfType(ePetEffectType.PetReduceEnemyBlood) as PetReduceEnemyBlood;
			bool flag = petReduceEnemyBlood != null;
			bool flag2;
			if (flag)
			{
				petReduceEnemyBlood.m_probability = ((this.m_probability > petReduceEnemyBlood.m_probability) ? this.m_probability : petReduceEnemyBlood.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079BC RID: 31164 RVA: 0x0002DE04 File Offset: 0x0002C004
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079BD RID: 31165 RVA: 0x0002DE1A File Offset: 0x0002C01A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079BE RID: 31166 RVA: 0x0028C56C File Offset: 0x0028A76C
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill != this.m_skillId;
			if (!flag)
			{
				foreach (Player player in living.Game.GetAllEnemyPlayers(living))
				{
					bool flag2 = this.distanceRequired == -1 || Math.Abs(living.X - player.X) <= this.distanceRequired;
					if (flag2)
					{
						for (int i = 0; i < this.m_count; i++)
						{
							player.Game.sendShowPicSkil(player, base.Info, true);
						}
						player.AddPetEffect(new PetReduceBloodEquip(this.m_count, base.ElementInfo.ID.ToString(), living), 0);
						player.Game.SendPlayerPicture(player, 2, true);
					}
				}
			}
		}

		// Token: 0x04004751 RID: 18257
		private int m_count;

		// Token: 0x04004752 RID: 18258
		private int m_probability;

		// Token: 0x04004753 RID: 18259
		private int m_skillId;

		// Token: 0x04004754 RID: 18260
		private int distanceRequired = -1;
	}
}
