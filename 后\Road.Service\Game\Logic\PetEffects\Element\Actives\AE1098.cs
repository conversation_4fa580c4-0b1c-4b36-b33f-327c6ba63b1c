﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DC6 RID: 3526
	public class AE1098 : BasePetEffect
	{
		// Token: 0x06007C81 RID: 31873 RVA: 0x00297D74 File Offset: 0x00295F74
		public AE1098(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1098, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C82 RID: 31874 RVA: 0x00297DF4 File Offset: 0x00295FF4
		public override bool Start(Living living)
		{
			AE1098 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1098) as AE1098;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C83 RID: 31875 RVA: 0x0002FB34 File Offset: 0x0002DD34
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C84 RID: 31876 RVA: 0x0002FB4A File Offset: 0x0002DD4A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C85 RID: 31877 RVA: 0x00297E54 File Offset: 0x00296054
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.AddPetEffect(new CE1098(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004AA4 RID: 19108
		private int m_type = 0;

		// Token: 0x04004AA5 RID: 19109
		private int m_count = 0;

		// Token: 0x04004AA6 RID: 19110
		private int m_probability = 0;

		// Token: 0x04004AA7 RID: 19111
		private int m_delay = 0;

		// Token: 0x04004AA8 RID: 19112
		private int m_coldDown = 0;

		// Token: 0x04004AA9 RID: 19113
		private int m_currentId;

		// Token: 0x04004AAA RID: 19114
		private int m_added = 0;
	}
}
