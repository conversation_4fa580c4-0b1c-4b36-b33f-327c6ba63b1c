﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D82 RID: 3458
	public class PE1422 : BasePetEffect
	{
		// Token: 0x06007B1F RID: 31519 RVA: 0x00291B3C File Offset: 0x0028FD3C
		public PE1422(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1422, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B20 RID: 31520 RVA: 0x00291BBC File Offset: 0x0028FDBC
		public override bool Start(Living living)
		{
			PE1422 pe = living.PetEffectList.GetOfType(ePetEffectType.AE1422) as PE1422;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B21 RID: 31521 RVA: 0x0002ED48 File Offset: 0x0002CF48
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.player_beginNextTurn;
		}

		// Token: 0x06007B22 RID: 31522 RVA: 0x0002ED5E File Offset: 0x0002CF5E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.player_beginNextTurn;
		}

		// Token: 0x06007B23 RID: 31523 RVA: 0x00291C1C File Offset: 0x0028FE1C
		public void player_beginNextTurn(Living living)
		{
			bool flag = (living as Player).ReloadPointDietYeu != 0;
			if (!flag)
			{
				List<Player> allTeamPlayers = living.Game.GetAllTeamPlayers(living);
				foreach (Player player in allTeamPlayers)
				{
					bool flag2 = player.Attack != 0.0;
					if (flag2)
					{
						this.m_added = (int)(player.Attack * 5.0 / 100.0);
						player.Attack += (double)this.m_added;
						player.ReloadPointDietYeu += this.m_added;
						player.Game.SendPetBuff(player, base.ElementInfo, true);
					}
				}
			}
		}

		// Token: 0x040048CE RID: 18638
		private int m_type = 0;

		// Token: 0x040048CF RID: 18639
		private int m_count = 0;

		// Token: 0x040048D0 RID: 18640
		private int m_probability = 0;

		// Token: 0x040048D1 RID: 18641
		private int m_delay = 0;

		// Token: 0x040048D2 RID: 18642
		private int m_coldDown = 0;

		// Token: 0x040048D3 RID: 18643
		private int m_currentId;

		// Token: 0x040048D4 RID: 18644
		private int m_added = 0;
	}
}
