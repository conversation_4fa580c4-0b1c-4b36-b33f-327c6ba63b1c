﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DE4 RID: 3556
	public class AE1173 : BasePetEffect
	{
		// Token: 0x06007D23 RID: 32035 RVA: 0x0029A86C File Offset: 0x00298A6C
		public AE1173(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1173, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D24 RID: 32036 RVA: 0x0029A8EC File Offset: 0x00298AEC
		public override bool Start(Living living)
		{
			AE1173 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1173) as AE1173;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D25 RID: 32037 RVA: 0x0003025E File Offset: 0x0002E45E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D26 RID: 32038 RVA: 0x00030274 File Offset: 0x0002E474
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D27 RID: 32039 RVA: 0x0029A94C File Offset: 0x00298B4C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1173(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004B74 RID: 19316
		private int m_type = 0;

		// Token: 0x04004B75 RID: 19317
		private int m_count = 0;

		// Token: 0x04004B76 RID: 19318
		private int m_probability = 0;

		// Token: 0x04004B77 RID: 19319
		private int m_delay = 0;

		// Token: 0x04004B78 RID: 19320
		private int m_coldDown = 0;

		// Token: 0x04004B79 RID: 19321
		private int m_currentId;

		// Token: 0x04004B7A RID: 19322
		private int m_added = 0;
	}
}
