﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D0E RID: 3342
	public class PetKungFuSpecializationEquip : BasePetEffect
	{
		// Token: 0x060078A7 RID: 30887 RVA: 0x0002CD61 File Offset: 0x0002AF61
		public PetKungFuSpecializationEquip(int count, string elementID)
			: base(ePetEffectType.PetKungFuSpecializationEquip, elementID)
		{
			this.m_count = count;
		}

		// Token: 0x060078A8 RID: 30888 RVA: 0x002870C0 File Offset: 0x002852C0
		public override bool Start(Living living)
		{
			PetKungFuSpecializationEquip petKungFuSpecializationEquip = living.PetEffectList.GetOfType(ePetEffectType.PetKungFuSpecializationEquip) as PetKungFuSpecializationEquip;
			bool flag = petKungFuSpecializationEquip != null;
			bool flag2;
			if (flag)
			{
				petKungFuSpecializationEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078A9 RID: 30889 RVA: 0x0002CD7F File Offset: 0x0002AF7F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x060078AA RID: 30890 RVA: 0x0002CD95 File Offset: 0x0002AF95
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x060078AB RID: 30891 RVA: 0x00287108 File Offset: 0x00285308
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x040046B4 RID: 18100
		private int m_count = 0;
	}
}
