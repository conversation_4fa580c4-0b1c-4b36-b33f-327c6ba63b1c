﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DCC RID: 3532
	public class AE1115 : BasePetEffect
	{
		// Token: 0x06007CA1 RID: 31905 RVA: 0x002985FC File Offset: 0x002967FC
		public AE1115(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1115, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CA2 RID: 31906 RVA: 0x00298678 File Offset: 0x00296878
		public override bool Start(Living living)
		{
			AE1115 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1115) as AE1115;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CA3 RID: 31907 RVA: 0x0002FC97 File Offset: 0x0002DE97
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007CA4 RID: 31908 RVA: 0x0002FCAD File Offset: 0x0002DEAD
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007CA5 RID: 31909 RVA: 0x002986D4 File Offset: 0x002968D4
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && player.Game is PVPGame;
			if (flag)
			{
				player.AddPetEffect(new CE1115(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004ACE RID: 19150
		private int m_type = 0;

		// Token: 0x04004ACF RID: 19151
		private int m_count = 0;

		// Token: 0x04004AD0 RID: 19152
		private int m_probability = 0;

		// Token: 0x04004AD1 RID: 19153
		private int m_delay = 0;

		// Token: 0x04004AD2 RID: 19154
		private int m_coldDown = 0;

		// Token: 0x04004AD3 RID: 19155
		private int m_currentId;

		// Token: 0x04004AD4 RID: 19156
		private int m_added = 0;
	}
}
