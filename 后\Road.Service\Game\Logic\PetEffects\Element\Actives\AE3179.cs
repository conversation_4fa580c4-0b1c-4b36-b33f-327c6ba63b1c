﻿using System;
using System.Collections.Generic;
using Game.Logic.Effects;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E3A RID: 3642
	public class AE3179 : BasePetEffect
	{
		// Token: 0x06007EEB RID: 32491 RVA: 0x002A2F04 File Offset: 0x002A1104
		public AE3179(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE3179, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007EEC RID: 32492 RVA: 0x002A2F80 File Offset: 0x002A1180
		public override bool Start(Living living)
		{
			AE3179 ae = living.PetEffectList.GetOfType(ePetEffectType.AE3179) as AE3179;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007EED RID: 32493 RVA: 0x00031484 File Offset: 0x0002F684
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EEE RID: 32494 RVA: 0x0003149A File Offset: 0x0002F69A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EEF RID: 32495 RVA: 0x002A2FDC File Offset: 0x002A11DC
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					int num = player2.MaxBlood * 15 / 100;
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddEffect(new AddGuardEquipEffect(num, 3, false), 0);
					player2.AddPetEffect(new CE3179(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004DD1 RID: 19921
		private int m_type = 0;

		// Token: 0x04004DD2 RID: 19922
		private int m_count = 0;

		// Token: 0x04004DD3 RID: 19923
		private int m_probability = 0;

		// Token: 0x04004DD4 RID: 19924
		private int m_delay = 0;

		// Token: 0x04004DD5 RID: 19925
		private int m_coldDown = 0;

		// Token: 0x04004DD6 RID: 19926
		private int m_currentId;

		// Token: 0x04004DD7 RID: 19927
		private int m_added = 0;
	}
}
