﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E46 RID: 3654
	public class CE1020 : BasePetEffect
	{
		// Token: 0x06007F2F RID: 32559 RVA: 0x002A40C0 File Offset: 0x002A22C0
		public CE1020(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1020, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F30 RID: 32560 RVA: 0x002A4140 File Offset: 0x002A2340
		public override bool Start(Living living)
		{
			CE1020 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1020) as CE1020;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F31 RID: 32561 RVA: 0x002A41A0 File Offset: 0x002A23A0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 500;
				player.Defence += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F32 RID: 32562 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F33 RID: 32563 RVA: 0x002A4204 File Offset: 0x002A2404
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F34 RID: 32564 RVA: 0x002A4238 File Offset: 0x002A2438
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Defence -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E25 RID: 20005
		private int m_type = 0;

		// Token: 0x04004E26 RID: 20006
		private int m_count = 0;

		// Token: 0x04004E27 RID: 20007
		private int m_probability = 0;

		// Token: 0x04004E28 RID: 20008
		private int m_delay = 0;

		// Token: 0x04004E29 RID: 20009
		private int m_coldDown = 0;

		// Token: 0x04004E2A RID: 20010
		private int m_currentId;

		// Token: 0x04004E2B RID: 20011
		private int m_added = 0;
	}
}
