﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E76 RID: 3702
	public class CE1153 : BasePetEffect
	{
		// Token: 0x06008055 RID: 32853 RVA: 0x002A8A6C File Offset: 0x002A6C6C
		public CE1153(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1153, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008056 RID: 32854 RVA: 0x002A8AEC File Offset: 0x002A6CEC
		public override bool Start(Living living)
		{
			CE1153 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1153) as CE1153;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008057 RID: 32855 RVA: 0x002A8B4C File Offset: 0x002A6D4C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				this.m_added = 50;
				player.BaseDamage += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008058 RID: 32856 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008059 RID: 32857 RVA: 0x002A8BC0 File Offset: 0x002A6DC0
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600805A RID: 32858 RVA: 0x002A8BF4 File Offset: 0x002A6DF4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BaseDamage -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F73 RID: 20339
		private int m_type = 0;

		// Token: 0x04004F74 RID: 20340
		private int m_count = 0;

		// Token: 0x04004F75 RID: 20341
		private int m_probability = 0;

		// Token: 0x04004F76 RID: 20342
		private int m_delay = 0;

		// Token: 0x04004F77 RID: 20343
		private int m_coldDown = 0;

		// Token: 0x04004F78 RID: 20344
		private int m_currentId;

		// Token: 0x04004F79 RID: 20345
		private int m_added = 0;
	}
}
