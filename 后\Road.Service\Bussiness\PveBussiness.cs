﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using SqlDataProvider.Data;

namespace Bussiness
{
	// Token: 0x02000FB6 RID: 4022
	public class PveBussiness : BaseBussiness
	{
		// Token: 0x06008A2C RID: 35372 RVA: 0x002F5410 File Offset: 0x002F3610
		public PveInfo[] GetAllPveInfos()
		{
			List<PveInfo> list = new List<PveInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_PveInfos_All");
				while (sqlDataReader.Read())
				{
					list.Add(new PveInfo
					{
						ID = (int)sqlDataReader["Id"],
						Name = ((sqlDataReader["Name"] == null) ? "" : sqlDataReader["Name"].ToString()),
						Type = (int)sqlDataReader["Type"],
						Ordering = (int)sqlDataReader["Ordering"],
						LevelLimits = (int)sqlDataReader["LevelLimits"],
						SimpleTemplateIds = ((sqlDataReader["SimpleTemplateIds"] == null) ? "" : sqlDataReader["SimpleTemplateIds"].ToString()),
						NormalTemplateIds = ((sqlDataReader["NormalTemplateIds"] == null) ? "" : sqlDataReader["NormalTemplateIds"].ToString()),
						HardTemplateIds = ((sqlDataReader["HardTemplateIds"] == null) ? "" : sqlDataReader["HardTemplateIds"].ToString()),
						TerrorTemplateIds = ((sqlDataReader["TerrorTemplateIds"] == null) ? "" : sqlDataReader["TerrorTemplateIds"].ToString()),
						Pic = ((sqlDataReader["Pic"] == null) ? "" : sqlDataReader["Pic"].ToString()),
						Description = ((sqlDataReader["Description"] == null) ? "" : sqlDataReader["Description"].ToString()),
						SimpleGameScript = (sqlDataReader["SimpleGameScript"] as string),
						NormalGameScript = (sqlDataReader["NormalGameScript"] as string),
						HardGameScript = (sqlDataReader["HardGameScript"] as string),
						TerrorGameScript = (sqlDataReader["TerrorGameScript"] as string),
						AdviceTips = (sqlDataReader["AdviceTips"] as string),
						BossFightNeedMoney = (sqlDataReader["BossFightNeedMoney"] as string)
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllPve", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}
	}
}
