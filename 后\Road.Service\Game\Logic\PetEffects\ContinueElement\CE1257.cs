﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EB6 RID: 3766
	public class CE1257 : BasePetEffect
	{
		// Token: 0x060081F4 RID: 33268 RVA: 0x002AECB4 File Offset: 0x002ACEB4
		public CE1257(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1257, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081F5 RID: 33269 RVA: 0x002AED34 File Offset: 0x002ACF34
		public override bool Start(Living living)
		{
			CE1257 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1257) as CE1257;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081F6 RID: 33270 RVA: 0x002AED94 File Offset: 0x002ACF94
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.BaseGuard += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081F7 RID: 33271 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081F8 RID: 33272 RVA: 0x002AEDF4 File Offset: 0x002ACFF4
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060081F9 RID: 33273 RVA: 0x002AEE28 File Offset: 0x002AD028
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BaseGuard -= (double)this.m_added;
			this.m_added = 0;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005137 RID: 20791
		private int m_type = 0;

		// Token: 0x04005138 RID: 20792
		private int m_count = 0;

		// Token: 0x04005139 RID: 20793
		private int m_probability = 0;

		// Token: 0x0400513A RID: 20794
		private int m_delay = 0;

		// Token: 0x0400513B RID: 20795
		private int m_coldDown = 0;

		// Token: 0x0400513C RID: 20796
		private int m_currentId;

		// Token: 0x0400513D RID: 20797
		private int m_added = 0;
	}
}
