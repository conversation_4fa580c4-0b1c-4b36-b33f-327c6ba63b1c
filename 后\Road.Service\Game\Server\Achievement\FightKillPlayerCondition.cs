﻿using System;
using Game.Logic;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C64 RID: 3172
	public class FightKillPlayerCondition : BaseCondition
	{
		// Token: 0x06007081 RID: 28801 RVA: 0x0002A421 File Offset: 0x00028621
		public FightKillPlayerCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x06007082 RID: 28802 RVA: 0x0002A4C4 File Offset: 0x000286C4
		public override void AddTrigger(GamePlayer player)
		{
			player.AfterKillingLiving += this.player_AfterKillingLiving;
		}

		// Token: 0x06007083 RID: 28803 RVA: 0x002500BC File Offset: 0x0024E2BC
		private void player_AfterKillingLiving(AbstractGame game, int type, int id, bool isLiving, int demage)
		{
			bool flag = type == 1 && !isLiving;
			if (flag)
			{
				int value = base.Value;
				base.Value = value + 1;
			}
		}

		// Token: 0x06007084 RID: 28804 RVA: 0x0002A4DA File Offset: 0x000286DA
		public override void RemoveTrigger(GamePlayer player)
		{
			player.AfterKillingLiving -= this.player_AfterKillingLiving;
		}

		// Token: 0x06007085 RID: 28805 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
