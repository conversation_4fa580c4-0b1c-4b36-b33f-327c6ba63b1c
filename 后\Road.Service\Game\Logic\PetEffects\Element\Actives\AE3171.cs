﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E34 RID: 3636
	public class AE3171 : BasePetEffect
	{
		// Token: 0x06007ECA RID: 32458 RVA: 0x002A2658 File Offset: 0x002A0858
		public AE3171(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE3171, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007ECB RID: 32459 RVA: 0x002A26D4 File Offset: 0x002A08D4
		public override bool Start(Living living)
		{
			AE3171 ae = living.PetEffectList.GetOfType(ePetEffectType.AE3171) as AE3171;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007ECC RID: 32460 RVA: 0x0003130A File Offset: 0x0002F50A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_afterBuffSkillPetByLiving;
			player.AfterKillingLiving += this.player_afterKillingLiving;
		}

		// Token: 0x06007ECD RID: 32461 RVA: 0x00031333 File Offset: 0x0002F533
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_afterBuffSkillPetByLiving;
			player.AfterKillingLiving -= this.player_afterKillingLiving;
		}

		// Token: 0x06007ECE RID: 32462 RVA: 0x002A2730 File Offset: 0x002A0930
		private void player_afterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007ECF RID: 32463 RVA: 0x002A2760 File Offset: 0x002A0960
		private void player_afterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = this.IsTrigger && target is Player;
			if (flag)
			{
				target.AddPetEffect(new CE3171(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				this.IsTrigger = false;
			}
		}

		// Token: 0x04004DA7 RID: 19879
		private int m_type = 0;

		// Token: 0x04004DA8 RID: 19880
		private int m_count = 0;

		// Token: 0x04004DA9 RID: 19881
		private int m_probability = 0;

		// Token: 0x04004DAA RID: 19882
		private int m_delay = 0;

		// Token: 0x04004DAB RID: 19883
		private int m_coldDown = 0;

		// Token: 0x04004DAC RID: 19884
		private int m_currentId;

		// Token: 0x04004DAD RID: 19885
		private int m_added = 0;
	}
}
