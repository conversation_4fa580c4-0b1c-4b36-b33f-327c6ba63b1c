﻿using System;
using Bussiness;
using Game.Base.Packets;
using Game.Server.GameObjects;
using Game.Server.Rooms;
using SqlDataProvider.Data;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C4E RID: 3150
	[ActiveSystemHandleAttbute(34)]
	public class CatchbeastChallenge : IActiveSystemCommandHadler
	{
		// Token: 0x0600701E RID: 28702 RVA: 0x0024D470 File Offset: 0x0024B670
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			bool flag = Player.MainWeapon == null;
			bool flag2;
			if (flag)
			{
				Player.SendMessage(LanguageMgr.GetTranslation("Game.Server.SceneGames.NoEquip", Array.Empty<object>()));
				flag2 = false;
			}
			else
			{
				bool flag3 = Player.Actives.Info.ChallengeNum > 0;
				if (flag3)
				{
					UserActiveInfo info = Player.Actives.Info;
					int challengeNum = info.ChallengeNum;
					info.ChallengeNum = challengeNum - 1;
					RoomMgr.CreateCatchBeastRoom(Player);
				}
				flag2 = true;
			}
			return flag2;
		}
	}
}
