﻿using System;
using System.Diagnostics;
using System.Threading;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic
{
	// Token: 0x02000C81 RID: 3201
	public class AbstractGame
	{
		// Token: 0x17001332 RID: 4914
		// (get) Token: 0x06007141 RID: 28993 RVA: 0x0002A885 File Offset: 0x00028A85
		public int Id
		{
			get
			{
				return this.m_id;
			}
		}

		// Token: 0x17001333 RID: 4915
		// (get) Token: 0x06007142 RID: 28994 RVA: 0x0002A88D File Offset: 0x00028A8D
		public eRoomType RoomType
		{
			get
			{
				return this.m_roomType;
			}
		}

		// Token: 0x17001334 RID: 4916
		// (get) Token: 0x06007143 RID: 28995 RVA: 0x0002A895 File Offset: 0x00028A95
		public eGameType GameType
		{
			get
			{
				return this.m_gameType;
			}
		}

		// Token: 0x17001335 RID: 4917
		// (get) Token: 0x06007144 RID: 28996 RVA: 0x0002A89D File Offset: 0x00028A9D
		public int TimeType
		{
			get
			{
				return this.m_timeType;
			}
		}

		// Token: 0x140000AF RID: 175
		// (add) Token: 0x06007145 RID: 28997 RVA: 0x0025B39C File Offset: 0x0025959C
		// (remove) Token: 0x06007146 RID: 28998 RVA: 0x0025B3D4 File Offset: 0x002595D4
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event GameEventHandle GameStarted;

		// Token: 0x140000B0 RID: 176
		// (add) Token: 0x06007147 RID: 28999 RVA: 0x0025B40C File Offset: 0x0025960C
		// (remove) Token: 0x06007148 RID: 29000 RVA: 0x0025B444 File Offset: 0x00259644
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event GameEventHandle GameStopped;

		// Token: 0x06007149 RID: 29001 RVA: 0x0025B47C File Offset: 0x0025967C
		public AbstractGame(int id, eRoomType roomType, eGameType gameType, int timeType)
		{
			this.m_id = id;
			this.m_roomType = roomType;
			this.m_gameType = gameType;
			this.m_timeType = timeType;
			eRoomType roomType2 = this.m_roomType;
			eRoomType eRoomType = roomType2;
			if (eRoomType != eRoomType.Match)
			{
				if (eRoomType == eRoomType.Freedom)
				{
					this.m_mapType = eMapType.Normal;
					return;
				}
				if (eRoomType - eRoomType.Score > 1)
				{
					this.m_mapType = eMapType.Normal;
					return;
				}
			}
			this.m_mapType = eMapType.PairUp;
		}

		// Token: 0x0600714A RID: 29002 RVA: 0x0002A8A5 File Offset: 0x00028AA5
		public virtual void Start()
		{
			this.OnGameStarted();
		}

		// Token: 0x0600714B RID: 29003 RVA: 0x0002A8AF File Offset: 0x00028AAF
		public virtual void Stop()
		{
			this.OnGameStopped();
		}

		// Token: 0x0600714C RID: 29004 RVA: 0x00068C5C File Offset: 0x00066E5C
		public virtual bool CanAddPlayer()
		{
			return false;
		}

		// Token: 0x0600714D RID: 29005 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void Pause(int time)
		{
		}

		// Token: 0x0600714E RID: 29006 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void Resume()
		{
		}

		// Token: 0x0600714F RID: 29007 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void ProcessData(GSPacketIn pkg)
		{
		}

		// Token: 0x06007150 RID: 29008 RVA: 0x0025B4F0 File Offset: 0x002596F0
		public virtual Player AddPlayer(IGamePlayer player)
		{
			return null;
		}

		// Token: 0x06007151 RID: 29009 RVA: 0x0025B4F0 File Offset: 0x002596F0
		public virtual Player RemovePlayer(IGamePlayer player, bool IsKick)
		{
			return null;
		}

		// Token: 0x06007152 RID: 29010 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void MissionStart(IGamePlayer host)
		{
		}

		// Token: 0x06007153 RID: 29011 RVA: 0x0025B504 File Offset: 0x00259704
		public bool IsMatchOrFreedom()
		{
			return this.m_roomType == eRoomType.Match || this.m_roomType == eRoomType.Freedom;
		}

		// Token: 0x06007154 RID: 29012 RVA: 0x0025B534 File Offset: 0x00259734
		public void Dispose()
		{
			bool flag = Interlocked.Exchange(ref this.m_disposed, 1) == 0;
			if (flag)
			{
				this.Dispose(true);
			}
		}

		// Token: 0x06007155 RID: 29013 RVA: 0x00005683 File Offset: 0x00003883
		protected virtual void Dispose(bool disposing)
		{
		}

		// Token: 0x06007156 RID: 29014 RVA: 0x0025B560 File Offset: 0x00259760
		protected void OnGameStarted()
		{
			bool flag = this.GameStarted != null;
			if (flag)
			{
				this.GameStarted(this);
			}
		}

		// Token: 0x06007157 RID: 29015 RVA: 0x0025B58C File Offset: 0x0025978C
		protected void OnGameStopped()
		{
			bool flag = this.GameStopped != null;
			if (flag)
			{
				this.GameStopped(this);
			}
		}

		// Token: 0x04003D77 RID: 15735
		private int m_id;

		// Token: 0x04003D78 RID: 15736
		protected eRoomType m_roomType;

		// Token: 0x04003D79 RID: 15737
		protected eGameType m_gameType;

		// Token: 0x04003D7A RID: 15738
		protected eMapType m_mapType;

		// Token: 0x04003D7B RID: 15739
		protected int m_timeType;

		// Token: 0x04003D7C RID: 15740
		private int m_disposed = 0;
	}
}
