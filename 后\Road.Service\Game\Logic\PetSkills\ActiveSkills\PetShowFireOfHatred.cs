﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D49 RID: 3401
	public class PetShowFireOfHatred : BasePetEffect
	{
		// Token: 0x060079E5 RID: 31205 RVA: 0x0002E10E File Offset: 0x0002C30E
		public PetShowFireOfHatred(int count, int skillId, string elementID)
			: base(ePetEffectType.PetShowFireOfHatred, elementID)
		{
			this.m_count = count;
			this.m_skillId = skillId;
		}

		// Token: 0x060079E6 RID: 31206 RVA: 0x0028CE04 File Offset: 0x0028B004
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_skillId;
			if (flag)
			{
				for (int i = 0; i < this.m_count; i++)
				{
					living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				}
				living.AddPetEffect(new PetShowFireOfHatredEquip(this.m_count, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x060079E7 RID: 31207 RVA: 0x0002E12C File Offset: 0x0002C32C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079E8 RID: 31208 RVA: 0x0002E142 File Offset: 0x0002C342
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079E9 RID: 31209 RVA: 0x0028CE7C File Offset: 0x0028B07C
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetShowFireOfHatred) is PetShowFireOfHatred;
			return flag || base.Start(living);
		}

		// Token: 0x0400476B RID: 18283
		private int m_skillId;

		// Token: 0x0400476C RID: 18284
		private int m_count;
	}
}
