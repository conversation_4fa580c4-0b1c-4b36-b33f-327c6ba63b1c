﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x64</Platform>
    <ProjectGuid>{4896D65B-2F8C-497C-A898-6C19AB0B2FB7}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>mscorlib</RootNamespace>
    <AssemblyName>mscorlib</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoStdLib>true</NoStdLib>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoStdLib>true</NoStdLib>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AssemblyRef.cs" />
    <Compile Include="EmptyArray.cs" />
    <Compile Include="FXAssembly.cs" />
    <Compile Include="Microsoft\Reflection\ReflectionExtensions.cs" />
    <Compile Include="Microsoft\Runtime\Hosting\IClrStrongName.cs" />
    <Compile Include="Microsoft\Runtime\Hosting\IClrStrongNameUsingIntPtr.cs" />
    <Compile Include="Microsoft\Runtime\Hosting\StrongNameHelpers.cs" />
    <Compile Include="Microsoft\Win32\ASM_CACHE.cs" />
    <Compile Include="Microsoft\Win32\ASM_NAME.cs" />
    <Compile Include="Microsoft\Win32\CANOF.cs" />
    <Compile Include="Microsoft\Win32\Fusion.cs" />
    <Compile Include="Microsoft\Win32\IApplicationContext.cs" />
    <Compile Include="Microsoft\Win32\IAssemblyEnum.cs" />
    <Compile Include="Microsoft\Win32\IAssemblyName.cs" />
    <Compile Include="Microsoft\Win32\OAVariantLib.cs" />
    <Compile Include="Microsoft\Win32\Registry.cs" />
    <Compile Include="Microsoft\Win32\RegistryHive.cs" />
    <Compile Include="Microsoft\Win32\RegistryKey.cs" />
    <Compile Include="Microsoft\Win32\RegistryKeyPermissionCheck.cs" />
    <Compile Include="Microsoft\Win32\RegistryOptions.cs" />
    <Compile Include="Microsoft\Win32\RegistryValueKind.cs" />
    <Compile Include="Microsoft\Win32\RegistryValueOptions.cs" />
    <Compile Include="Microsoft\Win32\RegistryView.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\CriticalHandleMinusOneIsInvalid.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\CriticalHandleZeroOrMinusOneIsInvalid.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeAccessTokenHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeFileHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeFileMappingHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeFindHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeHandleMinusOneIsInvalid.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeHandleZeroOrMinusOneIsInvalid.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeLocalAllocHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeLsaLogonProcessHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeLsaMemoryHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeLsaPolicyHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeLsaReturnBufferHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafePEFileHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeProcessHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeRegistryHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeThreadHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeViewOfFileHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeHandles\SafeWaitHandle.cs" />
    <Compile Include="Microsoft\Win32\SafeLibraryHandle.cs" />
    <Compile Include="Microsoft\Win32\UnsafeNativeMethods.cs" />
    <Compile Include="Microsoft\Win32\Win32Native.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="System\AccessViolationException.cs" />
    <Compile Include="System\Action.2.cs" />
    <Compile Include="System\Action.3.cs" />
    <Compile Include="System\Action.4.cs" />
    <Compile Include="System\Action.5.cs" />
    <Compile Include="System\Action.6.cs" />
    <Compile Include="System\Action.7.cs" />
    <Compile Include="System\Action.8.cs" />
    <Compile Include="System\Action.9.cs" />
    <Compile Include="System\Action.cs" />
    <Compile Include="System\ActivationContext.cs" />
    <Compile Include="System\Activator.cs" />
    <Compile Include="System\AggregateException.cs" />
    <Compile Include="System\AppContext.cs" />
    <Compile Include="System\AppContextDefaultValues.cs" />
    <Compile Include="System\AppContextSwitches.cs" />
    <Compile Include="System\AppDomain.cs" />
    <Compile Include="System\AppDomainHandle.cs" />
    <Compile Include="System\AppDomainInitializer.cs" />
    <Compile Include="System\AppDomainInitializerInfo.cs" />
    <Compile Include="System\AppDomainManager.cs" />
    <Compile Include="System\AppDomainManagerInitializationOptions.cs" />
    <Compile Include="System\AppDomainPauseManager.cs" />
    <Compile Include="System\AppDomainSetup.cs" />
    <Compile Include="System\AppDomainUnloadedException.cs" />
    <Compile Include="System\ApplicationException.cs" />
    <Compile Include="System\ApplicationId.cs" />
    <Compile Include="System\ApplicationIdentity.cs" />
    <Compile Include="System\ArgIterator.cs" />
    <Compile Include="System\ArgumentException.cs" />
    <Compile Include="System\ArgumentNullException.cs" />
    <Compile Include="System\ArgumentOutOfRangeException.cs" />
    <Compile Include="System\ArithmeticException.cs" />
    <Compile Include="System\Array.cs" />
    <Compile Include="System\ArraySegment.cs" />
    <Compile Include="System\ArrayTypeMismatchException.cs" />
    <Compile Include="System\AssemblyLoadEventArgs.cs" />
    <Compile Include="System\AssemblyLoadEventHandler.cs" />
    <Compile Include="System\AsyncCallback.cs" />
    <Compile Include="System\Attribute.cs" />
    <Compile Include="System\AttributeTargets.cs" />
    <Compile Include="System\AttributeUsageAttribute.cs" />
    <Compile Include="System\BadImageFormatException.cs" />
    <Compile Include="System\Base64FormattingOptions.cs" />
    <Compile Include="System\BaseConfigHandler.cs" />
    <Compile Include="System\BCLDebug.cs" />
    <Compile Include="System\BitConverter.cs" />
    <Compile Include="System\Boolean.cs" />
    <Compile Include="System\Buffer.cs" />
    <Compile Include="System\Byte.cs" />
    <Compile Include="System\CannotUnloadAppDomainException.cs" />
    <Compile Include="System\Char.cs" />
    <Compile Include="System\CharEnumerator.cs" />
    <Compile Include="System\CLRConfig.cs" />
    <Compile Include="System\CLSCompliantAttribute.cs" />
    <Compile Include="System\Collections\ArrayList.cs" />
    <Compile Include="System\Collections\BitArray.cs" />
    <Compile Include="System\Collections\CaseInsensitiveComparer.cs" />
    <Compile Include="System\Collections\CaseInsensitiveHashCodeProvider.cs" />
    <Compile Include="System\Collections\CollectionBase.cs" />
    <Compile Include="System\Collections\Comparer.cs" />
    <Compile Include="System\Collections\CompatibleComparer.cs" />
    <Compile Include="System\Collections\Concurrent\CDSCollectionETWBCLProvider.cs" />
    <Compile Include="System\Collections\Concurrent\ConcurrentDictionary.cs" />
    <Compile Include="System\Collections\Concurrent\ConcurrentQueue.cs" />
    <Compile Include="System\Collections\Concurrent\ConcurrentStack.cs" />
    <Compile Include="System\Collections\Concurrent\EnumerablePartitionerOptions.cs" />
    <Compile Include="System\Collections\Concurrent\IProducerConsumerCollection.cs" />
    <Compile Include="System\Collections\Concurrent\OrderablePartitioner.cs" />
    <Compile Include="System\Collections\Concurrent\Partitioner.2.cs" />
    <Compile Include="System\Collections\Concurrent\Partitioner.cs" />
    <Compile Include="System\Collections\Concurrent\SystemCollectionsConcurrent_ProducerConsumerCollectionDebugV.cs" />
    <Compile Include="System\Collections\Concurrent\VolatileBool.cs" />
    <Compile Include="System\Collections\DictionaryBase.cs" />
    <Compile Include="System\Collections\DictionaryEntry.cs" />
    <Compile Include="System\Collections\EmptyReadOnlyDictionaryInternal.cs" />
    <Compile Include="System\Collections\Generic\ArraySortHelper.2.cs" />
    <Compile Include="System\Collections\Generic\ArraySortHelper.cs" />
    <Compile Include="System\Collections\Generic\ByteEqualityComparer.cs" />
    <Compile Include="System\Collections\Generic\Comparer.cs" />
    <Compile Include="System\Collections\Generic\ComparisonComparer.cs" />
    <Compile Include="System\Collections\Generic\Dictionary.cs" />
    <Compile Include="System\Collections\Generic\EnumEqualityComparer.cs" />
    <Compile Include="System\Collections\Generic\EqualityComparer.cs" />
    <Compile Include="System\Collections\Generic\GenericArraySortHelper.2.cs" />
    <Compile Include="System\Collections\Generic\GenericArraySortHelper.cs" />
    <Compile Include="System\Collections\Generic\GenericComparer.cs" />
    <Compile Include="System\Collections\Generic\GenericEqualityComparer.cs" />
    <Compile Include="System\Collections\Generic\IArraySortHelper.2.cs" />
    <Compile Include="System\Collections\Generic\IArraySortHelper.cs" />
    <Compile Include="System\Collections\Generic\ICollection.cs" />
    <Compile Include="System\Collections\Generic\IComparer.cs" />
    <Compile Include="System\Collections\Generic\IDictionary.cs" />
    <Compile Include="System\Collections\Generic\IEnumerable.cs" />
    <Compile Include="System\Collections\Generic\IEnumerator.cs" />
    <Compile Include="System\Collections\Generic\IEqualityComparer.cs" />
    <Compile Include="System\Collections\Generic\IList.cs" />
    <Compile Include="System\Collections\Generic\IntrospectiveSortUtilities.cs" />
    <Compile Include="System\Collections\Generic\IReadOnlyCollection.cs" />
    <Compile Include="System\Collections\Generic\IReadOnlyDictionary.cs" />
    <Compile Include="System\Collections\Generic\IReadOnlyList.cs" />
    <Compile Include="System\Collections\Generic\KeyNotFoundException.cs" />
    <Compile Include="System\Collections\Generic\KeyValuePair.cs" />
    <Compile Include="System\Collections\Generic\List.cs" />
    <Compile Include="System\Collections\Generic\LongEnumEqualityComparer.cs" />
    <Compile Include="System\Collections\Generic\Mscorlib_CollectionDebugView.cs" />
    <Compile Include="System\Collections\Generic\Mscorlib_DictionaryDebugView.cs" />
    <Compile Include="System\Collections\Generic\Mscorlib_DictionaryKeyCollectionDebugView.cs" />
    <Compile Include="System\Collections\Generic\Mscorlib_DictionaryValueCollectionDebugView.cs" />
    <Compile Include="System\Collections\Generic\Mscorlib_KeyedCollectionDebugView.cs" />
    <Compile Include="System\Collections\Generic\NullableComparer.cs" />
    <Compile Include="System\Collections\Generic\NullableEqualityComparer.cs" />
    <Compile Include="System\Collections\Generic\ObjectComparer.cs" />
    <Compile Include="System\Collections\Generic\ObjectEqualityComparer.cs" />
    <Compile Include="System\Collections\Generic\RandomizedObjectEqualityComparer.cs" />
    <Compile Include="System\Collections\Generic\RandomizedStringEqualityComparer.cs" />
    <Compile Include="System\Collections\Generic\SByteEnumEqualityComparer.cs" />
    <Compile Include="System\Collections\Generic\ShortEnumEqualityComparer.cs" />
    <Compile Include="System\Collections\HashHelpers.cs" />
    <Compile Include="System\Collections\Hashtable.cs" />
    <Compile Include="System\Collections\ICollection.cs" />
    <Compile Include="System\Collections\IComparer.cs" />
    <Compile Include="System\Collections\IDictionary.cs" />
    <Compile Include="System\Collections\IDictionaryEnumerator.cs" />
    <Compile Include="System\Collections\IEnumerable.cs" />
    <Compile Include="System\Collections\IEnumerator.cs" />
    <Compile Include="System\Collections\IEqualityComparer.cs" />
    <Compile Include="System\Collections\IHashCodeProvider.cs" />
    <Compile Include="System\Collections\IList.cs" />
    <Compile Include="System\Collections\IStructuralComparable.cs" />
    <Compile Include="System\Collections\IStructuralEquatable.cs" />
    <Compile Include="System\Collections\KeyValuePairs.cs" />
    <Compile Include="System\Collections\ListDictionaryInternal.cs" />
    <Compile Include="System\Collections\ObjectModel\Collection.cs" />
    <Compile Include="System\Collections\ObjectModel\KeyedCollection.cs" />
    <Compile Include="System\Collections\ObjectModel\ReadOnlyCollection.cs" />
    <Compile Include="System\Collections\ObjectModel\ReadOnlyDictionary.cs" />
    <Compile Include="System\Collections\ObjectModel\ReadOnlyDictionaryHelpers.cs" />
    <Compile Include="System\Collections\Queue.cs" />
    <Compile Include="System\Collections\ReadOnlyCollectionBase.cs" />
    <Compile Include="System\Collections\SortedList.cs" />
    <Compile Include="System\Collections\Stack.cs" />
    <Compile Include="System\Collections\StructuralComparer.cs" />
    <Compile Include="System\Collections\StructuralComparisons.cs" />
    <Compile Include="System\Collections\StructuralEqualityComparer.cs" />
    <Compile Include="System\Comparison.cs" />
    <Compile Include="System\CompatibilityFlag.cs" />
    <Compile Include="System\CompatibilitySwitches.cs" />
    <Compile Include="System\ConfigEvents.cs" />
    <Compile Include="System\ConfigNode.cs" />
    <Compile Include="System\ConfigNodeSubType.cs" />
    <Compile Include="System\ConfigNodeType.cs" />
    <Compile Include="System\ConfigTreeParser.cs" />
    <Compile Include="System\Configuration\Assemblies\AssemblyHash.cs" />
    <Compile Include="System\Configuration\Assemblies\AssemblyHashAlgorithm.cs" />
    <Compile Include="System\Configuration\Assemblies\AssemblyVersionCompatibility.cs" />
    <Compile Include="System\Console.cs" />
    <Compile Include="System\ConsoleCancelEventArgs.cs" />
    <Compile Include="System\ConsoleCancelEventHandler.cs" />
    <Compile Include="System\ConsoleColor.cs" />
    <Compile Include="System\ConsoleKey.cs" />
    <Compile Include="System\ConsoleKeyInfo.cs" />
    <Compile Include="System\ConsoleModifiers.cs" />
    <Compile Include="System\ConsoleSpecialKey.cs" />
    <Compile Include="System\ContextBoundObject.cs" />
    <Compile Include="System\ContextMarshalException.cs" />
    <Compile Include="System\ContextStaticAttribute.cs" />
    <Compile Include="System\Convert.cs" />
    <Compile Include="System\Converter.cs" />
    <Compile Include="System\CrossAppDomainDelegate.cs" />
    <Compile Include="System\CtorDelegate.cs" />
    <Compile Include="System\CultureAwareComparer.cs" />
    <Compile Include="System\CultureAwareRandomizedComparer.cs" />
    <Compile Include="System\Currency.cs" />
    <Compile Include="System\CurrentSystemTimeZone.cs" />
    <Compile Include="System\DataMisalignedException.cs" />
    <Compile Include="System\DateTime.cs" />
    <Compile Include="System\DateTimeFormat.cs" />
    <Compile Include="System\DateTimeKind.cs" />
    <Compile Include="System\DateTimeOffset.cs" />
    <Compile Include="System\DateTimeParse.cs" />
    <Compile Include="System\DateTimeRawInfo.cs" />
    <Compile Include="System\DateTimeResult.cs" />
    <Compile Include="System\DateTimeToken.cs" />
    <Compile Include="System\DayOfWeek.cs" />
    <Compile Include="System\DBNull.cs" />
    <Compile Include="System\Decimal.cs" />
    <Compile Include="System\DefaultBinder.cs" />
    <Compile Include="System\Delegate.cs" />
    <Compile Include="System\DelegateBindingFlags.cs" />
    <Compile Include="System\DelegateSerializationHolder.cs" />
    <Compile Include="System\Deployment\Internal\InternalActivationContextHelper.cs" />
    <Compile Include="System\Deployment\Internal\InternalApplicationIdentityHelper.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\BLOB.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\CATEGORY.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\CATEGORY_INSTANCE.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\CATEGORY_SUBCATEGORY.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IActContext.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IAppIdAuthority.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IAPPIDAUTHORITY_ARE_DEFINITIONS_EQUAL_FLAGS.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IAPPIDAUTHORITY_ARE_REFERENCES_EQUAL_FLAGS.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\ICDF.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IDefinitionAppId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IDefinitionIdentity.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IDENTITY_ATTRIBUTE.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IEnumDefinitionIdentity.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IEnumIDENTITY_ATTRIBUTE.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IEnumReferenceIdentity.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IEnumSTORE_ASSEMBLY.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IEnumSTORE_ASSEMBLY_FILE.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IEnumSTORE_ASSEMBLY_INSTALLATION_REFERENCE.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IEnumSTORE_CATEGORY.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IEnumSTORE_CATEGORY_INSTANCE.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IEnumSTORE_CATEGORY_SUBCATEGORY.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IEnumSTORE_DEPLOYMENT_METADATA.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IEnumSTORE_DEPLOYMENT_METADATA_PROPERTY.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IEnumUnknown.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IIdentityAuthority.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IIDENTITYAUTHORITY_DEFINITION_IDENTITY_TO_TEXT_FLAGS.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IIDENTITYAUTHORITY_DOES_DEFINITION_MATCH_REFERENCE_FLAGS.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IIDENTITYAUTHORITY_REFERENCE_IDENTITY_TO_TEXT_FLAGS.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IManifestInformation.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IManifestParseErrorCallback.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IReferenceAppId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IReferenceIdentity.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\ISection.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\ISectionEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\ISectionWithReferenceIdentityKey.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\ISectionWithStringKey.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IsolationInterop.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IStateManager.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IStore.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IStore_BindingResult.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\IStore_BindingResult_BoundVersion.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\ISTORE_BIND_REFERENCE_TO_ASSEMBLY_FLAGS.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\ISTORE_ENUM_ASSEMBLIES_FLAGS.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\ISTORE_ENUM_FILES_FLAGS.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\AssemblyReferenceDependentAssemblyEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\AssemblyReferenceDependentAssemblyEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\AssemblyReferenceEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\AssemblyReferenceEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\AssemblyRequestEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\AssemblyRequestEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CategoryMembershipDataEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CategoryMembershipDataEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CategoryMembershipEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CategoryMembershipEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CLRSurrogateEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CLRSurrogateEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMSSECTIONID.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CmsUtils.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMS_ASSEMBLY_DEPLOYMENT_FLAG.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMS_ASSEMBLY_REFERENCE_DEPENDENT_ASSEMBLY_FLAG.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMS_ASSEMBLY_REFERENCE_FLAG.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMS_COM_SERVER_FLAG.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMS_ENTRY_POINT_FLAG.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMS_FILE_FLAG.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMS_FILE_HASH_ALGORITHM.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMS_FILE_WRITABLE_TYPE.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMS_HASH_DIGESTMETHOD.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMS_HASH_TRANSFORM.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMS_SCHEMA_VERSION.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMS_TIME_UNIT_TYPE.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CMS_USAGE_PATTERN.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CompatibleFrameworksMetadataEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\CompatibleFrameworksMetadataEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\COMServerEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\COMServerEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\DependentOSMetadataEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\DependentOSMetadataEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\DeploymentMetadataEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\DeploymentMetadataEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\DescriptionMetadataEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\DescriptionMetadataEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\EntryPointEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\EntryPointEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\FileAssociationEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\FileAssociationEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\FileEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\FileEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\HashElementEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\HashElementEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IAssemblyReferenceDependentAssemblyEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IAssemblyReferenceEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IAssemblyRequestEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\ICategoryMembershipDataEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\ICategoryMembershipEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\ICLRSurrogateEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\ICMS.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\ICompatibleFrameworksMetadataEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\ICOMServerEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IDependentOSMetadataEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IDeploymentMetadataEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IDescriptionMetadataEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IEntryPointEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IFileAssociationEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IFileEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IHashElementEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IMetadataSectionEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IMuiResourceIdLookupMapEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IMuiResourceMapEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IMuiResourceTypeIdIntEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IMuiResourceTypeIdStringEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IPermissionSetEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IProgIdRedirectionEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IResourceTableMappingEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\ISubcategoryMembershipEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\IWindowClassEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\MetadataSectionEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\MetadataSectionEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\MuiResourceIdLookupMapEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\MuiResourceIdLookupMapEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\MuiResourceMapEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\MuiResourceMapEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\MuiResourceTypeIdIntEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\MuiResourceTypeIdIntEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\MuiResourceTypeIdStringEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\MuiResourceTypeIdStringEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\PermissionSetEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\PermissionSetEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\ProgIdRedirectionEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\ProgIdRedirectionEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\ResourceTableMappingEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\ResourceTableMappingEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\SubcategoryMembershipEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\SubcategoryMembershipEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\WindowClassEntry.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Manifest\WindowClassEntryFieldId.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StateManager_RunningState.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\Store.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreApplicationReference.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreAssemblyEnumeration.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreAssemblyFileEnumeration.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreCategoryEnumeration.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreCategoryInstanceEnumeration.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreDeploymentMetadataEnumeration.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreDeploymentMetadataPropertyEnumeration.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreOperationInstallDeployment.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreOperationMetadataProperty.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreOperationPinDeployment.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreOperationScavenge.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreOperationSetCanonicalizationContext.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreOperationSetDeploymentMetadata.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreOperationStageComponent.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreOperationStageComponentFile.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreOperationUninstallDeployment.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreOperationUnpinDeployment.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreSubcategoryEnumeration.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreTransaction.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreTransactionData.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreTransactionOperation.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\StoreTransactionOperationType.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\STORE_ASSEMBLY.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\STORE_ASSEMBLY_FILE.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\STORE_ASSEMBLY_FILE_STATUS_FLAGS.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\STORE_ASSEMBLY_STATUS_FLAGS.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\STORE_CATEGORY.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\STORE_CATEGORY_INSTANCE.cs" />
    <Compile Include="System\Deployment\Internal\Isolation\STORE_CATEGORY_SUBCATEGORY.cs" />
    <Compile Include="System\Diagnostics\Assert.cs" />
    <Compile Include="System\Diagnostics\AssertFilter.cs" />
    <Compile Include="System\Diagnostics\AssertFilters.cs" />
    <Compile Include="System\Diagnostics\CodeAnalysis\SuppressMessageAttribute.cs" />
    <Compile Include="System\Diagnostics\ConditionalAttribute.cs" />
    <Compile Include="System\Diagnostics\Contracts\Contract.cs" />
    <Compile Include="System\Diagnostics\Contracts\ContractAbbreviatorAttribute.cs" />
    <Compile Include="System\Diagnostics\Contracts\ContractArgumentValidatorAttribute.cs" />
    <Compile Include="System\Diagnostics\Contracts\ContractClassAttribute.cs" />
    <Compile Include="System\Diagnostics\Contracts\ContractClassForAttribute.cs" />
    <Compile Include="System\Diagnostics\Contracts\ContractException.cs" />
    <Compile Include="System\Diagnostics\Contracts\ContractFailedEventArgs.cs" />
    <Compile Include="System\Diagnostics\Contracts\ContractFailureKind.cs" />
    <Compile Include="System\Diagnostics\Contracts\ContractInvariantMethodAttribute.cs" />
    <Compile Include="System\Diagnostics\Contracts\ContractOptionAttribute.cs" />
    <Compile Include="System\Diagnostics\Contracts\ContractPublicPropertyNameAttribute.cs" />
    <Compile Include="System\Diagnostics\Contracts\ContractReferenceAssemblyAttribute.cs" />
    <Compile Include="System\Diagnostics\Contracts\ContractRuntimeIgnoredAttribute.cs" />
    <Compile Include="System\Diagnostics\Contracts\ContractVerificationAttribute.cs" />
    <Compile Include="System\Diagnostics\Contracts\Internal\ContractHelper.cs" />
    <Compile Include="System\Diagnostics\Contracts\PureAttribute.cs" />
    <Compile Include="System\Diagnostics\DebuggableAttribute.cs" />
    <Compile Include="System\Diagnostics\Debugger.cs" />
    <Compile Include="System\Diagnostics\DebuggerBrowsableAttribute.cs" />
    <Compile Include="System\Diagnostics\DebuggerBrowsableState.cs" />
    <Compile Include="System\Diagnostics\DebuggerDisplayAttribute.cs" />
    <Compile Include="System\Diagnostics\DebuggerHiddenAttribute.cs" />
    <Compile Include="System\Diagnostics\DebuggerNonUserCodeAttribute.cs" />
    <Compile Include="System\Diagnostics\DebuggerStepperBoundaryAttribute.cs" />
    <Compile Include="System\Diagnostics\DebuggerStepThroughAttribute.cs" />
    <Compile Include="System\Diagnostics\DebuggerTypeProxyAttribute.cs" />
    <Compile Include="System\Diagnostics\DebuggerVisualizerAttribute.cs" />
    <Compile Include="System\Diagnostics\DefaultFilter.cs" />
    <Compile Include="System\Diagnostics\EditAndContinueHelper.cs" />
    <Compile Include="System\Diagnostics\ICustomDebuggerNotification.cs" />
    <Compile Include="System\Diagnostics\Log.cs" />
    <Compile Include="System\Diagnostics\LoggingLevels.cs" />
    <Compile Include="System\Diagnostics\LogMessageEventHandler.cs" />
    <Compile Include="System\Diagnostics\LogSwitch.cs" />
    <Compile Include="System\Diagnostics\LogSwitchLevelHandler.cs" />
    <Compile Include="System\Diagnostics\StackFrame.cs" />
    <Compile Include="System\Diagnostics\StackFrameHelper.cs" />
    <Compile Include="System\Diagnostics\StackTrace.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\ISymbolBinder.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\ISymbolBinder1.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\ISymbolDocument.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\ISymbolDocumentWriter.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\ISymbolMethod.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\ISymbolNamespace.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\ISymbolReader.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\ISymbolScope.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\ISymbolVariable.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\ISymbolWriter.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\SymAddressKind.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\SymbolToken.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\SymDocumentType.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\SymLanguageType.cs" />
    <Compile Include="System\Diagnostics\SymbolStore\SymLanguageVendor.cs" />
    <Compile Include="System\Diagnostics\Tracing\ActivityFilter.cs" />
    <Compile Include="System\Diagnostics\Tracing\ActivityTracker.cs" />
    <Compile Include="System\Diagnostics\Tracing\ArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\BooleanArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\BooleanTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\ByteArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\ByteTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\CharArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\CharTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\ClassPropertyWriter.cs" />
    <Compile Include="System\Diagnostics\Tracing\ConcurrentSet.cs" />
    <Compile Include="System\Diagnostics\Tracing\ConcurrentSetItem.cs" />
    <Compile Include="System\Diagnostics\Tracing\ControllerCommand.cs" />
    <Compile Include="System\Diagnostics\Tracing\DataCollector.cs" />
    <Compile Include="System\Diagnostics\Tracing\DateTimeOffsetTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\DateTimeTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\DecimalTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\DoubleArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\DoubleTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\EmptyStruct.cs" />
    <Compile Include="System\Diagnostics\Tracing\EnumByteTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\EnumerableTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\EnumHelper.cs" />
    <Compile Include="System\Diagnostics\Tracing\EnumInt16TypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\EnumInt32TypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\EnumInt64TypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\EnumSByteTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\EnumUInt16TypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\EnumUInt32TypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\EnumUInt64TypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\EtwSession.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventActivityOptions.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventAttribute.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventChannel.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventChannelAttribute.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventChannelType.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventCommand.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventCommandEventArgs.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventDataAttribute.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventDescriptor.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventDispatcher.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventFieldAttribute.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventFieldFormat.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventFieldTags.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventIgnoreAttribute.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventKeywords.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventLevel.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventListener.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventManifestOptions.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventOpcode.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventPayload.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventProvider.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventSource.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventSourceActivity.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventSourceAttribute.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventSourceCreatedEventArgs.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventSourceException.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventSourceOptions.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventSourceSettings.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventTags.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventTask.cs" />
    <Compile Include="System\Diagnostics\Tracing\EventWrittenEventArgs.cs" />
    <Compile Include="System\Diagnostics\Tracing\FieldMetadata.cs" />
    <Compile Include="System\Diagnostics\Tracing\FrameworkEventSource.cs" />
    <Compile Include="System\Diagnostics\Tracing\GuidArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\GuidTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\Int16ArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\Int16TypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\Int32ArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\Int32TypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\Int64ArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\Int64TypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\Internal\Environment.cs" />
    <Compile Include="System\Diagnostics\Tracing\IntPtrArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\IntPtrTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\InvokeTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\KeyValuePairTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\ManifestBuilder.cs" />
    <Compile Include="System\Diagnostics\Tracing\ManifestEnvelope.cs" />
    <Compile Include="System\Diagnostics\Tracing\NameInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\NonEventAttribute.cs" />
    <Compile Include="System\Diagnostics\Tracing\NonGenericProperytWriter.cs" />
    <Compile Include="System\Diagnostics\Tracing\NullableTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\NullTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\PropertyAccessor.cs" />
    <Compile Include="System\Diagnostics\Tracing\PropertyAnalysis.cs" />
    <Compile Include="System\Diagnostics\Tracing\SByteArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\SByteTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\SessionMask.cs" />
    <Compile Include="System\Diagnostics\Tracing\SimpleEventTypes.cs" />
    <Compile Include="System\Diagnostics\Tracing\SingleArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\SingleTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\Statics.cs" />
    <Compile Include="System\Diagnostics\Tracing\StringTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\StructPropertyWriter.cs" />
    <Compile Include="System\Diagnostics\Tracing\TimeSpanTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\TraceLoggingDataCollector.cs" />
    <Compile Include="System\Diagnostics\Tracing\TraceLoggingDataType.cs" />
    <Compile Include="System\Diagnostics\Tracing\TraceLoggingEventTypes.cs" />
    <Compile Include="System\Diagnostics\Tracing\TraceLoggingMetadataCollector.cs" />
    <Compile Include="System\Diagnostics\Tracing\TraceLoggingTypeInfo.2.cs" />
    <Compile Include="System\Diagnostics\Tracing\TraceLoggingTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\TypeAnalysis.cs" />
    <Compile Include="System\Diagnostics\Tracing\UInt16ArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\UInt16TypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\UInt32ArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\UInt32TypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\UInt64ArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\UInt64TypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\UIntPtrArrayTypeInfo.cs" />
    <Compile Include="System\Diagnostics\Tracing\UIntPtrTypeInfo.cs" />
    <Compile Include="System\DivideByZeroException.cs" />
    <Compile Include="System\DllNotFoundException.cs" />
    <Compile Include="System\Double.cs" />
    <Compile Include="System\DTSubString.cs" />
    <Compile Include="System\DTSubStringType.cs" />
    <Compile Include="System\DuplicateWaitObjectException.cs" />
    <Compile Include="System\Empty.cs" />
    <Compile Include="System\EntryPointNotFoundException.cs" />
    <Compile Include="System\Enum.cs" />
    <Compile Include="System\Environment.cs" />
    <Compile Include="System\EnvironmentVariableTarget.cs" />
    <Compile Include="System\EventArgs.cs" />
    <Compile Include="System\EventHandler.2.cs" />
    <Compile Include="System\EventHandler.cs" />
    <Compile Include="System\Exception.cs" />
    <Compile Include="System\ExceptionArgument.cs" />
    <Compile Include="System\ExceptionResource.cs" />
    <Compile Include="System\ExecutionEngineException.cs" />
    <Compile Include="System\FieldAccessException.cs" />
    <Compile Include="System\FlagsAttribute.cs" />
    <Compile Include="System\FormatException.cs" />
    <Compile Include="System\FormattableString.cs" />
    <Compile Include="System\Func.2.cs" />
    <Compile Include="System\Func.3.cs" />
    <Compile Include="System\Func.4.cs" />
    <Compile Include="System\Func.5.cs" />
    <Compile Include="System\Func.6.cs" />
    <Compile Include="System\Func.7.cs" />
    <Compile Include="System\Func.8.cs" />
    <Compile Include="System\Func.9.cs" />
    <Compile Include="System\Func.cs" />
    <Compile Include="System\GC.cs" />
    <Compile Include="System\GCCollectionMode.cs" />
    <Compile Include="System\GCNotificationStatus.cs" />
    <Compile Include="System\Globalization\AppDomainSortingSetupInfo.cs" />
    <Compile Include="System\Globalization\BidiCategory.cs" />
    <Compile Include="System\Globalization\Calendar.cs" />
    <Compile Include="System\Globalization\CalendarAlgorithmType.cs" />
    <Compile Include="System\Globalization\CalendarData.cs" />
    <Compile Include="System\Globalization\CalendarId.cs" />
    <Compile Include="System\Globalization\CalendarWeekRule.cs" />
    <Compile Include="System\Globalization\CalendricalCalculationsHelper.cs" />
    <Compile Include="System\Globalization\CharUnicodeInfo.cs" />
    <Compile Include="System\Globalization\ChineseLunisolarCalendar.cs" />
    <Compile Include="System\Globalization\CodePageDataItem.cs" />
    <Compile Include="System\Globalization\CompareInfo.cs" />
    <Compile Include="System\Globalization\CompareOptions.cs" />
    <Compile Include="System\Globalization\CultureData.cs" />
    <Compile Include="System\Globalization\CultureInfo.cs" />
    <Compile Include="System\Globalization\CultureNotFoundException.cs" />
    <Compile Include="System\Globalization\CultureTypes.cs" />
    <Compile Include="System\Globalization\DateTimeFormatFlags.cs" />
    <Compile Include="System\Globalization\DateTimeFormatInfo.cs" />
    <Compile Include="System\Globalization\DateTimeFormatInfoScanner.cs" />
    <Compile Include="System\Globalization\DateTimeStyles.cs" />
    <Compile Include="System\Globalization\DaylightTime.cs" />
    <Compile Include="System\Globalization\DaylightTimeStruct.cs" />
    <Compile Include="System\Globalization\DigitShapes.cs" />
    <Compile Include="System\Globalization\EastAsianLunisolarCalendar.cs" />
    <Compile Include="System\Globalization\EncodingTable.cs" />
    <Compile Include="System\Globalization\EraInfo.cs" />
    <Compile Include="System\Globalization\FORMATFLAGS.cs" />
    <Compile Include="System\Globalization\GlobalizationAssembly.cs" />
    <Compile Include="System\Globalization\GlobalizationExtensions.cs" />
    <Compile Include="System\Globalization\GregorianCalendar.cs" />
    <Compile Include="System\Globalization\GregorianCalendarHelper.cs" />
    <Compile Include="System\Globalization\GregorianCalendarTypes.cs" />
    <Compile Include="System\Globalization\HebrewCalendar.cs" />
    <Compile Include="System\Globalization\HebrewNumber.cs" />
    <Compile Include="System\Globalization\HebrewNumberParsingContext.cs" />
    <Compile Include="System\Globalization\HebrewNumberParsingState.cs" />
    <Compile Include="System\Globalization\HijriCalendar.cs" />
    <Compile Include="System\Globalization\IdnMapping.cs" />
    <Compile Include="System\Globalization\InternalCodePageDataItem.cs" />
    <Compile Include="System\Globalization\InternalEncodingDataItem.cs" />
    <Compile Include="System\Globalization\JapaneseCalendar.cs" />
    <Compile Include="System\Globalization\JapaneseLunisolarCalendar.cs" />
    <Compile Include="System\Globalization\JulianCalendar.cs" />
    <Compile Include="System\Globalization\KoreanCalendar.cs" />
    <Compile Include="System\Globalization\KoreanLunisolarCalendar.cs" />
    <Compile Include="System\Globalization\MonthNameStyles.cs" />
    <Compile Include="System\Globalization\NumberFormatInfo.cs" />
    <Compile Include="System\Globalization\NumberStyles.cs" />
    <Compile Include="System\Globalization\PersianCalendar.cs" />
    <Compile Include="System\Globalization\RegionInfo.cs" />
    <Compile Include="System\Globalization\SortKey.cs" />
    <Compile Include="System\Globalization\SortVersion.cs" />
    <Compile Include="System\Globalization\StringInfo.cs" />
    <Compile Include="System\Globalization\TaiwanCalendar.cs" />
    <Compile Include="System\Globalization\TaiwanLunisolarCalendar.cs" />
    <Compile Include="System\Globalization\TextElementEnumerator.cs" />
    <Compile Include="System\Globalization\TextInfo.cs" />
    <Compile Include="System\Globalization\ThaiBuddhistCalendar.cs" />
    <Compile Include="System\Globalization\TimeSpanFormat.cs" />
    <Compile Include="System\Globalization\TimeSpanParse.cs" />
    <Compile Include="System\Globalization\TimeSpanStyles.cs" />
    <Compile Include="System\Globalization\TokenHashValue.cs" />
    <Compile Include="System\Globalization\UmAlQuraCalendar.cs" />
    <Compile Include="System\Globalization\UnicodeCategory.cs" />
    <Compile Include="System\Guid.cs" />
    <Compile Include="System\IAppDomainSetup.cs" />
    <Compile Include="System\IAsyncResult.cs" />
    <Compile Include="System\ICloneable.cs" />
    <Compile Include="System\IComparable.2.cs" />
    <Compile Include="System\IComparable.cs" />
    <Compile Include="System\IConvertible.cs" />
    <Compile Include="System\ICustomFormatter.cs" />
    <Compile Include="System\IDisposable.cs" />
    <Compile Include="System\IEquatable.cs" />
    <Compile Include="System\IFormatProvider.cs" />
    <Compile Include="System\IFormattable.cs" />
    <Compile Include="System\IndexOutOfRangeException.cs" />
    <Compile Include="System\InsufficientExecutionStackException.cs" />
    <Compile Include="System\InsufficientMemoryException.cs" />
    <Compile Include="System\Int16.cs" />
    <Compile Include="System\Int32.cs" />
    <Compile Include="System\Int64.cs" />
    <Compile Include="System\Internal.cs" />
    <Compile Include="System\InternalGCCollectionMode.cs" />
    <Compile Include="System\IntPtr.cs" />
    <Compile Include="System\InvalidCastException.cs" />
    <Compile Include="System\InvalidOperationException.cs" />
    <Compile Include="System\InvalidProgramException.cs" />
    <Compile Include="System\InvalidTimeZoneException.cs" />
    <Compile Include="System\IObservable.cs" />
    <Compile Include="System\IObserver.cs" />
    <Compile Include="System\IO\BinaryReader.cs" />
    <Compile Include="System\IO\BinaryWriter.cs" />
    <Compile Include="System\IO\BufferedStream.cs" />
    <Compile Include="System\IO\Directory.cs" />
    <Compile Include="System\IO\DirectoryInfo.cs" />
    <Compile Include="System\IO\DirectoryInfoResultHandler.cs" />
    <Compile Include="System\IO\DirectoryNotFoundException.cs" />
    <Compile Include="System\IO\DriveInfo.cs" />
    <Compile Include="System\IO\DriveNotFoundException.cs" />
    <Compile Include="System\IO\DriveType.cs" />
    <Compile Include="System\IO\EndOfStreamException.cs" />
    <Compile Include="System\IO\File.cs" />
    <Compile Include="System\IO\FileAccess.cs" />
    <Compile Include="System\IO\FileAttributes.cs" />
    <Compile Include="System\IO\FileInfo.cs" />
    <Compile Include="System\IO\FileInfoResultHandler.cs" />
    <Compile Include="System\IO\FileLoadException.cs" />
    <Compile Include="System\IO\FileMode.cs" />
    <Compile Include="System\IO\FileNotFoundException.cs" />
    <Compile Include="System\IO\FileOptions.cs" />
    <Compile Include="System\IO\FileSecurityStateAccess.cs" />
    <Compile Include="System\IO\FileShare.cs" />
    <Compile Include="System\IO\FileStream.cs" />
    <Compile Include="System\IO\FileStreamAsyncResult.cs" />
    <Compile Include="System\IO\FileSystemEnumerableFactory.cs" />
    <Compile Include="System\IO\FileSystemEnumerableIterator.cs" />
    <Compile Include="System\IO\FileSystemInfo.cs" />
    <Compile Include="System\IO\FileSystemInfoResultHandler.cs" />
    <Compile Include="System\IO\IOException.cs" />
    <Compile Include="System\IO\IsolatedStorage\INormalizeForIsolatedStorage.cs" />
    <Compile Include="System\IO\IsolatedStorage\IsolatedStorage.cs" />
    <Compile Include="System\IO\IsolatedStorage\IsolatedStorageException.cs" />
    <Compile Include="System\IO\IsolatedStorage\IsolatedStorageFile.cs" />
    <Compile Include="System\IO\IsolatedStorage\IsolatedStorageFileEnumerator.cs" />
    <Compile Include="System\IO\IsolatedStorage\IsolatedStorageFileStream.cs" />
    <Compile Include="System\IO\IsolatedStorage\IsolatedStorageScope.cs" />
    <Compile Include="System\IO\IsolatedStorage\IsolatedStorageSecurityOptions.cs" />
    <Compile Include="System\IO\IsolatedStorage\IsolatedStorageSecurityState.cs" />
    <Compile Include="System\IO\IsolatedStorage\SafeIsolatedStorageFileHandle.cs" />
    <Compile Include="System\IO\IsolatedStorage\TwoLevelFileEnumerator.cs" />
    <Compile Include="System\IO\IsolatedStorage\TwoPaths.cs" />
    <Compile Include="System\IO\IsolatedStorage\__HResults.cs" />
    <Compile Include="System\IO\Iterator.cs" />
    <Compile Include="System\IO\LongPath.cs" />
    <Compile Include="System\IO\LongPathDirectory.cs" />
    <Compile Include="System\IO\LongPathFile.cs" />
    <Compile Include="System\IO\LongPathHelper.cs" />
    <Compile Include="System\IO\MemoryStream.cs" />
    <Compile Include="System\IO\Path.cs" />
    <Compile Include="System\IO\PathHelper.cs" />
    <Compile Include="System\IO\PathInternal.cs" />
    <Compile Include="System\IO\PathTooLongException.cs" />
    <Compile Include="System\IO\PinnedBufferMemoryStream.cs" />
    <Compile Include="System\IO\ReadLinesIterator.cs" />
    <Compile Include="System\IO\SearchOption.cs" />
    <Compile Include="System\IO\SearchResultHandler.cs" />
    <Compile Include="System\IO\SeekOrigin.cs" />
    <Compile Include="System\IO\Stream.cs" />
    <Compile Include="System\IO\StreamReader.cs" />
    <Compile Include="System\IO\StreamWriter.cs" />
    <Compile Include="System\IO\StringReader.cs" />
    <Compile Include="System\IO\StringResultHandler.cs" />
    <Compile Include="System\IO\StringWriter.cs" />
    <Compile Include="System\IO\TextReader.cs" />
    <Compile Include="System\IO\TextWriter.cs" />
    <Compile Include="System\IO\UnmanagedMemoryAccessor.cs" />
    <Compile Include="System\IO\UnmanagedMemoryStream.cs" />
    <Compile Include="System\IO\UnmanagedMemoryStreamWrapper.cs" />
    <Compile Include="System\IO\__ConsoleStream.cs" />
    <Compile Include="System\IO\__Error.cs" />
    <Compile Include="System\IO\__HResults.cs" />
    <Compile Include="System\IProgress.cs" />
    <Compile Include="System\IRuntimeFieldInfo.cs" />
    <Compile Include="System\IRuntimeMethodInfo.cs" />
    <Compile Include="System\IServiceProvider.cs" />
    <Compile Include="System\ITupleInternal.cs" />
    <Compile Include="System\IValueTupleInternal.cs" />
    <Compile Include="System\IWellKnownStringEqualityComparer.cs" />
    <Compile Include="System\Lazy.cs" />
    <Compile Include="System\LazyHelpers.cs" />
    <Compile Include="System\LoaderOptimization.cs" />
    <Compile Include="System\LoaderOptimizationAttribute.cs" />
    <Compile Include="System\LocalDataStore.cs" />
    <Compile Include="System\LocalDataStoreElement.cs" />
    <Compile Include="System\LocalDataStoreHolder.cs" />
    <Compile Include="System\LocalDataStoreMgr.cs" />
    <Compile Include="System\LocalDataStoreSlot.cs" />
    <Compile Include="System\LogLevel.cs" />
    <Compile Include="System\MarshalByRefObject.cs" />
    <Compile Include="System\Math.cs" />
    <Compile Include="System\Mda.cs" />
    <Compile Include="System\MemberAccessException.cs" />
    <Compile Include="System\MethodAccessException.cs" />
    <Compile Include="System\MidpointRounding.cs" />
    <Compile Include="System\MissingFieldException.cs" />
    <Compile Include="System\MissingMemberException.cs" />
    <Compile Include="System\MissingMethodException.cs" />
    <Compile Include="System\ModuleHandle.cs" />
    <Compile Include="System\MTAThreadAttribute.cs" />
    <Compile Include="System\MulticastDelegate.cs" />
    <Compile Include="System\MulticastNotSupportedException.cs" />
    <Compile Include="System\NonSerializedAttribute.cs" />
    <Compile Include="System\NotFiniteNumberException.cs" />
    <Compile Include="System\NotImplementedException.cs" />
    <Compile Include="System\NotSupportedException.cs" />
    <Compile Include="System\Nullable.2.cs" />
    <Compile Include="System\Nullable.cs" />
    <Compile Include="System\NullReferenceException.cs" />
    <Compile Include="System\Number.cs" />
    <Compile Include="System\Numerics\Hashing\HashHelpers.cs" />
    <Compile Include="System\Object.cs" />
    <Compile Include="System\ObjectDisposedException.cs" />
    <Compile Include="System\ObsoleteAttribute.cs" />
    <Compile Include="System\OleAutBinder.cs" />
    <Compile Include="System\OperatingSystem.cs" />
    <Compile Include="System\OperationCanceledException.cs" />
    <Compile Include="System\OrdinalComparer.cs" />
    <Compile Include="System\OrdinalRandomizedComparer.cs" />
    <Compile Include="System\OutOfMemoryException.cs" />
    <Compile Include="System\OverflowException.cs" />
    <Compile Include="System\ParamArrayAttribute.cs" />
    <Compile Include="System\ParamsArray.cs" />
    <Compile Include="System\ParseFailureKind.cs" />
    <Compile Include="System\ParseFlags.cs" />
    <Compile Include="System\ParseNumbers.cs" />
    <Compile Include="System\ParsingInfo.cs" />
    <Compile Include="System\PlatformID.cs" />
    <Compile Include="System\PlatformNotSupportedException.cs" />
    <Compile Include="System\Predicate.cs" />
    <Compile Include="System\Progress.cs" />
    <Compile Include="System\ProgressStatics.cs" />
    <Compile Include="System\Random.cs" />
    <Compile Include="System\RankException.cs" />
    <Compile Include="System\ReflectionOnlyType.cs" />
    <Compile Include="System\Reflection\AmbiguousMatchException.cs" />
    <Compile Include="System\Reflection\Assembly.cs" />
    <Compile Include="System\Reflection\AssemblyAlgorithmIdAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyCompanyAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyConfigurationAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyContentType.cs" />
    <Compile Include="System\Reflection\AssemblyCopyrightAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyCultureAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyDefaultAliasAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyDelaySignAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyDescriptionAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyFileVersionAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyFlagsAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyInformationalVersionAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyKeyFileAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyKeyNameAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyMetadataAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyName.cs" />
    <Compile Include="System\Reflection\AssemblyNameFlags.cs" />
    <Compile Include="System\Reflection\AssemblyNameProxy.cs" />
    <Compile Include="System\Reflection\AssemblyProductAttribute.cs" />
    <Compile Include="System\Reflection\AssemblySignatureKeyAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyTitleAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyTrademarkAttribute.cs" />
    <Compile Include="System\Reflection\AssemblyVersionAttribute.cs" />
    <Compile Include="System\Reflection\Associates.cs" />
    <Compile Include="System\Reflection\Binder.cs" />
    <Compile Include="System\Reflection\BindingFlags.cs" />
    <Compile Include="System\Reflection\CallingConventions.cs" />
    <Compile Include="System\Reflection\CerHashtable.cs" />
    <Compile Include="System\Reflection\ConstArray.cs" />
    <Compile Include="System\Reflection\ConstructorInfo.cs" />
    <Compile Include="System\Reflection\CorElementType.cs" />
    <Compile Include="System\Reflection\CustomAttribute.cs" />
    <Compile Include="System\Reflection\CustomAttributeCtorParameter.cs" />
    <Compile Include="System\Reflection\CustomAttributeData.cs" />
    <Compile Include="System\Reflection\CustomAttributeEncodedArgument.cs" />
    <Compile Include="System\Reflection\CustomAttributeEncoding.cs" />
    <Compile Include="System\Reflection\CustomAttributeExtensions.cs" />
    <Compile Include="System\Reflection\CustomAttributeFormatException.cs" />
    <Compile Include="System\Reflection\CustomAttributeNamedArgument.cs" />
    <Compile Include="System\Reflection\CustomAttributeNamedParameter.cs" />
    <Compile Include="System\Reflection\CustomAttributeRecord.cs" />
    <Compile Include="System\Reflection\CustomAttributeType.cs" />
    <Compile Include="System\Reflection\CustomAttributeTypedArgument.cs" />
    <Compile Include="System\Reflection\DefaultMemberAttribute.cs" />
    <Compile Include="System\Reflection\Emit\AssemblyBuilder.cs" />
    <Compile Include="System\Reflection\Emit\AssemblyBuilderAccess.cs" />
    <Compile Include="System\Reflection\Emit\AssemblyBuilderData.cs" />
    <Compile Include="System\Reflection\Emit\ConstructorBuilder.cs" />
    <Compile Include="System\Reflection\Emit\ConstructorOnTypeBuilderInstantiation.cs" />
    <Compile Include="System\Reflection\Emit\CustomAttributeBuilder.cs" />
    <Compile Include="System\Reflection\Emit\DynamicAssemblyFlags.cs" />
    <Compile Include="System\Reflection\Emit\DynamicILGenerator.cs" />
    <Compile Include="System\Reflection\Emit\DynamicILInfo.cs" />
    <Compile Include="System\Reflection\Emit\DynamicMethod.cs" />
    <Compile Include="System\Reflection\Emit\DynamicResolver.cs" />
    <Compile Include="System\Reflection\Emit\DynamicScope.cs" />
    <Compile Include="System\Reflection\Emit\EnumBuilder.cs" />
    <Compile Include="System\Reflection\Emit\EventBuilder.cs" />
    <Compile Include="System\Reflection\Emit\EventToken.cs" />
    <Compile Include="System\Reflection\Emit\ExceptionHandler.cs" />
    <Compile Include="System\Reflection\Emit\FieldBuilder.cs" />
    <Compile Include="System\Reflection\Emit\FieldOnTypeBuilderInstantiation.cs" />
    <Compile Include="System\Reflection\Emit\FieldToken.cs" />
    <Compile Include="System\Reflection\Emit\FlowControl.cs" />
    <Compile Include="System\Reflection\Emit\GenericFieldInfo.cs" />
    <Compile Include="System\Reflection\Emit\GenericMethodInfo.cs" />
    <Compile Include="System\Reflection\Emit\GenericTypeParameterBuilder.cs" />
    <Compile Include="System\Reflection\Emit\ILGenerator.cs" />
    <Compile Include="System\Reflection\Emit\InternalAssemblyBuilder.cs" />
    <Compile Include="System\Reflection\Emit\InternalModuleBuilder.cs" />
    <Compile Include="System\Reflection\Emit\Label.cs" />
    <Compile Include="System\Reflection\Emit\LineNumberInfo.cs" />
    <Compile Include="System\Reflection\Emit\LocalBuilder.cs" />
    <Compile Include="System\Reflection\Emit\LocalSymInfo.cs" />
    <Compile Include="System\Reflection\Emit\MethodBuilder.cs" />
    <Compile Include="System\Reflection\Emit\MethodBuilderInstantiation.cs" />
    <Compile Include="System\Reflection\Emit\MethodOnTypeBuilderInstantiation.cs" />
    <Compile Include="System\Reflection\Emit\MethodRental.cs" />
    <Compile Include="System\Reflection\Emit\MethodToken.cs" />
    <Compile Include="System\Reflection\Emit\ModuleBuilder.cs" />
    <Compile Include="System\Reflection\Emit\ModuleBuilderData.cs" />
    <Compile Include="System\Reflection\Emit\NativeVersionInfo.cs" />
    <Compile Include="System\Reflection\Emit\OpCode.cs" />
    <Compile Include="System\Reflection\Emit\OpCodes.cs" />
    <Compile Include="System\Reflection\Emit\OpCodeType.cs" />
    <Compile Include="System\Reflection\Emit\OpCodeValues.cs" />
    <Compile Include="System\Reflection\Emit\OperandType.cs" />
    <Compile Include="System\Reflection\Emit\PackingSize.cs" />
    <Compile Include="System\Reflection\Emit\ParameterBuilder.cs" />
    <Compile Include="System\Reflection\Emit\ParameterToken.cs" />
    <Compile Include="System\Reflection\Emit\PEFileKinds.cs" />
    <Compile Include="System\Reflection\Emit\PropertyBuilder.cs" />
    <Compile Include="System\Reflection\Emit\PropertyToken.cs" />
    <Compile Include="System\Reflection\Emit\REDocument.cs" />
    <Compile Include="System\Reflection\Emit\ResWriterData.cs" />
    <Compile Include="System\Reflection\Emit\ScopeAction.cs" />
    <Compile Include="System\Reflection\Emit\ScopeTree.cs" />
    <Compile Include="System\Reflection\Emit\SignatureHelper.cs" />
    <Compile Include="System\Reflection\Emit\SignatureToken.cs" />
    <Compile Include="System\Reflection\Emit\StackBehaviour.cs" />
    <Compile Include="System\Reflection\Emit\StringToken.cs" />
    <Compile Include="System\Reflection\Emit\SymbolMethod.cs" />
    <Compile Include="System\Reflection\Emit\SymbolType.cs" />
    <Compile Include="System\Reflection\Emit\TypeBuilder.cs" />
    <Compile Include="System\Reflection\Emit\TypeBuilderInstantiation.cs" />
    <Compile Include="System\Reflection\Emit\TypeKind.cs" />
    <Compile Include="System\Reflection\Emit\TypeNameBuilder.cs" />
    <Compile Include="System\Reflection\Emit\TypeToken.cs" />
    <Compile Include="System\Reflection\Emit\UnmanagedMarshal.cs" />
    <Compile Include="System\Reflection\Emit\VarArgMethod.cs" />
    <Compile Include="System\Reflection\Emit\__ExceptionInfo.cs" />
    <Compile Include="System\Reflection\Emit\__FixupData.cs" />
    <Compile Include="System\Reflection\EventAttributes.cs" />
    <Compile Include="System\Reflection\EventInfo.cs" />
    <Compile Include="System\Reflection\ExceptionHandlingClause.cs" />
    <Compile Include="System\Reflection\ExceptionHandlingClauseOptions.cs" />
    <Compile Include="System\Reflection\FieldAttributes.cs" />
    <Compile Include="System\Reflection\FieldInfo.cs" />
    <Compile Include="System\Reflection\GenericParameterAttributes.cs" />
    <Compile Include="System\Reflection\ICustomAttributeProvider.cs" />
    <Compile Include="System\Reflection\ImageFileMachine.cs" />
    <Compile Include="System\Reflection\InterfaceMapping.cs" />
    <Compile Include="System\Reflection\IntrospectionExtensions.cs" />
    <Compile Include="System\Reflection\InvalidFilterCriteriaException.cs" />
    <Compile Include="System\Reflection\INVOCATION_FLAGS.cs" />
    <Compile Include="System\Reflection\IReflect.cs" />
    <Compile Include="System\Reflection\IReflectableType.cs" />
    <Compile Include="System\Reflection\LoadContext.cs" />
    <Compile Include="System\Reflection\LoaderAllocator.cs" />
    <Compile Include="System\Reflection\LoaderAllocatorScout.cs" />
    <Compile Include="System\Reflection\LocalVariableInfo.cs" />
    <Compile Include="System\Reflection\ManifestResourceInfo.cs" />
    <Compile Include="System\Reflection\MdConstant.cs" />
    <Compile Include="System\Reflection\MdFieldInfo.cs" />
    <Compile Include="System\Reflection\MdSigCallingConvention.cs" />
    <Compile Include="System\Reflection\MemberFilter.cs" />
    <Compile Include="System\Reflection\MemberInfo.cs" />
    <Compile Include="System\Reflection\MemberInfoSerializationHolder.cs" />
    <Compile Include="System\Reflection\MemberTypes.cs" />
    <Compile Include="System\Reflection\MetadataEnumResult.cs" />
    <Compile Include="System\Reflection\MetadataException.cs" />
    <Compile Include="System\Reflection\MetadataImport.cs" />
    <Compile Include="System\Reflection\MetadataToken.cs" />
    <Compile Include="System\Reflection\MetadataTokenType.cs" />
    <Compile Include="System\Reflection\MethodAttributes.cs" />
    <Compile Include="System\Reflection\MethodBase.cs" />
    <Compile Include="System\Reflection\MethodBody.cs" />
    <Compile Include="System\Reflection\MethodImplAttributes.cs" />
    <Compile Include="System\Reflection\MethodInfo.cs" />
    <Compile Include="System\Reflection\MethodSemanticsAttributes.cs" />
    <Compile Include="System\Reflection\Missing.cs" />
    <Compile Include="System\Reflection\Module.cs" />
    <Compile Include="System\Reflection\ModuleResolveEventHandler.cs" />
    <Compile Include="System\Reflection\ObfuscateAssemblyAttribute.cs" />
    <Compile Include="System\Reflection\ObfuscationAttribute.cs" />
    <Compile Include="System\Reflection\ParameterAttributes.cs" />
    <Compile Include="System\Reflection\ParameterInfo.cs" />
    <Compile Include="System\Reflection\ParameterModifier.cs" />
    <Compile Include="System\Reflection\PInvokeAttributes.cs" />
    <Compile Include="System\Reflection\Pointer.cs" />
    <Compile Include="System\Reflection\PortableExecutableKinds.cs" />
    <Compile Include="System\Reflection\ProcessorArchitecture.cs" />
    <Compile Include="System\Reflection\PropertyAttributes.cs" />
    <Compile Include="System\Reflection\PropertyInfo.cs" />
    <Compile Include="System\Reflection\PseudoCustomAttribute.cs" />
    <Compile Include="System\Reflection\ReflectionContext.cs" />
    <Compile Include="System\Reflection\ReflectionTypeLoadException.cs" />
    <Compile Include="System\Reflection\ResourceAttributes.cs" />
    <Compile Include="System\Reflection\ResourceLocation.cs" />
    <Compile Include="System\Reflection\RtFieldInfo.cs" />
    <Compile Include="System\Reflection\RuntimeAssembly.cs" />
    <Compile Include="System\Reflection\RuntimeConstructorInfo.cs" />
    <Compile Include="System\Reflection\RuntimeEventInfo.cs" />
    <Compile Include="System\Reflection\RuntimeFieldInfo.cs" />
    <Compile Include="System\Reflection\RuntimeMethodInfo.cs" />
    <Compile Include="System\Reflection\RuntimeModule.cs" />
    <Compile Include="System\Reflection\RuntimeParameterInfo.cs" />
    <Compile Include="System\Reflection\RuntimePropertyInfo.cs" />
    <Compile Include="System\Reflection\RuntimeReflectionExtensions.cs" />
    <Compile Include="System\Reflection\SecurityContextFrame.cs" />
    <Compile Include="System\Reflection\StrongNameKeyPair.cs" />
    <Compile Include="System\Reflection\TargetException.cs" />
    <Compile Include="System\Reflection\TargetInvocationException.cs" />
    <Compile Include="System\Reflection\TargetParameterCountException.cs" />
    <Compile Include="System\Reflection\TypeAttributes.cs" />
    <Compile Include="System\Reflection\TypeDelegator.cs" />
    <Compile Include="System\Reflection\TypeFilter.cs" />
    <Compile Include="System\Reflection\TypeInfo.cs" />
    <Compile Include="System\Reflection\__Filters.cs" />
    <Compile Include="System\ResId.cs" />
    <Compile Include="System\ResolveEventArgs.cs" />
    <Compile Include="System\ResolveEventHandler.cs" />
    <Compile Include="System\Resolver.cs" />
    <Compile Include="System\Resources\FastResourceComparer.cs" />
    <Compile Include="System\Resources\FileBasedResourceGroveler.cs" />
    <Compile Include="System\Resources\IResourceGroveler.cs" />
    <Compile Include="System\Resources\IResourceReader.cs" />
    <Compile Include="System\Resources\IResourceWriter.cs" />
    <Compile Include="System\Resources\ManifestBasedResourceGroveler.cs" />
    <Compile Include="System\Resources\MissingManifestResourceException.cs" />
    <Compile Include="System\Resources\MissingSatelliteAssemblyException.cs" />
    <Compile Include="System\Resources\NeutralResourcesLanguageAttribute.cs" />
    <Compile Include="System\Resources\PRIExceptionInfo.cs" />
    <Compile Include="System\Resources\ResourceFallbackManager.cs" />
    <Compile Include="System\Resources\ResourceLocator.cs" />
    <Compile Include="System\Resources\ResourceManager.cs" />
    <Compile Include="System\Resources\ResourceReader.cs" />
    <Compile Include="System\Resources\ResourceSet.cs" />
    <Compile Include="System\Resources\ResourceTypeCode.cs" />
    <Compile Include="System\Resources\ResourceWriter.cs" />
    <Compile Include="System\Resources\RuntimeResourceSet.cs" />
    <Compile Include="System\Resources\SatelliteContractVersionAttribute.cs" />
    <Compile Include="System\Resources\UltimateResourceFallbackLocation.cs" />
    <Compile Include="System\Resources\WindowsRuntimeResourceManagerBase.cs" />
    <Compile Include="System\Resources\__HResults.cs" />
    <Compile Include="System\RuntimeArgumentHandle.cs" />
    <Compile Include="System\RuntimeFieldHandle.cs" />
    <Compile Include="System\RuntimeFieldHandleInternal.cs" />
    <Compile Include="System\RuntimeFieldInfoStub.cs" />
    <Compile Include="System\RuntimeMethodHandle.cs" />
    <Compile Include="System\RuntimeMethodHandleInternal.cs" />
    <Compile Include="System\RuntimeMethodInfoStub.cs" />
    <Compile Include="System\RuntimeType.cs" />
    <Compile Include="System\RuntimeTypeHandle.cs" />
    <Compile Include="System\Runtime\AssemblyTargetedPatchBandAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\AccessedThroughPropertyAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\AssemblyAttributesGoHere.cs" />
    <Compile Include="System\Runtime\CompilerServices\AssemblyAttributesGoHereM.cs" />
    <Compile Include="System\Runtime\CompilerServices\AssemblyAttributesGoHereS.cs" />
    <Compile Include="System\Runtime\CompilerServices\AssemblyAttributesGoHereSM.cs" />
    <Compile Include="System\Runtime\CompilerServices\AsyncMethodBuilderCore.cs" />
    <Compile Include="System\Runtime\CompilerServices\AsyncStateMachineAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\AsyncTaskCache.cs" />
    <Compile Include="System\Runtime\CompilerServices\AsyncTaskMethodBuilder.2.cs" />
    <Compile Include="System\Runtime\CompilerServices\AsyncTaskMethodBuilder.cs" />
    <Compile Include="System\Runtime\CompilerServices\AsyncVoidMethodBuilder.cs" />
    <Compile Include="System\Runtime\CompilerServices\CallConvCdecl.cs" />
    <Compile Include="System\Runtime\CompilerServices\CallConvFastcall.cs" />
    <Compile Include="System\Runtime\CompilerServices\CallConvStdcall.cs" />
    <Compile Include="System\Runtime\CompilerServices\CallConvThiscall.cs" />
    <Compile Include="System\Runtime\CompilerServices\CallerFilePathAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\CallerLineNumberAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\CallerMemberNameAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\CompilationRelaxations.cs" />
    <Compile Include="System\Runtime\CompilerServices\CompilationRelaxationsAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\CompilerGeneratedAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\CompilerGlobalScopeAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\CompilerMarshalOverride.cs" />
    <Compile Include="System\Runtime\CompilerServices\ConditionalWeakTable.cs" />
    <Compile Include="System\Runtime\CompilerServices\ConfiguredTaskAwaitable.2.cs" />
    <Compile Include="System\Runtime\CompilerServices\ConfiguredTaskAwaitable.cs" />
    <Compile Include="System\Runtime\CompilerServices\ContractHelper.cs" />
    <Compile Include="System\Runtime\CompilerServices\CustomConstantAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\DateTimeConstantAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\DecimalConstantAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\DecoratedNameAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\DefaultDependencyAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\DependencyAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\DependentHandle.cs" />
    <Compile Include="System\Runtime\CompilerServices\DisablePrivateReflectionAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\DiscardableAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\ExtensionAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\FixedAddressValueTypeAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\FixedBufferAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\FormattableStringFactory.cs" />
    <Compile Include="System\Runtime\CompilerServices\FriendAccessAllowedAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\HasCopySemanticsAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\IAsyncStateMachine.cs" />
    <Compile Include="System\Runtime\CompilerServices\ICriticalNotifyCompletion.cs" />
    <Compile Include="System\Runtime\CompilerServices\IDispatchConstantAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\IndexerNameAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\INotifyCompletion.cs" />
    <Compile Include="System\Runtime\CompilerServices\InternalsVisibleToAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsBoxed.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsByRefLikeAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsByValue.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsConst.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsCopyConstructed.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsExplicitlyDereferenced.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsImplicitlyDereferenced.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsJitIntrinsic.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsLong.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsPinned.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsReadOnlyAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsSignUnspecifiedByte.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsUdtReturn.cs" />
    <Compile Include="System\Runtime\CompilerServices\IsVolatile.cs" />
    <Compile Include="System\Runtime\CompilerServices\IteratorStateMachineAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\ITuple.cs" />
    <Compile Include="System\Runtime\CompilerServices\IUnknownConstantAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\JitHelpers.cs" />
    <Compile Include="System\Runtime\CompilerServices\LoadHint.cs" />
    <Compile Include="System\Runtime\CompilerServices\MethodCodeType.cs" />
    <Compile Include="System\Runtime\CompilerServices\MethodImplAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\MethodImplOptions.cs" />
    <Compile Include="System\Runtime\CompilerServices\NativeCppClassAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\ObjectHandleOnStack.cs" />
    <Compile Include="System\Runtime\CompilerServices\PinningHelper.cs" />
    <Compile Include="System\Runtime\CompilerServices\ReferenceAssemblyAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\RequiredAttributeAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\RuntimeCompatibilityAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\RuntimeFeature.cs" />
    <Compile Include="System\Runtime\CompilerServices\RuntimeHelpers.cs" />
    <Compile Include="System\Runtime\CompilerServices\RuntimeWrappedException.cs" />
    <Compile Include="System\Runtime\CompilerServices\ScopelessEnumAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\SpecialNameAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\StackCrawlMarkHandle.cs" />
    <Compile Include="System\Runtime\CompilerServices\StateMachineAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\StringFreezingAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\StringHandleOnStack.cs" />
    <Compile Include="System\Runtime\CompilerServices\SuppressIldasmAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\SuppressMergeCheckAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\TaskAwaiter.2.cs" />
    <Compile Include="System\Runtime\CompilerServices\TaskAwaiter.cs" />
    <Compile Include="System\Runtime\CompilerServices\TupleElementNamesAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\TypeDependencyAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\TypeForwardedFromAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\TypeForwardedToAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\UnsafeValueTypeAttribute.cs" />
    <Compile Include="System\Runtime\CompilerServices\YieldAwaitable.cs" />
    <Compile Include="System\Runtime\ConstrainedExecution\Cer.cs" />
    <Compile Include="System\Runtime\ConstrainedExecution\Consistency.cs" />
    <Compile Include="System\Runtime\ConstrainedExecution\CriticalFinalizerObject.cs" />
    <Compile Include="System\Runtime\ConstrainedExecution\PrePrepareMethodAttribute.cs" />
    <Compile Include="System\Runtime\ConstrainedExecution\ReliabilityContractAttribute.cs" />
    <Compile Include="System\Runtime\DesignerServices\WindowsRuntimeDesignerContext.cs" />
    <Compile Include="System\Runtime\ExceptionServices\ExceptionDispatchInfo.cs" />
    <Compile Include="System\Runtime\ExceptionServices\FirstChanceExceptionEventArgs.cs" />
    <Compile Include="System\Runtime\ExceptionServices\HandleProcessCorruptedStateExceptionsAttribute.cs" />
    <Compile Include="System\Runtime\GCLargeObjectHeapCompactionMode.cs" />
    <Compile Include="System\Runtime\GCLatencyMode.cs" />
    <Compile Include="System\Runtime\GCSettings.cs" />
    <Compile Include="System\Runtime\Hosting\ActivationArguments.cs" />
    <Compile Include="System\Runtime\Hosting\ApplicationActivator.cs" />
    <Compile Include="System\Runtime\Hosting\ManifestRunner.cs" />
    <Compile Include="System\Runtime\InteropServices\AllowReversePInvokeCallsAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\Architecture.cs" />
    <Compile Include="System\Runtime\InteropServices\ArrayWithOffset.cs" />
    <Compile Include="System\Runtime\InteropServices\AssemblyRegistrationFlags.cs" />
    <Compile Include="System\Runtime\InteropServices\AutomationProxyAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\BestFitMappingAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\BINDPTR.cs" />
    <Compile Include="System\Runtime\InteropServices\BIND_OPTS.cs" />
    <Compile Include="System\Runtime\InteropServices\BStrWrapper.cs" />
    <Compile Include="System\Runtime\InteropServices\CALLCONV.cs" />
    <Compile Include="System\Runtime\InteropServices\CallingConvention.cs" />
    <Compile Include="System\Runtime\InteropServices\CharSet.cs" />
    <Compile Include="System\Runtime\InteropServices\ClassInterfaceAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\ClassInterfaceType.cs" />
    <Compile Include="System\Runtime\InteropServices\CoClassAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\ComAliasNameAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\ComCompatibleVersionAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\ComConversionLossAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\ComDefaultInterfaceAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\ComEventInterfaceAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\ComEventsHelper.cs" />
    <Compile Include="System\Runtime\InteropServices\ComEventsInfo.cs" />
    <Compile Include="System\Runtime\InteropServices\ComEventsMethod.cs" />
    <Compile Include="System\Runtime\InteropServices\ComEventsSink.cs" />
    <Compile Include="System\Runtime\InteropServices\COMException.cs" />
    <Compile Include="System\Runtime\InteropServices\ComImportAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\ComInterfaceType.cs" />
    <Compile Include="System\Runtime\InteropServices\ComMemberType.cs" />
    <Compile Include="System\Runtime\InteropServices\ComRegisterFunctionAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\ComSourceInterfacesAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\BINDPTR.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\BIND_OPTS.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\CALLCONV.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\CONNECTDATA.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\DESCKIND.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\DISPPARAMS.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\ELEMDESC.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\EXCEPINFO.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\FILETIME.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\FUNCDESC.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\FUNCFLAGS.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\FUNCKIND.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IBindCtx.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IConnectionPoint.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IConnectionPointContainer.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IDLDESC.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IDLFLAG.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IEnumConnectionPoints.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IEnumConnections.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IEnumerable.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IEnumerator.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IEnumMoniker.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IEnumString.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IEnumVARIANT.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IExpando.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IMoniker.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IMPLTYPEFLAGS.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\INVOKEKIND.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IPersistFile.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IReflect.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IRunningObjectTable.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\IStream.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\ITypeComp.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\ITypeInfo.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\ITypeInfo2.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\ITypeLib.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\ITypeLib2.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\LIBFLAGS.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\PARAMDESC.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\PARAMFLAG.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\STATSTG.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\SYSKIND.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\TYPEATTR.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\TYPEDESC.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\TYPEFLAGS.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\TYPEKIND.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\TYPELIBATTR.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\VARDESC.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\VARFLAGS.cs" />
    <Compile Include="System\Runtime\InteropServices\ComTypes\VARKIND.cs" />
    <Compile Include="System\Runtime\InteropServices\ComUnregisterFunctionAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\ComVisibleAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\CONNECTDATA.cs" />
    <Compile Include="System\Runtime\InteropServices\CriticalHandle.cs" />
    <Compile Include="System\Runtime\InteropServices\CurrencyWrapper.cs" />
    <Compile Include="System\Runtime\InteropServices\CustomQueryInterfaceMode.cs" />
    <Compile Include="System\Runtime\InteropServices\CustomQueryInterfaceResult.cs" />
    <Compile Include="System\Runtime\InteropServices\DefaultCharSetAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\DefaultDllImportSearchPathsAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\DESCKIND.cs" />
    <Compile Include="System\Runtime\InteropServices\DispatchWrapper.cs" />
    <Compile Include="System\Runtime\InteropServices\DispIdAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\DISPPARAMS.cs" />
    <Compile Include="System\Runtime\InteropServices\DllImportAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\DllImportSearchPath.cs" />
    <Compile Include="System\Runtime\InteropServices\ELEMDESC.cs" />
    <Compile Include="System\Runtime\InteropServices\ErrorWrapper.cs" />
    <Compile Include="System\Runtime\InteropServices\EXCEPINFO.cs" />
    <Compile Include="System\Runtime\InteropServices\Expando\IExpando.cs" />
    <Compile Include="System\Runtime\InteropServices\ExporterEventKind.cs" />
    <Compile Include="System\Runtime\InteropServices\ExtensibleClassFactory.cs" />
    <Compile Include="System\Runtime\InteropServices\ExternalException.cs" />
    <Compile Include="System\Runtime\InteropServices\FieldOffsetAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\FILETIME.cs" />
    <Compile Include="System\Runtime\InteropServices\FUNCDESC.cs" />
    <Compile Include="System\Runtime\InteropServices\FUNCFLAGS.cs" />
    <Compile Include="System\Runtime\InteropServices\FUNCKIND.cs" />
    <Compile Include="System\Runtime\InteropServices\GCHandle.cs" />
    <Compile Include="System\Runtime\InteropServices\GCHandleCookieTable.cs" />
    <Compile Include="System\Runtime\InteropServices\GCHandleType.cs" />
    <Compile Include="System\Runtime\InteropServices\GuidAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\HandleRef.cs" />
    <Compile Include="System\Runtime\InteropServices\ICustomAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\ICustomFactory.cs" />
    <Compile Include="System\Runtime\InteropServices\ICustomMarshaler.cs" />
    <Compile Include="System\Runtime\InteropServices\ICustomQueryInterface.cs" />
    <Compile Include="System\Runtime\InteropServices\IDispatchImplAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\IDispatchImplType.cs" />
    <Compile Include="System\Runtime\InteropServices\IDLDESC.cs" />
    <Compile Include="System\Runtime\InteropServices\IDLFLAG.cs" />
    <Compile Include="System\Runtime\InteropServices\IMPLTYPEFLAGS.cs" />
    <Compile Include="System\Runtime\InteropServices\ImportedFromTypeLibAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\ImporterCallback.cs" />
    <Compile Include="System\Runtime\InteropServices\ImporterEventKind.cs" />
    <Compile Include="System\Runtime\InteropServices\InAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\InterfaceTypeAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\InvalidComObjectException.cs" />
    <Compile Include="System\Runtime\InteropServices\InvalidOleVariantTypeException.cs" />
    <Compile Include="System\Runtime\InteropServices\INVOKEKIND.cs" />
    <Compile Include="System\Runtime\InteropServices\IRegistrationServices.cs" />
    <Compile Include="System\Runtime\InteropServices\ITypeLibConverter.cs" />
    <Compile Include="System\Runtime\InteropServices\ITypeLibExporterNameProvider.cs" />
    <Compile Include="System\Runtime\InteropServices\ITypeLibExporterNotifySink.cs" />
    <Compile Include="System\Runtime\InteropServices\ITypeLibImporterNotifySink.cs" />
    <Compile Include="System\Runtime\InteropServices\LayoutKind.cs" />
    <Compile Include="System\Runtime\InteropServices\LCIDConversionAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\LIBFLAGS.cs" />
    <Compile Include="System\Runtime\InteropServices\ManagedToNativeComInteropStubAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\Marshal.cs" />
    <Compile Include="System\Runtime\InteropServices\MarshalAsAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\MarshalDirectiveException.cs" />
    <Compile Include="System\Runtime\InteropServices\NativeBuffer.cs" />
    <Compile Include="System\Runtime\InteropServices\NativeMethods.cs" />
    <Compile Include="System\Runtime\InteropServices\ObjectCreationDelegate.cs" />
    <Compile Include="System\Runtime\InteropServices\OptionalAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\OSPlatform.cs" />
    <Compile Include="System\Runtime\InteropServices\OutAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\PARAMDESC.cs" />
    <Compile Include="System\Runtime\InteropServices\PARAMFLAG.cs" />
    <Compile Include="System\Runtime\InteropServices\PInvokeMap.cs" />
    <Compile Include="System\Runtime\InteropServices\PreserveSigAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\PrimaryInteropAssemblyAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\ProgIdAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\RegistrationClassContext.cs" />
    <Compile Include="System\Runtime\InteropServices\RegistrationConnectionType.cs" />
    <Compile Include="System\Runtime\InteropServices\RegistrationServices.cs" />
    <Compile Include="System\Runtime\InteropServices\RuntimeEnvironment.cs" />
    <Compile Include="System\Runtime\InteropServices\RuntimeInformation.cs" />
    <Compile Include="System\Runtime\InteropServices\SafeArrayRankMismatchException.cs" />
    <Compile Include="System\Runtime\InteropServices\SafeArrayTypeMismatchException.cs" />
    <Compile Include="System\Runtime\InteropServices\SafeBuffer.cs" />
    <Compile Include="System\Runtime\InteropServices\SafeHandle.cs" />
    <Compile Include="System\Runtime\InteropServices\SafeHeapHandle.cs" />
    <Compile Include="System\Runtime\InteropServices\SafeHeapHandleCache.cs" />
    <Compile Include="System\Runtime\InteropServices\SEHException.cs" />
    <Compile Include="System\Runtime\InteropServices\SetWin32ContextInIDispatchAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\STATSTG.cs" />
    <Compile Include="System\Runtime\InteropServices\StringBuffer.cs" />
    <Compile Include="System\Runtime\InteropServices\StructLayoutAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\SYSKIND.cs" />
    <Compile Include="System\Runtime\InteropServices\TCEAdapterGen\EventItfInfo.cs" />
    <Compile Include="System\Runtime\InteropServices\TCEAdapterGen\EventProviderWriter.cs" />
    <Compile Include="System\Runtime\InteropServices\TCEAdapterGen\EventSinkHelperWriter.cs" />
    <Compile Include="System\Runtime\InteropServices\TCEAdapterGen\NameSpaceExtractor.cs" />
    <Compile Include="System\Runtime\InteropServices\TCEAdapterGen\TCEAdapterGenerator.cs" />
    <Compile Include="System\Runtime\InteropServices\TYPEATTR.cs" />
    <Compile Include="System\Runtime\InteropServices\TYPEDESC.cs" />
    <Compile Include="System\Runtime\InteropServices\TYPEFLAGS.cs" />
    <Compile Include="System\Runtime\InteropServices\TypeIdentifierAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\TYPEKIND.cs" />
    <Compile Include="System\Runtime\InteropServices\TYPELIBATTR.cs" />
    <Compile Include="System\Runtime\InteropServices\TypeLibConverter.cs" />
    <Compile Include="System\Runtime\InteropServices\TypeLibExporterFlags.cs" />
    <Compile Include="System\Runtime\InteropServices\TypeLibFuncAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\TypeLibFuncFlags.cs" />
    <Compile Include="System\Runtime\InteropServices\TypeLibImportClassAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\TypeLibImporterFlags.cs" />
    <Compile Include="System\Runtime\InteropServices\TypeLibTypeAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\TypeLibTypeFlags.cs" />
    <Compile Include="System\Runtime\InteropServices\TypeLibVarAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\TypeLibVarFlags.cs" />
    <Compile Include="System\Runtime\InteropServices\TypeLibVersionAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIBindCtx.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIConnectionPoint.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIConnectionPointContainer.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIEnumConnectionPoints.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIEnumConnections.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIEnumerable.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIEnumerator.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIEnumMoniker.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIEnumString.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIEnumVARIANT.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIExpando.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIMoniker.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIPersistFile.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIReflect.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIRunningObjectTable.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMIStream.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMITypeComp.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMITypeInfo.cs" />
    <Compile Include="System\Runtime\InteropServices\UCOMITypeLib.cs" />
    <Compile Include="System\Runtime\InteropServices\UnknownWrapper.cs" />
    <Compile Include="System\Runtime\InteropServices\UnmanagedFunctionPointerAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\UnmanagedType.cs" />
    <Compile Include="System\Runtime\InteropServices\VARDESC.cs" />
    <Compile Include="System\Runtime\InteropServices\VarEnum.cs" />
    <Compile Include="System\Runtime\InteropServices\VARFLAGS.cs" />
    <Compile Include="System\Runtime\InteropServices\Variant.cs" />
    <Compile Include="System\Runtime\InteropServices\VariantWrapper.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\BindableIterableToEnumerableAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\BindableVectorToCollectionAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\BindableVectorToListAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\CLRIKeyValuePairImpl.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\CLRIPropertyValueImpl.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\CLRIReferenceArrayImpl.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\CLRIReferenceImpl.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ConstantSplittableMap.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\CustomPropertyImpl.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\DefaultInterfaceAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\DesignerNamespaceResolveEventArgs.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\DictionaryKeyCollection.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\DictionaryKeyEnumerator.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\DictionaryToMapAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\DictionaryValueCollection.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\DictionaryValueEnumerator.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\EnumerableToBindableIterableAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\EnumerableToIterableAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\EnumeratorToIteratorAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\EventRegistrationToken.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\EventRegistrationTokenTable.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\GetEnumerator_Delegate.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\HSTRING_HEADER.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IActivationFactory.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IBindableIterable.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IBindableIterator.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IBindableVector.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IBindableVectorView.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IClosable.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IClosableToIDisposableAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ICustomProperty.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ICustomPropertyProvider.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ICustomPropertyProviderImpl.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ICustomPropertyProviderProxy.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IDisposableToIClosableAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IGetProxyTarget.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IIterable.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IIterator.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IKeyValuePair.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IManagedActivationFactory.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IMap.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IMapView.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IMapViewToIReadOnlyDictionaryAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\Indexer_Get_Delegate.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\InterfaceForwardingSupport.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\InterfaceImplementedInVersionAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IPropertyValue.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IReadOnlyDictionaryToIMapViewAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IReadOnlyListToIVectorViewAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IReference.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IReferenceArray.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IReferenceFactory.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IRestrictedErrorInfo.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IStringable.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IStringableHelper.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IterableToEnumerableAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IteratorToEnumeratorAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IVector.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IVectorView.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IVectorViewToIReadOnlyListAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IVector_Raw.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\IWinRTClassActivator.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ListToBindableVectorAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ListToBindableVectorViewAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ListToVectorAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ManagedActivationFactory.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\MapToCollectionAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\MapToDictionaryAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\MapViewToReadOnlyCollectionAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\NamespaceResolveEventArgs.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\Point.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\PropertyType.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ReadOnlyArrayAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ReadOnlyDictionaryKeyCollection.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ReadOnlyDictionaryKeyEnumerator.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ReadOnlyDictionaryValueCollection.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ReadOnlyDictionaryValueEnumerator.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\Rect.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\ReturnValueNameAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\RuntimeClass.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\Size.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\UnsafeNativeMethods.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\VectorToCollectionAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\VectorToListAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\VectorViewToReadOnlyCollectionAdapter.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\WindowsFoundationEventHandler.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\WindowsRuntimeBufferHelper.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\WindowsRuntimeImportAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\WindowsRuntimeMarshal.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\WindowsRuntimeMetadata.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\WinRTClassActivator.cs" />
    <Compile Include="System\Runtime\InteropServices\WindowsRuntime\WriteOnlyArrayAttribute.cs" />
    <Compile Include="System\Runtime\InteropServices\_Activator.cs" />
    <Compile Include="System\Runtime\InteropServices\_Assembly.cs" />
    <Compile Include="System\Runtime\InteropServices\_AssemblyBuilder.cs" />
    <Compile Include="System\Runtime\InteropServices\_AssemblyName.cs" />
    <Compile Include="System\Runtime\InteropServices\_Attribute.cs" />
    <Compile Include="System\Runtime\InteropServices\_ConstructorBuilder.cs" />
    <Compile Include="System\Runtime\InteropServices\_ConstructorInfo.cs" />
    <Compile Include="System\Runtime\InteropServices\_CustomAttributeBuilder.cs" />
    <Compile Include="System\Runtime\InteropServices\_EnumBuilder.cs" />
    <Compile Include="System\Runtime\InteropServices\_EventBuilder.cs" />
    <Compile Include="System\Runtime\InteropServices\_EventInfo.cs" />
    <Compile Include="System\Runtime\InteropServices\_Exception.cs" />
    <Compile Include="System\Runtime\InteropServices\_FieldBuilder.cs" />
    <Compile Include="System\Runtime\InteropServices\_FieldInfo.cs" />
    <Compile Include="System\Runtime\InteropServices\_ILGenerator.cs" />
    <Compile Include="System\Runtime\InteropServices\_LocalBuilder.cs" />
    <Compile Include="System\Runtime\InteropServices\_MemberInfo.cs" />
    <Compile Include="System\Runtime\InteropServices\_MethodBase.cs" />
    <Compile Include="System\Runtime\InteropServices\_MethodBuilder.cs" />
    <Compile Include="System\Runtime\InteropServices\_MethodInfo.cs" />
    <Compile Include="System\Runtime\InteropServices\_MethodRental.cs" />
    <Compile Include="System\Runtime\InteropServices\_Module.cs" />
    <Compile Include="System\Runtime\InteropServices\_ModuleBuilder.cs" />
    <Compile Include="System\Runtime\InteropServices\_ParameterBuilder.cs" />
    <Compile Include="System\Runtime\InteropServices\_ParameterInfo.cs" />
    <Compile Include="System\Runtime\InteropServices\_PropertyBuilder.cs" />
    <Compile Include="System\Runtime\InteropServices\_PropertyInfo.cs" />
    <Compile Include="System\Runtime\InteropServices\_SignatureHelper.cs" />
    <Compile Include="System\Runtime\InteropServices\_Thread.cs" />
    <Compile Include="System\Runtime\InteropServices\_Type.cs" />
    <Compile Include="System\Runtime\InteropServices\_TypeBuilder.cs" />
    <Compile Include="System\Runtime\MemoryFailPoint.cs" />
    <Compile Include="System\Runtime\ProfileOptimization.cs" />
    <Compile Include="System\Runtime\Remoting\ActivatedClientTypeEntry.cs" />
    <Compile Include="System\Runtime\Remoting\ActivatedServiceTypeEntry.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\ActivationAttributeStack.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\ActivationListener.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\ActivationServices.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\ActivatorLevel.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\AppDomainLevelActivator.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\ConstructionLevelActivator.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\ContextLevelActivator.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\IActivator.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\IConstructionCallMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\IConstructionReturnMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\LocalActivator.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\RemotePropertyHolderAttribute.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\RemotingXmlConfigFileData.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\RemotingXmlConfigFileParser.cs" />
    <Compile Include="System\Runtime\Remoting\Activation\UrlAttribute.cs" />
    <Compile Include="System\Runtime\Remoting\ChannelInfo.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\ADAsyncWorkItem.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\AggregateDictionary.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\AsyncMessageHelper.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\AsyncWorkItem.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\BaseChannelObjectWithProperties.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\BaseChannelSinkWithProperties.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\BaseChannelWithProperties.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\ChannelDataStore.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\ChannelServices.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\ChannelServicesData.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\ClientChannelSinkStack.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\CrossAppDomainChannel.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\CrossAppDomainData.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\CrossAppDomainSerializer.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\CrossAppDomainSink.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\CrossContextChannel.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\DictionaryEnumeratorByKeys.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\DispatchChannelSink.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\DispatchChannelSinkProvider.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IChannel.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IChannelDataStore.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IChannelReceiver.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IChannelReceiverHook.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IChannelSender.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IChannelSinkBase.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IClientChannelSink.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IClientChannelSinkProvider.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IClientChannelSinkStack.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IClientFormatterSink.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IClientFormatterSinkProvider.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IClientResponseChannelSinkStack.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\ISecurableChannel.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IServerChannelSink.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IServerChannelSinkProvider.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IServerChannelSinkStack.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IServerFormatterSinkProvider.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\IServerResponseChannelSinkStack.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\ITransportHeaders.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\Perf_Contexts.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\RegisteredChannel.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\RegisteredChannelList.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\RemotingProfilerEvent.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\ServerAsyncReplyTerminatorSink.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\ServerChannelSinkStack.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\ServerProcessing.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\SinkProviderData.cs" />
    <Compile Include="System\Runtime\Remoting\Channels\TransportHeaders.cs" />
    <Compile Include="System\Runtime\Remoting\ComRedirectionProxy.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\ArrayWithSize.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\CallBackHelper.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\Context.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\ContextAttribute.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\ContextProperty.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\CrossContextDelegate.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\DynamicPropertyHolder.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\IContextAttribute.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\IContextProperty.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\IContextPropertyActivator.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\IContributeClientContextSink.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\IContributeDynamicSink.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\IContributeEnvoySink.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\IContributeObjectSink.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\IContributeServerContextSink.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\IDynamicMessageSink.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\IDynamicProperty.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\SynchronizationAttribute.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\SynchronizedClientContextSink.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\SynchronizedServerContextSink.cs" />
    <Compile Include="System\Runtime\Remoting\Contexts\WorkItem.cs" />
    <Compile Include="System\Runtime\Remoting\CustomErrorsModes.cs" />
    <Compile Include="System\Runtime\Remoting\DelayLoadClientChannelEntry.cs" />
    <Compile Include="System\Runtime\Remoting\DomainSpecificRemotingData.cs" />
    <Compile Include="System\Runtime\Remoting\DuplicateIdentityOption.cs" />
    <Compile Include="System\Runtime\Remoting\DynamicTypeInfo.cs" />
    <Compile Include="System\Runtime\Remoting\EnvoyInfo.cs" />
    <Compile Include="System\Runtime\Remoting\IChannelInfo.cs" />
    <Compile Include="System\Runtime\Remoting\Identity.cs" />
    <Compile Include="System\Runtime\Remoting\IdentityHolder.cs" />
    <Compile Include="System\Runtime\Remoting\IdOps.cs" />
    <Compile Include="System\Runtime\Remoting\IEnvoyInfo.cs" />
    <Compile Include="System\Runtime\Remoting\InternalRemotingServices.cs" />
    <Compile Include="System\Runtime\Remoting\IObjectHandle.cs" />
    <Compile Include="System\Runtime\Remoting\IRemotingTypeInfo.cs" />
    <Compile Include="System\Runtime\Remoting\Lifetime\ClientSponsor.cs" />
    <Compile Include="System\Runtime\Remoting\Lifetime\ILease.cs" />
    <Compile Include="System\Runtime\Remoting\Lifetime\ISponsor.cs" />
    <Compile Include="System\Runtime\Remoting\Lifetime\Lease.cs" />
    <Compile Include="System\Runtime\Remoting\Lifetime\LeaseLifeTimeServiceProperty.cs" />
    <Compile Include="System\Runtime\Remoting\Lifetime\LeaseManager.cs" />
    <Compile Include="System\Runtime\Remoting\Lifetime\LeaseSink.cs" />
    <Compile Include="System\Runtime\Remoting\Lifetime\LeaseState.cs" />
    <Compile Include="System\Runtime\Remoting\Lifetime\LifetimeServices.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ArgMapper.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\AsyncReplySink.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\AsyncResult.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\CallContext.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\CallContextRemotingData.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\CallContextSecurityData.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\CCMDictionary.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ClientAsyncReplyTerminatorSink.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ClientContextTerminatorSink.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ConstructionCall.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ConstructionResponse.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ConstructorCallMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ConstructorReturnMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\CRMDictionary.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\DisposeSink.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\EnvoyTerminatorSink.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ErrorMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\Header.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\HeaderHandler.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\IInternalMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\IllogicalCallContext.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ILogicalThreadAffinative.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\IMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\IMessageCtrl.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\IMessageSink.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\IMethodCallMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\IMethodMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\IMethodReturnMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\InternalMessageWrapper.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\InternalSink.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\IRemotingFormatter.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ISerializationRootObject.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\LogicalCallContext.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\MCMDictionary.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\Message.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\MessageDictionary.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\MessageDictionaryEnumerator.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\MessageSmuggler.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\MessageSurrogate.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\MessageSurrogateFilter.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\MethodCall.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\MethodCallMessageWrapper.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\MethodResponse.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\MethodReturnMessageWrapper.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\MRMDictionary.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ObjRefSurrogate.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\OneWayAttribute.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\RemotingSurrogate.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\RemotingSurrogateSelector.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ReturnMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\SerializationMonkey.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ServerContextTerminatorSink.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\ServerObjectTerminatorSink.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\SmuggledMethodCallMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\SmuggledMethodReturnMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\SmuggledObjRef.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\SoapMessageSurrogate.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\StackBasedReturnMessage.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\StackBuilderSink.cs" />
    <Compile Include="System\Runtime\Remoting\Messaging\TransitionCall.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\RemotingCachedData.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\RemotingFieldCachedData.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\RemotingMethodCachedData.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\RemotingParameterCachedData.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\RemotingTypeCachedData.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\SoapAttribute.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\SoapFieldAttribute.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\SoapMethodAttribute.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\SoapOption.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\SoapParameterAttribute.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\SoapTypeAttribute.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\ISoapXsd.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapAnyUri.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapBase64Binary.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapDate.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapDateTime.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapDay.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapDuration.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapEntities.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapEntity.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapHexBinary.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapId.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapIdref.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapIdrefs.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapInteger.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapLanguage.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapMonth.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapMonthDay.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapName.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapNcName.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapNegativeInteger.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapNmtoken.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapNmtokens.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapNonNegativeInteger.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapNonPositiveInteger.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapNormalizedString.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapNotation.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapPositiveInteger.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapQName.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapTime.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapToken.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapType.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapYear.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\W3cXsd2001\SoapYearMonth.cs" />
    <Compile Include="System\Runtime\Remoting\Metadata\XmlFieldOrderOption.cs" />
    <Compile Include="System\Runtime\Remoting\ObjectHandle.cs" />
    <Compile Include="System\Runtime\Remoting\ObjRef.cs" />
    <Compile Include="System\Runtime\Remoting\Proxies\AgileAsyncWorkerItem.cs" />
    <Compile Include="System\Runtime\Remoting\Proxies\CallType.cs" />
    <Compile Include="System\Runtime\Remoting\Proxies\MessageData.cs" />
    <Compile Include="System\Runtime\Remoting\Proxies\ProxyAttribute.cs" />
    <Compile Include="System\Runtime\Remoting\Proxies\RealProxy.cs" />
    <Compile Include="System\Runtime\Remoting\Proxies\RealProxyFlags.cs" />
    <Compile Include="System\Runtime\Remoting\Proxies\RemotingProxy.cs" />
    <Compile Include="System\Runtime\Remoting\Proxies\__TransparentProxy.cs" />
    <Compile Include="System\Runtime\Remoting\RedirectionProxy.cs" />
    <Compile Include="System\Runtime\Remoting\RemoteAppEntry.cs" />
    <Compile Include="System\Runtime\Remoting\RemotingConfigHandler.cs" />
    <Compile Include="System\Runtime\Remoting\RemotingConfiguration.cs" />
    <Compile Include="System\Runtime\Remoting\RemotingException.cs" />
    <Compile Include="System\Runtime\Remoting\RemotingServices.cs" />
    <Compile Include="System\Runtime\Remoting\RemotingTimeoutException.cs" />
    <Compile Include="System\Runtime\Remoting\ServerException.cs" />
    <Compile Include="System\Runtime\Remoting\ServerIdentity.cs" />
    <Compile Include="System\Runtime\Remoting\Services\EnterpriseServicesHelper.cs" />
    <Compile Include="System\Runtime\Remoting\Services\ITrackingHandler.cs" />
    <Compile Include="System\Runtime\Remoting\Services\TrackingServices.cs" />
    <Compile Include="System\Runtime\Remoting\SoapServices.cs" />
    <Compile Include="System\Runtime\Remoting\TypeEntry.cs" />
    <Compile Include="System\Runtime\Remoting\TypeInfo.cs" />
    <Compile Include="System\Runtime\Remoting\WellKnownClientTypeEntry.cs" />
    <Compile Include="System\Runtime\Remoting\WellKnownObjectMode.cs" />
    <Compile Include="System\Runtime\Remoting\WellKnownServiceTypeEntry.cs" />
    <Compile Include="System\Runtime\Remoting\XmlNamespaceEncoder.cs" />
    <Compile Include="System\Runtime\Remoting\__HResults.cs" />
    <Compile Include="System\Runtime\Serialization\DeserializationEventHandler.cs" />
    <Compile Include="System\Runtime\Serialization\FixupHolder.cs" />
    <Compile Include="System\Runtime\Serialization\FixupHolderList.cs" />
    <Compile Include="System\Runtime\Serialization\Formatter.cs" />
    <Compile Include="System\Runtime\Serialization\FormatterConverter.cs" />
    <Compile Include="System\Runtime\Serialization\FormatterServices.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryArray.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryArrayTypeEnum.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryAssembly.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryAssemblyInfo.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryConverter.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryCrossAppDomainAssembly.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryCrossAppDomainMap.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryCrossAppDomainString.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryFormatter.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryHeaderEnum.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryMethodCall.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryMethodCallMessage.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryMethodReturn.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryMethodReturnMessage.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryObject.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryObjectString.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryObjectWithMap.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryObjectWithMapTyped.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryTypeEnum.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\BinaryUtil.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\Converter.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\InternalArrayTypeE.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\InternalElementTypeE.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\InternalFE.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\InternalMemberTypeE.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\InternalMemberValueE.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\InternalNameSpaceE.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\InternalObjectPositionE.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\InternalObjectTypeE.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\InternalParseStateE.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\InternalParseTypeE.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\InternalPrimitiveTypeE.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\InternalSerializerTypeE.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\IntSizedArray.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\IOUtil.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\IStreamable.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\MemberPrimitiveTyped.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\MemberPrimitiveUnTyped.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\MemberReference.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\MessageEnd.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\MessageEnum.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\NameCache.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\NameInfo.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\ObjectMap.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\ObjectMapInfo.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\ObjectNull.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\ObjectProgress.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\ObjectReader.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\ObjectWriter.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\ParseRecord.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\PrimitiveArray.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\ReadObjectInfo.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\SerializationHeaderRecord.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\SerObjectInfoCache.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\SerObjectInfoInit.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\SerStack.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\SizedArray.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\SoapAttributeType.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\TypeInformation.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\ValueFixup.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\ValueFixupEnum.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\WriteObjectInfo.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\__BinaryParser.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\Binary\__BinaryWriter.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\FormatterAssemblyStyle.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\FormatterTypeStyle.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\IFieldInfo.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\InternalRM.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\InternalST.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\ISoapMessage.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\SerTrace.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\ServerFault.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\SoapFault.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\SoapMessage.cs" />
    <Compile Include="System\Runtime\Serialization\Formatters\TypeFilterLevel.cs" />
    <Compile Include="System\Runtime\Serialization\IDeserializationCallback.cs" />
    <Compile Include="System\Runtime\Serialization\IFormatter.cs" />
    <Compile Include="System\Runtime\Serialization\IFormatterConverter.cs" />
    <Compile Include="System\Runtime\Serialization\IObjectReference.cs" />
    <Compile Include="System\Runtime\Serialization\ISafeSerializationData.cs" />
    <Compile Include="System\Runtime\Serialization\ISerializable.cs" />
    <Compile Include="System\Runtime\Serialization\ISerializationSurrogate.cs" />
    <Compile Include="System\Runtime\Serialization\ISurrogateSelector.cs" />
    <Compile Include="System\Runtime\Serialization\LongList.cs" />
    <Compile Include="System\Runtime\Serialization\MemberHolder.cs" />
    <Compile Include="System\Runtime\Serialization\ObjectCloneHelper.cs" />
    <Compile Include="System\Runtime\Serialization\ObjectHolder.cs" />
    <Compile Include="System\Runtime\Serialization\ObjectHolderList.cs" />
    <Compile Include="System\Runtime\Serialization\ObjectHolderListEnumerator.cs" />
    <Compile Include="System\Runtime\Serialization\ObjectIDGenerator.cs" />
    <Compile Include="System\Runtime\Serialization\ObjectManager.cs" />
    <Compile Include="System\Runtime\Serialization\OnDeserializedAttribute.cs" />
    <Compile Include="System\Runtime\Serialization\OnDeserializingAttribute.cs" />
    <Compile Include="System\Runtime\Serialization\OnSerializedAttribute.cs" />
    <Compile Include="System\Runtime\Serialization\OnSerializingAttribute.cs" />
    <Compile Include="System\Runtime\Serialization\OptionalFieldAttribute.cs" />
    <Compile Include="System\Runtime\Serialization\SafeSerializationEventArgs.cs" />
    <Compile Include="System\Runtime\Serialization\SafeSerializationManager.cs" />
    <Compile Include="System\Runtime\Serialization\SerializationBinder.cs" />
    <Compile Include="System\Runtime\Serialization\SerializationEntry.cs" />
    <Compile Include="System\Runtime\Serialization\SerializationEventHandler.cs" />
    <Compile Include="System\Runtime\Serialization\SerializationEvents.cs" />
    <Compile Include="System\Runtime\Serialization\SerializationEventsCache.cs" />
    <Compile Include="System\Runtime\Serialization\SerializationException.cs" />
    <Compile Include="System\Runtime\Serialization\SerializationFieldInfo.cs" />
    <Compile Include="System\Runtime\Serialization\SerializationInfo.cs" />
    <Compile Include="System\Runtime\Serialization\SerializationInfoEnumerator.cs" />
    <Compile Include="System\Runtime\Serialization\SerializationObjectManager.cs" />
    <Compile Include="System\Runtime\Serialization\StreamingContext.cs" />
    <Compile Include="System\Runtime\Serialization\StreamingContextStates.cs" />
    <Compile Include="System\Runtime\Serialization\SurrogateForCyclicalReference.cs" />
    <Compile Include="System\Runtime\Serialization\SurrogateHashtable.cs" />
    <Compile Include="System\Runtime\Serialization\SurrogateKey.cs" />
    <Compile Include="System\Runtime\Serialization\SurrogateSelector.cs" />
    <Compile Include="System\Runtime\Serialization\TypeLoadExceptionHolder.cs" />
    <Compile Include="System\Runtime\Serialization\ValueTypeFixupInfo.cs" />
    <Compile Include="System\Runtime\TargetedPatchingOptOutAttribute.cs" />
    <Compile Include="System\Runtime\Versioning\BinaryCompatibility.cs" />
    <Compile Include="System\Runtime\Versioning\CompatibilitySwitch.cs" />
    <Compile Include="System\Runtime\Versioning\ComponentGuaranteesAttribute.cs" />
    <Compile Include="System\Runtime\Versioning\ComponentGuaranteesOptions.cs" />
    <Compile Include="System\Runtime\Versioning\MultitargetingHelpers.cs" />
    <Compile Include="System\Runtime\Versioning\NonVersionableAttribute.cs" />
    <Compile Include="System\Runtime\Versioning\ResourceConsumptionAttribute.cs" />
    <Compile Include="System\Runtime\Versioning\ResourceExposureAttribute.cs" />
    <Compile Include="System\Runtime\Versioning\ResourceScope.cs" />
    <Compile Include="System\Runtime\Versioning\SxSRequirements.cs" />
    <Compile Include="System\Runtime\Versioning\TargetFrameworkAttribute.cs" />
    <Compile Include="System\Runtime\Versioning\TargetFrameworkId.cs" />
    <Compile Include="System\Runtime\Versioning\VersioningHelper.cs" />
    <Compile Include="System\SafeTypeNameParserHandle.cs" />
    <Compile Include="System\SByte.cs" />
    <Compile Include="System\Security\AccessControl\AccessControlActions.cs" />
    <Compile Include="System\Security\AccessControl\AccessControlModification.cs" />
    <Compile Include="System\Security\AccessControl\AccessControlSections.cs" />
    <Compile Include="System\Security\AccessControl\AccessControlType.cs" />
    <Compile Include="System\Security\AccessControl\AccessRule.2.cs" />
    <Compile Include="System\Security\AccessControl\AccessRule.cs" />
    <Compile Include="System\Security\AccessControl\AceEnumerator.cs" />
    <Compile Include="System\Security\AccessControl\AceFlags.cs" />
    <Compile Include="System\Security\AccessControl\AceQualifier.cs" />
    <Compile Include="System\Security\AccessControl\AceType.cs" />
    <Compile Include="System\Security\AccessControl\AuditFlags.cs" />
    <Compile Include="System\Security\AccessControl\AuditRule.2.cs" />
    <Compile Include="System\Security\AccessControl\AuditRule.cs" />
    <Compile Include="System\Security\AccessControl\AuthorizationRule.cs" />
    <Compile Include="System\Security\AccessControl\AuthorizationRuleCollection.cs" />
    <Compile Include="System\Security\AccessControl\CommonAce.cs" />
    <Compile Include="System\Security\AccessControl\CommonAcl.cs" />
    <Compile Include="System\Security\AccessControl\CommonObjectSecurity.cs" />
    <Compile Include="System\Security\AccessControl\CommonSecurityDescriptor.cs" />
    <Compile Include="System\Security\AccessControl\CompoundAce.cs" />
    <Compile Include="System\Security\AccessControl\CompoundAceType.cs" />
    <Compile Include="System\Security\AccessControl\ControlFlags.cs" />
    <Compile Include="System\Security\AccessControl\CryptoKeyAccessRule.cs" />
    <Compile Include="System\Security\AccessControl\CryptoKeyAuditRule.cs" />
    <Compile Include="System\Security\AccessControl\CryptoKeyRights.cs" />
    <Compile Include="System\Security\AccessControl\CryptoKeySecurity.cs" />
    <Compile Include="System\Security\AccessControl\CustomAce.cs" />
    <Compile Include="System\Security\AccessControl\DirectoryObjectSecurity.cs" />
    <Compile Include="System\Security\AccessControl\DirectorySecurity.cs" />
    <Compile Include="System\Security\AccessControl\DiscretionaryAcl.cs" />
    <Compile Include="System\Security\AccessControl\EventWaitHandleAccessRule.cs" />
    <Compile Include="System\Security\AccessControl\EventWaitHandleAuditRule.cs" />
    <Compile Include="System\Security\AccessControl\EventWaitHandleRights.cs" />
    <Compile Include="System\Security\AccessControl\EventWaitHandleSecurity.cs" />
    <Compile Include="System\Security\AccessControl\FileSecurity.cs" />
    <Compile Include="System\Security\AccessControl\FileSystemAccessRule.cs" />
    <Compile Include="System\Security\AccessControl\FileSystemAuditRule.cs" />
    <Compile Include="System\Security\AccessControl\FileSystemRights.cs" />
    <Compile Include="System\Security\AccessControl\FileSystemSecurity.cs" />
    <Compile Include="System\Security\AccessControl\GenericAce.cs" />
    <Compile Include="System\Security\AccessControl\GenericAcl.cs" />
    <Compile Include="System\Security\AccessControl\GenericSecurityDescriptor.cs" />
    <Compile Include="System\Security\AccessControl\InheritanceFlags.cs" />
    <Compile Include="System\Security\AccessControl\KnownAce.cs" />
    <Compile Include="System\Security\AccessControl\MutexAccessRule.cs" />
    <Compile Include="System\Security\AccessControl\MutexAuditRule.cs" />
    <Compile Include="System\Security\AccessControl\MutexRights.cs" />
    <Compile Include="System\Security\AccessControl\MutexSecurity.cs" />
    <Compile Include="System\Security\AccessControl\NativeObjectSecurity.cs" />
    <Compile Include="System\Security\AccessControl\ObjectAccessRule.cs" />
    <Compile Include="System\Security\AccessControl\ObjectAce.cs" />
    <Compile Include="System\Security\AccessControl\ObjectAceFlags.cs" />
    <Compile Include="System\Security\AccessControl\ObjectAuditRule.cs" />
    <Compile Include="System\Security\AccessControl\ObjectSecurity.2.cs" />
    <Compile Include="System\Security\AccessControl\ObjectSecurity.cs" />
    <Compile Include="System\Security\AccessControl\Privilege.cs" />
    <Compile Include="System\Security\AccessControl\PrivilegeNotHeldException.cs" />
    <Compile Include="System\Security\AccessControl\PropagationFlags.cs" />
    <Compile Include="System\Security\AccessControl\QualifiedAce.cs" />
    <Compile Include="System\Security\AccessControl\RawAcl.cs" />
    <Compile Include="System\Security\AccessControl\RawSecurityDescriptor.cs" />
    <Compile Include="System\Security\AccessControl\RegistryAccessRule.cs" />
    <Compile Include="System\Security\AccessControl\RegistryAuditRule.cs" />
    <Compile Include="System\Security\AccessControl\RegistryRights.cs" />
    <Compile Include="System\Security\AccessControl\RegistrySecurity.cs" />
    <Compile Include="System\Security\AccessControl\ResourceType.cs" />
    <Compile Include="System\Security\AccessControl\SecurityInfos.cs" />
    <Compile Include="System\Security\AccessControl\SystemAcl.cs" />
    <Compile Include="System\Security\AccessControl\Win32.cs" />
    <Compile Include="System\Security\AllowPartiallyTrustedCallersAttribute.cs" />
    <Compile Include="System\Security\BuiltInPermissionSets.cs" />
    <Compile Include="System\Security\Claims\Claim.cs" />
    <Compile Include="System\Security\Claims\ClaimsIdentity.cs" />
    <Compile Include="System\Security\Claims\ClaimsPrincipal.cs" />
    <Compile Include="System\Security\Claims\ClaimTypes.cs" />
    <Compile Include="System\Security\Claims\ClaimValueTypes.cs" />
    <Compile Include="System\Security\Claims\RoleClaimProvider.cs" />
    <Compile Include="System\Security\CodeAccessPermission.cs" />
    <Compile Include="System\Security\CodeAccessSecurityEngine.cs" />
    <Compile Include="System\Security\Cryptography\Aes.cs" />
    <Compile Include="System\Security\Cryptography\AsymmetricAlgorithm.cs" />
    <Compile Include="System\Security\Cryptography\AsymmetricKeyExchangeDeformatter.cs" />
    <Compile Include="System\Security\Cryptography\AsymmetricKeyExchangeFormatter.cs" />
    <Compile Include="System\Security\Cryptography\AsymmetricSignatureDeformatter.cs" />
    <Compile Include="System\Security\Cryptography\AsymmetricSignatureFormatter.cs" />
    <Compile Include="System\Security\Cryptography\CapiNative.cs" />
    <Compile Include="System\Security\Cryptography\CipherMode.cs" />
    <Compile Include="System\Security\Cryptography\CngHashAlgorithmFactory.cs" />
    <Compile Include="System\Security\Cryptography\Constants.cs" />
    <Compile Include="System\Security\Cryptography\CryptoAPITransform.cs" />
    <Compile Include="System\Security\Cryptography\CryptoAPITransformMode.cs" />
    <Compile Include="System\Security\Cryptography\CryptoConfig.cs" />
    <Compile Include="System\Security\Cryptography\CryptographicException.cs" />
    <Compile Include="System\Security\Cryptography\CryptographicUnexpectedOperationException.cs" />
    <Compile Include="System\Security\Cryptography\CryptoStream.cs" />
    <Compile Include="System\Security\Cryptography\CryptoStreamMode.cs" />
    <Compile Include="System\Security\Cryptography\CspAlgorithmType.cs" />
    <Compile Include="System\Security\Cryptography\CspKeyContainerInfo.cs" />
    <Compile Include="System\Security\Cryptography\CspParameters.cs" />
    <Compile Include="System\Security\Cryptography\CspProviderFlags.cs" />
    <Compile Include="System\Security\Cryptography\DeriveBytes.cs" />
    <Compile Include="System\Security\Cryptography\DES.cs" />
    <Compile Include="System\Security\Cryptography\DESCryptoServiceProvider.cs" />
    <Compile Include="System\Security\Cryptography\DSA.cs" />
    <Compile Include="System\Security\Cryptography\DSACryptoServiceProvider.cs" />
    <Compile Include="System\Security\Cryptography\DSACspObject.cs" />
    <Compile Include="System\Security\Cryptography\DSAParameters.cs" />
    <Compile Include="System\Security\Cryptography\DSASignatureDeformatter.cs" />
    <Compile Include="System\Security\Cryptography\DSASignatureDescription.cs" />
    <Compile Include="System\Security\Cryptography\DSASignatureFormatter.cs" />
    <Compile Include="System\Security\Cryptography\FromBase64Transform.cs" />
    <Compile Include="System\Security\Cryptography\FromBase64TransformMode.cs" />
    <Compile Include="System\Security\Cryptography\HashAlgorithm.cs" />
    <Compile Include="System\Security\Cryptography\HashAlgorithmName.cs" />
    <Compile Include="System\Security\Cryptography\HMAC.cs" />
    <Compile Include="System\Security\Cryptography\HMACMD5.cs" />
    <Compile Include="System\Security\Cryptography\HMACRIPEMD160.cs" />
    <Compile Include="System\Security\Cryptography\HMACSHA1.cs" />
    <Compile Include="System\Security\Cryptography\HMACSHA256.cs" />
    <Compile Include="System\Security\Cryptography\HMACSHA384.cs" />
    <Compile Include="System\Security\Cryptography\HMACSHA512.cs" />
    <Compile Include="System\Security\Cryptography\ICryptoTransform.cs" />
    <Compile Include="System\Security\Cryptography\ICspAsymmetricAlgorithm.cs" />
    <Compile Include="System\Security\Cryptography\KeyedHashAlgorithm.cs" />
    <Compile Include="System\Security\Cryptography\KeyNumber.cs" />
    <Compile Include="System\Security\Cryptography\KeySizes.cs" />
    <Compile Include="System\Security\Cryptography\MACTripleDES.cs" />
    <Compile Include="System\Security\Cryptography\MaskGenerationMethod.cs" />
    <Compile Include="System\Security\Cryptography\MD5.cs" />
    <Compile Include="System\Security\Cryptography\MD5CryptoServiceProvider.cs" />
    <Compile Include="System\Security\Cryptography\NativeHmac.cs" />
    <Compile Include="System\Security\Cryptography\PaddingMode.cs" />
    <Compile Include="System\Security\Cryptography\PasswordDeriveBytes.cs" />
    <Compile Include="System\Security\Cryptography\PKCS1MaskGenerationMethod.cs" />
    <Compile Include="System\Security\Cryptography\RandomNumberGenerator.cs" />
    <Compile Include="System\Security\Cryptography\RC2.cs" />
    <Compile Include="System\Security\Cryptography\RC2CryptoServiceProvider.cs" />
    <Compile Include="System\Security\Cryptography\Rfc2898DeriveBytes.cs" />
    <Compile Include="System\Security\Cryptography\Rijndael.cs" />
    <Compile Include="System\Security\Cryptography\RijndaelManaged.cs" />
    <Compile Include="System\Security\Cryptography\RijndaelManagedTransform.cs" />
    <Compile Include="System\Security\Cryptography\RijndaelManagedTransformMode.cs" />
    <Compile Include="System\Security\Cryptography\RIPEMD160.cs" />
    <Compile Include="System\Security\Cryptography\RIPEMD160Managed.cs" />
    <Compile Include="System\Security\Cryptography\RNGCryptoServiceProvider.cs" />
    <Compile Include="System\Security\Cryptography\RSA.cs" />
    <Compile Include="System\Security\Cryptography\RSACryptoServiceProvider.cs" />
    <Compile Include="System\Security\Cryptography\RSACspObject.cs" />
    <Compile Include="System\Security\Cryptography\RSAEncryptionPadding.cs" />
    <Compile Include="System\Security\Cryptography\RSAEncryptionPaddingMode.cs" />
    <Compile Include="System\Security\Cryptography\RSAOAEPKeyExchangeDeformatter.cs" />
    <Compile Include="System\Security\Cryptography\RSAOAEPKeyExchangeFormatter.cs" />
    <Compile Include="System\Security\Cryptography\RSAParameters.cs" />
    <Compile Include="System\Security\Cryptography\RSAPKCS1KeyExchangeDeformatter.cs" />
    <Compile Include="System\Security\Cryptography\RSAPKCS1KeyExchangeFormatter.cs" />
    <Compile Include="System\Security\Cryptography\RSAPKCS1SHA1SignatureDescription.cs" />
    <Compile Include="System\Security\Cryptography\RSAPKCS1SHA256SignatureDescription.cs" />
    <Compile Include="System\Security\Cryptography\RSAPKCS1SHA384SignatureDescription.cs" />
    <Compile Include="System\Security\Cryptography\RSAPKCS1SHA512SignatureDescription.cs" />
    <Compile Include="System\Security\Cryptography\RSAPKCS1SignatureDeformatter.cs" />
    <Compile Include="System\Security\Cryptography\RSAPKCS1SignatureDescription.cs" />
    <Compile Include="System\Security\Cryptography\RSAPKCS1SignatureFormatter.cs" />
    <Compile Include="System\Security\Cryptography\RSASignaturePadding.cs" />
    <Compile Include="System\Security\Cryptography\RSASignaturePaddingMode.cs" />
    <Compile Include="System\Security\Cryptography\SafeCspHandle.cs" />
    <Compile Include="System\Security\Cryptography\SafeCspHashHandle.cs" />
    <Compile Include="System\Security\Cryptography\SafeCspKeyHandle.cs" />
    <Compile Include="System\Security\Cryptography\SafeHashHandle.cs" />
    <Compile Include="System\Security\Cryptography\SafeKeyHandle.cs" />
    <Compile Include="System\Security\Cryptography\SafeProvHandle.cs" />
    <Compile Include="System\Security\Cryptography\SHA1.cs" />
    <Compile Include="System\Security\Cryptography\SHA1CryptoServiceProvider.cs" />
    <Compile Include="System\Security\Cryptography\SHA1Managed.cs" />
    <Compile Include="System\Security\Cryptography\SHA256.cs" />
    <Compile Include="System\Security\Cryptography\SHA256Managed.cs" />
    <Compile Include="System\Security\Cryptography\SHA384.cs" />
    <Compile Include="System\Security\Cryptography\SHA384Managed.cs" />
    <Compile Include="System\Security\Cryptography\SHA512.cs" />
    <Compile Include="System\Security\Cryptography\SHA512Managed.cs" />
    <Compile Include="System\Security\Cryptography\SignatureDescription.cs" />
    <Compile Include="System\Security\Cryptography\SymmetricAlgorithm.cs" />
    <Compile Include="System\Security\Cryptography\TailStream.cs" />
    <Compile Include="System\Security\Cryptography\ToBase64Transform.cs" />
    <Compile Include="System\Security\Cryptography\TripleDES.cs" />
    <Compile Include="System\Security\Cryptography\TripleDESCryptoServiceProvider.cs" />
    <Compile Include="System\Security\Cryptography\Utils.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\AlgorithmIdentifierAsn.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\Asn1Tag.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\AsnDecoder.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\AsnEncodingRules.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\AsnReader.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\AsnReaderOptions.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\AsnValueReader.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\AttributeAsn.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\BinaryPrimitives.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\ContentInfoAsn.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\CryptographicOperations.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\CryptoPool.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\CRYPT_OID_INFO.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\DigestInfoAsn.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\EncryptedContentInfoAsn.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\EncryptedDataAsn.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\EncryptedPrivateKeyInfoAsn.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\Helpers.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\IncrementalHash.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\IterationCountLimitEnforcer.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\KdfWorkLimiter.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\MacData.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\OidGroup.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\OidKeyType.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\Oids.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\OpenAlgorithmProviderFlags.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\PasswordBasedEncryption.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\PBEParameter.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\PBES2Params.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\Pbkdf2.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\Pbkdf2Params.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\Pbkdf2SaltChoice.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\PfxAsn.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\Pkcs12Kdf.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\Rc2CbcParameters.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\ReadOnlyMemory.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\ReadOnlySpan.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\SafeBagAsn.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\SafeBCryptAlgorithmHandle.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\SafeCertContextHandle.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\SafeCertStoreHandle.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\SetOfValueComparer.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\Span.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\SR.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\TagClass.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\Triple.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\UniversalTagNumber.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\Utility.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\X509Certificate.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\X509Constants.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\X509ContentType.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\X509KeyStorageFlags.cs" />
    <Compile Include="System\Security\Cryptography\X509Certificates\X509Utils.cs" />
    <Compile Include="System\Security\DynamicSecurityMethodAttribute.cs" />
    <Compile Include="System\Security\FrameSecurityDescriptor.cs" />
    <Compile Include="System\Security\FrameSecurityDescriptorWithResolver.cs" />
    <Compile Include="System\Security\HostProtectionException.cs" />
    <Compile Include="System\Security\HostSecurityManager.cs" />
    <Compile Include="System\Security\HostSecurityManagerOptions.cs" />
    <Compile Include="System\Security\IEvidenceFactory.cs" />
    <Compile Include="System\Security\IPermission.cs" />
    <Compile Include="System\Security\ISecurityElementFactory.cs" />
    <Compile Include="System\Security\ISecurityEncodable.cs" />
    <Compile Include="System\Security\ISecurityPolicyEncodable.cs" />
    <Compile Include="System\Security\IStackWalk.cs" />
    <Compile Include="System\Security\NamedPermissionSet.cs" />
    <Compile Include="System\Security\PartialTrustVisibilityLevel.cs" />
    <Compile Include="System\Security\PermissionListSet.cs" />
    <Compile Include="System\Security\PermissionSet.cs" />
    <Compile Include="System\Security\PermissionSetEnumerator.cs" />
    <Compile Include="System\Security\PermissionSetEnumeratorInternal.cs" />
    <Compile Include="System\Security\PermissionSetTriple.cs" />
    <Compile Include="System\Security\Permissions\BuiltInPermissionFlag.cs" />
    <Compile Include="System\Security\Permissions\BuiltInPermissionIndex.cs" />
    <Compile Include="System\Security\Permissions\CodeAccessSecurityAttribute.cs" />
    <Compile Include="System\Security\Permissions\EnvironmentPermission.cs" />
    <Compile Include="System\Security\Permissions\EnvironmentPermissionAccess.cs" />
    <Compile Include="System\Security\Permissions\EnvironmentPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\EnvironmentStringExpressionSet.cs" />
    <Compile Include="System\Security\Permissions\FileDialogPermission.cs" />
    <Compile Include="System\Security\Permissions\FileDialogPermissionAccess.cs" />
    <Compile Include="System\Security\Permissions\FileDialogPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\FileIOAccess.cs" />
    <Compile Include="System\Security\Permissions\FileIOPermission.cs" />
    <Compile Include="System\Security\Permissions\FileIOPermissionAccess.cs" />
    <Compile Include="System\Security\Permissions\FileIOPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\GacIdentityPermission.cs" />
    <Compile Include="System\Security\Permissions\GacIdentityPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\HostProtectionAttribute.cs" />
    <Compile Include="System\Security\Permissions\HostProtectionPermission.cs" />
    <Compile Include="System\Security\Permissions\HostProtectionResource.cs" />
    <Compile Include="System\Security\Permissions\IBuiltInPermission.cs" />
    <Compile Include="System\Security\Permissions\IDRole.cs" />
    <Compile Include="System\Security\Permissions\IsolatedStorageContainment.cs" />
    <Compile Include="System\Security\Permissions\IsolatedStorageFilePermission.cs" />
    <Compile Include="System\Security\Permissions\IsolatedStorageFilePermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\IsolatedStoragePermission.cs" />
    <Compile Include="System\Security\Permissions\IsolatedStoragePermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\IUnrestrictedPermission.cs" />
    <Compile Include="System\Security\Permissions\KeyContainerPermission.cs" />
    <Compile Include="System\Security\Permissions\KeyContainerPermissionAccessEntry.cs" />
    <Compile Include="System\Security\Permissions\KeyContainerPermissionAccessEntryCollection.cs" />
    <Compile Include="System\Security\Permissions\KeyContainerPermissionAccessEntryEnumerator.cs" />
    <Compile Include="System\Security\Permissions\KeyContainerPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\KeyContainerPermissionFlags.cs" />
    <Compile Include="System\Security\Permissions\PermissionSetAttribute.cs" />
    <Compile Include="System\Security\Permissions\PermissionState.cs" />
    <Compile Include="System\Security\Permissions\PrincipalPermission.cs" />
    <Compile Include="System\Security\Permissions\PrincipalPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\PublisherIdentityPermission.cs" />
    <Compile Include="System\Security\Permissions\PublisherIdentityPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\ReflectionPermission.cs" />
    <Compile Include="System\Security\Permissions\ReflectionPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\ReflectionPermissionFlag.cs" />
    <Compile Include="System\Security\Permissions\RegistryPermission.cs" />
    <Compile Include="System\Security\Permissions\RegistryPermissionAccess.cs" />
    <Compile Include="System\Security\Permissions\RegistryPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\SecurityAction.cs" />
    <Compile Include="System\Security\Permissions\SecurityAttribute.cs" />
    <Compile Include="System\Security\Permissions\SecurityPermission.cs" />
    <Compile Include="System\Security\Permissions\SecurityPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\SecurityPermissionFlag.cs" />
    <Compile Include="System\Security\Permissions\SiteIdentityPermission.cs" />
    <Compile Include="System\Security\Permissions\SiteIdentityPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\StrongName2.cs" />
    <Compile Include="System\Security\Permissions\StrongNameIdentityPermission.cs" />
    <Compile Include="System\Security\Permissions\StrongNameIdentityPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\StrongNamePublicKeyBlob.cs" />
    <Compile Include="System\Security\Permissions\UIPermission.cs" />
    <Compile Include="System\Security\Permissions\UIPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\UIPermissionClipboard.cs" />
    <Compile Include="System\Security\Permissions\UIPermissionWindow.cs" />
    <Compile Include="System\Security\Permissions\UrlIdentityPermission.cs" />
    <Compile Include="System\Security\Permissions\UrlIdentityPermissionAttribute.cs" />
    <Compile Include="System\Security\Permissions\ZoneIdentityPermission.cs" />
    <Compile Include="System\Security\Permissions\ZoneIdentityPermissionAttribute.cs" />
    <Compile Include="System\Security\PermissionToken.cs" />
    <Compile Include="System\Security\PermissionTokenFactory.cs" />
    <Compile Include="System\Security\PermissionTokenKeyComparer.cs" />
    <Compile Include="System\Security\PermissionTokenType.cs" />
    <Compile Include="System\Security\PermissionType.cs" />
    <Compile Include="System\Security\PolicyLevelType.cs" />
    <Compile Include="System\Security\PolicyManager.cs" />
    <Compile Include="System\Security\Policy\AllMembershipCondition.cs" />
    <Compile Include="System\Security\Policy\AppDomainEvidenceFactory.cs" />
    <Compile Include="System\Security\Policy\ApplicationDirectory.cs" />
    <Compile Include="System\Security\Policy\ApplicationDirectoryMembershipCondition.cs" />
    <Compile Include="System\Security\Policy\ApplicationSecurityInfo.cs" />
    <Compile Include="System\Security\Policy\ApplicationSecurityManager.cs" />
    <Compile Include="System\Security\Policy\ApplicationTrust.cs" />
    <Compile Include="System\Security\Policy\ApplicationTrustCollection.cs" />
    <Compile Include="System\Security\Policy\ApplicationTrustEnumerator.cs" />
    <Compile Include="System\Security\Policy\ApplicationVersionMatch.cs" />
    <Compile Include="System\Security\Policy\AssemblyEvidenceFactory.cs" />
    <Compile Include="System\Security\Policy\CodeConnectAccess.cs" />
    <Compile Include="System\Security\Policy\CodeGroup.cs" />
    <Compile Include="System\Security\Policy\CodeGroupPositionMarker.cs" />
    <Compile Include="System\Security\Policy\CodeGroupStack.cs" />
    <Compile Include="System\Security\Policy\CodeGroupStackFrame.cs" />
    <Compile Include="System\Security\Policy\ConfigId.cs" />
    <Compile Include="System\Security\Policy\Evidence.cs" />
    <Compile Include="System\Security\Policy\EvidenceBase.cs" />
    <Compile Include="System\Security\Policy\EvidenceTypeDescriptor.cs" />
    <Compile Include="System\Security\Policy\EvidenceTypeGenerated.cs" />
    <Compile Include="System\Security\Policy\FileCodeGroup.cs" />
    <Compile Include="System\Security\Policy\FirstMatchCodeGroup.cs" />
    <Compile Include="System\Security\Policy\GacInstalled.cs" />
    <Compile Include="System\Security\Policy\GacMembershipCondition.cs" />
    <Compile Include="System\Security\Policy\Hash.cs" />
    <Compile Include="System\Security\Policy\HashMembershipCondition.cs" />
    <Compile Include="System\Security\Policy\IApplicationTrustManager.cs" />
    <Compile Include="System\Security\Policy\IConstantMembershipCondition.cs" />
    <Compile Include="System\Security\Policy\IDelayEvaluatedEvidence.cs" />
    <Compile Include="System\Security\Policy\IIdentityPermissionFactory.cs" />
    <Compile Include="System\Security\Policy\ILegacyEvidenceAdapter.cs" />
    <Compile Include="System\Security\Policy\IMembershipCondition.cs" />
    <Compile Include="System\Security\Policy\IReportMatchMembershipCondition.cs" />
    <Compile Include="System\Security\Policy\IRuntimeEvidenceFactory.cs" />
    <Compile Include="System\Security\Policy\IUnionSemanticCodeGroup.cs" />
    <Compile Include="System\Security\Policy\LegacyEvidenceList.cs" />
    <Compile Include="System\Security\Policy\LegacyEvidenceWrapper.cs" />
    <Compile Include="System\Security\Policy\NetCodeGroup.cs" />
    <Compile Include="System\Security\Policy\PEFileEvidenceFactory.cs" />
    <Compile Include="System\Security\Policy\PermissionRequestEvidence.cs" />
    <Compile Include="System\Security\Policy\PolicyException.cs" />
    <Compile Include="System\Security\Policy\PolicyLevel.cs" />
    <Compile Include="System\Security\Policy\PolicyStatement.cs" />
    <Compile Include="System\Security\Policy\PolicyStatementAttribute.cs" />
    <Compile Include="System\Security\Policy\Publisher.cs" />
    <Compile Include="System\Security\Policy\PublisherMembershipCondition.cs" />
    <Compile Include="System\Security\Policy\Site.cs" />
    <Compile Include="System\Security\Policy\SiteMembershipCondition.cs" />
    <Compile Include="System\Security\Policy\StrongName.cs" />
    <Compile Include="System\Security\Policy\StrongNameMembershipCondition.cs" />
    <Compile Include="System\Security\Policy\TrustManagerContext.cs" />
    <Compile Include="System\Security\Policy\TrustManagerUIContext.cs" />
    <Compile Include="System\Security\Policy\UnionCodeGroup.cs" />
    <Compile Include="System\Security\Policy\Url.cs" />
    <Compile Include="System\Security\Policy\UrlMembershipCondition.cs" />
    <Compile Include="System\Security\Policy\Zone.cs" />
    <Compile Include="System\Security\Policy\ZoneMembershipCondition.cs" />
    <Compile Include="System\Security\Principal\GenericIdentity.cs" />
    <Compile Include="System\Security\Principal\GenericPrincipal.cs" />
    <Compile Include="System\Security\Principal\IdentifierAuthority.cs" />
    <Compile Include="System\Security\Principal\IdentityNotMappedException.cs" />
    <Compile Include="System\Security\Principal\IdentityReference.cs" />
    <Compile Include="System\Security\Principal\IdentityReferenceCollection.cs" />
    <Compile Include="System\Security\Principal\IdentityReferenceEnumerator.cs" />
    <Compile Include="System\Security\Principal\IIdentity.cs" />
    <Compile Include="System\Security\Principal\ImpersonationQueryResult.cs" />
    <Compile Include="System\Security\Principal\IPrincipal.cs" />
    <Compile Include="System\Security\Principal\KerbLogonSubmitType.cs" />
    <Compile Include="System\Security\Principal\NTAccount.cs" />
    <Compile Include="System\Security\Principal\PolicyRights.cs" />
    <Compile Include="System\Security\Principal\PrincipalPolicy.cs" />
    <Compile Include="System\Security\Principal\SecurityIdentifier.cs" />
    <Compile Include="System\Security\Principal\SecurityLogonType.cs" />
    <Compile Include="System\Security\Principal\SidNameUse.cs" />
    <Compile Include="System\Security\Principal\TokenAccessLevels.cs" />
    <Compile Include="System\Security\Principal\TokenImpersonationLevel.cs" />
    <Compile Include="System\Security\Principal\TokenInformationClass.cs" />
    <Compile Include="System\Security\Principal\TokenType.cs" />
    <Compile Include="System\Security\Principal\WellKnownSidType.cs" />
    <Compile Include="System\Security\Principal\Win32.cs" />
    <Compile Include="System\Security\Principal\WindowsAccountType.cs" />
    <Compile Include="System\Security\Principal\WindowsBuiltInRole.cs" />
    <Compile Include="System\Security\Principal\WindowsIdentity.cs" />
    <Compile Include="System\Security\Principal\WindowsImpersonationContext.cs" />
    <Compile Include="System\Security\Principal\WindowsPrincipal.cs" />
    <Compile Include="System\Security\Principal\WinSecurityContext.cs" />
    <Compile Include="System\Security\ReadOnlyPermissionSet.cs" />
    <Compile Include="System\Security\ReadOnlyPermissionSetEnumerator.cs" />
    <Compile Include="System\Security\SafeBSTRHandle.cs" />
    <Compile Include="System\Security\SecureString.cs" />
    <Compile Include="System\Security\SecurityContext.cs" />
    <Compile Include="System\Security\SecurityContextDisableFlow.cs" />
    <Compile Include="System\Security\SecurityContextSource.cs" />
    <Compile Include="System\Security\SecurityContextSwitcher.cs" />
    <Compile Include="System\Security\SecurityCriticalAttribute.cs" />
    <Compile Include="System\Security\SecurityCriticalScope.cs" />
    <Compile Include="System\Security\SecurityDocument.cs" />
    <Compile Include="System\Security\SecurityDocumentElement.cs" />
    <Compile Include="System\Security\SecurityElement.cs" />
    <Compile Include="System\Security\SecurityElementType.cs" />
    <Compile Include="System\Security\SecurityException.cs" />
    <Compile Include="System\Security\SecurityManager.cs" />
    <Compile Include="System\Security\SecurityRulesAttribute.cs" />
    <Compile Include="System\Security\SecurityRuleSet.cs" />
    <Compile Include="System\Security\SecurityRuntime.cs" />
    <Compile Include="System\Security\SecuritySafeCriticalAttribute.cs" />
    <Compile Include="System\Security\SecurityState.cs" />
    <Compile Include="System\Security\SecurityTransparentAttribute.cs" />
    <Compile Include="System\Security\SecurityTreatAsSafeAttribute.cs" />
    <Compile Include="System\Security\SecurityZone.cs" />
    <Compile Include="System\Security\SpecialPermissionSetFlag.cs" />
    <Compile Include="System\Security\SuppressUnmanagedCodeSecurityAttribute.cs" />
    <Compile Include="System\Security\UnverifiableCodeAttribute.cs" />
    <Compile Include="System\Security\Util\Config.cs" />
    <Compile Include="System\Security\Util\DirectoryString.cs" />
    <Compile Include="System\Security\Util\Hex.cs" />
    <Compile Include="System\Security\Util\LocalSiteString.cs" />
    <Compile Include="System\Security\Util\Parser.cs" />
    <Compile Include="System\Security\Util\QuickCacheEntryType.cs" />
    <Compile Include="System\Security\Util\SiteString.cs" />
    <Compile Include="System\Security\Util\StringExpressionSet.cs" />
    <Compile Include="System\Security\Util\TokenBasedSet.cs" />
    <Compile Include="System\Security\Util\TokenBasedSetEnumerator.cs" />
    <Compile Include="System\Security\Util\Tokenizer.cs" />
    <Compile Include="System\Security\Util\TokenizerShortBlock.cs" />
    <Compile Include="System\Security\Util\TokenizerStream.cs" />
    <Compile Include="System\Security\Util\TokenizerStringBlock.cs" />
    <Compile Include="System\Security\Util\URLString.cs" />
    <Compile Include="System\Security\Util\XMLUtil.cs" />
    <Compile Include="System\Security\VerificationException.cs" />
    <Compile Include="System\Security\WindowsImpersonationFlowMode.cs" />
    <Compile Include="System\Security\XmlSyntaxException.cs" />
    <Compile Include="System\SerializableAttribute.cs" />
    <Compile Include="System\SharedStatics.cs" />
    <Compile Include="System\Signature.cs" />
    <Compile Include="System\Single.cs" />
    <Compile Include="System\SizedReference.cs" />
    <Compile Include="System\StackOverflowException.cs" />
    <Compile Include="System\STAThreadAttribute.cs" />
    <Compile Include="System\String.cs" />
    <Compile Include="System\StringComparer.cs" />
    <Compile Include="System\StringComparison.cs" />
    <Compile Include="System\StringSplitOptions.cs" />
    <Compile Include="System\StubHelpers\AnsiBSTRMarshaler.cs" />
    <Compile Include="System\StubHelpers\AnsiCharMarshaler.cs" />
    <Compile Include="System\StubHelpers\AsAnyMarshaler.cs" />
    <Compile Include="System\StubHelpers\BSTRMarshaler.cs" />
    <Compile Include="System\StubHelpers\CleanupWorkList.cs" />
    <Compile Include="System\StubHelpers\CleanupWorkListElement.cs" />
    <Compile Include="System\StubHelpers\CSTRMarshaler.cs" />
    <Compile Include="System\StubHelpers\DateMarshaler.cs" />
    <Compile Include="System\StubHelpers\DateTimeNative.cs" />
    <Compile Include="System\StubHelpers\DateTimeOffsetMarshaler.cs" />
    <Compile Include="System\StubHelpers\EventArgsMarshaler.cs" />
    <Compile Include="System\StubHelpers\HResultExceptionMarshaler.cs" />
    <Compile Include="System\StubHelpers\HStringMarshaler.cs" />
    <Compile Include="System\StubHelpers\InterfaceMarshaler.cs" />
    <Compile Include="System\StubHelpers\KeyValuePairMarshaler.cs" />
    <Compile Include="System\StubHelpers\MngdHiddenLengthArrayMarshaler.cs" />
    <Compile Include="System\StubHelpers\MngdNativeArrayMarshaler.cs" />
    <Compile Include="System\StubHelpers\MngdRefCustomMarshaler.cs" />
    <Compile Include="System\StubHelpers\MngdSafeArrayMarshaler.cs" />
    <Compile Include="System\StubHelpers\NativeVariant.cs" />
    <Compile Include="System\StubHelpers\NullableMarshaler.cs" />
    <Compile Include="System\StubHelpers\ObjectMarshaler.cs" />
    <Compile Include="System\StubHelpers\StubHelpers.cs" />
    <Compile Include="System\StubHelpers\SystemTypeMarshaler.cs" />
    <Compile Include="System\StubHelpers\TypeKind.cs" />
    <Compile Include="System\StubHelpers\TypeNameNative.cs" />
    <Compile Include="System\StubHelpers\UriMarshaler.cs" />
    <Compile Include="System\StubHelpers\UTF8BufferMarshaler.cs" />
    <Compile Include="System\StubHelpers\UTF8Marshaler.cs" />
    <Compile Include="System\StubHelpers\ValueClassMarshaler.cs" />
    <Compile Include="System\StubHelpers\VBByValStrMarshaler.cs" />
    <Compile Include="System\StubHelpers\WinRTTypeNameConverter.cs" />
    <Compile Include="System\StubHelpers\WSTRBufferMarshaler.cs" />
    <Compile Include="System\SwitchStructure.cs" />
    <Compile Include="System\SystemException.cs" />
    <Compile Include="System\System_LazyDebugView.cs" />
    <Compile Include="System\SZArrayHelper.cs" />
    <Compile Include="System\Text\ASCIIEncoding.cs" />
    <Compile Include="System\Text\BaseCodePageEncoding.cs" />
    <Compile Include="System\Text\CodePageEncoding.cs" />
    <Compile Include="System\Text\DBCSCodePageEncoding.cs" />
    <Compile Include="System\Text\Decoder.cs" />
    <Compile Include="System\Text\DecoderExceptionFallback.cs" />
    <Compile Include="System\Text\DecoderExceptionFallbackBuffer.cs" />
    <Compile Include="System\Text\DecoderFallback.cs" />
    <Compile Include="System\Text\DecoderFallbackBuffer.cs" />
    <Compile Include="System\Text\DecoderFallbackException.cs" />
    <Compile Include="System\Text\DecoderNLS.cs" />
    <Compile Include="System\Text\DecoderReplacementFallback.cs" />
    <Compile Include="System\Text\DecoderReplacementFallbackBuffer.cs" />
    <Compile Include="System\Text\Encoder.cs" />
    <Compile Include="System\Text\EncoderExceptionFallback.cs" />
    <Compile Include="System\Text\EncoderExceptionFallbackBuffer.cs" />
    <Compile Include="System\Text\EncoderFallback.cs" />
    <Compile Include="System\Text\EncoderFallbackBuffer.cs" />
    <Compile Include="System\Text\EncoderFallbackException.cs" />
    <Compile Include="System\Text\EncoderNLS.cs" />
    <Compile Include="System\Text\EncoderReplacementFallback.cs" />
    <Compile Include="System\Text\EncoderReplacementFallbackBuffer.cs" />
    <Compile Include="System\Text\Encoding.cs" />
    <Compile Include="System\Text\EncodingInfo.cs" />
    <Compile Include="System\Text\EncodingNLS.cs" />
    <Compile Include="System\Text\EncodingProvider.cs" />
    <Compile Include="System\Text\EUCJPEncoding.cs" />
    <Compile Include="System\Text\ExtendedNormalizationForms.cs" />
    <Compile Include="System\Text\GB18030Encoding.cs" />
    <Compile Include="System\Text\InternalDecoderBestFitFallback.cs" />
    <Compile Include="System\Text\InternalDecoderBestFitFallbackBuffer.cs" />
    <Compile Include="System\Text\InternalEncoderBestFitFallback.cs" />
    <Compile Include="System\Text\InternalEncoderBestFitFallbackBuffer.cs" />
    <Compile Include="System\Text\ISCIIEncoding.cs" />
    <Compile Include="System\Text\ISO2022Encoding.cs" />
    <Compile Include="System\Text\Latin1Encoding.cs" />
    <Compile Include="System\Text\MLangCodePageEncoding.cs" />
    <Compile Include="System\Text\Normalization.cs" />
    <Compile Include="System\Text\NormalizationForm.cs" />
    <Compile Include="System\Text\SBCSCodePageEncoding.cs" />
    <Compile Include="System\Text\StringBuilder.cs" />
    <Compile Include="System\Text\StringBuilderCache.cs" />
    <Compile Include="System\Text\SurrogateEncoder.cs" />
    <Compile Include="System\Text\UnicodeEncoding.cs" />
    <Compile Include="System\Text\UTF32Encoding.cs" />
    <Compile Include="System\Text\UTF7Encoding.cs" />
    <Compile Include="System\Text\UTF8Encoding.cs" />
    <Compile Include="System\Threading\AbandonedMutexException.cs" />
    <Compile Include="System\Threading\ApartmentState.cs" />
    <Compile Include="System\Threading\AsyncFlowControl.cs" />
    <Compile Include="System\Threading\AsyncLocal.cs" />
    <Compile Include="System\Threading\AsyncLocalValueChangedArgs.cs" />
    <Compile Include="System\Threading\AsyncLocalValueMap.cs" />
    <Compile Include="System\Threading\AutoResetEvent.cs" />
    <Compile Include="System\Threading\CancellationCallbackCoreWorkArguments.cs" />
    <Compile Include="System\Threading\CancellationCallbackInfo.cs" />
    <Compile Include="System\Threading\CancellationToken.cs" />
    <Compile Include="System\Threading\CancellationTokenRegistration.cs" />
    <Compile Include="System\Threading\CancellationTokenSource.cs" />
    <Compile Include="System\Threading\CdsSyncEtwBCLProvider.cs" />
    <Compile Include="System\Threading\CompressedStack.cs" />
    <Compile Include="System\Threading\CompressedStackSwitcher.cs" />
    <Compile Include="System\Threading\ContextCallback.cs" />
    <Compile Include="System\Threading\CountdownEvent.cs" />
    <Compile Include="System\Threading\DeferredDisposableLifetime.cs" />
    <Compile Include="System\Threading\DomainCompressedStack.cs" />
    <Compile Include="System\Threading\EventResetMode.cs" />
    <Compile Include="System\Threading\EventWaitHandle.cs" />
    <Compile Include="System\Threading\ExecutionContext.cs" />
    <Compile Include="System\Threading\ExecutionContextSwitcher.cs" />
    <Compile Include="System\Threading\Gen2GcCallback.cs" />
    <Compile Include="System\Threading\HostExecutionContext.cs" />
    <Compile Include="System\Threading\HostExecutionContextManager.cs" />
    <Compile Include="System\Threading\HostExecutionContextSwitcher.cs" />
    <Compile Include="System\Threading\IAsyncLocal.cs" />
    <Compile Include="System\Threading\IAsyncLocalValueMap.cs" />
    <Compile Include="System\Threading\IDeferredDisposable.cs" />
    <Compile Include="System\Threading\Interlocked.cs" />
    <Compile Include="System\Threading\InternalCrossContextDelegate.cs" />
    <Compile Include="System\Threading\IOCompletionCallback.cs" />
    <Compile Include="System\Threading\IThreadPoolWorkItem.cs" />
    <Compile Include="System\Threading\IUnknownSafeHandle.cs" />
    <Compile Include="System\Threading\LazyHelpers.cs" />
    <Compile Include="System\Threading\LazyInitializer.cs" />
    <Compile Include="System\Threading\LazyThreadSafetyMode.cs" />
    <Compile Include="System\Threading\LockCookie.cs" />
    <Compile Include="System\Threading\LockRecursionException.cs" />
    <Compile Include="System\Threading\ManualResetEvent.cs" />
    <Compile Include="System\Threading\ManualResetEventSlim.cs" />
    <Compile Include="System\Threading\Monitor.cs" />
    <Compile Include="System\Threading\Mutex.cs" />
    <Compile Include="System\Threading\NativeOverlapped.cs" />
    <Compile Include="System\Threading\NetCore\TimerQueue.cs" />
    <Compile Include="System\Threading\NetCore\TimerQueueTimer.cs" />
    <Compile Include="System\Threading\Overlapped.cs" />
    <Compile Include="System\Threading\OverlappedData.cs" />
    <Compile Include="System\Threading\ParameterizedThreadStart.cs" />
    <Compile Include="System\Threading\PinnableBufferCache.cs" />
    <Compile Include="System\Threading\PinnableBufferCacheEventSource.cs" />
    <Compile Include="System\Threading\PlatformHelper.cs" />
    <Compile Include="System\Threading\PreAllocatedOverlapped.cs" />
    <Compile Include="System\Threading\QueueUserWorkItemCallback.cs" />
    <Compile Include="System\Threading\ReaderWriterLock.cs" />
    <Compile Include="System\Threading\RegisteredWaitHandle.cs" />
    <Compile Include="System\Threading\RegisteredWaitHandleSafe.cs" />
    <Compile Include="System\Threading\SafeCompressedStackHandle.cs" />
    <Compile Include="System\Threading\SemaphoreFullException.cs" />
    <Compile Include="System\Threading\SemaphoreSlim.cs" />
    <Compile Include="System\Threading\SendOrPostCallback.cs" />
    <Compile Include="System\Threading\SparselyPopulatedArray.cs" />
    <Compile Include="System\Threading\SparselyPopulatedArrayAddInfo.cs" />
    <Compile Include="System\Threading\SparselyPopulatedArrayFragment.cs" />
    <Compile Include="System\Threading\SpinLock.cs" />
    <Compile Include="System\Threading\SpinWait.cs" />
    <Compile Include="System\Threading\StackCrawlMark.cs" />
    <Compile Include="System\Threading\SynchronizationContext.cs" />
    <Compile Include="System\Threading\SynchronizationContextProperties.cs" />
    <Compile Include="System\Threading\SynchronizationLockException.cs" />
    <Compile Include="System\Threading\SystemThreading_ThreadLocalDebugView.cs" />
    <Compile Include="System\Threading\Tasks\AsyncCausalityStatus.cs" />
    <Compile Include="System\Threading\Tasks\AsyncCausalityTracer.cs" />
    <Compile Include="System\Threading\Tasks\AwaitTaskContinuation.cs" />
    <Compile Include="System\Threading\Tasks\BeginEndAwaitableAdapter.cs" />
    <Compile Include="System\Threading\Tasks\CausalityRelation.cs" />
    <Compile Include="System\Threading\Tasks\CausalitySynchronousWork.cs" />
    <Compile Include="System\Threading\Tasks\CausalityTraceLevel.cs" />
    <Compile Include="System\Threading\Tasks\CompletionActionInvoker.cs" />
    <Compile Include="System\Threading\Tasks\ConcurrentExclusiveSchedulerPair.cs" />
    <Compile Include="System\Threading\Tasks\ContinuationResultTaskFromResultTask.cs" />
    <Compile Include="System\Threading\Tasks\ContinuationResultTaskFromTask.cs" />
    <Compile Include="System\Threading\Tasks\ContinuationTaskFromResultTask.cs" />
    <Compile Include="System\Threading\Tasks\ContinuationTaskFromTask.cs" />
    <Compile Include="System\Threading\Tasks\GenericDelegateCache.cs" />
    <Compile Include="System\Threading\Tasks\IndexRange.cs" />
    <Compile Include="System\Threading\Tasks\InternalTaskOptions.cs" />
    <Compile Include="System\Threading\Tasks\IProducerConsumerQueue.cs" />
    <Compile Include="System\Threading\Tasks\ITaskCompletionAction.cs" />
    <Compile Include="System\Threading\Tasks\MultiProducerMultiConsumerQueue.cs" />
    <Compile Include="System\Threading\Tasks\PaddingFor32.cs" />
    <Compile Include="System\Threading\Tasks\PaddingHelpers.cs" />
    <Compile Include="System\Threading\Tasks\Parallel.cs" />
    <Compile Include="System\Threading\Tasks\ParallelForReplicaTask.cs" />
    <Compile Include="System\Threading\Tasks\ParallelForReplicatingTask.cs" />
    <Compile Include="System\Threading\Tasks\ParallelLoopResult.cs" />
    <Compile Include="System\Threading\Tasks\ParallelLoopState.cs" />
    <Compile Include="System\Threading\Tasks\ParallelLoopState32.cs" />
    <Compile Include="System\Threading\Tasks\ParallelLoopState64.cs" />
    <Compile Include="System\Threading\Tasks\ParallelLoopStateFlags.cs" />
    <Compile Include="System\Threading\Tasks\ParallelLoopStateFlags32.cs" />
    <Compile Include="System\Threading\Tasks\ParallelLoopStateFlags64.cs" />
    <Compile Include="System\Threading\Tasks\ParallelOptions.cs" />
    <Compile Include="System\Threading\Tasks\RangeManager.cs" />
    <Compile Include="System\Threading\Tasks\RangeWorker.cs" />
    <Compile Include="System\Threading\Tasks\Shared.cs" />
    <Compile Include="System\Threading\Tasks\SingleProducerSingleConsumerQueue.cs" />
    <Compile Include="System\Threading\Tasks\StackGuard.cs" />
    <Compile Include="System\Threading\Tasks\StandardTaskContinuation.cs" />
    <Compile Include="System\Threading\Tasks\SynchronizationContextAwaitTaskContinuation.cs" />
    <Compile Include="System\Threading\Tasks\SynchronizationContextTaskScheduler.cs" />
    <Compile Include="System\Threading\Tasks\SystemThreadingTasks_FutureDebugView.cs" />
    <Compile Include="System\Threading\Tasks\SystemThreadingTasks_TaskDebugView.cs" />
    <Compile Include="System\Threading\Tasks\Task.2.cs" />
    <Compile Include="System\Threading\Tasks\Task.cs" />
    <Compile Include="System\Threading\Tasks\TaskCanceledException.cs" />
    <Compile Include="System\Threading\Tasks\TaskCompletionSource.cs" />
    <Compile Include="System\Threading\Tasks\TaskContinuation.cs" />
    <Compile Include="System\Threading\Tasks\TaskContinuationOptions.cs" />
    <Compile Include="System\Threading\Tasks\TaskCreationOptions.cs" />
    <Compile Include="System\Threading\Tasks\TaskExceptionHolder.cs" />
    <Compile Include="System\Threading\Tasks\TaskFactory.2.cs" />
    <Compile Include="System\Threading\Tasks\TaskFactory.cs" />
    <Compile Include="System\Threading\Tasks\TaskScheduler.cs" />
    <Compile Include="System\Threading\Tasks\TaskSchedulerAwaitTaskContinuation.cs" />
    <Compile Include="System\Threading\Tasks\TaskSchedulerException.cs" />
    <Compile Include="System\Threading\Tasks\TaskStatus.cs" />
    <Compile Include="System\Threading\Tasks\TaskToApm.cs" />
    <Compile Include="System\Threading\Tasks\ThreadPoolTaskScheduler.cs" />
    <Compile Include="System\Threading\Tasks\TplEtwProvider.cs" />
    <Compile Include="System\Threading\Tasks\UnobservedTaskExceptionEventArgs.cs" />
    <Compile Include="System\Threading\Tasks\UnwrapPromise.cs" />
    <Compile Include="System\Threading\Tasks\VoidTaskResult.cs" />
    <Compile Include="System\Threading\Thread.cs" />
    <Compile Include="System\Threading\ThreadAbortException.cs" />
    <Compile Include="System\Threading\ThreadHandle.cs" />
    <Compile Include="System\Threading\ThreadHelper.cs" />
    <Compile Include="System\Threading\ThreadInterruptedException.cs" />
    <Compile Include="System\Threading\ThreadLocal.cs" />
    <Compile Include="System\Threading\ThreadPool.cs" />
    <Compile Include="System\Threading\ThreadPoolBoundHandle.cs" />
    <Compile Include="System\Threading\ThreadPoolBoundHandleOverlapped.cs" />
    <Compile Include="System\Threading\ThreadPoolGlobals.cs" />
    <Compile Include="System\Threading\ThreadPoolWorkQueue.cs" />
    <Compile Include="System\Threading\ThreadPoolWorkQueueThreadLocals.cs" />
    <Compile Include="System\Threading\ThreadPriority.cs" />
    <Compile Include="System\Threading\ThreadStart.cs" />
    <Compile Include="System\Threading\ThreadStartException.cs" />
    <Compile Include="System\Threading\ThreadState.cs" />
    <Compile Include="System\Threading\ThreadStateException.cs" />
    <Compile Include="System\Threading\Timeout.cs" />
    <Compile Include="System\Threading\TimeoutHelper.cs" />
    <Compile Include="System\Threading\Timer.cs" />
    <Compile Include="System\Threading\TimerCallback.cs" />
    <Compile Include="System\Threading\TimerHolder.cs" />
    <Compile Include="System\Threading\TimerQueue.cs" />
    <Compile Include="System\Threading\TimerQueueTimer.cs" />
    <Compile Include="System\Threading\Volatile.cs" />
    <Compile Include="System\Threading\WaitCallback.cs" />
    <Compile Include="System\Threading\WaitHandle.cs" />
    <Compile Include="System\Threading\WaitHandleCannotBeOpenedException.cs" />
    <Compile Include="System\Threading\WaitHandleExtensions.cs" />
    <Compile Include="System\Threading\WaitOrTimerCallback.cs" />
    <Compile Include="System\Threading\WinRTSynchronizationContextFactoryBase.cs" />
    <Compile Include="System\Threading\_IOCompletionCallback.cs" />
    <Compile Include="System\Threading\_ThreadPoolWaitCallback.cs" />
    <Compile Include="System\Threading\_ThreadPoolWaitOrTimerCallback.cs" />
    <Compile Include="System\ThreadStaticAttribute.cs" />
    <Compile Include="System\ThrowHelper.cs" />
    <Compile Include="System\TimeoutException.cs" />
    <Compile Include="System\TimeSpan.cs" />
    <Compile Include="System\TimeZone.cs" />
    <Compile Include="System\TimeZoneInfo.cs" />
    <Compile Include="System\TimeZoneInfoOptions.cs" />
    <Compile Include="System\TimeZoneNotFoundException.cs" />
    <Compile Include="System\TokenType.cs" />
    <Compile Include="System\Tuple.2.cs" />
    <Compile Include="System\Tuple.3.cs" />
    <Compile Include="System\Tuple.4.cs" />
    <Compile Include="System\Tuple.5.cs" />
    <Compile Include="System\Tuple.6.cs" />
    <Compile Include="System\Tuple.7.cs" />
    <Compile Include="System\Tuple.8.cs" />
    <Compile Include="System\Tuple.9.cs" />
    <Compile Include="System\Tuple.cs" />
    <Compile Include="System\TupleExtensions.cs" />
    <Compile Include="System\Type.cs" />
    <Compile Include="System\TypeAccessException.cs" />
    <Compile Include="System\TypeCode.cs" />
    <Compile Include="System\TypedReference.cs" />
    <Compile Include="System\TypeInitializationException.cs" />
    <Compile Include="System\TypeLoadException.cs" />
    <Compile Include="System\TypeNameFormatFlags.cs" />
    <Compile Include="System\TypeNameKind.cs" />
    <Compile Include="System\TypeNameParser.cs" />
    <Compile Include="System\TypeUnloadedException.cs" />
    <Compile Include="System\UInt16.cs" />
    <Compile Include="System\UInt32.cs" />
    <Compile Include="System\UInt64.cs" />
    <Compile Include="System\UIntPtr.cs" />
    <Compile Include="System\UnauthorizedAccessException.cs" />
    <Compile Include="System\UnhandledExceptionEventArgs.cs" />
    <Compile Include="System\UnhandledExceptionEventHandler.cs" />
    <Compile Include="System\UnitySerializationHolder.cs" />
    <Compile Include="System\UnSafeCharBuffer.cs" />
    <Compile Include="System\Utf8String.cs" />
    <Compile Include="System\ValueTuple.2.cs" />
    <Compile Include="System\ValueTuple.3.cs" />
    <Compile Include="System\ValueTuple.4.cs" />
    <Compile Include="System\ValueTuple.5.cs" />
    <Compile Include="System\ValueTuple.6.cs" />
    <Compile Include="System\ValueTuple.7.cs" />
    <Compile Include="System\ValueTuple.8.cs" />
    <Compile Include="System\ValueTuple.9.cs" />
    <Compile Include="System\ValueTuple.cs" />
    <Compile Include="System\ValueType.cs" />
    <Compile Include="System\Variant.cs" />
    <Compile Include="System\Version.cs" />
    <Compile Include="System\Void.cs" />
    <Compile Include="System\WeakReference.2.cs" />
    <Compile Include="System\WeakReference.cs" />
    <Compile Include="System\XmlIgnoreMemberAttribute.cs" />
    <Compile Include="System\_AppDomain.cs" />
    <Compile Include="System\__Canon.cs" />
    <Compile Include="System\__ComObject.cs" />
    <Compile Include="System\__DTString.cs" />
    <Compile Include="System\__Filters.cs" />
    <Compile Include="System\__HResults.cs" />
    <Compile Include="ThisAssembly.cs" />
    <Compile Include="Windows\Foundation\Diagnostics\AsyncCausalityStatus.cs" />
    <Compile Include="Windows\Foundation\Diagnostics\CausalityRelation.cs" />
    <Compile Include="Windows\Foundation\Diagnostics\CausalitySource.cs" />
    <Compile Include="Windows\Foundation\Diagnostics\CausalitySynchronousWork.cs" />
    <Compile Include="Windows\Foundation\Diagnostics\CausalityTraceLevel.cs" />
    <Compile Include="Windows\Foundation\Diagnostics\IAsyncCausalityTracerStatics.cs" />
    <Compile Include="Windows\Foundation\Diagnostics\ITracingStatusChangedEventArgs.cs" />
    <Compile Include="Windows\Foundation\Diagnostics\TracingStatusChangedEventArgs.cs" />
    <Compile Include="__DynamicallyInvokableAttribute.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="charinfo.nlp" />
    <EmbeddedResource Include="codepages.nlp" />
    <EmbeddedResource Include="mscorlib.resources" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>