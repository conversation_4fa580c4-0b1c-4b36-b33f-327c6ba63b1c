﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D7E RID: 3454
	public class PE1263 : BasePetEffect
	{
		// Token: 0x06007B0B RID: 31499 RVA: 0x00291614 File Offset: 0x0028F814
		public PE1263(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1263, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B0C RID: 31500 RVA: 0x00291694 File Offset: 0x0028F894
		public override bool Start(Living living)
		{
			PE1263 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1263) as PE1263;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B0D RID: 31501 RVA: 0x0002EC98 File Offset: 0x0002CE98
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
		}

		// Token: 0x06007B0E RID: 31502 RVA: 0x0002EB12 File Offset: 0x0002CD12
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			((Player)living).AddPetMP(2);
		}

		// Token: 0x06007B0F RID: 31503 RVA: 0x0002ECAE File Offset: 0x0002CEAE
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x040048B2 RID: 18610
		private int m_type = 0;

		// Token: 0x040048B3 RID: 18611
		private int m_count = 0;

		// Token: 0x040048B4 RID: 18612
		private int m_probability = 0;

		// Token: 0x040048B5 RID: 18613
		private int m_delay = 0;

		// Token: 0x040048B6 RID: 18614
		private int m_coldDown = 0;

		// Token: 0x040048B7 RID: 18615
		private int m_currentId;

		// Token: 0x040048B8 RID: 18616
		private int m_added = 0;
	}
}
