﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E0C RID: 3596
	public class AE1226 : BasePetEffect
	{
		// Token: 0x06007DF1 RID: 32241 RVA: 0x0029E530 File Offset: 0x0029C730
		public AE1226(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1226, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DF2 RID: 32242 RVA: 0x0029E5B0 File Offset: 0x0029C7B0
		public override bool Start(Living living)
		{
			AE1226 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1226) as AE1226;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DF3 RID: 32243 RVA: 0x00030A19 File Offset: 0x0002EC19
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DF4 RID: 32244 RVA: 0x00030A2F File Offset: 0x0002EC2F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DF5 RID: 32245 RVA: 0x0029E610 File Offset: 0x0029C810
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && !this.IsTrigger;
			if (flag)
			{
				this.m_added = 500;
				player.AddBlood(-this.m_added, 1);
				bool flag2 = player.Blood <= 0;
				if (flag2)
				{
					player.Die();
				}
				this.IsTrigger = true;
			}
		}

		// Token: 0x04004C8C RID: 19596
		private int m_type = 0;

		// Token: 0x04004C8D RID: 19597
		private int m_count = 0;

		// Token: 0x04004C8E RID: 19598
		private int m_probability = 0;

		// Token: 0x04004C8F RID: 19599
		private int m_delay = 0;

		// Token: 0x04004C90 RID: 19600
		private int m_coldDown = 0;

		// Token: 0x04004C91 RID: 19601
		private int m_currentId;

		// Token: 0x04004C92 RID: 19602
		private int m_added = 0;
	}
}
