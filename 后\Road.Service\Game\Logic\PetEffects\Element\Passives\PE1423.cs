﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D83 RID: 3459
	public class PE1423 : BasePetEffect
	{
		// Token: 0x06007B24 RID: 31524 RVA: 0x00291D0C File Offset: 0x0028FF0C
		public PE1423(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1423, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B25 RID: 31525 RVA: 0x00291D8C File Offset: 0x0028FF8C
		public override bool Start(Living living)
		{
			PE1423 pe = living.PetEffectList.GetOfType(ePetEffectType.AE1423) as PE1423;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B26 RID: 31526 RVA: 0x0002ED74 File Offset: 0x0002CF74
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.player_beginNextTurn;
		}

		// Token: 0x06007B27 RID: 31527 RVA: 0x0002ED8A File Offset: 0x0002CF8A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.player_beginNextTurn;
		}

		// Token: 0x06007B28 RID: 31528 RVA: 0x00291DEC File Offset: 0x0028FFEC
		public void player_beginNextTurn(Living living)
		{
			bool flag = this.m_added != 0;
			if (!flag)
			{
				List<Player> allEnemyPlayers = living.Game.GetAllEnemyPlayers(living);
				foreach (Player player in allEnemyPlayers)
				{
					bool flag2 = player.Defence != 0.0;
					if (flag2)
					{
						this.m_added = (int)(player.Defence * 10.0 / 100.0);
						player.Defence -= (double)this.m_added;
						player.Game.SendPetBuff(player, base.ElementInfo, true);
					}
				}
			}
		}

		// Token: 0x040048D5 RID: 18645
		private int m_type = 0;

		// Token: 0x040048D6 RID: 18646
		private int m_count = 0;

		// Token: 0x040048D7 RID: 18647
		private int m_probability = 0;

		// Token: 0x040048D8 RID: 18648
		private int m_delay = 0;

		// Token: 0x040048D9 RID: 18649
		private int m_coldDown = 0;

		// Token: 0x040048DA RID: 18650
		private int m_currentId;

		// Token: 0x040048DB RID: 18651
		private int m_added = 0;
	}
}
