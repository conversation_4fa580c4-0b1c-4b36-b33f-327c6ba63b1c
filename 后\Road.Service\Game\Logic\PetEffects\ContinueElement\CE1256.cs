﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EB5 RID: 3765
	public class CE1256 : BasePetEffect
	{
		// Token: 0x060081ED RID: 33261 RVA: 0x002AEAF0 File Offset: 0x002ACCF0
		public CE1256(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1256, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081EE RID: 33262 RVA: 0x002AEB70 File Offset: 0x002ACD70
		public override bool Start(Living living)
		{
			CE1256 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1256) as CE1256;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081EF RID: 33263 RVA: 0x00033132 File Offset: 0x00031332
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081F0 RID: 33264 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081F1 RID: 33265 RVA: 0x002AEBD0 File Offset: 0x002ACDD0
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				bool flag2 = living.Attack < (double)this.m_added;
				if (flag2)
				{
					this.m_added = (int)living.Attack - 1;
				}
				living.Attack -= (double)this.m_added;
			}
		}

		// Token: 0x060081F2 RID: 33266 RVA: 0x002AEC30 File Offset: 0x002ACE30
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060081F3 RID: 33267 RVA: 0x002AEC64 File Offset: 0x002ACE64
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Attack += (double)this.m_added;
			this.m_added = 0;
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005130 RID: 20784
		private int m_type = 0;

		// Token: 0x04005131 RID: 20785
		private int m_count = 0;

		// Token: 0x04005132 RID: 20786
		private int m_probability = 0;

		// Token: 0x04005133 RID: 20787
		private int m_delay = 0;

		// Token: 0x04005134 RID: 20788
		private int m_coldDown = 0;

		// Token: 0x04005135 RID: 20789
		private int m_currentId;

		// Token: 0x04005136 RID: 20790
		private int m_added = 0;
	}
}
