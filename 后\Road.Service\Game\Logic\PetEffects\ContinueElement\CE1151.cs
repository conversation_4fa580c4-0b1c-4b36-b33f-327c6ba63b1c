﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E74 RID: 3700
	public class CE1151 : BasePetEffect
	{
		// Token: 0x06008047 RID: 32839 RVA: 0x002A8724 File Offset: 0x002A6924
		public CE1151(int count, int probability, int type, int skillId, int delay, string elementID, Living source)
			: base(ePetEffectType.CE1151, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
			this.m_source = source;
		}

		// Token: 0x06008048 RID: 32840 RVA: 0x002A87AC File Offset: 0x002A69AC
		public override bool Start(Living living)
		{
			CE1151 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1151) as CE1151;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008049 RID: 32841 RVA: 0x0003208E File Offset: 0x0003028E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600804A RID: 32842 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600804B RID: 32843 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginNextTurn(Living living)
		{
		}

		// Token: 0x0600804C RID: 32844 RVA: 0x002A880C File Offset: 0x002A6A0C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 300;
				living.AddBlood(-this.m_added, 1);
				bool flag2 = living.Blood <= 0;
				if (flag2)
				{
					living.Die();
					bool flag3 = this.m_source != null && this.m_source is Player;
					if (flag3)
					{
						(this.m_source as Player).PlayerDetail.OnKillingLiving(this.m_source.Game, 2, living.Id, living.IsLiving, this.m_added);
					}
				}
			}
		}

		// Token: 0x0600804D RID: 32845 RVA: 0x000320CA File Offset: 0x000302CA
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F63 RID: 20323
		private int m_type = 0;

		// Token: 0x04004F64 RID: 20324
		private int m_count = 0;

		// Token: 0x04004F65 RID: 20325
		private int m_probability = 0;

		// Token: 0x04004F66 RID: 20326
		private int m_delay = 0;

		// Token: 0x04004F67 RID: 20327
		private int m_coldDown = 0;

		// Token: 0x04004F68 RID: 20328
		private int m_currentId;

		// Token: 0x04004F69 RID: 20329
		private int m_added = 0;

		// Token: 0x04004F6A RID: 20330
		private Living m_source;
	}
}
