﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000ED2 RID: 3794
	public class AddBombEquipEffect : BasePlayerEffect
	{
		// Token: 0x0600829B RID: 33435 RVA: 0x00033735 File Offset: 0x00031935
		public AddBombEquipEffect(int count, int probability)
			: base(eEffectType.AddBombEquipEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x0600829C RID: 33436 RVA: 0x002B12E4 File Offset: 0x002AF4E4
		public override bool Start(Living living)
		{
			AddBombEquipEffect addBombEquipEffect = living.EffectList.GetOfType(eEffectType.AddBombEquipEffect) as AddBombEquipEffect;
			bool flag = addBombEquipEffect != null;
			bool flag2;
			if (flag)
			{
				this.m_probability = ((this.m_probability > addBombEquipEffect.m_probability) ? this.m_probability : addBombEquipEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600829D RID: 33437 RVA: 0x0003375D File Offset: 0x0003195D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginAttacking += this.ChangeProperty;
			player.BeforePlayerShoot += this.playerShot;
		}

		// Token: 0x0600829E RID: 33438 RVA: 0x00033786 File Offset: 0x00031986
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginAttacking -= this.ChangeProperty;
			player.BeforePlayerShoot -= this.playerShot;
		}

		// Token: 0x0600829F RID: 33439 RVA: 0x002B1340 File Offset: 0x002AF540
		private void ChangeProperty(Living player)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000 && player is Player && (player as Player).CurrentBall.ID != 3;
			if (flag)
			{
				this.IsTrigger = true;
				(player as Player).ShootCount += this.m_count;
				player.AttackEffectTrigger = true;
			}
		}

		// Token: 0x060082A0 RID: 33440 RVA: 0x002B13C0 File Offset: 0x002AF5C0
		private void playerShot(Player player, int ball)
		{
			bool flag = this.IsTrigger && player.CurrentBall.ID != 1 && player.CurrentBall.ID != 64 && player.CurrentBall.ID != 5;
			if (flag)
			{
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("AddBombEquipEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051C9 RID: 20937
		private int m_count = 0;

		// Token: 0x040051CA RID: 20938
		private int m_probability = 0;
	}
}
