﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D1F RID: 3359
	public class AE4844 : BasePetEffect
	{
		// Token: 0x06007900 RID: 30976 RVA: 0x0002D1C9 File Offset: 0x0002B3C9
		public AE4844(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE4844, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x06007901 RID: 30977 RVA: 0x00288E20 File Offset: 0x00287020
		public override bool Start(Living living)
		{
			AE4844 ae = living.PetEffectList.GetOfType(ePetEffectType.AE4844) as AE4844;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007902 RID: 30978 RVA: 0x0002D1FB File Offset: 0x0002B3FB
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x06007903 RID: 30979 RVA: 0x0002D211 File Offset: 0x0002B411
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x06007904 RID: 30980 RVA: 0x00288E80 File Offset: 0x00287080
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_currentId && living.Game is PVPGame;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				new CE4844(this.m_count, base.Info.ID.ToString()).Start(living);
			}
		}

		// Token: 0x040046E4 RID: 18148
		private int m_count;

		// Token: 0x040046E5 RID: 18149
		private int m_probability;

		// Token: 0x040046E6 RID: 18150
		private int m_currentId;
	}
}
