﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E3B RID: 3643
	public class AE4845 : BasePetEffect
	{
		// Token: 0x06007EF0 RID: 32496 RVA: 0x002A30C4 File Offset: 0x002A12C4
		public AE4845(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1127, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007EF1 RID: 32497 RVA: 0x002A3144 File Offset: 0x002A1344
		public override bool Start(Living living)
		{
			AE4845 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1127) as AE4845;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007EF2 RID: 32498 RVA: 0x000314B0 File Offset: 0x0002F6B0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007EF3 RID: 32499 RVA: 0x000314D9 File Offset: 0x0002F6D9
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007EF4 RID: 32500 RVA: 0x002A31A4 File Offset: 0x002A13A4
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 180;
				player.CurrentDamagePlus += (float)(this.m_added / 100);
			}
		}

		// Token: 0x06007EF5 RID: 32501 RVA: 0x002A31F0 File Offset: 0x002A13F0
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_added / 100);
				this.m_added = 0;
			}
		}

		// Token: 0x04004DD8 RID: 19928
		private int m_type = 0;

		// Token: 0x04004DD9 RID: 19929
		private int m_count = 0;

		// Token: 0x04004DDA RID: 19930
		private int m_probability = 0;

		// Token: 0x04004DDB RID: 19931
		private int m_delay = 0;

		// Token: 0x04004DDC RID: 19932
		private int m_coldDown = 0;

		// Token: 0x04004DDD RID: 19933
		private int m_currentId;

		// Token: 0x04004DDE RID: 19934
		private int m_added = 0;
	}
}
