﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FE9 RID: 4073
	public static class ShopMgr
	{
		// Token: 0x06008B56 RID: 35670 RVA: 0x002FBC30 File Offset: 0x002F9E30
		public static bool Init()
		{
			return ShopMgr.ReLoad();
		}

		// Token: 0x06008B57 RID: 35671 RVA: 0x002FBC48 File Offset: 0x002F9E48
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, ShopItemInfo> shopAll = ShopMgr.GetShopAll();
				bool flag = shopAll.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, ShopItemInfo>>(ref ShopMgr.m_shop, shopAll);
				}
				Dictionary<int, ShopGoodsShowListInfo> allShopGoods = ShopMgr.GetAllShopGoods();
				bool flag2 = allShopGoods.Count > 0;
				if (flag2)
				{
					Interlocked.Exchange<Dictionary<int, ShopGoodsShowListInfo>>(ref ShopMgr.m_goodshow, allShopGoods);
				}
				return true;
			}
			catch (Exception ex)
			{
				ShopMgr.log.Error("ShopInfoMgr", ex);
			}
			return false;
		}

		// Token: 0x06008B58 RID: 35672 RVA: 0x002FBCD0 File Offset: 0x002F9ED0
		private static Dictionary<int, ShopItemInfo> GetShopAll()
		{
			Dictionary<int, ShopItemInfo> dictionary = new Dictionary<int, ShopItemInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				ShopItemInfo[] alllShop = produceBussiness.GetALllShop();
				ShopItemInfo[] array = alllShop;
				ShopItemInfo[] array2 = array;
				ShopItemInfo[] array3 = array2;
				foreach (ShopItemInfo shopItemInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(shopItemInfo.ID);
					if (flag)
					{
						dictionary.Add(shopItemInfo.ID, shopItemInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008B59 RID: 35673 RVA: 0x002FBD6C File Offset: 0x002F9F6C
		private static Dictionary<int, ShopGoodsShowListInfo> GetAllShopGoods()
		{
			Dictionary<int, ShopGoodsShowListInfo> dictionary = new Dictionary<int, ShopGoodsShowListInfo>();
			Dictionary<int, ShopGoodsShowListInfo> dictionary2;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				ShopGoodsShowListInfo[] allShopGoodsShowList = produceBussiness.GetAllShopGoodsShowList();
				ShopGoodsShowListInfo[] array = allShopGoodsShowList;
				ShopGoodsShowListInfo[] array2 = array;
				ShopGoodsShowListInfo[] array3 = array2;
				foreach (ShopGoodsShowListInfo shopGoodsShowListInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(shopGoodsShowListInfo.ShopId);
					if (flag)
					{
						dictionary.Add(shopGoodsShowListInfo.ShopId, shopGoodsShowListInfo);
					}
				}
				dictionary2 = dictionary;
			}
			return dictionary2;
		}

		// Token: 0x06008B5A RID: 35674 RVA: 0x002FBE04 File Offset: 0x002FA004
		public static ShopItemInfo GetShopItemInfoById(int ID)
		{
			bool flag = ShopMgr.m_shop.ContainsKey(ID);
			ShopItemInfo shopItemInfo;
			if (flag)
			{
				shopItemInfo = ShopMgr.m_shop[ID];
			}
			else
			{
				shopItemInfo = null;
			}
			return shopItemInfo;
		}

		// Token: 0x06008B5B RID: 35675 RVA: 0x002FBE38 File Offset: 0x002FA038
		public static bool IsOnShop(int Id)
		{
			bool flag = ShopMgr.m_goodshow == null;
			if (flag)
			{
				ShopMgr.ReLoad();
			}
			bool flag2 = ShopMgr.IsSpecialItem(Id);
			bool flag3;
			if (flag2)
			{
				flag3 = true;
			}
			else
			{
				ShopMgr.m_lock.AcquireReaderLock(-1);
				try
				{
					bool flag4 = ShopMgr.m_goodshow.Keys.Contains(Id);
					if (flag4)
					{
						return true;
					}
				}
				finally
				{
					ShopMgr.m_lock.ReleaseReaderLock();
				}
				flag3 = false;
			}
			return flag3;
		}

		// Token: 0x06008B5C RID: 35676 RVA: 0x002FBEB8 File Offset: 0x002FA0B8
		public static bool IsSpecialItem(int Id)
		{
			if (Id <= 1100801)
			{
				if (Id != 1100401 && Id != 1100801)
				{
					goto IL_0039;
				}
			}
			else if (Id != 1101201 && Id != 1101601)
			{
				goto IL_0039;
			}
			return true;
			IL_0039:
			return false;
		}

		// Token: 0x06008B5D RID: 35677 RVA: 0x002FBF04 File Offset: 0x002FA104
		public static bool CanBuy(int shopID, int consortiaShopLevel, ref bool isBinds, int cousortiaID, int playerRiches)
		{
			bool flag = false;
			using (ConsortiaBussiness consortiaBussiness = new ConsortiaBussiness())
			{
				if (shopID <= 72)
				{
					switch (shopID)
					{
					case 1:
						flag = true;
						isBinds = false;
						break;
					case 2:
						flag = true;
						isBinds = false;
						break;
					case 3:
						flag = true;
						isBinds = false;
						break;
					case 4:
						flag = true;
						isBinds = false;
						break;
					case 5:
					case 6:
					case 7:
					case 8:
					case 9:
					case 10:
						break;
					case 11:
					{
						ConsortiaEquipControlInfo consortiaEquipControlInfo = consortiaBussiness.GetConsortiaEuqipRiches(cousortiaID, 1, 1);
						bool flag2 = consortiaShopLevel >= consortiaEquipControlInfo.Level && playerRiches >= consortiaEquipControlInfo.Riches;
						if (flag2)
						{
							flag = true;
							isBinds = true;
						}
						break;
					}
					case 12:
					{
						ConsortiaEquipControlInfo consortiaEquipControlInfo = consortiaBussiness.GetConsortiaEuqipRiches(cousortiaID, 2, 1);
						bool flag3 = consortiaShopLevel >= consortiaEquipControlInfo.Level && playerRiches >= consortiaEquipControlInfo.Riches;
						if (flag3)
						{
							flag = true;
							isBinds = true;
						}
						break;
					}
					case 13:
					{
						ConsortiaEquipControlInfo consortiaEquipControlInfo = consortiaBussiness.GetConsortiaEuqipRiches(cousortiaID, 3, 1);
						bool flag4 = consortiaShopLevel >= consortiaEquipControlInfo.Level && playerRiches >= consortiaEquipControlInfo.Riches;
						if (flag4)
						{
							flag = true;
							isBinds = true;
						}
						break;
					}
					case 14:
					{
						ConsortiaEquipControlInfo consortiaEquipControlInfo = consortiaBussiness.GetConsortiaEuqipRiches(cousortiaID, 4, 1);
						bool flag5 = consortiaShopLevel >= consortiaEquipControlInfo.Level && playerRiches >= consortiaEquipControlInfo.Riches;
						if (flag5)
						{
							flag = true;
							isBinds = true;
						}
						break;
					}
					case 15:
					{
						ConsortiaEquipControlInfo consortiaEquipControlInfo = consortiaBussiness.GetConsortiaEuqipRiches(cousortiaID, 5, 1);
						bool flag6 = consortiaShopLevel >= consortiaEquipControlInfo.Level && playerRiches >= consortiaEquipControlInfo.Riches;
						if (flag6)
						{
							flag = true;
							isBinds = true;
						}
						break;
					}
					default:
						if (shopID != 20)
						{
							if (shopID == 72)
							{
								flag = true;
								isBinds = true;
								return flag;
							}
						}
						else
						{
							flag = true;
							isBinds = true;
						}
						break;
					}
				}
				else if (shopID <= 154)
				{
					if (shopID == 91)
					{
						flag = true;
						isBinds = true;
						return flag;
					}
					if (shopID == 154)
					{
						flag = true;
						isBinds = true;
						return flag;
					}
				}
				else
				{
					if (shopID == 160)
					{
						flag = true;
						isBinds = true;
						return flag;
					}
					if (shopID == 161)
					{
						flag = true;
						isBinds = true;
						return flag;
					}
				}
			}
			return flag;
		}

		// Token: 0x06008B5E RID: 35678 RVA: 0x002FC15C File Offset: 0x002FA35C
		public static int FindItemTemplateID(int id)
		{
			bool flag = ShopMgr.m_shop.ContainsKey(id);
			int num;
			if (flag)
			{
				num = ShopMgr.m_shop[id].TemplateID;
			}
			else
			{
				num = 0;
			}
			return num;
		}

		// Token: 0x06008B5F RID: 35679 RVA: 0x002FC194 File Offset: 0x002FA394
		public static List<ShopItemInfo> FindShopbyTemplatID(int TemplatID)
		{
			List<ShopItemInfo> list = new List<ShopItemInfo>();
			foreach (ShopItemInfo shopItemInfo in ShopMgr.m_shop.Values)
			{
				bool flag = shopItemInfo.TemplateID == TemplatID;
				if (flag)
				{
					list.Add(shopItemInfo);
				}
			}
			return list;
		}

		// Token: 0x06008B60 RID: 35680 RVA: 0x002FC210 File Offset: 0x002FA410
		public static List<ShopItemInfo> FindShopByID(int ID)
		{
			List<ShopItemInfo> list = new List<ShopItemInfo>();
			foreach (ShopItemInfo shopItemInfo in ShopMgr.m_shop.Values)
			{
				bool flag = shopItemInfo.ID == ID;
				if (flag)
				{
					list.Add(shopItemInfo);
				}
			}
			return list;
		}

		// Token: 0x06008B61 RID: 35681 RVA: 0x002FC28C File Offset: 0x002FA48C
		public static ItemInfo CreateItem(ShopItemInfo shopItem, int addtype, int valuetype, string color, string skin, bool isBinding)
		{
			bool flag = shopItem != null;
			ItemInfo itemInfo2;
			if (flag)
			{
				ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(shopItem.TemplateID);
				ItemInfo itemInfo = ItemInfo.CreateFromTemplate(itemTemplateInfo, 1, addtype);
				bool flag2 = shopItem.BuyType == 0;
				if (flag2)
				{
					bool flag3 = 1 == valuetype;
					if (flag3)
					{
						itemInfo.ValidDate = shopItem.AUnit;
					}
					bool flag4 = 2 == valuetype;
					if (flag4)
					{
						itemInfo.ValidDate = shopItem.BUnit;
					}
					bool flag5 = 3 == valuetype;
					if (flag5)
					{
						itemInfo.ValidDate = shopItem.CUnit;
					}
				}
				else
				{
					bool flag6 = 1 == valuetype;
					if (flag6)
					{
						itemInfo.Count = shopItem.AUnit;
					}
					bool flag7 = 2 == valuetype;
					if (flag7)
					{
						itemInfo.Count = shopItem.BUnit;
					}
					bool flag8 = 3 == valuetype;
					if (flag8)
					{
						itemInfo.Count = shopItem.CUnit;
					}
				}
				itemInfo.Color = color ?? "";
				itemInfo.Skin = skin ?? "";
				if (isBinding)
				{
					itemInfo.IsBinds = true;
				}
				else
				{
					itemInfo.IsBinds = Convert.ToBoolean(shopItem.IsBind);
				}
				itemInfo2 = itemInfo;
			}
			else
			{
				itemInfo2 = null;
			}
			return itemInfo2;
		}

		// Token: 0x06008B62 RID: 35682 RVA: 0x002FC3BC File Offset: 0x002FA5BC
		public static bool FindSpecialItemInfo(ItemInfo info, ref SpecialItemBoxInfo specialValue)
		{
			int templateID = info.TemplateID;
			int num = templateID;
			if (num <= -400)
			{
				if (num <= -6100)
				{
					if (num == -6300)
					{
						specialValue.NiuNiuMoney += info.Count;
						return true;
					}
					if (num == -6100)
					{
						specialValue.MoonChip += info.Count;
						return true;
					}
				}
				else
				{
					if (num == -5200)
					{
						specialValue.MarkCount += info.Count;
						return true;
					}
					if (num == -400)
					{
						specialValue.MyHornor += info.Count;
						return true;
					}
				}
			}
			else if (num <= -200)
			{
				if (num == -300)
				{
					specialValue.GiftToken += info.Count;
					return true;
				}
				if (num == -200)
				{
					specialValue.Money += info.Count;
					return true;
				}
			}
			else
			{
				if (num == -100)
				{
					specialValue.Gold += info.Count;
					return true;
				}
				if (num == 11107)
				{
					specialValue.GP += info.Count;
					return true;
				}
			}
			return false;
		}

		// Token: 0x04005543 RID: 21827
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005544 RID: 21828
		private static Dictionary<int, ShopItemInfo> m_shop = new Dictionary<int, ShopItemInfo>();

		// Token: 0x04005545 RID: 21829
		private static Dictionary<int, ShopGoodsShowListInfo> m_goodshow = new Dictionary<int, ShopGoodsShowListInfo>();

		// Token: 0x04005546 RID: 21830
		private static Dictionary<int, int> m_LimitCount = new Dictionary<int, int>();

		// Token: 0x04005547 RID: 21831
		private static ReaderWriterLock m_lock = new ReaderWriterLock();
	}
}
