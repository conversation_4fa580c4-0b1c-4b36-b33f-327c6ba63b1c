﻿using System;
using System.Collections;
using System.Net.Sockets;
using System.Reflection;
using System.Text;
using System.Threading;
using log4net;
using Road.Base.Packets;

namespace Game.Base.Packets
{
	// Token: 0x02000F89 RID: 3977
	public class StreamProcessor
	{
		// Token: 0x0600876C RID: 34668 RVA: 0x002C69C8 File Offset: 0x002C4BC8
		public StreamProcessor(BaseClient client)
		{
			this.m_client = client;
			this.m_client.resetKey();
			this.m_tcpSendBuffer = client.SendBuffer;
			this.m_tcpQueue = new Queue(256);
			this.send_event = new SocketAsyncEventArgs();
			this.send_event.UserToken = this;
			this.send_event.Completed += StreamProcessor.AsyncTcpSendCallback;
			this.send_event.SetBuffer(this.m_tcpSendBuffer, 0, 0);
			this.send_fsm = new FSM(2059198199, 1501, "send_fsm");
			this.receive_fsm = new FSM(2059198199, 1501, "receive_fsm");
		}

		// Token: 0x0600876D RID: 34669 RVA: 0x00035DB3 File Offset: 0x00033FB3
		public void SetFsm(int adder, int muliter)
		{
			this.send_fsm.Setup(adder, muliter);
			this.receive_fsm.Setup(adder, muliter);
		}

		// Token: 0x0600876E RID: 34670 RVA: 0x002C6A94 File Offset: 0x002C4C94
		public void SendTCP(GSPacketIn packet)
		{
			packet.WriteHeader();
			packet.Offset = 0;
			bool flag = !this.m_client.Socket.Connected;
			if (!flag)
			{
				try
				{
					Statistics.BytesOut += (long)packet.Length;
					Statistics.PacketsOut += 1L;
					object syncRoot = this.m_tcpQueue.SyncRoot;
					lock (syncRoot)
					{
						this.m_tcpQueue.Enqueue(packet);
						bool sendingTcp = this.m_sendingTcp;
						if (sendingTcp)
						{
							return;
						}
						this.m_sendingTcp = true;
					}
					bool asyncPostSend = this.m_client.AsyncPostSend;
					if (asyncPostSend)
					{
						ThreadPool.QueueUserWorkItem(new WaitCallback(StreamProcessor.AsyncSendTcpImp), this);
					}
					else
					{
						StreamProcessor.AsyncTcpSendCallback(this, this.send_event);
					}
				}
				catch (Exception ex)
				{
					StreamProcessor.log.Error("SendTCP", ex);
					StreamProcessor.log.WarnFormat("It seems <{0}> went linkdead. Closing connection. (SendTCP, {1}: {2})", this.m_client, ex.GetType(), ex.Message);
					this.m_client.Disconnect();
				}
			}
		}

		// Token: 0x0600876F RID: 34671 RVA: 0x002C6BD8 File Offset: 0x002C4DD8
		private static void AsyncSendTcpImp(object state)
		{
			StreamProcessor streamProcessor = state as StreamProcessor;
			BaseClient client = streamProcessor.m_client;
			try
			{
				StreamProcessor.AsyncTcpSendCallback(streamProcessor, streamProcessor.send_event);
			}
			catch (Exception ex)
			{
				StreamProcessor.log.Error("AsyncSendTcpImp", ex);
				client.Disconnect();
			}
		}

		// Token: 0x06008770 RID: 34672 RVA: 0x002C6C34 File Offset: 0x002C4E34
		private static void AsyncTcpSendCallback(object sender, SocketAsyncEventArgs e)
		{
			StreamProcessor streamProcessor = (StreamProcessor)e.UserToken;
			BaseClient client = streamProcessor.m_client;
			try
			{
				Queue tcpQueue = streamProcessor.m_tcpQueue;
				bool flag = tcpQueue == null || !client.Socket.Connected;
				if (!flag)
				{
					int bytesTransferred = e.BytesTransferred;
					byte[] tcpSendBuffer = streamProcessor.m_tcpSendBuffer;
					int num = 0;
					bool flag2 = bytesTransferred != e.Count && streamProcessor.m_sendBufferLength > bytesTransferred;
					if (flag2)
					{
						num = streamProcessor.m_sendBufferLength - bytesTransferred;
						Array.Copy(tcpSendBuffer, bytesTransferred, tcpSendBuffer, 0, num);
					}
					e.SetBuffer(0, 0);
					int num2 = streamProcessor.m_firstPkgOffset;
					object syncRoot = tcpQueue.SyncRoot;
					lock (syncRoot)
					{
						bool flag4 = tcpQueue.Count > 0;
						if (flag4)
						{
							do
							{
								PacketIn packetIn = (PacketIn)tcpQueue.Peek();
								int num3 = ((!client.Encryted) ? packetIn.CopyTo(tcpSendBuffer, num, num2) : packetIn.CopyTo(tcpSendBuffer, num, num2, client.SEND_KEY, ref client.KEYREF));
								num2 += num3;
								num += num3;
								bool flag5 = packetIn.Length <= num2;
								if (flag5)
								{
									tcpQueue.Dequeue();
									num2 = 0;
								}
							}
							while (tcpSendBuffer.Length != num && tcpQueue.Count > 0);
						}
						streamProcessor.m_firstPkgOffset = num2;
						bool flag6 = num <= 0;
						if (flag6)
						{
							streamProcessor.m_sendingTcp = false;
							return;
						}
					}
					streamProcessor.m_sendBufferLength = num;
					e.SetBuffer(0, num);
					bool flag7 = !client.SendAsync(e);
					if (flag7)
					{
						e.SetBuffer(0, 0);
						StreamProcessor.AsyncTcpSendCallback(sender, e);
					}
				}
			}
			catch (Exception ex)
			{
				StreamProcessor.log.Error("AsyncTcpSendCallback", ex);
				StreamProcessor.log.WarnFormat("It seems <{0}> went linkdead. Closing connection. (SendTCP, {1}: {2})", client, ex.GetType(), ex.Message);
				client.Disconnect();
			}
		}

		// Token: 0x06008771 RID: 34673 RVA: 0x002C6E6C File Offset: 0x002C506C
		public void ReceiveBytes(int numBytes)
		{
			lock (this)
			{
				byte[] packetBuf = this.m_client.PacketBuf;
				int num = this.m_client.PacketBufSize + numBytes;
				bool flag2 = num < 20;
				if (flag2)
				{
					this.m_client.PacketBufSize = num;
				}
				else
				{
					this.m_client.PacketBufSize = 0;
					int num2 = 0;
					int num3;
					int num5;
					for (;;)
					{
						num3 = 0;
						bool encryted = this.m_client.Encryted;
						if (encryted)
						{
							byte[] array = StreamProcessor.CloneArrary(this.m_client.RECEIVE_KEY, 8);
							while (num2 + 8 < num)
							{
								byte[] array2 = StreamProcessor.DecryptBytes(packetBuf, num2, 8, array);
								int num4 = ((int)array2[0] << 8) + (int)array2[1];
								bool flag3 = num4 == 29099;
								if (flag3)
								{
									num3 = ((int)array2[2] << 8) + (int)array2[3];
									break;
								}
								num2++;
							}
						}
						else
						{
							while (num2 + 4 < num)
							{
								int num4 = ((int)packetBuf[num2] << 8) + (int)packetBuf[num2 + 1];
								bool flag4 = num4 == 29099;
								if (flag4)
								{
									num3 = ((int)packetBuf[num2 + 2] << 8) + (int)packetBuf[num2 + 3];
									break;
								}
								num2++;
							}
						}
						bool flag5 = (num3 != 0 && num3 < 20) || num3 > 8192;
						if (flag5)
						{
							break;
						}
						num5 = num - num2;
						bool flag6 = num5 < num3 || num3 == 0;
						if (flag6)
						{
							goto Block_11;
						}
						GSPacketIn gspacketIn = new GSPacketIn(new byte[8192], 8192);
						bool encryted2 = this.m_client.Encryted;
						if (encryted2)
						{
							gspacketIn.CopyFrom(packetBuf, num2, 0, num3, this.m_client.RECEIVE_KEY);
						}
						else
						{
							gspacketIn.CopyFrom(packetBuf, num2, 0, num3);
						}
						gspacketIn.ReadHeader();
						try
						{
							this.m_client.OnRecvPacket(gspacketIn);
						}
						catch (Exception ex)
						{
							bool isErrorEnabled = StreamProcessor.log.IsErrorEnabled;
							if (isErrorEnabled)
							{
								StreamProcessor.log.Error("HandlePacket(pak)::", ex);
							}
						}
						num2 += num3;
						if (num - 1 <= num2)
						{
							goto IL_02EE;
						}
					}
					StreamProcessor.log.Error(string.Concat(new string[]
					{
						"packetLength:",
						num3.ToString(),
						",GSPacketIn.HDR_SIZE:",
						20.ToString(),
						",offset:",
						num2.ToString(),
						",bufferSize:",
						num.ToString(),
						",numBytes:",
						numBytes.ToString()
					}));
					StreamProcessor.log.ErrorFormat("Err pkg from {0}:", this.m_client.TcpEndpoint);
					this.m_client.PacketBufSize = 0;
					bool strict = this.m_client.Strict;
					if (strict)
					{
						this.m_client.Disconnect();
					}
					return;
					Block_11:
					Array.Copy(packetBuf, num2, packetBuf, 0, num5);
					this.m_client.PacketBufSize = num5;
					IL_02EE:
					bool flag7 = num - 1 == num2;
					if (flag7)
					{
						packetBuf[0] = packetBuf[num2];
						this.m_client.PacketBufSize = 1;
					}
				}
			}
		}

		// Token: 0x06008772 RID: 34674 RVA: 0x002C71CC File Offset: 0x002C53CC
		public static byte[] CloneArrary(byte[] arr, int length = 8)
		{
			byte[] array = new byte[length];
			for (int i = 0; i < length; i++)
			{
				array[i] = arr[i];
			}
			return array;
		}

		// Token: 0x06008773 RID: 34675 RVA: 0x002C7200 File Offset: 0x002C5400
		public static string PrintArray(byte[] arr, int length = 8)
		{
			byte[] array = new byte[length];
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("[");
			for (int i = 0; i < length; i++)
			{
				stringBuilder.AppendFormat("{0} ", arr[i]);
			}
			stringBuilder.Append("]");
			return stringBuilder.ToString();
		}

		// Token: 0x06008774 RID: 34676 RVA: 0x002C7268 File Offset: 0x002C5468
		public static string PrintArray(byte[] arr, int first, int length)
		{
			byte[] array = new byte[length];
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("[");
			for (int i = first; i < first + length; i++)
			{
				stringBuilder.AppendFormat("{0} ", arr[i]);
			}
			stringBuilder.Append("]");
			return stringBuilder.ToString();
		}

		// Token: 0x06008775 RID: 34677 RVA: 0x002C72D0 File Offset: 0x002C54D0
		public static byte[] DecryptBytes(byte[] param1, int curOffset, int param2, byte[] param3)
		{
			byte[] array = new byte[param2];
			for (int i = 0; i < param2; i++)
			{
				array[i] = param1[i];
			}
			for (int j = 0; j < param2; j++)
			{
				bool flag = j > 0;
				if (flag)
				{
					param3[j % 8] = (byte)((int)(param3[j % 8] + param1[curOffset + j - 1]) ^ j);
					array[j] = (param1[curOffset + j] - param1[curOffset + j - 1]) ^ param3[j % 8];
				}
				else
				{
					array[0] = param1[curOffset] ^ param3[0];
				}
			}
			return array;
		}

		// Token: 0x06008776 RID: 34678 RVA: 0x00035DD2 File Offset: 0x00033FD2
		public void Dispose()
		{
			this.send_event.Dispose();
			this.m_tcpQueue.Clear();
		}

		// Token: 0x040053A1 RID: 21409
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040053A2 RID: 21410
		protected readonly BaseClient m_client;

		// Token: 0x040053A3 RID: 21411
		private FSM send_fsm;

		// Token: 0x040053A4 RID: 21412
		private FSM receive_fsm;

		// Token: 0x040053A5 RID: 21413
		private SocketAsyncEventArgs send_event;

		// Token: 0x040053A6 RID: 21414
		protected byte[] m_tcpSendBuffer;

		// Token: 0x040053A7 RID: 21415
		protected Queue m_tcpQueue;

		// Token: 0x040053A8 RID: 21416
		protected bool m_sendingTcp;

		// Token: 0x040053A9 RID: 21417
		protected int m_firstPkgOffset = 0;

		// Token: 0x040053AA RID: 21418
		protected int m_sendBufferLength = 0;

		// Token: 0x040053AB RID: 21419
		public static byte[] KEY = new byte[] { 174, 191, 86, 120, 171, 205, 239, 241 };
	}
}
