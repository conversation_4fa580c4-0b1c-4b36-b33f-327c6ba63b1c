﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D71 RID: 3441
	public class PE1214 : BasePetEffect
	{
		// Token: 0x06007ACC RID: 31436 RVA: 0x00290614 File Offset: 0x0028E814
		public PE1214(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1214, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007ACD RID: 31437 RVA: 0x00290694 File Offset: 0x0028E894
		public override bool Start(Living living)
		{
			PE1214 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1214) as PE1214;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007ACE RID: 31438 RVA: 0x0002EAA4 File Offset: 0x0002CCA4
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.player_beginNextTurn;
		}

		// Token: 0x06007ACF RID: 31439 RVA: 0x0002EABA File Offset: 0x0002CCBA
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.player_beginNextTurn;
		}

		// Token: 0x06007AD0 RID: 31440 RVA: 0x002906F4 File Offset: 0x0028E8F4
		public void player_beginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				living.BaseGuard += (double)this.m_added;
			}
		}

		// Token: 0x04004857 RID: 18519
		private int m_type = 0;

		// Token: 0x04004858 RID: 18520
		private int m_count = 0;

		// Token: 0x04004859 RID: 18521
		private int m_probability = 0;

		// Token: 0x0400485A RID: 18522
		private int m_delay = 0;

		// Token: 0x0400485B RID: 18523
		private int m_coldDown = 0;

		// Token: 0x0400485C RID: 18524
		private int m_currentId;

		// Token: 0x0400485D RID: 18525
		private int m_added = 0;
	}
}
