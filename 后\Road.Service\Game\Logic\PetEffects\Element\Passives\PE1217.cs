﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D74 RID: 3444
	public class PE1217 : BasePetEffect
	{
		// Token: 0x06007ADA RID: 31450 RVA: 0x00290968 File Offset: 0x0028EB68
		public PE1217(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1217, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007ADB RID: 31451 RVA: 0x002909E8 File Offset: 0x0028EBE8
		public override bool Start(Living living)
		{
			PE1217 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1217) as PE1217;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007ADC RID: 31452 RVA: 0x00290A48 File Offset: 0x0028EC48
		protected override void OnAttachedToPlayer(Player player)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 3000;
				player.PetEffects.AddMaxBloodValue = this.m_added;
			}
		}

		// Token: 0x06007ADD RID: 31453 RVA: 0x00005683 File Offset: 0x00003883
		protected override void OnRemovedFromPlayer(Player player)
		{
		}

		// Token: 0x0400486C RID: 18540
		private int m_type = 0;

		// Token: 0x0400486D RID: 18541
		private int m_count = 0;

		// Token: 0x0400486E RID: 18542
		private int m_probability = 0;

		// Token: 0x0400486F RID: 18543
		private int m_delay = 0;

		// Token: 0x04004870 RID: 18544
		private int m_coldDown = 0;

		// Token: 0x04004871 RID: 18545
		private int m_currentId;

		// Token: 0x04004872 RID: 18546
		private int m_added = 0;
	}
}
