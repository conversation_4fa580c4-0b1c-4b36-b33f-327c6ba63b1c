﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FCF RID: 4047
	public class EmblemMgr
	{
		// Token: 0x06008AA0 RID: 35488 RVA: 0x002F7AB4 File Offset: 0x002F5CB4
		public static bool Init()
		{
			return EmblemMgr.ReLoad();
		}

		// Token: 0x06008AA1 RID: 35489 RVA: 0x002F7ACC File Offset: 0x002F5CCC
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, TsEmblemInfo> dictionary = EmblemMgr.LoadEmblemDb();
				bool flag = dictionary.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, TsEmblemInfo>>(ref EmblemMgr.m_emblem, dictionary);
				}
				Dictionary<int, TsScrollInfo> dictionary2 = EmblemMgr.LoadScrollDb();
				bool flag2 = dictionary2.Count > 0;
				if (flag2)
				{
					Interlocked.Exchange<Dictionary<int, TsScrollInfo>>(ref EmblemMgr.m_scroll, dictionary2);
				}
				return true;
			}
			catch (Exception ex)
			{
				EmblemMgr.log.Error("EmblemMgr", ex);
			}
			return false;
		}

		// Token: 0x06008AA2 RID: 35490 RVA: 0x002F7B54 File Offset: 0x002F5D54
		public static Dictionary<int, TsEmblemInfo> LoadEmblemDb()
		{
			Dictionary<int, TsEmblemInfo> dictionary = new Dictionary<int, TsEmblemInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				TsEmblemInfo[] allEmblems = produceBussiness.GetAllEmblems();
				TsEmblemInfo[] array = allEmblems;
				TsEmblemInfo[] array2 = array;
				TsEmblemInfo[] array3 = array2;
				foreach (TsEmblemInfo tsEmblemInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(tsEmblemInfo.TemplateID);
					if (flag)
					{
						dictionary.Add(tsEmblemInfo.TemplateID, tsEmblemInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008AA3 RID: 35491 RVA: 0x002F7BF0 File Offset: 0x002F5DF0
		public static Dictionary<int, TsScrollInfo> LoadScrollDb()
		{
			Dictionary<int, TsScrollInfo> dictionary = new Dictionary<int, TsScrollInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				TsScrollInfo[] allScrolls = produceBussiness.GetAllScrolls();
				TsScrollInfo[] array = allScrolls;
				TsScrollInfo[] array2 = array;
				TsScrollInfo[] array3 = array2;
				foreach (TsScrollInfo tsScrollInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(tsScrollInfo.TemplateID);
					if (flag)
					{
						dictionary.Add(tsScrollInfo.TemplateID, tsScrollInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008AA4 RID: 35492 RVA: 0x002F7C8C File Offset: 0x002F5E8C
		public static TsScrollInfo FindScroll(int scrollId)
		{
			bool flag = EmblemMgr.m_scroll == null;
			if (flag)
			{
				EmblemMgr.Init();
			}
			EmblemMgr.m_lock.AcquireReaderLock(-1);
			try
			{
				foreach (TsScrollInfo tsScrollInfo in EmblemMgr.m_scroll.Values)
				{
					bool flag2 = tsScrollInfo.TemplateID == scrollId;
					if (flag2)
					{
						return tsScrollInfo;
					}
				}
			}
			finally
			{
				EmblemMgr.m_lock.ReleaseReaderLock();
			}
			return null;
		}

		// Token: 0x06008AA5 RID: 35493 RVA: 0x002F7D3C File Offset: 0x002F5F3C
		public static TsEmblemInfo FindEmblemByID(int EmblemID)
		{
			bool flag = EmblemMgr.m_emblem == null;
			if (flag)
			{
				EmblemMgr.Init();
			}
			EmblemMgr.m_lock.AcquireReaderLock(-1);
			try
			{
				foreach (TsEmblemInfo tsEmblemInfo in EmblemMgr.m_emblem.Values)
				{
					bool flag2 = tsEmblemInfo.TemplateID == EmblemID;
					if (flag2)
					{
						return tsEmblemInfo;
					}
				}
			}
			finally
			{
				EmblemMgr.m_lock.ReleaseReaderLock();
			}
			return null;
		}

		// Token: 0x06008AA6 RID: 35494 RVA: 0x002F7DEC File Offset: 0x002F5FEC
		public static TsEmblemInfo FindEmblem(int EmblemID, int Profile)
		{
			bool flag = EmblemMgr.m_emblem == null;
			if (flag)
			{
				EmblemMgr.Init();
			}
			EmblemMgr.m_lock.AcquireReaderLock(-1);
			try
			{
				foreach (TsEmblemInfo tsEmblemInfo in EmblemMgr.m_emblem.Values)
				{
					bool flag2 = tsEmblemInfo.TemplateID == EmblemID && tsEmblemInfo.Profile == Profile;
					if (flag2)
					{
						return tsEmblemInfo;
					}
				}
			}
			finally
			{
				EmblemMgr.m_lock.ReleaseReaderLock();
			}
			return null;
		}

		// Token: 0x06008AA7 RID: 35495 RVA: 0x002F7EA8 File Offset: 0x002F60A8
		public static TsEmblemInfo FindEmblemByType(int currentProfile, int mainType)
		{
			bool flag = EmblemMgr.m_emblem == null;
			if (flag)
			{
				EmblemMgr.Init();
			}
			EmblemMgr.m_lock.AcquireReaderLock(-1);
			try
			{
				foreach (TsEmblemInfo tsEmblemInfo in EmblemMgr.m_emblem.Values)
				{
					bool flag2 = tsEmblemInfo.Profile == currentProfile && tsEmblemInfo.MainType == mainType;
					if (flag2)
					{
						return tsEmblemInfo;
					}
				}
			}
			finally
			{
				EmblemMgr.m_lock.ReleaseReaderLock();
			}
			return null;
		}

		// Token: 0x06008AA8 RID: 35496 RVA: 0x002F7F64 File Offset: 0x002F6164
		public static List<TsEmblemInfo> GetAllEmblem()
		{
			return EmblemMgr.m_emblem.Values.ToList<TsEmblemInfo>();
		}

		// Token: 0x06008AA9 RID: 35497 RVA: 0x002F7F88 File Offset: 0x002F6188
		public static List<TsScrollInfo> GetAllScroll()
		{
			return EmblemMgr.m_scroll.Values.ToList<TsScrollInfo>();
		}

		// Token: 0x040054F1 RID: 21745
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040054F2 RID: 21746
		private static ReaderWriterLock m_lock = new ReaderWriterLock();

		// Token: 0x040054F3 RID: 21747
		private static Dictionary<int, TsEmblemInfo> m_emblem = new Dictionary<int, TsEmblemInfo>();

		// Token: 0x040054F4 RID: 21748
		private static Dictionary<int, TsScrollInfo> m_scroll = new Dictionary<int, TsScrollInfo>();
	}
}
