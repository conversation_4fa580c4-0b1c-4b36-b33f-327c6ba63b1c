﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E8F RID: 3727
	public class CE1195 : BasePetEffect
	{
		// Token: 0x060080F4 RID: 33012 RVA: 0x002AB084 File Offset: 0x002A9284
		public CE1195(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1195, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060080F5 RID: 33013 RVA: 0x002AB104 File Offset: 0x002A9304
		public override bool Start(Living living)
		{
			CE1195 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1195) as CE1195;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080F6 RID: 33014 RVA: 0x002AB164 File Offset: 0x002A9364
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 200;
				player.Attack += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060080F7 RID: 33015 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060080F8 RID: 33016 RVA: 0x002AB1C8 File Offset: 0x002A93C8
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060080F9 RID: 33017 RVA: 0x00032743 File Offset: 0x00030943
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Attack -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005026 RID: 20518
		private int m_type = 0;

		// Token: 0x04005027 RID: 20519
		private int m_count = 0;

		// Token: 0x04005028 RID: 20520
		private int m_probability = 0;

		// Token: 0x04005029 RID: 20521
		private int m_delay = 0;

		// Token: 0x0400502A RID: 20522
		private int m_coldDown = 0;

		// Token: 0x0400502B RID: 20523
		private int m_currentId;

		// Token: 0x0400502C RID: 20524
		private int m_added = 0;
	}
}
