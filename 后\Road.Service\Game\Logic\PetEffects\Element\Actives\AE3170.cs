﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E33 RID: 3635
	public class AE3170 : BasePetEffect
	{
		// Token: 0x06007EC4 RID: 32452 RVA: 0x002A24F0 File Offset: 0x002A06F0
		public AE3170(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE3170, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007EC5 RID: 32453 RVA: 0x002A256C File Offset: 0x002A076C
		public override bool Start(Living living)
		{
			AE3170 ae = living.PetEffectList.GetOfType(ePetEffectType.AE3170) as AE3170;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007EC6 RID: 32454 RVA: 0x000312B8 File Offset: 0x0002F4B8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007EC7 RID: 32455 RVA: 0x000312E1 File Offset: 0x0002F4E1
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007EC8 RID: 32456 RVA: 0x002A25C8 File Offset: 0x002A07C8
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 120;
				player.CurrentDamagePlus += (float)(this.m_added / 100);
			}
		}

		// Token: 0x06007EC9 RID: 32457 RVA: 0x002A2610 File Offset: 0x002A0810
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_added / 100);
				this.m_added = 0;
			}
		}

		// Token: 0x04004DA0 RID: 19872
		private int m_type = 0;

		// Token: 0x04004DA1 RID: 19873
		private int m_count = 0;

		// Token: 0x04004DA2 RID: 19874
		private int m_probability = 0;

		// Token: 0x04004DA3 RID: 19875
		private int m_delay = 0;

		// Token: 0x04004DA4 RID: 19876
		private int m_coldDown = 0;

		// Token: 0x04004DA5 RID: 19877
		private int m_currentId;

		// Token: 0x04004DA6 RID: 19878
		private int m_added = 0;
	}
}
