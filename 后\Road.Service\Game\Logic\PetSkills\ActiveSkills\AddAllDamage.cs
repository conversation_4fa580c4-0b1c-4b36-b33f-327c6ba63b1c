﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D1D RID: 3357
	public class AddAllDamage : AbstractPetEffect
	{
		// Token: 0x060078F5 RID: 30965 RVA: 0x00288C60 File Offset: 0x00286E60
		public AddAllDamage(int skillId, string elementID)
			: base(ePetEffectType.AddAllDamage, elementID)
		{
			this.m_skillID = skillId;
			if (skillId <= 2359)
			{
				if (skillId != 2348)
				{
					if (skillId == 2359)
					{
						this.m_percent = 50;
					}
				}
				else
				{
					this.m_percent = 25;
				}
			}
			else if (skillId != 2364)
			{
				if (skillId == 10091)
				{
					this.m_percent = 12;
				}
			}
			else
			{
				this.m_percent = 75;
			}
		}

		// Token: 0x060078F6 RID: 30966 RVA: 0x00288CEC File Offset: 0x00286EEC
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.AddAllDamage) is AddAllDamage;
			return flag || base.Start(living);
		}

		// Token: 0x060078F7 RID: 30967 RVA: 0x0002D120 File Offset: 0x0002B320
		private void sendImgSkill(Living living)
		{
			living.Game.SendPetBuff(living, base.ElementInfo, true);
			living.BeginNextTurn -= this.sendImgSkill;
		}

		// Token: 0x060078F8 RID: 30968 RVA: 0x0002D14A File Offset: 0x0002B34A
		public override void OnAttached(Living living)
		{
			living.BeginNextTurn += this.sendImgSkill;
			living.OnMakeDamageEvent += this.player_OnMakeDamageEvent;
		}

		// Token: 0x060078F9 RID: 30969 RVA: 0x0002D173 File Offset: 0x0002B373
		private void player_OnMakeDamageEvent(Living living, Living target, ref int damageAmount, ref int criticalAmount)
		{
			damageAmount += this.m_percent * damageAmount / 100;
		}

		// Token: 0x060078FA RID: 30970 RVA: 0x0002D187 File Offset: 0x0002B387
		public override void OnRemoved(Living living)
		{
			living.OnMakeDamageEvent -= this.player_OnMakeDamageEvent;
		}

		// Token: 0x040046DE RID: 18142
		private int m_skillID;

		// Token: 0x040046DF RID: 18143
		private int m_added;

		// Token: 0x040046E0 RID: 18144
		private int m_percent = 0;

		// Token: 0x040046E1 RID: 18145
		private bool canadd = false;
	}
}
