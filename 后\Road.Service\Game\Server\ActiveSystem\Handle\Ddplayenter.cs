﻿using System;
using Game.Base.Packets;
using Game.Server.GameObjects;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C59 RID: 3161
	[ActiveSystemHandleAttbute(75)]
	public class Ddplayenter : IActiveSystemCommandHadler
	{
		// Token: 0x06007036 RID: 28726 RVA: 0x0024E300 File Offset: 0x0024C500
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			gspacketIn.WriteByte(75);
			gspacketIn.WriteInt(Player.PlayerCharacter.Info.DDPlayPoint);
			Player.SendTCP(gspacketIn);
			return true;
		}
	}
}
