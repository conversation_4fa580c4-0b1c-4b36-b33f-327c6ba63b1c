﻿using System;
using System.ComponentModel;
using System.Configuration;
using System.Data.SqlClient;
using System.Drawing;
using System.Net;
using System.Text;
using System.Windows.Forms;
using Game.Manager.Properties;
using SqlDataProvider.BaseClass;

namespace Game.Manager.AddData
{
	// Token: 0x02000C80 RID: 3200
	public partial class AddShopBox : Form
	{
		// Token: 0x17001331 RID: 4913
		// (get) Token: 0x06007134 RID: 28980 RVA: 0x000056B7 File Offset: 0x000038B7
		private static string Resource
		{
			get
			{
				return ConfigurationManager.AppSettings["Resource"];
			}
		}

		// Token: 0x06007135 RID: 28981 RVA: 0x0002A86D File Offset: 0x00028A6D
		public AddShopBox()
		{
			this.InitializeComponent();
		}

		// Token: 0x06007136 RID: 28982 RVA: 0x00258848 File Offset: 0x00256A48
		private void WriteLogs(string text, Color color)
		{
			this.LogBox.Select(this.LogBox.Text.Length, 0);
			this.LogBox.SelectionFont = new Font("微软雅黑", 8f, FontStyle.Regular);
			this.LogBox.SelectionColor = color;
			this.LogBox.SelectedText = text;
		}

		// Token: 0x06007137 RID: 28983 RVA: 0x002588AC File Offset: 0x00256AAC
		private void AddShop_Click(object sender, EventArgs e)
		{
			string text = (this.IsBind.Checked ? "1" : "0");
			string text2 = (this.IsCheap.Checked ? "1" : "0");
			bool flag = this.ShopID.Text != "" || this.TemplateID.Text != "";
			if (flag)
			{
				SqlDataReader sqlDataReader = null;
				string text3 = "SELECT MAX(ID)ID FROM Shop";
				this.db = new DataBaseHelper("conString");
				this.db.ExecuteReader(text3, ref sqlDataReader);
				bool flag2 = sqlDataReader.Read();
				if (flag2)
				{
					int num = (int)sqlDataReader["ID"] + 1;
					sqlDataReader.Close();
					text3 = string.Format("INSERT INTO Shop VALUES ({0},{1},0,{2},{3},0,{4},0,{5},{6},{7},{8},{9},-1,0,-1,0,{10},{11},{12},-1,0,-1,0,{13},{14},{15},-1,0,-1,0,{16},{17},'2000-01-01 00:00:00.000','2050-01-01 00:00:00.000')", new object[]
					{
						num,
						this.ShopID.Text,
						this.TemplateID.Text,
						this.BuyType.Text,
						text,
						this.Label.Text,
						this.Beat.Text,
						this.AUnit.Text,
						this.APrice1.Text,
						this.AValue1.Text,
						this.BUnit.Text,
						this.BPrice1.Text,
						this.BValue1.Text,
						this.CUnit.Text,
						this.CPrice1.Text,
						this.CValue1.Text,
						this.LimitCount.Text,
						text2
					});
					bool flag3 = this.db.ExecuteNonQuery(text3);
					if (flag3)
					{
						this.ID.Text = num.ToString();
						this.ShopShowID.Text = num.ToString();
						this.WriteLogs(string.Format("添加物品ID： -- {0} -- 到商品ID： --{1} 成功！\r\n", this.TemplateID.Text, num), Color.Red);
					}
				}
			}
			else
			{
				MessageBox.Show("请输入完整信息");
			}
		}

		// Token: 0x06007138 RID: 28984 RVA: 0x00258AF0 File Offset: 0x00256CF0
		private void FindItem_Btn_Click(object sender, EventArgs e)
		{
			this.TemplateID_CBox.Items.Clear();
			string text = string.Empty;
			SqlDataReader sqlDataReader = null;
			bool flag = this.ItemName_Text.Text != "";
			if (flag)
			{
				text = "SELECT TemplateID, Name FROM Shop_Goods WHERE Name LIKE '%" + this.ItemName_Text.Text + "%'";
				this.db = new DataBaseHelper("conString");
				this.db.ExecuteReader(text, ref sqlDataReader);
				while (sqlDataReader.Read())
				{
					this.TemplateID_CBox.Items.Add(sqlDataReader["TemplateID"].ToString() + " -- " + sqlDataReader["Name"].ToString());
				}
				this.TemplateID_CBox.SelectedIndex = 0;
				sqlDataReader.Close();
			}
			else
			{
				MessageBox.Show("请输入你需要查询的道具名称!");
			}
		}

		// Token: 0x06007139 RID: 28985 RVA: 0x0003A428 File Offset: 0x00038628
		private static string GetIDByChar(string CBoxText)
		{
			string[] array = CBoxText.Split(new string[] { " -- " }, StringSplitOptions.None);
			return array[0];
		}

		// Token: 0x0600713A RID: 28986 RVA: 0x0003A9B8 File Offset: 0x00038BB8
		public static bool isEncryed(string url)
		{
			WebClient webClient = new WebClient();
			string @string = Encoding.UTF8.GetString(webClient.DownloadData(url));
			return @string.Contains("^_^");
		}

		// Token: 0x0600713B RID: 28987 RVA: 0x00258BDC File Offset: 0x00256DDC
		public static string GetLink(string pic, int categoryId, int needSex)
		{
			string text = "";
			pic = pic.ToLower();
			string text2 = AddShopBox.Resource;
			string text3 = ((needSex == 1) ? "m" : "f");
			switch (categoryId)
			{
			case 1:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/head/", pic, "/icon_1.png" });
				goto IL_0479;
			case 2:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/glass/", pic, "/icon_1.png" });
				goto IL_0479;
			case 3:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/hair/", pic, "/icon_1.png" });
				goto IL_0479;
			case 4:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/eff/", pic, "/icon_1.png" });
				goto IL_0479;
			case 5:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/cloth/", pic, "/icon_1.png" });
				goto IL_0479;
			case 6:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/face/", pic, "/icon_1.png" });
				goto IL_0479;
			case 7:
			case 27:
			case 64:
				text = text + "image/arm/" + pic + "/1/icon.png";
				goto IL_0479;
			case 8:
			case 28:
				text = text + "image/equip/armlet/" + pic + "/icon.png";
				goto IL_0479;
			case 9:
			case 29:
				text = text + "image/equip/ring/" + pic + "/icon.png";
				goto IL_0479;
			case 11:
			case 20:
			case 24:
			case 30:
			case 34:
			case 35:
			case 36:
			case 37:
			case 40:
			case 53:
			case 60:
			case 61:
			case 62:
			case 68:
			case 69:
			case 71:
			case 72:
				text = text + "image/unfrightprop/" + pic + "/icon.png";
				goto IL_0479;
			case 12:
				text = text + "image/task/" + pic + "/icon.png";
				goto IL_0479;
			case 13:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/suits/", pic, "/icon_1.png" });
				goto IL_0479;
			case 14:
				text = text + "image/equip/necklace/" + pic + "/icon.png";
				goto IL_0479;
			case 15:
				text = text + "image/equip/wing/" + pic + "/icon.png";
				goto IL_0479;
			case 16:
				text = text + "image/specialprop/chatball/" + pic + "/icon.png";
				goto IL_0479;
			case 17:
			case 31:
				text = text + "image/equip/offhand/" + pic + "/icon.png";
				goto IL_0479;
			case 18:
			case 66:
				text = text + "image/cardbox/" + pic + "/icon.png";
				goto IL_0479;
			case 19:
				text = text + "image/equip/recover/" + pic + "/icon.png";
				goto IL_0479;
			case 25:
				text = text + "image/gift/" + pic + "/icon.png";
				goto IL_0479;
			case 26:
				text = text + "image/card/" + pic + "/icon.jpg";
				goto IL_0479;
			case 33:
				text = text + "image/farm/fertilizer/" + pic + "/icon.png";
				goto IL_0479;
			case 50:
				text = text + "image/petequip/arm/" + pic + "/icon.png";
				goto IL_0479;
			case 51:
				text = text + "image/petequip/hat/" + pic + "/icon.png";
				goto IL_0479;
			case 52:
				text = text + "image/petequip/cloth/" + pic + "/icon.png";
				goto IL_0479;
			case 80:
				text = text + "image/prop/" + pic + "/icon.png";
				goto IL_0479;
			}
			text2 = null;
			IL_0479:
			return text2 + text;
		}

		// Token: 0x0600713C RID: 28988 RVA: 0x0003A918 File Offset: 0x00038B18
		public static string GetWebStatusCode(string url, int timeout)
		{
			HttpWebRequest httpWebRequest = null;
			string text;
			try
			{
				httpWebRequest = (HttpWebRequest)WebRequest.CreateDefault(new Uri(url));
				httpWebRequest.Method = "HEAD";
				httpWebRequest.Timeout = timeout;
				HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				text = Convert.ToInt32(httpWebResponse.StatusCode).ToString();
			}
			catch (Exception ex)
			{
				text = ex.Message;
			}
			finally
			{
				bool flag = httpWebRequest != null;
				if (flag)
				{
					httpWebRequest.Abort();
					httpWebRequest = null;
				}
			}
			return text;
		}

		// Token: 0x0600713D RID: 28989 RVA: 0x00259070 File Offset: 0x00257270
		private void TemplateID_CBox_SelectedIndexChanged(object sender, EventArgs e)
		{
			SqlDataReader sqlDataReader = null;
			string idbyChar = AddShopBox.GetIDByChar(this.TemplateID_CBox.Text);
			this.TemplateID.Text = idbyChar;
			string text = "SELECT CateGoryID, Pic, NeedSex,MaxCount FROM Shop_Goods WHERE TemplateID = " + idbyChar;
			this.db = new DataBaseHelper("conString");
			this.db.ExecuteReader(text, ref sqlDataReader);
			bool flag = sqlDataReader.Read();
			if (flag)
			{
				string link = AddShopBox.GetLink(sqlDataReader["Pic"].ToString(), (int)sqlDataReader["CateGoryID"], (int)sqlDataReader["NeedSex"]);
				bool flag2 = !link.Equals("") && AddShopBox.GetWebStatusCode(link, 2000) != "远程服务器返回错误: (404) 未找到。";
				if (flag2)
				{
					bool flag3 = !AddShopBox.isEncryed(link);
					if (flag3)
					{
						this.pictureBox1.Load(link);
					}
				}
				else
				{
					this.pictureBox1.Image = Resources._1_asset_core_icon_2;
				}
			}
			else
			{
				this.pictureBox1.Image = Resources._1_asset_core_icon_2;
			}
			sqlDataReader.Close();
		}

		// Token: 0x0600713E RID: 28990 RVA: 0x00259190 File Offset: 0x00257390
		private void AddShopShow_Click(object sender, EventArgs e)
		{
			bool flag = this.ShopShowID.Text != "" || this.ShopShowType.Text != "";
			if (flag)
			{
				string text = string.Concat(new string[]
				{
					"INSERT INTO Shop_Goods_Show VALUES (",
					this.ShopShowType.Text,
					", ",
					this.ShopShowID.Text,
					")"
				});
				this.db = new DataBaseHelper("conString");
				bool flag2 = this.db.ExecuteNonQuery(text);
				if (flag2)
				{
					this.WriteLogs(string.Concat(new string[]
					{
						"添加商品ID： -- ",
						this.ShopShowID.Text,
						" -- 到类型ID： --",
						this.ShopShowType.Text,
						" 成功！\r\n"
					}), Color.Red);
				}
			}
			else
			{
				MessageBox.Show("请输入完整信息.");
			}
		}

		// Token: 0x04003D3A RID: 15674
		private DataBaseHelper db;
	}
}
