﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DFB RID: 3579
	public class AE1202 : BasePetEffect
	{
		// Token: 0x06007D98 RID: 32152 RVA: 0x0029CA78 File Offset: 0x0029AC78
		public AE1202(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1202, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D99 RID: 32153 RVA: 0x0029CAF8 File Offset: 0x0029ACF8
		public override bool Start(Living living)
		{
			AE1202 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1202) as AE1202;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D9A RID: 32154 RVA: 0x0003068B File Offset: 0x0002E88B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007D9B RID: 32155 RVA: 0x0029CB58 File Offset: 0x0029AD58
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007D9C RID: 32156 RVA: 0x0029CB88 File Offset: 0x0029AD88
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				this.IsTrigger = false;
				target.AddPetEffect(new CE1202(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007D9D RID: 32157 RVA: 0x000306B4 File Offset: 0x0002E8B4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004C15 RID: 19477
		private int m_type = 0;

		// Token: 0x04004C16 RID: 19478
		private int m_count = 0;

		// Token: 0x04004C17 RID: 19479
		private int m_probability = 0;

		// Token: 0x04004C18 RID: 19480
		private int m_delay = 0;

		// Token: 0x04004C19 RID: 19481
		private int m_coldDown = 0;

		// Token: 0x04004C1A RID: 19482
		private int m_currentId;

		// Token: 0x04004C1B RID: 19483
		private int m_added = 0;
	}
}
