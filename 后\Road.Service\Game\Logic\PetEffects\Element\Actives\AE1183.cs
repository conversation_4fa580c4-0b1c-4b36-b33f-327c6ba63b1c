﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DEE RID: 3566
	public class AE1183 : BasePetEffect
	{
		// Token: 0x06007D55 RID: 32085 RVA: 0x0029B820 File Offset: 0x00299A20
		public AE1183(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1183, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D56 RID: 32086 RVA: 0x0029B8A0 File Offset: 0x00299AA0
		public override bool Start(Living living)
		{
			AE1183 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1183) as AE1183;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D57 RID: 32087 RVA: 0x00030416 File Offset: 0x0002E616
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D58 RID: 32088 RVA: 0x0003042C File Offset: 0x0002E62C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D59 RID: 32089 RVA: 0x0029B900 File Offset: 0x00299B00
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					int num = 2;
					CE1181 ce = player2.PetEffectList.GetOfType(ePetEffectType.CE1181) as CE1181;
					CE1182 ce2 = player2.PetEffectList.GetOfType(ePetEffectType.CE1182) as CE1182;
					CE1183 ce3 = player2.PetEffectList.GetOfType(ePetEffectType.CE1183) as CE1183;
					bool flag2 = ce != null;
					if (flag2)
					{
						num = ce.Count;
						ce.Stop();
					}
					bool flag3 = ce2 != null;
					if (flag3)
					{
						num = ce2.Count;
						ce2.Stop();
					}
					bool flag4 = ce3 != null;
					if (flag4)
					{
						num = ce3.Count;
						ce3.Stop();
					}
					player2.AddPetEffect(new CE1183(num, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004BBA RID: 19386
		private int m_type = 0;

		// Token: 0x04004BBB RID: 19387
		private int m_count = 0;

		// Token: 0x04004BBC RID: 19388
		private int m_probability = 0;

		// Token: 0x04004BBD RID: 19389
		private int m_delay = 0;

		// Token: 0x04004BBE RID: 19390
		private int m_coldDown = 0;

		// Token: 0x04004BBF RID: 19391
		private int m_currentId;

		// Token: 0x04004BC0 RID: 19392
		private int m_added = 0;
	}
}
