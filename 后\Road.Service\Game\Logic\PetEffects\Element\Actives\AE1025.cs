﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D98 RID: 3480
	public class AE1025 : BasePetEffect
	{
		// Token: 0x06007B95 RID: 31637 RVA: 0x002939EC File Offset: 0x00291BEC
		public AE1025(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1025, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B96 RID: 31638 RVA: 0x00293A6C File Offset: 0x00291C6C
		public override bool Start(Living living)
		{
			AE1025 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1025) as AE1025;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B97 RID: 31639 RVA: 0x0002F325 File Offset: 0x0002D525
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.PlayerAnyShellThrow += this.Player_PlayerAnyShellThrow;
		}

		// Token: 0x06007B98 RID: 31640 RVA: 0x00293ACC File Offset: 0x00291CCC
		private void Player_PlayerAnyShellThrow(Player player)
		{
			bool flag = !this.IsTrigger;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					bool flag2 = player2.PlayerDetail != player.PlayerDetail;
					if (flag2)
					{
						player2.Game.SendPetBuff(player2, base.ElementInfo, true);
						player2.Game.SendPlayerPicture(player2, 29, true);
						player2.AddPetEffect(new CE1025(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
					}
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007B99 RID: 31641 RVA: 0x0002F34E File Offset: 0x0002D54E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.PlayerAnyShellThrow -= this.Player_PlayerAnyShellThrow;
		}

		// Token: 0x06007B9A RID: 31642 RVA: 0x00293BBC File Offset: 0x00291DBC
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x04004962 RID: 18786
		private int m_type = 0;

		// Token: 0x04004963 RID: 18787
		private int m_count = 0;

		// Token: 0x04004964 RID: 18788
		private int m_probability = 0;

		// Token: 0x04004965 RID: 18789
		private int m_delay = 0;

		// Token: 0x04004966 RID: 18790
		private int m_coldDown = 0;

		// Token: 0x04004967 RID: 18791
		private int m_currentId;

		// Token: 0x04004968 RID: 18792
		private int m_added = 0;
	}
}
