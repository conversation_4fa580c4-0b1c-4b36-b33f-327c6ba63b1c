﻿using System;
using System.ComponentModel;
using System.Configuration;
using System.Data.SqlClient;
using System.Drawing;
using System.Net;
using System.Text;
using System.Windows.Forms;
using Game.Manager.Properties;
using SqlDataProvider.BaseClass;

namespace Game.Manager.AddData
{
	// Token: 0x02000C7F RID: 3199
	public partial class AddDropBox : Form
	{
		// Token: 0x17001330 RID: 4912
		// (get) Token: 0x06007128 RID: 28968 RVA: 0x000056B7 File Offset: 0x000038B7
		private static string Resource
		{
			get
			{
				return ConfigurationManager.AppSettings["Resource"];
			}
		}

		// Token: 0x06007129 RID: 28969 RVA: 0x0002A855 File Offset: 0x00028A55
		public AddDropBox()
		{
			this.InitializeComponent();
		}

		// Token: 0x0600712A RID: 28970 RVA: 0x00256D88 File Offset: 0x00254F88
		private void FindItem_Btn_Click(object sender, EventArgs e)
		{
			this.TemplateID_CBox.Items.Clear();
			string text = string.Empty;
			SqlDataReader sqlDataReader = null;
			bool flag = this.ItemName_Text.Text != "";
			if (flag)
			{
				text = "SELECT TemplateID, Name FROM Shop_Goods WHERE Name LIKE '%" + this.ItemName_Text.Text + "%'";
				this.db = new DataBaseHelper("conString");
				this.db.ExecuteReader(text, ref sqlDataReader);
				while (sqlDataReader.Read())
				{
					this.TemplateID_CBox.Items.Add(sqlDataReader["TemplateID"].ToString() + " -- " + sqlDataReader["Name"].ToString());
				}
				this.TemplateID_CBox.SelectedIndex = 0;
				sqlDataReader.Close();
			}
			else
			{
				MessageBox.Show("请输入你需要查询的道具名称!");
			}
		}

		// Token: 0x0600712B RID: 28971 RVA: 0x0003A428 File Offset: 0x00038628
		private static string GetIDByChar(string CBoxText)
		{
			string[] array = CBoxText.Split(new string[] { " -- " }, StringSplitOptions.None);
			return array[0];
		}

		// Token: 0x0600712C RID: 28972 RVA: 0x0003A9B8 File Offset: 0x00038BB8
		public static bool isEncryed(string url)
		{
			WebClient webClient = new WebClient();
			string @string = Encoding.UTF8.GetString(webClient.DownloadData(url));
			return @string.Contains("^_^");
		}

		// Token: 0x0600712D RID: 28973 RVA: 0x00256E74 File Offset: 0x00255074
		public static string GetLink(string pic, int categoryId, int needSex)
		{
			string text = ConfigurationManager.AppSettings["Resource"];
			pic = pic.ToLower();
			string text2 = AddDropBox.Resource;
			string text3 = ((needSex == 1) ? "m" : "f");
			switch (categoryId)
			{
			case 1:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/head/", pic, "/icon_1.png" });
				goto IL_0483;
			case 2:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/glass/", pic, "/icon_1.png" });
				goto IL_0483;
			case 3:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/hair/", pic, "/icon_1.png" });
				goto IL_0483;
			case 4:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/eff/", pic, "/icon_1.png" });
				goto IL_0483;
			case 5:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/cloth/", pic, "/icon_1.png" });
				goto IL_0483;
			case 6:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/face/", pic, "/icon_1.png" });
				goto IL_0483;
			case 7:
			case 27:
			case 64:
				text = text + "image/arm/" + pic + "/1/icon.png";
				goto IL_0483;
			case 8:
			case 28:
				text = text + "image/equip/armlet/" + pic + "/icon.png";
				goto IL_0483;
			case 9:
			case 29:
				text = text + "image/equip/ring/" + pic + "/icon.png";
				goto IL_0483;
			case 11:
			case 20:
			case 24:
			case 30:
			case 34:
			case 35:
			case 36:
			case 37:
			case 40:
			case 53:
			case 60:
			case 61:
			case 62:
			case 68:
			case 69:
			case 71:
			case 72:
				text = text + "image/unfrightprop/" + pic + "/icon.png";
				goto IL_0483;
			case 12:
				text = text + "image/task/" + pic + "/icon.png";
				goto IL_0483;
			case 13:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/suits/", pic, "/icon_1.png" });
				goto IL_0483;
			case 14:
				text = text + "image/equip/necklace/" + pic + "/icon.png";
				goto IL_0483;
			case 15:
				text = text + "image/equip/wing/" + pic + "/icon.png";
				goto IL_0483;
			case 16:
				text = text + "image/specialprop/chatball/" + pic + "/icon.png";
				goto IL_0483;
			case 17:
			case 31:
				text = text + "image/equip/offhand/" + pic + "/icon.png";
				goto IL_0483;
			case 18:
			case 66:
				text = text + "image/cardbox/" + pic + "/icon.png";
				goto IL_0483;
			case 19:
				text = text + "image/equip/recover/" + pic + "/icon.png";
				goto IL_0483;
			case 25:
				text = text + "image/gift/" + pic + "/icon.png";
				goto IL_0483;
			case 26:
				text = text + "image/card/" + pic + "/icon.jpg";
				goto IL_0483;
			case 33:
				text = text + "image/farm/fertilizer/" + pic + "/icon.png";
				goto IL_0483;
			case 50:
				text = text + "image/petequip/arm/" + pic + "/icon.png";
				goto IL_0483;
			case 51:
				text = text + "image/petequip/hat/" + pic + "/icon.png";
				goto IL_0483;
			case 52:
				text = text + "image/petequip/cloth/" + pic + "/icon.png";
				goto IL_0483;
			case 80:
				text = text + "image/prop/" + pic + "/icon.png";
				goto IL_0483;
			}
			text2 = null;
			IL_0483:
			return text2 + text;
		}

		// Token: 0x0600712E RID: 28974 RVA: 0x0003A918 File Offset: 0x00038B18
		public static string GetWebStatusCode(string url, int timeout)
		{
			HttpWebRequest httpWebRequest = null;
			string text;
			try
			{
				httpWebRequest = (HttpWebRequest)WebRequest.CreateDefault(new Uri(url));
				httpWebRequest.Method = "HEAD";
				httpWebRequest.Timeout = timeout;
				HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				text = Convert.ToInt32(httpWebResponse.StatusCode).ToString();
			}
			catch (Exception ex)
			{
				text = ex.Message;
			}
			finally
			{
				bool flag = httpWebRequest != null;
				if (flag)
				{
					httpWebRequest.Abort();
					httpWebRequest = null;
				}
			}
			return text;
		}

		// Token: 0x0600712F RID: 28975 RVA: 0x00257314 File Offset: 0x00255514
		private void TemplateID_CBox_SelectedIndexChanged(object sender, EventArgs e)
		{
			SqlDataReader sqlDataReader = null;
			string idbyChar = AddDropBox.GetIDByChar(this.TemplateID_CBox.Text);
			this.ItemId.Text = idbyChar;
			string text = "SELECT CateGoryID, Pic, NeedSex,MaxCount FROM Shop_Goods WHERE TemplateID = " + idbyChar;
			this.db = new DataBaseHelper("conString");
			this.db.ExecuteReader(text, ref sqlDataReader);
			bool flag = sqlDataReader.Read();
			if (flag)
			{
				string link = AddDropBox.GetLink(sqlDataReader["Pic"].ToString(), (int)sqlDataReader["CateGoryID"], (int)sqlDataReader["NeedSex"]);
				bool flag2 = !link.Equals("") && AddDropBox.GetWebStatusCode(link, 2000) != "远程服务器返回错误: (404) 未找到。";
				if (flag2)
				{
					bool flag3 = !AddDropBox.isEncryed(link);
					if (flag3)
					{
						this.pictureBox1.Load(link);
					}
				}
				else
				{
					this.pictureBox1.Image = Resources._1_asset_core_icon_2;
				}
			}
			else
			{
				this.pictureBox1.Image = Resources._1_asset_core_icon_2;
			}
			sqlDataReader.Close();
		}

		// Token: 0x06007130 RID: 28976 RVA: 0x00257434 File Offset: 0x00255634
		private void WriteLogs(string text, Color color)
		{
			this.LogBox.Select(this.LogBox.Text.Length, 0);
			this.LogBox.SelectionFont = new Font("微软雅黑", 8f, FontStyle.Regular);
			this.LogBox.SelectionColor = color;
			this.LogBox.SelectedText = text;
		}

		// Token: 0x06007131 RID: 28977 RVA: 0x00257498 File Offset: 0x00255698
		private void AddDrop_Click(object sender, EventArgs e)
		{
			string text = (this.IsBind.Checked ? "1" : "0");
			string text2 = (this.IsTips.Checked ? "1" : "0");
			string text3 = (this.IsLogs.Checked ? "1" : "0");
			string text4 = string.Concat(new string[]
			{
				"INSERT INTO Drop_Item VALUES (",
				this.DropId.Text,
				", ",
				this.ItemId.Text,
				", ",
				this.ValueDate.Text,
				", ",
				text,
				", ",
				this.Random.Text,
				", ",
				this.BeginData.Text,
				", ",
				this.EndData.Text,
				", ",
				text2,
				", ",
				text3,
				")"
			});
			this.db = new DataBaseHelper("conString");
			bool flag = this.db.ExecuteNonQuery(text4);
			if (flag)
			{
				this.WriteLogs(string.Concat(new string[]
				{
					"添加物品ID： -- ",
					this.DropId.Text,
					" -- 到掉落ID： --",
					this.ItemId.Text,
					" 成功！\r\n"
				}), Color.Red);
			}
		}

		// Token: 0x04003D19 RID: 15641
		private DataBaseHelper db;
	}
}
