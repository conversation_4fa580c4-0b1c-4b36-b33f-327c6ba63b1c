﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using SqlDataProvider.Data;

namespace Bussiness
{
	// Token: 0x02000FAF RID: 4015
	public class ItemRecordBussiness : BaseBussiness
	{
		// Token: 0x0600889B RID: 34971 RVA: 0x002D1FB4 File Offset: 0x002D01B4
		public void PropertyString(ItemInfo item, ref string Property)
		{
			bool flag = item != null;
			if (flag)
			{
				Property = string.Format("{0},{1},{2},{3},{4},{5},{6},{7},{8}", new object[] { item.StrengthenLevel, item.Attack, item.Defence, item.Agility, item.Luck, item.AttackCompose, item.DefendCompose, item.AgilityCompose, item.LuckCompose });
			}
		}

		// Token: 0x0600889C RID: 34972 RVA: 0x002D2060 File Offset: 0x002D0260
		public void FusionItem(ItemInfo item, ref string Property)
		{
			bool flag = item != null;
			if (flag)
			{
				Property = Property + string.Format("{0}:{1},{2}", item.ItemID, item.Template.Name, Convert.ToInt32(item.IsBinds)) + "|";
			}
		}

		// Token: 0x0600889D RID: 34973 RVA: 0x002D20B8 File Offset: 0x002D02B8
		public bool LogItemDb(DataTable dt)
		{
			bool flag = false;
			bool flag2 = dt == null;
			bool flag3;
			if (flag2)
			{
				flag3 = flag;
			}
			else
			{
				SqlBulkCopy sqlBulkCopy = new SqlBulkCopy(ConfigurationManager.AppSettings["countDb"], SqlBulkCopyOptions.UseInternalTransaction);
				try
				{
					sqlBulkCopy.NotifyAfter = dt.Rows.Count;
					sqlBulkCopy.DestinationTableName = "Log_Item";
					sqlBulkCopy.ColumnMappings.Add(0, "ApplicationId");
					sqlBulkCopy.ColumnMappings.Add(1, "SubId");
					sqlBulkCopy.ColumnMappings.Add(2, "LineId");
					sqlBulkCopy.ColumnMappings.Add(3, "EnterTime");
					sqlBulkCopy.ColumnMappings.Add(4, "UserId");
					sqlBulkCopy.ColumnMappings.Add(5, "Operation");
					sqlBulkCopy.ColumnMappings.Add(6, "ItemName");
					sqlBulkCopy.ColumnMappings.Add(7, "ItemID");
					sqlBulkCopy.ColumnMappings.Add(8, "AddItem");
					sqlBulkCopy.ColumnMappings.Add(9, "BeginProperty");
					sqlBulkCopy.ColumnMappings.Add(10, "EndProperty");
					sqlBulkCopy.ColumnMappings.Add(11, "Result");
					sqlBulkCopy.WriteToServer(dt);
					flag = true;
					dt.Clear();
				}
				catch (Exception ex)
				{
					bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						BaseBussiness.log.Error("Smith Log Error:" + ex.ToString());
					}
				}
				finally
				{
					sqlBulkCopy.Close();
				}
				flag3 = flag;
			}
			return flag3;
		}

		// Token: 0x0600889E RID: 34974 RVA: 0x002D2278 File Offset: 0x002D0478
		public bool LogMoneyDb(DataTable dt)
		{
			bool flag = false;
			bool flag2 = dt == null;
			bool flag3;
			if (flag2)
			{
				flag3 = flag;
			}
			else
			{
				SqlBulkCopy sqlBulkCopy = new SqlBulkCopy(ConfigurationManager.AppSettings["countDb"], SqlBulkCopyOptions.UseInternalTransaction);
				try
				{
					sqlBulkCopy.NotifyAfter = dt.Rows.Count;
					sqlBulkCopy.DestinationTableName = "Log_Money";
					sqlBulkCopy.ColumnMappings.Add(0, "ApplicationId");
					sqlBulkCopy.ColumnMappings.Add(1, "SubId");
					sqlBulkCopy.ColumnMappings.Add(2, "LineId");
					sqlBulkCopy.ColumnMappings.Add(3, "MastType");
					sqlBulkCopy.ColumnMappings.Add(4, "SonType");
					sqlBulkCopy.ColumnMappings.Add(5, "UserId");
					sqlBulkCopy.ColumnMappings.Add(6, "EnterTime");
					sqlBulkCopy.ColumnMappings.Add(7, "Moneys");
					sqlBulkCopy.ColumnMappings.Add(8, "SpareMoney");
					sqlBulkCopy.ColumnMappings.Add(9, "Gold");
					sqlBulkCopy.ColumnMappings.Add(10, "GiftToken");
					sqlBulkCopy.ColumnMappings.Add(11, "Offer");
					sqlBulkCopy.ColumnMappings.Add(12, "OtherPay");
					sqlBulkCopy.ColumnMappings.Add(13, "GoodId");
					sqlBulkCopy.ColumnMappings.Add(14, "GoodsType");
					sqlBulkCopy.WriteToServer(dt);
					flag = true;
				}
				catch (Exception ex)
				{
					bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						BaseBussiness.log.Error("Money Log Error:" + ex.ToString());
					}
				}
				finally
				{
					sqlBulkCopy.Close();
					dt.Clear();
				}
				flag3 = flag;
			}
			return flag3;
		}

		// Token: 0x0600889F RID: 34975 RVA: 0x002D2470 File Offset: 0x002D0670
		public bool LogFightDb(DataTable dt)
		{
			bool flag = false;
			bool flag2 = dt == null;
			bool flag3;
			if (flag2)
			{
				flag3 = flag;
			}
			else
			{
				SqlBulkCopy sqlBulkCopy = new SqlBulkCopy(ConfigurationManager.AppSettings["countDb"], SqlBulkCopyOptions.UseInternalTransaction);
				try
				{
					sqlBulkCopy.NotifyAfter = dt.Rows.Count;
					sqlBulkCopy.DestinationTableName = "Log_Fight";
					sqlBulkCopy.ColumnMappings.Add(0, "ApplicationId");
					sqlBulkCopy.ColumnMappings.Add(1, "SubId");
					sqlBulkCopy.ColumnMappings.Add(2, "LineId");
					sqlBulkCopy.ColumnMappings.Add(3, "RoomId");
					sqlBulkCopy.ColumnMappings.Add(4, "RoomType");
					sqlBulkCopy.ColumnMappings.Add(5, "FightType");
					sqlBulkCopy.ColumnMappings.Add(6, "ChangeTeam");
					sqlBulkCopy.ColumnMappings.Add(7, "PlayBegin");
					sqlBulkCopy.ColumnMappings.Add(8, "PlayEnd");
					sqlBulkCopy.ColumnMappings.Add(9, "UserCount");
					sqlBulkCopy.ColumnMappings.Add(10, "MapId");
					sqlBulkCopy.ColumnMappings.Add(11, "TeamA");
					sqlBulkCopy.ColumnMappings.Add(12, "TeamB");
					sqlBulkCopy.ColumnMappings.Add(13, "PlayResult");
					sqlBulkCopy.ColumnMappings.Add(14, "WinTeam");
					sqlBulkCopy.ColumnMappings.Add(15, "Detail");
					sqlBulkCopy.WriteToServer(dt);
					flag = true;
				}
				catch (Exception ex)
				{
					bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						BaseBussiness.log.Error("Fight Log Error:" + ex.ToString());
					}
				}
				finally
				{
					sqlBulkCopy.Close();
					dt.Clear();
				}
				flag3 = flag;
			}
			return flag3;
		}

		// Token: 0x060088A0 RID: 34976 RVA: 0x002D267C File Offset: 0x002D087C
		public bool LogServerDb(DataTable dt)
		{
			bool flag = false;
			bool flag2 = dt == null;
			bool flag3;
			if (flag2)
			{
				flag3 = flag;
			}
			else
			{
				SqlBulkCopy sqlBulkCopy = new SqlBulkCopy(ConfigurationManager.AppSettings["countDb"], SqlBulkCopyOptions.UseInternalTransaction);
				try
				{
					sqlBulkCopy.NotifyAfter = dt.Rows.Count;
					sqlBulkCopy.DestinationTableName = "Log_Server";
					sqlBulkCopy.ColumnMappings.Add(0, "ApplicationId");
					sqlBulkCopy.ColumnMappings.Add(1, "SubId");
					sqlBulkCopy.ColumnMappings.Add(2, "EnterTime");
					sqlBulkCopy.ColumnMappings.Add(3, "Online");
					sqlBulkCopy.ColumnMappings.Add(4, "Reg");
					sqlBulkCopy.WriteToServer(dt);
					flag = true;
				}
				catch (Exception ex)
				{
					bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						BaseBussiness.log.Error("Server Log Error:" + ex.ToString());
					}
				}
				finally
				{
					sqlBulkCopy.Close();
					dt.Clear();
				}
				flag3 = flag;
			}
			return flag3;
		}
	}
}
