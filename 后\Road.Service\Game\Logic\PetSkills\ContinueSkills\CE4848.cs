﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000CFD RID: 3325
	public class CE4848 : BasePetEffect
	{
		// Token: 0x0600784C RID: 30796 RVA: 0x0002C8F2 File Offset: 0x0002AAF2
		public CE4848(int count, string elementID)
			: base(ePetEffectType.CE4848, elementID)
		{
			this.m_count = count;
		}

		// Token: 0x0600784D RID: 30797 RVA: 0x0028511C File Offset: 0x0028331C
		public override bool Start(Living living)
		{
			CE4848 ce = living.PetEffectList.GetOfType(ePetEffectType.CE4848) as CE4848;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600784E RID: 30798 RVA: 0x0002C910 File Offset: 0x0002AB10
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_beginSeftTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600784F RID: 30799 RVA: 0x0002C939 File Offset: 0x0002AB39
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_beginSeftTurn;
		}

		// Token: 0x06007850 RID: 30800 RVA: 0x00285164 File Offset: 0x00283364
		public void player_beginSeftTurn(Living player)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				player.Game.sendShowPicSkil(player, base.ElementInfo, true);
				this.Stop();
			}
			else
			{
				this.m_living.PetEffects.DisibleActiveSkill = true;
			}
		}

		// Token: 0x06007851 RID: 30801 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x04004684 RID: 18052
		private int m_count = 0;
	}
}
