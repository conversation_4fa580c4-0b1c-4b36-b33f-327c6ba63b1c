﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E7B RID: 3707
	public class CE1162 : BasePetEffect
	{
		// Token: 0x06008073 RID: 32883 RVA: 0x002A923C File Offset: 0x002A743C
		public CE1162(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1162, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008074 RID: 32884 RVA: 0x002A92BC File Offset: 0x002A74BC
		public override bool Start(Living living)
		{
			CE1162 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1162) as CE1162;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008075 RID: 32885 RVA: 0x00032235 File Offset: 0x00030435
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008076 RID: 32886 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008077 RID: 32887 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_BeginSelfTurn(Living living)
		{
			this.Stop();
		}

		// Token: 0x06008078 RID: 32888 RVA: 0x0003225E File Offset: 0x0003045E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F96 RID: 20374
		private int m_type = 0;

		// Token: 0x04004F97 RID: 20375
		private int m_count = 0;

		// Token: 0x04004F98 RID: 20376
		private int m_probability = 0;

		// Token: 0x04004F99 RID: 20377
		private int m_delay = 0;

		// Token: 0x04004F9A RID: 20378
		private int m_coldDown = 0;

		// Token: 0x04004F9B RID: 20379
		private int m_currentId;

		// Token: 0x04004F9C RID: 20380
		private int m_added = 0;
	}
}
