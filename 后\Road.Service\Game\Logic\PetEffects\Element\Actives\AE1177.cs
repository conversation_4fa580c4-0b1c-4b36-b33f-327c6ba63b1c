﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DE8 RID: 3560
	public class AE1177 : BasePetEffect
	{
		// Token: 0x06007D37 RID: 32055 RVA: 0x0029ADE8 File Offset: 0x00298FE8
		public AE1177(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1177, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D38 RID: 32056 RVA: 0x0029AE68 File Offset: 0x00299068
		public override bool Start(Living living)
		{
			AE1177 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1177) as AE1177;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D39 RID: 32057 RVA: 0x0003030E File Offset: 0x0002E50E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007D3A RID: 32058 RVA: 0x0029AEC8 File Offset: 0x002990C8
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddPetEffect(new CE1177(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString(), player), 0);
				}
			}
		}

		// Token: 0x06007D3B RID: 32059 RVA: 0x00030324 File Offset: 0x0002E524
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004B90 RID: 19344
		private int m_type = 0;

		// Token: 0x04004B91 RID: 19345
		private int m_count = 0;

		// Token: 0x04004B92 RID: 19346
		private int m_probability = 0;

		// Token: 0x04004B93 RID: 19347
		private int m_delay = 0;

		// Token: 0x04004B94 RID: 19348
		private int m_coldDown = 0;

		// Token: 0x04004B95 RID: 19349
		private int m_currentId;

		// Token: 0x04004B96 RID: 19350
		private int m_added = 0;
	}
}
