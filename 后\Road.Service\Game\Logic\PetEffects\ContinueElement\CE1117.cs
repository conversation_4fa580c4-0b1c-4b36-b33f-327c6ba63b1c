﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E70 RID: 3696
	public class CE1117 : BasePetEffect
	{
		// Token: 0x0600802E RID: 32814 RVA: 0x002A8230 File Offset: 0x002A6430
		public CE1117(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1117, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600802F RID: 32815 RVA: 0x002A82B0 File Offset: 0x002A64B0
		public override bool Start(Living living)
		{
			CE1117 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1117) as CE1117;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008030 RID: 32816 RVA: 0x00031EE5 File Offset: 0x000300E5
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008031 RID: 32817 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008032 RID: 32818 RVA: 0x00031F21 File Offset: 0x00030121
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			damageAmount = 0;
			criticalAmount = 0;
		}

		// Token: 0x06008033 RID: 32819 RVA: 0x002A8310 File Offset: 0x002A6510
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008034 RID: 32820 RVA: 0x00031F2B File Offset: 0x0003012B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
		}

		// Token: 0x04004F4B RID: 20299
		private int m_type = 0;

		// Token: 0x04004F4C RID: 20300
		private int m_count = 0;

		// Token: 0x04004F4D RID: 20301
		private int m_probability = 0;

		// Token: 0x04004F4E RID: 20302
		private int m_delay = 0;

		// Token: 0x04004F4F RID: 20303
		private int m_coldDown = 0;

		// Token: 0x04004F50 RID: 20304
		private int m_currentId;

		// Token: 0x04004F51 RID: 20305
		private int m_added = 0;
	}
}
