﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D4B RID: 3403
	public class PetSpikeEnemy : BasePetEffect
	{
		// Token: 0x060079EF RID: 31215 RVA: 0x0028CF84 File Offset: 0x0028B184
		public PetSpikeEnemy(int probability, int skillId, string elementID)
			: base(ePetEffectType.PetSpikeEnemy, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
			bool flag = elementID == "3486";
			if (flag)
			{
				this.m_value = 10;
			}
		}

		// Token: 0x060079F0 RID: 31216 RVA: 0x0028CFE0 File Offset: 0x0028B1E0
		public override bool Start(Living living)
		{
			PetSpikeEnemy petSpikeEnemy = living.PetEffectList.GetOfType(ePetEffectType.PetSpikeEnemy) as PetSpikeEnemy;
			bool flag = petSpikeEnemy != null;
			bool flag2;
			if (flag)
			{
				petSpikeEnemy.m_probability = ((this.m_probability > petSpikeEnemy.m_probability) ? this.m_probability : petSpikeEnemy.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079F1 RID: 31217 RVA: 0x0002E1A2 File Offset: 0x0002C3A2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
		}

		// Token: 0x060079F2 RID: 31218 RVA: 0x0002E1B8 File Offset: 0x0002C3B8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
		}

		// Token: 0x060079F3 RID: 31219 RVA: 0x0028D040 File Offset: 0x0028B240
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					this.m_value = player2.MaxBlood * 5 / 100;
					bool flag2 = player2.Blood < this.m_value;
					if (flag2)
					{
						player2.Die();
						if (player != null)
						{
							player.OnAfterKillingLiving(player2, this.m_value, 0);
						}
					}
				}
			}
		}

		// Token: 0x0400476F RID: 18287
		private int m_probability = 0;

		// Token: 0x04004770 RID: 18288
		private int m_currentId;

		// Token: 0x04004771 RID: 18289
		private int m_value = 0;
	}
}
