﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D6B RID: 3435
	public class PE1166 : BasePetEffect
	{
		// Token: 0x06007AAE RID: 31406 RVA: 0x0028FDD8 File Offset: 0x0028DFD8
		public PE1166(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1166, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AAF RID: 31407 RVA: 0x0028FE58 File Offset: 0x0028E058
		public override bool Start(Living living)
		{
			PE1166 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1166) as PE1166;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AB0 RID: 31408 RVA: 0x0002E99C File Offset: 0x0002CB9C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007AB1 RID: 31409 RVA: 0x0028FEB8 File Offset: 0x0028E0B8
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 200;
				living.BaseDamage += (double)this.m_added;
			}
		}

		// Token: 0x06007AB2 RID: 31410 RVA: 0x0002E9B2 File Offset: 0x0002CBB2
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x0400482D RID: 18477
		private int m_type = 0;

		// Token: 0x0400482E RID: 18478
		private int m_count = 0;

		// Token: 0x0400482F RID: 18479
		private int m_probability = 0;

		// Token: 0x04004830 RID: 18480
		private int m_delay = 0;

		// Token: 0x04004831 RID: 18481
		private int m_coldDown = 0;

		// Token: 0x04004832 RID: 18482
		private int m_currentId;

		// Token: 0x04004833 RID: 18483
		private int m_added = 0;
	}
}
