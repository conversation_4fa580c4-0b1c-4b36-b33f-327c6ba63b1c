﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DA3 RID: 3491
	public class AE1041 : BasePetEffect
	{
		// Token: 0x06007BCF RID: 31695 RVA: 0x00294970 File Offset: 0x00292B70
		public AE1041(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1041, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BD0 RID: 31696 RVA: 0x002949F0 File Offset: 0x00292BF0
		public override bool Start(Living living)
		{
			AE1041 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1041) as AE1041;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BD1 RID: 31697 RVA: 0x0002F57B File Offset: 0x0002D77B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BD2 RID: 31698 RVA: 0x0002F591 File Offset: 0x0002D791
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BD3 RID: 31699 RVA: 0x00294A50 File Offset: 0x00292C50
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1041(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x040049AF RID: 18863
		private int m_type = 0;

		// Token: 0x040049B0 RID: 18864
		private int m_count = 0;

		// Token: 0x040049B1 RID: 18865
		private int m_probability = 0;

		// Token: 0x040049B2 RID: 18866
		private int m_delay = 0;

		// Token: 0x040049B3 RID: 18867
		private int m_coldDown = 0;

		// Token: 0x040049B4 RID: 18868
		private int m_currentId;

		// Token: 0x040049B5 RID: 18869
		private int m_added = 0;
	}
}
