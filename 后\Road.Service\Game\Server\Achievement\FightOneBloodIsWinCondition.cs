﻿using System;
using Game.Logic;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C66 RID: 3174
	public class FightOneBloodIsWinCondition : BaseCondition
	{
		// Token: 0x0600708B RID: 28811 RVA: 0x0002A421 File Offset: 0x00028621
		public FightOneBloodIsWinCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x0600708C RID: 28812 RVA: 0x0002A51C File Offset: 0x0002871C
		public override void AddTrigger(GamePlayer player)
		{
			player.FightOneBloodIsWin += this.player_PlayerFightOneBloodIsWin;
		}

		// Token: 0x0600708D RID: 28813 RVA: 0x00250134 File Offset: 0x0024E334
		private void player_PlayerFightOneBloodIsWin(eRoomType roomType, bool isWin)
		{
			bool flag = roomType == eRoomType.Match;
			if (flag)
			{
				int value = base.Value;
				base.Value = value + 1;
			}
		}

		// Token: 0x0600708E RID: 28814 RVA: 0x0002A532 File Offset: 0x00028732
		public override void RemoveTrigger(GamePlayer player)
		{
			player.FightOneBloodIsWin -= this.player_PlayerFightOneBloodIsWin;
		}

		// Token: 0x0600708F RID: 28815 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
