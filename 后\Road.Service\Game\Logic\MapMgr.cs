﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using Bussiness;
using Game.Logic.Phy.Maps;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000CA3 RID: 3235
	public class MapMgr
	{
		// Token: 0x170013D8 RID: 5080
		// (get) Token: 0x060073BC RID: 29628 RVA: 0x002639AC File Offset: 0x00261BAC
		public static int GetWeekDay
		{
			get
			{
				int num = Convert.ToInt32(DateTime.Now.DayOfWeek);
				return (num == 0) ? 7 : num;
			}
		}

		// Token: 0x060073BD RID: 29629 RVA: 0x002639E0 File Offset: 0x00261BE0
		public static bool ReLoadMap()
		{
			try
			{
				Dictionary<int, MapPoint> dictionary = new Dictionary<int, MapPoint>();
				Dictionary<int, Map> dictionary2 = new Dictionary<int, Map>();
				bool flag = MapMgr.LoadMap(dictionary, dictionary2);
				if (flag)
				{
					MapMgr.m_lock.AcquireWriterLock(-1);
					try
					{
						MapMgr._maps = dictionary;
						MapMgr._mapInfos = dictionary2;
						return true;
					}
					catch
					{
					}
					finally
					{
						MapMgr.m_lock.ReleaseWriterLock();
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = MapMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					MapMgr.log.Error("ReLoadMap", ex);
				}
			}
			return false;
		}

		// Token: 0x060073BE RID: 29630 RVA: 0x00263A94 File Offset: 0x00261C94
		public static bool ReLoadMapServer()
		{
			try
			{
				Dictionary<int, List<int>> dictionary = new Dictionary<int, List<int>>();
				bool flag = MapMgr.InitServerMap(dictionary);
				if (flag)
				{
					MapMgr.m_lock.AcquireWriterLock(-1);
					try
					{
						MapMgr._serverMap = dictionary;
						return true;
					}
					catch
					{
					}
					finally
					{
						MapMgr.m_lock.ReleaseWriterLock();
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = MapMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					MapMgr.log.Error("ReLoadMapWeek", ex);
				}
			}
			return false;
		}

		// Token: 0x060073BF RID: 29631 RVA: 0x00263B3C File Offset: 0x00261D3C
		public static bool Init()
		{
			try
			{
				MapMgr.random = new ThreadSafeRandom();
				MapMgr.m_lock = new ReaderWriterLock();
				MapMgr._maps = new Dictionary<int, MapPoint>();
				MapMgr._mapInfos = new Dictionary<int, Map>();
				bool flag = !MapMgr.LoadMap(MapMgr._maps, MapMgr._mapInfos);
				if (flag)
				{
					return false;
				}
				MapMgr._serverMap = new Dictionary<int, List<int>>();
				bool flag2 = !MapMgr.InitServerMap(MapMgr._serverMap);
				if (flag2)
				{
					return false;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = MapMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					MapMgr.log.Error("MapMgr", ex);
				}
				return false;
			}
			return true;
		}

		// Token: 0x060073C0 RID: 29632 RVA: 0x00263BF0 File Offset: 0x00261DF0
		public static bool LoadMap(Dictionary<int, MapPoint> maps, Dictionary<int, Map> mapInfos)
		{
			try
			{
				MapBussiness mapBussiness = new MapBussiness();
				MapInfo[] allMap = mapBussiness.GetAllMap();
				MapInfo[] array = allMap;
				MapInfo[] array2 = array;
				MapInfo[] array3 = array2;
				foreach (MapInfo mapInfo in array3)
				{
					bool flag = string.IsNullOrEmpty(mapInfo.PosX);
					if (!flag)
					{
						bool flag2 = !maps.Keys.Contains(mapInfo.ID);
						if (flag2)
						{
							string[] array5 = mapInfo.PosX.Split(new char[] { '|' });
							string[] array6 = mapInfo.PosX1.Split(new char[] { '|' });
							MapPoint mapPoint = new MapPoint();
							string[] array7 = array5;
							string[] array8 = array7;
							string[] array9 = array8;
							foreach (string text in array9)
							{
								bool flag3 = !string.IsNullOrEmpty(text.Trim());
								if (flag3)
								{
									string[] array11 = text.Split(new char[] { ',' });
									mapPoint.PosX.Add(new Point(int.Parse(array11[0]), int.Parse(array11[1])));
								}
							}
							string[] array12 = array6;
							string[] array13 = array12;
							string[] array14 = array13;
							foreach (string text2 in array14)
							{
								bool flag4 = !string.IsNullOrEmpty(text2.Trim());
								if (flag4)
								{
									string[] array16 = text2.Split(new char[] { ',' });
									mapPoint.PosX1.Add(new Point(int.Parse(array16[0]), int.Parse(array16[1])));
								}
							}
							maps.Add(mapInfo.ID, mapPoint);
						}
						bool flag5 = !mapInfos.ContainsKey(mapInfo.ID);
						if (flag5)
						{
							Tile tile = null;
							string text3 = string.Format("map\\{0}\\fore.map", mapInfo.ID);
							bool flag6 = File.Exists(text3);
							if (flag6)
							{
								tile = new Tile(text3, true);
							}
							Tile tile2 = null;
							text3 = string.Format("map\\{0}\\dead.map", mapInfo.ID);
							bool flag7 = File.Exists(text3);
							if (flag7)
							{
								tile2 = new Tile(text3, false);
							}
							bool flag8 = tile != null || tile2 != null;
							if (flag8)
							{
								mapInfos.Add(mapInfo.ID, new Map(mapInfo, tile, tile2));
							}
							else
							{
								bool isErrorEnabled = MapMgr.log.IsErrorEnabled;
								if (isErrorEnabled)
								{
									MapMgr.log.Error("Map's file" + mapInfo.ID.ToString() + " is not exist!");
								}
							}
						}
					}
				}
				bool flag9 = maps.Count == 0 || mapInfos.Count == 0;
				if (flag9)
				{
					bool isErrorEnabled2 = MapMgr.log.IsErrorEnabled;
					if (isErrorEnabled2)
					{
						MapMgr.log.Error("maps:" + maps.Count.ToString() + ",mapInfos:" + mapInfos.Count.ToString());
					}
					return false;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled3 = MapMgr.log.IsErrorEnabled;
				if (isErrorEnabled3)
				{
					MapMgr.log.Error("MapMgr", ex);
				}
				return false;
			}
			return true;
		}

		// Token: 0x060073C1 RID: 29633 RVA: 0x00263F60 File Offset: 0x00262160
		public static Map CloneMap(int index)
		{
			bool flag = MapMgr._mapInfos.ContainsKey(index);
			Map map;
			if (flag)
			{
				map = MapMgr._mapInfos[index].Clone();
			}
			else
			{
				map = null;
			}
			return map;
		}

		// Token: 0x060073C2 RID: 29634 RVA: 0x00263F98 File Offset: 0x00262198
		public static MapInfo FindMapInfo(int index)
		{
			bool flag = MapMgr._mapInfos.ContainsKey(index);
			MapInfo mapInfo;
			if (flag)
			{
				mapInfo = MapMgr._mapInfos[index].Info;
			}
			else
			{
				mapInfo = null;
			}
			return mapInfo;
		}

		// Token: 0x060073C3 RID: 29635 RVA: 0x00263FD0 File Offset: 0x002621D0
		public static int GetMapIndex(int index, byte type, int serverId)
		{
			bool flag = index != 0 && !MapMgr._maps.Keys.Contains(index);
			if (flag)
			{
				index = 0;
			}
			bool flag2 = index == 0;
			int num2;
			if (flag2)
			{
				bool flag3 = serverId > MapMgr._serverMap.Count;
				if (flag3)
				{
					serverId = MapMgr._serverMap.Count - 1;
				}
				List<int> list = new List<int>();
				foreach (int num in MapMgr._serverMap[serverId])
				{
					MapInfo mapInfo = MapMgr.FindMapInfo(num);
					bool flag4 = mapInfo == null && MapMgr.log.IsErrorEnabled;
					if (flag4)
					{
						MapMgr.log.Error("MapId " + num.ToString() + " is not exist!");
					}
					else
					{
						bool flag5 = (type & mapInfo.Type) > 0;
						if (flag5)
						{
							list.Add(num);
						}
					}
				}
				bool flag6 = list.Count == 0;
				if (flag6)
				{
					int count = MapMgr._serverMap[serverId].Count;
					num2 = MapMgr._serverMap[serverId][MapMgr.random.Next(count)];
				}
				else
				{
					int count2 = list.Count;
					num2 = list[MapMgr.random.Next(count2)];
				}
			}
			else
			{
				num2 = index;
			}
			return num2;
		}

		// Token: 0x060073C4 RID: 29636 RVA: 0x00264148 File Offset: 0x00262348
		public static MapPoint GetMapRandomPos(int index)
		{
			MapPoint mapPoint = new MapPoint();
			bool flag = index != 0 && !MapMgr._maps.Keys.Contains(index);
			if (flag)
			{
				index = 0;
			}
			bool flag2 = index == 0;
			MapPoint mapPoint2;
			if (flag2)
			{
				int[] array = MapMgr._maps.Keys.ToArray<int>();
				mapPoint2 = MapMgr._maps[array[MapMgr.random.Next(array.Length)]];
			}
			else
			{
				mapPoint2 = MapMgr._maps[index];
			}
			bool flag3 = MapMgr.random.Next(2) == 1;
			if (flag3)
			{
				mapPoint.PosX.AddRange(mapPoint2.PosX);
				mapPoint.PosX1.AddRange(mapPoint2.PosX1);
			}
			else
			{
				mapPoint.PosX.AddRange(mapPoint2.PosX1);
				mapPoint.PosX1.AddRange(mapPoint2.PosX);
			}
			return mapPoint;
		}

		// Token: 0x060073C5 RID: 29637 RVA: 0x00264230 File Offset: 0x00262430
		public static MapPoint GetPVEMapRandomPos(int index)
		{
			MapPoint mapPoint = new MapPoint();
			bool flag = index != 0 && !MapMgr._maps.Keys.Contains(index);
			if (flag)
			{
				index = 0;
			}
			bool flag2 = index == 0;
			MapPoint mapPoint2;
			if (flag2)
			{
				int[] array = MapMgr._maps.Keys.ToArray<int>();
				mapPoint2 = MapMgr._maps[array[MapMgr.random.Next(array.Length)]];
			}
			else
			{
				mapPoint2 = MapMgr._maps[index];
			}
			mapPoint.PosX.AddRange(mapPoint2.PosX);
			mapPoint.PosX1.AddRange(mapPoint2.PosX1);
			return mapPoint;
		}

		// Token: 0x060073C6 RID: 29638 RVA: 0x002642D8 File Offset: 0x002624D8
		public static bool InitServerMap(Dictionary<int, List<int>> servermap)
		{
			MapBussiness mapBussiness = new MapBussiness();
			ServerMapInfo[] allServerMap = mapBussiness.GetAllServerMap();
			try
			{
				int num = 0;
				ServerMapInfo[] array = allServerMap;
				ServerMapInfo[] array2 = array;
				ServerMapInfo[] array3 = array2;
				foreach (ServerMapInfo serverMapInfo in array3)
				{
					bool flag = servermap.Keys.Contains(serverMapInfo.ServerID);
					if (!flag)
					{
						string[] array5 = serverMapInfo.OpenMap.Split(new char[] { '|' });
						string[] array6 = array5[array5.Length - 1].Split(new char[] { ',' });
						List<int> list = new List<int>();
						string[] array7 = array6;
						string[] array8 = array7;
						string[] array9 = array8;
						foreach (string text in array9)
						{
							bool flag2 = !string.IsNullOrEmpty(text) && int.TryParse(text, out num);
							if (flag2)
							{
								list.Add(num);
							}
						}
						servermap.Add(serverMapInfo.ServerID, list);
					}
				}
			}
			catch (Exception ex)
			{
				MapMgr.log.Error(ex.ToString());
			}
			return true;
		}

		// Token: 0x040043CC RID: 17356
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040043CD RID: 17357
		private static Dictionary<int, MapPoint> _maps;

		// Token: 0x040043CE RID: 17358
		private static Dictionary<int, Map> _mapInfos;

		// Token: 0x040043CF RID: 17359
		private static Dictionary<int, List<int>> _serverMap;

		// Token: 0x040043D0 RID: 17360
		private static ThreadSafeRandom random;

		// Token: 0x040043D1 RID: 17361
		private static ReaderWriterLock m_lock;
	}
}
