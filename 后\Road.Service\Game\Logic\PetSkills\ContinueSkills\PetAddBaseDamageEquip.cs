﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D00 RID: 3328
	public class PetAddBaseDamageEquip : AbstractPetEffect
	{
		// Token: 0x0600785C RID: 30812 RVA: 0x00285724 File Offset: 0x00283924
		public PetAddBaseDamageEquip(int count, string elementID)
			: base(ePetEffectType.PetAddBaseDamageEquip, elementID)
		{
			this.m_count = count;
			bool flag = elementID == null;
			if (!flag)
			{
				int length = elementID.Length;
				bool flag2 = length != 4;
				if (!flag2)
				{
					switch (elementID[2])
					{
					case '1':
					{
						bool flag3 = elementID == "1814";
						if (flag3)
						{
							this.m_value = 20.0;
							this.m_percent = true;
						}
						return;
					}
					case '2':
					{
						bool flag4 = !(elementID == "1622");
						if (flag4)
						{
							return;
						}
						break;
					}
					case '3':
					case '7':
						return;
					case '4':
					{
						bool flag5 = elementID == "1744";
						if (flag5)
						{
							this.m_value = 35.0;
							this.m_percent = true;
						}
						return;
					}
					case '5':
					{
						bool flag6 = !(elementID == "1153");
						if (flag6)
						{
							bool flag7 = elementID == "1154";
							if (flag7)
							{
								this.m_value = 25.0;
								this.m_percent = true;
							}
							return;
						}
						break;
					}
					case '6':
					{
						bool flag8 = elementID == "1465";
						if (flag8)
						{
							this.m_value = 30.0;
							this.m_percent = true;
						}
						return;
					}
					case '8':
					{
						bool flag9 = !(elementID == "1387");
						if (flag9)
						{
							bool flag10 = elementID == "1483";
							if (flag10)
							{
								this.m_value = 6.0;
								this.m_percent = true;
							}
						}
						else
						{
							this.m_value = 20.0;
							this.m_percent = true;
						}
						return;
					}
					case '9':
						if (!(elementID == "1490"))
						{
							if (!(elementID == "1196"))
							{
								if (elementID == "1197")
								{
									this.m_value = 150.0;
								}
							}
							else
							{
								this.m_value = 100.0;
							}
						}
						else
						{
							this.m_value = 5.0;
							this.m_percent = true;
						}
						return;
					default:
						return;
					}
					this.m_value = 15.0;
					this.m_percent = true;
				}
			}
		}

		// Token: 0x0600785D RID: 30813 RVA: 0x0028597C File Offset: 0x00283B7C
		public override bool Start(Living living)
		{
			PetAddBaseDamageEquip petAddBaseDamageEquip = living.PetEffectList.GetOfType(ePetEffectType.PetAddBaseDamageEquip) as PetAddBaseDamageEquip;
			bool flag = petAddBaseDamageEquip != null;
			bool flag2;
			if (flag)
			{
				petAddBaseDamageEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600785E RID: 30814 RVA: 0x002859C4 File Offset: 0x00283BC4
		public override void OnAttached(Living living)
		{
			bool flag = this.m_added == 0.0;
			if (flag)
			{
				bool percent = this.m_percent;
				if (percent)
				{
					this.m_added = living.BaseDamage * this.m_value / 100.0;
					living.BaseDamage += this.m_added;
				}
				else
				{
					this.m_added = this.m_value;
				}
				living.BaseDamage += this.m_added;
			}
			living.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x0600785F RID: 30815 RVA: 0x0002C985 File Offset: 0x0002AB85
		public override void OnRemoved(Living living)
		{
			living.BaseDamage -= this.m_added;
			this.m_added = 0.0;
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x06007860 RID: 30816 RVA: 0x00285A5C File Offset: 0x00283C5C
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
		}

		// Token: 0x0400468D RID: 18061
		private int m_count;

		// Token: 0x0400468E RID: 18062
		private double m_value;

		// Token: 0x0400468F RID: 18063
		private bool m_percent;

		// Token: 0x04004690 RID: 18064
		private double m_added;
	}
}
