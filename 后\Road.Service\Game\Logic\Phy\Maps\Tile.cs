﻿using System;
using System.Drawing;
using System.IO;

namespace Game.Logic.Phy.Maps
{
	// Token: 0x02000CE7 RID: 3303
	public class Tile
	{
		// Token: 0x17001492 RID: 5266
		// (get) Token: 0x060077D8 RID: 30680 RVA: 0x0002C288 File Offset: 0x0002A488
		public Rectangle Bound
		{
			get
			{
				return this._rect;
			}
		}

		// Token: 0x17001493 RID: 5267
		// (get) Token: 0x060077D9 RID: 30681 RVA: 0x0002C290 File Offset: 0x0002A490
		public byte[] Data
		{
			get
			{
				return this._data;
			}
		}

		// Token: 0x17001494 RID: 5268
		// (get) Token: 0x060077DA RID: 30682 RVA: 0x0002C298 File Offset: 0x0002A498
		public int Width
		{
			get
			{
				return this._width;
			}
		}

		// Token: 0x17001495 RID: 5269
		// (get) Token: 0x060077DB RID: 30683 RVA: 0x0002C2A0 File Offset: 0x0002A4A0
		public int Height
		{
			get
			{
				return this._height;
			}
		}

		// Token: 0x060077DC RID: 30684 RVA: 0x00282A70 File Offset: 0x00280C70
		public Tile(byte[] data, int width, int height, bool digable)
		{
			this._data = data;
			this._width = width;
			this._height = height;
			this._digable = digable;
			this._bw = this._width / 8 + 1;
			this._bh = this._height;
			this._rect = new Rectangle(0, 0, this._width, this._height);
			GC.AddMemoryPressure((long)data.Length);
		}

		// Token: 0x060077DD RID: 30685 RVA: 0x00282AF0 File Offset: 0x00280CF0
		public Tile(Bitmap bitmap, bool digable)
		{
			this._width = bitmap.Width;
			this._height = bitmap.Height;
			this._bw = this._width / 8 + 1;
			this._bh = this._height;
			this._data = new byte[this._bw * this._bh];
			this._digable = digable;
			for (int i = 0; i < bitmap.Height; i++)
			{
				for (int j = 0; j < bitmap.Width; j++)
				{
					byte b = ((bitmap.GetPixel(j, i).A > 100) ? 1 : 0);
					byte[] data = this._data;
					int num = i * this._bw + j / 8;
					data[num] |= (byte)(b << 7 - j % 8);
				}
			}
			this._rect = new Rectangle(0, 0, this._width, this._height);
			GC.AddMemoryPressure((long)this._data.Length);
		}

		// Token: 0x060077DE RID: 30686 RVA: 0x00282C00 File Offset: 0x00280E00
		public Tile(string file, bool digable)
		{
			FileStream fileStream = File.Open(file, FileMode.Open);
			BinaryReader binaryReader = new BinaryReader(fileStream);
			this._width = binaryReader.ReadInt32();
			this._height = binaryReader.ReadInt32();
			this._bw = this._width / 8 + 1;
			this._bh = this._height;
			this._data = binaryReader.ReadBytes(this._bw * this._bh);
			this._digable = digable;
			this._rect = new Rectangle(0, 0, this._width, this._height);
			binaryReader.Close();
			GC.AddMemoryPressure((long)this._data.Length);
		}

		// Token: 0x060077DF RID: 30687 RVA: 0x00282CB8 File Offset: 0x00280EB8
		public void Dig(int cx, int cy, Tile surface, Tile border)
		{
			bool flag = this._digable && surface != null;
			if (flag)
			{
				int num = cx - surface.Width / 2;
				int num2 = cy - surface.Height / 2;
				this.Remove(num, num2, surface);
				bool flag2 = border != null;
				if (flag2)
				{
					num = cx - border.Width / 2;
					num2 = cy - border.Height / 2;
					this.Add(num, num2, surface);
				}
			}
		}

		// Token: 0x060077E0 RID: 30688 RVA: 0x00282D28 File Offset: 0x00280F28
		protected void Add(int x, int y, Tile tile)
		{
			byte[] data = tile._data;
			Rectangle bound = tile.Bound;
			bound.Offset(x, y);
			bound.Intersect(this._rect);
			bool flag = bound.Width == 0 || bound.Height == 0;
			if (!flag)
			{
				bound.Offset(-x, -y);
				int num = bound.X / 8;
				int num2 = (bound.X + x) / 8;
				int y2 = bound.Y;
				int num3 = (int)Math.Floor((double)bound.Width / 8.0);
				int height = bound.Height;
				int num4 = bound.X % 8;
				for (int i = 0; i < height; i++)
				{
					int num5 = 0;
					for (int j = 0; j < num3; j++)
					{
						int num6 = (i + y + y2) * this._bw + j + num2;
						int num7 = (i + y2) * tile._bw + j + num;
						int num8 = (int)data[num7];
						int num9 = num8 >> num4;
						byte[] data2 = this._data;
						int num10 = num6;
						data2[num10] |= (byte)num9;
						byte[] data3 = this._data;
						int num11 = num6;
						data3[num11] |= (byte)num5;
						num5 = num8 << 8 - num4;
					}
				}
			}
		}

		// Token: 0x060077E1 RID: 30689 RVA: 0x00282E98 File Offset: 0x00281098
		protected void Remove(int x, int y, Tile tile)
		{
			byte[] data = tile._data;
			Rectangle bound = tile.Bound;
			bound.Offset(x, y);
			bound.Intersect(this._rect);
			bool flag = bound.Width == 0 || bound.Height == 0;
			if (!flag)
			{
				bound.Offset(-x, -y);
				int num = bound.X / 8;
				int num2 = (bound.X + x) / 8;
				int y2 = bound.Y;
				int num3 = bound.Width / 8 + 1;
				int height = bound.Height;
				bool flag2 = bound.X == 0;
				if (flag2)
				{
					bool flag3 = num3 + num2 < this._bw;
					if (flag3)
					{
						num3++;
						num3 = ((num3 > tile._bw) ? tile._bw : num3);
					}
					int num4 = (bound.X + x) % 8;
					for (int i = 0; i < height; i++)
					{
						int num5 = 0;
						for (int j = 0; j < num3; j++)
						{
							int num6 = (i + y + y2) * this._bw + j + num2;
							int num7 = (i + y2) * tile._bw + j + num;
							int num8 = (int)data[num7];
							int num9 = num8 >> num4;
							int num10 = (int)this._data[num6];
							num10 &= ~(num10 & num9);
							bool flag4 = num5 != 0;
							if (flag4)
							{
								num10 &= ~(num10 & num5);
							}
							this._data[num6] = (byte)num10;
							num5 = num8 << 8 - num4;
						}
					}
				}
				else
				{
					int num11 = bound.X % 8;
					for (int k = 0; k < height; k++)
					{
						for (int l = 0; l < num3; l++)
						{
							int num12 = (k + y + y2) * this._bw + l + num2;
							int num13 = (k + y2) * tile._bw + l + num;
							int num14 = (int)data[num13];
							int num15 = num14 << num11;
							bool flag5 = l < num3 - 1;
							int num16;
							if (flag5)
							{
								num14 = (int)data[num13 + 1];
								num16 = num14 >> 8 - num11;
							}
							else
							{
								num16 = 0;
							}
							int num17 = (int)this._data[num12];
							num17 &= ~(num17 & num15);
							bool flag6 = num16 != 0;
							if (flag6)
							{
								num17 &= ~(num17 & num16);
							}
							this._data[num12] = (byte)num17;
						}
					}
				}
			}
		}

		// Token: 0x060077E2 RID: 30690 RVA: 0x00283158 File Offset: 0x00281358
		public bool IsEmpty(int x, int y)
		{
			bool flag = x >= 0 && x < this._width && y >= 0 && y < this._height;
			bool flag2;
			if (flag)
			{
				byte b = (byte)(1 << 7 - x % 8);
				flag2 = (this._data[y * this._bw + x / 8] & b) == 0;
			}
			else
			{
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x060077E3 RID: 30691 RVA: 0x002831B4 File Offset: 0x002813B4
		public bool IsYLineEmtpy(int x, int y, int h)
		{
			bool flag = x >= 0 && x < this._width;
			bool flag3;
			if (flag)
			{
				y = ((y >= 0) ? y : 0);
				h = ((y + h > this._height) ? (this._height - y) : h);
				for (int i = 0; i < h; i++)
				{
					bool flag2 = !this.IsEmpty(x, y + i);
					if (flag2)
					{
						return false;
					}
				}
				flag3 = true;
			}
			else
			{
				flag3 = true;
			}
			return flag3;
		}

		// Token: 0x060077E4 RID: 30692 RVA: 0x0028322C File Offset: 0x0028142C
		public bool IsRectangleEmptyQuick(Rectangle rect)
		{
			rect.Intersect(this._rect);
			return this.IsEmpty(rect.Right, rect.Bottom) && this.IsEmpty(rect.Left, rect.Bottom) && this.IsEmpty(rect.Right, rect.Top) && this.IsEmpty(rect.Left, rect.Top);
		}

		// Token: 0x060077E5 RID: 30693 RVA: 0x002832B0 File Offset: 0x002814B0
		public Point FindNotEmptyPoint(int x, int y, int h)
		{
			bool flag = x >= 0 && x < this._width;
			Point point;
			if (flag)
			{
				y = ((y >= 0) ? y : 0);
				h = ((y + h > this._height) ? (this._height - y) : h);
				for (int i = 0; i < h; i++)
				{
					bool flag2 = !this.IsEmpty(x, y + i);
					if (flag2)
					{
						return new Point(x, y + i);
					}
				}
				point = new Point(-1, -1);
			}
			else
			{
				point = new Point(-1, -1);
			}
			return point;
		}

		// Token: 0x060077E6 RID: 30694 RVA: 0x0028333C File Offset: 0x0028153C
		public Bitmap ToBitmap()
		{
			Bitmap bitmap = new Bitmap(this._width, this._height);
			for (int i = 0; i < this._height; i++)
			{
				for (int j = 0; j < this._width; j++)
				{
					bool flag = this.IsEmpty(j, i);
					if (flag)
					{
						bitmap.SetPixel(j, i, Color.FromArgb(0, 0, 0, 0));
					}
					else
					{
						bitmap.SetPixel(j, i, Color.FromArgb(255, 0, 0, 0));
					}
				}
			}
			return bitmap;
		}

		// Token: 0x060077E7 RID: 30695 RVA: 0x002833D0 File Offset: 0x002815D0
		public Tile Clone()
		{
			return new Tile(this._data.Clone() as byte[], this._width, this._height, this._digable);
		}

		// Token: 0x0400461C RID: 17948
		private byte[] _data;

		// Token: 0x0400461D RID: 17949
		private int _width;

		// Token: 0x0400461E RID: 17950
		private int _height;

		// Token: 0x0400461F RID: 17951
		private Rectangle _rect;

		// Token: 0x04004620 RID: 17952
		private int _bw = 0;

		// Token: 0x04004621 RID: 17953
		private int _bh = 0;

		// Token: 0x04004622 RID: 17954
		private bool _digable;
	}
}
