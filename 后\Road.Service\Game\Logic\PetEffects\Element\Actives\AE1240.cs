﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E14 RID: 3604
	public class AE1240 : BasePetEffect
	{
		// Token: 0x06007E19 RID: 32281 RVA: 0x0029EF8C File Offset: 0x0029D18C
		public AE1240(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1240, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E1A RID: 32282 RVA: 0x0029F00C File Offset: 0x0029D20C
		public override bool Start(Living living)
		{
			AE1240 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1240) as AE1240;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E1B RID: 32283 RVA: 0x00030B79 File Offset: 0x0002ED79
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E1C RID: 32284 RVA: 0x00030B8F File Offset: 0x0002ED8F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E1D RID: 32285 RVA: 0x0029F06C File Offset: 0x0029D26C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1240(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004CC4 RID: 19652
		private int m_type = 0;

		// Token: 0x04004CC5 RID: 19653
		private int m_count = 0;

		// Token: 0x04004CC6 RID: 19654
		private int m_probability = 0;

		// Token: 0x04004CC7 RID: 19655
		private int m_delay = 0;

		// Token: 0x04004CC8 RID: 19656
		private int m_coldDown = 0;

		// Token: 0x04004CC9 RID: 19657
		private int m_currentId;

		// Token: 0x04004CCA RID: 19658
		private int m_added = 0;
	}
}
