﻿using System;
using System.Collections.Generic;
using System.Drawing;

namespace Game.Logic
{
	// Token: 0x02000CA4 RID: 3236
	public class MapPoint
	{
		// Token: 0x170013D9 RID: 5081
		// (get) Token: 0x060073C9 RID: 29641 RVA: 0x00264414 File Offset: 0x00262614
		// (set) Token: 0x060073CA RID: 29642 RVA: 0x0002B11A File Offset: 0x0002931A
		public List<Point> PosX
		{
			get
			{
				return this.posX;
			}
			set
			{
				this.posX = value;
			}
		}

		// Token: 0x170013DA RID: 5082
		// (get) Token: 0x060073CB RID: 29643 RVA: 0x0026442C File Offset: 0x0026262C
		// (set) Token: 0x060073CC RID: 29644 RVA: 0x0002B124 File Offset: 0x00029324
		public List<Point> PosX1
		{
			get
			{
				return this.posX1;
			}
			set
			{
				this.posX1 = value;
			}
		}

		// Token: 0x060073CD RID: 29645 RVA: 0x0002B12E File Offset: 0x0002932E
		public MapPoint()
		{
			this.posX = new List<Point>();
			this.posX1 = new List<Point>();
		}

		// Token: 0x040043D2 RID: 17362
		private List<Point> posX;

		// Token: 0x040043D3 RID: 17363
		private List<Point> posX1;
	}
}
