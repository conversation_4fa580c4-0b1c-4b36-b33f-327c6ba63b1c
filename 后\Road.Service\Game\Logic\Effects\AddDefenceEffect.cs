﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000ED6 RID: 3798
	public class AddDefenceEffect : BasePlayerEffect
	{
		// Token: 0x060082B4 RID: 33460 RVA: 0x000338CA File Offset: 0x00031ACA
		public AddDefenceEffect(int count, int probability)
			: base(eEffectType.AddDefenceEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
			this.m_added = 0;
		}

		// Token: 0x060082B5 RID: 33461 RVA: 0x002B1788 File Offset: 0x002AF988
		public override bool Start(Living living)
		{
			AddDefenceEffect addDefenceEffect = living.EffectList.GetOfType(eEffectType.AddDefenceEffect) as AddDefenceEffect;
			bool flag = addDefenceEffect != null;
			bool flag2;
			if (flag)
			{
				addDefenceEffect.m_probability = ((this.m_probability > addDefenceEffect.m_probability) ? this.m_probability : addDefenceEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082B6 RID: 33462 RVA: 0x000338EA File Offset: 0x00031AEA
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginAttacked += this.ChangeProperty;
		}

		// Token: 0x060082B7 RID: 33463 RVA: 0x00033900 File Offset: 0x00031B00
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginAttacked -= this.ChangeProperty;
		}

		// Token: 0x060082B8 RID: 33464 RVA: 0x002B17E4 File Offset: 0x002AF9E4
		public void ChangeProperty(Living living)
		{
			living.Defence -= (double)this.m_added;
			this.m_added = 0;
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				this.IsTrigger = true;
				living.Defence += (double)this.m_count;
				this.m_added = this.m_count;
				living.DefenceEffectTrigger = true;
				living.Game.AddAction(new LivingSayAction(living, LanguageMgr.GetTranslation("AddDefenceEffect.msg", Array.Empty<object>()), 9, 3000, 1000));
			}
		}

		// Token: 0x040051D1 RID: 20945
		private int m_count;

		// Token: 0x040051D2 RID: 20946
		private int m_probability;

		// Token: 0x040051D3 RID: 20947
		private int m_added;
	}
}
