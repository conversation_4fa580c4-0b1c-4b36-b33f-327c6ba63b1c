﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DCF RID: 3535
	public class AE1118 : BasePetEffect
	{
		// Token: 0x06007CB0 RID: 31920 RVA: 0x0002FD1B File Offset: 0x0002DF1B
		public AE1118(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1118, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CB1 RID: 31921 RVA: 0x002989FC File Offset: 0x00296BFC
		public override bool Start(Living living)
		{
			AE1118 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1118) as AE1118;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CB2 RID: 31922 RVA: 0x0002FD59 File Offset: 0x0002DF59
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerCure += new PlayerEventHandle(this.player_AfterKilledByLiving);
		}

		// Token: 0x06007CB3 RID: 31923 RVA: 0x0002FD6F File Offset: 0x0002DF6F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerCure -= new PlayerEventHandle(this.player_AfterKilledByLiving);
		}

		// Token: 0x06007CB4 RID: 31924 RVA: 0x00298A58 File Offset: 0x00296C58
		private void player_AfterKilledByLiving(Living living)
		{
			bool flag = this.rand.Next(10000) < this.m_probability;
			if (flag)
			{
				living.PetEffectTrigger = true;
				new CE1118(2, this.m_currentId, base.Info.ID.ToString()).Start(living);
			}
		}

		// Token: 0x04004AE3 RID: 19171
		private int m_type;

		// Token: 0x04004AE4 RID: 19172
		private int m_count;

		// Token: 0x04004AE5 RID: 19173
		private int m_probability;

		// Token: 0x04004AE6 RID: 19174
		private int m_delay;

		// Token: 0x04004AE7 RID: 19175
		private int m_currentId;
	}
}
