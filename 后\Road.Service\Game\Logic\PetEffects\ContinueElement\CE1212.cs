﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E9F RID: 3743
	public class CE1212 : BasePetEffect
	{
		// Token: 0x0600815D RID: 33117 RVA: 0x002ACA24 File Offset: 0x002AAC24
		public CE1212(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1212, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600815E RID: 33118 RVA: 0x002ACAA4 File Offset: 0x002AACA4
		public override bool Start(Living living)
		{
			CE1212 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1212) as CE1212;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600815F RID: 33119 RVA: 0x00032AEA File Offset: 0x00030CEA
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008160 RID: 33120 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008161 RID: 33121 RVA: 0x002ACB04 File Offset: 0x002AAD04
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008162 RID: 33122 RVA: 0x00032B13 File Offset: 0x00030D13
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PetEffects.CritRate = 0;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005096 RID: 20630
		private int m_type = 0;

		// Token: 0x04005097 RID: 20631
		private int m_count = 0;

		// Token: 0x04005098 RID: 20632
		private int m_probability = 0;

		// Token: 0x04005099 RID: 20633
		private int m_delay = 0;

		// Token: 0x0400509A RID: 20634
		private int m_coldDown = 0;

		// Token: 0x0400509B RID: 20635
		private int m_currentId;

		// Token: 0x0400509C RID: 20636
		private int m_added = 0;
	}
}
