﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E59 RID: 3673
	public class CE1046 : BasePetEffect
	{
		// Token: 0x06007FA4 RID: 32676 RVA: 0x002A5E0C File Offset: 0x002A400C
		public CE1046(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1046, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FA5 RID: 32677 RVA: 0x002A5E8C File Offset: 0x002A408C
		public override bool Start(Living living)
		{
			CE1046 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1046) as CE1046;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FA6 RID: 32678 RVA: 0x002A5EEC File Offset: 0x002A40EC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.Defence += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007FA7 RID: 32679 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FA8 RID: 32680 RVA: 0x002A5F4C File Offset: 0x002A414C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007FA9 RID: 32681 RVA: 0x002A5F80 File Offset: 0x002A4180
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Defence -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004EAA RID: 20138
		private int m_type = 0;

		// Token: 0x04004EAB RID: 20139
		private int m_count = 0;

		// Token: 0x04004EAC RID: 20140
		private int m_probability = 0;

		// Token: 0x04004EAD RID: 20141
		private int m_delay = 0;

		// Token: 0x04004EAE RID: 20142
		private int m_coldDown = 0;

		// Token: 0x04004EAF RID: 20143
		private int m_currentId;

		// Token: 0x04004EB0 RID: 20144
		private int m_added = 0;
	}
}
