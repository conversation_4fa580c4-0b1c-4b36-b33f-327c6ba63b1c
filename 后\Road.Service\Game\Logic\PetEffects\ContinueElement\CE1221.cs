﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EA2 RID: 3746
	public class CE1221 : BasePetEffect
	{
		// Token: 0x0600816F RID: 33135 RVA: 0x002ACE10 File Offset: 0x002AB010
		public CE1221(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1221, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008170 RID: 33136 RVA: 0x002ACE90 File Offset: 0x002AB090
		public override bool Start(Living living)
		{
			CE1221 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1221) as CE1221;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008171 RID: 33137 RVA: 0x002ACEF0 File Offset: 0x002AB0F0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 200;
				player.BaseGuard += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008172 RID: 33138 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008173 RID: 33139 RVA: 0x002ACF54 File Offset: 0x002AB154
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008174 RID: 33140 RVA: 0x002ACF88 File Offset: 0x002AB188
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BaseGuard -= (double)this.m_added;
			this.m_added = 0;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x040050AB RID: 20651
		private int m_type = 0;

		// Token: 0x040050AC RID: 20652
		private int m_count = 0;

		// Token: 0x040050AD RID: 20653
		private int m_probability = 0;

		// Token: 0x040050AE RID: 20654
		private int m_delay = 0;

		// Token: 0x040050AF RID: 20655
		private int m_coldDown = 0;

		// Token: 0x040050B0 RID: 20656
		private int m_currentId;

		// Token: 0x040050B1 RID: 20657
		private int m_added = 0;
	}
}
