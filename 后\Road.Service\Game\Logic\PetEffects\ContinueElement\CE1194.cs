﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E8E RID: 3726
	public class CE1194 : BasePetEffect
	{
		// Token: 0x060080EE RID: 33006 RVA: 0x002AAF10 File Offset: 0x002A9110
		public CE1194(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1194, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060080EF RID: 33007 RVA: 0x002AAF90 File Offset: 0x002A9190
		public override bool Start(Living living)
		{
			CE1194 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1194) as CE1194;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080F0 RID: 33008 RVA: 0x002AAFF0 File Offset: 0x002A91F0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.Attack += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060080F1 RID: 33009 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060080F2 RID: 33010 RVA: 0x002AB050 File Offset: 0x002A9250
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060080F3 RID: 33011 RVA: 0x00032712 File Offset: 0x00030912
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Attack -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x0400501F RID: 20511
		private int m_type = 0;

		// Token: 0x04005020 RID: 20512
		private int m_count = 0;

		// Token: 0x04005021 RID: 20513
		private int m_probability = 0;

		// Token: 0x04005022 RID: 20514
		private int m_delay = 0;

		// Token: 0x04005023 RID: 20515
		private int m_coldDown = 0;

		// Token: 0x04005024 RID: 20516
		private int m_currentId;

		// Token: 0x04005025 RID: 20517
		private int m_added = 0;
	}
}
