﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D70 RID: 3440
	public class PE1200 : BasePetEffect
	{
		// Token: 0x06007AC7 RID: 31431 RVA: 0x002904D0 File Offset: 0x0028E6D0
		public PE1200(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1200, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AC8 RID: 31432 RVA: 0x00290550 File Offset: 0x0028E750
		public override bool Start(Living living)
		{
			PE1200 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1200) as PE1200;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AC9 RID: 31433 RVA: 0x0002EA78 File Offset: 0x0002CC78
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_beginSeftTurn;
		}

		// Token: 0x06007ACA RID: 31434 RVA: 0x0002EA8E File Offset: 0x0002CC8E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_beginSeftTurn;
		}

		// Token: 0x06007ACB RID: 31435 RVA: 0x002905B0 File Offset: 0x0028E7B0
		public void player_beginSeftTurn(Living living)
		{
			List<Player> allTeamPlayers = living.Game.GetAllTeamPlayers(living);
			foreach (Player player in allTeamPlayers)
			{
				player.AddPetMP(2);
			}
		}

		// Token: 0x04004850 RID: 18512
		private int m_type = 0;

		// Token: 0x04004851 RID: 18513
		private int m_count = 0;

		// Token: 0x04004852 RID: 18514
		private int m_probability = 0;

		// Token: 0x04004853 RID: 18515
		private int m_delay = 0;

		// Token: 0x04004854 RID: 18516
		private int m_coldDown = 0;

		// Token: 0x04004855 RID: 18517
		private int m_currentId;

		// Token: 0x04004856 RID: 18518
		private int m_added = 0;
	}
}
