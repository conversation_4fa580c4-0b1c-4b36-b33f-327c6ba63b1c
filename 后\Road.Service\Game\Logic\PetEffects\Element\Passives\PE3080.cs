﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D8B RID: 3467
	public class PE3080 : BasePetEffect
	{
		// Token: 0x06007B4D RID: 31565 RVA: 0x0002EEFA File Offset: 0x0002D0FA
		public PE3080(string elementID)
			: base(ePetEffectType.PE3080, elementID)
		{
		}

		// Token: 0x06007B4E RID: 31566 RVA: 0x00292864 File Offset: 0x00290A64
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PE3080) is PE3080;
			return flag || base.Start(living);
		}

		// Token: 0x06007B4F RID: 31567 RVA: 0x0002EF0A File Offset: 0x0002D10A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_Turn;
		}

		// Token: 0x06007B50 RID: 31568 RVA: 0x0002EF20 File Offset: 0x0002D120
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_Turn;
		}

		// Token: 0x06007B51 RID: 31569 RVA: 0x002928A0 File Offset: 0x00290AA0
		public void player_Turn(Living living)
		{
			bool flag = false;
			foreach (Player player in living.Game.GetAllEnemyPlayers(living as Player))
			{
				flag = living.Agility + living.Speed * 30.0 >= player.Agility + player.Speed * 30.0;
			}
			bool flag2 = flag || living.Game is PVEGame;
			if (flag2)
			{
				(living as Player).AddPetMP(10);
			}
			else
			{
				(living as Player).AddPetMP(5);
			}
		}

		// Token: 0x0400490D RID: 18701
		private int m_currentId;
	}
}
