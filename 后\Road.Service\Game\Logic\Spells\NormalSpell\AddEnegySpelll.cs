﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.NormalSpell
{
	// Token: 0x02000CB4 RID: 3252
	[SpellAttibute(91)]
	public class AddEnegySpelll : ISpellHandler
	{
		// Token: 0x060074EA RID: 29930 RVA: 0x0026DE80 File Offset: 0x0026C080
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.SetBall(117);
			}
		}
	}
}
