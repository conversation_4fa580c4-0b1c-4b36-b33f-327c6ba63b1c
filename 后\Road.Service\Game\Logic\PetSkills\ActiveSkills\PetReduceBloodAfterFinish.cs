﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D40 RID: 3392
	public class PetReduceBloodAfterFinish : BasePetEffect
	{
		// Token: 0x060079B5 RID: 31157 RVA: 0x0002DDA7 File Offset: 0x0002BFA7
		public PetReduceBloodAfterFinish(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetReduceBloodAfterFinish, elementID)
		{
			this.m_count = count;
			this.m_skillId = skillId;
			this.m_probability = ((probability == -1) ? 10000 : probability);
		}

		// Token: 0x060079B6 RID: 31158 RVA: 0x0028C3A4 File Offset: 0x0028A5A4
		private void player_PlayerBuffSkillPet(Living living_0)
		{
			bool flag = living_0.PetEffects.CurrentUseSkill == this.m_skillId;
			if (flag)
			{
				for (int i = 0; i < this.m_count; i++)
				{
					living_0.Game.sendShowPicSkil(living_0, base.ElementInfo, true);
				}
				living_0.AddPetEffect(new PetReduceBloodAfterFinishEquip(this.m_count, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x060079B7 RID: 31159 RVA: 0x0002DDD8 File Offset: 0x0002BFD8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079B8 RID: 31160 RVA: 0x0002DDEE File Offset: 0x0002BFEE
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079B9 RID: 31161 RVA: 0x0028C41C File Offset: 0x0028A61C
		public override bool Start(Living living)
		{
			PetReduceBloodAfterFinish petReduceBloodAfterFinish = living.PetEffectList.GetOfType(ePetEffectType.PetReduceBloodAfterFinish) as PetReduceBloodAfterFinish;
			bool flag = petReduceBloodAfterFinish != null;
			bool flag2;
			if (flag)
			{
				petReduceBloodAfterFinish.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0400474E RID: 18254
		private int m_count;

		// Token: 0x0400474F RID: 18255
		private int m_skillId;

		// Token: 0x04004750 RID: 18256
		private int m_probability;
	}
}
