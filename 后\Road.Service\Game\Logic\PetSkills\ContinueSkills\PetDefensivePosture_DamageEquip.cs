﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D0B RID: 3339
	public class PetDefensivePosture_DamageEquip : BasePetEffect
	{
		// Token: 0x06007894 RID: 30868 RVA: 0x00286BA4 File Offset: 0x00284DA4
		public PetDefensivePosture_DamageEquip(int count, string elementID)
			: base(ePetEffectType.PetDefensivePosture_DamageEquip, elementID)
		{
			this.m_count = count;
			bool flag = !(elementID == "1554");
			if (flag)
			{
				bool flag2 = elementID == "1555";
				if (flag2)
				{
					this.m_value = 40;
					this.m_value2 = 60;
				}
			}
			else
			{
				this.m_value = 20;
			}
		}

		// Token: 0x06007895 RID: 30869 RVA: 0x00286C08 File Offset: 0x00284E08
		public override bool Start(Living living)
		{
			PetDefensivePosture_DamageEquip petDefensivePosture_DamageEquip = living.PetEffectList.GetOfType(ePetEffectType.PetDefensivePosture_DamageEquip) as PetDefensivePosture_DamageEquip;
			bool flag = petDefensivePosture_DamageEquip != null;
			bool flag2;
			if (flag)
			{
				petDefensivePosture_DamageEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007896 RID: 30870 RVA: 0x0002CBEF File Offset: 0x0002ADEF
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x06007897 RID: 30871 RVA: 0x0002CC18 File Offset: 0x0002AE18
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.sendShowPicSkil(player, base.ElementInfo, false);
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x06007898 RID: 30872 RVA: 0x00286C50 File Offset: 0x00284E50
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
		}

		// Token: 0x06007899 RID: 30873 RVA: 0x0002CC55 File Offset: 0x0002AE55
		private void player_BeforeTakeDamage(Living living, Living targe, ref int damageAmount, ref int criticalAmount)
		{
			damageAmount -= damageAmount * this.m_value / 100;
			criticalAmount -= criticalAmount * this.m_value2 / 100;
		}

		// Token: 0x040046AD RID: 18093
		private int m_count;

		// Token: 0x040046AE RID: 18094
		private int m_value;

		// Token: 0x040046AF RID: 18095
		private int m_value2;
	}
}
