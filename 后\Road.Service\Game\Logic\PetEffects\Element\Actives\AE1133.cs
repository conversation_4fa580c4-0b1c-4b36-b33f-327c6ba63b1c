﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DD4 RID: 3540
	public class AE1133 : BasePetEffect
	{
		// Token: 0x06007CCC RID: 31948 RVA: 0x00299048 File Offset: 0x00297248
		public AE1133(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1133, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CCD RID: 31949 RVA: 0x002990C8 File Offset: 0x002972C8
		public override bool Start(Living living)
		{
			AE1133 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1133) as AE1133;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CCE RID: 31950 RVA: 0x0002FEA7 File Offset: 0x0002E0A7
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007CCF RID: 31951 RVA: 0x0002FED0 File Offset: 0x0002E0D0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007CD0 RID: 31952 RVA: 0x00299128 File Offset: 0x00297328
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 120;
				player.CurrentDamagePlus += (float)(this.m_added / 100);
			}
		}

		// Token: 0x06007CD1 RID: 31953 RVA: 0x00299170 File Offset: 0x00297370
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_added / 100);
				this.m_added = 0;
			}
		}

		// Token: 0x04004B04 RID: 19204
		private int m_type = 0;

		// Token: 0x04004B05 RID: 19205
		private int m_count = 0;

		// Token: 0x04004B06 RID: 19206
		private int m_probability = 0;

		// Token: 0x04004B07 RID: 19207
		private int m_delay = 0;

		// Token: 0x04004B08 RID: 19208
		private int m_coldDown = 0;

		// Token: 0x04004B09 RID: 19209
		private int m_currentId;

		// Token: 0x04004B0A RID: 19210
		private int m_added = 0;
	}
}
