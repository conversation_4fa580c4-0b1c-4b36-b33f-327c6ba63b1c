﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E88 RID: 3720
	public class CE1182 : BasePetEffect
	{
		// Token: 0x1700149D RID: 5277
		// (get) Token: 0x060080C8 RID: 32968 RVA: 0x000325E4 File Offset: 0x000307E4
		public int Count
		{
			get
			{
				return this.m_count;
			}
		}

		// Token: 0x060080C9 RID: 32969 RVA: 0x002AA5D0 File Offset: 0x002A87D0
		public CE1182(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1182, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060080CA RID: 32970 RVA: 0x002AA650 File Offset: 0x002A8850
		public override bool Start(Living living)
		{
			CE1182 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1182) as CE1182;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080CB RID: 32971 RVA: 0x002AA6B0 File Offset: 0x002A88B0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 200;
				player.BaseGuard += (double)this.m_added;
				player.Game.SendPetBuff(player, base.Info, true);
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060080CC RID: 32972 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060080CD RID: 32973 RVA: 0x002AA728 File Offset: 0x002A8928
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060080CE RID: 32974 RVA: 0x002AA75C File Offset: 0x002A895C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BaseGuard -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004FF5 RID: 20469
		private int m_type = 0;

		// Token: 0x04004FF6 RID: 20470
		private int m_count = 0;

		// Token: 0x04004FF7 RID: 20471
		private int m_probability = 0;

		// Token: 0x04004FF8 RID: 20472
		private int m_delay = 0;

		// Token: 0x04004FF9 RID: 20473
		private int m_coldDown = 0;

		// Token: 0x04004FFA RID: 20474
		private int m_currentId;

		// Token: 0x04004FFB RID: 20475
		private int m_added = 0;
	}
}
