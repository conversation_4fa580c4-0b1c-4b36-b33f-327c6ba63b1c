﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E22 RID: 3618
	public class AE1260 : BasePetEffect
	{
		// Token: 0x06007E62 RID: 32354 RVA: 0x002A0320 File Offset: 0x0029E520
		public AE1260(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1260, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E63 RID: 32355 RVA: 0x002A03A0 File Offset: 0x0029E5A0
		public override bool Start(Living living)
		{
			AE1260 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1260) as AE1260;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E64 RID: 32356 RVA: 0x00030E53 File Offset: 0x0002F053
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E65 RID: 32357 RVA: 0x00030E69 File Offset: 0x0002F069
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E66 RID: 32358 RVA: 0x002A0400 File Offset: 0x0029E600
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.AddPetEffect(new CE1260(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004D26 RID: 19750
		private int m_type = 0;

		// Token: 0x04004D27 RID: 19751
		private int m_count = 0;

		// Token: 0x04004D28 RID: 19752
		private int m_probability = 0;

		// Token: 0x04004D29 RID: 19753
		private int m_delay = 0;

		// Token: 0x04004D2A RID: 19754
		private int m_coldDown = 0;

		// Token: 0x04004D2B RID: 19755
		private int m_currentId;

		// Token: 0x04004D2C RID: 19756
		private int m_added = 0;
	}
}
