﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D1E RID: 3358
	public class AddBlood : BasePetEffect
	{
		// Token: 0x060078FB RID: 30971 RVA: 0x00288D28 File Offset: 0x00286F28
		public AddBlood(int count, int skillId, string elementID)
			: base(ePetEffectType.AddBlood, elementID)
		{
			this.m_skillId = skillId;
			if (skillId != 2347)
			{
				if (skillId != 2358)
				{
					if (skillId == 2363)
					{
						this.m_added = 80;
					}
				}
				else
				{
					this.m_added = 50;
				}
			}
			else
			{
				this.m_added = 30;
			}
		}

		// Token: 0x060078FC RID: 30972 RVA: 0x00288D8C File Offset: 0x00286F8C
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.AddBlood) is AddBlood;
			return flag || base.Start(living);
		}

		// Token: 0x060078FD RID: 30973 RVA: 0x0002D19D File Offset: 0x0002B39D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060078FE RID: 30974 RVA: 0x0002D1B3 File Offset: 0x0002B3B3
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060078FF RID: 30975 RVA: 0x00288DC8 File Offset: 0x00286FC8
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_skillId && living.WhiteTiger;
			if (flag)
			{
				living.SyncAtTime = true;
				living.AddBlood(living.MaxBlood * this.m_added / 100);
				living.SyncAtTime = false;
			}
		}

		// Token: 0x040046E2 RID: 18146
		private int m_skillId;

		// Token: 0x040046E3 RID: 18147
		private int m_added;
	}
}
