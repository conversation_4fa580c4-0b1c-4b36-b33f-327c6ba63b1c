﻿using System;
using System.Collections.Generic;
using Game.Logic.Event;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects
{
	// Token: 0x02000D54 RID: 3412
	public class PetNaoThienCungEquipEffect : BasePetEffect
	{
		// Token: 0x06007A33 RID: 31283 RVA: 0x0028DDF0 File Offset: 0x0028BFF0
		public PetNaoThienCungEquipEffect(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PetNaoThienCungEquipEffect, elementID)
		{
			this.int_1 = count;
			this.int_2 = ((probability == -1) ? 10000 : probability);
			this.int_0 = type;
			this.int_3 = delay;
			this.int_4 = skillId;
			if (skillId <= 276)
			{
				if (skillId != 275)
				{
					if (skillId == 276)
					{
						this.int_5 = 150;
					}
				}
				else
				{
					this.int_5 = 100;
				}
			}
			else if (skillId != 375)
			{
				if (skillId == 376)
				{
					this.int_5 = 100;
				}
			}
			else
			{
				this.int_5 = 60;
			}
			this.n = new Random();
		}

		// Token: 0x06007A34 RID: 31284 RVA: 0x0028DEB0 File Offset: 0x0028C0B0
		private void method_0(Living living_0)
		{
			bool flag = living_0.PetEffects.CurrentUseSkill != this.int_4;
			if (!flag)
			{
				List<Player> allEnemyPlayers = living_0.Game.GetAllEnemyPlayers(living_0);
				List<Player> allTeamPlayers = living_0.Game.GetAllTeamPlayers(living_0);
				foreach (Player player in allEnemyPlayers)
				{
					for (int i = 0; i < 5; i++)
					{
						player.SyncAtTime = true;
						int num = this.n.Next(1, this.int_5);
						double num2 = (double)(1 + num / 100);
						player.AddBlood(-(int)(living_0.BaseDamage * num2), 1);
						player.SyncAtTime = false;
						bool flag2 = player.Blood <= 0;
						if (flag2)
						{
							player.Die();
							break;
						}
					}
				}
			}
		}

		// Token: 0x06007A35 RID: 31285 RVA: 0x0002E44F File Offset: 0x0002C64F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.method_0);
		}

		// Token: 0x06007A36 RID: 31286 RVA: 0x0002E465 File Offset: 0x0002C665
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.method_0);
		}

		// Token: 0x06007A37 RID: 31287 RVA: 0x0028DFB8 File Offset: 0x0028C1B8
		public override bool Start(Living living)
		{
			PetNaoThienCungEquipEffect petNaoThienCungEquipEffect = living.PetEffectList.GetOfType(ePetEffectType.PetNaoThienCungEquipEffect) as PetNaoThienCungEquipEffect;
			bool flag = petNaoThienCungEquipEffect != null;
			bool flag2;
			if (flag)
			{
				petNaoThienCungEquipEffect.int_2 = ((this.int_2 > petNaoThienCungEquipEffect.int_2) ? this.int_2 : petNaoThienCungEquipEffect.int_2);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0400478C RID: 18316
		private int int_0;

		// Token: 0x0400478D RID: 18317
		private int int_1;

		// Token: 0x0400478E RID: 18318
		private int int_2;

		// Token: 0x0400478F RID: 18319
		private int int_3;

		// Token: 0x04004790 RID: 18320
		private int int_4;

		// Token: 0x04004791 RID: 18321
		private int int_5;

		// Token: 0x04004792 RID: 18322
		private Random n = null;
	}
}
