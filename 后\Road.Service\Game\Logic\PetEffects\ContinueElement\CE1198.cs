﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E92 RID: 3730
	public class CE1198 : BasePetEffect
	{
		// Token: 0x06008106 RID: 33030 RVA: 0x002AB4E8 File Offset: 0x002A96E8
		public CE1198(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1198, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008107 RID: 33031 RVA: 0x002AB568 File Offset: 0x002A9768
		public override bool Start(Living living)
		{
			CE1198 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1198) as CE1198;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008108 RID: 33032 RVA: 0x002AB5C8 File Offset: 0x002A97C8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = !this.IsTrigger;
			if (flag)
			{
				this.m_added = 30;
				player.PetEffects.CritRate += this.m_added;
				this.IsTrigger = true;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008109 RID: 33033 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600810A RID: 33034 RVA: 0x002AB634 File Offset: 0x002A9834
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600810B RID: 33035 RVA: 0x002AB668 File Offset: 0x002A9868
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.PetEffects.CritRate -= this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x0400503B RID: 20539
		private int m_type = 0;

		// Token: 0x0400503C RID: 20540
		private int m_count = 0;

		// Token: 0x0400503D RID: 20541
		private int m_probability = 0;

		// Token: 0x0400503E RID: 20542
		private int m_delay = 0;

		// Token: 0x0400503F RID: 20543
		private int m_coldDown = 0;

		// Token: 0x04005040 RID: 20544
		private int m_currentId;

		// Token: 0x04005041 RID: 20545
		private int m_added = 0;
	}
}
