﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DDC RID: 3548
	public class AE1154 : BasePetEffect
	{
		// Token: 0x06007CFB RID: 31995 RVA: 0x00299BE4 File Offset: 0x00297DE4
		public AE1154(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1154, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CFC RID: 31996 RVA: 0x00299C64 File Offset: 0x00297E64
		public override bool Start(Living living)
		{
			AE1154 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1154) as AE1154;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CFD RID: 31997 RVA: 0x000300FE File Offset: 0x0002E2FE
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007CFE RID: 31998 RVA: 0x00030114 File Offset: 0x0002E314
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007CFF RID: 31999 RVA: 0x00299CC4 File Offset: 0x00297EC4
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1154(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004B3C RID: 19260
		private int m_type = 0;

		// Token: 0x04004B3D RID: 19261
		private int m_count = 0;

		// Token: 0x04004B3E RID: 19262
		private int m_probability = 0;

		// Token: 0x04004B3F RID: 19263
		private int m_delay = 0;

		// Token: 0x04004B40 RID: 19264
		private int m_coldDown = 0;

		// Token: 0x04004B41 RID: 19265
		private int m_currentId;

		// Token: 0x04004B42 RID: 19266
		private int m_added = 0;
	}
}
