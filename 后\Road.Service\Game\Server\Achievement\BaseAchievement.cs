﻿using System;
using System.Collections.Generic;
using Bussiness.Managers;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C5E RID: 3166
	public class BaseAchievement
	{
		// Token: 0x17001323 RID: 4899
		// (get) Token: 0x06007058 RID: 28760 RVA: 0x0002A3C0 File Offset: 0x000285C0
		public AchievementInfo Info
		{
			get
			{
				return this.m_info;
			}
		}

		// Token: 0x17001324 RID: 4900
		// (get) Token: 0x06007059 RID: 28761 RVA: 0x0002A3C8 File Offset: 0x000285C8
		public AchievementDataInfo Data
		{
			get
			{
				return this.m_data;
			}
		}

		// Token: 0x0600705A RID: 28762 RVA: 0x0002A3D0 File Offset: 0x000285D0
		public BaseAchievement(AchievementInfo info, AchievementDataInfo data, Dictionary<int, AchievementProcessInfo> processInfo)
		{
			this.CreateBaseAchievement(info, data, processInfo);
		}

		// Token: 0x0600705B RID: 28763 RVA: 0x0024F494 File Offset: 0x0024D694
		public void CreateBaseAchievement(AchievementInfo info, AchievementDataInfo data, Dictionary<int, AchievementProcessInfo> processInfo)
		{
			this.m_info = info;
			this.m_data = data;
			this.m_data.AchievementID = this.m_info.ID;
			this.m_list = new List<BaseCondition>();
			foreach (AchievementConditionInfo achievementConditionInfo in AchievementMgr.GetAchievementCondiction(info))
			{
				int num = 0;
				bool flag = processInfo != null && processInfo.ContainsKey(achievementConditionInfo.CondictionType);
				if (flag)
				{
					num = processInfo[achievementConditionInfo.CondictionType].Value;
				}
				BaseCondition baseCondition = BaseCondition.CreateCondition(this, achievementConditionInfo, num);
				bool flag2 = baseCondition != null;
				if (flag2)
				{
					this.m_list.Add(baseCondition);
				}
			}
		}

		// Token: 0x0600705C RID: 28764 RVA: 0x0024F568 File Offset: 0x0024D768
		public void AddToPlayer(GamePlayer player)
		{
			this.m_player = player;
			this.m_data.UserID = player.PlayerCharacter.ID;
			bool flag = !this.m_data.IsComplete;
			if (flag)
			{
				this.AddTrigger(player);
			}
		}

		// Token: 0x0600705D RID: 28765 RVA: 0x0024F5B0 File Offset: 0x0024D7B0
		public void RemoveFromPlayer(GamePlayer player)
		{
			bool isComplete = this.m_data.IsComplete;
			if (isComplete)
			{
				this.RemoveTrigger(player);
			}
			this.m_player = null;
		}

		// Token: 0x0600705E RID: 28766 RVA: 0x0024F5E0 File Offset: 0x0024D7E0
		private void AddTrigger(GamePlayer player)
		{
			foreach (BaseCondition baseCondition in this.m_list)
			{
				baseCondition.AddTrigger(player);
			}
		}

		// Token: 0x0600705F RID: 28767 RVA: 0x0024F63C File Offset: 0x0024D83C
		private void RemoveTrigger(GamePlayer player)
		{
			foreach (BaseCondition baseCondition in this.m_list)
			{
				baseCondition.RemoveTrigger(player);
			}
		}

		// Token: 0x06007060 RID: 28768 RVA: 0x0024F698 File Offset: 0x0024D898
		public void SaveData()
		{
			bool flag = this.m_player == null;
			if (!flag)
			{
				foreach (BaseCondition baseCondition in this.m_list)
				{
					this.m_player.AchievementInventory.UpdateProcess(baseCondition);
				}
			}
		}

		// Token: 0x06007061 RID: 28769 RVA: 0x0024F70C File Offset: 0x0024D90C
		public void Update()
		{
			this.SaveData();
			bool flag = this.m_player != null;
			if (flag)
			{
				this.m_player.AchievementInventory.Update(this);
				bool flag2 = this.CanCompleted(this.m_player);
				if (flag2)
				{
					this.m_player.AchievementInventory.Finish(this);
				}
			}
		}

		// Token: 0x06007062 RID: 28770 RVA: 0x0024F768 File Offset: 0x0024D968
		public bool CanCompleted(GamePlayer player)
		{
			bool isComplete = this.m_data.IsComplete;
			bool flag;
			if (isComplete)
			{
				flag = false;
			}
			else
			{
				foreach (BaseCondition baseCondition in this.m_list)
				{
					bool flag2 = !baseCondition.IsCompleted(player);
					if (flag2)
					{
						return false;
					}
				}
				flag = true;
			}
			return flag;
		}

		// Token: 0x06007063 RID: 28771 RVA: 0x0024F7E8 File Offset: 0x0024D9E8
		public bool Finish(GamePlayer player)
		{
			bool flag = this.CanCompleted(player);
			bool flag3;
			if (flag)
			{
				foreach (BaseCondition baseCondition in this.m_list)
				{
					bool flag2 = !baseCondition.Finish(player);
					if (flag2)
					{
						return false;
					}
				}
				this.m_data.IsComplete = true;
				this.RemoveTrigger(player);
				this.m_data.CompletedDate = DateTime.Now;
				flag3 = true;
			}
			else
			{
				flag3 = false;
			}
			return flag3;
		}

		// Token: 0x04003C58 RID: 15448
		private AchievementInfo m_info;

		// Token: 0x04003C59 RID: 15449
		private AchievementDataInfo m_data;

		// Token: 0x04003C5A RID: 15450
		private List<BaseCondition> m_list;

		// Token: 0x04003C5B RID: 15451
		private GamePlayer m_player;
	}
}
