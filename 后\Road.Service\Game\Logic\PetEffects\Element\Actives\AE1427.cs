﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E2D RID: 3629
	public class AE1427 : BasePetEffect
	{
		// Token: 0x06007EA2 RID: 32418 RVA: 0x002A1490 File Offset: 0x0029F690
		public AE1427(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1427, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007EA3 RID: 32419 RVA: 0x002A1510 File Offset: 0x0029F710
		public override bool Start(Living living)
		{
			AE1427 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1427) as AE1427;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007EA4 RID: 32420 RVA: 0x0003118D File Offset: 0x0002F38D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EA5 RID: 32421 RVA: 0x000311A3 File Offset: 0x0002F3A3
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EA6 RID: 32422 RVA: 0x002A1570 File Offset: 0x0029F770
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.RemovePetMP(10);
				}
			}
		}

		// Token: 0x04004D73 RID: 19827
		private int m_type = 0;

		// Token: 0x04004D74 RID: 19828
		private int m_count = 0;

		// Token: 0x04004D75 RID: 19829
		private int m_probability = 0;

		// Token: 0x04004D76 RID: 19830
		private int m_delay = 0;

		// Token: 0x04004D77 RID: 19831
		private int m_coldDown = 0;

		// Token: 0x04004D78 RID: 19832
		private int m_currentId;

		// Token: 0x04004D79 RID: 19833
		private int m_added = 0;
	}
}
