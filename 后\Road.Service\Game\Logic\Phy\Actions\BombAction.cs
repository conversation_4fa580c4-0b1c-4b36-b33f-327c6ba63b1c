﻿using System;

namespace Game.Logic.Phy.Actions
{
	// Token: 0x02000CE9 RID: 3305
	public class BombAction
	{
		// Token: 0x17001496 RID: 5270
		// (get) Token: 0x060077E8 RID: 30696 RVA: 0x0002C2A8 File Offset: 0x0002A4A8
		public int TimeInt
		{
			get
			{
				return (int)Math.Round((double)(this.Time * 1000f));
			}
		}

		// Token: 0x060077E9 RID: 30697 RVA: 0x0002C2BD File Offset: 0x0002A4BD
		public BombAction(float time, ActionType type, int para1, int para2, int para3, int para4)
		{
			this.Time = time;
			this.Type = (int)type;
			this.Param1 = para1;
			this.Param2 = para2;
			this.Param3 = para3;
			this.Param4 = para4;
		}

		// Token: 0x04004638 RID: 17976
		public float Time;

		// Token: 0x04004639 RID: 17977
		public int Type;

		// Token: 0x0400463A RID: 17978
		public int Param1;

		// Token: 0x0400463B RID: 17979
		public int Param2;

		// Token: 0x0400463C RID: 17980
		public int Param3;

		// Token: 0x0400463D RID: 17981
		public int Param4;
	}
}
