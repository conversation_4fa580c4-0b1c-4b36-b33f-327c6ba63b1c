﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Event;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EF2 RID: 3826
	public class NoHoleEquipEffect : BasePlayerEffect
	{
		// Token: 0x06008358 RID: 33624 RVA: 0x000343DA File Offset: 0x000325DA
		public NoHoleEquipEffect(int count, int probability)
			: base(eEffectType.NoHoleEquipEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x06008359 RID: 33625 RVA: 0x002B37B8 File Offset: 0x002B19B8
		public override bool Start(Living living)
		{
			NoHoleEquipEffect noHoleEquipEffect = living.EffectList.GetOfType(eEffectType.NoHoleEquipEffect) as NoHoleEquipEffect;
			bool flag = noHoleEquipEffect != null;
			bool flag2;
			if (flag)
			{
				noHoleEquipEffect.m_probability = ((this.m_probability > noHoleEquipEffect.m_probability) ? this.m_probability : noHoleEquipEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600835A RID: 33626 RVA: 0x00034402 File Offset: 0x00032602
		protected override void OnAttachedToPlayer(Player player)
		{
			player.CollidByObject += new PlayerEventHandle(this.player_AfterKilledByLiving);
		}

		// Token: 0x0600835B RID: 33627 RVA: 0x00034418 File Offset: 0x00032618
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.CollidByObject -= new PlayerEventHandle(this.player_AfterKilledByLiving);
		}

		// Token: 0x0600835C RID: 33628 RVA: 0x002B3814 File Offset: 0x002B1A14
		private void player_AfterKilledByLiving(Living living)
		{
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				living.DefenceEffectTrigger = true;
				new NoHoleEffect(1).Start(living);
				living.Game.AddAction(new LivingSayAction(living, LanguageMgr.GetTranslation("NoHoleEquipEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x0400520E RID: 21006
		private int m_count = 0;

		// Token: 0x0400520F RID: 21007
		private int m_probability = 0;
	}
}
