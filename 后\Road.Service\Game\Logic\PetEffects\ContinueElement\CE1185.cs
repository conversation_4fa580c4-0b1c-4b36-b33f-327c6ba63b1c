﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E8B RID: 3723
	public class CE1185 : BasePetEffect
	{
		// Token: 0x060080DC RID: 32988 RVA: 0x002AAACC File Offset: 0x002A8CCC
		public CE1185(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1185, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060080DD RID: 32989 RVA: 0x002AAB4C File Offset: 0x002A8D4C
		public override bool Start(Living living)
		{
			CE1185 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1185) as CE1185;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080DE RID: 32990 RVA: 0x002AABAC File Offset: 0x002A8DAC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				player.BaseDamage += (double)this.m_added;
			}
		}

		// Token: 0x060080DF RID: 32991 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginNextTurn(Living living)
		{
		}

		// Token: 0x060080E0 RID: 32992 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginSelfTurn(Living living)
		{
		}

		// Token: 0x060080E1 RID: 32993 RVA: 0x00032631 File Offset: 0x00030831
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
		}

		// Token: 0x0400500A RID: 20490
		private int m_type = 0;

		// Token: 0x0400500B RID: 20491
		private int m_count = 0;

		// Token: 0x0400500C RID: 20492
		private int m_probability = 0;

		// Token: 0x0400500D RID: 20493
		private int m_delay = 0;

		// Token: 0x0400500E RID: 20494
		private int m_coldDown = 0;

		// Token: 0x0400500F RID: 20495
		private int m_currentId;

		// Token: 0x04005010 RID: 20496
		private int m_added = 0;
	}
}
