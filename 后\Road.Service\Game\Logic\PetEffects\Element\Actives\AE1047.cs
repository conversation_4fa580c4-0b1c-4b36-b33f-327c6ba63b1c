﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DA9 RID: 3497
	public class AE1047 : BasePetEffect
	{
		// Token: 0x06007BED RID: 31725 RVA: 0x00295168 File Offset: 0x00293368
		public AE1047(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1047, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BEE RID: 31726 RVA: 0x002951E4 File Offset: 0x002933E4
		public override bool Start(Living living)
		{
			AE1047 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1047) as AE1047;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BEF RID: 31727 RVA: 0x0002F683 File Offset: 0x0002D883
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BF0 RID: 31728 RVA: 0x0002F699 File Offset: 0x0002D899
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BF1 RID: 31729 RVA: 0x00295240 File Offset: 0x00293440
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1047(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x040049D9 RID: 18905
		private int m_type = 0;

		// Token: 0x040049DA RID: 18906
		private int m_count = 0;

		// Token: 0x040049DB RID: 18907
		private int m_probability = 0;

		// Token: 0x040049DC RID: 18908
		private int m_delay = 0;

		// Token: 0x040049DD RID: 18909
		private int m_coldDown = 0;

		// Token: 0x040049DE RID: 18910
		private int m_currentId;

		// Token: 0x040049DF RID: 18911
		private int m_added = 0;
	}
}
