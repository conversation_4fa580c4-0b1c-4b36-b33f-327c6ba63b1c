﻿using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.ServiceModel;

namespace Bussiness.WebLogin
{
	// Token: 0x02000FBC RID: 4028
	[DebuggerStepThrough]
	[GeneratedCode("System.ServiceModel", "4.0.0.0")]
	[EditorBrowsable(EditorBrowsableState.Advanced)]
	[MessageContract(WrapperName = "Get_UserSex", WrapperNamespace = "dandantang", IsWrapped = true)]
	public class Get_UserSexRequest
	{
		// Token: 0x06008A4C RID: 35404 RVA: 0x0000586E File Offset: 0x00003A6E
		public Get_UserSexRequest()
		{
		}

		// Token: 0x06008A4D RID: 35405 RVA: 0x000363A5 File Offset: 0x000345A5
		public Get_UserSexRequest(string username)
		{
			this.username = username;
		}

		// Token: 0x040054A2 RID: 21666
		[MessageBodyMember(Namespace = "dandantang", Order = 0)]
		public string username;
	}
}
