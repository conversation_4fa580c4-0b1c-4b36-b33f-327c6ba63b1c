﻿using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using Game.Manager.AddData;

namespace Game.Manager.Data
{
	// Token: 0x02000C7C RID: 3196
	public partial class EditDrop : Form
	{
		// Token: 0x060070FD RID: 28925 RVA: 0x002521E8 File Offset: 0x002503E8
		public EditDrop()
		{
			this.InitializeComponent();
		}

		// Token: 0x060070FE RID: 28926 RVA: 0x00252240 File Offset: 0x00250440
		private void EditDrop_Load(object sender, EventArgs e)
		{
			string text = string.Empty;
			text = "SELECT * FROM Drop_Item";
			this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
			this.InitDataSet();
		}

		// Token: 0x060070FF RID: 28927 RVA: 0x00252278 File Offset: 0x00250478
		private void InitDataSet()
		{
			this.pageSize = 20;
			this.nMax = this.dtInfo.Rows.Count;
			this.pageCount = this.nMax / this.pageSize;
			bool flag = this.nMax % this.pageSize > 0;
			if (flag)
			{
				this.pageCount++;
			}
			this.pageCurrent = 1;
			this.nCurrent = 0;
			this.LoadData();
		}

		// Token: 0x06007100 RID: 28928 RVA: 0x002522F0 File Offset: 0x002504F0
		private void LoadData()
		{
			DataTable dataTable = this.dtInfo.Clone();
			int num = ((this.pageCurrent != this.pageCount) ? (this.pageSize * this.pageCurrent) : this.nMax);
			int num2 = this.nCurrent;
			this.lblPageCount.Text = this.pageCount.ToString();
			this.txtCurrentPage.Text = Convert.ToString(this.pageCurrent);
			for (int i = num2; i < num; i++)
			{
				dataTable.ImportRow(this.dtInfo.Rows[i]);
				this.nCurrent++;
			}
			this.bdsInfo.DataSource = dataTable;
			this.bdnInfo.BindingSource = this.bdsInfo;
			this.EditDropBox.DataSource = this.bdsInfo;
			string text = string.Empty;
			string empty = string.Empty;
			DataTable dropList = ConnectDataBase.GetDropList();
			for (int j = 0; j < dataTable.Rows.Count; j++)
			{
				text = string.Format("SELECT TemplateID, Name FROM Shop_Goods where TemplateID = {0}", dataTable.Rows[j]["ItemId"]);
				this.EditDropBox.Rows[j].Cells["ItemName"].Value = ((ConnectDataBase.GetDataSet(text).Tables[0].Rows.Count == 0) ? "未知" : ConnectDataBase.GetDataSet(text).Tables[0].Rows[0]["Name"].ToString());
			}
		}

		// Token: 0x06007101 RID: 28929 RVA: 0x002524AC File Offset: 0x002506AC
		private void Name_Button_Click(object sender, EventArgs e)
		{
			string text = string.Empty;
			bool flag = this.Name_Text.Text != "";
			if (flag)
			{
				text = "SELECT * FROM Drop_Item where DropId = " + this.Name_Text.Text;
				this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
				this.InitDataSet();
			}
			else
			{
				text = "SELECT * FROM Drop_Item";
				this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
				this.InitDataSet();
			}
		}

		// Token: 0x06007102 RID: 28930 RVA: 0x00252538 File Offset: 0x00250738
		private void LastPage_Click(object sender, EventArgs e)
		{
			this.pageCurrent--;
			bool flag = this.pageCurrent <= 0;
			if (flag)
			{
				MessageBox.Show("已经是第一页，请点击“下一页”查看！");
			}
			else
			{
				this.nCurrent = this.pageSize * (this.pageCurrent - 1);
				this.LoadData();
			}
		}

		// Token: 0x06007103 RID: 28931 RVA: 0x00252590 File Offset: 0x00250790
		private void NextPage_Click(object sender, EventArgs e)
		{
			this.pageCurrent++;
			bool flag = this.pageCurrent > this.pageCount;
			if (flag)
			{
				MessageBox.Show("已经是最后一页，请点击“上一页”查看！");
			}
			else
			{
				this.nCurrent = this.pageSize * (this.pageCurrent - 1);
				this.LoadData();
			}
		}

		// Token: 0x06007104 RID: 28932 RVA: 0x002525E8 File Offset: 0x002507E8
		private void EditDropBox_CellEndEdit(object sender, DataGridViewCellEventArgs e)
		{
			string dataPropertyName = this.EditDropBox.Columns[e.ColumnIndex].DataPropertyName;
			string text = this.EditDropBox.Rows[e.RowIndex].Cells["Id"].Value.ToString();
			string text2 = this.EditDropBox.CurrentCell.Value.ToString();
			string text3 = string.Concat(new string[] { "Update [dbo].[Drop_Item] set ", dataPropertyName, "='", text2, "'Where Id = ", text });
			ConnectDataBase.GetNonQueryEffectedRow(text3);
		}

		// Token: 0x06007105 RID: 28933 RVA: 0x00252690 File Offset: 0x00250890
		private void AddDrop_Click(object sender, EventArgs e)
		{
			bool flag = this.AddDropForm == null || this.AddDropForm.IsDisposed;
			if (flag)
			{
				this.AddDropForm = new AddDropBox();
				this.AddDropForm.Show();
			}
		}

		// Token: 0x06007106 RID: 28934 RVA: 0x002526D4 File Offset: 0x002508D4
		private void FindButton_Click(object sender, EventArgs e)
		{
			string text = string.Empty;
			bool flag = this.Name_Text.Text != "";
			if (flag)
			{
				text = "SELECT * FROM Drop_Item where DropId like '%" + this.Name_Text.Text + "%'";
				this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
				this.InitDataSet();
			}
			else
			{
				text = "SELECT * FROM Drop_Item";
				this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
				this.InitDataSet();
			}
		}

		// Token: 0x06007107 RID: 28935 RVA: 0x00005683 File Offset: 0x00003883
		private void label1_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x06007108 RID: 28936 RVA: 0x00005683 File Offset: 0x00003883
		private void Name_Text_TextChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x04003C94 RID: 15508
		private int pageSize = 0;

		// Token: 0x04003C95 RID: 15509
		private int nMax = 0;

		// Token: 0x04003C96 RID: 15510
		private int pageCount = 0;

		// Token: 0x04003C97 RID: 15511
		private int pageCurrent = 0;

		// Token: 0x04003C98 RID: 15512
		private int nCurrent = 0;

		// Token: 0x04003C99 RID: 15513
		private DataTable dtInfo = new DataTable();

		// Token: 0x04003C9A RID: 15514
		public AddDropBox AddDropForm = null;
	}
}
