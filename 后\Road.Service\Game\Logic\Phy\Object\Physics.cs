﻿using System;
using System.Drawing;
using Game.Logic.Phy.Maps;

namespace Game.Logic.Phy.Object
{
	// Token: 0x02000CDA RID: 3290
	public class Physics
	{
		// Token: 0x17001445 RID: 5189
		// (get) Token: 0x0600764F RID: 30287 RVA: 0x00272DF0 File Offset: 0x00270FF0
		// (set) Token: 0x06007650 RID: 30288 RVA: 0x0002BC87 File Offset: 0x00029E87
		public int ShootCount
		{
			get
			{
				return this.m_ShootCount;
			}
			set
			{
				this.m_ShootCount = value;
			}
		}

		// Token: 0x17001446 RID: 5190
		// (get) Token: 0x06007651 RID: 30289 RVA: 0x00272E08 File Offset: 0x00271008
		// (set) Token: 0x06007652 RID: 30290 RVA: 0x0002BC91 File Offset: 0x00029E91
		public object Properties1
		{
			get
			{
				return this._prop1;
			}
			set
			{
				this._prop1 = value;
			}
		}

		// Token: 0x17001447 RID: 5191
		// (get) Token: 0x06007653 RID: 30291 RVA: 0x00272E20 File Offset: 0x00271020
		// (set) Token: 0x06007654 RID: 30292 RVA: 0x0002BC9B File Offset: 0x00029E9B
		public object Properties2
		{
			get
			{
				return this._prop2;
			}
			set
			{
				this._prop2 = value;
			}
		}

		// Token: 0x17001448 RID: 5192
		// (get) Token: 0x06007655 RID: 30293 RVA: 0x00272E38 File Offset: 0x00271038
		// (set) Token: 0x06007656 RID: 30294 RVA: 0x0002BCA5 File Offset: 0x00029EA5
		public object Properties3
		{
			get
			{
				return this._prop3;
			}
			set
			{
				this._prop3 = value;
			}
		}

		// Token: 0x17001449 RID: 5193
		// (get) Token: 0x06007657 RID: 30295 RVA: 0x00272E50 File Offset: 0x00271050
		// (set) Token: 0x06007658 RID: 30296 RVA: 0x0002BCAF File Offset: 0x00029EAF
		public Player TriggeredPlayer
		{
			get
			{
				return this.m_TriggeredPlayer;
			}
			set
			{
				this.m_TriggeredPlayer = value;
			}
		}

		// Token: 0x1700144A RID: 5194
		// (get) Token: 0x06007659 RID: 30297 RVA: 0x00272E68 File Offset: 0x00271068
		// (set) Token: 0x0600765A RID: 30298 RVA: 0x0002BCB9 File Offset: 0x00029EB9
		public bool IsTriggered
		{
			get
			{
				return this.m_isTriggered;
			}
			set
			{
				this.m_isTriggered = value;
			}
		}

		// Token: 0x1700144B RID: 5195
		// (get) Token: 0x0600765B RID: 30299 RVA: 0x0002BCC3 File Offset: 0x00029EC3
		public int Id
		{
			get
			{
				return this.m_id;
			}
		}

		// Token: 0x1700144C RID: 5196
		// (get) Token: 0x0600765C RID: 30300 RVA: 0x0002BCCB File Offset: 0x00029ECB
		public Rectangle Bound
		{
			get
			{
				return this.m_rect;
			}
		}

		// Token: 0x1700144D RID: 5197
		// (get) Token: 0x0600765D RID: 30301 RVA: 0x0002BCD3 File Offset: 0x00029ED3
		public Rectangle Bound1
		{
			get
			{
				return this.m_rectBomb;
			}
		}

		// Token: 0x1700144E RID: 5198
		// (get) Token: 0x0600765E RID: 30302 RVA: 0x0002BCDB File Offset: 0x00029EDB
		public bool IsMoving
		{
			get
			{
				return this.m_isMoving;
			}
		}

		// Token: 0x1700144F RID: 5199
		// (get) Token: 0x0600765F RID: 30303 RVA: 0x00272E80 File Offset: 0x00271080
		// (set) Token: 0x06007660 RID: 30304 RVA: 0x0002BCE3 File Offset: 0x00029EE3
		public bool IsLiving
		{
			get
			{
				return this.m_isLiving;
			}
			set
			{
				this.m_isLiving = value;
			}
		}

		// Token: 0x17001450 RID: 5200
		// (get) Token: 0x06007661 RID: 30305 RVA: 0x0002BCED File Offset: 0x00029EED
		public virtual int X
		{
			get
			{
				return this.m_x;
			}
		}

		// Token: 0x17001451 RID: 5201
		// (get) Token: 0x06007662 RID: 30306 RVA: 0x0002BCF5 File Offset: 0x00029EF5
		public virtual int Y
		{
			get
			{
				return this.m_y;
			}
		}

		// Token: 0x17001452 RID: 5202
		// (get) Token: 0x06007663 RID: 30307 RVA: 0x0002BCFD File Offset: 0x00029EFD
		// (set) Token: 0x06007664 RID: 30308 RVA: 0x0002BD05 File Offset: 0x00029F05
		public string ActionMovie { get; set; }

		// Token: 0x06007665 RID: 30309 RVA: 0x00272E98 File Offset: 0x00271098
		public Physics(int id)
		{
			this.m_id = id;
			this.m_rect = new Rectangle(-5, -5, 10, 10);
			this.m_rectBomb = new Rectangle(0, 0, 0, 0);
			this.m_ef = new Point(0, 0);
			this.m_isLiving = true;
		}

		// Token: 0x06007666 RID: 30310 RVA: 0x00272EF8 File Offset: 0x002710F8
		public virtual Point GetCollidePoint()
		{
			return new Point(this.X, this.Y);
		}

		// Token: 0x06007667 RID: 30311 RVA: 0x0002BD0E File Offset: 0x00029F0E
		public void AddExternForce(Point p)
		{
			this.m_ef.X = this.m_ef.X + p.X;
			this.m_ef.Y = this.m_ef.Y + p.Y;
		}

		// Token: 0x06007668 RID: 30312 RVA: 0x0002BD45 File Offset: 0x00029F45
		public void SetRect(int x, int y, int width, int height)
		{
			this.m_rect.X = x;
			this.m_rect.Y = y;
			this.m_rect.Width = width;
			this.m_rect.Height = height;
		}

		// Token: 0x06007669 RID: 30313 RVA: 0x0002BD7D File Offset: 0x00029F7D
		public void SetRectBomb(int x, int y, int width, int height)
		{
			this.m_rectBomb.X = x;
			this.m_rectBomb.Y = y;
			this.m_rectBomb.Width = width;
			this.m_rectBomb.Height = height;
		}

		// Token: 0x0600766A RID: 30314 RVA: 0x0002BDB5 File Offset: 0x00029FB5
		public virtual void SetXY(int x, int y)
		{
			this.m_x = x;
			this.m_y = y;
		}

		// Token: 0x0600766B RID: 30315 RVA: 0x0002BDC6 File Offset: 0x00029FC6
		public void SetXY(Point p)
		{
			this.SetXY(p.X, p.Y);
		}

		// Token: 0x0600766C RID: 30316 RVA: 0x0002BDDE File Offset: 0x00029FDE
		public virtual void SetMap(Map map)
		{
			this.m_map = map;
		}

		// Token: 0x0600766D RID: 30317 RVA: 0x00272F1C File Offset: 0x0027111C
		public virtual void StartMoving()
		{
			bool flag = this.m_map != null;
			if (flag)
			{
				this.m_isMoving = true;
			}
		}

		// Token: 0x0600766E RID: 30318 RVA: 0x0002BDE8 File Offset: 0x00029FE8
		public virtual void StopMoving()
		{
			this.m_isMoving = false;
		}

		// Token: 0x0600766F RID: 30319 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void CollidedByObject(Physics phy)
		{
		}

		// Token: 0x06007670 RID: 30320 RVA: 0x0002BDF2 File Offset: 0x00029FF2
		public virtual void Die()
		{
			this.StopMoving();
			this.m_isLiving = false;
		}

		// Token: 0x06007671 RID: 30321 RVA: 0x00272F40 File Offset: 0x00271140
		public double Distance(int x, int y)
		{
			return Math.Sqrt((double)((this.m_x - x) * (this.m_x - x) + (this.m_y - y) * (this.m_y - y)));
		}

		// Token: 0x06007672 RID: 30322 RVA: 0x00272F7C File Offset: 0x0027117C
		public static int PointToLine(int x1, int y1, int x2, int y2, int px, int py)
		{
			int num = y1 - y2;
			int num2 = x2 - x1;
			int num3 = x1 * y2 - x2 * y1;
			return (int)((double)Math.Abs(num * px + num2 * py + num3) / Math.Sqrt((double)(num * num + num2 * num2)));
		}

		// Token: 0x06007673 RID: 30323 RVA: 0x0002BE03 File Offset: 0x0002A003
		public virtual void Revive()
		{
			this.m_isLiving = true;
		}

		// Token: 0x06007674 RID: 30324 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void PrepareNewTurn()
		{
		}

		// Token: 0x06007675 RID: 30325 RVA: 0x00272FC0 File Offset: 0x002711C0
		public virtual void Dispose()
		{
			bool flag = this.m_map != null;
			if (flag)
			{
				this.m_map.RemovePhysical(this);
			}
		}

		// Token: 0x0400457B RID: 17787
		protected int m_id;

		// Token: 0x0400457C RID: 17788
		protected Map m_map;

		// Token: 0x0400457D RID: 17789
		protected int m_x;

		// Token: 0x0400457E RID: 17790
		protected int m_y;

		// Token: 0x0400457F RID: 17791
		protected Rectangle m_rect;

		// Token: 0x04004580 RID: 17792
		protected Rectangle m_rectBomb;

		// Token: 0x04004581 RID: 17793
		protected bool m_isLiving;

		// Token: 0x04004582 RID: 17794
		protected bool m_isMoving;

		// Token: 0x04004583 RID: 17795
		private object _prop1;

		// Token: 0x04004584 RID: 17796
		protected Point m_ef;

		// Token: 0x04004585 RID: 17797
		private Player m_TriggeredPlayer = null;

		// Token: 0x04004586 RID: 17798
		private bool m_isTriggered = false;

		// Token: 0x04004587 RID: 17799
		private object _prop2;

		// Token: 0x04004588 RID: 17800
		private object _prop3;

		// Token: 0x04004589 RID: 17801
		private int m_ShootCount;
	}
}
