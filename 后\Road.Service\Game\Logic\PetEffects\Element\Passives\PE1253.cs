﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D7B RID: 3451
	public class PE1253 : BasePetEffect
	{
		// Token: 0x06007AFC RID: 31484 RVA: 0x00291294 File Offset: 0x0028F494
		public PE1253(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1253, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AFD RID: 31485 RVA: 0x00291314 File Offset: 0x0028F514
		public override bool Start(Living living)
		{
			PE1253 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1253) as PE1253;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AFE RID: 31486 RVA: 0x0002EC14 File Offset: 0x0002CE14
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
		}

		// Token: 0x06007AFF RID: 31487 RVA: 0x0002EB12 File Offset: 0x0002CD12
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			((Player)living).AddPetMP(2);
		}

		// Token: 0x06007B00 RID: 31488 RVA: 0x0002EC2A File Offset: 0x0002CE2A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x0400489D RID: 18589
		private int m_type = 0;

		// Token: 0x0400489E RID: 18590
		private int m_count = 0;

		// Token: 0x0400489F RID: 18591
		private int m_probability = 0;

		// Token: 0x040048A0 RID: 18592
		private int m_delay = 0;

		// Token: 0x040048A1 RID: 18593
		private int m_coldDown = 0;

		// Token: 0x040048A2 RID: 18594
		private int m_currentId;

		// Token: 0x040048A3 RID: 18595
		private int m_added = 0;
	}
}
