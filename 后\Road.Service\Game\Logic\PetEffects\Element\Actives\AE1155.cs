﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DDD RID: 3549
	public class AE1155 : BasePetEffect
	{
		// Token: 0x06007D00 RID: 32000 RVA: 0x00299D24 File Offset: 0x00297F24
		public AE1155(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1155, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D01 RID: 32001 RVA: 0x00299DA4 File Offset: 0x00297FA4
		public override bool Start(Living living)
		{
			AE1155 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1155) as AE1155;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D02 RID: 32002 RVA: 0x0003012A File Offset: 0x0002E32A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D03 RID: 32003 RVA: 0x00030140 File Offset: 0x0002E340
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D04 RID: 32004 RVA: 0x00299E04 File Offset: 0x00298004
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1155(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004B43 RID: 19267
		private int m_type = 0;

		// Token: 0x04004B44 RID: 19268
		private int m_count = 0;

		// Token: 0x04004B45 RID: 19269
		private int m_probability = 0;

		// Token: 0x04004B46 RID: 19270
		private int m_delay = 0;

		// Token: 0x04004B47 RID: 19271
		private int m_coldDown = 0;

		// Token: 0x04004B48 RID: 19272
		private int m_currentId;

		// Token: 0x04004B49 RID: 19273
		private int m_added = 0;
	}
}
