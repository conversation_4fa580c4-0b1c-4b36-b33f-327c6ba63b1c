﻿using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.Serialization;

namespace Bussiness.CenterService
{
	// Token: 0x02001007 RID: 4103
	[DebuggerStepThrough]
	[GeneratedCode("System.Runtime.Serialization", "4.0.0.0")]
	[DataContract(Name = "ServerData", Namespace = "http://schemas.datacontract.org/2004/07/Center.Server")]
	[Serializable]
	public class ServerData : IExtensibleDataObject, INotifyPropertyChanged
	{
		// Token: 0x170014F1 RID: 5361
		// (get) Token: 0x06008C0E RID: 35854 RVA: 0x002FEF70 File Offset: 0x002FD170
		// (set) Token: 0x06008C0F RID: 35855 RVA: 0x00036C9A File Offset: 0x00034E9A
		[Browsable(false)]
		public ExtensionDataObject ExtensionData
		{
			get
			{
				return this.extensionDataField;
			}
			set
			{
				this.extensionDataField = value;
			}
		}

		// Token: 0x170014F2 RID: 5362
		// (get) Token: 0x06008C10 RID: 35856 RVA: 0x002FEF88 File Offset: 0x002FD188
		// (set) Token: 0x06008C11 RID: 35857 RVA: 0x002FEFA0 File Offset: 0x002FD1A0
		[DataMember]
		public int Id
		{
			get
			{
				return this.IdField;
			}
			set
			{
				bool flag = !this.IdField.Equals(value);
				if (flag)
				{
					this.IdField = value;
					this.RaisePropertyChanged("Id");
				}
			}
		}

		// Token: 0x170014F3 RID: 5363
		// (get) Token: 0x06008C12 RID: 35858 RVA: 0x002FEFD8 File Offset: 0x002FD1D8
		// (set) Token: 0x06008C13 RID: 35859 RVA: 0x002FEFF0 File Offset: 0x002FD1F0
		[DataMember]
		public string Ip
		{
			get
			{
				return this.IpField;
			}
			set
			{
				bool flag = this.IpField != value;
				if (flag)
				{
					this.IpField = value;
					this.RaisePropertyChanged("Ip");
				}
			}
		}

		// Token: 0x170014F4 RID: 5364
		// (get) Token: 0x06008C14 RID: 35860 RVA: 0x002FF024 File Offset: 0x002FD224
		// (set) Token: 0x06008C15 RID: 35861 RVA: 0x002FF03C File Offset: 0x002FD23C
		[DataMember]
		public int LowestLevel
		{
			get
			{
				return this.LowestLevelField;
			}
			set
			{
				bool flag = !this.LowestLevelField.Equals(value);
				if (flag)
				{
					this.LowestLevelField = value;
					this.RaisePropertyChanged("LowestLevel");
				}
			}
		}

		// Token: 0x170014F5 RID: 5365
		// (get) Token: 0x06008C16 RID: 35862 RVA: 0x002FF074 File Offset: 0x002FD274
		// (set) Token: 0x06008C17 RID: 35863 RVA: 0x002FF08C File Offset: 0x002FD28C
		[DataMember]
		public int MustLevel
		{
			get
			{
				return this.MustLevelField;
			}
			set
			{
				bool flag = !this.MustLevelField.Equals(value);
				if (flag)
				{
					this.MustLevelField = value;
					this.RaisePropertyChanged("MustLevel");
				}
			}
		}

		// Token: 0x170014F6 RID: 5366
		// (get) Token: 0x06008C18 RID: 35864 RVA: 0x002FF0C4 File Offset: 0x002FD2C4
		// (set) Token: 0x06008C19 RID: 35865 RVA: 0x002FF0DC File Offset: 0x002FD2DC
		[DataMember]
		public string Name
		{
			get
			{
				return this.NameField;
			}
			set
			{
				bool flag = this.NameField != value;
				if (flag)
				{
					this.NameField = value;
					this.RaisePropertyChanged("Name");
				}
			}
		}

		// Token: 0x170014F7 RID: 5367
		// (get) Token: 0x06008C1A RID: 35866 RVA: 0x002FF110 File Offset: 0x002FD310
		// (set) Token: 0x06008C1B RID: 35867 RVA: 0x002FF128 File Offset: 0x002FD328
		[DataMember]
		public int Online
		{
			get
			{
				return this.OnlineField;
			}
			set
			{
				bool flag = !this.OnlineField.Equals(value);
				if (flag)
				{
					this.OnlineField = value;
					this.RaisePropertyChanged("Online");
				}
			}
		}

		// Token: 0x170014F8 RID: 5368
		// (get) Token: 0x06008C1C RID: 35868 RVA: 0x002FF160 File Offset: 0x002FD360
		// (set) Token: 0x06008C1D RID: 35869 RVA: 0x002FF178 File Offset: 0x002FD378
		[DataMember]
		public int Port
		{
			get
			{
				return this.PortField;
			}
			set
			{
				bool flag = !this.PortField.Equals(value);
				if (flag)
				{
					this.PortField = value;
					this.RaisePropertyChanged("Port");
				}
			}
		}

		// Token: 0x170014F9 RID: 5369
		// (get) Token: 0x06008C1E RID: 35870 RVA: 0x002FF1B0 File Offset: 0x002FD3B0
		// (set) Token: 0x06008C1F RID: 35871 RVA: 0x002FF1C8 File Offset: 0x002FD3C8
		[DataMember]
		public int State
		{
			get
			{
				return this.StateField;
			}
			set
			{
				bool flag = !this.StateField.Equals(value);
				if (flag)
				{
					this.StateField = value;
					this.RaisePropertyChanged("State");
				}
			}
		}

		// Token: 0x140000E2 RID: 226
		// (add) Token: 0x06008C20 RID: 35872 RVA: 0x002FF200 File Offset: 0x002FD400
		// (remove) Token: 0x06008C21 RID: 35873 RVA: 0x002FF238 File Offset: 0x002FD438
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PropertyChangedEventHandler PropertyChanged;

		// Token: 0x06008C22 RID: 35874 RVA: 0x00036CA4 File Offset: 0x00034EA4
		protected void RaisePropertyChanged(string propertyName)
		{
			PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
			if (propertyChanged != null)
			{
				propertyChanged(this, new PropertyChangedEventArgs(propertyName));
			}
		}

		// Token: 0x0400557A RID: 21882
		[NonSerialized]
		private ExtensionDataObject extensionDataField;

		// Token: 0x0400557B RID: 21883
		[OptionalField]
		private int IdField;

		// Token: 0x0400557C RID: 21884
		[OptionalField]
		private string IpField;

		// Token: 0x0400557D RID: 21885
		[OptionalField]
		private int LowestLevelField;

		// Token: 0x0400557E RID: 21886
		[OptionalField]
		private int MustLevelField;

		// Token: 0x0400557F RID: 21887
		[OptionalField]
		private string NameField;

		// Token: 0x04005580 RID: 21888
		[OptionalField]
		private int OnlineField;

		// Token: 0x04005581 RID: 21889
		[OptionalField]
		private int PortField;

		// Token: 0x04005582 RID: 21890
		[OptionalField]
		private int StateField;
	}
}
