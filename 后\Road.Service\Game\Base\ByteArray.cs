﻿using System;
using System.IO;

namespace Game.Base
{
	// Token: 0x02000F71 RID: 3953
	public class ByteArray
	{
		// Token: 0x170014BE RID: 5310
		// (get) Token: 0x0600857D RID: 34173 RVA: 0x000359B2 File Offset: 0x00033BB2
		public uint Length
		{
			get
			{
				return (uint)this._memoryStream.Length;
			}
		}

		// Token: 0x170014BF RID: 5311
		// (get) Token: 0x0600857E RID: 34174 RVA: 0x002BB668 File Offset: 0x002B9868
		// (set) Token: 0x0600857F RID: 34175 RVA: 0x000359C0 File Offset: 0x00033BC0
		public uint Position
		{
			get
			{
				return (uint)this._memoryStream.Position;
			}
			set
			{
				this._memoryStream.Position = (long)((ulong)value);
			}
		}

		// Token: 0x170014C0 RID: 5312
		// (get) Token: 0x06008580 RID: 34176 RVA: 0x000359D1 File Offset: 0x00033BD1
		public uint BytesAvailable
		{
			get
			{
				return this.Length - this.Position;
			}
		}

		// Token: 0x06008581 RID: 34177 RVA: 0x000359E0 File Offset: 0x00033BE0
		public ByteArray()
		{
			this._memoryStream = new MemoryStream();
			this._dataOutput = new DataOutput(this._memoryStream);
			this._dataInput = new DataInput(this._memoryStream);
			this._objectEncoding = ObjectEncoding.AMF3;
		}

		// Token: 0x06008582 RID: 34178 RVA: 0x002BB688 File Offset: 0x002B9888
		public ByteArray(byte[] buffer)
		{
			this._memoryStream = new MemoryStream();
			this._memoryStream.Write(buffer, 0, buffer.Length);
			this._memoryStream.Position = 0L;
			this._dataOutput = new DataOutput(this._memoryStream);
			this._dataInput = new DataInput(this._memoryStream);
			this._objectEncoding = ObjectEncoding.AMF3;
		}

		// Token: 0x06008583 RID: 34179 RVA: 0x002BB6F0 File Offset: 0x002B98F0
		public byte[] ToArray()
		{
			return this._memoryStream.ToArray();
		}

		// Token: 0x06008584 RID: 34180 RVA: 0x002BB710 File Offset: 0x002B9910
		public bool ReadBoolean()
		{
			return this._dataInput.ReadBoolean();
		}

		// Token: 0x06008585 RID: 34181 RVA: 0x002BB730 File Offset: 0x002B9930
		public byte ReadByte()
		{
			return this._dataInput.ReadByte();
		}

		// Token: 0x06008586 RID: 34182 RVA: 0x002BB750 File Offset: 0x002B9950
		public void ReadBytes(ByteArray bytes, uint offset, uint length)
		{
			uint position = bytes.Position;
			int num = (int)((length != 0U) ? length : this.BytesAvailable);
			for (int i = 0; i < num; i++)
			{
				bytes._memoryStream.Position = (long)i + (long)((ulong)offset);
				bytes._memoryStream.WriteByte(this.ReadByte());
			}
			bytes.Position = position;
		}

		// Token: 0x06008587 RID: 34183 RVA: 0x002BB7B0 File Offset: 0x002B99B0
		public int ReadInt()
		{
			return this._dataInput.ReadInt();
		}

		// Token: 0x06008588 RID: 34184 RVA: 0x002BB7D0 File Offset: 0x002B99D0
		public short ReadShort()
		{
			return this._dataInput.ReadShort();
		}

		// Token: 0x06008589 RID: 34185 RVA: 0x002BB7F0 File Offset: 0x002B99F0
		public string ReadUTF()
		{
			return this._dataInput.ReadString();
		}

		// Token: 0x04005361 RID: 21345
		private MemoryStream _memoryStream;

		// Token: 0x04005362 RID: 21346
		private DataOutput _dataOutput;

		// Token: 0x04005363 RID: 21347
		private DataInput _dataInput;

		// Token: 0x04005364 RID: 21348
		private ObjectEncoding _objectEncoding;
	}
}
