﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E1B RID: 3611
	public class AE1250 : BasePetEffect
	{
		// Token: 0x06007E3C RID: 32316 RVA: 0x0029F888 File Offset: 0x0029DA88
		public AE1250(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1250, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E3D RID: 32317 RVA: 0x0029F908 File Offset: 0x0029DB08
		public override bool Start(Living living)
		{
			AE1250 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1250) as AE1250;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E3E RID: 32318 RVA: 0x00030CAD File Offset: 0x0002EEAD
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E3F RID: 32319 RVA: 0x00030CC3 File Offset: 0x0002EEC3
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E40 RID: 32320 RVA: 0x0029F968 File Offset: 0x0029DB68
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1250(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004CF5 RID: 19701
		private int m_type = 0;

		// Token: 0x04004CF6 RID: 19702
		private int m_count = 0;

		// Token: 0x04004CF7 RID: 19703
		private int m_probability = 0;

		// Token: 0x04004CF8 RID: 19704
		private int m_delay = 0;

		// Token: 0x04004CF9 RID: 19705
		private int m_coldDown = 0;

		// Token: 0x04004CFA RID: 19706
		private int m_currentId;

		// Token: 0x04004CFB RID: 19707
		private int m_added = 0;
	}
}
