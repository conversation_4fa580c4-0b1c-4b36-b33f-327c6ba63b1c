﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E9B RID: 3739
	public class CE1208 : BasePetEffect
	{
		// Token: 0x06008143 RID: 33091 RVA: 0x002AC51C File Offset: 0x002AA71C
		public CE1208(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1208, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008144 RID: 33092 RVA: 0x002AC59C File Offset: 0x002AA79C
		public override bool Start(Living living)
		{
			CE1208 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1208) as CE1208;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008145 RID: 33093 RVA: 0x0003297A File Offset: 0x00030B7A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008146 RID: 33094 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008147 RID: 33095 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_BeginSelfTurn(Living living)
		{
			this.Stop();
		}

		// Token: 0x06008148 RID: 33096 RVA: 0x000329A3 File Offset: 0x00030BA3
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x0400507A RID: 20602
		private int m_type = 0;

		// Token: 0x0400507B RID: 20603
		private int m_count = 0;

		// Token: 0x0400507C RID: 20604
		private int m_probability = 0;

		// Token: 0x0400507D RID: 20605
		private int m_delay = 0;

		// Token: 0x0400507E RID: 20606
		private int m_coldDown = 0;

		// Token: 0x0400507F RID: 20607
		private int m_currentId;

		// Token: 0x04005080 RID: 20608
		private int m_added = 0;
	}
}
