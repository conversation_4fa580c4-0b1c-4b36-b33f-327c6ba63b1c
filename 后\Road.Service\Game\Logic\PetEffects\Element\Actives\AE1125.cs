﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DD0 RID: 3536
	public class AE1125 : BasePetEffect
	{
		// Token: 0x06007CB5 RID: 31925 RVA: 0x00298AB4 File Offset: 0x00296CB4
		public AE1125(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1125, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CB6 RID: 31926 RVA: 0x00298B34 File Offset: 0x00296D34
		public override bool Start(Living living)
		{
			AE1125 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1125) as AE1125;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CB7 RID: 31927 RVA: 0x0002FD85 File Offset: 0x0002DF85
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007CB8 RID: 31928 RVA: 0x0002FDAE File Offset: 0x0002DFAE
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007CB9 RID: 31929 RVA: 0x00298B94 File Offset: 0x00296D94
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 100;
				player.CurrentDamagePlus += (float)(this.m_added / 100);
			}
		}

		// Token: 0x06007CBA RID: 31930 RVA: 0x00298BDC File Offset: 0x00296DDC
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_added / 100);
				this.m_added = 0;
			}
		}

		// Token: 0x04004AE8 RID: 19176
		private int m_type = 0;

		// Token: 0x04004AE9 RID: 19177
		private int m_count = 0;

		// Token: 0x04004AEA RID: 19178
		private int m_probability = 0;

		// Token: 0x04004AEB RID: 19179
		private int m_delay = 0;

		// Token: 0x04004AEC RID: 19180
		private int m_coldDown = 0;

		// Token: 0x04004AED RID: 19181
		private int m_currentId;

		// Token: 0x04004AEE RID: 19182
		private int m_added = 0;
	}
}
