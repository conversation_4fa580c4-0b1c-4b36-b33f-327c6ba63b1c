﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DF6 RID: 3574
	public class AE1196 : BasePetEffect
	{
		// Token: 0x06007D7E RID: 32126 RVA: 0x0029C3E4 File Offset: 0x0029A5E4
		public AE1196(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1196, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D7F RID: 32127 RVA: 0x0029C464 File Offset: 0x0029A664
		public override bool Start(Living living)
		{
			AE1196 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1196) as AE1196;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D80 RID: 32128 RVA: 0x00030589 File Offset: 0x0002E789
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D81 RID: 32129 RVA: 0x0003059F File Offset: 0x0002E79F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D82 RID: 32130 RVA: 0x0029C4C4 File Offset: 0x0029A6C4
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1196(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004BF2 RID: 19442
		private int m_type = 0;

		// Token: 0x04004BF3 RID: 19443
		private int m_count = 0;

		// Token: 0x04004BF4 RID: 19444
		private int m_probability = 0;

		// Token: 0x04004BF5 RID: 19445
		private int m_delay = 0;

		// Token: 0x04004BF6 RID: 19446
		private int m_coldDown = 0;

		// Token: 0x04004BF7 RID: 19447
		private int m_currentId;

		// Token: 0x04004BF8 RID: 19448
		private int m_added = 0;
	}
}
