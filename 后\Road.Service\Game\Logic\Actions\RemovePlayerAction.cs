﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F68 RID: 3944
	public class RemovePlayerAction : IAction
	{
		// Token: 0x0600852C RID: 34092 RVA: 0x00035692 File Offset: 0x00033892
		public RemovePlayerAction(Player player)
		{
			this.m_player = player;
			this.m_isFinished = false;
		}

		// Token: 0x0600852D RID: 34093 RVA: 0x000356AA File Offset: 0x000338AA
		public void Execute(BaseGame game, long tick)
		{
			this.m_player.DeadLink();
			this.m_isFinished = true;
			game.SendPlayerRemove(this.m_player);
		}

		// Token: 0x0600852E RID: 34094 RVA: 0x002BA650 File Offset: 0x002B8850
		public bool IsFinished(BaseGame game, long tick)
		{
			return this.m_isFinished;
		}

		// Token: 0x0400533A RID: 21306
		private bool m_isFinished;

		// Token: 0x0400533B RID: 21307
		private Player m_player;
	}
}
