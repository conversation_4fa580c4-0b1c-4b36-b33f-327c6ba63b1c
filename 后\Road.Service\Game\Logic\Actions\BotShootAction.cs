﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F44 RID: 3908
	public class BotShootAction : BaseAction
	{
		// Token: 0x060084DB RID: 34011 RVA: 0x002B8CB4 File Offset: 0x002B6EB4
		public BotShootAction(Player living, int x, int y, int force, int angle, int bombCount, int minTime, int maxTime, float time, int delay)
			: base(delay, 1000)
		{
			this.m_player = living;
			this.m_tx = x;
			this.m_ty = y;
			this.m_force = force;
			this.m_angle = angle;
			this.m_bombCount = bombCount;
			this.m_bombId = living.CurrentBall.ID;
			this.m_minTime = minTime;
			this.m_maxTime = maxTime;
			this.m_Time = time;
		}

		// Token: 0x060084DC RID: 34012 RVA: 0x002B8D28 File Offset: 0x002B6F28
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			this.m_player.GetShootForceAndAngle(ref this.m_tx, ref this.m_ty, this.m_bombId, this.m_minTime, this.m_maxTime, this.m_bombCount, this.m_Time, ref this.m_force, ref this.m_angle);
			this.m_player.ShootImp(this.m_bombId, this.m_tx, this.m_ty, this.m_force, this.m_angle, this.m_bombCount, 0);
			base.Finish(tick);
		}

		// Token: 0x040052B5 RID: 21173
		private Living m_player;

		// Token: 0x040052B6 RID: 21174
		private int m_tx;

		// Token: 0x040052B7 RID: 21175
		private int m_ty;

		// Token: 0x040052B8 RID: 21176
		private int m_bombId;

		// Token: 0x040052B9 RID: 21177
		private int m_force;

		// Token: 0x040052BA RID: 21178
		private int m_angle;

		// Token: 0x040052BB RID: 21179
		private int m_bombCount;

		// Token: 0x040052BC RID: 21180
		private int m_minTime;

		// Token: 0x040052BD RID: 21181
		private int m_maxTime;

		// Token: 0x040052BE RID: 21182
		private float m_Time;
	}
}
