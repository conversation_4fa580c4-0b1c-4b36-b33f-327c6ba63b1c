﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EF3 RID: 3827
	public class PowerNextTurn : AbstractEffect
	{
		// Token: 0x0600835D RID: 33629 RVA: 0x0003442E File Offset: 0x0003262E
		public PowerNextTurn(int count, int damage)
			: base(eEffectType.PowerNextTurn)
		{
			this.m_count = count;
			this.m_damage = damage;
		}

		// Token: 0x0600835E RID: 33630 RVA: 0x002B3884 File Offset: 0x002B1A84
		public override bool Start(Living living)
		{
			PowerNextTurn powerNextTurn = living.EffectList.GetOfType(eEffectType.PowerNextTurn) as PowerNextTurn;
			bool flag = powerNextTurn != null;
			bool flag2;
			if (flag)
			{
				powerNextTurn.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600835F RID: 33631 RVA: 0x00034448 File Offset: 0x00032648
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginFitting;
		}

		// Token: 0x06008360 RID: 33632 RVA: 0x0003445E File Offset: 0x0003265E
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginFitting;
			living.SetNiutou(false);
		}

		// Token: 0x06008361 RID: 33633 RVA: 0x002B38CC File Offset: 0x002B1ACC
		private void player_BeginFitting(Living living)
		{
			this.m_count--;
			bool flag = this.m_count == 0;
			if (flag)
			{
				living.CurrentDamagePlus += (float)(this.m_damage / 100);
				living.SetNiutou(true);
			}
			else
			{
				bool flag2 = this.m_count < 0;
				if (flag2)
				{
					this.Stop();
				}
			}
		}

		// Token: 0x04005210 RID: 21008
		private int m_count;

		// Token: 0x04005211 RID: 21009
		private int m_damage;
	}
}
