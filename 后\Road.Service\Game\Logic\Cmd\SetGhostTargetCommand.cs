﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F12 RID: 3858
	[GameCommand(54, "设置鬼魂目标")]
	public class SetGhostTargetCommand : ICommandHandler
	{
		// Token: 0x060083BD RID: 33725 RVA: 0x002B5734 File Offset: 0x002B3934
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool flag = !player.IsLiving;
			if (flag)
			{
				player.TargetPoint.X = packet.ReadInt();
				player.TargetPoint.Y = packet.ReadInt();
			}
		}
	}
}
