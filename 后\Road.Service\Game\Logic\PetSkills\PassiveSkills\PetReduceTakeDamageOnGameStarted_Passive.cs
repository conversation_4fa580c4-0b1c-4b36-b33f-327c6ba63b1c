﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CF7 RID: 3319
	public class PetReduceTakeDamageOnGameStarted_Passive : BasePetEffect
	{
		// Token: 0x06007830 RID: 30768 RVA: 0x0002C77D File Offset: 0x0002A97D
		public PetReduceTakeDamageOnGameStarted_Passive(int count, string elementID)
			: base(ePetEffectType.PetReduceTakeDamageOnGameStarted_Passive, elementID)
		{
			this.m_count = count;
		}

		// Token: 0x06007831 RID: 30769 RVA: 0x002847AC File Offset: 0x002829AC
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetReduceTakeDamageOnGameStarted_Passive) is PetReduceTakeDamageOnGameStarted_Passive;
			return flag || base.Start(living);
		}

		// Token: 0x06007832 RID: 30770 RVA: 0x0002C7A2 File Offset: 0x0002A9A2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.GameStarted += this.player_GameStarted;
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x06007833 RID: 30771 RVA: 0x0002C7CB File Offset: 0x0002A9CB
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.GameStarted -= this.player_GameStarted;
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x06007834 RID: 30772 RVA: 0x002847E8 File Offset: 0x002829E8
		private void player_BeginSelfTurn(Living living)
		{
			this.m_coldDown++;
			bool flag = this.m_coldDown >= 8;
			if (flag)
			{
				this.m_coldDown = 0;
				living.Game.sendShowPicSkil(living, base.Info, true);
				new PetReduceTakeDamageEquip(this.m_count, base.Info.ID.ToString()).Start(living);
			}
		}

		// Token: 0x06007835 RID: 30773 RVA: 0x00284858 File Offset: 0x00282A58
		private void player_GameStarted(Living living)
		{
			living.Game.sendShowPicSkil(living, base.Info, true);
			new PetReduceTakeDamageEquip(this.m_count, base.Info.ID.ToString()).Start(living);
		}

		// Token: 0x04004673 RID: 18035
		private int m_count = 0;

		// Token: 0x04004674 RID: 18036
		private int m_coldDown = 0;
	}
}
