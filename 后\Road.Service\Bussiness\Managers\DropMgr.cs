﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using Bussiness.Protocol;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FCD RID: 4045
	public class DropMgr
	{
		// Token: 0x06008A96 RID: 35478 RVA: 0x002F7840 File Offset: 0x002F5A40
		public static bool Init()
		{
			return DropMgr.ReLoad();
		}

		// Token: 0x06008A97 RID: 35479 RVA: 0x002F7858 File Offset: 0x002F5A58
		public static bool ReLoad()
		{
			try
			{
				List<DropCondiction> list = DropMgr.LoadDropConditionDb();
				Interlocked.Exchange<List<DropCondiction>>(ref DropMgr.m_dropcondiction, list);
				Dictionary<int, List<DropItem>> dictionary = DropMgr.LoadDropItemDb();
				Interlocked.Exchange<Dictionary<int, List<DropItem>>>(ref DropMgr.m_dropitem, dictionary);
				return true;
			}
			catch (Exception ex)
			{
				DropMgr.log.Error("DropMgr", ex);
			}
			return false;
		}

		// Token: 0x06008A98 RID: 35480 RVA: 0x002F78BC File Offset: 0x002F5ABC
		public static List<DropCondiction> LoadDropConditionDb()
		{
			List<DropCondiction> list;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				DropCondiction[] allDropCondictions = produceBussiness.GetAllDropCondictions();
				list = ((allDropCondictions != null) ? allDropCondictions.ToList<DropCondiction>() : null);
			}
			return list;
		}

		// Token: 0x06008A99 RID: 35481 RVA: 0x002F7904 File Offset: 0x002F5B04
		public static Dictionary<int, List<DropItem>> LoadDropItemDb()
		{
			Dictionary<int, List<DropItem>> dictionary = new Dictionary<int, List<DropItem>>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				DropItem[] allDropItems = produceBussiness.GetAllDropItems();
				using (List<DropCondiction>.Enumerator enumerator = DropMgr.m_dropcondiction.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						DropCondiction info = enumerator.Current;
						IEnumerable<DropItem> enumerable = allDropItems.Where((DropItem s) => s.DropId == info.DropId);
						dictionary.Add(info.DropId, enumerable.ToList<DropItem>());
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008A9A RID: 35482 RVA: 0x002F79C8 File Offset: 0x002F5BC8
		public static int FindCondiction(eDropType type, string para1, string para2)
		{
			string text = "," + para1 + ",";
			string text2 = "," + para2 + ",";
			foreach (DropCondiction dropCondiction in DropMgr.m_dropcondiction)
			{
				bool flag = dropCondiction.CondictionType == (int)type && dropCondiction.Para1.IndexOf(text) != -1 && dropCondiction.Para2.IndexOf(text2) != -1;
				if (flag)
				{
					return dropCondiction.DropId;
				}
			}
			return 0;
		}

		// Token: 0x06008A9B RID: 35483 RVA: 0x002F7A80 File Offset: 0x002F5C80
		public static List<DropItem> FindDropItem(int dropId)
		{
			bool flag = DropMgr.m_dropitem.ContainsKey(dropId);
			List<DropItem> list;
			if (flag)
			{
				list = DropMgr.m_dropitem[dropId];
			}
			else
			{
				list = null;
			}
			return list;
		}

		// Token: 0x040054EC RID: 21740
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040054ED RID: 21741
		private static string[] m_DropTypes = Enum.GetNames(typeof(eDropType));

		// Token: 0x040054EE RID: 21742
		private static List<DropCondiction> m_dropcondiction = new List<DropCondiction>();

		// Token: 0x040054EF RID: 21743
		private static Dictionary<int, List<DropItem>> m_dropitem = new Dictionary<int, List<DropItem>>();
	}
}
