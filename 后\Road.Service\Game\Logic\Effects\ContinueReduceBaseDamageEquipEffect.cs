﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EE3 RID: 3811
	public class ContinueReduceBaseDamageEquipEffect : BasePlayerEffect
	{
		// Token: 0x060082FD RID: 33533 RVA: 0x00033DDF File Offset: 0x00031FDF
		public ContinueReduceBaseDamageEquipEffect(int count, int probability)
			: base(eEffectType.ContinueReduceBaseDamageEquipEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x060082FE RID: 33534 RVA: 0x002B2538 File Offset: 0x002B0738
		public override bool Start(Living living)
		{
			ContinueReduceBaseDamageEquipEffect continueReduceBaseDamageEquipEffect = living.EffectList.GetOfType(eEffectType.ContinueReduceBaseDamageEquipEffect) as ContinueReduceBaseDamageEquipEffect;
			bool flag = continueReduceBaseDamageEquipEffect != null;
			bool flag2;
			if (flag)
			{
				continueReduceBaseDamageEquipEffect.m_probability = ((this.m_probability > continueReduceBaseDamageEquipEffect.m_probability) ? this.m_probability : continueReduceBaseDamageEquipEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082FF RID: 33535 RVA: 0x00033E07 File Offset: 0x00032007
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.ChangeProperty;
			player.AfterKillingLiving += this.player_AfterKillingLiving;
		}

		// Token: 0x06008300 RID: 33536 RVA: 0x002B2594 File Offset: 0x002B0794
		private void player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = this.IsTrigger && target is Player;
			if (flag)
			{
				target.AddEffect(new ContinueReduceBaseDamageEffect(2, this.m_count), 0);
			}
		}

		// Token: 0x06008301 RID: 33537 RVA: 0x00033E30 File Offset: 0x00032030
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.ChangeProperty;
			player.AfterKillingLiving -= this.player_AfterKillingLiving;
		}

		// Token: 0x06008302 RID: 33538 RVA: 0x002B25D0 File Offset: 0x002B07D0
		private void ChangeProperty(Player player, int ball)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				this.IsTrigger = true;
				player.AttackEffectTrigger = true;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("ContinueReduceBaseDamageEquipEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051EF RID: 20975
		private int m_count = 0;

		// Token: 0x040051F0 RID: 20976
		private int m_probability = 0;
	}
}
