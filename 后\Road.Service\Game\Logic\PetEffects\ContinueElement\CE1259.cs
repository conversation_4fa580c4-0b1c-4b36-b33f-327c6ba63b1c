﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EB8 RID: 3768
	public class CE1259 : BasePetEffect
	{
		// Token: 0x06008200 RID: 33280 RVA: 0x002AF040 File Offset: 0x002AD240
		public CE1259(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1259, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008201 RID: 33281 RVA: 0x002AF0C0 File Offset: 0x002AD2C0
		public override bool Start(Living living)
		{
			CE1259 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1259) as CE1259;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008202 RID: 33282 RVA: 0x0003316E File Offset: 0x0003136E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008203 RID: 33283 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008204 RID: 33284 RVA: 0x002AF120 File Offset: 0x002AD320
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				living.SyncAtTime = true;
				living.AddBlood(800);
				living.SyncAtTime = false;
			}
		}

		// Token: 0x06008205 RID: 33285 RVA: 0x00033197 File Offset: 0x00031397
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005145 RID: 20805
		private int m_type = 0;

		// Token: 0x04005146 RID: 20806
		private int m_count = 0;

		// Token: 0x04005147 RID: 20807
		private int m_probability = 0;

		// Token: 0x04005148 RID: 20808
		private int m_delay = 0;

		// Token: 0x04005149 RID: 20809
		private int m_coldDown = 0;

		// Token: 0x0400514A RID: 20810
		private int m_currentId;

		// Token: 0x0400514B RID: 20811
		private int m_added = 0;
	}
}
