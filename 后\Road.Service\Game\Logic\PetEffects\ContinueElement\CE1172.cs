﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E7E RID: 3710
	public class CE1172 : BasePetEffect
	{
		// Token: 0x06008087 RID: 32903 RVA: 0x002A9664 File Offset: 0x002A7864
		public CE1172(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1172, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008088 RID: 32904 RVA: 0x002A96E4 File Offset: 0x002A78E4
		public override bool Start(Living living)
		{
			CE1172 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1172) as CE1172;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008089 RID: 32905 RVA: 0x00032352 File Offset: 0x00030552
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600808A RID: 32906 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600808B RID: 32907 RVA: 0x002A9744 File Offset: 0x002A7944
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = target != living && this.rand.Next(100) < 50;
			if (flag)
			{
				this.m_added = 500;
				living.SyncAtTime = true;
				living.AddBlood(this.m_added);
				living.SyncAtTime = false;
			}
		}

		// Token: 0x0600808C RID: 32908 RVA: 0x002A9798 File Offset: 0x002A7998
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600808D RID: 32909 RVA: 0x0003238E File Offset: 0x0003058E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x04004FAD RID: 20397
		private int m_type = 0;

		// Token: 0x04004FAE RID: 20398
		private int m_count = 0;

		// Token: 0x04004FAF RID: 20399
		private int m_probability = 0;

		// Token: 0x04004FB0 RID: 20400
		private int m_delay = 0;

		// Token: 0x04004FB1 RID: 20401
		private int m_coldDown = 0;

		// Token: 0x04004FB2 RID: 20402
		private int m_currentId;

		// Token: 0x04004FB3 RID: 20403
		private int m_added = 0;
	}
}
