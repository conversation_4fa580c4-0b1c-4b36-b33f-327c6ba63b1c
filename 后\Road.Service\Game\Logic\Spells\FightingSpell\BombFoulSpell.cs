﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CC3 RID: 3267
	[SpellAttibute(81)]
	public class BombFoulSpell : ISpellHandler
	{
		// Token: 0x06007508 RID: 29960 RVA: 0x0026E6E0 File Offset: 0x0026C8E0
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.BombFoul = true;
			}
			else
			{
				bool flag = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
				if (flag)
				{
					game.CurrentLiving.BombFoul = true;
				}
			}
		}
	}
}
