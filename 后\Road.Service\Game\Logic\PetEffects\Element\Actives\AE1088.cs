﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DBD RID: 3517
	public class AE1088 : BasePetEffect
	{
		// Token: 0x06007C52 RID: 31826 RVA: 0x00296EA0 File Offset: 0x002950A0
		public AE1088(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1088, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C53 RID: 31827 RVA: 0x00296F20 File Offset: 0x00295120
		public override bool Start(Living living)
		{
			AE1088 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1088) as AE1088;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C54 RID: 31828 RVA: 0x0002F998 File Offset: 0x0002DB98
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C55 RID: 31829 RVA: 0x00296F80 File Offset: 0x00295180
		protected override void OnPausedOnPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Defence += (double)this.m_added;
			this.IsTrigger = false;
		}

		// Token: 0x06007C56 RID: 31830 RVA: 0x00296FD0 File Offset: 0x002951D0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && !this.IsTrigger;
			if (flag)
			{
				this.IsTrigger = true;
				this.m_added = 800;
				player.Defence -= (double)this.m_added;
				player.Game.SendPetBuff(player, base.ElementInfo, true);
			}
		}

		// Token: 0x04004A65 RID: 19045
		private int m_type = 0;

		// Token: 0x04004A66 RID: 19046
		private int m_count = 0;

		// Token: 0x04004A67 RID: 19047
		private int m_probability = 0;

		// Token: 0x04004A68 RID: 19048
		private int m_delay = 0;

		// Token: 0x04004A69 RID: 19049
		private int m_coldDown = 0;

		// Token: 0x04004A6A RID: 19050
		private int m_currentId;

		// Token: 0x04004A6B RID: 19051
		private int m_added = 0;
	}
}
