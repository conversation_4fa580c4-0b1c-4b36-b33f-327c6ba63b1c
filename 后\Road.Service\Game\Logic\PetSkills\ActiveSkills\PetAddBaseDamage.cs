﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D25 RID: 3365
	public class PetAddBaseDamage : BasePetEffect
	{
		// Token: 0x06007921 RID: 31009 RVA: 0x0002D43A File Offset: 0x0002B63A
		public PetAddBaseDamage(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetAddBaseDamage, elementID)
		{
			this.m_count = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x06007922 RID: 31010 RVA: 0x00289414 File Offset: 0x00287614
		public override bool Start(Living living)
		{
			PetAddBaseDamage petAddBaseDamage = living.PetEffectList.GetOfType(ePetEffectType.PetAddBaseDamage) as PetAddBaseDamage;
			bool flag = petAddBaseDamage != null;
			bool flag2;
			if (flag)
			{
				petAddBaseDamage.m_probability = ((this.m_probability > petAddBaseDamage.m_probability) ? this.m_probability : petAddBaseDamage.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007923 RID: 31011 RVA: 0x0002D46B File Offset: 0x0002B66B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x06007924 RID: 31012 RVA: 0x0002D481 File Offset: 0x0002B681
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x06007925 RID: 31013 RVA: 0x00289474 File Offset: 0x00287674
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				living.PetEffectTrigger = true;
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				new PetAddBaseDamageEquip(this.m_count, base.Info.ID.ToString()).Start(living);
			}
		}

		// Token: 0x040046F9 RID: 18169
		private int m_count;

		// Token: 0x040046FA RID: 18170
		private int m_probability;

		// Token: 0x040046FB RID: 18171
		private int m_currentId;
	}
}
