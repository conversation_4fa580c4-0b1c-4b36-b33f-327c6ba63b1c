﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DB1 RID: 3505
	public class AE1059 : BasePetEffect
	{
		// Token: 0x06007C16 RID: 31766 RVA: 0x00295CD8 File Offset: 0x00293ED8
		public AE1059(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1059, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C17 RID: 31767 RVA: 0x00295D58 File Offset: 0x00293F58
		public override bool Start(Living living)
		{
			AE1059 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1059) as AE1059;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C18 RID: 31768 RVA: 0x0002F7F6 File Offset: 0x0002D9F6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007C19 RID: 31769 RVA: 0x00295DB8 File Offset: 0x00293FB8
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				player.SyncAtTime = true;
				player.AddBlood(2000);
				player.SyncAtTime = false;
				List<Living> list = player.Game.Map.FindAllNearestSameTeam(player.X, player.Y, 250.0, player);
				foreach (Living living in list)
				{
					this.m_added = 2000;
					living.SyncAtTime = true;
					living.AddBlood(this.m_added);
					living.SyncAtTime = false;
				}
			}
		}

		// Token: 0x06007C1A RID: 31770 RVA: 0x0002F80C File Offset: 0x0002DA0C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004A11 RID: 18961
		private int m_type = 0;

		// Token: 0x04004A12 RID: 18962
		private int m_count = 0;

		// Token: 0x04004A13 RID: 18963
		private int m_probability = 0;

		// Token: 0x04004A14 RID: 18964
		private int m_delay = 0;

		// Token: 0x04004A15 RID: 18965
		private int m_coldDown = 0;

		// Token: 0x04004A16 RID: 18966
		private int m_currentId;

		// Token: 0x04004A17 RID: 18967
		private int m_added = 0;
	}
}
