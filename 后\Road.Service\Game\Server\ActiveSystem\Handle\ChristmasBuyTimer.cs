﻿using System;
using Bussiness;
using Game.Base.Packets;
using Game.Server.GameObjects;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C51 RID: 3153
	[ActiveSystemHandleAttbute(29)]
	public class ChristmasBuyTimer : IActiveSystemCommandHadler
	{
		// Token: 0x06007025 RID: 28709 RVA: 0x0024D810 File Offset: 0x0024BA10
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			int christmasBuyTimeMoney = GameProperties.ChristmasBuyTimeMoney;
			Player.RemoveMoney(christmasBuyTimeMoney);
			int christmasBuyMinute = GameProperties.ChristmasBuyMinute;
			Player.Actives.AddTime(christmasBuyMinute);
			Player.SendMessage(LanguageMgr.GetTranslation("ActiveSystemHandler.Msg2", Array.Empty<object>()));
			return true;
		}
	}
}
