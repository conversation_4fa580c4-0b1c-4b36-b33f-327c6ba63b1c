﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FD2 RID: 4050
	public class ExerciseMgr
	{
		// Token: 0x06008AB5 RID: 35509 RVA: 0x002F8138 File Offset: 0x002F6338
		public static bool Init()
		{
			bool flag;
			try
			{
				ExerciseMgr._exercises = new Dictionary<int, ExerciseInfo>();
				ExerciseMgr.rand = new Random();
				flag = ExerciseMgr.LoadExercise(ExerciseMgr._exercises);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = ExerciseMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ExerciseMgr.log.Error("ExercisesMgr", ex);
				}
				flag = false;
			}
			return flag;
		}

		// Token: 0x06008AB6 RID: 35510 RVA: 0x002F81A0 File Offset: 0x002F63A0
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, ExerciseInfo> dictionary = new Dictionary<int, ExerciseInfo>();
				bool flag = ExerciseMgr.LoadExercise(dictionary);
				if (flag)
				{
					try
					{
						ExerciseMgr._exercises = dictionary;
						return true;
					}
					catch
					{
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = ExerciseMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ExerciseMgr.log.Error("ExerciseMgr", ex);
				}
			}
			return false;
		}

		// Token: 0x06008AB7 RID: 35511 RVA: 0x002F8220 File Offset: 0x002F6420
		private static bool LoadExercise(Dictionary<int, ExerciseInfo> exercise)
		{
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				ExerciseInfo[] allExercise = produceBussiness.GetAllExercise();
				ExerciseInfo[] array = allExercise;
				ExerciseInfo[] array2 = array;
				ExerciseInfo[] array3 = array2;
				foreach (ExerciseInfo exerciseInfo in array3)
				{
					bool flag = !exercise.ContainsKey(exerciseInfo.Grage);
					if (flag)
					{
						exercise.Add(exerciseInfo.Grage, exerciseInfo);
					}
				}
			}
			return true;
		}

		// Token: 0x06008AB8 RID: 35512 RVA: 0x002F82B4 File Offset: 0x002F64B4
		public static ExerciseInfo FindExercise(int grage)
		{
			ExerciseMgr.m_clientLocker.AcquireWriterLock(-1);
			try
			{
				bool flag = ExerciseMgr._exercises.ContainsKey(grage);
				if (flag)
				{
					return ExerciseMgr._exercises[grage];
				}
			}
			finally
			{
				ExerciseMgr.m_clientLocker.ReleaseWriterLock();
			}
			return null;
		}

		// Token: 0x06008AB9 RID: 35513 RVA: 0x002F8314 File Offset: 0x002F6514
		public static int GetMaxLevel()
		{
			return ExerciseMgr._exercises.Values.Count;
		}

		// Token: 0x06008ABA RID: 35514 RVA: 0x002F8338 File Offset: 0x002F6538
		public static ExerciseInfo GetExercise(int gp)
		{
			bool flag = gp >= ExerciseMgr.FindExercise(ExerciseMgr.GetMaxLevel()).GP;
			ExerciseInfo exerciseInfo;
			if (flag)
			{
				exerciseInfo = ExerciseMgr.FindExercise(ExerciseMgr.GetMaxLevel());
			}
			else
			{
				for (int i = ExerciseMgr.GetMaxLevel(); i >= 1; i--)
				{
					bool flag2 = gp >= ExerciseMgr.FindExercise(i).GP;
					if (flag2)
					{
						return ExerciseMgr.FindExercise(i);
					}
				}
				exerciseInfo = new ExerciseInfo
				{
					Grage = 1,
					ExerciseA = 1,
					ExerciseD = 1,
					ExerciseAG = 1,
					ExerciseL = 1,
					ExerciseH = 1
				};
			}
			return exerciseInfo;
		}

		// Token: 0x040054F8 RID: 21752
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040054F9 RID: 21753
		private static Dictionary<int, ExerciseInfo> _exercises;

		// Token: 0x040054FA RID: 21754
		private static Random rand;

		// Token: 0x040054FB RID: 21755
		private static ReaderWriterLock m_clientLocker = new ReaderWriterLock();
	}
}
