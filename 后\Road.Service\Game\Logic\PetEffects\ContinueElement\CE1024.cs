﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E4A RID: 3658
	public class CE1024 : BasePetEffect
	{
		// Token: 0x06007F47 RID: 32583 RVA: 0x002A4740 File Offset: 0x002A2940
		public CE1024(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1024, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F48 RID: 32584 RVA: 0x002A47C0 File Offset: 0x002A29C0
		public override bool Start(Living living)
		{
			CE1024 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1024) as CE1024;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F49 RID: 32585 RVA: 0x002A4820 File Offset: 0x002A2A20
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.BaseDamage += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F4A RID: 32586 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F4B RID: 32587 RVA: 0x002A4880 File Offset: 0x002A2A80
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F4C RID: 32588 RVA: 0x002A48B4 File Offset: 0x002A2AB4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Game.SendPlayerPicture(player, 29, false);
			player.BaseDamage -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E41 RID: 20033
		private int m_type = 0;

		// Token: 0x04004E42 RID: 20034
		private int m_count = 0;

		// Token: 0x04004E43 RID: 20035
		private int m_probability = 0;

		// Token: 0x04004E44 RID: 20036
		private int m_delay = 0;

		// Token: 0x04004E45 RID: 20037
		private int m_coldDown = 0;

		// Token: 0x04004E46 RID: 20038
		private int m_currentId;

		// Token: 0x04004E47 RID: 20039
		private int m_added = 0;
	}
}
