﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EB3 RID: 3763
	public class CE1254 : BasePetEffect
	{
		// Token: 0x060081DF RID: 33247 RVA: 0x002AE768 File Offset: 0x002AC968
		public CE1254(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1254, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081E0 RID: 33248 RVA: 0x002AE7E8 File Offset: 0x002AC9E8
		public override bool Start(Living living)
		{
			CE1254 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1254) as CE1254;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081E1 RID: 33249 RVA: 0x000330BA File Offset: 0x000312BA
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081E2 RID: 33250 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081E3 RID: 33251 RVA: 0x002AE848 File Offset: 0x002ACA48
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				bool flag2 = living.Attack < (double)this.m_added;
				if (flag2)
				{
					this.m_added = (int)living.Attack - 1;
				}
				living.Attack -= (double)this.m_added;
			}
		}

		// Token: 0x060081E4 RID: 33252 RVA: 0x002AE8A8 File Offset: 0x002ACAA8
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060081E5 RID: 33253 RVA: 0x002AE8DC File Offset: 0x002ACADC
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Attack += (double)this.m_added;
			this.m_added = 0;
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005122 RID: 20770
		private int m_type = 0;

		// Token: 0x04005123 RID: 20771
		private int m_count = 0;

		// Token: 0x04005124 RID: 20772
		private int m_probability = 0;

		// Token: 0x04005125 RID: 20773
		private int m_delay = 0;

		// Token: 0x04005126 RID: 20774
		private int m_coldDown = 0;

		// Token: 0x04005127 RID: 20775
		private int m_currentId;

		// Token: 0x04005128 RID: 20776
		private int m_added = 0;
	}
}
