﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D61 RID: 3425
	public class PE1107 : BasePetEffect
	{
		// Token: 0x06007A7C RID: 31356 RVA: 0x0028F0F4 File Offset: 0x0028D2F4
		public PE1107(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1107, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A7D RID: 31357 RVA: 0x0028F174 File Offset: 0x0028D374
		public override bool Start(Living living)
		{
			PE1107 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1107) as PE1107;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A7E RID: 31358 RVA: 0x0002E7D4 File Offset: 0x0002C9D4
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007A7F RID: 31359 RVA: 0x0028F1D4 File Offset: 0x0028D3D4
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 50;
				(living as Player).AddPetMP(this.m_added);
			}
		}

		// Token: 0x06007A80 RID: 31360 RVA: 0x0002E7EA File Offset: 0x0002C9EA
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x040047E7 RID: 18407
		private int m_type = 0;

		// Token: 0x040047E8 RID: 18408
		private int m_count = 0;

		// Token: 0x040047E9 RID: 18409
		private int m_probability = 0;

		// Token: 0x040047EA RID: 18410
		private int m_delay = 0;

		// Token: 0x040047EB RID: 18411
		private int m_coldDown = 0;

		// Token: 0x040047EC RID: 18412
		private int m_currentId;

		// Token: 0x040047ED RID: 18413
		private int m_added = 0;
	}
}
