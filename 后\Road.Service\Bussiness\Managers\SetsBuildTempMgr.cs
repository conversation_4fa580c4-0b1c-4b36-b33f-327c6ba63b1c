﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FE8 RID: 4072
	public class SetsBuildTempMgr
	{
		// Token: 0x06008B4B RID: 35659 RVA: 0x002FB938 File Offset: 0x002F9B38
		public static bool Init()
		{
			return SetsBuildTempMgr.ReLoad();
		}

		// Token: 0x06008B4C RID: 35660 RVA: 0x002FB950 File Offset: 0x002F9B50
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, SetsBuildTempInfo> dictionary = SetsBuildTempMgr.LoadFromDatabase();
				bool flag = dictionary.Values.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, SetsBuildTempInfo>>(ref SetsBuildTempMgr.m_setsBuildTemps, dictionary);
					return true;
				}
			}
			catch (Exception ex)
			{
				SetsBuildTempMgr.log.Error("SetsBuildTempMgr init error:", ex);
			}
			return false;
		}

		// Token: 0x06008B4D RID: 35661 RVA: 0x002FB9B8 File Offset: 0x002F9BB8
		private static Dictionary<int, SetsBuildTempInfo> LoadFromDatabase()
		{
			Dictionary<int, SetsBuildTempInfo> dictionary = new Dictionary<int, SetsBuildTempInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				SetsBuildTempInfo[] allSetsBuildTemp = produceBussiness.GetAllSetsBuildTemp();
				SetsBuildTempInfo[] array = allSetsBuildTemp;
				SetsBuildTempInfo[] array2 = array;
				SetsBuildTempInfo[] array3 = array2;
				foreach (SetsBuildTempInfo setsBuildTempInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(setsBuildTempInfo.Level);
					if (flag)
					{
						dictionary.Add(setsBuildTempInfo.Level, setsBuildTempInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008B4E RID: 35662 RVA: 0x002FBA54 File Offset: 0x002F9C54
		public static List<SetsBuildTempInfo> GetAllSetsBuildTemp()
		{
			return SetsBuildTempMgr.m_setsBuildTemps.Values.ToList<SetsBuildTempInfo>();
		}

		// Token: 0x06008B4F RID: 35663 RVA: 0x002FBA78 File Offset: 0x002F9C78
		public static int SetsBuildMax()
		{
			int count = SetsBuildTempMgr.m_setsBuildTemps.Count;
			return SetsBuildTempMgr.m_setsBuildTemps[count].Exp;
		}

		// Token: 0x06008B50 RID: 35664 RVA: 0x002FBAA8 File Offset: 0x002F9CA8
		public static SetsBuildTempInfo FindNextSetsBuildExp(int exp)
		{
			List<SetsBuildTempInfo> allSetsBuildTemp = SetsBuildTempMgr.GetAllSetsBuildTemp();
			SetsBuildTempInfo setsBuildTempInfo = null;
			for (int i = 0; i < allSetsBuildTemp.Count; i++)
			{
				setsBuildTempInfo = allSetsBuildTemp[i];
				bool flag = exp < setsBuildTempInfo.Exp;
				if (flag)
				{
					return setsBuildTempInfo;
				}
			}
			return setsBuildTempInfo;
		}

		// Token: 0x06008B51 RID: 35665 RVA: 0x002FBAFC File Offset: 0x002F9CFC
		public static void GetSetsBuildProp(int exp, ref int atk, ref int def, ref int agi, ref int luck)
		{
			List<SetsBuildTempInfo> allSetsBuildTemp = SetsBuildTempMgr.GetAllSetsBuildTemp();
			for (int i = 0; i < allSetsBuildTemp.Count; i++)
			{
				SetsBuildTempInfo setsBuildTempInfo = allSetsBuildTemp[i];
				bool flag = setsBuildTempInfo != null && exp >= setsBuildTempInfo.Exp;
				if (flag)
				{
					atk += setsBuildTempInfo.AttackGrow;
					def += setsBuildTempInfo.DefenceGrow;
					agi += setsBuildTempInfo.AgilityGrow;
					luck += setsBuildTempInfo.LuckGrow;
				}
			}
		}

		// Token: 0x06008B52 RID: 35666 RVA: 0x002FBB78 File Offset: 0x002F9D78
		public static void GetSetsBuildGuardProp(int exp, ref double guard)
		{
			List<SetsBuildTempInfo> allSetsBuildTemp = SetsBuildTempMgr.GetAllSetsBuildTemp();
			for (int i = 0; i < allSetsBuildTemp.Count; i++)
			{
				SetsBuildTempInfo setsBuildTempInfo = allSetsBuildTemp[i];
				bool flag = setsBuildTempInfo != null && exp >= setsBuildTempInfo.Exp;
				if (flag)
				{
					guard += (double)setsBuildTempInfo.GuardGrow;
				}
			}
		}

		// Token: 0x06008B53 RID: 35667 RVA: 0x002FBBD4 File Offset: 0x002F9DD4
		public static void GetSetsBuildDamageProp(int exp, ref double damage)
		{
			List<SetsBuildTempInfo> allSetsBuildTemp = SetsBuildTempMgr.GetAllSetsBuildTemp();
			for (int i = 0; i < allSetsBuildTemp.Count; i++)
			{
				SetsBuildTempInfo setsBuildTempInfo = allSetsBuildTemp[i];
				bool flag = setsBuildTempInfo != null && exp >= setsBuildTempInfo.Exp;
				if (flag)
				{
					damage += (double)setsBuildTempInfo.DamageGrow;
				}
			}
		}

		// Token: 0x04005540 RID: 21824
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005541 RID: 21825
		private static Dictionary<int, SetsBuildTempInfo> m_setsBuildTemps = new Dictionary<int, SetsBuildTempInfo>();

		// Token: 0x04005542 RID: 21826
		private static ThreadSafeRandom random = new ThreadSafeRandom();
	}
}
