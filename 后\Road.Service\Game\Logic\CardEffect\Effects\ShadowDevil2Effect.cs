﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F35 RID: 3893
	public class ShadowDevil2Effect : BaseCardEffect
	{
		// Token: 0x0600846E RID: 33902 RVA: 0x002B817C File Offset: 0x002B637C
		public ShadowDevil2Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.ShadowDevil2, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x0600846F RID: 33903 RVA: 0x002B81EC File Offset: 0x002B63EC
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.ShadowDevil2) is ShadowDevil2Effect;
			return flag || base.Start(living);
		}

		// Token: 0x06008470 RID: 33904 RVA: 0x00034EB6 File Offset: 0x000330B6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x06008471 RID: 33905 RVA: 0x00034ECC File Offset: 0x000330CC
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x06008472 RID: 33906 RVA: 0x002B8224 File Offset: 0x002B6424
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.Attack -= (double)this.m_added;
				player.Agility -= (double)this.m_added;
				player.Lucky -= (double)this.m_added;
				player.Defence -= (double)this.m_added;
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 4;
			if (flag2)
			{
				this.m_added = this.m_value;
				player.Attack += (double)this.m_added;
				player.Agility += (double)this.m_added;
				player.Lucky += (double)this.m_added;
				player.Defence += (double)this.m_added;
				player.Game.SendMessage(player.PlayerDetail, "您激活了黑暗堡垒2件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活黑暗堡垒2件套卡.", 3);
			}
		}

		// Token: 0x04005293 RID: 21139
		private int m_indexValue = 0;

		// Token: 0x04005294 RID: 21140
		private int m_value = 0;

		// Token: 0x04005295 RID: 21141
		private int m_added = 0;
	}
}
