﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CF0 RID: 3312
	public class PetAddBaseDamage_Passive : BasePetEffect
	{
		// Token: 0x06007809 RID: 30729 RVA: 0x0002C506 File Offset: 0x0002A706
		public PetAddBaseDamage_Passive(int probability, string elementID)
			: base(ePetEffectType.PetAddBaseDamage_Passive, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_elementID = elementID;
		}

		// Token: 0x0600780A RID: 30730 RVA: 0x00283BB8 File Offset: 0x00281DB8
		public override bool Start(Living living)
		{
			PetAddBaseDamage_Passive petAddBaseDamage_Passive = living.PetEffectList.GetOfType(ePetEffectType.PetAddBaseDamage_Passive) as PetAddBaseDamage_Passive;
			bool flag = petAddBaseDamage_Passive != null;
			bool flag2;
			if (flag)
			{
				petAddBaseDamage_Passive.m_probability = ((this.m_probability > petAddBaseDamage_Passive.m_probability) ? this.m_probability : petAddBaseDamage_Passive.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600780B RID: 30731 RVA: 0x0002C53D File Offset: 0x0002A73D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginNextTurn;
		}

		// Token: 0x0600780C RID: 30732 RVA: 0x0002C553 File Offset: 0x0002A753
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginNextTurn;
		}

		// Token: 0x0600780D RID: 30733 RVA: 0x00283C18 File Offset: 0x00281E18
		private void player_BeginNextTurn(Living living)
		{
			bool flag = this.m_value == 0;
			if (flag)
			{
				string elementID = this.m_elementID;
				string text = elementID;
				bool flag2 = text == "2977";
				if (flag2)
				{
					this.m_value = 10;
					this.m_percent = true;
				}
				living.Game.sendShowPicSkil(living, base.Info, true);
				bool percent = this.m_percent;
				if (percent)
				{
					living.BaseDamage += living.BaseDamage * (double)this.m_value / 100.0;
				}
				else
				{
					living.BaseDamage += (double)this.m_value;
				}
			}
		}

		// Token: 0x04004658 RID: 18008
		private int m_probability = 0;

		// Token: 0x04004659 RID: 18009
		private int m_value = 0;

		// Token: 0x0400465A RID: 18010
		private string m_elementID;

		// Token: 0x0400465B RID: 18011
		private bool m_percent;
	}
}
