﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Bussiness
{
	// Token: 0x02000FAA RID: 4010
	public static class Functions
	{
		// Token: 0x06008871 RID: 34929 RVA: 0x000361AD File Offset: 0x000343AD
		public static IEnumerable<IEnumerable<T>> Split<T>(this T[] array, int size)
		{
			int i = 0;
			while ((float)i < (float)array.Length / (float)size)
			{
				yield return array.Skip(i * size).Take(size);
				int num = i;
				i = num + 1;
			}
			yield break;
		}

		// Token: 0x06008872 RID: 34930 RVA: 0x000361C4 File Offset: 0x000343C4
		public static IEnumerable<IEnumerable<T>> Split<T>(this T[] array, int size, int startIndex)
		{
			int i = 1;
			while ((float)i < (float)array.Length / (float)size)
			{
				yield return array.Skip(i * size).Take(size);
				int num = i;
				i = num + 1;
			}
			yield break;
		}
	}
}
