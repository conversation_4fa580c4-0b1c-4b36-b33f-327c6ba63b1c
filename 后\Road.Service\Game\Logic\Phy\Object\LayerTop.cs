﻿using System;

namespace Game.Logic.Phy.Object
{
	// Token: 0x02000CD2 RID: 3282
	public class LayerTop : PhysicalObj
	{
		// Token: 0x1700140E RID: 5134
		// (get) Token: 0x06007536 RID: 30006 RVA: 0x00005350 File Offset: 0x00003550
		public override int Type
		{
			get
			{
				return 0;
			}
		}

		// Token: 0x06007537 RID: 30007 RVA: 0x0002B6D4 File Offset: 0x000298D4
		public LayerTop(int id, string name, string model, string defaultAction, int scale, int rotation)
			: base(id, name, model, defaultAction, scale, rotation, -1)
		{
		}
	}
}
