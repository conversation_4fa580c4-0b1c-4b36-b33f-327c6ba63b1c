﻿using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;

namespace Bussiness
{
	// Token: 0x02000FA3 RID: 4003
	public class CheckCode
	{
		// Token: 0x06008817 RID: 34839 RVA: 0x002CA174 File Offset: 0x002C8374
		public static byte[] CreateImage(string randomcode)
		{
			int num = 30;
			int num2 = randomcode.Length * 30;
			Bitmap bitmap = new Bitmap(num2, 36);
			Graphics graphics = Graphics.FromImage(bitmap);
			byte[] array2;
			try
			{
				graphics.Clear(Color.White);
				int num3 = CheckCode.rand.Next(7);
				Brush brush = new SolidBrush(CheckCode.c[num3]);
				for (int i = 0; i < 1; i++)
				{
					int num4 = CheckCode.rand.Next(bitmap.Width / 2);
					int num5 = CheckCode.rand.Next(bitmap.Width * 3 / 4, bitmap.Width);
					int num6 = CheckCode.rand.Next(bitmap.Height);
					int num7 = CheckCode.rand.Next(bitmap.Height);
					graphics.DrawBezier(new Pen(CheckCode.c[num3], 2f), (float)num4, (float)num6, (float)((num4 + num5) / 4), 0f, (float)((num4 + num5) * 3 / 4), (float)bitmap.Height, (float)num5, (float)num7);
				}
				char[] array = randomcode.ToCharArray();
				StringFormat stringFormat = new StringFormat(StringFormatFlags.NoClip);
				stringFormat.Alignment = StringAlignment.Center;
				stringFormat.LineAlignment = StringAlignment.Center;
				for (int j = 0; j < array.Length; j++)
				{
					int num8 = CheckCode.rand.Next(5);
					Font font = new Font(CheckCode.font[num8], 22f, FontStyle.Bold);
					Point point = new Point(16, 16);
					float num9 = (float)ThreadSafeRandom.NextStatic(-num, num);
					graphics.TranslateTransform((float)point.X, (float)point.Y);
					graphics.RotateTransform(num9);
					graphics.DrawString(array[j].ToString(), font, brush, 1f, 1f, stringFormat);
					graphics.RotateTransform(0f - num9);
					graphics.TranslateTransform(2f, (float)(-(float)point.Y));
				}
				MemoryStream memoryStream = new MemoryStream();
				bitmap.Save(memoryStream, ImageFormat.Gif);
				array2 = memoryStream.ToArray();
			}
			finally
			{
				graphics.Dispose();
				bitmap.Dispose();
			}
			return array2;
		}

		// Token: 0x06008818 RID: 34840 RVA: 0x002CA3BC File Offset: 0x002C85BC
		private static string GenerateRandomString(int length, CheckCode.RandomStringMode mode)
		{
			string text = string.Empty;
			bool flag = length == 0;
			string text2;
			if (flag)
			{
				text2 = text;
			}
			else
			{
				int[] array = new int[2];
				switch (mode)
				{
				case CheckCode.RandomStringMode.LowerLetter:
				{
					for (int i = 0; i < length; i++)
					{
						text += CheckCode.lowerLetters[CheckCode.rand.Next(0, CheckCode.lowerLetters.Length)].ToString();
					}
					break;
				}
				case CheckCode.RandomStringMode.UpperLetter:
				{
					for (int j = 0; j < length; j++)
					{
						text += CheckCode.upperLetters[CheckCode.rand.Next(0, CheckCode.upperLetters.Length)].ToString();
					}
					break;
				}
				case CheckCode.RandomStringMode.Letter:
				{
					for (int k = 0; k < length; k++)
					{
						text += CheckCode.letters[CheckCode.rand.Next(0, CheckCode.letters.Length)].ToString();
					}
					break;
				}
				case CheckCode.RandomStringMode.Digital:
				{
					for (int l = 0; l < length; l++)
					{
						text += CheckCode.digitals[CheckCode.rand.Next(0, CheckCode.digitals.Length)].ToString();
					}
					break;
				}
				default:
				{
					for (int m = 0; m < length; m++)
					{
						text += CheckCode.mix[CheckCode.rand.Next(0, CheckCode.mix.Length)].ToString();
					}
					break;
				}
				}
				text2 = text;
			}
			return text2;
		}

		// Token: 0x06008819 RID: 34841 RVA: 0x002CA568 File Offset: 0x002C8768
		public static string GenerateCheckCode()
		{
			return CheckCode.GenerateRandomString(4, CheckCode.RandomStringMode.Digital);
		}

		// Token: 0x040053CA RID: 21450
		public static ThreadSafeRandom rand = new ThreadSafeRandom();

		// Token: 0x040053CB RID: 21451
		private static Color[] c = new Color[]
		{
			Color.Black,
			Color.Red,
			Color.DarkBlue,
			Color.Green,
			Color.Orange,
			Color.Brown,
			Color.DarkCyan,
			Color.Purple
		};

		// Token: 0x040053CC RID: 21452
		private static string[] font = new string[] { "Verdana", "Microsoft Sans Serif", "Comic Sans MS", "Arial", "宋体" };

		// Token: 0x040053CD RID: 21453
		private static char[] digitals = new char[] { '1', '2', '3', '4', '5', '6', '7', '8', '9' };

		// Token: 0x040053CE RID: 21454
		private static char[] lowerLetters = new char[]
		{
			'a', 'b', 'c', 'd', 'e', 'f', 'h', 'k', 'm', 'n',
			'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y',
			'z'
		};

		// Token: 0x040053CF RID: 21455
		private static char[] upperLetters = new char[]
		{
			'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'K', 'M',
			'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X',
			'Y', 'Z'
		};

		// Token: 0x040053D0 RID: 21456
		private static char[] letters = new char[]
		{
			'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j',
			'k', 'l', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u',
			'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E',
			'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'P',
			'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
		};

		// Token: 0x040053D1 RID: 21457
		private static char[] mix = new char[]
		{
			'2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b',
			'c', 'd', 'e', 'f', 'h', 'k', 'm', 'n', 'p', 'q',
			'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A',
			'B', 'C', 'D', 'E', 'F', 'G', 'H', 'K', 'M', 'N',
			'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y',
			'Z'
		};

		// Token: 0x02000FA4 RID: 4004
		private enum RandomStringMode
		{
			// Token: 0x040053D3 RID: 21459
			LowerLetter,
			// Token: 0x040053D4 RID: 21460
			UpperLetter,
			// Token: 0x040053D5 RID: 21461
			Letter,
			// Token: 0x040053D6 RID: 21462
			Digital,
			// Token: 0x040053D7 RID: 21463
			Mix
		}
	}
}
