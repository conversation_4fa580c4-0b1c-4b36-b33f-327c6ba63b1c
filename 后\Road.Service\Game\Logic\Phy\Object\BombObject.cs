﻿using System;
using System.Drawing;
using Game.Logic.Phy.Maps;
using Game.Logic.Phy.Maths;

namespace Game.Logic.Phy.Object
{
	// Token: 0x02000CCE RID: 3278
	public class BombObject : Physics
	{
		// Token: 0x17001408 RID: 5128
		// (get) Token: 0x0600751D RID: 29981 RVA: 0x0002B5E4 File Offset: 0x000297E4
		public float vX
		{
			get
			{
				return this.m_vx.x1;
			}
		}

		// Token: 0x17001409 RID: 5129
		// (get) Token: 0x0600751E RID: 29982 RVA: 0x0002B5F1 File Offset: 0x000297F1
		public float vY
		{
			get
			{
				return this.m_vy.x1;
			}
		}

		// Token: 0x1700140A RID: 5130
		// (get) Token: 0x0600751F RID: 29983 RVA: 0x0002B5FE File Offset: 0x000297FE
		public float Arf
		{
			get
			{
				return this.m_arf;
			}
		}

		// Token: 0x1700140B RID: 5131
		// (get) Token: 0x06007520 RID: 29984 RVA: 0x0002B606 File Offset: 0x00029806
		public float Gf
		{
			get
			{
				return this.m_gf;
			}
		}

		// Token: 0x1700140C RID: 5132
		// (get) Token: 0x06007521 RID: 29985 RVA: 0x0002B60E File Offset: 0x0002980E
		public float Wf
		{
			get
			{
				return this.m_wf;
			}
		}

		// Token: 0x06007522 RID: 29986 RVA: 0x0026EB4C File Offset: 0x0026CD4C
		public BombObject(int id, float mass, float gravityFactor, float windFactor, float airResitFactor)
			: base(id)
		{
			this.m_mass = mass;
			this.m_gravityFactor = gravityFactor;
			this.m_windFactor = windFactor;
			this.m_airResitFactor = airResitFactor;
			this.m_vx = new EulerVector(0, 0, 0f);
			this.m_vy = new EulerVector(0, 0, 0f);
			this.m_rect = new Rectangle(-3, -3, 6, 6);
		}

		// Token: 0x06007523 RID: 29987 RVA: 0x0002B616 File Offset: 0x00029816
		public void setSpeedXY(int vx, int vy)
		{
			this.m_vx.x1 = (float)vx;
			this.m_vy.x1 = (float)vy;
		}

		// Token: 0x06007524 RID: 29988 RVA: 0x0002B633 File Offset: 0x00029833
		public override void SetXY(int x, int y)
		{
			base.SetXY(x, y);
			this.m_vx.x0 = (float)x;
			this.m_vy.x0 = (float)y;
		}

		// Token: 0x06007525 RID: 29989 RVA: 0x0002B659 File Offset: 0x00029859
		public override void SetMap(Map map)
		{
			base.SetMap(map);
			this.UpdateAGW();
		}

		// Token: 0x06007526 RID: 29990 RVA: 0x0002B66B File Offset: 0x0002986B
		protected void UpdateForceFactor(float air, float gravity, float wind)
		{
			this.m_airResitFactor = air;
			this.m_gravityFactor = gravity;
			this.m_windFactor = wind;
			this.UpdateAGW();
		}

		// Token: 0x06007527 RID: 29991 RVA: 0x0026EBB8 File Offset: 0x0026CDB8
		private void UpdateAGW()
		{
			bool flag = this.m_map != null;
			if (flag)
			{
				this.m_arf = this.m_map.airResistance * this.m_airResitFactor;
				this.m_gf = this.m_map.gravity * this.m_gravityFactor * this.m_mass;
				this.m_wf = this.m_map.wind * this.m_windFactor * this.m_map.windRate;
			}
		}

		// Token: 0x06007528 RID: 29992 RVA: 0x0026EC30 File Offset: 0x0026CE30
		public virtual Point CompleteNextMovePoint(float dt)
		{
			this.m_vx.ComputeOneEulerStep(this.m_mass, this.m_arf, this.m_wf, dt);
			this.m_vy.ComputeOneEulerStep(this.m_mass, this.m_arf, this.m_gf, dt);
			return new Point((int)this.m_vx.x0, (int)this.m_vy.x0);
		}

		// Token: 0x06007529 RID: 29993 RVA: 0x0026ECA0 File Offset: 0x0026CEA0
		public virtual void MoveTo(int px, int py)
		{
			bool flag = px == this.m_x && py == this.m_y;
			if (!flag)
			{
				int num = px - this.m_x;
				int num2 = py - this.m_y;
				bool flag2 = Math.Abs(num) > Math.Abs(num2);
				bool flag3;
				int num3;
				int num4;
				if (flag2)
				{
					flag3 = true;
					num3 = Math.Abs(num);
					num4 = num / num3;
				}
				else
				{
					flag3 = false;
					num3 = Math.Abs(num2);
					num4 = num2 / num3;
				}
				Point point = new Point(this.m_x, this.m_y);
				for (int i = 1; i <= num3; i += 3)
				{
					point = ((!flag3) ? this.GetNextPointByY(this.m_x, px, this.m_y, py, this.m_y + i * num4) : this.GetNextPointByX(this.m_x, px, this.m_y, py, this.m_x + i * num4));
					Rectangle rect = this.m_rect;
					rect.Offset(point.X, point.Y);
					Physics[] array = this.m_map.FindPhysicalObjects(rect, this);
					bool flag4 = array.Length != 0;
					if (flag4)
					{
						base.SetXY(point.X, point.Y);
						this.CollideObjects(array);
					}
					else
					{
						bool flag5 = !this.m_map.IsRectangleEmpty(rect);
						if (flag5)
						{
							base.SetXY(point.X, point.Y);
							this.CollideGround();
						}
						else
						{
							bool flag6 = this.m_map.IsOutMap(point.X, point.Y);
							if (flag6)
							{
								base.SetXY(point.X, point.Y);
								this.FlyoutMap();
							}
						}
					}
					bool flag7 = !this.m_isLiving || !this.m_isMoving;
					if (flag7)
					{
						return;
					}
				}
				base.SetXY(px, py);
			}
		}

		// Token: 0x0600752A RID: 29994 RVA: 0x00005683 File Offset: 0x00003883
		protected virtual void CollideObjects(Physics[] list)
		{
		}

		// Token: 0x0600752B RID: 29995 RVA: 0x0002B68A File Offset: 0x0002988A
		protected virtual void CollideGround()
		{
			this.StopMoving();
		}

		// Token: 0x0600752C RID: 29996 RVA: 0x0026EE90 File Offset: 0x0026D090
		protected virtual void FlyoutMap()
		{
			this.StopMoving();
			bool isLiving = this.m_isLiving;
			if (isLiving)
			{
				this.Die();
			}
		}

		// Token: 0x0600752D RID: 29997 RVA: 0x0026EEB8 File Offset: 0x0026D0B8
		private Point GetNextPointByX(int x1, int x2, int y1, int y2, int x)
		{
			bool flag = x2 == x1;
			Point point;
			if (flag)
			{
				point = new Point(x, y1);
			}
			else
			{
				point = new Point(x, (x - x1) * (y2 - y1) / (x2 - x1) + y1);
			}
			return point;
		}

		// Token: 0x0600752E RID: 29998 RVA: 0x0026EEF4 File Offset: 0x0026D0F4
		private Point GetNextPointByY(int x1, int x2, int y1, int y2, int y)
		{
			bool flag = y2 == y1;
			Point point;
			if (flag)
			{
				point = new Point(x1, y);
			}
			else
			{
				point = new Point((y - y1) * (x2 - x1) / (y2 - y1) + x1, y);
			}
			return point;
		}

		// Token: 0x040044BC RID: 17596
		protected float m_mass;

		// Token: 0x040044BD RID: 17597
		protected float m_gravityFactor;

		// Token: 0x040044BE RID: 17598
		protected float m_windFactor;

		// Token: 0x040044BF RID: 17599
		protected float m_airResitFactor;

		// Token: 0x040044C0 RID: 17600
		protected EulerVector m_vx;

		// Token: 0x040044C1 RID: 17601
		protected EulerVector m_vy;

		// Token: 0x040044C2 RID: 17602
		protected float m_arf;

		// Token: 0x040044C3 RID: 17603
		protected float m_gf;

		// Token: 0x040044C4 RID: 17604
		protected float m_wf;
	}
}
