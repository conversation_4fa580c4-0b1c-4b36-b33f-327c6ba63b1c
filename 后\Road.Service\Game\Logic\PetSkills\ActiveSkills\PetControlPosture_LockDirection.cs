﻿using System;
using Game.Logic.Effects;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D35 RID: 3381
	public class PetControlPosture_LockDirection : BasePetEffect
	{
		// Token: 0x06007976 RID: 31094 RVA: 0x0002D9AC File Offset: 0x0002BBAC
		public PetControlPosture_LockDirection(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetControlPosture_LockDirection, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
			this.m_count = count;
		}

		// Token: 0x06007977 RID: 31095 RVA: 0x0028AA4C File Offset: 0x00288C4C
		public override bool Start(Living living)
		{
			PetControlPosture_LockDirection petControlPosture_LockDirection = living.PetEffectList.GetOfType(ePetEffectType.PetControlPosture_LockDirection) as PetControlPosture_LockDirection;
			bool flag = petControlPosture_LockDirection != null;
			bool flag2;
			if (flag)
			{
				petControlPosture_LockDirection.m_probability = ((this.m_probability > petControlPosture_LockDirection.m_probability) ? this.m_probability : petControlPosture_LockDirection.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007978 RID: 31096 RVA: 0x0002D9DD File Offset: 0x0002BBDD
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
			player.AfterKillingLiving += this.player_AfterKillingLiving;
		}

		// Token: 0x06007979 RID: 31097 RVA: 0x0002DA06 File Offset: 0x0002BC06
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
			player.AfterKillingLiving += this.player_AfterKillingLiving;
		}

		// Token: 0x0600797A RID: 31098 RVA: 0x0028AAAC File Offset: 0x00288CAC
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x0600797B RID: 31099 RVA: 0x0028AADC File Offset: 0x00288CDC
		private void player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				this.IsTrigger = false;
				target.Game.sendShowPicSkil(target, base.ElementInfo, true);
				new LockDirectionEffect(this.m_count).Start(target);
			}
		}

		// Token: 0x04004729 RID: 18217
		private int m_probability;

		// Token: 0x0400472A RID: 18218
		private int m_currentId;

		// Token: 0x0400472B RID: 18219
		private int m_count;
	}
}
