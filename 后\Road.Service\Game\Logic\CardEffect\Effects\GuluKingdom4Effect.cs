﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F2B RID: 3883
	public class GuluKingdom4Effect : BaseCardEffect
	{
		// Token: 0x06008438 RID: 33848 RVA: 0x002B7290 File Offset: 0x002B5490
		public GuluKingdom4Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.GuluKingdom4, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008439 RID: 33849 RVA: 0x002B7300 File Offset: 0x002B5500
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.GuluKingdom4) is GuluKingdom4Effect;
			return flag || base.Start(living);
		}

		// Token: 0x0600843A RID: 33850 RVA: 0x00034CA2 File Offset: 0x00032EA2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty1;
			player.AfterKillingLiving += this.ChangeProperty;
		}

		// Token: 0x0600843B RID: 33851 RVA: 0x00034CCB File Offset: 0x00032ECB
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty1;
			player.AfterKillingLiving -= this.ChangeProperty;
		}

		// Token: 0x0600843C RID: 33852 RVA: 0x002B7338 File Offset: 0x002B5538
		private void ChangeProperty(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = living.Game is PVEGame && (living.Game as PVEGame).Info.ID == 1 && (target is SimpleNpc || target is SimpleBoss);
			if (flag)
			{
				damageAmount += this.m_value;
			}
		}

		// Token: 0x0600843D RID: 33853 RVA: 0x002B7394 File Offset: 0x002B5594
		private void ChangeProperty1(Player player)
		{
			bool flag = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 1;
			if (flag)
			{
				player.Game.SendMessage(player.PlayerDetail, "您激活了啵咕王城4件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活啵咕王城4件套卡.", 3);
			}
		}

		// Token: 0x04005275 RID: 21109
		private int m_indexValue = 0;

		// Token: 0x04005276 RID: 21110
		private int m_value = 0;

		// Token: 0x04005277 RID: 21111
		private int m_added = 0;
	}
}
