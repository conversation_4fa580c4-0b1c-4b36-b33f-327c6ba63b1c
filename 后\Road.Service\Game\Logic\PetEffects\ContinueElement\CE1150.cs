﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E73 RID: 3699
	public class CE1150 : BasePetEffect
	{
		// Token: 0x06008040 RID: 32832 RVA: 0x002A8580 File Offset: 0x002A6780
		public CE1150(int count, int probability, int type, int skillId, int delay, string elementID, Living source)
			: base(ePetEffectType.CE1150, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
			this.m_source = source;
		}

		// Token: 0x06008041 RID: 32833 RVA: 0x002A8608 File Offset: 0x002A6808
		public override bool Start(Living living)
		{
			CE1150 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1150) as CE1150;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008042 RID: 32834 RVA: 0x00032015 File Offset: 0x00030215
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008043 RID: 32835 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008044 RID: 32836 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginNextTurn(Living living)
		{
		}

		// Token: 0x06008045 RID: 32837 RVA: 0x002A8668 File Offset: 0x002A6868
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 200;
				living.AddBlood(-this.m_added, 1);
				bool flag2 = living.Blood <= 0;
				if (flag2)
				{
					living.Die();
					bool flag3 = this.m_source != null && this.m_source is Player;
					if (flag3)
					{
						(this.m_source as Player).PlayerDetail.OnKillingLiving(this.m_source.Game, 2, living.Id, living.IsLiving, this.m_added);
					}
				}
			}
		}

		// Token: 0x06008046 RID: 32838 RVA: 0x00032051 File Offset: 0x00030251
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F5B RID: 20315
		private int m_type = 0;

		// Token: 0x04004F5C RID: 20316
		private int m_count = 0;

		// Token: 0x04004F5D RID: 20317
		private int m_probability = 0;

		// Token: 0x04004F5E RID: 20318
		private int m_delay = 0;

		// Token: 0x04004F5F RID: 20319
		private int m_coldDown = 0;

		// Token: 0x04004F60 RID: 20320
		private int m_currentId;

		// Token: 0x04004F61 RID: 20321
		private int m_added = 0;

		// Token: 0x04004F62 RID: 20322
		private Living m_source;
	}
}
