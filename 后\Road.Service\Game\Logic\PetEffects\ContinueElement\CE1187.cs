﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E8D RID: 3725
	public class CE1187 : BasePetEffect
	{
		// Token: 0x060080E8 RID: 33000 RVA: 0x002AAD90 File Offset: 0x002A8F90
		public CE1187(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1187, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060080E9 RID: 33001 RVA: 0x002AAE10 File Offset: 0x002A9010
		public override bool Start(Living living)
		{
			CE1187 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1187) as CE1187;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080EA RID: 33002 RVA: 0x000326C0 File Offset: 0x000308C0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
		}

		// Token: 0x060080EB RID: 33003 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginNextTurn(Living living)
		{
		}

		// Token: 0x060080EC RID: 33004 RVA: 0x002AAE70 File Offset: 0x002A9070
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_added = 800;
			living.AddBlood(-this.m_added, 1);
			bool flag = living.Blood <= 0;
			if (flag)
			{
				living.Die();
				bool flag2 = living.Game.CurrentLiving != null && living.Game.CurrentLiving is Player;
				if (flag2)
				{
					(living.Game.CurrentLiving as Player).PlayerDetail.OnKillingLiving(living.Game, 2, living.Id, living.IsLiving, this.m_added);
				}
			}
		}

		// Token: 0x060080ED RID: 33005 RVA: 0x000326E9 File Offset: 0x000308E9
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005018 RID: 20504
		private int m_type = 0;

		// Token: 0x04005019 RID: 20505
		private int m_count = 0;

		// Token: 0x0400501A RID: 20506
		private int m_probability = 0;

		// Token: 0x0400501B RID: 20507
		private int m_delay = 0;

		// Token: 0x0400501C RID: 20508
		private int m_coldDown = 0;

		// Token: 0x0400501D RID: 20509
		private int m_currentId;

		// Token: 0x0400501E RID: 20510
		private int m_added = 0;
	}
}
