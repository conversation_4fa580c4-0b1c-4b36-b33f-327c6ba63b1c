﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D92 RID: 3474
	public class AE1019 : BasePetEffect
	{
		// Token: 0x06007B74 RID: 31604 RVA: 0x00293010 File Offset: 0x00291210
		public AE1019(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1019, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B75 RID: 31605 RVA: 0x00293090 File Offset: 0x00291290
		public override bool Start(Living living)
		{
			AE1019 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1019) as AE1019;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B76 RID: 31606 RVA: 0x0002F1AB File Offset: 0x0002D3AB
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007B77 RID: 31607 RVA: 0x0002F1C1 File Offset: 0x0002D3C1
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007B78 RID: 31608 RVA: 0x002930F0 File Offset: 0x002912F0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1019(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004938 RID: 18744
		private int m_type = 0;

		// Token: 0x04004939 RID: 18745
		private int m_count = 0;

		// Token: 0x0400493A RID: 18746
		private int m_probability = 0;

		// Token: 0x0400493B RID: 18747
		private int m_delay = 0;

		// Token: 0x0400493C RID: 18748
		private int m_coldDown = 0;

		// Token: 0x0400493D RID: 18749
		private int m_currentId;

		// Token: 0x0400493E RID: 18750
		private int m_added = 0;
	}
}
