﻿using System;
using System.Collections.Generic;
using System.Reflection;
using Game.Logic.AI;
using Game.Logic.AI.Npc;
using Game.Server.Managers;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic.Phy.Object
{
	// Token: 0x02000CE1 RID: 3297
	public class SimpleNpc : Living
	{
		// Token: 0x17001481 RID: 5249
		// (get) Token: 0x06007773 RID: 30579 RVA: 0x0002C12B File Offset: 0x0002A32B
		public new NpcInfo NpcInfo
		{
			get
			{
				return this.m_npcInfo;
			}
		}

		// Token: 0x17001482 RID: 5250
		// (get) Token: 0x06007774 RID: 30580 RVA: 0x0002C133 File Offset: 0x0002A333
		public int Rank
		{
			get
			{
				return this.m_rank;
			}
		}

		// Token: 0x06007775 RID: 30581 RVA: 0x00280A28 File Offset: 0x0027EC28
		public SimpleNpc(int id, BaseGame game, NpcInfo npcInfo, int type, int direction, string actions, int rank)
			: base(id, game, npcInfo.Camp, npcInfo.Name, npcInfo.ModelID, npcInfo.Blood, npcInfo.Immunity, direction)
		{
			switch (type)
			{
			case 0:
				base.Type = eLivingType.SimpleNpc;
				break;
			case 1:
				base.Type = eLivingType.SimpleNpcNormal;
				break;
			case 2:
				base.Type = eLivingType.SimpleNpcDeck;
				break;
			case 3:
				base.Type = eLivingType.SimpleWingNpc;
				break;
			}
			this.m_npcInfo = npcInfo;
			base.ActionStr = actions;
			this.m_ai = ScriptMgr.CreateInstance(npcInfo.Script) as ABrain;
			bool flag = this.m_ai == null;
			if (flag)
			{
				SimpleNpc.log.ErrorFormat("Can't create abrain :{0}", npcInfo.Script);
				this.m_ai = SimpleBrain.Simple;
			}
			this.m_ai.Game = this.m_game;
			this.m_ai.Body = this;
			this.m_rank = rank;
			try
			{
				this.m_ai.OnCreated();
			}
			catch (Exception ex)
			{
				SimpleNpc.log.ErrorFormat("SimpleNpc Created error:{1}", ex);
			}
		}

		// Token: 0x06007776 RID: 30582 RVA: 0x00280B58 File Offset: 0x0027ED58
		public override void Reset()
		{
			this.Agility = (double)this.m_npcInfo.Agility;
			this.Attack = (double)this.m_npcInfo.Attack;
			this.BaseDamage = (double)this.m_npcInfo.BaseDamage;
			this.BaseGuard = (double)this.m_npcInfo.BaseGuard;
			this.Lucky = (double)this.m_npcInfo.Lucky;
			this.Grade = this.m_npcInfo.Level;
			this.Experience = this.m_npcInfo.Experience;
			base.SetRect(this.m_npcInfo.X, this.m_npcInfo.Y, this.m_npcInfo.Width, this.m_npcInfo.Height);
			base.SetRelateDemagemRect(this.m_npcInfo.X, this.m_npcInfo.Y, this.m_npcInfo.Width, this.m_npcInfo.Height);
			bool flag = this.m_direction == 1;
			if (flag)
			{
				base.ReSetRectWithDir();
			}
			base.FireX = this.NpcInfo.FireX;
			base.FireY = this.NpcInfo.FireY;
			base.Reset();
		}

		// Token: 0x06007777 RID: 30583 RVA: 0x00280C8C File Offset: 0x0027EE8C
		public void GetDropItemInfo()
		{
			bool flag = !(this.m_game.CurrentLiving is Player);
			if (!flag)
			{
				Player player = this.m_game.CurrentLiving as Player;
				SpecialItemDataInfo specialItemDataInfo = new SpecialItemDataInfo();
				List<ItemInfo> list = null;
				DropInventory.NPCDrop(this.m_npcInfo.DropId, ref list);
				bool flag2 = list == null;
				if (!flag2)
				{
					foreach (ItemInfo itemInfo in list)
					{
						ItemInfo.FindSpecialItemInfo(itemInfo, specialItemDataInfo);
						bool flag3 = itemInfo != null;
						if (flag3)
						{
							bool flag4 = itemInfo.Template.CategoryID == 10;
							if (flag4)
							{
								player.PlayerDetail.AddTemplate(itemInfo, eBageType.FightBag, itemInfo.Count);
							}
							else
							{
								player.PlayerDetail.AddTemplate(itemInfo, eBageType.TempBag, itemInfo.Count);
							}
						}
					}
					player.PlayerDetail.AddGold(specialItemDataInfo.Gold);
					player.PlayerDetail.AddMoney(specialItemDataInfo.Money);
					player.PlayerDetail.LogAddMoney(AddMoneyType.Award, AddMoneyType.Award_Drop, player.PlayerDetail.PlayerCharacter.ID, specialItemDataInfo.Money, player.PlayerDetail.PlayerCharacter.Money);
					player.PlayerDetail.AddGiftToken(specialItemDataInfo.GiftToken);
				}
			}
		}

		// Token: 0x06007778 RID: 30584 RVA: 0x00280E04 File Offset: 0x0027F004
		public override void PrepareNewTurn()
		{
			base.PrepareNewTurn();
			try
			{
				this.m_ai.OnBeginNewTurn();
			}
			catch (Exception ex)
			{
				SimpleNpc.log.ErrorFormat("SimpleNpc BeginNewTurn error:{1}", ex);
			}
		}

		// Token: 0x06007779 RID: 30585 RVA: 0x00280E50 File Offset: 0x0027F050
		public override void StartAttacking()
		{
			base.StartAttacking();
			try
			{
				this.m_ai.OnStartAttacking();
			}
			catch (Exception ex)
			{
				SimpleNpc.log.ErrorFormat("SimpleNpc StartAttacking error:{1}", ex);
			}
		}

		// Token: 0x0600777A RID: 30586 RVA: 0x00280E9C File Offset: 0x0027F09C
		public override void OnAfterTakedBomb()
		{
			try
			{
				this.m_ai.OnAfterTakedBomb();
			}
			catch (Exception ex)
			{
				SimpleNpc.log.ErrorFormat("SimpleNpc OnAfterTakedBomb error:{1}", ex);
			}
		}

		// Token: 0x0600777B RID: 30587 RVA: 0x00280EE0 File Offset: 0x0027F0E0
		public override void BeforeTakedDamage(Living source, ref int damageAmount, ref int criticalAmount)
		{
			try
			{
				this.m_ai.OnBeforeTakedDamage(source, ref damageAmount, ref criticalAmount);
			}
			catch (Exception ex)
			{
				SimpleNpc.log.ErrorFormat("SimpleNpc BeforeTakeDamage error:{0}", ex);
			}
			base.BeforeTakedDamage(source, ref damageAmount, ref criticalAmount);
		}

		// Token: 0x0600777C RID: 30588 RVA: 0x00280F34 File Offset: 0x0027F134
		public override void OnAfterTakeDamage(Living source)
		{
			try
			{
				this.m_ai.OnAfterTakeDamage(source);
			}
			catch (Exception ex)
			{
				SimpleNpc.log.ErrorFormat("SimpleNpc OnAfterTakedDamage error:{1}", ex);
			}
		}

		// Token: 0x0600777D RID: 30589 RVA: 0x00280F7C File Offset: 0x0027F17C
		public override void OnAfterTakedFrozen()
		{
			try
			{
				this.m_ai.OnAfterTakedFrozen();
			}
			catch (Exception ex)
			{
				SimpleNpc.log.ErrorFormat("SimpleBoss OnAfterTakedFrozen error:{1}", ex);
			}
		}

		// Token: 0x0600777E RID: 30590 RVA: 0x00280FC0 File Offset: 0x0027F1C0
		public override void Die()
		{
			this.GetDropItemInfo();
			base.Die();
			try
			{
				this.m_ai.OnDie();
			}
			catch (Exception ex)
			{
				SimpleNpc.log.ErrorFormat("SimpleNpc Die error:{1}", ex);
			}
		}

		// Token: 0x0600777F RID: 30591 RVA: 0x00281014 File Offset: 0x0027F214
		public override void Die(int delay)
		{
			this.GetDropItemInfo();
			base.Die(delay);
			try
			{
				this.m_ai.OnDie();
			}
			catch (Exception ex)
			{
				SimpleNpc.log.ErrorFormat("SimpleNpc Die error:{1}", ex);
			}
		}

		// Token: 0x06007780 RID: 30592 RVA: 0x00281068 File Offset: 0x0027F268
		public void DiedSay()
		{
			try
			{
				this.m_ai.OnDiedSay();
			}
			catch (Exception ex)
			{
				SimpleNpc.log.ErrorFormat("SimpleBoss DiedSay error {0}", ex);
			}
		}

		// Token: 0x06007781 RID: 30593 RVA: 0x002810AC File Offset: 0x0027F2AC
		public void DiedEvent()
		{
			try
			{
				this.m_ai.OnDiedEvent();
			}
			catch (Exception ex)
			{
				SimpleNpc.log.ErrorFormat("SimpleNpc DiedEvent error {0}", ex);
			}
		}

		// Token: 0x06007782 RID: 30594 RVA: 0x002810F0 File Offset: 0x0027F2F0
		public override void Dispose()
		{
			base.Dispose();
			try
			{
				this.m_ai.Dispose();
			}
			catch (Exception ex)
			{
				SimpleNpc.log.ErrorFormat("SimpleNpc Dispose error:{1}", ex);
			}
		}

		// Token: 0x04004604 RID: 17924
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04004605 RID: 17925
		private NpcInfo m_npcInfo;

		// Token: 0x04004606 RID: 17926
		private ABrain m_ai;

		// Token: 0x04004607 RID: 17927
		private int m_rank;
	}
}
