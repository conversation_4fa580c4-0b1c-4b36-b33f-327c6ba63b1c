﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C61 RID: 3169
	public class DefaultCondition : BaseCondition
	{
		// Token: 0x06007073 RID: 28787 RVA: 0x0002A421 File Offset: 0x00028621
		public DefaultCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x06007074 RID: 28788 RVA: 0x00005683 File Offset: 0x00003883
		public override void AddTrigger(GamePlayer player)
		{
		}

		// Token: 0x06007075 RID: 28789 RVA: 0x00005683 File Offset: 0x00003883
		public override void RemoveTrigger(GamePlayer player)
		{
		}

		// Token: 0x06007076 RID: 28790 RVA: 0x00068C5C File Offset: 0x00066E5C
		public override bool IsCompleted(GamePlayer player)
		{
			return false;
		}
	}
}
