﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D36 RID: 3382
	public class PetCritRate100 : BasePetEffect
	{
		// Token: 0x0600797C RID: 31100 RVA: 0x0002DA2F File Offset: 0x0002BC2F
		public PetCritRate100(int probability, int skillId, string elementID)
			: base(ePetEffectType.PetCritRate100, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x0600797D RID: 31101 RVA: 0x0028AB24 File Offset: 0x00288D24
		public override bool Start(Living living)
		{
			PetCritRate100 petCritRate = living.PetEffectList.GetOfType(ePetEffectType.PetCritRate100) as PetCritRate100;
			bool flag = petCritRate != null;
			bool flag2;
			if (flag)
			{
				petCritRate.m_probability = ((this.m_probability > petCritRate.m_probability) ? this.m_probability : petCritRate.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600797E RID: 31102 RVA: 0x0002DA5F File Offset: 0x0002BC5F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x0600797F RID: 31103 RVA: 0x0002DA88 File Offset: 0x0002BC88
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007980 RID: 31104 RVA: 0x0028AB84 File Offset: 0x00288D84
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.PetEffects.CritRate += 100;
			}
		}

		// Token: 0x06007981 RID: 31105 RVA: 0x0028ABC0 File Offset: 0x00288DC0
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.PetEffects.CritRate -= 100;
			}
		}

		// Token: 0x0400472C RID: 18220
		private int m_probability = 0;

		// Token: 0x0400472D RID: 18221
		private int m_currentId;
	}
}
