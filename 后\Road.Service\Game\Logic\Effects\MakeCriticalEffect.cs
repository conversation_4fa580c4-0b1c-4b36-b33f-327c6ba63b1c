﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EF0 RID: 3824
	public class MakeCriticalEffect : BasePlayerEffect
	{
		// Token: 0x0600834C RID: 33612 RVA: 0x000342EB File Offset: 0x000324EB
		public MakeCriticalEffect(int count, int probability)
			: base(eEffectType.MakeCriticalEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x0600834D RID: 33613 RVA: 0x002B3628 File Offset: 0x002B1828
		public override bool Start(Living living)
		{
			MakeCriticalEffect makeCriticalEffect = living.EffectList.GetOfType(eEffectType.MakeCriticalEffect) as MakeCriticalEffect;
			bool flag = makeCriticalEffect != null;
			bool flag2;
			if (flag)
			{
				makeCriticalEffect.m_probability = ((this.m_probability > makeCriticalEffect.m_probability) ? this.m_probability : makeCriticalEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600834E RID: 33614 RVA: 0x00034313 File Offset: 0x00032513
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.player_BeforePlayerShoot;
			player.TakePlayerDamage += this.player_BeforeTakeDamage;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x0600834F RID: 33615 RVA: 0x002B3684 File Offset: 0x002B1884
		private void player_BeforePlayerShoot(Player player, int ball)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				this.IsTrigger = true;
				player.AttackEffectTrigger = true;
				player.FlyingPartical = 65;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("MakeCriticalEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x06008350 RID: 33616 RVA: 0x000335DB File Offset: 0x000317DB
		private void player_AfterPlayerShooted(Player player)
		{
			player.FlyingPartical = 0;
		}

		// Token: 0x06008351 RID: 33617 RVA: 0x0003434F File Offset: 0x0003254F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.player_BeforePlayerShoot;
			player.TakePlayerDamage -= this.player_BeforeTakeDamage;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06008352 RID: 33618 RVA: 0x002B36FC File Offset: 0x002B18FC
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				criticalAmount = (int)((0.5 + living.Lucky * 0.0003) * (double)damageAmount);
			}
		}

		// Token: 0x0400520B RID: 21003
		private int m_count = 0;

		// Token: 0x0400520C RID: 21004
		private int m_probability = 0;
	}
}
