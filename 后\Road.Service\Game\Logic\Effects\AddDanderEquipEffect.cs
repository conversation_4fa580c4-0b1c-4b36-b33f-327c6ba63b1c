﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000ED5 RID: 3797
	public class AddDanderEquipEffect : BasePlayerEffect
	{
		// Token: 0x060082AF RID: 33455 RVA: 0x00033876 File Offset: 0x00031A76
		public AddDanderEquipEffect(int count, int probability)
			: base(eEffectType.AddDander)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x060082B0 RID: 33456 RVA: 0x002B169C File Offset: 0x002AF89C
		public override bool Start(Living living)
		{
			AddDanderEquipEffect addDanderEquipEffect = living.EffectList.GetOfType(eEffectType.AddDander) as AddDanderEquipEffect;
			bool flag = addDanderEquipEffect != null;
			bool flag2;
			if (flag)
			{
				this.m_probability = ((this.m_probability > addDanderEquipEffect.m_probability) ? this.m_probability : addDanderEquipEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082B1 RID: 33457 RVA: 0x0003389E File Offset: 0x00031A9E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.ChangeProperty;
		}

		// Token: 0x060082B2 RID: 33458 RVA: 0x000338B4 File Offset: 0x00031AB4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage -= this.ChangeProperty;
		}

		// Token: 0x060082B3 RID: 33459 RVA: 0x002B16F8 File Offset: 0x002AF8F8
		private void ChangeProperty(Living player, Living source, ref int damageAmount, ref int criticalAmount)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				bool flag2 = player is Player;
				if (flag2)
				{
					(player as Player).AddDander(this.m_count);
				}
				this.IsTrigger = true;
				player.DefenceEffectTrigger = true;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("AddDanderEquipEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051CF RID: 20943
		private int m_count = 0;

		// Token: 0x040051D0 RID: 20944
		private int m_probability = 0;
	}
}
