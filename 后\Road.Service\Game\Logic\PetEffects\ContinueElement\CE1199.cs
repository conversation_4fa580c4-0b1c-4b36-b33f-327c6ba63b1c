﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E93 RID: 3731
	public class CE1199 : BasePetEffect
	{
		// Token: 0x0600810C RID: 33036 RVA: 0x002AB6C0 File Offset: 0x002A98C0
		public CE1199(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1199, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600810D RID: 33037 RVA: 0x002AB740 File Offset: 0x002A9940
		public override bool Start(Living living)
		{
			CE1199 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1199) as CE1199;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600810E RID: 33038 RVA: 0x002AB7A0 File Offset: 0x002A99A0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = !this.IsTrigger;
			if (flag)
			{
				this.m_added = 50;
				player.PetEffects.CritRate += this.m_added;
				this.IsTrigger = true;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600810F RID: 33039 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008110 RID: 33040 RVA: 0x002AB80C File Offset: 0x002A9A0C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008111 RID: 33041 RVA: 0x002AB840 File Offset: 0x002A9A40
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.PetEffects.CritRate -= this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005042 RID: 20546
		private int m_type = 0;

		// Token: 0x04005043 RID: 20547
		private int m_count = 0;

		// Token: 0x04005044 RID: 20548
		private int m_probability = 0;

		// Token: 0x04005045 RID: 20549
		private int m_delay = 0;

		// Token: 0x04005046 RID: 20550
		private int m_coldDown = 0;

		// Token: 0x04005047 RID: 20551
		private int m_currentId;

		// Token: 0x04005048 RID: 20552
		private int m_added = 0;
	}
}
