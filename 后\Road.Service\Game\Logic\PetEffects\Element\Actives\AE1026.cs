﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D99 RID: 3481
	public class AE1026 : BasePetEffect
	{
		// Token: 0x06007B9B RID: 31643 RVA: 0x00293BEC File Offset: 0x00291DEC
		public AE1026(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1026, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B9C RID: 31644 RVA: 0x00293C6C File Offset: 0x00291E6C
		public override bool Start(Living living)
		{
			AE1026 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1026) as AE1026;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B9D RID: 31645 RVA: 0x0002F377 File Offset: 0x0002D577
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007B9E RID: 31646 RVA: 0x00293CCC File Offset: 0x00291ECC
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1026(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007B9F RID: 31647 RVA: 0x0002F38D File Offset: 0x0002D58D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004969 RID: 18793
		private int m_type = 0;

		// Token: 0x0400496A RID: 18794
		private int m_count = 0;

		// Token: 0x0400496B RID: 18795
		private int m_probability = 0;

		// Token: 0x0400496C RID: 18796
		private int m_delay = 0;

		// Token: 0x0400496D RID: 18797
		private int m_coldDown = 0;

		// Token: 0x0400496E RID: 18798
		private int m_currentId;

		// Token: 0x0400496F RID: 18799
		private int m_added = 0;
	}
}
