﻿using System;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Net;
using System.Text;
using System.Windows.Forms;
using Game.Manager.AddData;
using Game.Manager.Properties;
using SqlDataProvider.BaseClass;

namespace Game.Manager.Data
{
	// Token: 0x02000C7D RID: 3197
	public partial class EditShop : Form
	{
		// Token: 0x1700132F RID: 4911
		// (get) Token: 0x0600710B RID: 28939 RVA: 0x000056B7 File Offset: 0x000038B7
		private static string Resource
		{
			get
			{
				return ConfigurationManager.AppSettings["Resource"];
			}
		}

		// Token: 0x0600710C RID: 28940 RVA: 0x00253818 File Offset: 0x00251A18
		public EditShop()
		{
			this.InitializeComponent();
		}

		// Token: 0x0600710D RID: 28941 RVA: 0x00253870 File Offset: 0x00251A70
		private void EditShop_Load(object sender, EventArgs e)
		{
			string text = string.Empty;
			text = "SELECT ID,ShopID,TemplateID,BuyType,IsBind,Label,Beat,AUnit,APrice1,AValue1,BUnit,BPrice1,BValue1,CUnit,CPrice1,CValue1,LimitCount,IsCheap FROM [Shop]";
			this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
			this.InitDataSet();
		}

		// Token: 0x0600710E RID: 28942 RVA: 0x002538A8 File Offset: 0x00251AA8
		private void InitDataSet()
		{
			this.pageSize = 20;
			this.nMax = this.dtInfo.Rows.Count;
			this.pageCount = this.nMax / this.pageSize;
			bool flag = this.nMax % this.pageSize > 0;
			if (flag)
			{
				this.pageCount++;
			}
			this.pageCurrent = 1;
			this.nCurrent = 0;
			this.LoadData();
		}

		// Token: 0x0600710F RID: 28943 RVA: 0x00253920 File Offset: 0x00251B20
		private void LoadData()
		{
			DataTable dataTable = this.dtInfo.Clone();
			int num = ((this.pageCurrent != this.pageCount) ? (this.pageSize * this.pageCurrent) : this.nMax);
			int num2 = this.nCurrent;
			this.lblPageCount.Text = this.pageCount.ToString();
			this.txtCurrentPage.Text = Convert.ToString(this.pageCurrent);
			for (int i = num2; i < num; i++)
			{
				dataTable.ImportRow(this.dtInfo.Rows[i]);
				this.nCurrent++;
			}
			this.bdsInfo.DataSource = dataTable;
			this.bdnInfo.BindingSource = this.bdsInfo;
			this.EditShopBox.DataSource = this.bdsInfo;
			string text = string.Empty;
			string empty = string.Empty;
			DataTable shopList = ConnectDataBase.GetShopList();
			for (int j = 0; j < dataTable.Rows.Count; j++)
			{
				text = string.Format("SELECT TemplateID, Name FROM Shop_Goods where TemplateID = {0}", dataTable.Rows[j]["TemplateID"]);
				this.EditShopBox.Rows[j].Cells["ItemName"].Value = ((ConnectDataBase.GetDataSet(text).Tables[0].Rows.Count == 0) ? "未知" : ConnectDataBase.GetDataSet(text).Tables[0].Rows[0]["Name"].ToString());
			}
		}

		// Token: 0x06007110 RID: 28944 RVA: 0x00253ADC File Offset: 0x00251CDC
		private void EditShopBox_CellEndEdit(object sender, DataGridViewCellEventArgs e)
		{
			string dataPropertyName = this.EditShopBox.Columns[e.ColumnIndex].DataPropertyName;
			string text = this.EditShopBox.Rows[e.RowIndex].Cells["ID"].Value.ToString();
			string text2 = this.EditShopBox.CurrentCell.Value.ToString();
			string text3 = string.Concat(new string[] { "Update [dbo].[Shop] set ", dataPropertyName, "='", text2, "'Where Id = ", text });
			ConnectDataBase.GetNonQueryEffectedRow(text3);
		}

		// Token: 0x06007111 RID: 28945 RVA: 0x00253B84 File Offset: 0x00251D84
		private void Name_Button_Click(object sender, EventArgs e)
		{
			string text = string.Empty;
			bool flag = this.Name_Text.Text != "";
			if (flag)
			{
				text = "SELECT ID,ShopID,TemplateID,BuyType,IsBind,Label,Beat,AUnit,APrice1,AValue1,BUnit,BPrice1,BValue1,CUnit,CPrice1,CValue1,LimitCount,IsCheap FROM Shop where TemplateID = " + this.Name_Text.Text;
				this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
				this.InitDataSet();
			}
			else
			{
				text = "SELECT ID,ShopID,TemplateID,BuyType,IsBind,Label,Beat,AUnit,APrice1,AValue1,BUnit,BPrice1,BValue1,CUnit,CPrice1,CValue1,LimitCount,IsCheap FROM Shop";
				this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
				this.InitDataSet();
			}
		}

		// Token: 0x06007112 RID: 28946 RVA: 0x00253C10 File Offset: 0x00251E10
		private void FindItem_Btn_Click(object sender, EventArgs e)
		{
			this.TemplateID_CBox.Items.Clear();
			string text = string.Empty;
			SqlDataReader sqlDataReader = null;
			bool flag = this.ItemName_Text.Text != "";
			if (flag)
			{
				text = "SELECT TemplateID, Name FROM Shop_Goods WHERE Name LIKE '%" + this.ItemName_Text.Text + "%'";
				this.db = new DataBaseHelper("conString");
				this.db.ExecuteReader(text, ref sqlDataReader);
				while (sqlDataReader.Read())
				{
					this.TemplateID_CBox.Items.Add(sqlDataReader["TemplateID"].ToString() + " -- " + sqlDataReader["Name"].ToString());
				}
				this.TemplateID_CBox.SelectedIndex = 0;
				sqlDataReader.Close();
			}
			else
			{
				MessageBox.Show("请输入你需要查询的道具名称!");
			}
		}

		// Token: 0x06007113 RID: 28947 RVA: 0x00253CFC File Offset: 0x00251EFC
		public static string GetLink(string pic, int categoryId, int needSex)
		{
			string text = "";
			pic = pic.ToLower();
			string text2 = EditShop.Resource;
			string text3 = ((needSex == 1) ? "m" : "f");
			switch (categoryId)
			{
			case 1:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/head/", pic, "/icon_1.png" });
				goto IL_0479;
			case 2:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/glass/", pic, "/icon_1.png" });
				goto IL_0479;
			case 3:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/hair/", pic, "/icon_1.png" });
				goto IL_0479;
			case 4:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/eff/", pic, "/icon_1.png" });
				goto IL_0479;
			case 5:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/cloth/", pic, "/icon_1.png" });
				goto IL_0479;
			case 6:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/face/", pic, "/icon_1.png" });
				goto IL_0479;
			case 7:
			case 27:
			case 64:
				text = text + "image/arm/" + pic + "/1/icon.png";
				goto IL_0479;
			case 8:
			case 28:
				text = text + "image/equip/armlet/" + pic + "/icon.png";
				goto IL_0479;
			case 9:
			case 29:
				text = text + "image/equip/ring/" + pic + "/icon.png";
				goto IL_0479;
			case 11:
			case 20:
			case 24:
			case 30:
			case 34:
			case 35:
			case 36:
			case 37:
			case 40:
			case 53:
			case 60:
			case 61:
			case 62:
			case 68:
			case 69:
			case 71:
			case 72:
				text = text + "image/unfrightprop/" + pic + "/icon.png";
				goto IL_0479;
			case 12:
				text = text + "image/task/" + pic + "/icon.png";
				goto IL_0479;
			case 13:
				text = string.Concat(new string[] { text, "image/equip/", text3, "/suits/", pic, "/icon_1.png" });
				goto IL_0479;
			case 14:
				text = text + "image/equip/necklace/" + pic + "/icon.png";
				goto IL_0479;
			case 15:
				text = text + "image/equip/wing/" + pic + "/icon.png";
				goto IL_0479;
			case 16:
				text = text + "image/specialprop/chatball/" + pic + "/icon.png";
				goto IL_0479;
			case 17:
			case 31:
				text = text + "image/equip/offhand/" + pic + "/icon.png";
				goto IL_0479;
			case 18:
			case 66:
				text = text + "image/cardbox/" + pic + "/icon.png";
				goto IL_0479;
			case 19:
				text = text + "image/equip/recover/" + pic + "/icon.png";
				goto IL_0479;
			case 25:
				text = text + "image/gift/" + pic + "/icon.png";
				goto IL_0479;
			case 26:
				text = text + "image/card/" + pic + "/icon.jpg";
				goto IL_0479;
			case 33:
				text = text + "image/farm/fertilizer/" + pic + "/icon.png";
				goto IL_0479;
			case 50:
				text = text + "image/petequip/arm/" + pic + "/icon.png";
				goto IL_0479;
			case 51:
				text = text + "image/petequip/hat/" + pic + "/icon.png";
				goto IL_0479;
			case 52:
				text = text + "image/petequip/cloth/" + pic + "/icon.png";
				goto IL_0479;
			case 80:
				text = text + "image/prop/" + pic + "/icon.png";
				goto IL_0479;
			}
			text2 = null;
			IL_0479:
			return text2 + text;
		}

		// Token: 0x06007114 RID: 28948 RVA: 0x0003A918 File Offset: 0x00038B18
		public static string GetWebStatusCode(string url, int timeout)
		{
			HttpWebRequest httpWebRequest = null;
			string text;
			try
			{
				httpWebRequest = (HttpWebRequest)WebRequest.CreateDefault(new Uri(url));
				httpWebRequest.Method = "HEAD";
				httpWebRequest.Timeout = timeout;
				HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				text = Convert.ToInt32(httpWebResponse.StatusCode).ToString();
			}
			catch (Exception ex)
			{
				text = ex.Message;
			}
			finally
			{
				bool flag = httpWebRequest != null;
				if (flag)
				{
					httpWebRequest.Abort();
					httpWebRequest = null;
				}
			}
			return text;
		}

		// Token: 0x06007115 RID: 28949 RVA: 0x0003A428 File Offset: 0x00038628
		private static string GetIDByChar(string CBoxText)
		{
			string[] array = CBoxText.Split(new string[] { " -- " }, StringSplitOptions.None);
			return array[0];
		}

		// Token: 0x06007116 RID: 28950 RVA: 0x0003A9B8 File Offset: 0x00038BB8
		public static bool isEncryed(string url)
		{
			WebClient webClient = new WebClient();
			string @string = Encoding.UTF8.GetString(webClient.DownloadData(url));
			return @string.Contains("^_^");
		}

		// Token: 0x06007117 RID: 28951 RVA: 0x00254190 File Offset: 0x00252390
		private void TemplateID_CBox_SelectedIndexChanged(object sender, EventArgs e)
		{
			SqlDataReader sqlDataReader = null;
			string idbyChar = EditShop.GetIDByChar(this.TemplateID_CBox.Text);
			this.Name_Text.Text = idbyChar;
			string text = "SELECT CateGoryID, Pic, NeedSex,MaxCount FROM Shop_Goods WHERE TemplateID = " + idbyChar;
			this.db = new DataBaseHelper("conString");
			this.db.ExecuteReader(text, ref sqlDataReader);
			bool flag = sqlDataReader.Read();
			if (flag)
			{
				string link = EditShop.GetLink(sqlDataReader["Pic"].ToString(), (int)sqlDataReader["CateGoryID"], (int)sqlDataReader["NeedSex"]);
				bool flag2 = !link.Equals("") && EditShop.GetWebStatusCode(link, 2000) != "远程服务器返回错误: (404) 未找到。";
				if (flag2)
				{
					bool flag3 = !EditShop.isEncryed(link);
					if (flag3)
					{
						this.pictureBox1.Load(link);
					}
				}
				else
				{
					this.pictureBox1.Image = Resources._1_asset_core_icon_2;
				}
			}
			else
			{
				this.pictureBox1.Image = Resources._1_asset_core_icon_2;
			}
			sqlDataReader.Close();
		}

		// Token: 0x06007118 RID: 28952 RVA: 0x002542B0 File Offset: 0x002524B0
		private void AddShop_Click(object sender, EventArgs e)
		{
			bool flag = this.AddShopForm == null || this.AddShopForm.IsDisposed;
			if (flag)
			{
				this.AddShopForm = new AddShopBox();
				this.AddShopForm.Show();
			}
		}

		// Token: 0x06007119 RID: 28953 RVA: 0x002542F4 File Offset: 0x002524F4
		private void LastPage_Click(object sender, EventArgs e)
		{
			this.pageCurrent--;
			bool flag = this.pageCurrent <= 0;
			if (flag)
			{
				MessageBox.Show("已经是第一页，请点击“下一页”查看！");
			}
			else
			{
				this.nCurrent = this.pageSize * (this.pageCurrent - 1);
				this.LoadData();
			}
		}

		// Token: 0x0600711A RID: 28954 RVA: 0x0025434C File Offset: 0x0025254C
		private void NextPage_Click(object sender, EventArgs e)
		{
			this.pageCurrent++;
			bool flag = this.pageCurrent > this.pageCount;
			if (flag)
			{
				MessageBox.Show("已经是最后一页，请点击“上一页”查看！");
			}
			else
			{
				this.nCurrent = this.pageSize * (this.pageCurrent - 1);
				this.LoadData();
			}
		}

		// Token: 0x0600711B RID: 28955 RVA: 0x002543A4 File Offset: 0x002525A4
		private void ID_Button_Click(object sender, EventArgs e)
		{
			string text = string.Empty;
			bool flag = this.Name_Text.Text != "";
			if (flag)
			{
				text = "SELECT ID,ShopID,TemplateID,BuyType,IsBind,Label,Beat,AUnit,APrice1,AValue1,BUnit,BPrice1,BValue1,CUnit,CPrice1,CValue1,LimitCount,IsCheap FROM Shop where ID = " + this.Name_Text.Text;
				this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
				this.InitDataSet();
			}
			else
			{
				text = "SELECT ID,ShopID,TemplateID,BuyType,IsBind,Label,Beat,AUnit,APrice1,AValue1,BUnit,BPrice1,BValue1,CUnit,CPrice1,CValue1,LimitCount,IsCheap FROM Shop";
				this.dtInfo = ConnectDataBase.GetDataSet(text).Tables[0];
				this.InitDataSet();
			}
		}

		// Token: 0x04003CBD RID: 15549
		private DataBaseHelper db;

		// Token: 0x04003CBE RID: 15550
		private int pageSize = 0;

		// Token: 0x04003CBF RID: 15551
		private int nMax = 0;

		// Token: 0x04003CC0 RID: 15552
		private int pageCount = 0;

		// Token: 0x04003CC1 RID: 15553
		private int pageCurrent = 0;

		// Token: 0x04003CC2 RID: 15554
		private int nCurrent = 0;

		// Token: 0x04003CC3 RID: 15555
		private DataTable dtInfo = new DataTable();

		// Token: 0x04003CC4 RID: 15556
		public AddShopBox AddShopForm = null;
	}
}
