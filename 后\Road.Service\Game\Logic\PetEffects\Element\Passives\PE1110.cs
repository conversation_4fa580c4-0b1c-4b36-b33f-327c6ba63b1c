﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D63 RID: 3427
	public class PE1110 : BasePetEffect
	{
		// Token: 0x06007A86 RID: 31366 RVA: 0x0028F2EC File Offset: 0x0028D4EC
		public PE1110(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1110, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A87 RID: 31367 RVA: 0x0028F36C File Offset: 0x0028D56C
		public override bool Start(Living living)
		{
			PE1110 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1110) as PE1110;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A88 RID: 31368 RVA: 0x0002E83C File Offset: 0x0002CA3C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
		}

		// Token: 0x06007A89 RID: 31369 RVA: 0x0028F3CC File Offset: 0x0028D5CC
		private void Player_BeginSelfTurn(Living living)
		{
			PE1107 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1107) as PE1107;
			bool flag = pe != null;
			if (flag)
			{
				pe.Stop();
			}
		}

		// Token: 0x06007A8A RID: 31370 RVA: 0x0002E852 File Offset: 0x0002CA52
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x040047F5 RID: 18421
		private int m_type = 0;

		// Token: 0x040047F6 RID: 18422
		private int m_count = 0;

		// Token: 0x040047F7 RID: 18423
		private int m_probability = 0;

		// Token: 0x040047F8 RID: 18424
		private int m_delay = 0;

		// Token: 0x040047F9 RID: 18425
		private int m_coldDown = 0;

		// Token: 0x040047FA RID: 18426
		private int m_currentId;

		// Token: 0x040047FB RID: 18427
		private int m_added = 0;
	}
}
