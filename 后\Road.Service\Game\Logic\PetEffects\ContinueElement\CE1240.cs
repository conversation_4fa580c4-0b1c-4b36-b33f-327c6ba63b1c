﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EAB RID: 3755
	public class CE1240 : BasePetEffect
	{
		// Token: 0x060081A8 RID: 33192 RVA: 0x002ADC08 File Offset: 0x002ABE08
		public CE1240(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1240, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081A9 RID: 33193 RVA: 0x002ADC88 File Offset: 0x002ABE88
		public override bool Start(Living living)
		{
			CE1240 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1240) as CE1240;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081AA RID: 33194 RVA: 0x002ADCE8 File Offset: 0x002ABEE8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 115;
				player.BaseDamage += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081AB RID: 33195 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081AC RID: 33196 RVA: 0x002ADD48 File Offset: 0x002ABF48
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060081AD RID: 33197 RVA: 0x002ADD7C File Offset: 0x002ABF7C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, false);
				player.BaseDamage -= (double)this.m_added;
				this.m_added = 0;
			}
		}

		// Token: 0x040050EA RID: 20714
		private int m_type = 0;

		// Token: 0x040050EB RID: 20715
		private int m_count = 0;

		// Token: 0x040050EC RID: 20716
		private int m_probability = 0;

		// Token: 0x040050ED RID: 20717
		private int m_delay = 0;

		// Token: 0x040050EE RID: 20718
		private int m_coldDown = 0;

		// Token: 0x040050EF RID: 20719
		private int m_currentId;

		// Token: 0x040050F0 RID: 20720
		private int m_added = 0;
	}
}
