﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F15 RID: 3861
	[GameCommand(17, "自杀")]
	public class SuicideCommand : ICommandHandler
	{
		// Token: 0x060083C3 RID: 33731 RVA: 0x002B57DC File Offset: 0x002B39DC
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool flag = player.IsLiving && game.GameState == eGameState.Playing;
			if (flag)
			{
				packet.Parameter1 = player.Id;
				game.SendToAll(packet);
				player.Die();
				game.CheckState(0);
			}
		}
	}
}
