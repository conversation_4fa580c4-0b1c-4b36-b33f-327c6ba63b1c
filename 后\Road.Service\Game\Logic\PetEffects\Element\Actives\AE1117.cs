﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DCE RID: 3534
	public class AE1117 : BasePetEffect
	{
		// Token: 0x06007CAB RID: 31915 RVA: 0x00298894 File Offset: 0x00296A94
		public AE1117(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1117, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CAC RID: 31916 RVA: 0x00298914 File Offset: 0x00296B14
		public override bool Start(Living living)
		{
			AE1117 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1117) as AE1117;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CAD RID: 31917 RVA: 0x0002FCEF File Offset: 0x0002DEEF
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007CAE RID: 31918 RVA: 0x0002FD05 File Offset: 0x0002DF05
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007CAF RID: 31919 RVA: 0x00298974 File Offset: 0x00296B74
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && player.Game is PVPGame;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1117(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004ADC RID: 19164
		private int m_type = 0;

		// Token: 0x04004ADD RID: 19165
		private int m_count = 0;

		// Token: 0x04004ADE RID: 19166
		private int m_probability = 0;

		// Token: 0x04004ADF RID: 19167
		private int m_delay = 0;

		// Token: 0x04004AE0 RID: 19168
		private int m_coldDown = 0;

		// Token: 0x04004AE1 RID: 19169
		private int m_currentId;

		// Token: 0x04004AE2 RID: 19170
		private int m_added = 0;
	}
}
