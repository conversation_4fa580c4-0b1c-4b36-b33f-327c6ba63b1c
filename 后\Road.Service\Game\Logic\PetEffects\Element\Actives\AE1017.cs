﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D90 RID: 3472
	public class AE1017 : BasePetEffect
	{
		// Token: 0x06007B6A RID: 31594 RVA: 0x00292D7C File Offset: 0x00290F7C
		public AE1017(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1017, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B6B RID: 31595 RVA: 0x00292DFC File Offset: 0x00290FFC
		public override bool Start(Living living)
		{
			AE1017 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1017) as AE1017;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B6C RID: 31596 RVA: 0x0002F153 File Offset: 0x0002D353
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007B6D RID: 31597 RVA: 0x0002F169 File Offset: 0x0002D369
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007B6E RID: 31598 RVA: 0x00292E5C File Offset: 0x0029105C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1017(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x0400492A RID: 18730
		private int m_type = 0;

		// Token: 0x0400492B RID: 18731
		private int m_count = 0;

		// Token: 0x0400492C RID: 18732
		private int m_probability = 0;

		// Token: 0x0400492D RID: 18733
		private int m_delay = 0;

		// Token: 0x0400492E RID: 18734
		private int m_coldDown = 0;

		// Token: 0x0400492F RID: 18735
		private int m_currentId;

		// Token: 0x04004930 RID: 18736
		private int m_added = 0;
	}
}
