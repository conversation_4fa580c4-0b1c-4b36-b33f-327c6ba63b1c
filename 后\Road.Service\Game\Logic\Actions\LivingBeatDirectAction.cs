﻿using System;
using Bussiness;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F50 RID: 3920
	public class LivingBeatDirectAction : BaseAction
	{
		// Token: 0x060084F7 RID: 34039 RVA: 0x000352D1 File Offset: 0x000334D1
		public LivingBeatDirectAction(Living living, Living target, string action, int delay, int livingCount, int attackEffect)
			: base(delay)
		{
			this.m_living = living;
			this.m_target = target;
			this.m_action = action;
			this.m_livingCount = livingCount;
			this.m_attackEffect = attackEffect;
		}

		// Token: 0x060084F8 RID: 34040 RVA: 0x002B9488 File Offset: 0x002B7688
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			this.m_target.SyncAtTime = false;
			try
			{
				GSPacketIn gspacketIn = new GSPacketIn(91, this.m_living.Id);
				gspacketIn.Parameter1 = this.m_living.Id;
				gspacketIn.WriteByte(58);
				gspacketIn.WriteString((!string.IsNullOrEmpty(this.m_action)) ? this.m_action : "");
				gspacketIn.WriteInt(this.m_livingCount);
				for (int i = 1; i <= this.m_livingCount; i++)
				{
					int num = this.m_living.MakeDamage(this.m_target);
					int num2 = this.MakeCriticalDamage(num);
					int num3 = 0;
					bool flag = this.m_target is Player;
					if (flag)
					{
						num3 = (this.m_target as Player).Dander;
					}
					bool isFrost = this.m_target.IsFrost;
					if (isFrost)
					{
						this.m_target.IsFrost = false;
						game.SendGameUpdateFrozenState(this.m_target);
					}
					bool flag2 = !this.m_target.TakeDamage(this.m_living, ref num, ref num2, "小怪伤血");
					if (flag2)
					{
						Console.WriteLine("//error beat direct damage");
					}
					gspacketIn.WriteInt(this.m_target.Id);
					gspacketIn.WriteInt(num + num2);
					gspacketIn.WriteInt(this.m_target.Blood);
					gspacketIn.WriteInt(num3);
					gspacketIn.WriteInt(this.m_attackEffect);
				}
				game.SendToAll(gspacketIn);
				base.Finish(tick);
			}
			finally
			{
				this.m_target.SyncAtTime = true;
			}
		}

		// Token: 0x060084F9 RID: 34041 RVA: 0x002B9648 File Offset: 0x002B7848
		protected int MakeCriticalDamage(int baseDamage)
		{
			double lucky = this.m_living.Lucky;
			bool flag = lucky * 45.0 / (800.0 + lucky) > (double)ThreadSafeRandom.NextStatic(100);
			int num2;
			if (flag)
			{
				int num = (int)((0.5 + lucky * 0.00015) * (double)baseDamage);
				num /= 100;
				bool flag2 = this.m_living.FightBuffers.ConsortionAddCritical > 0;
				if (flag2)
				{
					num += this.m_living.FightBuffers.ConsortionAddCritical;
				}
				num2 = num;
			}
			else
			{
				num2 = 0;
			}
			return num2;
		}

		// Token: 0x040052DE RID: 21214
		private Living m_living;

		// Token: 0x040052DF RID: 21215
		private Living m_target;

		// Token: 0x040052E0 RID: 21216
		private string m_action;

		// Token: 0x040052E1 RID: 21217
		private int m_livingCount;

		// Token: 0x040052E2 RID: 21218
		private int m_attackEffect;
	}
}
