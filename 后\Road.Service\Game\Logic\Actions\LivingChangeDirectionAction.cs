﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F53 RID: 3923
	public class LivingChangeDirectionAction : BaseAction
	{
		// Token: 0x060084FE RID: 34046 RVA: 0x00035346 File Offset: 0x00033546
		public LivingChangeDirectionAction(Living living, int direction, int delay)
			: base(delay)
		{
			this.m_Living = living;
			this.m_direction = direction;
		}

		// Token: 0x060084FF RID: 34047 RVA: 0x0003535F File Offset: 0x0003355F
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			this.m_Living.Direction = this.m_direction;
			base.Finish(tick);
		}

		// Token: 0x040052E9 RID: 21225
		private Living m_Living;

		// Token: 0x040052EA RID: 21226
		private int m_direction;
	}
}
