﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DAC RID: 3500
	public class AE1050 : BasePetEffect
	{
		// Token: 0x06007BFC RID: 31740 RVA: 0x00295554 File Offset: 0x00293754
		public AE1050(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1050, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BFD RID: 31741 RVA: 0x002955D0 File Offset: 0x002937D0
		public override bool Start(Living living)
		{
			AE1050 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1050) as AE1050;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BFE RID: 31742 RVA: 0x0002F707 File Offset: 0x0002D907
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BFF RID: 31743 RVA: 0x0002F71D File Offset: 0x0002D91D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C00 RID: 31744 RVA: 0x0029562C File Offset: 0x0029382C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1050(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x040049EE RID: 18926
		private int m_type = 0;

		// Token: 0x040049EF RID: 18927
		private int m_count = 0;

		// Token: 0x040049F0 RID: 18928
		private int m_probability = 0;

		// Token: 0x040049F1 RID: 18929
		private int m_delay = 0;

		// Token: 0x040049F2 RID: 18930
		private int m_coldDown = 0;

		// Token: 0x040049F3 RID: 18931
		private int m_currentId;

		// Token: 0x040049F4 RID: 18932
		private int m_added = 0;
	}
}
