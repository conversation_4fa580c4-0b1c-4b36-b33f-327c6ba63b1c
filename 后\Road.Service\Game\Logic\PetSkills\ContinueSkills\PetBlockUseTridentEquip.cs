﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D07 RID: 3335
	public class PetBlockUseTridentEquip : BasePetEffect
	{
		// Token: 0x06007880 RID: 30848 RVA: 0x0002CACC File Offset: 0x0002ACCC
		public PetBlockUseTridentEquip(int count, string elementID)
			: base(ePetEffectType.PetBlockUseTridentEquip, elementID)
		{
			this.m_count = count;
		}

		// Token: 0x06007881 RID: 30849 RVA: 0x00286728 File Offset: 0x00284928
		public override bool Start(Living living)
		{
			PetBlockUseTridentEquip petBlockUseTridentEquip = living.PetEffectList.GetOfType(ePetEffectType.PetBlockUseTridentEquip) as PetBlockUseTridentEquip;
			bool flag = petBlockUseTridentEquip != null;
			bool flag2;
			if (flag)
			{
				petBlockUseTridentEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007882 RID: 30850 RVA: 0x0002CAE3 File Offset: 0x0002ACE3
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
			player.lockProp(10003);
			player.Prop.Remove(10003);
		}

		// Token: 0x06007883 RID: 30851 RVA: 0x0002CB16 File Offset: 0x0002AD16
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
			player.unlockProp(10003);
		}

		// Token: 0x06007884 RID: 30852 RVA: 0x00286770 File Offset: 0x00284970
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = base.ElementInfo.Pic != -1;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.Info, false);
			}
			bool flag2 = this.m_count < 0;
			if (flag2)
			{
				living.Game.sendShowPicSkil(living, base.Info, false);
				this.Stop();
			}
		}

		// Token: 0x040046A6 RID: 18086
		private int m_count;
	}
}
