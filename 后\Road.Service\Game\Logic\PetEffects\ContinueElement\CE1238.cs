﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EA9 RID: 3753
	public class CE1238 : BasePetEffect
	{
		// Token: 0x0600819C RID: 33180 RVA: 0x002AD864 File Offset: 0x002ABA64
		public CE1238(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1238, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600819D RID: 33181 RVA: 0x002AD8E4 File Offset: 0x002ABAE4
		public override bool Start(Living living)
		{
			CE1238 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1238) as CE1238;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600819E RID: 33182 RVA: 0x002AD944 File Offset: 0x002ABB44
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 45;
				player.BaseDamage += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600819F RID: 33183 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081A0 RID: 33184 RVA: 0x002AD9A4 File Offset: 0x002ABBA4
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060081A1 RID: 33185 RVA: 0x002AD9D8 File Offset: 0x002ABBD8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, false);
				player.BaseDamage -= (double)this.m_added;
				this.m_added = 0;
			}
		}

		// Token: 0x040050DC RID: 20700
		private int m_type = 0;

		// Token: 0x040050DD RID: 20701
		private int m_count = 0;

		// Token: 0x040050DE RID: 20702
		private int m_probability = 0;

		// Token: 0x040050DF RID: 20703
		private int m_delay = 0;

		// Token: 0x040050E0 RID: 20704
		private int m_coldDown = 0;

		// Token: 0x040050E1 RID: 20705
		private int m_currentId;

		// Token: 0x040050E2 RID: 20706
		private int m_added = 0;
	}
}
