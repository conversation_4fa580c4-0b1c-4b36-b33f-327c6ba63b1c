﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E27 RID: 3623
	public class AE1270 : BasePetEffect
	{
		// Token: 0x06007E7D RID: 32381 RVA: 0x002A0B84 File Offset: 0x0029ED84
		public AE1270(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1270, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E7E RID: 32382 RVA: 0x002A0C04 File Offset: 0x0029EE04
		public override bool Start(Living living)
		{
			AE1270 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1270) as AE1270;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E7F RID: 32383 RVA: 0x00030F7B File Offset: 0x0002F17B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
		}

		// Token: 0x06007E80 RID: 32384 RVA: 0x0003097B File Offset: 0x0002EB7B
		private void Player_BeginSelfTurn(Living living)
		{
			this.IsTrigger = false;
		}

		// Token: 0x06007E81 RID: 32385 RVA: 0x002A0C64 File Offset: 0x0029EE64
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				target.AddPetEffect(new CE1270(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString(), living), 0);
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007E82 RID: 32386 RVA: 0x00030FB7 File Offset: 0x0002F1B7
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x06007E83 RID: 32387 RVA: 0x002A0CC0 File Offset: 0x0029EEC0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x04004D49 RID: 19785
		private int m_type = 0;

		// Token: 0x04004D4A RID: 19786
		private int m_count = 0;

		// Token: 0x04004D4B RID: 19787
		private int m_probability = 0;

		// Token: 0x04004D4C RID: 19788
		private int m_delay = 0;

		// Token: 0x04004D4D RID: 19789
		private int m_coldDown = 0;

		// Token: 0x04004D4E RID: 19790
		private int m_currentId;

		// Token: 0x04004D4F RID: 19791
		private int m_added = 0;
	}
}
