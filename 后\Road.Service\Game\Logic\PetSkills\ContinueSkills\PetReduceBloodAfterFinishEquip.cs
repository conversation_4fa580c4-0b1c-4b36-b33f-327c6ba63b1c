﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D11 RID: 3345
	public class PetReduceBloodAfterFinishEquip : BasePetEffect
	{
		// Token: 0x060078B6 RID: 30902 RVA: 0x00287440 File Offset: 0x00285640
		public PetReduceBloodAfterFinishEquip(int count, string elementID)
			: base(ePetEffectType.PetReduceBloodAfterFinishEquip, elementID)
		{
			this.m_count = count;
			bool flag = elementID == "1745";
			if (flag)
			{
				this.m_value = 20;
				this.percent = true;
			}
		}

		// Token: 0x060078B7 RID: 30903 RVA: 0x0002CE13 File Offset: 0x0002B013
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x060078B8 RID: 30904 RVA: 0x0002CE29 File Offset: 0x0002B029
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x060078B9 RID: 30905 RVA: 0x00287484 File Offset: 0x00285684
		public override bool Start(Living living)
		{
			PetReduceBloodAfterFinishEquip petReduceBloodAfterFinishEquip = living.PetEffectList.GetOfType(ePetEffectType.PetReduceBloodAfterFinishEquip) as PetReduceBloodAfterFinishEquip;
			bool flag = petReduceBloodAfterFinishEquip != null;
			bool flag2;
			if (flag)
			{
				petReduceBloodAfterFinishEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078BA RID: 30906 RVA: 0x002874CC File Offset: 0x002856CC
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = base.ElementInfo.Pic != -1;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
			}
			bool flag2 = this.m_count <= 0;
			if (flag2)
			{
				int num = ((!this.percent) ? this.m_value : Convert.ToInt32(living.MaxBlood * this.m_value / 100));
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				living.SyncAtTime = true;
				bool flag3 = num >= living.Blood;
				if (flag3)
				{
					living.RemoveBlood(living.Blood - 1);
				}
				else
				{
					living.RemoveBlood(num);
				}
				living.SyncAtTime = false;
				this.Stop();
			}
		}

		// Token: 0x040046BD RID: 18109
		private int m_value;

		// Token: 0x040046BE RID: 18110
		private int m_count;

		// Token: 0x040046BF RID: 18111
		private bool percent;
	}
}
