﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E2A RID: 3626
	public class AE1419 : BasePetEffect
	{
		// Token: 0x06007E91 RID: 32401 RVA: 0x002A0FD0 File Offset: 0x0029F1D0
		public AE1419(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1419, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E92 RID: 32402 RVA: 0x002A1050 File Offset: 0x0029F250
		public override bool Start(Living living)
		{
			AE1419 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1419) as AE1419;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E93 RID: 32403 RVA: 0x000310BD File Offset: 0x0002F2BD
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007E94 RID: 32404 RVA: 0x000310E6 File Offset: 0x0002F2E6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007E95 RID: 32405 RVA: 0x002A10B0 File Offset: 0x0029F2B0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 180;
				player.CurrentDamagePlus += (float)(this.m_added / 100);
			}
		}

		// Token: 0x06007E96 RID: 32406 RVA: 0x002A10FC File Offset: 0x0029F2FC
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_added / 100);
				this.m_added = 0;
			}
		}

		// Token: 0x04004D5E RID: 19806
		private int m_type = 0;

		// Token: 0x04004D5F RID: 19807
		private int m_count = 0;

		// Token: 0x04004D60 RID: 19808
		private int m_probability = 0;

		// Token: 0x04004D61 RID: 19809
		private int m_delay = 0;

		// Token: 0x04004D62 RID: 19810
		private int m_coldDown = 0;

		// Token: 0x04004D63 RID: 19811
		private int m_currentId;

		// Token: 0x04004D64 RID: 19812
		private int m_added = 0;
	}
}
