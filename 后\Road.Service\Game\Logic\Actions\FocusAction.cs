﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F49 RID: 3913
	public class FocusAction : BaseAction
	{
		// Token: 0x060084E8 RID: 34024 RVA: 0x000351A8 File Offset: 0x000333A8
		public FocusAction(Physics obj, int type, int delay, int finishTime)
			: base(delay, finishTime)
		{
			this.m_obj = obj;
			this.m_type = type;
		}

		// Token: 0x060084E9 RID: 34025 RVA: 0x000351C3 File Offset: 0x000333C3
		public FocusAction(int x, int y, int type, int delay, int finishTime)
			: base(delay, finishTime)
		{
			this.m_x = x;
			this.m_y = y;
			this.m_type = type;
		}

		// Token: 0x060084EA RID: 34026 RVA: 0x000351E6 File Offset: 0x000333E6
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			game.SendPhysicalObjFocus(this.m_obj, this.m_type);
			game.SendPhysicalObjFocus(this.m_x, this.m_y, this.m_type);
			base.Finish(tick);
		}

		// Token: 0x040052C9 RID: 21193
		private Physics m_obj;

		// Token: 0x040052CA RID: 21194
		private int m_type;

		// Token: 0x040052CB RID: 21195
		private int m_x;

		// Token: 0x040052CC RID: 21196
		private int m_y;
	}
}
