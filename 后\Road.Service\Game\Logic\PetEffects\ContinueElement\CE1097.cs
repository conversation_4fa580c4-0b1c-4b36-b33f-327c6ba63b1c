﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E67 RID: 3687
	public class CE1097 : BasePetEffect
	{
		// Token: 0x06007FF6 RID: 32758 RVA: 0x002A753C File Offset: 0x002A573C
		public CE1097(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1097, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FF7 RID: 32759 RVA: 0x002A75BC File Offset: 0x002A57BC
		public override bool Start(Living living)
		{
			CE1097 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1097) as CE1097;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FF8 RID: 32760 RVA: 0x002A761C File Offset: 0x002A581C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				player.Agility += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007FF9 RID: 32761 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FFA RID: 32762 RVA: 0x002A7680 File Offset: 0x002A5880
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007FFB RID: 32763 RVA: 0x00031D26 File Offset: 0x0002FF26
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Agility -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F0C RID: 20236
		private int m_type = 0;

		// Token: 0x04004F0D RID: 20237
		private int m_count = 0;

		// Token: 0x04004F0E RID: 20238
		private int m_probability = 0;

		// Token: 0x04004F0F RID: 20239
		private int m_delay = 0;

		// Token: 0x04004F10 RID: 20240
		private int m_coldDown = 0;

		// Token: 0x04004F11 RID: 20241
		private int m_currentId;

		// Token: 0x04004F12 RID: 20242
		private int m_added = 0;
	}
}
