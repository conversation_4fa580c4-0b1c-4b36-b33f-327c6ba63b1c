﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using Bussiness;
using Bussiness.CenterService;
using Bussiness.Managers;
using EntityDatabase.PlayerModels;
using EntityDatabase.ServerModels;
using Game.Base.Packets;
using Game.Logic.Actions;
using Game.Logic.AI;
using Game.Logic.AI.Game;
using Game.Logic.AI.Mission;
using Game.Logic.Phy.Maps;
using Game.Logic.Phy.Object;
using Game.Server.Managers;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000CAA RID: 3242
	public class PVEGame : BaseGame
	{
		// Token: 0x06007430 RID: 29744 RVA: 0x00266284 File Offset: 0x00264484
		public List<SimpleNpc> FindAllLivingNpc()
		{
			return this.m_livings.OfType<SimpleNpc>().ToList<SimpleNpc>();
		}

		// Token: 0x170013F4 RID: 5108
		// (get) Token: 0x06007431 RID: 29745 RVA: 0x0002B311 File Offset: 0x00029511
		public AMissionControl MissionAI
		{
			get
			{
				return this.m_missionAI;
			}
		}

		// Token: 0x170013F5 RID: 5109
		// (get) Token: 0x06007432 RID: 29746 RVA: 0x0002B319 File Offset: 0x00029519
		// (set) Token: 0x06007433 RID: 29747 RVA: 0x0002B321 File Offset: 0x00029521
		private DateTime beginTime { get; set; }

		// Token: 0x170013F6 RID: 5110
		// (get) Token: 0x06007434 RID: 29748 RVA: 0x002662A8 File Offset: 0x002644A8
		// (set) Token: 0x06007435 RID: 29749 RVA: 0x0002B32A File Offset: 0x0002952A
		public int CountMosterPlace
		{
			get
			{
				return this.m_countAward;
			}
			set
			{
				this.m_countAward = value;
			}
		}

		// Token: 0x170013F7 RID: 5111
		// (get) Token: 0x06007436 RID: 29750 RVA: 0x0002B334 File Offset: 0x00029534
		public PveInfo Info
		{
			get
			{
				return this.m_info;
			}
		}

		// Token: 0x170013F8 RID: 5112
		// (get) Token: 0x06007437 RID: 29751 RVA: 0x0002B33C File Offset: 0x0002953C
		public Dictionary<int, int> NpcTurnQueue
		{
			get
			{
				return this.m_NpcTurnQueue;
			}
		}

		// Token: 0x170013F9 RID: 5113
		// (get) Token: 0x06007438 RID: 29752 RVA: 0x002662C0 File Offset: 0x002644C0
		public bool IsNpcTurn
		{
			get
			{
				List<Living> livedLivingsHadTurn = base.GetLivedLivingsHadTurn();
				int num = this.FindTurnNpcRank();
				return livedLivingsHadTurn.Count > 0 && this.NpcTurnQueue.Count > 0 && this.m_currentLiving.Delay > this.NpcTurnQueue[num];
			}
		}

		// Token: 0x170013FA RID: 5114
		// (get) Token: 0x06007439 RID: 29753 RVA: 0x00266320 File Offset: 0x00264520
		// (set) Token: 0x0600743A RID: 29754 RVA: 0x0002B344 File Offset: 0x00029544
		public MissionInfo MissionInfo
		{
			get
			{
				return this.m_missionInfo;
			}
			set
			{
				this.m_missionInfo = value;
			}
		}

		// Token: 0x170013FB RID: 5115
		// (get) Token: 0x0600743B RID: 29755 RVA: 0x0002B34E File Offset: 0x0002954E
		public Player CurrentPlayer
		{
			get
			{
				return this.m_currentLiving as Player;
			}
		}

		// Token: 0x170013FC RID: 5116
		// (get) Token: 0x0600743C RID: 29756 RVA: 0x0002A94F File Offset: 0x00028B4F
		public TurnedLiving CurrentTurnLiving
		{
			get
			{
				return this.m_currentLiving;
			}
		}

		// Token: 0x170013FD RID: 5117
		// (get) Token: 0x0600743D RID: 29757 RVA: 0x00266338 File Offset: 0x00264538
		// (set) Token: 0x0600743E RID: 29758 RVA: 0x0002B35B File Offset: 0x0002955B
		public List<int> MapHistoryIds
		{
			get
			{
				return this.m_mapHistoryIds;
			}
			set
			{
				this.m_mapHistoryIds = value;
			}
		}

		// Token: 0x170013FE RID: 5118
		// (get) Token: 0x0600743F RID: 29759 RVA: 0x0002B365 File Offset: 0x00029565
		public eHardLevel HandLevel
		{
			get
			{
				return this.m_hardLevel;
			}
		}

		// Token: 0x170013FF RID: 5119
		// (get) Token: 0x06007440 RID: 29760 RVA: 0x0002B36D File Offset: 0x0002956D
		public MapPoint MapPos
		{
			get
			{
				return this.mapPos;
			}
		}

		// Token: 0x17001400 RID: 5120
		// (get) Token: 0x06007441 RID: 29761 RVA: 0x00266350 File Offset: 0x00264550
		// (set) Token: 0x06007442 RID: 29762 RVA: 0x0002B375 File Offset: 0x00029575
		public string IsBossWar
		{
			get
			{
				return this.m_IsBossType;
			}
			set
			{
				this.m_IsBossType = value;
			}
		}

		// Token: 0x17001401 RID: 5121
		// (get) Token: 0x06007443 RID: 29763 RVA: 0x00266368 File Offset: 0x00264568
		// (set) Token: 0x06007444 RID: 29764 RVA: 0x0002B37F File Offset: 0x0002957F
		public bool IsPassDrama
		{
			get
			{
				return this.m_isPassDrama;
			}
			set
			{
				this.m_isPassDrama = value;
			}
		}

		// Token: 0x17001402 RID: 5122
		// (get) Token: 0x06007445 RID: 29765 RVA: 0x0002B389 File Offset: 0x00029589
		public List<string> GameOverResources
		{
			get
			{
				return this.m_gameOverResources;
			}
		}

		// Token: 0x17001403 RID: 5123
		// (get) Token: 0x06007446 RID: 29766 RVA: 0x00266380 File Offset: 0x00264580
		// (set) Token: 0x06007447 RID: 29767 RVA: 0x00266398 File Offset: 0x00264598
		public int BossCardCount
		{
			get
			{
				return this.m_bossCardCount;
			}
			set
			{
				bool flag = value > 0;
				if (flag)
				{
					this.BossCards = new int[9];
					this.m_bossCardCount = value;
				}
			}
		}

		// Token: 0x17001404 RID: 5124
		// (get) Token: 0x06007448 RID: 29768 RVA: 0x002663C4 File Offset: 0x002645C4
		// (set) Token: 0x06007449 RID: 29769 RVA: 0x0002B391 File Offset: 0x00029591
		public int PveGameDelay
		{
			get
			{
				return this.m_pveGameDelay;
			}
			set
			{
				this.m_pveGameDelay = value;
			}
		}

		// Token: 0x0600744A RID: 29770 RVA: 0x0002B39B File Offset: 0x0002959B
		public void SendObjectFocus(Physics obj, int type, int delay, int finishTime)
		{
			base.AddAction(new FocusAction(obj, type, delay, finishTime));
		}

		// Token: 0x0600744B RID: 29771 RVA: 0x0002B3AF File Offset: 0x000295AF
		public void SendGameFocus(Physics p, int delay, int finishTime)
		{
			base.AddAction(new FocusAction(p, 1, delay, finishTime));
		}

		// Token: 0x0600744C RID: 29772 RVA: 0x002663DC File Offset: 0x002645DC
		public PVEGame(int id, int roomId, PveInfo info, List<IGamePlayer> players, Map map, eRoomType roomType, eGameType gameType, int timeType, eHardLevel hardLevel, int currentFloor)
			: base(id, roomId, map, roomType, gameType, timeType)
		{
			foreach (IGamePlayer gamePlayer in players)
			{
				IGamePlayer gamePlayer2 = gamePlayer;
				int physicalId = this.PhysicalId;
				this.PhysicalId = physicalId + 1;
				base.AddPlayer(new Player(gamePlayer2, physicalId, this, 1, gamePlayer.PlayerCharacter.Blood)
				{
					Direction = ((this.m_random.Next(0, 1) == 0) ? 1 : (-1))
				});
				this.WorldbossBood = gamePlayer.WorldbossBood;
				this.AllWorldDameBoss = gamePlayer.AllWorldDameBoss;
			}
			this.m_info = info;
			this.BeginPlayersCount = players.Count;
			this.TotalNpcGrade = 0.0;
			this.TotalNpcExperience = 0.0;
			this.TotalHurt = 0;
			this.ParamLiving = null;
			this.ParamLiving1 = null;
			this.ParamLiving2 = null;
			this.ParamLiving3 = null;
			this.m_IsBossType = "";
			this.m_isPassDrama = false;
			this.WantTryAgain = 0;
			this.loadBossID = currentFloor;
			bool flag = currentFloor > 0;
			if (flag)
			{
				this.SessionId = currentFloor - 1;
			}
			else
			{
				this.SessionId = 0;
			}
			this.PreSessionId = 0;
			this.m_gameOverResources = new List<string>();
			this.Misssions = new Dictionary<int, MissionInfo>();
			this.m_mapHistoryIds = new List<int>();
			this.m_hardLevel = hardLevel;
			string script = this.GetScript(info, hardLevel);
			this.m_gameAI = ScriptMgr.CreateInstance(script) as APVEGameControl;
			bool flag2 = this.m_gameAI == null;
			if (flag2)
			{
				PVEGame.log.ErrorFormat("Can't create game ai :{0}", script);
				this.m_gameAI = SimplePVEGameControl.Simple;
				foreach (IGamePlayer gamePlayer3 in players)
				{
					gamePlayer3.ResetRoom(false, "CreateInstanceFail");
					this.RemovePlayer(gamePlayer3, false);
				}
			}
			this.m_gameAI.Game = this;
			this.m_gameAI.OnCreated();
			this.m_missionAI = SimpleMissionControl.Simple;
			this.beginTime = DateTime.Now;
			this.m_bossCardCount = 0;
		}

		// Token: 0x0600744D RID: 29773 RVA: 0x00266668 File Offset: 0x00264868
		private string GetScript(PveInfo pveInfo, eHardLevel hardLevel)
		{
			string empty = string.Empty;
			if (!true)
			{
			}
			string text;
			switch (hardLevel)
			{
			case eHardLevel.Simple:
				text = pveInfo.SimpleGameScript;
				break;
			case eHardLevel.Normal:
				text = pveInfo.NormalGameScript;
				break;
			case eHardLevel.Hard:
				text = pveInfo.HardGameScript;
				break;
			case eHardLevel.Terror:
				text = pveInfo.TerrorGameScript;
				break;
			default:
				text = pveInfo.SimpleGameScript;
				break;
			}
			if (!true)
			{
			}
			return text;
		}

		// Token: 0x0600744E RID: 29774 RVA: 0x002666EC File Offset: 0x002648EC
		public void SetupMissions(string missionIds)
		{
			bool flag = string.IsNullOrEmpty(missionIds);
			if (flag)
			{
				PVEGame.log.Error("SetupMissions fail, missionIds is empty or null " + missionIds);
			}
			else
			{
				bool flag2 = base.RoomType == eRoomType.Dungeon && this.SessionId != 0;
				if (flag2)
				{
					string[] array = missionIds.Split(new char[] { ',' });
					MissionInfo missionInfo = MissionInfoMgr.GetMissionInfo(int.Parse(array[this.SessionId]));
					this.Misssions.Add(1, missionInfo);
					this.SessionId = 0;
					bool flag3 = missionInfo == null;
					if (flag3)
					{
						PVEGame.log.Error("SetupMissions fail, not found mission Ids: " + missionIds);
					}
				}
				else
				{
					int num = 0;
					string[] array2 = missionIds.Split(new char[] { ',' });
					string[] array3 = array2;
					string[] array4 = array3;
					string[] array5 = array4;
					foreach (string text in array5)
					{
						num++;
						MissionInfo missionInfo2 = MissionInfoMgr.GetMissionInfo(int.Parse(text));
						this.Misssions.Add(num, missionInfo2);
					}
				}
			}
		}

		// Token: 0x0600744F RID: 29775 RVA: 0x00266800 File Offset: 0x00264A00
		public void ChangeMissionDelay(int rankID, int delay)
		{
			bool flag = this.m_NpcTurnQueue.ContainsKey(rankID);
			if (flag)
			{
				this.m_NpcTurnQueue[rankID] = delay;
			}
		}

		// Token: 0x06007450 RID: 29776 RVA: 0x00266830 File Offset: 0x00264A30
		public int FindTurnNpcRank()
		{
			int num = int.MaxValue;
			int num2 = 0;
			foreach (int num3 in this.m_NpcTurnQueue.Keys)
			{
				bool flag = this.m_NpcTurnQueue[num3] < num;
				if (flag)
				{
					num = this.m_NpcTurnQueue[num3];
					num2 = num3;
				}
			}
			return num2;
		}

		// Token: 0x06007451 RID: 29777 RVA: 0x002668BC File Offset: 0x00264ABC
		public LivingConfig BaseConfig()
		{
			return new LivingConfig
			{
				isBotom = 1,
				HasTurn = true,
				isShowBlood = true,
				isShowSmallMapPoint = true,
				ReduceBloodStart = 1,
				CanTakeDamage = true,
				CanFrost = false,
				CanCountKill = true
			};
		}

		// Token: 0x06007452 RID: 29778 RVA: 0x00266914 File Offset: 0x00264B14
		public SimpleNpc CreateNpc(int npcId, int x, int y, int type)
		{
			return this.CreateNpc(npcId, x, y, type, -1, "", this.BaseConfig());
		}

		// Token: 0x06007453 RID: 29779 RVA: 0x00266940 File Offset: 0x00264B40
		public SimpleNpc CreateNpc(int npcId, int x, int y, int type, int direction)
		{
			return this.CreateNpc(npcId, x, y, type, direction, "", this.BaseConfig());
		}

		// Token: 0x06007454 RID: 29780 RVA: 0x0026696C File Offset: 0x00264B6C
		public SimpleNpc CreateNpc(int npcId, int x, int y, int type, int direction, int rank)
		{
			return this.CreateNpc(npcId, x, y, type, direction, rank, this.BaseConfig());
		}

		// Token: 0x06007455 RID: 29781 RVA: 0x00266994 File Offset: 0x00264B94
		public SimpleNpc CreateNpc(int npcId, int x, int y, int type, int direction, string action)
		{
			return this.CreateNpc(npcId, x, y, type, direction, action, this.BaseConfig());
		}

		// Token: 0x06007456 RID: 29782 RVA: 0x002669BC File Offset: 0x00264BBC
		public SimpleNpc CreateNpc(int npcId, int x, int y, int type, int direction, string action, int rank)
		{
			return this.CreateNpc(npcId, x, y, type, direction, action, rank, this.BaseConfig());
		}

		// Token: 0x06007457 RID: 29783 RVA: 0x002669E8 File Offset: 0x00264BE8
		public SimpleNpc CreateNpc(int npcId, int x, int y, int type, int direction, int rank, LivingConfig config)
		{
			return this.CreateNpc(npcId, x, y, type, direction, "", rank, config);
		}

		// Token: 0x06007458 RID: 29784 RVA: 0x00266A10 File Offset: 0x00264C10
		public SimpleNpc CreateNpc(int npcId, int x, int y, int type, int direction, LivingConfig config)
		{
			return this.CreateNpc(npcId, x, y, type, direction, "", 0, config);
		}

		// Token: 0x06007459 RID: 29785 RVA: 0x00266A38 File Offset: 0x00264C38
		public SimpleNpc CreateNpc(int npcId, int x, int y, int type, int direction, string action, LivingConfig config)
		{
			return this.CreateNpc(npcId, x, y, type, direction, action, 0, config);
		}

		// Token: 0x0600745A RID: 29786 RVA: 0x00266A5C File Offset: 0x00264C5C
		public SimpleNpc CreateNpc(int npcId, int x, int y, int type, int direction, string action, int rank, LivingConfig config)
		{
			NpcInfo npcInfoById = NPCInfoMgr.GetNpcInfoById(npcId);
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			SimpleNpc simpleNpc = new SimpleNpc(physicalId, this, npcInfoById, type, direction, action, rank);
			bool flag = config != null;
			if (flag)
			{
				simpleNpc.Config = config;
			}
			bool flag2 = simpleNpc.Config.ReduceBloodStart > 1;
			if (flag2)
			{
				simpleNpc.Blood = npcInfoById.Blood / simpleNpc.Config.ReduceBloodStart;
			}
			else
			{
				simpleNpc.Reset();
			}
			bool flag3 = rank != -1 && !this.m_NpcTurnQueue.ContainsKey(rank);
			if (flag3)
			{
				this.m_NpcTurnQueue.Add(rank, this.m_pveGameDelay + rank * 2);
			}
			simpleNpc.SetXY(x, y);
			this.AddLiving(simpleNpc);
			simpleNpc.StartMoving();
			return simpleNpc;
		}

		// Token: 0x0600745B RID: 29787 RVA: 0x00266B38 File Offset: 0x00264D38
		public SimpleNpc CreateCaptainNpc(int npcId, int x, int y, int type, int direction, int bloodInver, int rank)
		{
			NpcInfo npcInfoById = NPCInfoMgr.GetNpcInfoById(npcId);
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			SimpleNpc simpleNpc = new SimpleNpc(physicalId, this, npcInfoById, type, direction, "", rank);
			LivingConfig livingConfig = this.BaseConfig();
			livingConfig.CanTakeDamage = false;
			simpleNpc.Config = livingConfig;
			simpleNpc.Reset();
			bool flag = npcId != 23001;
			if (flag)
			{
				simpleNpc.Blood = simpleNpc.Blood / 100 * bloodInver;
			}
			else
			{
				simpleNpc.Blood = npcInfoById.Blood;
			}
			simpleNpc.SetXY(x, y);
			bool flag2 = rank != -1 && !this.NpcTurnQueue.ContainsKey(rank);
			if (flag2)
			{
				this.NpcTurnQueue.Add(rank, this.m_pveGameDelay + rank * 2);
			}
			this.AddLiving(simpleNpc);
			simpleNpc.StartMoving();
			return simpleNpc;
		}

		// Token: 0x0600745C RID: 29788 RVA: 0x00266C20 File Offset: 0x00264E20
		public SimpleBoss CreateBoss(int npcId, int x, int y, int direction, int type)
		{
			return this.CreateBoss(npcId, x, y, direction, type, "", this.BaseConfig());
		}

		// Token: 0x0600745D RID: 29789 RVA: 0x00266C4C File Offset: 0x00264E4C
		public SimpleBoss CreateBoss(int npcId, int x, int y, int direction, int type, string action)
		{
			return this.CreateBoss(npcId, x, y, direction, type, action, this.BaseConfig());
		}

		// Token: 0x0600745E RID: 29790 RVA: 0x00266C74 File Offset: 0x00264E74
		public SimpleBoss CreateBoss(int npcId, int x, int y, int direction, int type, string action, LivingConfig config)
		{
			NpcInfo npcInfoById = NPCInfoMgr.GetNpcInfoById(npcId);
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			SimpleBoss simpleBoss = new SimpleBoss(physicalId, this, npcInfoById, direction, type, action);
			bool flag = config != null;
			if (flag)
			{
				simpleBoss.Config = config;
			}
			simpleBoss.CaeateAction = action;
			bool flag2 = simpleBoss.Config.ReduceBloodStart > 1;
			if (flag2)
			{
				simpleBoss.Blood = npcInfoById.Blood / simpleBoss.Config.ReduceBloodStart;
			}
			else
			{
				simpleBoss.Reset();
				bool flag3 = simpleBoss.Config.IsWorldBoss && this.WorldbossBood < 2147483647L;
				if (flag3)
				{
					simpleBoss.Blood = (int)this.WorldbossBood;
				}
			}
			simpleBoss.SetXY(x, y);
			this.AddLiving(simpleBoss);
			simpleBoss.StartMoving();
			return simpleBoss;
		}

		// Token: 0x0600745F RID: 29791 RVA: 0x00266D54 File Offset: 0x00264F54
		private void OnFirstKill(int missionID, List<Player> player)
		{
			EntityDatabase.ServerModels.ServerData serverData = new EntityDatabase.ServerModels.ServerData();
			PlayerData playerData = new PlayerData();
			CenterServiceClient centerServiceClient = new CenterServiceClient();
			TS_FirstCopy ts_FirstCopy = serverData.TS_FirstCopy.Where((TS_FirstCopy p) => p.MissionID == this.MissionInfo.Id).FirstOrDefault<TS_FirstCopy>();
			bool flag = ts_FirstCopy == null;
			if (!flag)
			{
				Sys_FirstKill_Data sys_FirstKill_Data = playerData.Sys_FirstKill_Data.Where((Sys_FirstKill_Data p) => p.MissionID == this.MissionInfo.Id).FirstOrDefault<Sys_FirstKill_Data>();
				bool flag2 = sys_FirstKill_Data != null;
				if (!flag2)
				{
					sys_FirstKill_Data = new Sys_FirstKill_Data();
					sys_FirstKill_Data.MissionID = this.MissionInfo.Id;
					sys_FirstKill_Data.BossID = 0;
					sys_FirstKill_Data.AreaName = "弹弹堂";
					sys_FirstKill_Data.NickName1 = "";
					sys_FirstKill_Data.NickName2 = "";
					sys_FirstKill_Data.NickName3 = "";
					sys_FirstKill_Data.NickName4 = "";
					for (int i = 0; i < player.Count; i++)
					{
						switch (i)
						{
						case 0:
							sys_FirstKill_Data.NickName1 = player[0].PlayerDetail.PlayerCharacter.NickName;
							break;
						case 1:
							sys_FirstKill_Data.NickName2 = player[1].PlayerDetail.PlayerCharacter.NickName;
							break;
						case 2:
							sys_FirstKill_Data.NickName3 = player[2].PlayerDetail.PlayerCharacter.NickName;
							break;
						case 3:
							sys_FirstKill_Data.NickName4 = player[3].PlayerDetail.PlayerCharacter.NickName;
							break;
						}
					}
					sys_FirstKill_Data.PassMissonTime = DateTime.Now;
					playerData.Sys_FirstKill_Data.Add(sys_FirstKill_Data);
					playerData.SaveChanges();
					List<ItemInfo> list = new List<ItemInfo>();
					bool flag3 = ts_FirstCopy.TemplateIDA != 0;
					if (flag3)
					{
						ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(ts_FirstCopy.TemplateIDA);
						ItemInfo itemInfo = ItemInfo.CreateFromTemplate(itemTemplateInfo, 1, 106);
						itemInfo.Count = ts_FirstCopy.CountA;
						itemInfo.ValidDate = ts_FirstCopy.ValidDateA;
						itemInfo.IsBinds = true;
						list.Add(itemInfo);
					}
					bool flag4 = ts_FirstCopy.TemplateIDB != 0;
					if (flag4)
					{
						ItemTemplateInfo itemTemplateInfo2 = ItemMgr.FindItemTemplate(ts_FirstCopy.TemplateIDB);
						ItemInfo itemInfo2 = ItemInfo.CreateFromTemplate(itemTemplateInfo2, 1, 106);
						itemInfo2.Count = ts_FirstCopy.CountB;
						itemInfo2.ValidDate = ts_FirstCopy.ValidDateB;
						itemInfo2.IsBinds = true;
						list.Add(itemInfo2);
					}
					bool flag5 = ts_FirstCopy.TemplateIDC != 0;
					if (flag5)
					{
						ItemTemplateInfo itemTemplateInfo3 = ItemMgr.FindItemTemplate(ts_FirstCopy.TemplateIDC);
						ItemInfo itemInfo3 = ItemInfo.CreateFromTemplate(itemTemplateInfo3, 1, 106);
						itemInfo3.Count = ts_FirstCopy.CountC;
						itemInfo3.ValidDate = ts_FirstCopy.ValidDateC;
						itemInfo3.IsBinds = true;
						list.Add(itemInfo3);
					}
					StringBuilder stringBuilder = new StringBuilder();
					foreach (Player player2 in player)
					{
						stringBuilder.Append("[" + player2.PlayerDetail.PlayerCharacter.NickName + "]");
						WorldEventMgr.SendItemsToMails(list, player2.PlayerDetail.PlayerCharacter.ID, player2.PlayerDetail.PlayerCharacter.NickName, 1001, ts_FirstCopy.CopyName + "的副本首杀奖励.");
						centerServiceClient.MailNotice(player2.PlayerDetail.PlayerCharacter.ID, 1001);
					}
					centerServiceClient.SystemNotice(string.Concat(new string[]
					{
						"恭喜玩家:",
						(stringBuilder != null) ? stringBuilder.ToString() : null,
						",全服首通副本",
						ts_FirstCopy.CopyName,
						"难度"
					}));
				}
			}
		}

		// Token: 0x06007460 RID: 29792 RVA: 0x00267220 File Offset: 0x00265420
		public Layer CreateLayerBoss(int x, int y, string name, string model, string defaultAction, int scale, int rotation)
		{
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			Layer layer = new Layer(physicalId, name, model, defaultAction, scale, rotation);
			layer.SetXY(x, y);
			this.AddPhysicalObj(layer, true);
			return layer;
		}

		// Token: 0x06007461 RID: 29793 RVA: 0x00267268 File Offset: 0x00265468
		public SimpleBox CreateBox(int x, int y, string model, ItemInfo item)
		{
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			SimpleBox simpleBox = new SimpleBox(physicalId, model, item, 1);
			simpleBox.SetXY(x, y);
			this.m_map.AddPhysical(simpleBox);
			base.AddBox(simpleBox, true);
			return simpleBox;
		}

		// Token: 0x06007462 RID: 29794 RVA: 0x002672B8 File Offset: 0x002654B8
		public PhysicalObj CreatePhysicalObj(int x, int y, string name, string model, string defaultAction, int scale, int rotation)
		{
			return this.CreatePhysicalObj(x, y, name, model, defaultAction, scale, rotation, 0);
		}

		// Token: 0x06007463 RID: 29795 RVA: 0x002672DC File Offset: 0x002654DC
		public PhysicalObj CreatePhysicalObj(int x, int y, string name, string model, string defaultAction, int scale, int rotation, int typeEffect)
		{
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			PhysicalObj physicalObj = new PhysicalObj(physicalId, name, model, defaultAction, scale, rotation, typeEffect);
			physicalObj.SetXY(x, y);
			this.AddPhysicalObj(physicalObj, true);
			return physicalObj;
		}

		// Token: 0x06007464 RID: 29796 RVA: 0x00267220 File Offset: 0x00265420
		public Layer Createlayer(int x, int y, string name, string model, string defaultAction, int scale, int rotation)
		{
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			Layer layer = new Layer(physicalId, name, model, defaultAction, scale, rotation);
			layer.SetXY(x, y);
			this.AddPhysicalObj(layer, true);
			return layer;
		}

		// Token: 0x06007465 RID: 29797 RVA: 0x00267220 File Offset: 0x00265420
		public Layer Createlayer(int x, int y, string name, string model, string defaultAction, int scale, int rotation, bool CanPenetrate)
		{
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			Layer layer = new Layer(physicalId, name, model, defaultAction, scale, rotation);
			layer.SetXY(x, y);
			this.AddPhysicalObj(layer, true);
			return layer;
		}

		// Token: 0x06007466 RID: 29798 RVA: 0x00267324 File Offset: 0x00265524
		public LayerTop CreateLayerTop(int x, int y, string name, string model, string defaultAction, int scale, int rotation)
		{
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			LayerTop layerTop = new LayerTop(physicalId, name, model, defaultAction, scale, rotation);
			layerTop.SetXY(x, y);
			this.AddPhysicalObj(layerTop, true);
			return layerTop;
		}

		// Token: 0x06007467 RID: 29799 RVA: 0x0026736C File Offset: 0x0026556C
		public Layer CreateTip(int x, int y, string name, string model, string defaultAction, int scale, int rotation, int type)
		{
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			Layer layer = new Layer(physicalId, name, model, defaultAction, scale, rotation, type);
			layer.SetXY(x, y);
			this.AddPhysicalTip(layer, true);
			return layer;
		}

		// Token: 0x06007468 RID: 29800 RVA: 0x002673B4 File Offset: 0x002655B4
		public Ball CreateBall(int x, int y, string name, string defaultAction, int scale, int rotation)
		{
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			Ball ball = new Ball(physicalId, name, defaultAction, scale, rotation);
			ball.SetXY(x, y);
			this.AddPhysicalObj(ball, true);
			base.SendLivingActionMapping(ball.Id, "pick", name);
			return ball;
		}

		// Token: 0x06007469 RID: 29801 RVA: 0x0026740C File Offset: 0x0026560C
		public void ClearMissionData()
		{
			foreach (Living living in this.m_livings)
			{
				living.Dispose();
			}
			this.m_livings.Clear();
			this.m_decklivings.Clear();
			List<TurnedLiving> list = new List<TurnedLiving>();
			foreach (TurnedLiving turnedLiving in base.TurnQueue)
			{
				bool flag = turnedLiving is Player;
				if (flag)
				{
					bool isLiving = turnedLiving.IsLiving;
					if (isLiving)
					{
						list.Add(turnedLiving);
					}
				}
				else
				{
					turnedLiving.Dispose();
				}
			}
			base.TurnQueue.Clear();
			foreach (TurnedLiving turnedLiving2 in list)
			{
				base.TurnQueue.Add(turnedLiving2);
			}
			bool flag2 = this.m_map == null;
			if (!flag2)
			{
				foreach (PhysicalObj physicalObj in this.m_map.GetAllPhysicalObjSafe())
				{
					physicalObj.Dispose();
				}
			}
		}

		// Token: 0x0600746A RID: 29802 RVA: 0x002675B0 File Offset: 0x002657B0
		public void AddAllPlayerToTurn()
		{
			foreach (Player player in base.Players.Values)
			{
				base.TurnQueue.Add(player);
			}
		}

		// Token: 0x0600746B RID: 29803 RVA: 0x00267614 File Offset: 0x00265814
		public override void MissionStart(IGamePlayer host)
		{
			bool flag = base.GameState != eGameState.SessionPrepared && base.GameState != eGameState.GameOver;
			if (!flag)
			{
				foreach (Player player in base.Players.Values)
				{
					player.Ready = true;
				}
				this.CheckState(0);
			}
		}

		// Token: 0x0600746C RID: 29804 RVA: 0x0002B3C2 File Offset: 0x000295C2
		public override void AddLiving(Living living)
		{
			base.AddLiving(living);
			living.Died += this.living_Died;
		}

		// Token: 0x0600746D RID: 29805 RVA: 0x0026769C File Offset: 0x0026589C
		private void living_Died(Living living)
		{
			bool flag = base.CurrentLiving != null && base.CurrentLiving is Player && !(living is Player) && living != base.CurrentLiving && living.Config.CanCountKill;
			if (flag)
			{
				this.TotalKillCount++;
				this.TotalNpcExperience += (double)living.Experience;
				this.TotalNpcGrade += (double)living.Grade;
			}
			bool flag2 = living is SimpleBoss;
			if (flag2)
			{
				((SimpleBoss)living).DiedEvent();
				((SimpleBoss)living).DiedSay();
			}
			bool flag3 = living is SimpleNpc;
			if (flag3)
			{
				((SimpleNpc)living).DiedEvent();
				((SimpleNpc)living).DiedSay();
			}
			bool flag4 = living is Player && base.CurrentLiving is SimpleBoss;
			if (flag4)
			{
				((SimpleBoss)base.CurrentLiving).KillPlayerSay();
			}
		}

		// Token: 0x0600746E RID: 29806 RVA: 0x00267798 File Offset: 0x00265998
		public override bool CanAddPlayer()
		{
			Dictionary<int, Player> players = this.m_players;
			bool flag2;
			lock (players)
			{
				flag2 = base.GameState == eGameState.SessionPrepared && this.m_players.Count < 4;
			}
			return flag2;
		}

		// Token: 0x0600746F RID: 29807 RVA: 0x002677F4 File Offset: 0x002659F4
		public override Player AddPlayer(IGamePlayer gp)
		{
			bool flag = this.CanAddPlayer();
			Player player2;
			if (flag)
			{
				int physicalId = this.PhysicalId;
				this.PhysicalId = physicalId + 1;
				Player player = new Player(gp, physicalId, this, 1, gp.PlayerCharacter.Blood);
				player.Direction = ((this.m_random.Next(0, 1) == 0) ? 1 : (-1));
				player.Ready = true;
				base.AddPlayer(player);
				this.SendCreateGameToSingle(this, gp);
				base.SendPlayerInfoInGame(player);
				player2 = player;
			}
			else
			{
				player2 = null;
			}
			return player2;
		}

		// Token: 0x06007470 RID: 29808 RVA: 0x00267878 File Offset: 0x00265A78
		public override Player RemovePlayer(IGamePlayer gp, bool isKick)
		{
			Player player = base.GetPlayer(gp);
			bool flag = player != null;
			if (flag)
			{
				player.PlayerDetail.ClearPropItem();
				player.PlayerDetail.ResetRoom(false, "RemovePlayer");
				base.RemovePlayer(gp, isKick);
				base.RemovePlayer(gp, isKick);
			}
			return player;
		}

		// Token: 0x06007471 RID: 29809 RVA: 0x002678D0 File Offset: 0x00265AD0
		public string GuluOlympics(string style, int place)
		{
			string[] array = style.Split(new char[] { ',' });
			string[] array2 = new string[] { "13300|suits100", "13301|suits101", "13302|suits102", "13303|suits103" };
			string text = array[0];
			for (int i = 1; i < this.EquipPlace.Length; i++)
			{
				text += ",";
				bool flag = this.EquipPlace[i] == 11;
				if (flag)
				{
					text = text + array[i] + ",";
					text += array2[place];
				}
				else
				{
					text += array[i];
				}
			}
			return text;
		}

		// Token: 0x06007472 RID: 29810 RVA: 0x00267988 File Offset: 0x00265B88
		internal void SendTempStyle(int skin)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(134);
			List<Player> allFightPlayers = base.GetAllFightPlayers();
			int num = 0;
			gspacketIn.WriteInt(allFightPlayers.Count);
			foreach (Player player in allFightPlayers)
			{
				IGamePlayer playerDetail = player.PlayerDetail;
				string style = playerDetail.PlayerCharacter.Style;
				bool flag = skin == 6;
				if (flag)
				{
					gspacketIn.WriteString(this.GuluOlympics(style, num));
				}
				else
				{
					gspacketIn.WriteString(style);
				}
				num++;
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Hide);
				gspacketIn.WriteBoolean(playerDetail.PlayerCharacter.Sex);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Skin);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Colors);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ID);
			}
			bool flag2 = this.HasChangeStyle();
			if (flag2)
			{
				this.SendToAll(gspacketIn);
			}
		}

		// Token: 0x06007473 RID: 29811 RVA: 0x00267AC0 File Offset: 0x00265CC0
		public bool HasChangeStyle()
		{
			bool flag = this.m_info == null;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				int id = this.m_info.ID;
				int num = id;
				bool flag3 = num == 6;
				flag2 = flag3;
			}
			return flag2;
		}

		// Token: 0x06007474 RID: 29812 RVA: 0x0002B3E0 File Offset: 0x000295E0
		public void SetupStyle()
		{
			this.SendTempStyle((this.m_info != null) ? this.m_info.ID : 0);
		}

		// Token: 0x06007475 RID: 29813 RVA: 0x00267B04 File Offset: 0x00265D04
		public void LoadResources(int[] npcIds)
		{
			bool flag = npcIds == null || npcIds.Length == 0;
			if (!flag)
			{
				foreach (int num in npcIds)
				{
					NpcInfo npcInfoById = NPCInfoMgr.GetNpcInfoById(num);
					bool flag2 = npcInfoById == null;
					if (flag2)
					{
						PVEGame.log.ErrorFormat("LoadResources npcInfo {0} resoure is not exits", num);
					}
					else
					{
						base.AddLoadingFile(2, npcInfoById.ResourcesPath, npcInfoById.ModelID);
					}
				}
			}
		}

		// Token: 0x06007476 RID: 29814 RVA: 0x0002B400 File Offset: 0x00029600
		public void SetDelayMissionTryAgain()
		{
			this.MissionTryAgain = false;
			base.AddAction(new BaseAction(this.waitingTime * 1000));
			this.WaitingTimeResponseTryAgain = DateTime.Now;
		}

		// Token: 0x06007477 RID: 29815 RVA: 0x00267B80 File Offset: 0x00265D80
		public bool ResponseMissionTryAgain()
		{
			return DateTime.Compare(this.WaitingTimeResponseTryAgain.AddSeconds((double)this.waitingTime), DateTime.Now) > 0;
		}

		// Token: 0x06007478 RID: 29816 RVA: 0x00267BB4 File Offset: 0x00265DB4
		public void LoadNpcGameOverResources(int[] npcIds)
		{
			bool flag = npcIds == null || npcIds.Length == 0;
			if (!flag)
			{
				foreach (int num in npcIds)
				{
					NpcInfo npcInfoById = NPCInfoMgr.GetNpcInfoById(num);
					bool flag2 = npcInfoById == null;
					if (flag2)
					{
						PVEGame.log.Error("LoadGameOverResources npcInfo resoure is not exits");
					}
					else
					{
						this.m_gameOverResources.Add(npcInfoById.ModelID);
					}
				}
			}
		}

		// Token: 0x06007479 RID: 29817 RVA: 0x00267C28 File Offset: 0x00265E28
		public void Prepare()
		{
			bool flag = base.GetAllPlayers().Length == 0;
			if (flag)
			{
				this.m_gameState = eGameState.Stopped;
				PVEGame.log.Error("Prepare GetAllPlayers().Length == 0");
			}
			else
			{
				bool flag2 = base.GameState == eGameState.Inited;
				if (flag2)
				{
					this.m_gameState = eGameState.Prepared;
					base.SendCreateGame();
					this.CheckState(0);
					try
					{
						this.m_gameAI.OnPrepated();
					}
					catch (Exception ex)
					{
						PVEGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex);
					}
				}
			}
		}

		// Token: 0x0600747A RID: 29818 RVA: 0x00267CC8 File Offset: 0x00265EC8
		public void PrepareNewSession()
		{
			bool flag = base.GameState != eGameState.Prepared && base.GameState != eGameState.GameOver && base.GameState != eGameState.ALLSessionStopped;
			if (!flag)
			{
				try
				{
					this.m_gameState = eGameState.SessionPrepared;
					this.SessionId++;
					base.ClearLoadingFiles();
					this.ClearMissionData();
					this.m_gameOverResources.Clear();
					this.WantTryAgain = 0;
					this.m_missionInfo = this.Misssions[this.SessionId];
					this.m_pveGameDelay = this.m_missionInfo.Delay;
					this.TotalCount = this.m_missionInfo.TotalCount;
					this.TotalTurn = this.m_missionInfo.TotalTurn;
					this.Param1 = this.m_missionInfo.Param1;
					this.Param2 = this.m_missionInfo.Param2;
					this.Param3 = -1;
					this.Param4 = -1;
					this.m_missionAI = ScriptMgr.CreateInstance(this.m_missionInfo.Script) as AMissionControl;
					bool flag2 = this.m_missionAI == null;
					if (flag2)
					{
						PVEGame.log.ErrorFormat("Can't create game mission ai :{0}", this.m_missionInfo.Script);
						this.m_missionAI = SimpleMissionControl.Simple;
						List<Player> allFightPlayers = base.GetAllFightPlayers();
						foreach (Player player in allFightPlayers)
						{
							player.PlayerDetail.ResetRoom(false, "CreateInstanceFail");
							this.RemovePlayer(player.PlayerDetail, false);
						}
						this.m_gameState = eGameState.Stopped;
					}
					bool flag3 = base.RoomType == eRoomType.Dungeon;
					if (flag3)
					{
						this.Pic = string.Format("show{0}.jpg", this.SessionId);
						List<Player> allFightPlayers2 = base.GetAllFightPlayers();
						foreach (Player player2 in allFightPlayers2)
						{
							player2.PlayerDetail.UpdateBarrier(this.SessionId, this.Pic);
						}
					}
					this.IsBossWar = "";
					this.m_missionAI.Game = this;
					try
					{
						this.m_missionAI.OnPrepareNewSession();
					}
					catch (Exception ex)
					{
						PVEGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex);
					}
				}
				catch (Exception ex2)
				{
					this.m_gameState = eGameState.Stopped;
					PVEGame.log.ErrorFormat("Preparen new session, m_missionInfo.Script {0}, m_missionInfo.Id {1}, exception: {2}", this.m_missionInfo.Script, this.m_missionInfo.Id, ex2);
				}
			}
		}

		// Token: 0x0600747B RID: 29819 RVA: 0x00267FD4 File Offset: 0x002661D4
		public void PrepareCouniteSession()
		{
			bool flag = base.GameState != eGameState.Prepared && base.GameState != eGameState.GameOver && base.GameState != eGameState.ALLSessionStopped;
			if (!flag)
			{
				try
				{
					this.m_gameState = eGameState.SessionPrepared;
					base.ClearLoadingFiles();
					this.ClearMissionData();
					this.m_gameOverResources.Clear();
					this.WantTryAgain = 0;
					this.m_missionInfo = this.Misssions[this.SessionId];
					this.m_pveGameDelay = this.m_missionInfo.Delay;
					this.TotalCount = this.m_missionInfo.TotalCount;
					this.TotalTurn = this.m_missionInfo.TotalTurn;
					this.Param1 = this.m_missionInfo.Param1;
					this.Param2 = this.m_missionInfo.Param2;
					this.Param3 = -1;
					this.Param4 = -1;
					this.m_missionAI = ScriptMgr.CreateInstance(this.m_missionInfo.Script) as AMissionControl;
					bool flag2 = this.m_missionAI == null;
					if (flag2)
					{
						PVEGame.log.ErrorFormat("Can't create game mission ai :{0}", this.m_missionInfo.Script);
						this.m_missionAI = SimpleMissionControl.Simple;
						List<Player> allFightPlayers = base.GetAllFightPlayers();
						foreach (Player player in allFightPlayers)
						{
							player.PlayerDetail.ResetRoom(false, "CreateInstanceFail");
							this.RemovePlayer(player.PlayerDetail, false);
						}
						this.m_gameState = eGameState.Stopped;
					}
					bool flag3 = base.RoomType == eRoomType.Dungeon;
					if (flag3)
					{
						this.Pic = string.Format("show{0}.jpg", this.SessionId);
						List<Player> allFightPlayers2 = base.GetAllFightPlayers();
						foreach (Player player2 in allFightPlayers2)
						{
							player2.PlayerDetail.UpdateBarrier(this.SessionId, this.Pic);
						}
					}
					this.IsBossWar = "";
					this.m_missionAI.Game = this;
					try
					{
						this.m_missionAI.OnPrepareNewSession();
					}
					catch (Exception ex)
					{
						PVEGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex);
					}
				}
				catch (Exception ex2)
				{
					this.m_gameState = eGameState.Stopped;
					PVEGame.log.ErrorFormat("Preparen new session, m_missionInfo.Script {0}, m_missionInfo.Id {1}, exception: {2}", this.m_missionInfo.Script, this.m_missionInfo.Id, ex2);
				}
			}
		}

		// Token: 0x0600747C RID: 29820 RVA: 0x002682D4 File Offset: 0x002664D4
		public bool CanStartNewSession()
		{
			return base.m_turnIndex == 0 || this.IsAllReady();
		}

		// Token: 0x0600747D RID: 29821 RVA: 0x002682F8 File Offset: 0x002664F8
		public bool IsAllReady()
		{
			foreach (Player player in base.Players.Values)
			{
				bool flag = !player.Ready;
				if (flag)
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x0600747E RID: 29822 RVA: 0x0002B42D File Offset: 0x0002962D
		public void StartLoading()
		{
			this.StartLoading(null);
		}

		// Token: 0x0600747F RID: 29823 RVA: 0x00268368 File Offset: 0x00266568
		public void StartLoading(Player np)
		{
			bool flag = base.GameState != eGameState.SessionPrepared;
			if (!flag)
			{
				try
				{
					bool flag2 = np == null;
					if (flag2)
					{
						this.m_gameState = eGameState.Loading;
						base.m_turnIndex = 0;
					}
					base.VaneLoading(np);
					this.SendMissionInfo(np);
					this.SetupStyle();
					base.SendStartLoading(60);
					bool flag3 = np == null;
					if (flag3)
					{
						base.AddAction(new WaitPlayerLoadingAction(this, 61000));
					}
				}
				catch (Exception ex)
				{
					this.m_gameState = eGameState.Stopped;
					PVEGame.log.Error(ex);
				}
			}
		}

		// Token: 0x06007480 RID: 29824 RVA: 0x00268410 File Offset: 0x00266610
		public void StartGameMovie()
		{
			bool flag = base.GameState == eGameState.Loading;
			if (flag)
			{
				try
				{
					this.m_missionAI.OnStartMovie();
				}
				catch (Exception ex)
				{
					PVEGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex);
				}
			}
		}

		// Token: 0x06007481 RID: 29825 RVA: 0x00268470 File Offset: 0x00266670
		public void StartGame()
		{
			bool flag = base.GameState != eGameState.Loading;
			if (!flag)
			{
				try
				{
					this.StartGameMovie();
					this.m_gameState = eGameState.GameStart;
					this.SendSyncLifeTime();
					this.TotalKillCount = 0;
					this.TotalNpcGrade = 0.0;
					this.TotalNpcExperience = 0.0;
					this.TotalHurt = 0;
					this.PlayersXY.Clear();
					this.m_bossCardCount = 0;
					this.BossCards = null;
					List<Player> allFightPlayers = base.GetAllFightPlayers();
					this.mapPos = MapMgr.GetPVEMapRandomPos(this.m_map.Info.ID);
					GSPacketIn gspacketIn = new GSPacketIn(91);
					gspacketIn.WriteByte(99);
					gspacketIn.WriteInt(allFightPlayers.Count);
					foreach (Player player in allFightPlayers)
					{
						bool flag2 = !player.IsLiving;
						if (flag2)
						{
							this.AddLiving(player);
						}
						player.Reset();
						Point playerPoint = base.GetPlayerPoint(this.mapPos, player.Team);
						this.PlayersXY.Add(playerPoint);
						player.SetXY(playerPoint);
						this.m_map.AddPhysical(player);
						player.StartMoving();
						player.StartGame();
						player.OnGameStarted();
						gspacketIn.WriteInt(player.Id);
						gspacketIn.WriteInt(player.X);
						gspacketIn.WriteInt(player.Y);
						bool flag3 = playerPoint.X < 600;
						if (flag3)
						{
							player.Direction = 1;
						}
						else
						{
							player.Direction = -1;
						}
						gspacketIn.WriteInt(player.Direction);
						gspacketIn.WriteInt(player.MaxBlood);
						gspacketIn.WriteInt(player.Team);
						gspacketIn.WriteInt(player.PlayerDetail.MainWeapon.RefineryLevel);
						gspacketIn.WriteInt(player.PowerRatio);
						gspacketIn.WriteInt(player.Dander);
						gspacketIn.WriteInt(player.PlayerDetail.FightBuffs.Count);
						foreach (UserBufferInfo userBufferInfo in player.PlayerDetail.FightBuffs)
						{
							gspacketIn.WriteInt(userBufferInfo.Type);
							gspacketIn.WriteInt(userBufferInfo.Value);
						}
					}
					this.SendToAll(gspacketIn);
					try
					{
						this.m_missionAI.OnPrepareStartGame();
						foreach (Player player2 in allFightPlayers)
						{
							player2.StartMoving();
							player2.BoltMove(player2.X, player2.Y, 1000);
						}
					}
					catch (Exception ex)
					{
						PVEGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex);
					}
					this.SendUpdateUiData(null);
					base.WaitTime(base.PlayerCount * 2500 + 1000);
					base.OnGameStarted();
				}
				catch (Exception ex2)
				{
					this.m_gameState = eGameState.Stopped;
					PVEGame.log.Error(ex2);
				}
			}
		}

		// Token: 0x06007482 RID: 29826 RVA: 0x00268850 File Offset: 0x00266A50
		public void PrepareNewGame()
		{
			bool flag = base.GameState == eGameState.GameStart;
			if (flag)
			{
				this.m_gameState = eGameState.Playing;
				this.BossCardCount = 0;
				this.SendSyncLifeTime();
				base.WaitTime(base.PlayerCount * 1000);
				try
				{
					this.m_missionAI.OnStartGame();
					this.m_missionAI.OnPrepareNewGame();
				}
				catch (Exception ex)
				{
					PVEGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex);
				}
			}
		}

		// Token: 0x06007483 RID: 29827 RVA: 0x002688E4 File Offset: 0x00266AE4
		public void GeneralCommand(GSPacketIn packet)
		{
			bool flag = base.GameState == eGameState.Playing;
			if (flag)
			{
				this.m_gameState = eGameState.Playing;
				try
				{
					this.m_missionAI.OnGeneralCommand(packet);
				}
				catch (Exception ex)
				{
					BaseGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex);
				}
			}
		}

		// Token: 0x06007484 RID: 29828 RVA: 0x0026894C File Offset: 0x00266B4C
		public void NextTurn()
		{
			bool flag = base.GameState != eGameState.Playing;
			if (!flag)
			{
				this.IsPassDrama = false;
				base.ClearWaitTimer();
				base.ClearDiedPhysicals();
				base.CheckBox();
				this.LivingRandSay();
				List<Physics> allPhysicalSafe = this.m_map.GetAllPhysicalSafe();
				foreach (Physics physics in allPhysicalSafe)
				{
					physics.PrepareNewTurn();
				}
				List<SimpleBox> list = base.CreateBox();
				this.m_currentLiving = base.FindNextTurnedLiving(false);
				this.LastTurnLiving = this.m_currentLiving;
				try
				{
					this.m_missionAI.OnNewTurnStarted();
				}
				catch (Exception ex)
				{
					PVEGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex);
				}
				bool flag2 = this.m_currentLiving != null;
				if (flag2)
				{
					bool flag3 = this.m_currentLiving != this.LastTurnLiving && this.LastTurnLiving is Player && (this.LastTurnLiving as Player).IsAddTurn;
					if (flag3)
					{
						this.m_currentLiving = this.LastTurnLiving;
						(this.LastTurnLiving as Player).IsAddTurn = false;
					}
					int num = base.m_turnIndex;
					base.m_turnIndex = num + 1;
					this.SendUpdateUiData(null);
					List<Living> livedLivingsHadTurn = base.GetLivedLivingsHadTurn();
					Living living = null;
					int num2 = this.FindTurnNpcRank();
					bool flag4 = livedLivingsHadTurn.Count > 0 && this.m_NpcTurnQueue.Count > 0 && this.m_currentLiving.Delay > this.m_NpcTurnQueue[num2];
					if (flag4)
					{
						this.MinusDelays(this.m_NpcTurnQueue[num2]);
						foreach (Living living2 in this.m_livings)
						{
							living2.PrepareSelfTurn();
							bool flag5 = !living2.IsFrost;
							if (flag5)
							{
								living2.StartAttacking();
								bool flag6 = living == null;
								if (flag6)
								{
									living = living2;
								}
							}
						}
						bool flag7 = living != null;
						if (flag7)
						{
							base.SendGameNextTurn(living, this, list);
						}
						foreach (Living living3 in this.m_livings)
						{
							bool isAttacking = living3.IsAttacking;
							if (isAttacking)
							{
								living3.StopAttacking();
							}
						}
						Dictionary<int, int> npcTurnQueue = this.m_NpcTurnQueue;
						num = num2;
						npcTurnQueue[num] += this.MissionInfo.IncrementDelay;
						this.CheckState(0);
					}
					else
					{
						this.MinusDelays(this.m_currentLiving.Delay);
						bool flag8 = this.m_currentLiving is Player;
						if (flag8)
						{
							base.UpdateWind(base.GetNextWind(), false);
						}
						this.CurrentTurnTotalDamage = 0;
						this.m_currentLiving.PrepareSelfTurn();
						bool flag9 = this.m_currentLiving.IsLiving && !this.m_currentLiving.IsFrost && !this.m_currentLiving.BlockTurn;
						if (flag9)
						{
							this.m_currentLiving.StartAttacking();
							this.SendSyncLifeTime();
							base.SendGameNextTurn(this.m_currentLiving, this, list);
							bool isAttacking2 = this.m_currentLiving.IsAttacking;
							if (isAttacking2)
							{
								base.AddAction(new WaitLivingAttackingAction(this.m_currentLiving, base.m_turnIndex, (base.getTurnTime() + 20) * 1000));
							}
						}
					}
				}
				base.OnBeginNewTurn();
				try
				{
					this.m_missionAI.OnBeginNewTurn();
				}
				catch (Exception ex2)
				{
					PVEGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex2);
				}
			}
		}

		// Token: 0x06007485 RID: 29829 RVA: 0x00268D6C File Offset: 0x00266F6C
		public void LivingRandSay()
		{
			bool flag = this.m_livings == null || this.m_livings.Count == 0;
			if (!flag)
			{
				int count = this.m_livings.Count;
				foreach (Living living in this.m_livings)
				{
					living.IsSay = false;
				}
				bool flag2 = base.TurnIndex % 2 == 0;
				if (!flag2)
				{
					int num = ((count <= 5) ? base.Random.Next(0, 2) : ((count <= 5 || count > 10) ? base.Random.Next(1, 4) : base.Random.Next(1, 3)));
					bool flag3 = num <= 0;
					if (!flag3)
					{
						int[] array = new int[num];
						int i = 0;
						while (i < num)
						{
							int num2 = base.Random.Next(0, count);
							bool flag4 = !this.m_livings[num2].IsSay;
							if (flag4)
							{
								this.m_livings[num2].IsSay = true;
								i++;
							}
						}
					}
				}
			}
		}

		// Token: 0x06007486 RID: 29830 RVA: 0x00268EB8 File Offset: 0x002670B8
		public override bool TakeCard(Player player, bool isSysTake)
		{
			int num = 0;
			for (int i = 0; i < this.Cards.Length; i++)
			{
				bool flag = this.Cards[i] == 0;
				if (flag)
				{
					num = i;
					break;
				}
			}
			return this.TakeCard(player, num, isSysTake);
		}

		// Token: 0x06007487 RID: 29831 RVA: 0x00268F04 File Offset: 0x00267104
		public override bool TakeCard(Player player, int index, bool isSysTake)
		{
			bool flag = player.CanTakeOut == 0;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = !player.IsActive || index < 0 || index > this.Cards.Length || player.FinishTakeCard || this.Cards[index] > 0;
				if (flag3)
				{
					flag2 = false;
				}
				else
				{
					List<ItemInfo> list = null;
					int num = 0;
					int num2 = 0;
					int num3 = 0;
					int num4 = 0;
					int num5 = 0;
					bool flag4 = DropInventory.CopyUserDrop(this.m_missionInfo.Id, ref list) && list != null;
					if (flag4)
					{
						foreach (ItemInfo itemInfo in list)
						{
							num = itemInfo.Template.TemplateID;
							ItemInfo itemInfo2 = ItemInfo.FindSpecialItemInfo(itemInfo, ref num3, ref num4, ref num5);
							bool flag5 = itemInfo2 != null && num > 0;
							if (flag5)
							{
								player.PlayerDetail.AddTemplate(itemInfo2, eBageType.TempBag, itemInfo.Count);
							}
						}
					}
					bool flag6 = base.RoomType == eRoomType.Dungeon || base.RoomType == eRoomType.Boss;
					if (flag6)
					{
						player.CanTakeOut--;
						bool flag7 = player.CanTakeOut == 0;
						if (flag7)
						{
							player.FinishTakeCard = true;
						}
					}
					else
					{
						player.FinishTakeCard = true;
					}
					this.Cards[index] = 1;
					int num6 = num;
					int num7 = num6;
					if (num7 <= -200)
					{
						if (num7 != -300)
						{
							if (num7 == -200)
							{
								num2 = num4;
							}
						}
						else
						{
							num2 = num5;
						}
					}
					else if (num7 != -100)
					{
						if (num7 == 0)
						{
							num = -100;
							num3 = 100;
							num2 = 2000;
						}
					}
					else
					{
						num2 = num3;
					}
					player.PlayerDetail.AddGold(num3);
					player.PlayerDetail.AddMoney(num4);
					player.PlayerDetail.AddGiftToken(num5);
					base.SendGamePlayerTakeCard(player, index, num, num2, isSysTake);
					flag2 = true;
				}
			}
			return flag2;
		}

		// Token: 0x06007488 RID: 29832 RVA: 0x00269100 File Offset: 0x00267300
		public bool CanGameOver()
		{
			bool flag = base.PlayerCount == 0;
			bool flag2;
			if (flag)
			{
				flag2 = true;
			}
			else
			{
				bool flag3 = base.GetDiedPlayerCount() == base.PlayerCount;
				if (flag3)
				{
					this.IsWin = false;
					flag2 = true;
				}
				else
				{
					try
					{
						return this.m_missionAI.CanGameOver();
					}
					catch (Exception ex)
					{
						PVEGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex);
					}
					flag2 = true;
				}
			}
			return flag2;
		}

		// Token: 0x06007489 RID: 29833 RVA: 0x00269184 File Offset: 0x00267384
		public void PrepareGameOver()
		{
			bool flag = base.GameState == eGameState.Playing;
			if (flag)
			{
				this.m_gameState = eGameState.PrepareGameOver;
				try
				{
					this.m_missionAI.OnPrepareGameOver();
				}
				catch (Exception ex)
				{
					PVEGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex);
				}
			}
		}

		// Token: 0x0600748A RID: 29834 RVA: 0x002691EC File Offset: 0x002673EC
		public void GameOver()
		{
			bool flag = base.GameState != eGameState.PrepareGameOver;
			if (!flag)
			{
				try
				{
					this.m_gameState = eGameState.GameOver;
					this.SendUpdateUiData(null);
					try
					{
						this.m_missionAI.OnGameOver();
					}
					catch (Exception ex)
					{
						PVEGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex);
					}
					List<Player> allFightPlayers = base.GetAllFightPlayers();
					this.CurrentTurnTotalDamage = 0;
					this.BossCardCount = 5;
					this.m_bossCardCount = 1;
					bool flag2 = this.HasNextSession();
					bool flag3 = !this.IsWin || !flag2;
					if (flag3)
					{
						this.m_bossCardCount = 0;
					}
					bool flag4 = this.IsWin && !flag2 && !base.IsSpecialPVE();
					if (flag4)
					{
						this.m_bossCardCount = 2;
					}
					GSPacketIn gspacketIn = new GSPacketIn(91);
					gspacketIn.WriteByte(112);
					gspacketIn.WriteInt(this.BossCardCount);
					bool flag5 = flag2 || this.IsShowLargeCards();
					if (flag5)
					{
						gspacketIn.WriteBoolean(true);
						gspacketIn.WriteString(string.Format("show{0}.jpg", 1 + this.SessionId));
						gspacketIn.WriteBoolean(true);
					}
					else
					{
						gspacketIn.WriteBoolean(false);
						gspacketIn.WriteBoolean(false);
					}
					gspacketIn.WriteInt(base.PlayerCount);
					foreach (Player player in allFightPlayers)
					{
						player.OnPlayerClearBuffSkillPet();
						int num = this.CalculateExperience(player);
						int num2 = this.CalculateScore(player);
						int num3 = this.m_missionAI.CalculateScoreGrade(player.TotalAllScore);
						bool flag6 = player.FightBuffers.ConsortionAddPercentGoldOrGP > 0;
						if (flag6)
						{
							num += num * player.FightBuffers.ConsortionAddPercentGoldOrGP / 100;
						}
						player.CanTakeOut = this.BossCardCount;
						bool currentIsHitTarget = player.CurrentIsHitTarget;
						if (currentIsHitTarget)
						{
							player.TotalHitTargetCount++;
						}
						int num4 = this.CalculateHitRate(player.TotalHitTargetCount, player.TotalShootCount);
						player.TotalAllHurt += player.TotalHurt;
						player.TotalAllCure += player.TotalCure;
						player.TotalAllHitTargetCount += player.TotalHitTargetCount;
						player.TotalAllShootCount += player.TotalShootCount;
						player.GainGP = player.PlayerDetail.AddGP(num);
						player.TotalAllExperience += player.GainGP;
						player.TotalAllScore += num2;
						player.BossCardCount = this.BossCardCount;
						gspacketIn.WriteInt(player.PlayerDetail.PlayerCharacter.ID);
						gspacketIn.WriteInt(player.PlayerDetail.PlayerCharacter.Grade);
						gspacketIn.WriteInt(0);
						gspacketIn.WriteInt((player.GainGP > 10000) ? 10000 : player.GainGP);
						gspacketIn.WriteBoolean(this.IsWin);
						gspacketIn.WriteInt(player.BossCardCount);
						gspacketIn.WriteBoolean(false);
						gspacketIn.WriteBoolean(false);
					}
					bool flag7 = this.BossCardCount > 0;
					if (flag7)
					{
						gspacketIn.WriteInt(this.m_gameOverResources.Count);
						foreach (string text in this.m_gameOverResources)
						{
							gspacketIn.WriteString(text);
						}
					}
					this.SendToAll(gspacketIn);
					StringBuilder stringBuilder = new StringBuilder();
					foreach (Player player2 in allFightPlayers)
					{
						stringBuilder.Append(player2.PlayerDetail.PlayerCharacter.ID).Append(",");
						player2.Ready = false;
						player2.PlayerDetail.OnMissionOver(player2.Game, this.IsWin, this.MissionInfo.Id, player2.TurnNum);
					}
					int num5 = (this.IsWin ? 1 : 2);
					string text2 = stringBuilder.ToString();
					string text3 = "";
					string text4 = "";
					StringBuilder stringBuilder2 = new StringBuilder();
					bool flag8 = this.IsWin && this.IsBossWar != "";
					if (flag8)
					{
						stringBuilder2.Append(this.IsBossWar).Append(",");
						foreach (Player player3 in allFightPlayers)
						{
							stringBuilder2.Append("玩家ID:").Append(player3.PlayerDetail.PlayerCharacter.ID).Append(",");
							stringBuilder2.Append("等级:").Append(player3.PlayerDetail.PlayerCharacter.Grade).Append(",");
							stringBuilder2.Append("攻击回合数:").Append(player3.TurnNum).Append(",");
							stringBuilder2.Append("攻击:").Append(player3.PlayerDetail.PlayerCharacter.Attack).Append(",");
							stringBuilder2.Append("防御:").Append(player3.PlayerDetail.PlayerCharacter.Defence).Append(",");
							stringBuilder2.Append("敏捷:").Append(player3.PlayerDetail.PlayerCharacter.Agility).Append(",");
							stringBuilder2.Append("幸运:").Append(player3.PlayerDetail.PlayerCharacter.Luck).Append(",");
							stringBuilder2.Append("伤害:").Append(player3.PlayerDetail.GetBaseAttack()).Append(",");
							stringBuilder2.Append("总血量:").Append(player3.MaxBlood).Append(",");
							stringBuilder2.Append("护甲:").Append(player3.PlayerDetail.GetBaseDefence()).Append(",");
							bool flag9 = player3.PlayerDetail.SecondWeapon != null;
							if (flag9)
							{
								stringBuilder2.Append("副武器:").Append(player3.PlayerDetail.SecondWeapon.TemplateID).Append(",");
								stringBuilder2.Append("副武器强化等级:").Append(player3.PlayerDetail.SecondWeapon.StrengthenLevel).Append(".");
							}
						}
					}
					bool isWin = this.IsWin;
					if (isWin)
					{
						this.OnFirstKill(this.MissionInfo.Id, allFightPlayers);
					}
					this.BossWarField = stringBuilder2.ToString();
					base.OnGameOverLog(base.RoomId, base.RoomType, base.GameType, 0, this.beginTime, DateTime.Now, this.BeginPlayersCount, this.MissionInfo.Id, text2, text3, text4, num5, this.BossWarField);
					base.OnGameOverred();
				}
				catch (Exception ex2)
				{
					this.m_gameState = eGameState.Stopped;
					PVEGame.log.Error(ex2);
				}
			}
		}

		// Token: 0x0600748B RID: 29835 RVA: 0x00269A0C File Offset: 0x00267C0C
		public bool HasNextSession()
		{
			bool flag = base.PlayerCount == 0 || !this.IsWin || this.IsShowLargeCards();
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = this.Misssions.ContainsKey(1 + this.SessionId);
				flag2 = flag3;
			}
			return flag2;
		}

		// Token: 0x0600748C RID: 29836 RVA: 0x00269A60 File Offset: 0x00267C60
		public bool IsShowLargeCards()
		{
			bool flag = !this.IsWin;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = this.Misssions.ContainsKey(1 + this.SessionId) && (this.m_info.ID == 555 || this.m_info.ID == 14);
				flag2 = flag3;
			}
			return flag2;
		}

		// Token: 0x0600748D RID: 29837 RVA: 0x00269ACC File Offset: 0x00267CCC
		public void GameOverAllSession()
		{
			bool flag = base.GameState != eGameState.GameOver;
			if (!flag)
			{
				this.m_gameState = eGameState.ALLSessionStopped;
				try
				{
					this.m_gameAI.OnGameOverAllSession();
				}
				catch (Exception ex)
				{
					PVEGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex);
				}
				List<Player> allFightPlayers = base.GetAllFightPlayers();
				this.SendSyncLifeTime();
				GSPacketIn gspacketIn = new GSPacketIn(91);
				gspacketIn.WriteByte(115);
				gspacketIn.WriteInt(base.PlayerCount);
				foreach (Player player in allFightPlayers)
				{
					bool flag2 = this.m_roomType == eRoomType.Dungeon || this.m_roomType == eRoomType.Boss || this.m_roomType == eRoomType.Academy;
					if (flag2)
					{
						player.CanTakeOut = (this.IsWin ? player.PlayerDetail.PlayerCharacter.TakeCardNum : 0);
					}
					else
					{
						player.CanTakeOut = (this.IsWin ? 1 : 0);
					}
					int num = this.CalculateHitRate(player.TotalAllHitTargetCount, player.TotalAllShootCount);
					int num2 = this.m_gameAI.CalculateScoreGrade(player.TotalAllScore);
					gspacketIn.WriteInt(player.PlayerDetail.PlayerCharacter.ID);
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(player.GainGP);
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(player.TotalAllExperience);
					gspacketIn.WriteBoolean(this.IsWin);
					bool flag3 = base.GameType == eGameType.FightLib && this.IsWin;
					if (flag3)
					{
						player.PlayerDetail.SetFightLabPermission(this.m_info.ID, this.m_hardLevel, this.MissionInfo.Id);
					}
					player.PlayerDetail.OnGameOver(this, this.IsWin, player.GainGP, player.Blood, base.CountPlayersTeam(player.Team), base.PlayerCount, player.PlayerDetail.GPApprenticeTeam > 0.0, player.PlayerDetail.GPSpouseTeam > 0.0, false);
				}
				gspacketIn.WriteInt(this.m_gameOverResources.Count);
				foreach (string text in this.m_gameOverResources)
				{
					gspacketIn.WriteString(text);
				}
				this.SendToAll(gspacketIn);
				base.WaitTime(21000);
				this.CanStopGame();
			}
		}

		// Token: 0x0600748E RID: 29838 RVA: 0x0002B438 File Offset: 0x00029638
		public void CanStopGame()
		{
			this.SendTempStyle(0);
		}

		// Token: 0x0600748F RID: 29839 RVA: 0x00269E10 File Offset: 0x00268010
		public void ShowLargeCard()
		{
			bool flag = base.GameState != eGameState.ALLSessionStopped || !this.IsWin;
			if (!flag)
			{
				List<Player> allFightPlayers = base.GetAllFightPlayers();
				foreach (Player player in allFightPlayers)
				{
					bool flag2 = player.IsActive && player.CanTakeOut > 0;
					if (flag2)
					{
						player.HasPaymentTakeCard = true;
						int canTakeOut = player.CanTakeOut;
						for (int i = 0; i < canTakeOut; i++)
						{
							this.TakeCard(player, true);
						}
					}
				}
				this.SendShowCards();
			}
		}

		// Token: 0x06007490 RID: 29840 RVA: 0x00269ED8 File Offset: 0x002680D8
		public void PrepareFightingLivings()
		{
			bool flag = base.GameState == eGameState.GameStart;
			if (flag)
			{
				this.m_gameState = eGameState.Playing;
				this.SendSyncLifeTime();
				base.WaitTime(base.PlayerCount * 1000);
				try
				{
					this.m_missionAI.OnPrepareNewGame();
				}
				catch (Exception ex)
				{
					BaseGame.log.ErrorFormat("game ai script {0} error:{1}", base.GameState, ex);
				}
			}
		}

		// Token: 0x06007491 RID: 29841 RVA: 0x00269F58 File Offset: 0x00268158
		public override void Stop()
		{
			bool flag = base.GameState != eGameState.ALLSessionStopped;
			if (!flag)
			{
				this.m_gameState = eGameState.Stopped;
				List<Player> allFightPlayers = base.GetAllFightPlayers();
				bool isWin = this.IsWin;
				if (isWin)
				{
					foreach (Player player in allFightPlayers)
					{
						bool flag2 = player.IsActive && player.CanTakeOut > 0;
						if (flag2)
						{
							player.HasPaymentTakeCard = true;
							int canTakeOut = player.CanTakeOut;
							for (int i = 0; i < canTakeOut; i++)
							{
								this.TakeCard(player, true);
							}
						}
					}
					bool flag3 = base.RoomType == eRoomType.Dungeon || base.RoomType == eRoomType.Boss || base.RoomType == eRoomType.Academy;
					if (flag3)
					{
						this.SendShowCards();
					}
					bool flag4 = base.GameType == eGameType.Dungeon;
					if (flag4)
					{
						foreach (Player player2 in allFightPlayers)
						{
							player2.PlayerDetail.SetPvePermission(this.m_info.ID, this.m_hardLevel);
						}
					}
				}
				bool flag5 = this.Misssions.ContainsKey(1 + this.SessionId);
				foreach (Player player3 in allFightPlayers)
				{
					player3.PlayerDetail.ResetRoom(this.IsWin, flag5.ToString());
				}
				Dictionary<int, Player> players = this.m_players;
				lock (players)
				{
					this.m_players.Clear();
				}
				base.OnGameStopped();
			}
		}

		// Token: 0x06007492 RID: 29842 RVA: 0x0026A178 File Offset: 0x00268378
		protected override void Dispose(bool disposing)
		{
			base.Dispose(disposing);
			foreach (Living living in this.m_livings)
			{
				living.Dispose();
			}
			try
			{
				this.m_missionAI.Dispose();
			}
			catch (Exception ex)
			{
				PVEGame.log.ErrorFormat("game ai script m_missionAI.Dispose() error:{1}", ex);
			}
			try
			{
				this.m_gameAI.Dispose();
			}
			catch (Exception ex2)
			{
				PVEGame.log.ErrorFormat("game ai script m_gameAI.Dispose() error:{1}", ex2);
			}
		}

		// Token: 0x06007493 RID: 29843 RVA: 0x0026A240 File Offset: 0x00268440
		public void DoOther()
		{
			try
			{
				this.m_missionAI.DoOther();
			}
			catch (Exception ex)
			{
				PVEGame.log.ErrorFormat("game ai script m_gameAI.DoOther() error:{1}", ex);
			}
		}

		// Token: 0x06007494 RID: 29844 RVA: 0x0026A284 File Offset: 0x00268484
		internal void OnShooted()
		{
			try
			{
				this.m_missionAI.OnShooted();
			}
			catch (Exception ex)
			{
				PVEGame.log.ErrorFormat("game ai script m_gameAI.OnShooted() error:{1}", ex);
			}
		}

		// Token: 0x06007495 RID: 29845 RVA: 0x0026A2C8 File Offset: 0x002684C8
		internal void OnDied()
		{
			try
			{
				this.m_missionAI.OnDied();
			}
			catch (Exception ex)
			{
				PVEGame.log.ErrorFormat("game ai script m_gameAI.OnDied() error:{0}", ex);
			}
		}

		// Token: 0x06007496 RID: 29846 RVA: 0x0026A30C File Offset: 0x0026850C
		internal void OnMoving()
		{
			try
			{
				this.m_missionAI.OnMoving();
			}
			catch (Exception ex)
			{
				PVEGame.log.ErrorFormat("game ai script m_gameAI.OnMoving() error:{0}", ex);
			}
		}

		// Token: 0x06007497 RID: 29847 RVA: 0x0026A350 File Offset: 0x00268550
		internal void OnTakeDamage()
		{
			try
			{
				this.m_missionAI.OnTakeDamage();
			}
			catch (Exception ex)
			{
				PVEGame.log.ErrorFormat("game ai script m_gameAI.OnTakeDamage() error:{0}", ex);
			}
		}

		// Token: 0x06007498 RID: 29848 RVA: 0x0026A394 File Offset: 0x00268594
		private int CalculateExperience(Player p)
		{
			bool flag = this.TotalKillCount == 0;
			int num;
			if (flag)
			{
				num = 1;
			}
			else
			{
				double num2 = Math.Abs((double)p.Grade - this.TotalNpcGrade / (double)this.TotalKillCount);
				bool flag2 = num2 >= 7.0;
				if (flag2)
				{
					num = 1;
				}
				else
				{
					double num3 = 0.0;
					bool flag3 = this.TotalKillCount > 0;
					if (flag3)
					{
						num3 += (double)p.TotalKill / (double)this.TotalKillCount * 0.4;
					}
					bool flag4 = this.TotalHurt > 0;
					if (flag4)
					{
						num3 += (double)p.TotalHurt / (double)this.TotalHurt * 0.4;
					}
					bool isLiving = p.IsLiving;
					if (isLiving)
					{
						num3 += 0.4;
					}
					double num4 = 1.0;
					bool flag5 = num2 >= 3.0 && num2 <= 4.0;
					if (flag5)
					{
						num4 = 0.7;
					}
					else
					{
						bool flag6 = num2 >= 5.0 && num2 <= 6.0;
						if (flag6)
						{
							num4 = 0.4;
						}
					}
					double num5 = (0.9 + (double)(this.BeginPlayersCount - 1) * 0.4) / (double)base.PlayerCount;
					double num6 = this.TotalNpcExperience * num3 * num4 * num5;
					num6 = ((num6 == 0.0) ? 1.0 : num6);
					num = (int)num6;
				}
			}
			return num;
		}

		// Token: 0x06007499 RID: 29849 RVA: 0x0026A544 File Offset: 0x00268744
		private int CalculateScore(Player p)
		{
			int num = (200 - base.TurnIndex) * 5 + p.TotalKill * 5 + (int)((double)p.Blood / (double)p.MaxBlood) * 10;
			bool flag = !this.IsWin;
			if (flag)
			{
				num -= 400;
			}
			return num;
		}

		// Token: 0x0600749A RID: 29850 RVA: 0x0026A59C File Offset: 0x0026879C
		private int CalculateHitRate(int hitTargetCount, int shootCount)
		{
			double num = 0.0;
			bool flag = shootCount > 0;
			if (flag)
			{
				num = (double)hitTargetCount / (double)shootCount;
			}
			return (int)(num * 100.0);
		}

		// Token: 0x0600749B RID: 29851 RVA: 0x0026A5D4 File Offset: 0x002687D4
		public void TakeSnow()
		{
			ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(201144);
			bool flag = itemTemplateInfo == null;
			if (!flag)
			{
				int num = base.Random.Next(1, 9);
				ItemInfo itemInfo = ItemInfo.CreateFromTemplate(itemTemplateInfo, num, 101);
				itemInfo.IsBinds = true;
				string text = "";
				foreach (Player player in base.GetAllFightPlayers())
				{
					player.PlayerDetail.AddTemplate(itemInfo, itemInfo.Template.BagType, num);
					text += LanguageMgr.GetTranslation("PVEGame.Msg1", new object[]
					{
						itemInfo.Template.Name,
						num
					});
					player.PlayerDetail.SendMessage(text);
				}
			}
		}

		// Token: 0x0600749C RID: 29852 RVA: 0x0002B443 File Offset: 0x00029643
		public override void CheckState(int delay)
		{
			base.AddAction(new CheckPVEGameStateAction(delay));
		}

		// Token: 0x0600749D RID: 29853 RVA: 0x0026A6C0 File Offset: 0x002688C0
		public bool TakeBossCard(Player player)
		{
			int num = 0;
			for (int i = 0; i < this.BossCards.Length; i++)
			{
				bool flag = this.Cards[i] == 0;
				if (flag)
				{
					num = i;
					break;
				}
			}
			return this.TakeBossCard(player, num);
		}

		// Token: 0x0600749E RID: 29854 RVA: 0x0026A70C File Offset: 0x0026890C
		public bool TakeBossCard(Player player, int index)
		{
			bool flag = player.BossCardCount <= 0 || index < 0 || index > this.BossCards.Length || this.BossCards[index] > 0;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				List<ItemInfo> list = null;
				int num = 0;
				int num2 = 0;
				int num3 = 0;
				int num4 = 0;
				int num5 = 0;
				Console.WriteLine("Map.Info.ID{0}", base.Map.Info.ID);
				bool flag3 = DropInventory.BossDrop(base.Map.Info.ID, ref list) && list != null;
				if (flag3)
				{
					foreach (ItemInfo itemInfo in list)
					{
						num = itemInfo.Template.TemplateID;
						ItemInfo itemInfo2 = ItemInfo.FindSpecialItemInfo(itemInfo, ref num3, ref num4, ref num5);
						bool flag4 = itemInfo2 != null && num > 0;
						if (flag4)
						{
							player.PlayerDetail.AddTemplate(itemInfo2, eBageType.TempBag, itemInfo.Count);
						}
					}
				}
				player.BossCardCount--;
				this.BossCards[index] = 1;
				int num6 = num;
				int num7 = num6;
				if (num7 <= -200)
				{
					if (num7 != -300)
					{
						if (num7 == -200)
						{
							num2 = num4;
						}
					}
					else
					{
						num2 = num5;
					}
				}
				else if (num7 != -100)
				{
					if (num7 == 0)
					{
						num = -100;
						num3 = 100;
						num2 = 2000;
					}
				}
				else
				{
					num2 = num3;
				}
				player.PlayerDetail.AddGold(num3);
				player.PlayerDetail.AddMoney(num4);
				player.PlayerDetail.AddGiftToken(num5);
				base.SendGamePlayerTakeCard(player, index, num, num2, true);
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x0600749F RID: 29855 RVA: 0x0026A8D0 File Offset: 0x00268AD0
		public void SendMissionTryAgain()
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(119);
			gspacketIn.WriteInt(this.WantTryAgain);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x060074A0 RID: 29856 RVA: 0x0026A904 File Offset: 0x00268B04
		public void SendMissionInfo(Player p)
		{
			bool flag = this.m_missionInfo != null;
			if (flag)
			{
				GSPacketIn gspacketIn = new GSPacketIn(91);
				gspacketIn.WriteByte(113);
				gspacketIn.WriteString(this.m_missionInfo.Name);
				gspacketIn.WriteString(this.m_missionInfo.Success);
				gspacketIn.WriteString(this.m_missionInfo.Failure);
				gspacketIn.WriteString(this.m_missionInfo.Description);
				gspacketIn.WriteString(this.m_missionInfo.Title);
				gspacketIn.WriteInt(this.TotalMissionCount);
				gspacketIn.WriteInt(this.SessionId);
				gspacketIn.WriteInt(this.TotalTurn);
				gspacketIn.WriteInt(this.TotalCount);
				gspacketIn.WriteInt(this.Param1);
				gspacketIn.WriteInt(this.Param2);
				gspacketIn.WriteInt(this.WantTryAgain);
				bool flag2 = p != null;
				if (flag2)
				{
					p.PlayerDetail.SendTCP(gspacketIn);
				}
				else
				{
					this.SendToAll(gspacketIn);
				}
			}
		}

		// Token: 0x060074A1 RID: 29857 RVA: 0x0026AA10 File Offset: 0x00268C10
		public void SendUpdateUiData(Player p)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(104);
			int num = 0;
			try
			{
				num = this.m_missionAI.UpdateUIData();
			}
			catch (Exception ex)
			{
				PVEGame.log.ErrorFormat("game ai script {0} error:{1}", "m_missionAI.UpdateUIData()", ex);
			}
			gspacketIn.WriteInt(base.TurnIndex);
			gspacketIn.WriteInt(num);
			gspacketIn.WriteInt(this.Param3);
			gspacketIn.WriteInt(this.Param4);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x060074A2 RID: 29858 RVA: 0x0026AAA4 File Offset: 0x00268CA4
		internal void SendShowCards()
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(89);
			int num = 0;
			List<int> list = new List<int>();
			for (int i = 0; i < this.Cards.Length; i++)
			{
				bool flag = this.Cards[i] == 0;
				if (flag)
				{
					list.Add(i);
					num++;
				}
			}
			gspacketIn.WriteInt(num);
			int num2 = 0;
			int num3 = 0;
			int num4 = this.m_missionInfo.Id;
			bool flag2 = this.Misssions.ContainsKey(this.PreSessionId);
			if (flag2)
			{
				num4 = this.Misssions[this.PreSessionId].Id;
			}
			foreach (int num5 in list)
			{
				List<ItemInfo> list2 = DropInventory.CopySystemDrop(num4, list.Count);
				bool flag3 = list2 != null;
				if (flag3)
				{
					foreach (ItemInfo itemInfo in list2)
					{
						num2 = itemInfo.TemplateID;
						num3 = itemInfo.Count;
					}
				}
				gspacketIn.WriteByte((byte)num5);
				gspacketIn.WriteInt(num2);
				gspacketIn.WriteInt(num3);
			}
			this.SendToAll(gspacketIn);
		}

		// Token: 0x060074A3 RID: 29859 RVA: 0x0026AC2C File Offset: 0x00268E2C
		public void SendPassDrama(bool isShowPassButton)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(133);
			gspacketIn.WriteBoolean(isShowPassButton);
			base.SendToAll(gspacketIn);
		}

		// Token: 0x060074A4 RID: 29860 RVA: 0x0026AC60 File Offset: 0x00268E60
		public void SendPlayBackgroundSound(bool isPlay)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(71);
			gspacketIn.WriteBoolean(isPlay);
			base.SendToAll(gspacketIn);
		}

		// Token: 0x060074A5 RID: 29861 RVA: 0x0026AC90 File Offset: 0x00268E90
		public void SendLivingToTop(Living living)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(70);
			gspacketIn.WriteInt(living.Id);
			base.SendToAll(gspacketIn);
		}

		// Token: 0x060074A6 RID: 29862 RVA: 0x0002B453 File Offset: 0x00029653
		public new void SendSyncLifeTime()
		{
			base.SendSyncLifeTime();
		}

		// Token: 0x060074A7 RID: 29863 RVA: 0x0026ACC4 File Offset: 0x00268EC4
		public void SendGameObjectFocus(int type, string name, int delay, int finishTime)
		{
			Physics[] array = base.FindPhysicalObjByName(name);
			Physics[] array2 = array;
			Physics[] array3 = array2;
			Physics[] array4 = array3;
			Physics[] array5 = array4;
			Physics[] array6 = array5;
			Physics[] array7 = array6;
			Physics[] array8 = array7;
			foreach (Physics physics in array8)
			{
				base.AddAction(new FocusAction(physics, type, delay, finishTime));
			}
		}

		// Token: 0x060074A8 RID: 29864 RVA: 0x0002B39B File Offset: 0x0002959B
		public void SendGameObjectFocus(Physics obj, int type, int delay, int finishTime)
		{
			base.AddAction(new FocusAction(obj, type, delay, finishTime));
		}

		// Token: 0x060074A9 RID: 29865 RVA: 0x0002B3AF File Offset: 0x000295AF
		public void SendGameObjectFocus(Physics p, int delay, int finishTime)
		{
			base.AddAction(new FocusAction(p, 1, delay, finishTime));
		}

		// Token: 0x060074AA RID: 29866 RVA: 0x0002B45D File Offset: 0x0002965D
		public void SendFreeFocus(int x, int y, int type, int delay, int finishTime)
		{
			base.AddAction(new FocusFreeAction(x, y, type, delay, finishTime));
		}

		// Token: 0x060074AB RID: 29867 RVA: 0x0002B473 File Offset: 0x00029673
		public void SendGameObjectFocus(int x, int y, int type, int delay, int finishTime)
		{
			this.Createlayer(x, y, "pic", "", "", 1, 0);
			this.SendGameObjectFocus(1, "pic", delay, finishTime);
		}

		// Token: 0x060074AC RID: 29868 RVA: 0x0002B4A1 File Offset: 0x000296A1
		public void SendHideBlood(Living living, int hide)
		{
			base.SendLivingShowBlood(living, hide);
		}

		// Token: 0x060074AD RID: 29869 RVA: 0x0026AD24 File Offset: 0x00268F24
		public void SendHideBlood(List<SimpleNpc> livings, int hide)
		{
			foreach (SimpleNpc simpleNpc in livings)
			{
				base.SendLivingShowBlood(simpleNpc, hide);
			}
		}

		// Token: 0x060074AE RID: 29870 RVA: 0x0026AD7C File Offset: 0x00268F7C
		public void SendLivingActionMapping(Living liv, string source, string value)
		{
			bool flag = liv != null;
			if (flag)
			{
				base.SendLivingActionMapping(liv.Id, source, value);
			}
		}

		// Token: 0x060074AF RID: 29871 RVA: 0x0026ADA4 File Offset: 0x00268FA4
		private void SendCreateGameToSingle(PVEGame game, IGamePlayer gamePlayer)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(121);
			gspacketIn.WriteInt(game.Map.Info.ID);
			gspacketIn.WriteInt((int)((byte)game.RoomType));
			gspacketIn.WriteInt((int)((byte)game.GameType));
			gspacketIn.WriteInt(game.TimeType);
			List<Player> allFightPlayers = game.GetAllFightPlayers();
			gspacketIn.WriteInt(allFightPlayers.Count);
			foreach (Player player in allFightPlayers)
			{
				IGamePlayer playerDetail = player.PlayerDetail;
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ID);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.NickName);
				gspacketIn.WriteBoolean(playerDetail.PlayerCharacter.isViewer);
				gspacketIn.WriteByte(playerDetail.PlayerCharacter.TypeVIP);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.VIPLevel);
				gspacketIn.WriteBoolean(playerDetail.PlayerCharacter.Sex);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Hide);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Style);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Colors);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Skin);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Grade);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Repute);
				bool flag = playerDetail.MainWeapon == null;
				if (flag)
				{
					gspacketIn.WriteInt(0);
				}
				else
				{
					bool flag2 = playerDetail.MainWeapon.GoldValidDate();
					if (flag2)
					{
						gspacketIn.WriteInt(playerDetail.MainWeapon.GoldEquip.TemplateID);
					}
					else
					{
						gspacketIn.WriteInt(playerDetail.MainWeapon.TemplateID);
					}
					gspacketIn.WriteInt(playerDetail.MainWeapon.RefineryLevel);
					gspacketIn.WriteString(playerDetail.MainWeapon.Template.Name);
					gspacketIn.WriteDateTime(DateTime.MinValue);
				}
				gspacketIn.WriteInt((playerDetail.SecondWeapon != null) ? playerDetail.SecondWeapon.TemplateID : 0);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ConsortiaID);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.ConsortiaName);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.badgeID);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ConsortiaLevel);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ConsortiaRepute);
				gspacketIn.WriteBoolean(playerDetail.PlayerCharacter.DailyLeagueFirst);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.DailyLeagueLastScore);
				gspacketIn.WriteInt(player.Team);
				gspacketIn.WriteInt(player.Id);
				gspacketIn.WriteInt(player.MaxBlood);
				gspacketIn.WriteBoolean(player.Ready);
			}
			int sessionId = game.SessionId;
			MissionInfo missionInfo = game.Misssions[sessionId];
			gspacketIn.WriteString(missionInfo.Name);
			gspacketIn.WriteString(string.Format("show{0}.jpg", sessionId));
			gspacketIn.WriteString(missionInfo.Success);
			gspacketIn.WriteString(missionInfo.Failure);
			gspacketIn.WriteString(missionInfo.Description);
			gspacketIn.WriteInt(game.TotalMissionCount);
			gspacketIn.WriteInt(sessionId);
			gamePlayer.SendTCP(gspacketIn);
		}

		// Token: 0x060074B0 RID: 29872 RVA: 0x0026B148 File Offset: 0x00269348
		public void SendPlaySound(string playStr)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(63);
			gspacketIn.WriteString(playStr);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x060074B1 RID: 29873 RVA: 0x0026B178 File Offset: 0x00269378
		public void SendLoadResource(List<LoadingFileInfo> loadingFileInfos)
		{
			bool flag = loadingFileInfos == null || loadingFileInfos.Count <= 0;
			if (!flag)
			{
				GSPacketIn gspacketIn = new GSPacketIn(91);
				gspacketIn.WriteByte(67);
				gspacketIn.WriteInt(loadingFileInfos.Count);
				foreach (LoadingFileInfo loadingFileInfo in loadingFileInfos)
				{
					gspacketIn.WriteInt(loadingFileInfo.Type);
					gspacketIn.WriteString(loadingFileInfo.Path);
					gspacketIn.WriteString(loadingFileInfo.ClassName);
				}
				this.SendToAll(gspacketIn);
			}
		}

		// Token: 0x060074B2 RID: 29874 RVA: 0x0002B4AD File Offset: 0x000296AD
		public void SendPlayersPicture(Living living, int type, bool state)
		{
			base.SendPlayerPicture(living, type, state);
		}

		// Token: 0x060074B3 RID: 29875 RVA: 0x0026B22C File Offset: 0x0026942C
		public override void MinusDelays(int lowestDelay)
		{
			int[] array = this.m_NpcTurnQueue.Keys.ToArray<int>();
			int[] array2 = array;
			int[] array3 = array2;
			int[] array4 = array3;
			foreach (int num in array4)
			{
				this.m_NpcTurnQueue[num] = ((this.m_NpcTurnQueue[num] - lowestDelay > 0) ? (this.m_NpcTurnQueue[num] - lowestDelay) : 0);
			}
			base.MinusDelays(lowestDelay);
		}

		// Token: 0x060074B4 RID: 29876 RVA: 0x0002B4BA File Offset: 0x000296BA
		public void Print(string str)
		{
			Console.WriteLine(str);
		}

		// Token: 0x060074B5 RID: 29877 RVA: 0x0002B4BA File Offset: 0x000296BA
		public void SendGameMessage(string message)
		{
			Console.WriteLine(message);
		}

		// Token: 0x0400440F RID: 17423
		private new static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04004410 RID: 17424
		private APVEGameControl m_gameAI = null;

		// Token: 0x04004411 RID: 17425
		private AMissionControl m_missionAI = null;

		// Token: 0x04004412 RID: 17426
		public int SessionId;

		// Token: 0x04004413 RID: 17427
		public int PreSessionId;

		// Token: 0x04004414 RID: 17428
		public bool CanShowBigBox;

		// Token: 0x04004415 RID: 17429
		public bool IsWin;

		// Token: 0x04004416 RID: 17430
		public bool CanEnd = false;

		// Token: 0x04004417 RID: 17431
		public int TotalMissionCount;

		// Token: 0x04004418 RID: 17432
		public int TotalCount;

		// Token: 0x04004419 RID: 17433
		public int TotalTurn;

		// Token: 0x0400441A RID: 17434
		public int Param1;

		// Token: 0x0400441B RID: 17435
		public int Param2;

		// Token: 0x0400441C RID: 17436
		public int Param3;

		// Token: 0x0400441D RID: 17437
		public int Param4;

		// Token: 0x0400441E RID: 17438
		public int Param5;

		// Token: 0x0400441F RID: 17439
		public int Param6;

		// Token: 0x04004420 RID: 17440
		public int Param7;

		// Token: 0x04004421 RID: 17441
		public int Param8;

		// Token: 0x04004422 RID: 17442
		public Living ParamLiving;

		// Token: 0x04004423 RID: 17443
		public Living ParamLiving1;

		// Token: 0x04004424 RID: 17444
		public Living ParamLiving2;

		// Token: 0x04004425 RID: 17445
		public Living ParamLiving3;

		// Token: 0x04004426 RID: 17446
		public string Pic;

		// Token: 0x04004427 RID: 17447
		public int TotalKillCount;

		// Token: 0x04004428 RID: 17448
		public double TotalNpcExperience;

		// Token: 0x04004429 RID: 17449
		public double TotalNpcGrade;

		// Token: 0x0400442A RID: 17450
		private int BeginPlayersCount;

		// Token: 0x0400442B RID: 17451
		private PveInfo m_info;

		// Token: 0x0400442C RID: 17452
		private List<string> m_gameOverResources;

		// Token: 0x0400442D RID: 17453
		public Dictionary<int, MissionInfo> Misssions;

		// Token: 0x0400442E RID: 17454
		private MapPoint mapPos;

		// Token: 0x0400442F RID: 17455
		public int WantTryAgain;

		// Token: 0x04004430 RID: 17456
		public bool OpenTryAgain;

		// Token: 0x04004431 RID: 17457
		public bool MissionTryAgain;

		// Token: 0x04004432 RID: 17458
		private eHardLevel m_hardLevel;

		// Token: 0x04004433 RID: 17459
		private string m_IsBossType;

		// Token: 0x04004434 RID: 17460
		private bool m_isPassDrama;

		// Token: 0x04004435 RID: 17461
		private int m_countAward;

		// Token: 0x04004436 RID: 17462
		public bool IsKillWorldBoss;

		// Token: 0x04004437 RID: 17463
		public long WorldbossBood;

		// Token: 0x04004438 RID: 17464
		public long AllWorldDameBoss;

		// Token: 0x04004439 RID: 17465
		private List<Point> PlayersXY = new List<Point>();

		// Token: 0x0400443A RID: 17466
		private Dictionary<int, int> m_NpcTurnQueue = new Dictionary<int, int>();

		// Token: 0x0400443B RID: 17467
		private MissionInfo m_missionInfo;

		// Token: 0x0400443C RID: 17468
		public DateTime WaitingTimeResponseTryAgain;

		// Token: 0x0400443D RID: 17469
		private int waitingTime = 18;

		// Token: 0x0400443E RID: 17470
		private List<int> m_mapHistoryIds;

		// Token: 0x0400443F RID: 17471
		public int[] BossCards;

		// Token: 0x04004440 RID: 17472
		private int m_bossCardCount;

		// Token: 0x04004441 RID: 17473
		private int m_pveGameDelay;
	}
}
