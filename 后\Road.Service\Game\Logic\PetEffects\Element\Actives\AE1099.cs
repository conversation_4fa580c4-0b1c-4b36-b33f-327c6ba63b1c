﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DC7 RID: 3527
	public class AE1099 : BasePetEffect
	{
		// Token: 0x06007C86 RID: 31878 RVA: 0x00297F04 File Offset: 0x00296104
		public AE1099(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1099, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C87 RID: 31879 RVA: 0x00297F84 File Offset: 0x00296184
		public override bool Start(Living living)
		{
			AE1099 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1099) as AE1099;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C88 RID: 31880 RVA: 0x0002FB60 File Offset: 0x0002DD60
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C89 RID: 31881 RVA: 0x0002FB76 File Offset: 0x0002DD76
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C8A RID: 31882 RVA: 0x00297FE4 File Offset: 0x002961E4
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.AddPetEffect(new CE1099(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004AAB RID: 19115
		private int m_type = 0;

		// Token: 0x04004AAC RID: 19116
		private int m_count = 0;

		// Token: 0x04004AAD RID: 19117
		private int m_probability = 0;

		// Token: 0x04004AAE RID: 19118
		private int m_delay = 0;

		// Token: 0x04004AAF RID: 19119
		private int m_coldDown = 0;

		// Token: 0x04004AB0 RID: 19120
		private int m_currentId;

		// Token: 0x04004AB1 RID: 19121
		private int m_added = 0;
	}
}
