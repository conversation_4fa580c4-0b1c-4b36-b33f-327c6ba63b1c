﻿using System;
using System.Drawing;

namespace Game.Logic.Phy.Maths
{
	// Token: 0x02000CE5 RID: 3301
	public static class PointHelper
	{
		// Token: 0x060077AB RID: 30635 RVA: 0x0028157C File Offset: 0x0027F77C
		public static Point Normalize(this Point point, int len)
		{
			double num = point.Length();
			return new Point((int)((double)(point.X * len) / num), (int)((double)(point.Y * len) / num));
		}

		// Token: 0x060077AC RID: 30636 RVA: 0x002815B4 File Offset: 0x0027F7B4
		public static double Length(this Point point)
		{
			return Math.Sqrt((double)(point.X * point.X + point.Y * point.Y));
		}

		// Token: 0x060077AD RID: 30637 RVA: 0x002815EC File Offset: 0x0027F7EC
		public static double Distance(this Point point, Point target)
		{
			int num = point.X - target.X;
			int num2 = point.Y - target.Y;
			return Math.Sqrt((double)(num * num + num2 * num2));
		}

		// Token: 0x060077AE RID: 30638 RVA: 0x0028162C File Offset: 0x0027F82C
		public static double Distance(this Point point, int tx, int ty)
		{
			int num = point.X - tx;
			int num2 = point.Y - ty;
			return Math.Sqrt((double)(num * num + num2 * num2));
		}

		// Token: 0x060077AF RID: 30639 RVA: 0x00281660 File Offset: 0x0027F860
		public static PointF Normalize(this PointF point, float len)
		{
			double num = Math.Sqrt((double)(point.X * point.X + point.Y * point.Y));
			return new PointF((float)((double)(point.X * len) / num), (float)((double)(point.Y * len) / num));
		}

		// Token: 0x060077B0 RID: 30640 RVA: 0x002816B8 File Offset: 0x0027F8B8
		public static double Length(this PointF point)
		{
			return Math.Sqrt((double)(point.X * point.X + point.Y * point.Y));
		}
	}
}
