﻿using System;
using System.Collections.Generic;
using System.Text;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FF0 RID: 4080
	public class WorldEventMgr
	{
		// Token: 0x06008B88 RID: 35720 RVA: 0x002FD17C File Offset: 0x002FB37C
		public static bool SendItemToMail(ItemInfo info, int PlayerId, string Nickname, int zoneId, string title, string sender)
		{
			List<ItemInfo> list = new List<ItemInfo> { info };
			return WorldEventMgr.SendItemsToMail(list, PlayerId, Nickname, zoneId, title, 83, sender, null);
		}

		// Token: 0x06008B89 RID: 35721 RVA: 0x002FD1AC File Offset: 0x002FB3AC
		public static bool SendItemsToMails(List<ItemInfo> infos, int PlayerId, string Nickname, int zoneId, string title)
		{
			return WorldEventMgr.SendItemsToMail(infos, PlayerId, Nickname, zoneId, title, 83, null, null);
		}

		// Token: 0x06008B8A RID: 35722 RVA: 0x002FD1D0 File Offset: 0x002FB3D0
		public static bool SendItemsToMail(List<ItemInfo> infos, int PlayerId, string Nickname, int zoneId, string title, int type, string sender, string content)
		{
			bool flag = false;
			using (PlayerBussiness playerBussiness = new PlayerBussiness())
			{
				List<ItemInfo> list = new List<ItemInfo>();
				foreach (ItemInfo itemInfo in infos)
				{
					bool flag2 = itemInfo.Template.MaxCount == 1 && itemInfo.Count > itemInfo.Template.MaxCount;
					if (flag2)
					{
						for (int i = 0; i < itemInfo.Count; i++)
						{
							ItemInfo itemInfo2 = ItemInfo.CloneFromTemplate(itemInfo.Template, itemInfo);
							itemInfo2.Count = 1;
							list.Add(itemInfo2);
						}
					}
					else
					{
						list.Add(itemInfo);
					}
				}
				for (int j = 0; j < list.Count; j += 5)
				{
					UserMailInfo userMailInfo = new UserMailInfo
					{
						Title = title,
						Gold = 0,
						IsExist = true,
						Money = 0,
						Receiver = Nickname,
						ReceiverID = PlayerId,
						Sender = (string.IsNullOrEmpty(sender) ? LanguageMgr.GetTranslation("SystermSender.Msg", Array.Empty<object>()) : sender),
						SenderID = 0,
						Type = type,
						GiftToken = 0
					};
					StringBuilder stringBuilder = new StringBuilder();
					StringBuilder stringBuilder2 = new StringBuilder();
					stringBuilder.Append(LanguageMgr.GetTranslation("Game.Server.GameUtils.CommonBag.AnnexRemark", Array.Empty<object>()));
					int num = j;
					bool flag3 = list.Count > num;
					if (flag3)
					{
						ItemInfo itemInfo3 = list[num];
						bool flag4 = itemInfo3.ItemID == 0;
						if (flag4)
						{
							playerBussiness.AddGoods(itemInfo3);
						}
						userMailInfo.Annex1 = itemInfo3.ItemID.ToString();
						userMailInfo.Annex1Name = itemInfo3.Template.Name;
						stringBuilder.Append(string.Concat(new string[]
						{
							"1、",
							userMailInfo.Annex1Name,
							"x",
							itemInfo3.Count.ToString(),
							";"
						}));
						stringBuilder2.Append(string.Concat(new string[]
						{
							"1、",
							userMailInfo.Annex1Name,
							"x",
							itemInfo3.Count.ToString(),
							";"
						}));
					}
					num = j + 1;
					bool flag5 = list.Count > num;
					if (flag5)
					{
						ItemInfo itemInfo4 = list[num];
						bool flag6 = itemInfo4.ItemID == 0;
						if (flag6)
						{
							playerBussiness.AddGoods(itemInfo4);
						}
						userMailInfo.Annex2 = itemInfo4.ItemID.ToString();
						userMailInfo.Annex2Name = itemInfo4.Template.Name;
						stringBuilder.Append(string.Concat(new string[]
						{
							"2、",
							userMailInfo.Annex2Name,
							"x",
							itemInfo4.Count.ToString(),
							";"
						}));
						stringBuilder2.Append(string.Concat(new string[]
						{
							"2、",
							userMailInfo.Annex2Name,
							"x",
							itemInfo4.Count.ToString(),
							";"
						}));
					}
					num = j + 2;
					bool flag7 = list.Count > num;
					if (flag7)
					{
						ItemInfo itemInfo5 = list[num];
						bool flag8 = itemInfo5.ItemID == 0;
						if (flag8)
						{
							playerBussiness.AddGoods(itemInfo5);
						}
						userMailInfo.Annex3 = itemInfo5.ItemID.ToString();
						userMailInfo.Annex3Name = itemInfo5.Template.Name;
						stringBuilder.Append(string.Concat(new string[]
						{
							"3、",
							userMailInfo.Annex3Name,
							"x",
							itemInfo5.Count.ToString(),
							";"
						}));
						stringBuilder2.Append(string.Concat(new string[]
						{
							"3、",
							userMailInfo.Annex3Name,
							"x",
							itemInfo5.Count.ToString(),
							";"
						}));
					}
					num = j + 3;
					bool flag9 = list.Count > num;
					if (flag9)
					{
						ItemInfo itemInfo6 = list[num];
						bool flag10 = itemInfo6.ItemID == 0;
						if (flag10)
						{
							playerBussiness.AddGoods(itemInfo6);
						}
						userMailInfo.Annex4 = itemInfo6.ItemID.ToString();
						userMailInfo.Annex4Name = itemInfo6.Template.Name;
						stringBuilder.Append(string.Concat(new string[]
						{
							"4、",
							userMailInfo.Annex4Name,
							"x",
							itemInfo6.Count.ToString(),
							";"
						}));
						stringBuilder2.Append(string.Concat(new string[]
						{
							"4、",
							userMailInfo.Annex4Name,
							"x",
							itemInfo6.Count.ToString(),
							";"
						}));
					}
					num = j + 4;
					bool flag11 = list.Count > num;
					if (flag11)
					{
						ItemInfo itemInfo7 = list[num];
						bool flag12 = itemInfo7.ItemID == 0;
						if (flag12)
						{
							playerBussiness.AddGoods(itemInfo7);
						}
						userMailInfo.Annex5 = itemInfo7.ItemID.ToString();
						userMailInfo.Annex5Name = itemInfo7.Template.Name;
						stringBuilder.Append(string.Concat(new string[]
						{
							"5、",
							userMailInfo.Annex5Name,
							"x",
							itemInfo7.Count.ToString(),
							";"
						}));
						stringBuilder2.Append(string.Concat(new string[]
						{
							"5、",
							userMailInfo.Annex5Name,
							"x",
							itemInfo7.Count.ToString(),
							";"
						}));
					}
					userMailInfo.AnnexRemark = stringBuilder.ToString();
					userMailInfo.Content = ((content == null) ? stringBuilder2.ToString() : content);
					flag = playerBussiness.SendMail(userMailInfo);
				}
			}
			return flag;
		}
	}
}
