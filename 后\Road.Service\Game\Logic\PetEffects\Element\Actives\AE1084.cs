﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DB9 RID: 3513
	public class AE1084 : BasePetEffect
	{
		// Token: 0x06007C3E RID: 31806 RVA: 0x00296820 File Offset: 0x00294A20
		public AE1084(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1084, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C3F RID: 31807 RVA: 0x002968A0 File Offset: 0x00294AA0
		public override bool Start(Living living)
		{
			AE1084 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1084) as AE1084;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C40 RID: 31808 RVA: 0x0002F940 File Offset: 0x0002DB40
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C41 RID: 31809 RVA: 0x00296900 File Offset: 0x00294B00
		protected override void OnPausedOnPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Attack -= (double)this.m_added;
			this.IsTrigger = false;
		}

		// Token: 0x06007C42 RID: 31810 RVA: 0x00296950 File Offset: 0x00294B50
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && !this.IsTrigger;
			if (flag)
			{
				this.IsTrigger = true;
				this.m_added = 800;
				player.Attack += (double)this.m_added;
				player.Game.SendPetBuff(player, base.ElementInfo, true);
			}
		}

		// Token: 0x04004A49 RID: 19017
		private int m_type = 0;

		// Token: 0x04004A4A RID: 19018
		private int m_count = 0;

		// Token: 0x04004A4B RID: 19019
		private int m_probability = 0;

		// Token: 0x04004A4C RID: 19020
		private int m_delay = 0;

		// Token: 0x04004A4D RID: 19021
		private int m_coldDown = 0;

		// Token: 0x04004A4E RID: 19022
		private int m_currentId;

		// Token: 0x04004A4F RID: 19023
		private int m_added = 0;
	}
}
