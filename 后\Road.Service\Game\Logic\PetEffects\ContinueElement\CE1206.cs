﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E99 RID: 3737
	public class CE1206 : BasePetEffect
	{
		// Token: 0x06008135 RID: 33077 RVA: 0x002AC194 File Offset: 0x002AA394
		public CE1206(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1206, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008136 RID: 33078 RVA: 0x002AC214 File Offset: 0x002AA414
		public override bool Start(Living living)
		{
			CE1206 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1206) as CE1206;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008137 RID: 33079 RVA: 0x00032902 File Offset: 0x00030B02
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008138 RID: 33080 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008139 RID: 33081 RVA: 0x002AC274 File Offset: 0x002AA474
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				bool flag2 = living.Defence < (double)this.m_added;
				if (flag2)
				{
					this.m_added = (int)living.Defence - 1;
				}
				living.Defence -= (double)this.m_added;
			}
		}

		// Token: 0x0600813A RID: 33082 RVA: 0x002AC2D4 File Offset: 0x002AA4D4
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0600813B RID: 33083 RVA: 0x002AC308 File Offset: 0x002AA508
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Defence += (double)this.m_added;
			this.m_added = 0;
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x0400506C RID: 20588
		private int m_type = 0;

		// Token: 0x0400506D RID: 20589
		private int m_count = 0;

		// Token: 0x0400506E RID: 20590
		private int m_probability = 0;

		// Token: 0x0400506F RID: 20591
		private int m_delay = 0;

		// Token: 0x04005070 RID: 20592
		private int m_coldDown = 0;

		// Token: 0x04005071 RID: 20593
		private int m_currentId;

		// Token: 0x04005072 RID: 20594
		private int m_added = 0;
	}
}
