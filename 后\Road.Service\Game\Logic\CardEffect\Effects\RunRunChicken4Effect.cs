﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F34 RID: 3892
	public class RunRunChicken4Effect : BaseCardEffect
	{
		// Token: 0x170014AA RID: 5290
		// (get) Token: 0x06008468 RID: 33896 RVA: 0x00034E82 File Offset: 0x00033082
		public int ReduceValue
		{
			get
			{
				return this.m_added;
			}
		}

		// Token: 0x06008469 RID: 33897 RVA: 0x002B8044 File Offset: 0x002B6244
		public RunRunChicken4Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.RunRunChicken4, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x0600846A RID: 33898 RVA: 0x002B80B4 File Offset: 0x002B62B4
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.RunRunChicken4) is RunRunChicken4Effect;
			return flag || base.Start(living);
		}

		// Token: 0x0600846B RID: 33899 RVA: 0x00034E8A File Offset: 0x0003308A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x0600846C RID: 33900 RVA: 0x00034EA0 File Offset: 0x000330A0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x0600846D RID: 33901 RVA: 0x002B80EC File Offset: 0x002B62EC
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 7;
			if (flag2)
			{
				this.m_added = this.m_value;
				player.Game.SendMessage(player.PlayerDetail, "您激活了小鸡快跑4件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活小鸡快跑4件套卡.", 3);
			}
		}

		// Token: 0x04005290 RID: 21136
		private int m_indexValue = 0;

		// Token: 0x04005291 RID: 21137
		private int m_value = 0;

		// Token: 0x04005292 RID: 21138
		private int m_added = 0;
	}
}
