﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E26 RID: 3622
	public class AE1269 : BasePetEffect
	{
		// Token: 0x06007E78 RID: 32376 RVA: 0x002A0A08 File Offset: 0x0029EC08
		public AE1269(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1269, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E79 RID: 32377 RVA: 0x002A0A88 File Offset: 0x0029EC88
		public override bool Start(Living living)
		{
			AE1269 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1269) as AE1269;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E7A RID: 32378 RVA: 0x00030F4F File Offset: 0x0002F14F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E7B RID: 32379 RVA: 0x00030F65 File Offset: 0x0002F165
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E7C RID: 32380 RVA: 0x002A0AE8 File Offset: 0x0029ECE8
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				this.m_added = 10;
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.AddPetMP(this.m_added);
				}
			}
		}

		// Token: 0x04004D42 RID: 19778
		private int m_type = 0;

		// Token: 0x04004D43 RID: 19779
		private int m_count = 0;

		// Token: 0x04004D44 RID: 19780
		private int m_probability = 0;

		// Token: 0x04004D45 RID: 19781
		private int m_delay = 0;

		// Token: 0x04004D46 RID: 19782
		private int m_coldDown = 0;

		// Token: 0x04004D47 RID: 19783
		private int m_currentId;

		// Token: 0x04004D48 RID: 19784
		private int m_added = 0;
	}
}
