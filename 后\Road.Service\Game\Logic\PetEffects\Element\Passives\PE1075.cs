﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D59 RID: 3417
	public class PE1075 : BasePetEffect
	{
		// Token: 0x06007A51 RID: 31313 RVA: 0x0028E5A8 File Offset: 0x0028C7A8
		public PE1075(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1075, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A52 RID: 31314 RVA: 0x0028E628 File Offset: 0x0028C828
		public override bool Start(Living living)
		{
			PE1075 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1075) as PE1075;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A53 RID: 31315 RVA: 0x0002E5EB File Offset: 0x0002C7EB
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007A54 RID: 31316 RVA: 0x0028E688 File Offset: 0x0028C888
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 600;
				living.PetEffects.IncreaseAngelicPoint = this.m_added;
			}
		}

		// Token: 0x06007A55 RID: 31317 RVA: 0x0002E601 File Offset: 0x0002C801
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x040047AF RID: 18351
		private int m_type = 0;

		// Token: 0x040047B0 RID: 18352
		private int m_count = 0;

		// Token: 0x040047B1 RID: 18353
		private int m_probability = 0;

		// Token: 0x040047B2 RID: 18354
		private int m_delay = 0;

		// Token: 0x040047B3 RID: 18355
		private int m_coldDown = 0;

		// Token: 0x040047B4 RID: 18356
		private int m_currentId;

		// Token: 0x040047B5 RID: 18357
		private int m_added = 0;
	}
}
