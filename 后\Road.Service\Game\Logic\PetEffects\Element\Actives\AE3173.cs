﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E36 RID: 3638
	public class AE3173 : BasePetEffect
	{
		// Token: 0x06007ED6 RID: 32470 RVA: 0x002A2934 File Offset: 0x002A0B34
		public AE3173(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE3173, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007ED7 RID: 32471 RVA: 0x002A29B0 File Offset: 0x002A0BB0
		public override bool Start(Living living)
		{
			AE3173 ae = living.PetEffectList.GetOfType(ePetEffectType.AE3173) as AE3173;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007ED8 RID: 32472 RVA: 0x000313AE File Offset: 0x0002F5AE
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007ED9 RID: 32473 RVA: 0x000313D7 File Offset: 0x0002F5D7
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007EDA RID: 32474 RVA: 0x002A2A0C File Offset: 0x002A0C0C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 180;
				player.CurrentDamagePlus += (float)(this.m_added / 100);
			}
		}

		// Token: 0x06007EDB RID: 32475 RVA: 0x002A2A58 File Offset: 0x002A0C58
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.CurrentDamagePlus -= (float)(this.m_added / 100);
				this.m_added = 0;
			}
		}

		// Token: 0x04004DB5 RID: 19893
		private int m_type = 0;

		// Token: 0x04004DB6 RID: 19894
		private int m_count = 0;

		// Token: 0x04004DB7 RID: 19895
		private int m_probability = 0;

		// Token: 0x04004DB8 RID: 19896
		private int m_delay = 0;

		// Token: 0x04004DB9 RID: 19897
		private int m_coldDown = 0;

		// Token: 0x04004DBA RID: 19898
		private int m_currentId;

		// Token: 0x04004DBB RID: 19899
		private int m_added = 0;
	}
}
