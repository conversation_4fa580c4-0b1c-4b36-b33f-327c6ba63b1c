﻿using System;

namespace Game.Logic
{
	// Token: 0x02000CA7 RID: 3239
	public class PetEffectInfo
	{
		// Token: 0x170013DB RID: 5083
		// (get) Token: 0x060073D8 RID: 29656 RVA: 0x002646DC File Offset: 0x002628DC
		// (set) Token: 0x060073D9 RID: 29657 RVA: 0x0002B1B6 File Offset: 0x000293B6
		public bool DisibleActiveSkill
		{
			get
			{
				return this.m_DisibleActiveSkill;
			}
			set
			{
				this.m_DisibleActiveSkill = value;
			}
		}

		// Token: 0x170013DC RID: 5084
		// (get) Token: 0x060073DA RID: 29658 RVA: 0x002646F4 File Offset: 0x002628F4
		// (set) Token: 0x060073DB RID: 29659 RVA: 0x0002B1C0 File Offset: 0x000293C0
		public int DamagePercent
		{
			get
			{
				return this.m_DamagePercent;
			}
			set
			{
				this.m_DamagePercent = value;
			}
		}

		// Token: 0x170013DD RID: 5085
		// (get) Token: 0x060073DC RID: 29660 RVA: 0x0026470C File Offset: 0x0026290C
		// (set) Token: 0x060073DD RID: 29661 RVA: 0x0002B1CA File Offset: 0x000293CA
		public bool ActiveGuard
		{
			get
			{
				return this.m_activeGuard;
			}
			set
			{
				this.m_activeGuard = value;
			}
		}

		// Token: 0x170013DE RID: 5086
		// (get) Token: 0x060073DE RID: 29662 RVA: 0x00264724 File Offset: 0x00262924
		// (set) Token: 0x060073DF RID: 29663 RVA: 0x0002B1D4 File Offset: 0x000293D4
		public bool ActivePetHit
		{
			get
			{
				return this.m_activePetHit;
			}
			set
			{
				this.m_activePetHit = value;
			}
		}

		// Token: 0x170013DF RID: 5087
		// (get) Token: 0x060073E0 RID: 29664 RVA: 0x0026473C File Offset: 0x0026293C
		// (set) Token: 0x060073E1 RID: 29665 RVA: 0x0002B1DE File Offset: 0x000293DE
		public int AddAttackValue
		{
			get
			{
				return this.m_addAttackValue;
			}
			set
			{
				this.m_addAttackValue = value;
			}
		}

		// Token: 0x170013E0 RID: 5088
		// (get) Token: 0x060073E2 RID: 29666 RVA: 0x00264754 File Offset: 0x00262954
		// (set) Token: 0x060073E3 RID: 29667 RVA: 0x0002B1E8 File Offset: 0x000293E8
		public int AddDameValue
		{
			get
			{
				return this.m_addDameValue;
			}
			set
			{
				this.m_addDameValue = value;
			}
		}

		// Token: 0x170013E1 RID: 5089
		// (get) Token: 0x060073E4 RID: 29668 RVA: 0x0026476C File Offset: 0x0026296C
		// (set) Token: 0x060073E5 RID: 29669 RVA: 0x0002B1F2 File Offset: 0x000293F2
		public int AddGuardValue
		{
			get
			{
				return this.m_addGuardValue;
			}
			set
			{
				this.m_addGuardValue = value;
			}
		}

		// Token: 0x170013E2 RID: 5090
		// (get) Token: 0x060073E6 RID: 29670 RVA: 0x00264784 File Offset: 0x00262984
		// (set) Token: 0x060073E7 RID: 29671 RVA: 0x0002B1FC File Offset: 0x000293FC
		public int AddLuckValue
		{
			get
			{
				return this.m_addLuckValue;
			}
			set
			{
				this.m_addLuckValue = value;
			}
		}

		// Token: 0x170013E3 RID: 5091
		// (get) Token: 0x060073E8 RID: 29672 RVA: 0x0026479C File Offset: 0x0026299C
		// (set) Token: 0x060073E9 RID: 29673 RVA: 0x0002B206 File Offset: 0x00029406
		public int BonusAgility
		{
			get
			{
				return this.m_bonusAgility;
			}
			set
			{
				this.m_bonusAgility = value;
			}
		}

		// Token: 0x170013E4 RID: 5092
		// (get) Token: 0x060073EA RID: 29674 RVA: 0x002647B4 File Offset: 0x002629B4
		// (set) Token: 0x060073EB RID: 29675 RVA: 0x0002B210 File Offset: 0x00029410
		public int BonusAttack
		{
			get
			{
				return this.m_bonusAttack;
			}
			set
			{
				this.m_bonusAttack = value;
			}
		}

		// Token: 0x170013E5 RID: 5093
		// (get) Token: 0x060073EC RID: 29676 RVA: 0x002647CC File Offset: 0x002629CC
		// (set) Token: 0x060073ED RID: 29677 RVA: 0x0002B21A File Offset: 0x0002941A
		public int BonusBaseDamage
		{
			get
			{
				return this.m_bonusBaseDamage;
			}
			set
			{
				this.m_bonusBaseDamage = value;
			}
		}

		// Token: 0x170013E6 RID: 5094
		// (get) Token: 0x060073EE RID: 29678 RVA: 0x002647E4 File Offset: 0x002629E4
		// (set) Token: 0x060073EF RID: 29679 RVA: 0x0002B224 File Offset: 0x00029424
		public int BonusDefend
		{
			get
			{
				return this.m_bonusDefend;
			}
			set
			{
				this.m_bonusDefend = value;
			}
		}

		// Token: 0x170013E7 RID: 5095
		// (get) Token: 0x060073F0 RID: 29680 RVA: 0x002647FC File Offset: 0x002629FC
		// (set) Token: 0x060073F1 RID: 29681 RVA: 0x0002B22E File Offset: 0x0002942E
		public int BonusGuard
		{
			get
			{
				return this.m_bonusGuard;
			}
			set
			{
				this.m_bonusGuard = value;
			}
		}

		// Token: 0x170013E8 RID: 5096
		// (get) Token: 0x060073F2 RID: 29682 RVA: 0x00264814 File Offset: 0x00262A14
		// (set) Token: 0x060073F3 RID: 29683 RVA: 0x0002B238 File Offset: 0x00029438
		public int BonusLucky
		{
			get
			{
				return this.m_bonusLucky;
			}
			set
			{
				this.m_bonusLucky = value;
			}
		}

		// Token: 0x170013E9 RID: 5097
		// (get) Token: 0x060073F4 RID: 29684 RVA: 0x0026482C File Offset: 0x00262A2C
		// (set) Token: 0x060073F5 RID: 29685 RVA: 0x0002B242 File Offset: 0x00029442
		public int BonusPoint
		{
			get
			{
				return this.m_bonusPoint;
			}
			set
			{
				this.m_bonusPoint = value;
			}
		}

		// Token: 0x170013EA RID: 5098
		// (get) Token: 0x060073F6 RID: 29686 RVA: 0x00264844 File Offset: 0x00262A44
		// (set) Token: 0x060073F7 RID: 29687 RVA: 0x0002B24C File Offset: 0x0002944C
		public bool CritActive
		{
			get
			{
				return this.m_critActive;
			}
			set
			{
				this.m_critActive = value;
			}
		}

		// Token: 0x170013EB RID: 5099
		// (get) Token: 0x060073F8 RID: 29688 RVA: 0x0026485C File Offset: 0x00262A5C
		// (set) Token: 0x060073F9 RID: 29689 RVA: 0x0002B256 File Offset: 0x00029456
		public int CritRate
		{
			get
			{
				return this.m_critRate;
			}
			set
			{
				this.m_critRate = value;
			}
		}

		// Token: 0x170013EC RID: 5100
		// (get) Token: 0x060073FA RID: 29690 RVA: 0x00264874 File Offset: 0x00262A74
		// (set) Token: 0x060073FB RID: 29691 RVA: 0x0002B260 File Offset: 0x00029460
		public int CurrentUseSkill
		{
			get
			{
				return this.m_currentUseSkill;
			}
			set
			{
				this.m_currentUseSkill = value;
			}
		}

		// Token: 0x170013ED RID: 5101
		// (get) Token: 0x060073FC RID: 29692 RVA: 0x0026488C File Offset: 0x00262A8C
		// (set) Token: 0x060073FD RID: 29693 RVA: 0x0002B26A File Offset: 0x0002946A
		public int BallType
		{
			get
			{
				return this.m_BallType;
			}
			set
			{
				this.m_BallType = value;
			}
		}

		// Token: 0x170013EE RID: 5102
		// (get) Token: 0x060073FE RID: 29694 RVA: 0x002648A4 File Offset: 0x00262AA4
		// (set) Token: 0x060073FF RID: 29695 RVA: 0x0002B274 File Offset: 0x00029474
		public bool IsPetUseSkill
		{
			get
			{
				return this.m_isPetUseSkill;
			}
			set
			{
				this.m_isPetUseSkill = value;
			}
		}

		// Token: 0x170013EF RID: 5103
		// (get) Token: 0x06007400 RID: 29696 RVA: 0x002648BC File Offset: 0x00262ABC
		// (set) Token: 0x06007401 RID: 29697 RVA: 0x0002B27E File Offset: 0x0002947E
		public int MaxBlood
		{
			get
			{
				return this.m_maxBlood;
			}
			set
			{
				this.m_maxBlood = value;
			}
		}

		// Token: 0x170013F0 RID: 5104
		// (get) Token: 0x06007402 RID: 29698 RVA: 0x002648D4 File Offset: 0x00262AD4
		// (set) Token: 0x06007403 RID: 29699 RVA: 0x0002B288 File Offset: 0x00029488
		public int PetBaseAtt
		{
			get
			{
				return this.m_petBaseAtt;
			}
			set
			{
				this.m_petBaseAtt = value;
			}
		}

		// Token: 0x170013F1 RID: 5105
		// (get) Token: 0x06007404 RID: 29700 RVA: 0x002648EC File Offset: 0x00262AEC
		// (set) Token: 0x06007405 RID: 29701 RVA: 0x0002B292 File Offset: 0x00029492
		public int PetDelay
		{
			get
			{
				return this.m_petDelay;
			}
			set
			{
				this.m_petDelay = value;
			}
		}

		// Token: 0x170013F2 RID: 5106
		// (get) Token: 0x06007406 RID: 29702 RVA: 0x00264904 File Offset: 0x00262B04
		// (set) Token: 0x06007407 RID: 29703 RVA: 0x0002B29C File Offset: 0x0002949C
		public int ReduceDefendValue
		{
			get
			{
				return this.m_reduceDefendValue;
			}
			set
			{
				this.m_reduceDefendValue = value;
			}
		}

		// Token: 0x170013F3 RID: 5107
		// (get) Token: 0x06007408 RID: 29704 RVA: 0x0026491C File Offset: 0x00262B1C
		// (set) Token: 0x06007409 RID: 29705 RVA: 0x0002B2A6 File Offset: 0x000294A6
		public bool StopMoving
		{
			get
			{
				return this.m_stopMoving;
			}
			set
			{
				this.m_stopMoving = value;
			}
		}

		// Token: 0x040043DC RID: 17372
		private bool m_activeGuard;

		// Token: 0x040043DD RID: 17373
		private bool m_activePetHit;

		// Token: 0x040043DE RID: 17374
		private int m_addAttackValue;

		// Token: 0x040043DF RID: 17375
		private int m_addDameValue;

		// Token: 0x040043E0 RID: 17376
		private int m_addGuardValue;

		// Token: 0x040043E1 RID: 17377
		private int m_addLuckValue;

		// Token: 0x040043E2 RID: 17378
		private int m_bonusAgility;

		// Token: 0x040043E3 RID: 17379
		private int m_bonusAttack;

		// Token: 0x040043E4 RID: 17380
		private int m_bonusBaseDamage;

		// Token: 0x040043E5 RID: 17381
		private int m_bonusDefend;

		// Token: 0x040043E6 RID: 17382
		private int m_bonusGuard;

		// Token: 0x040043E7 RID: 17383
		private int m_bonusLucky;

		// Token: 0x040043E8 RID: 17384
		private int m_bonusPoint;

		// Token: 0x040043E9 RID: 17385
		private bool m_critActive;

		// Token: 0x040043EA RID: 17386
		private int m_critRate;

		// Token: 0x040043EB RID: 17387
		private int m_currentUseSkill;

		// Token: 0x040043EC RID: 17388
		private int m_BallType;

		// Token: 0x040043ED RID: 17389
		private bool m_isPetUseSkill;

		// Token: 0x040043EE RID: 17390
		private int m_maxBlood;

		// Token: 0x040043EF RID: 17391
		private int m_petBaseAtt;

		// Token: 0x040043F0 RID: 17392
		private int m_petDelay;

		// Token: 0x040043F1 RID: 17393
		private int m_reduceDefendValue;

		// Token: 0x040043F2 RID: 17394
		private bool m_stopMoving;

		// Token: 0x040043F3 RID: 17395
		public int AddBloodPercent;

		// Token: 0x040043F4 RID: 17396
		private int m_DamagePercent;

		// Token: 0x040043F5 RID: 17397
		public bool ActiveEffect;

		// Token: 0x040043F6 RID: 17398
		public int ReduceCritValue;

		// Token: 0x040043F7 RID: 17399
		public int IncreaseAngelicPoint;

		// Token: 0x040043F8 RID: 17400
		private bool m_DisibleActiveSkill;

		// Token: 0x040043F9 RID: 17401
		public int Delay;

		// Token: 0x040043FA RID: 17402
		public int ReboundDamage;

		// Token: 0x040043FB RID: 17403
		public int AddMaxBloodValue;

		// Token: 0x040043FC RID: 17404
		public int ReduceDamage;

		// Token: 0x040043FD RID: 17405
		public int ReduceCritical;

		// Token: 0x040043FE RID: 17406
		public int SkipCritical;
	}
}
