﻿using System;

namespace Game.Server.ActiveSystem
{
	// Token: 0x02000C49 RID: 3145
	public class ActiveSystemProcessorAtribute : Attribute
	{
		// Token: 0x17001320 RID: 4896
		// (get) Token: 0x06007010 RID: 28688 RVA: 0x0002A2A4 File Offset: 0x000284A4
		public byte Code
		{
			get
			{
				return this._code;
			}
		}

		// Token: 0x17001321 RID: 4897
		// (get) Token: 0x06007011 RID: 28689 RVA: 0x0002A2AC File Offset: 0x000284AC
		public string Description
		{
			get
			{
				return this._descript;
			}
		}

		// Token: 0x06007012 RID: 28690 RVA: 0x0002A2B4 File Offset: 0x000284B4
		public ActiveSystemProcessorAtribute(byte code, string description)
		{
			this._code = code;
			this._descript = description;
		}

		// Token: 0x04003C49 RID: 15433
		private byte _code;

		// Token: 0x04003C4A RID: 15434
		private string _descript;
	}
}
