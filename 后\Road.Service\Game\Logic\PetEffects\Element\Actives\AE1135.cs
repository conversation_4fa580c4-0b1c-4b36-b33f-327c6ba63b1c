﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DD6 RID: 3542
	public class AE1135 : BasePetEffect
	{
		// Token: 0x06007CD8 RID: 31960 RVA: 0x0029932C File Offset: 0x0029752C
		public AE1135(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1135, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CD9 RID: 31961 RVA: 0x002993A8 File Offset: 0x002975A8
		public override bool Start(Living living)
		{
			AE1135 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1135) as AE1135;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CDA RID: 31962 RVA: 0x0002FF4B File Offset: 0x0002E14B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
		}

		// Token: 0x06007CDB RID: 31963 RVA: 0x00299404 File Offset: 0x00297604
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				this.m_added = 500;
				damageAmount -= this.m_added;
				bool flag = damageAmount < 0;
				if (flag)
				{
					damageAmount = 1;
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007CDC RID: 31964 RVA: 0x0002FF74 File Offset: 0x0002E174
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007CDD RID: 31965 RVA: 0x0029944C File Offset: 0x0029764C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x04004B12 RID: 19218
		private int m_type = 0;

		// Token: 0x04004B13 RID: 19219
		private int m_count = 0;

		// Token: 0x04004B14 RID: 19220
		private int m_probability = 0;

		// Token: 0x04004B15 RID: 19221
		private int m_delay = 0;

		// Token: 0x04004B16 RID: 19222
		private int m_coldDown = 0;

		// Token: 0x04004B17 RID: 19223
		private int m_currentId;

		// Token: 0x04004B18 RID: 19224
		private int m_added = 0;
	}
}
