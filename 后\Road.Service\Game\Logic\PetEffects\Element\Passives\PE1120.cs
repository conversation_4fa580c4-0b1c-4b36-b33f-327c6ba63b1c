﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D66 RID: 3430
	public class PE1120 : BasePetEffect
	{
		// Token: 0x06007A95 RID: 31381 RVA: 0x0028F7D4 File Offset: 0x0028D9D4
		public PE1120(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1120, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A96 RID: 31382 RVA: 0x0028F850 File Offset: 0x0028DA50
		public override bool Start(Living living)
		{
			PE1120 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1120) as PE1120;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A97 RID: 31383 RVA: 0x0002E8C0 File Offset: 0x0002CAC0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007A98 RID: 31384 RVA: 0x0028F8AC File Offset: 0x0028DAAC
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = living.PetEffects.AddGuardValue == 0;
			if (flag)
			{
				this.m_added = 70;
				living.BaseGuard += (double)this.m_added;
				living.PetEffects.AddGuardValue += this.m_added;
				living.Game.SendPetBuff(living, base.Info, true);
			}
		}

		// Token: 0x06007A99 RID: 31385 RVA: 0x0002E8D6 File Offset: 0x0002CAD6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x0400480A RID: 18442
		private int m_type = 0;

		// Token: 0x0400480B RID: 18443
		private int m_count = 0;

		// Token: 0x0400480C RID: 18444
		private int m_probability = 0;

		// Token: 0x0400480D RID: 18445
		private int m_delay = 0;

		// Token: 0x0400480E RID: 18446
		private int m_coldDown = 0;

		// Token: 0x0400480F RID: 18447
		private int m_currentId;

		// Token: 0x04004810 RID: 18448
		private int m_added = 0;
	}
}
