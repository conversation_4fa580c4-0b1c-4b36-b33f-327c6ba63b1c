﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D5B RID: 3419
	public class PE1077 : BasePetEffect
	{
		// Token: 0x06007A5C RID: 31324 RVA: 0x0028E8A8 File Offset: 0x0028CAA8
		public PE1077(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1077, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A5D RID: 31325 RVA: 0x0028E928 File Offset: 0x0028CB28
		public override bool Start(Living living)
		{
			PE1077 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1077) as PE1077;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A5E RID: 31326 RVA: 0x0002E680 File Offset: 0x0002C880
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerShoot += this.Player_PlayerShoot;
			player.AfterPlayerShooted += this.Player_AfterPlayerShooted;
		}

		// Token: 0x06007A5F RID: 31327 RVA: 0x0002E640 File Offset: 0x0002C840
		private void Player_AfterPlayerShooted(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
		}

		// Token: 0x06007A60 RID: 31328 RVA: 0x0028E988 File Offset: 0x0028CB88
		private void Player_PlayerShoot(Player player)
		{
			this.m_added = 400;
			player.Game.SendPetBuff(player, base.ElementInfo, true);
			List<Living> list = player.Game.Map.FindAllNearestEnemy(player.X, player.Y, 100.0, player);
			foreach (Living living in list)
			{
				living.SyncAtTime = true;
				living.AddBlood(-this.m_added, 1);
				living.SyncAtTime = false;
				bool flag = living.Blood <= 0;
				if (flag)
				{
					living.Die();
					bool flag2 = player != null;
					if (flag2)
					{
						if (player != null)
						{
							player.PlayerDetail.OnKillingLiving(player.Game, 2, living.Id, living.IsLiving, this.m_added);
						}
					}
				}
			}
		}

		// Token: 0x06007A61 RID: 31329 RVA: 0x0002E6A9 File Offset: 0x0002C8A9
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerShoot -= this.Player_PlayerShoot;
			player.AfterPlayerShooted -= this.Player_AfterPlayerShooted;
		}

		// Token: 0x040047BD RID: 18365
		private int m_type = 0;

		// Token: 0x040047BE RID: 18366
		private int m_count = 0;

		// Token: 0x040047BF RID: 18367
		private int m_probability = 0;

		// Token: 0x040047C0 RID: 18368
		private int m_delay = 0;

		// Token: 0x040047C1 RID: 18369
		private int m_coldDown = 0;

		// Token: 0x040047C2 RID: 18370
		private int m_currentId;

		// Token: 0x040047C3 RID: 18371
		private int m_added = 0;
	}
}
