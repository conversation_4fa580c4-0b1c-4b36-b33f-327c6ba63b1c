﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DE6 RID: 3558
	public class AE1175 : BasePetEffect
	{
		// Token: 0x06007D2D RID: 32045 RVA: 0x0029AAEC File Offset: 0x00298CEC
		public AE1175(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1175, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D2E RID: 32046 RVA: 0x0029AB6C File Offset: 0x00298D6C
		public override bool Start(Living living)
		{
			AE1175 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1175) as AE1175;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D2F RID: 32047 RVA: 0x000302B6 File Offset: 0x0002E4B6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D30 RID: 32048 RVA: 0x000302CC File Offset: 0x0002E4CC
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D31 RID: 32049 RVA: 0x0029ABCC File Offset: 0x00298DCC
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1175(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004B82 RID: 19330
		private int m_type = 0;

		// Token: 0x04004B83 RID: 19331
		private int m_count = 0;

		// Token: 0x04004B84 RID: 19332
		private int m_probability = 0;

		// Token: 0x04004B85 RID: 19333
		private int m_delay = 0;

		// Token: 0x04004B86 RID: 19334
		private int m_coldDown = 0;

		// Token: 0x04004B87 RID: 19335
		private int m_currentId;

		// Token: 0x04004B88 RID: 19336
		private int m_added = 0;
	}
}
