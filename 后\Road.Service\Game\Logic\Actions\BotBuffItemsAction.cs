﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Actions
{
	// Token: 0x02000F43 RID: 3907
	public class BotBuffItemsAction : BaseAction
	{
		// Token: 0x060084D9 RID: 34009 RVA: 0x000350C5 File Offset: 0x000332C5
		public BotBuffItemsAction(Player player, ItemTemplateInfo buff, int delay)
			: base(delay, 1000)
		{
			this.m_player = player;
			this.m_buff = buff;
		}

		// Token: 0x060084DA RID: 34010 RVA: 0x002B8C7C File Offset: 0x002B6E7C
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			bool flag = this.m_buff != null;
			if (flag)
			{
				this.m_player.UseItem(this.m_buff);
			}
			base.Finish(tick);
		}

		// Token: 0x040052B3 RID: 21171
		private Player m_player;

		// Token: 0x040052B4 RID: 21172
		private ItemTemplateInfo m_buff;
	}
}
