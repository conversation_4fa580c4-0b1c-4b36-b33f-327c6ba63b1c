﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CEC RID: 3308
	public class binglongfuhuo : BasePetEffect
	{
		// Token: 0x060077F4 RID: 30708 RVA: 0x0028361C File Offset: 0x0028181C
		public binglongfuhuo(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.binglongfuhuo, elementID)
		{
			this.int_1 = 2;
			this.int_4 = 5;
			this.int_2 = ((probability == -1) ? 10000 : probability);
			this.int_0 = type;
			this.int_3 = delay;
			this.int_5 = skillId;
		}

		// Token: 0x060077F5 RID: 30709 RVA: 0x00283670 File Offset: 0x00281870
		private void method_0(Living living_0, Living living_1, int int_7, int int_8)
		{
			int num = living_0.MaxBlood / 100;
			bool flag = living_0.Blood <= num;
			if (flag)
			{
				bool flag2 = living_0.KeepLifeCount < this.int_1;
				if (flag2)
				{
					this.IsTrigger = true;
					living_0.EffectTrigger = true;
					living_0.SyncAtTime = true;
					living_0.Blood = Convert.ToInt32((double)living_0.MaxBlood * 0.5);
					living_0.Game.SendGameUpdateHealth(living_0, 1, living_0.Blood);
					living_0.SyncAtTime = false;
					living_0.Game.SendEquipEffect(living_0, "触发<极寒复苏>免疫本次致命伤害,恢复自身血量80%");
					int keepLifeCount = living_0.KeepLifeCount;
					living_0.KeepLifeCount = keepLifeCount + 1;
				}
				else
				{
					living_0.KeepLife = false;
					this.Stop();
				}
			}
		}

		// Token: 0x060077F6 RID: 30710 RVA: 0x0002C3B1 File Offset: 0x0002A5B1
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKilledByLiving += this.method_0;
			player.AfterKillingLiving += this.method_0;
		}

		// Token: 0x060077F7 RID: 30711 RVA: 0x0002C3DA File Offset: 0x0002A5DA
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.method_0;
			player.AfterKillingLiving -= this.method_0;
		}

		// Token: 0x060077F8 RID: 30712 RVA: 0x00283738 File Offset: 0x00281938
		public override bool Start(Living living)
		{
			binglongfuhuo binglongfuhuo = living.PetEffectList.GetOfType(ePetEffectType.binglongfuhuo) as binglongfuhuo;
			bool flag = binglongfuhuo == null;
			bool flag2;
			if (flag)
			{
				flag2 = base.Start(living);
			}
			else
			{
				binglongfuhuo.int_2 = ((this.int_2 > binglongfuhuo.int_2) ? this.int_2 : binglongfuhuo.int_2);
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x04004645 RID: 17989
		private int int_0;

		// Token: 0x04004646 RID: 17990
		private int int_1;

		// Token: 0x04004647 RID: 17991
		private int int_2;

		// Token: 0x04004648 RID: 17992
		private int int_3;

		// Token: 0x04004649 RID: 17993
		private int int_4;

		// Token: 0x0400464A RID: 17994
		private int int_5;

		// Token: 0x0400464B RID: 17995
		private int int_6;
	}
}
