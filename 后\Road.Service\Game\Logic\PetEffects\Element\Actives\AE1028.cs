﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D9B RID: 3483
	public class AE1028 : BasePetEffect
	{
		// Token: 0x06007BA5 RID: 31653 RVA: 0x00293E94 File Offset: 0x00292094
		public AE1028(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1028, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BA6 RID: 31654 RVA: 0x00293F14 File Offset: 0x00292114
		public override bool Start(Living living)
		{
			AE1028 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1028) as AE1028;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BA7 RID: 31655 RVA: 0x0002F3CF File Offset: 0x0002D5CF
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007BA8 RID: 31656 RVA: 0x00293F74 File Offset: 0x00292174
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1028(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007BA9 RID: 31657 RVA: 0x0002F3E5 File Offset: 0x0002D5E5
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004977 RID: 18807
		private int m_type = 0;

		// Token: 0x04004978 RID: 18808
		private int m_count = 0;

		// Token: 0x04004979 RID: 18809
		private int m_probability = 0;

		// Token: 0x0400497A RID: 18810
		private int m_delay = 0;

		// Token: 0x0400497B RID: 18811
		private int m_coldDown = 0;

		// Token: 0x0400497C RID: 18812
		private int m_currentId;

		// Token: 0x0400497D RID: 18813
		private int m_added = 0;
	}
}
