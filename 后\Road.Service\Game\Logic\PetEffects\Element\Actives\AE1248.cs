﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E19 RID: 3609
	public class AE1248 : BasePetEffect
	{
		// Token: 0x06007E32 RID: 32306 RVA: 0x0029F608 File Offset: 0x0029D808
		public AE1248(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1248, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E33 RID: 32307 RVA: 0x0029F688 File Offset: 0x0029D888
		public override bool Start(Living living)
		{
			AE1248 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1248) as AE1248;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E34 RID: 32308 RVA: 0x00030C55 File Offset: 0x0002EE55
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E35 RID: 32309 RVA: 0x00030C6B File Offset: 0x0002EE6B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E36 RID: 32310 RVA: 0x0029F6E8 File Offset: 0x0029D8E8
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1248(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004CE7 RID: 19687
		private int m_type = 0;

		// Token: 0x04004CE8 RID: 19688
		private int m_count = 0;

		// Token: 0x04004CE9 RID: 19689
		private int m_probability = 0;

		// Token: 0x04004CEA RID: 19690
		private int m_delay = 0;

		// Token: 0x04004CEB RID: 19691
		private int m_coldDown = 0;

		// Token: 0x04004CEC RID: 19692
		private int m_currentId;

		// Token: 0x04004CED RID: 19693
		private int m_added = 0;
	}
}
