﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E55 RID: 3669
	public class CE1042 : BasePetEffect
	{
		// Token: 0x06007F8C RID: 32652 RVA: 0x002A5890 File Offset: 0x002A3A90
		public CE1042(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1042, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F8D RID: 32653 RVA: 0x002A5910 File Offset: 0x002A3B10
		public override bool Start(Living living)
		{
			CE1042 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1042) as CE1042;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F8E RID: 32654 RVA: 0x002A5970 File Offset: 0x002A3B70
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 500;
				player.Lucky += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F8F RID: 32655 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F90 RID: 32656 RVA: 0x002A59D4 File Offset: 0x002A3BD4
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F91 RID: 32657 RVA: 0x002A5A08 File Offset: 0x002A3C08
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Lucky -= (double)this.m_added;
			this.m_added = 0;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E8E RID: 20110
		private int m_type = 0;

		// Token: 0x04004E8F RID: 20111
		private int m_count = 0;

		// Token: 0x04004E90 RID: 20112
		private int m_probability = 0;

		// Token: 0x04004E91 RID: 20113
		private int m_delay = 0;

		// Token: 0x04004E92 RID: 20114
		private int m_coldDown = 0;

		// Token: 0x04004E93 RID: 20115
		private int m_currentId;

		// Token: 0x04004E94 RID: 20116
		private int m_added = 0;
	}
}
