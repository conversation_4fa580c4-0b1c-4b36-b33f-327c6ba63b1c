﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E04 RID: 3588
	public class AE1211 : BasePetEffect
	{
		// Token: 0x06007DC7 RID: 32199 RVA: 0x0029D8D0 File Offset: 0x0029BAD0
		public AE1211(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1211, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DC8 RID: 32200 RVA: 0x0029D950 File Offset: 0x0029BB50
		public override bool Start(Living living)
		{
			AE1211 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1211) as AE1211;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DC9 RID: 32201 RVA: 0x00030863 File Offset: 0x0002EA63
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DCA RID: 32202 RVA: 0x00030879 File Offset: 0x0002EA79
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DCB RID: 32203 RVA: 0x0029D9B0 File Offset: 0x0029BBB0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1211(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004C54 RID: 19540
		private int m_type = 0;

		// Token: 0x04004C55 RID: 19541
		private int m_count = 0;

		// Token: 0x04004C56 RID: 19542
		private int m_probability = 0;

		// Token: 0x04004C57 RID: 19543
		private int m_delay = 0;

		// Token: 0x04004C58 RID: 19544
		private int m_coldDown = 0;

		// Token: 0x04004C59 RID: 19545
		private int m_currentId;

		// Token: 0x04004C5A RID: 19546
		private int m_added = 0;
	}
}
