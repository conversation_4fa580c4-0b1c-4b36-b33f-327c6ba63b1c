﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using Bussiness;
using Bussiness.Managers;
using Game.Logic.Actions;
using Game.Logic.CardEffect.Effects;
using Game.Logic.Effects;
using Game.Logic.Event;
using Game.Logic.GhostEffect;
using Game.Logic.PetEffects;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.PetEffects.Element;
using Game.Logic.PetEffects.Element.Actives;
using Game.Logic.PetEffects.Element.Passives;
using Game.Logic.PetSkills.ActiveSkills;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.PetSkills.PassiveSkills;
using Game.Logic.Phy.Maths;
using Game.Logic.Spells;
using Newtonsoft.Json;
using SqlDataProvider.Data;

namespace Game.Logic.Phy.Object
{
	// Token: 0x02000CDB RID: 3291
	public class Player : TurnedLiving
	{
		// Token: 0x140000C7 RID: 199
		// (add) Token: 0x06007676 RID: 30326 RVA: 0x00272FEC File Offset: 0x002711EC
		// (remove) Token: 0x06007677 RID: 30327 RVA: 0x00273024 File Offset: 0x00271224
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event Action<Player> StarFishEnergyUpdated;

		// Token: 0x17001453 RID: 5203
		// (get) Token: 0x06007678 RID: 30328 RVA: 0x0002BE0D File Offset: 0x0002A00D
		// (set) Token: 0x06007679 RID: 30329 RVA: 0x0002BE15 File Offset: 0x0002A015
		public bool IsInThisSpecialMission { get; set; }

		// Token: 0x17001454 RID: 5204
		// (get) Token: 0x0600767A RID: 30330 RVA: 0x0002BE1E File Offset: 0x0002A01E
		// (set) Token: 0x0600767B RID: 30331 RVA: 0x0002BE26 File Offset: 0x0002A026
		public bool IsInJiyuan456789Mission { get; set; }

		// Token: 0x17001455 RID: 5205
		// (get) Token: 0x0600767C RID: 30332 RVA: 0x0002BE2F File Offset: 0x0002A02F
		// (set) Token: 0x0600767D RID: 30333 RVA: 0x0002BE37 File Offset: 0x0002A037
		public int OriginalDeputyWeaponStrengthenLevel { get; set; }

		// Token: 0x17001456 RID: 5206
		// (get) Token: 0x0600767E RID: 30334 RVA: 0x0027305C File Offset: 0x0027125C
		// (set) Token: 0x0600767F RID: 30335 RVA: 0x0002BE40 File Offset: 0x0002A040
		public int PowerRatio
		{
			get
			{
				return this.m_ratioPower;
			}
			set
			{
				this.m_ratioPower = value;
			}
		}

		// Token: 0x17001457 RID: 5207
		// (get) Token: 0x06007680 RID: 30336 RVA: 0x0002BE4A File Offset: 0x0002A04A
		public Dictionary<int, PetSkillInfo> PetSkillCD
		{
			get
			{
				return this._petSkillCd;
			}
		}

		// Token: 0x17001458 RID: 5208
		// (get) Token: 0x06007681 RID: 30337 RVA: 0x0002BE52 File Offset: 0x0002A052
		public UserPetInfo UserPet
		{
			get
			{
				return this.m_pet;
			}
		}

		// Token: 0x17001459 RID: 5209
		// (get) Token: 0x06007682 RID: 30338 RVA: 0x0002BE5A File Offset: 0x0002A05A
		// (set) Token: 0x06007683 RID: 30339 RVA: 0x0002BE62 File Offset: 0x0002A062
		public bool jaUsouSkill { get; set; }

		// Token: 0x1700145A RID: 5210
		// (get) Token: 0x06007684 RID: 30340 RVA: 0x0002BE6B File Offset: 0x0002A06B
		// (set) Token: 0x06007685 RID: 30341 RVA: 0x0002BE73 File Offset: 0x0002A073
		public bool IsBlackFlame { get; set; }

		// Token: 0x1700145B RID: 5211
		// (get) Token: 0x06007686 RID: 30342 RVA: 0x0002BE7C File Offset: 0x0002A07C
		// (set) Token: 0x06007687 RID: 30343 RVA: 0x0002BE84 File Offset: 0x0002A084
		public int CureBombValue { get; set; }

		// Token: 0x1700145C RID: 5212
		// (get) Token: 0x06007688 RID: 30344 RVA: 0x0002BE8D File Offset: 0x0002A08D
		public IGamePlayer PlayerDetail
		{
			get
			{
				return this.m_player;
			}
		}

		// Token: 0x1700145D RID: 5213
		// (get) Token: 0x06007689 RID: 30345 RVA: 0x0002BE95 File Offset: 0x0002A095
		public ItemInfo Weapon
		{
			get
			{
				return this.m_weapon;
			}
		}

		// Token: 0x1700145E RID: 5214
		// (get) Token: 0x0600768A RID: 30346 RVA: 0x0002BE9D File Offset: 0x0002A09D
		public ItemInfo DeputyWeapon
		{
			get
			{
				return this.m_DeputyWeapon;
			}
		}

		// Token: 0x1700145F RID: 5215
		// (get) Token: 0x0600768B RID: 30347 RVA: 0x0002BEA5 File Offset: 0x0002A0A5
		public bool IsActive
		{
			get
			{
				return this.m_isActive;
			}
		}

		// Token: 0x17001460 RID: 5216
		// (get) Token: 0x0600768C RID: 30348 RVA: 0x00273074 File Offset: 0x00271274
		// (set) Token: 0x0600768D RID: 30349 RVA: 0x0002BEAD File Offset: 0x0002A0AD
		public List<int> Prop
		{
			get
			{
				return this.m_prop;
			}
			set
			{
				this.m_prop = value;
			}
		}

		// Token: 0x17001461 RID: 5217
		// (get) Token: 0x0600768E RID: 30350 RVA: 0x0027308C File Offset: 0x0027128C
		// (set) Token: 0x0600768F RID: 30351 RVA: 0x002730A4 File Offset: 0x002712A4
		public bool CanGetProp
		{
			get
			{
				return this.m_canGetProp;
			}
			set
			{
				bool flag = this.m_canGetProp != value;
				if (flag)
				{
					this.m_canGetProp = value;
				}
			}
		}

		// Token: 0x17001462 RID: 5218
		// (get) Token: 0x06007690 RID: 30352 RVA: 0x002730CC File Offset: 0x002712CC
		// (set) Token: 0x06007691 RID: 30353 RVA: 0x0002BEB7 File Offset: 0x0002A0B7
		public int KilledPunishmentOffer
		{
			get
			{
				return this.m_killedPunishmentOffer;
			}
			set
			{
				this.m_killedPunishmentOffer = value;
			}
		}

		// Token: 0x17001463 RID: 5219
		// (get) Token: 0x06007692 RID: 30354 RVA: 0x002730E4 File Offset: 0x002712E4
		// (set) Token: 0x06007693 RID: 30355 RVA: 0x002730FC File Offset: 0x002712FC
		public int LoadingProcess
		{
			get
			{
				return this.m_loadingProcess;
			}
			set
			{
				bool flag = this.m_loadingProcess != value;
				if (flag)
				{
					this.m_loadingProcess = value;
					bool flag2 = this.m_loadingProcess >= 100;
					if (flag2)
					{
						this.OnLoadingCompleted();
					}
				}
			}
		}

		// Token: 0x17001464 RID: 5220
		// (get) Token: 0x06007694 RID: 30356 RVA: 0x00273140 File Offset: 0x00271340
		public int LevelPlusBlood
		{
			get
			{
				int num = 0;
				for (int i = 10; i <= 60; i += 10)
				{
					bool flag = this.PlayerDetail.PlayerCharacter.Grade - i > 0;
					if (flag)
					{
						num += (this.PlayerDetail.PlayerCharacter.Grade - i) * (i + 20);
					}
				}
				return num;
			}
		}

		// Token: 0x17001465 RID: 5221
		// (get) Token: 0x06007695 RID: 30357 RVA: 0x002731A4 File Offset: 0x002713A4
		// (set) Token: 0x06007696 RID: 30358 RVA: 0x0002BEC1 File Offset: 0x0002A0C1
		public int Energy
		{
			get
			{
				return this.m_energy;
			}
			set
			{
				this.m_energy = value;
			}
		}

		// Token: 0x17001466 RID: 5222
		// (get) Token: 0x06007697 RID: 30359 RVA: 0x0002BECB File Offset: 0x0002A0CB
		public BallInfo CurrentBall
		{
			get
			{
				return this.m_currentBall;
			}
		}

		// Token: 0x17001467 RID: 5223
		// (get) Token: 0x06007698 RID: 30360 RVA: 0x0002BED3 File Offset: 0x0002A0D3
		public bool IsSpecialSkill
		{
			get
			{
				return this.m_currentBall.ID == this.m_spBallId;
			}
		}

		// Token: 0x17001468 RID: 5224
		// (get) Token: 0x06007699 RID: 30361 RVA: 0x002731BC File Offset: 0x002713BC
		// (set) Token: 0x0600769A RID: 30362 RVA: 0x0002BEE8 File Offset: 0x0002A0E8
		public int ChangeSpecialBall
		{
			get
			{
				return this.m_changeSpecialball;
			}
			set
			{
				this.m_changeSpecialball = value;
			}
		}

		// Token: 0x17001469 RID: 5225
		// (get) Token: 0x0600769B RID: 30363 RVA: 0x002731D4 File Offset: 0x002713D4
		// (set) Token: 0x0600769C RID: 30364 RVA: 0x002731EC File Offset: 0x002713EC
		public new int ShootCount
		{
			get
			{
				return this.m_shootCount;
			}
			set
			{
				bool flag = this.m_shootCount != value;
				if (flag)
				{
					this.m_shootCount = value;
					this.m_game.SendGameUpdateShootCount(this);
				}
			}
		}

		// Token: 0x1700146A RID: 5226
		// (get) Token: 0x0600769D RID: 30365 RVA: 0x00273220 File Offset: 0x00271420
		// (set) Token: 0x0600769E RID: 30366 RVA: 0x00273238 File Offset: 0x00271438
		public int BallCount
		{
			get
			{
				return this.m_ballCount;
			}
			set
			{
				bool flag = this.m_ballCount != value;
				if (flag)
				{
					this.m_ballCount = value;
				}
			}
		}

		// Token: 0x1700146B RID: 5227
		// (get) Token: 0x0600769F RID: 30367 RVA: 0x0002BEF2 File Offset: 0x0002A0F2
		public int flyCount
		{
			get
			{
				return this._flyCoolDown;
			}
		}

		// Token: 0x1700146C RID: 5228
		// (get) Token: 0x060076A0 RID: 30368 RVA: 0x0002BEFA File Offset: 0x0002A0FA
		public int deputyWeaponCount
		{
			get
			{
				return this.deputyWeaponResCount;
			}
		}

		// Token: 0x1700146D RID: 5229
		// (get) Token: 0x060076A1 RID: 30369 RVA: 0x0002BF02 File Offset: 0x0002A102
		// (set) Token: 0x060076A2 RID: 30370 RVA: 0x0002BF0A File Offset: 0x0002A10A
		public int ReloadPointDietYeu { get; set; }

		// Token: 0x1700146E RID: 5230
		// (get) Token: 0x060076A3 RID: 30371 RVA: 0x0002BF13 File Offset: 0x0002A113
		// (set) Token: 0x060076A4 RID: 30372 RVA: 0x0002BF1B File Offset: 0x0002A11B
		public bool ActiveKimCang { get; set; }

		// Token: 0x1700146F RID: 5231
		// (get) Token: 0x060076A5 RID: 30373 RVA: 0x0002BF24 File Offset: 0x0002A124
		// (set) Token: 0x060076A6 RID: 30374 RVA: 0x0002BF2C File Offset: 0x0002A12C
		public bool ActiveThanMayMan { get; set; }

		// Token: 0x17001470 RID: 5232
		// (get) Token: 0x060076A7 RID: 30375 RVA: 0x0002BF35 File Offset: 0x0002A135
		// (set) Token: 0x060076A8 RID: 30376 RVA: 0x0002BF3D File Offset: 0x0002A13D
		public bool ActiveKhiepSo { get; set; }

		// Token: 0x17001471 RID: 5233
		// (get) Token: 0x060076A9 RID: 30377 RVA: 0x0002BF46 File Offset: 0x0002A146
		// (set) Token: 0x060076AA RID: 30378 RVA: 0x0002BF4E File Offset: 0x0002A14E
		public int DamageIncrease { get; internal set; }

		// Token: 0x140000C8 RID: 200
		// (add) Token: 0x060076AB RID: 30379 RVA: 0x00273260 File Offset: 0x00271460
		// (remove) Token: 0x060076AC RID: 30380 RVA: 0x00273298 File Offset: 0x00271498
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle LoadingCompleted;

		// Token: 0x140000C9 RID: 201
		// (add) Token: 0x060076AD RID: 30381 RVA: 0x002732D0 File Offset: 0x002714D0
		// (remove) Token: 0x060076AE RID: 30382 RVA: 0x00273308 File Offset: 0x00271508
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerShoot;

		// Token: 0x140000CA RID: 202
		// (add) Token: 0x060076AF RID: 30383 RVA: 0x00273340 File Offset: 0x00271540
		// (remove) Token: 0x060076B0 RID: 30384 RVA: 0x00273378 File Offset: 0x00271578
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerSkip;

		// Token: 0x140000CB RID: 203
		// (add) Token: 0x060076B1 RID: 30385 RVA: 0x002733B0 File Offset: 0x002715B0
		// (remove) Token: 0x060076B2 RID: 30386 RVA: 0x002733E8 File Offset: 0x002715E8
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerAfterBuffSkillPet;

		// Token: 0x140000CC RID: 204
		// (add) Token: 0x060076B3 RID: 30387 RVA: 0x00273420 File Offset: 0x00271620
		// (remove) Token: 0x060076B4 RID: 30388 RVA: 0x00273458 File Offset: 0x00271658
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerBuffSkill;

		// Token: 0x140000CD RID: 205
		// (add) Token: 0x060076B5 RID: 30389 RVA: 0x00273490 File Offset: 0x00271690
		// (remove) Token: 0x060076B6 RID: 30390 RVA: 0x002734C8 File Offset: 0x002716C8
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerAnyShellThrow;

		// Token: 0x140000CE RID: 206
		// (add) Token: 0x060076B7 RID: 30391 RVA: 0x00273500 File Offset: 0x00271700
		// (remove) Token: 0x060076B8 RID: 30392 RVA: 0x00273538 File Offset: 0x00271738
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerShootCure;

		// Token: 0x140000CF RID: 207
		// (add) Token: 0x060076B9 RID: 30393 RVA: 0x00273570 File Offset: 0x00271770
		// (remove) Token: 0x060076BA RID: 30394 RVA: 0x002735A8 File Offset: 0x002717A8
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerBeginMoving;

		// Token: 0x140000D0 RID: 208
		// (add) Token: 0x060076BB RID: 30395 RVA: 0x002735E0 File Offset: 0x002717E0
		// (remove) Token: 0x060076BC RID: 30396 RVA: 0x00273618 File Offset: 0x00271818
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerShootEventHandle BeforePlayerShoot;

		// Token: 0x140000D1 RID: 209
		// (add) Token: 0x060076BD RID: 30397 RVA: 0x00273650 File Offset: 0x00271850
		// (remove) Token: 0x060076BE RID: 30398 RVA: 0x00273688 File Offset: 0x00271888
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle AfterPlayerShooted;

		// Token: 0x140000D2 RID: 210
		// (add) Token: 0x060076BF RID: 30399 RVA: 0x002736C0 File Offset: 0x002718C0
		// (remove) Token: 0x060076C0 RID: 30400 RVA: 0x002736F8 File Offset: 0x002718F8
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerCompleteShoot;

		// Token: 0x140000D3 RID: 211
		// (add) Token: 0x060076C1 RID: 30401 RVA: 0x00273730 File Offset: 0x00271930
		// (remove) Token: 0x060076C2 RID: 30402 RVA: 0x00273768 File Offset: 0x00271968
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerGuard;

		// Token: 0x140000D4 RID: 212
		// (add) Token: 0x060076C3 RID: 30403 RVA: 0x002737A0 File Offset: 0x002719A0
		// (remove) Token: 0x060076C4 RID: 30404 RVA: 0x002737D8 File Offset: 0x002719D8
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerCure;

		// Token: 0x140000D5 RID: 213
		// (add) Token: 0x060076C5 RID: 30405 RVA: 0x00273810 File Offset: 0x00271A10
		// (remove) Token: 0x060076C6 RID: 30406 RVA: 0x00273848 File Offset: 0x00271A48
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerBuffSkillPet;

		// Token: 0x140000D6 RID: 214
		// (add) Token: 0x060076C7 RID: 30407 RVA: 0x00273880 File Offset: 0x00271A80
		// (remove) Token: 0x060076C8 RID: 30408 RVA: 0x002738B8 File Offset: 0x00271AB8
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerClearBuffSkillPet;

		// Token: 0x140000D7 RID: 215
		// (add) Token: 0x060076C9 RID: 30409 RVA: 0x002738F0 File Offset: 0x00271AF0
		// (remove) Token: 0x060076CA RID: 30410 RVA: 0x00273928 File Offset: 0x00271B28
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle CollidByObject;

		// Token: 0x140000D8 RID: 216
		// (add) Token: 0x060076CB RID: 30411 RVA: 0x00273960 File Offset: 0x00271B60
		// (remove) Token: 0x060076CC RID: 30412 RVA: 0x00273998 File Offset: 0x00271B98
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerUseDander;

		// Token: 0x140000D9 RID: 217
		// (add) Token: 0x060076CD RID: 30413 RVA: 0x002739D0 File Offset: 0x00271BD0
		// (remove) Token: 0x060076CE RID: 30414 RVA: 0x00273A08 File Offset: 0x00271C08
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerBeforeReset;

		// Token: 0x140000DA RID: 218
		// (add) Token: 0x060076CF RID: 30415 RVA: 0x00273A40 File Offset: 0x00271C40
		// (remove) Token: 0x060076D0 RID: 30416 RVA: 0x00273A78 File Offset: 0x00271C78
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerAfterReset;

		// Token: 0x140000DB RID: 219
		// (add) Token: 0x060076D1 RID: 30417 RVA: 0x00273AB0 File Offset: 0x00271CB0
		// (remove) Token: 0x060076D2 RID: 30418 RVA: 0x00273AE8 File Offset: 0x00271CE8
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerSecondWeaponEventHandle PlayerUseSecondWeapon;

		// Token: 0x140000DC RID: 220
		// (add) Token: 0x060076D3 RID: 30419 RVA: 0x00273B20 File Offset: 0x00271D20
		// (remove) Token: 0x060076D4 RID: 30420 RVA: 0x00273B58 File Offset: 0x00271D58
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event Player.PlayerUsePetMPEventHandle PlayerUsePetMP;

		// Token: 0x140000DD RID: 221
		// (add) Token: 0x060076D5 RID: 30421 RVA: 0x00273B90 File Offset: 0x00271D90
		// (remove) Token: 0x060076D6 RID: 30422 RVA: 0x00273BC8 File Offset: 0x00271DC8
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerBuffSkillHorse;

		// Token: 0x060076D7 RID: 30423 RVA: 0x00273C00 File Offset: 0x00271E00
		public Player(IGamePlayer player, int id, BaseGame game, int team, int maxBlood)
			: base(id, game, team, "", "", 1000, 0, 1)
		{
			this.m_rect = new Rectangle(-15, -20, 30, 30);
			this.m_player = player;
			this.m_player.GamePlayerId = id;
			this.m_canGetProp = true;
			this.m_isActive = true;
			this.Grade = player.PlayerCharacter.Grade;
			this.TotalAllHurt = 0;
			this.TotalAllHitTargetCount = 0;
			this.TotalAllShootCount = 0;
			this.TotalAllKill = 0;
			this.TotalAllExperience = 0;
			this.TotalAllScore = 0;
			this.TotalAllCure = 0;
			this.m_prop = new List<int>();
			this.propsBloqueados = new List<int>();
			this.IsShowEffectA = true;
			this.IsShowEffectB = true;
			this.m_weapon = this.m_player.MainWeapon;
			this.m_DeputyWeapon = this.m_player.SecondWeapon;
			this.m_loadingProcess = 0;
			base.VaneOpen = player.PlayerCharacter.IsWeakGuildFinish(9);
			this.CanUsePetSkill = true;
			this.ChangeSpecialBall = 0;
			this.EquipGhostReduceCritDamage = 0;
			this.m_pet = player.UserPet;
			this._petSkillCd = new Dictionary<int, PetSkillInfo>();
			bool flag = game == null;
			if (!flag)
			{
				this.InitBuffer(this.m_player.EquipEffect);
				this.InitFightBuffer(this.m_player.FightBuffs);
				this.InitCardBuffer(this.m_player.CardEffect);
				this.InitEquipGhostSkills();
				bool flag2 = this.m_pet != null;
				if (flag2)
				{
					this.InitPetSkillEffect();
				}
				bool flag3 = this.m_DeputyWeapon != null;
				if (flag3)
				{
					this.deputyWeaponResCount = this.m_DeputyWeapon.StrengthenLevel + 1;
				}
				else
				{
					this.deputyWeaponResCount = 1;
				}
				bool flag4 = !game.IsSpecialPVE();
				if (flag4)
				{
					UserBufferInfo fightBuffByType = this.GetFightBuffByType(eBuffType.Agility);
					bool flag5 = fightBuffByType != null && this.m_player.UsePayBuff(eBuffType.Agility) && this.m_game.RoomType == eRoomType.Dungeon;
					if (flag5)
					{
						this.m_bufferPoint = fightBuffByType;
					}
				}
				bool flag6 = this.m_weapon != null;
				if (flag6)
				{
					BombConfigInfo bombConfigInfo = BallConfigMgr.FindBall(this.m_weapon.TemplateID);
					bool flag7 = this.m_weapon.GoldValidDate();
					if (flag7)
					{
						bombConfigInfo = BallConfigMgr.FindBall(this.m_weapon.GoldEquip.TemplateID);
					}
					bool flag8 = bombConfigInfo != null;
					if (flag8)
					{
						this.m_mainBallId = bombConfigInfo.Common;
						this.m_spBallId = bombConfigInfo.Special;
						this.m_spsBallId = bombConfigInfo.SpecialII;
						this.m_AddWoundBallId = bombConfigInfo.CommonAddWound;
						this.m_MultiBallId = bombConfigInfo.CommonMultiBall;
					}
				}
			}
		}

		// Token: 0x060076D8 RID: 30424 RVA: 0x00273EF0 File Offset: 0x002720F0
		public override void Reset()
		{
			this.OnPlayerBeforeReset();
			bool flag = this.m_game.RoomType == eRoomType.Dungeon || this.m_game.RoomType == eRoomType.Boss || this.m_game.RoomType == eRoomType.Academy;
			if (flag)
			{
				this.m_game.Cards = new int[121];
			}
			else
			{
				this.m_game.Cards = new int[9];
			}
			base.Dander = 0;
			this.ReloadPointDietYeu = 0;
			base.psychic = 0;
			base.IsLiving = true;
			this.FinishTakeCard = false;
			this.m_changeSpecialball = 0;
			this.m_weapon = this.m_player.MainWeapon;
			BombConfigInfo bombConfigInfo = BallConfigMgr.FindBall(this.m_weapon.TemplateID);
			bool flag2 = bombConfigInfo != null;
			if (flag2)
			{
				this.m_mainBallId = bombConfigInfo.Common;
				this.m_spBallId = bombConfigInfo.Special;
				this.m_spsBallId = bombConfigInfo.SpecialII;
				this.m_AddWoundBallId = bombConfigInfo.CommonAddWound;
				this.m_MultiBallId = bombConfigInfo.CommonMultiBall;
			}
			else
			{
				Console.WriteLine("systerm do not suport this weapon ID {0}, enter db and check dbo.BallConfig", this.m_weapon.TemplateID);
			}
			this.m_DeputyWeapon = this.m_player.SecondWeapon;
			bool flag3 = this.m_DeputyWeapon != null;
			if (flag3)
			{
				this.deputyWeaponResCount = this.m_DeputyWeapon.StrengthenLevel + 1;
			}
			else
			{
				this.deputyWeaponResCount = 1;
			}
			this.m_maxBlood = this.m_player.PlayerCharacter.Blood;
			bool flag4 = base.FightBuffers.ConsortionAddMaxBlood > 0;
			if (flag4)
			{
				this.m_maxBlood += this.m_maxBlood * base.FightBuffers.ConsortionAddMaxBlood / 100;
			}
			this.m_maxBlood += base.FightBuffers.WorldBossHP + base.FightBuffers.WorldBossHP_MoneyBuff;
			this.m_maxBlood += ((base.PetEffects != null) ? base.PetEffects.AddMaxBloodValue : 0);
			this.BaseDamage = this.m_player.GetBaseAttack();
			bool flag5 = base.FightBuffers.ConsortionAddDamage > 0;
			if (flag5)
			{
				this.BaseDamage += (double)base.FightBuffers.ConsortionAddDamage;
			}
			bool flag6 = base.FightBuffers.WorldBossAttrack_MoneyBuff > 0;
			if (flag6)
			{
				this.BaseDamage += (double)base.FightBuffers.WorldBossAttrack_MoneyBuff;
			}
			this.BaseGuard = this.m_player.GetBaseDefence();
			this.Attack = (double)this.m_player.PlayerCharacter.Attack;
			this.Defence = (double)this.m_player.PlayerCharacter.Defence;
			this.Agility = (double)this.m_player.PlayerCharacter.Agility;
			this.Lucky = (double)this.m_player.PlayerCharacter.Luck;
			bool flag7 = base.FightBuffers.ConsortionAddProperty > 0;
			if (flag7)
			{
				this.Attack += (double)base.FightBuffers.ConsortionAddProperty;
				this.Defence += (double)base.FightBuffers.ConsortionAddProperty;
				this.Agility += (double)base.FightBuffers.ConsortionAddProperty;
				this.Lucky += (double)base.FightBuffers.ConsortionAddProperty;
			}
			bool flag8 = this.m_bufferPoint != null;
			if (flag8)
			{
				this.Attack += this.Attack / 100.0 * (double)this.m_bufferPoint.Value;
				this.Defence += this.Defence / 100.0 * (double)this.m_bufferPoint.Value;
				this.Agility += this.Agility / 100.0 * (double)this.m_bufferPoint.Value;
				this.Lucky += this.Lucky / 100.0 * (double)this.m_bufferPoint.Value;
			}
			this.m_energy = (int)this.Agility / 30 + 240;
			bool flag9 = base.FightBuffers.ConsortionAddEnergy > 0;
			if (flag9)
			{
				this.m_energy += base.FightBuffers.ConsortionAddEnergy;
			}
			bool flag10 = this.m_game.GameType == eGameType.GuildLeage || this.m_game.GameType == eGameType.Leage;
			if (flag10)
			{
				this.BaseDamage = 1000.0;
				this.BaseGuard = 500.0;
				this.Attack = 2000.0;
				this.Defence = 2200.0;
				this.Agility = 2000.0;
				this.Lucky = 2000.0;
				this.m_maxBlood = 15000;
				this.m_player.SendMessage("当前比赛为联赛，您的属性已经平衡.");
			}
			bool flag11 = this.m_game.GameType == eGameType.Free;
			if (flag11)
			{
				this.BaseDamage = 1000.0;
				this.BaseGuard = 500.0;
				this.Attack = 2000.0;
				this.Defence = 2200.0;
				this.Agility = 2000.0;
				this.Lucky = 2000.0;
				this.m_maxBlood = 15000;
				this.m_player.SendMessage("平衡属性生效.");
			}
			this.m_currentBall = BallMgr.FindBall(this.m_mainBallId);
			this.m_shootCount = 1;
			this.m_ballCount = 1;
			this.CurrentIsHitTarget = false;
			this.LimitEnergy = false;
			this.TotalCure = 0;
			this.TotalHitTargetCount = 0;
			this.TotalHurt = 0;
			this.TotalKill = 0;
			this.TotalShootCount = 0;
			this.TotalDamageForMatch = 0;
			this.LockDirection = false;
			this.isLockXY = false;
			this.GainGP = 0;
			this.GainOffer = 0;
			this.Ready = false;
			this.IsAddTurn = false;
			base.OldAttack = this.Attack;
			base.OldDefence = this.Defence;
			base.OldAgility = this.Agility;
			base.OldLucky = this.Lucky;
			base.OldBaseDamage = this.BaseDamage;
			base.OldBaseGuard = this.BaseGuard;
			this.LoadingProcess = 0;
			this.InitBuffer(this.m_player.EquipEffect);
			this.InitFightBuffer(this.m_player.FightBuffs);
			this.isLockEmery = false;
			this.InitCardBuffer(this.m_player.CardEffect);
			bool flag12 = !this.isLockEmery;
			if (flag12)
			{
				this.m_ratioPower = 100 - base.FightBuffers.ConsortionReduceEnergyUse;
			}
			else
			{
				this.m_ratioPower = 0;
			}
			this.SoulPropCount = 0;
			this.m_prop.Clear();
			this.PlayerDetail.ClearTempBag();
			this.m_delay = this.GetTurnDelay();
			base.PetMP = 10;
			base.PetFlag = 0;
			this.ResetSkillCd();
			this.OnPlayerAfterReset();
			base.Reset();
		}

		// Token: 0x060076D9 RID: 30425 RVA: 0x00274600 File Offset: 0x00272800
		private int GetTurnDelay()
		{
			return 1600 - 1200 * this.PlayerDetail.PlayerCharacter.Agility / (this.PlayerDetail.PlayerCharacter.Agility + 1200) + this.PlayerDetail.PlayerCharacter.Attack / 2000;
		}

		// Token: 0x060076DA RID: 30426 RVA: 0x0027465C File Offset: 0x0027285C
		public void CalculatePlayerOffer(Player player)
		{
			bool flag = this.m_game.RoomType == eRoomType.Match && (this.m_game.GameType == eGameType.Guild || this.m_game.GameType == eGameType.Free || this.m_game.GameType == eGameType.Leage || this.m_game.GameType == eGameType.GuildLeage) && !player.IsLiving;
			if (flag)
			{
				int num = ((base.Game.GameType == eGameType.Guild || this.m_game.GameType == eGameType.GuildLeage) ? 10 : ((this.PlayerDetail.PlayerCharacter.ConsortiaID == 0 || player.PlayerDetail.PlayerCharacter.ConsortiaID == 0) ? 1 : 3));
				bool flag2 = num > player.PlayerDetail.PlayerCharacter.Offer;
				if (flag2)
				{
					num = player.PlayerDetail.PlayerCharacter.Offer;
				}
				bool flag3 = num > 0;
				if (flag3)
				{
					this.GainOffer += num;
					player.KilledPunishmentOffer = num;
				}
			}
		}

		// Token: 0x060076DB RID: 30427 RVA: 0x0027475C File Offset: 0x0027295C
		public void InitBuffer(List<int> equpedEffect)
		{
			base.EffectList.StopAllEffect();
			for (int i = 0; i < equpedEffect.Count; i++)
			{
				ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(equpedEffect[i]);
				switch (itemTemplateInfo.Property3)
				{
				case 1:
					new AddAttackEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 2:
					new AddDefenceEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 3:
					new AddAgilityEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 4:
					new AddLuckyEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 5:
					new AddDamageEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 6:
					new ReduceDamageEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 7:
					new AddBloodEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 8:
					new FatalEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 9:
					new IceFronzeEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 10:
					new NoHoleEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 11:
					new AtomBombEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 12:
					new ArmorPiercerEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 13:
					new AvoidDamageEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 14:
					new MakeCriticalEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 15:
					new AssimilateDamageEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 16:
					new AssimilateBloodEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 17:
					new SealEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 18:
					new AddTurnEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5, itemTemplateInfo.TemplateID).Start(this);
					break;
				case 19:
					new AddDanderEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 20:
					new ReflexDamageEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 21:
					new ReduceStrengthEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 22:
					new ContinueReduceBloodEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 23:
					new LockDirectionEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 24:
					new AddBombEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 25:
					new ContinueReduceBaseDamageEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 26:
					new RecoverBloodEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				}
			}
		}

		// Token: 0x060076DC RID: 30428 RVA: 0x00274B0C File Offset: 0x00272D0C
		public void InitFightBuffer(List<UserBufferInfo> buffers)
		{
			foreach (UserBufferInfo userBufferInfo in buffers)
			{
				switch (userBufferInfo.Type)
				{
				case 101:
					base.FightBuffers.ConsortionAddBloodGunCount = userBufferInfo.Value;
					break;
				case 102:
					base.FightBuffers.ConsortionAddDamage = userBufferInfo.Value;
					break;
				case 103:
					base.FightBuffers.ConsortionAddCritical = userBufferInfo.Value;
					break;
				case 104:
					base.FightBuffers.ConsortionAddMaxBlood = userBufferInfo.Value;
					break;
				case 105:
					base.FightBuffers.ConsortionAddProperty = userBufferInfo.Value;
					break;
				case 106:
					base.FightBuffers.ConsortionReduceEnergyUse = userBufferInfo.Value;
					break;
				case 107:
					base.FightBuffers.ConsortionAddEnergy = userBufferInfo.Value;
					break;
				case 108:
					base.FightBuffers.ConsortionAddEffectTurn = userBufferInfo.Value;
					break;
				case 109:
					base.FightBuffers.ConsortionAddOfferRate = userBufferInfo.Value;
					break;
				case 110:
					base.FightBuffers.ConsortionAddPercentGoldOrGP = userBufferInfo.Value;
					break;
				case 111:
					base.FightBuffers.ConsortionAddSpellCount = userBufferInfo.Value;
					break;
				case 112:
					base.FightBuffers.ConsortionReduceDander = userBufferInfo.Value;
					break;
				default:
					Console.WriteLine(string.Format("Not Found FightBuff Type {0} Value {1}", userBufferInfo.Type, userBufferInfo.Value));
					break;
				}
			}
		}

		// Token: 0x060076DD RID: 30429 RVA: 0x00274CE0 File Offset: 0x00272EE0
		public void InitCardBuffer(List<int> cards)
		{
			base.CardEffectList.StopAllEffect();
			int num = 30;
			foreach (int num2 in cards)
			{
				bool flag = num2 < 1100;
				if (flag)
				{
					num = num2 - 1000;
				}
			}
			int num3 = 0;
			bool flag2 = num >= 10;
			if (flag2)
			{
				num3 = 1;
			}
			bool flag3 = num >= 20;
			if (flag3)
			{
				num3 = 2;
			}
			bool flag4 = num >= 30;
			if (flag4)
			{
				num3 = 3;
			}
			Dictionary<int, List<CardGroupInfo>> allCard = CardBuffMgr.GetAllCard();
			List<CardBuffInfo> list = new List<CardBuffInfo>();
			int num4 = 0;
			List<CardBuffInfo> list2 = new List<CardBuffInfo>();
			foreach (int num5 in allCard.Keys)
			{
				num4 = 0;
				foreach (CardGroupInfo cardGroupInfo in allCard[num5])
				{
					foreach (int num6 in cards)
					{
						bool flag5 = num6 == cardGroupInfo.TemplateID;
						if (flag5)
						{
							num4++;
						}
					}
				}
				list = CardBuffMgr.FindCardBuffs(num5);
				bool flag6 = list == null;
				if (!flag6)
				{
					list.Reverse();
					foreach (CardBuffInfo cardBuffInfo in list)
					{
						bool flag7 = num4 >= cardBuffInfo.Condition;
						if (flag7)
						{
							CardInfo cardInfo = CardBuffMgr.FindCard(num5);
							bool flag8 = cardInfo != null && cardBuffInfo != null;
							if (flag8)
							{
								list2.Add(cardBuffInfo);
							}
						}
					}
				}
			}
			foreach (CardBuffInfo cardBuffInfo2 in list2)
			{
				Console.WriteLine(string.Format("Card ID = {0}", cardBuffInfo2.CardID));
				switch (cardBuffInfo2.CardID)
				{
				case 1:
					new AntCaveEffect(num3, cardBuffInfo2).Start(this);
					break;
				case 2:
				{
					bool flag9 = cardBuffInfo2.Condition >= 2;
					if (flag9)
					{
						new GuluKingdom2Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag10 = cardBuffInfo2.Condition >= 4;
					if (flag10)
					{
						new GuluKingdom4Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 3:
				{
					bool flag11 = cardBuffInfo2.Condition >= 3;
					if (flag11)
					{
						new EvilTribe3Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag12 = cardBuffInfo2.Condition >= 5;
					if (flag12)
					{
						new EvilTribe5Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 4:
				{
					bool flag13 = cardBuffInfo2.Condition >= 2;
					if (flag13)
					{
						new ShadowDevil2Effect(num3, cardBuffInfo2).Start(this);
						bool flag14 = this.m_game.IsPVE();
						if (flag14)
						{
							this.isLockEmery = true;
						}
					}
					bool flag15 = cardBuffInfo2.Condition >= 4;
					if (flag15)
					{
						new ShadowDevil4Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 5:
				{
					bool flag16 = cardBuffInfo2.Condition >= 2;
					if (flag16)
					{
						new FourArtifacts2Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag17 = cardBuffInfo2.Condition >= 4;
					if (flag17)
					{
						new FourArtifacts4Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 6:
				{
					bool flag18 = cardBuffInfo2.Condition >= 2;
					if (flag18)
					{
						new Goblin2Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag19 = cardBuffInfo2.Condition >= 4;
					if (flag19)
					{
						new Goblin4Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag20 = cardBuffInfo2.Condition >= 5;
					if (flag20)
					{
						new Goblin5Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 7:
				{
					bool flag21 = cardBuffInfo2.Condition >= 2;
					if (flag21)
					{
						new RunRunChicken2Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag22 = cardBuffInfo2.Condition >= 4;
					if (flag22)
					{
						new RunRunChicken4Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 8:
				{
					bool flag23 = cardBuffInfo2.Condition >= 2;
					if (flag23)
					{
						new GuluSportsMeeting2Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag24 = cardBuffInfo2.Condition >= 4;
					if (flag24)
					{
						new GuluSportsMeeting4Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag25 = cardBuffInfo2.Condition >= 5;
					if (flag25)
					{
						new GuluSportsMeeting5Effect(num3, cardBuffInfo2).Start(this);
						bool flag26 = base.Game is PVEGame && (base.Game as PVEGame).Info.ID == 8;
						if (flag26)
						{
							this.isLockEmery = true;
						}
					}
					break;
				}
				case 9:
				{
					bool flag27 = cardBuffInfo2.Condition >= 2;
					if (flag27)
					{
						new FiveGodSoldier2Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag28 = cardBuffInfo2.Condition >= 5;
					if (flag28)
					{
						new FiveGodSoldier5Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 10:
				{
					bool flag29 = cardBuffInfo2.Condition >= 3;
					if (flag29)
					{
						new TimeVortex3Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag30 = cardBuffInfo2.Condition >= 5;
					if (flag30)
					{
						new TimeVortex5Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 11:
				{
					bool flag31 = cardBuffInfo2.Condition >= 3;
					if (flag31)
					{
						new WarriorsArena3Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag32 = cardBuffInfo2.Condition >= 5;
					if (flag32)
					{
						new WarriorsArena5Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 12:
					new PioneerEffect(num3, cardBuffInfo2).Start(this);
					break;
				case 13:
					new WeaponMasterEffect(num3, cardBuffInfo2).Start(this);
					break;
				case 14:
					new DivineEffect(num3, cardBuffInfo2).Start(this);
					break;
				case 15:
					new LuckyEffect(num3, cardBuffInfo2).Start(this);
					break;
				default:
					Console.WriteLine("CardBuffer --- CardID Not Found");
					break;
				}
			}
		}

		// Token: 0x060076DE RID: 30430 RVA: 0x00275418 File Offset: 0x00273618
		private void InitEquipGhostSkills()
		{
			Dictionary<string, UserEquipGhostInfo> dictionary = JsonConvert.DeserializeObject<Dictionary<string, UserEquipGhostInfo>>(this.PlayerDetail.PlayerCharacter.GhostEquipList) ?? new Dictionary<string, UserEquipGhostInfo>();
			bool flag = dictionary == null;
			if (!flag)
			{
				foreach (UserEquipGhostInfo userEquipGhostInfo in dictionary.Values)
				{
					int spiritSkill = SpiritInfoMgr.GetSpiritSkill(userEquipGhostInfo.BagType, userEquipGhostInfo.Place, userEquipGhostInfo.Level);
					PetSkillInfo petSkillInfo = PetMgr.FindPetSkill(spiritSkill);
					bool flag2 = petSkillInfo == null;
					if (!flag2)
					{
						string[] array = petSkillInfo.ElementIDs.Split(new char[] { ',' });
						string[] array2 = array;
						string[] array3 = array2;
						string[] array4 = array3;
						int i = 0;
						while (i < array4.Length)
						{
							string text = array4[i];
							string text2 = text;
							string text3 = text2;
							uint num = <PrivateImplementationDetails>.ComputeStringHash(text3);
							if (num <= 1435975058U)
							{
								if (num <= 759923188U)
								{
									if (num <= 692812712U)
									{
										if (num != 659257474U)
										{
											if (num != 676035093U)
											{
												if (num == 692812712U)
												{
													if (text3 == "2207")
													{
														this.EquipGhostReduceCritDamage = 35;
													}
												}
											}
											else if (text3 == "2208")
											{
												this.EquipGhostReduceCritDamage = 40;
											}
										}
										else if (text3 == "2209")
										{
											this.EquipGhostReduceCritDamage = 45;
										}
									}
									else if (num <= 726367950U)
									{
										if (num != 709590331U)
										{
											if (num == 726367950U)
											{
												if (text3 == "2205")
												{
													this.EquipGhostReduceCritDamage = 25;
												}
											}
										}
										else if (text3 == "2206")
										{
											this.EquipGhostReduceCritDamage = 30;
										}
									}
									else if (num != 743145569U)
									{
										if (num == 759923188U)
										{
											if (text3 == "2203")
											{
												this.EquipGhostReduceCritDamage = 15;
											}
										}
									}
									else if (text3 == "2204")
									{
										this.EquipGhostReduceCritDamage = 20;
									}
								}
								else if (num <= 1352086963U)
								{
									if (num <= 793478426U)
									{
										if (num != 776700807U)
										{
											if (num == 793478426U)
											{
												if (text3 == "2201")
												{
													this.EquipGhostReduceCritDamage = 5;
												}
											}
										}
										else if (text3 == "2202")
										{
											this.EquipGhostReduceCritDamage = 10;
										}
									}
									else if (num != 1335309344U)
									{
										if (num == 1352086963U)
										{
											if (text3 == "2105")
											{
												goto IL_059A;
											}
										}
									}
									else if (text3 == "2104")
									{
										goto IL_059A;
									}
								}
								else if (num <= 1385642201U)
								{
									if (num != 1368864582U)
									{
										if (num == 1385642201U)
										{
											if (text3 == "2107")
											{
												goto IL_059A;
											}
										}
									}
									else if (text3 == "2106")
									{
										goto IL_059A;
									}
								}
								else if (num != 1419197439U)
								{
									if (num == 1435975058U)
									{
										if (text3 == "2102")
										{
											goto IL_059A;
										}
									}
								}
								else if (text3 == "2101")
								{
									goto IL_059A;
								}
							}
							else
							{
								if (num <= 2425431714U)
								{
									if (num <= 1536640772U)
									{
										if (num != 1452752677U)
										{
											if (num != 1503232629U)
											{
												if (num != 1536640772U)
												{
													goto IL_061D;
												}
												if (!(text3 == "2108"))
												{
													goto IL_061D;
												}
												goto IL_059A;
											}
											else
											{
												if (!(text3 == "2110"))
												{
													goto IL_061D;
												}
												goto IL_059A;
											}
										}
										else
										{
											if (!(text3 == "2103"))
											{
												goto IL_061D;
											}
											goto IL_059A;
										}
									}
									else if (num <= 2375245952U)
									{
										if (num != 1553418391U)
										{
											if (num != 2375245952U)
											{
												goto IL_061D;
											}
											if (!(text3 == "2409"))
											{
												goto IL_061D;
											}
										}
										else
										{
											if (!(text3 == "2109"))
											{
												goto IL_061D;
											}
											goto IL_059A;
										}
									}
									else if (num != 2392023571U)
									{
										if (num != 2425431714U)
										{
											goto IL_061D;
										}
										if (!(text3 == "2410"))
										{
											goto IL_061D;
										}
									}
									else if (!(text3 == "2408"))
									{
										goto IL_061D;
									}
								}
								else if (num <= 2576577380U)
								{
									if (num <= 2543022142U)
									{
										if (num != 2509466904U)
										{
											if (num != 2543022142U)
											{
												goto IL_061D;
											}
											if (!(text3 == "2403"))
											{
												goto IL_061D;
											}
										}
										else if (!(text3 == "2401"))
										{
											goto IL_061D;
										}
									}
									else if (num != 2559799761U)
									{
										if (num != 2576577380U)
										{
											goto IL_061D;
										}
										if (!(text3 == "2405"))
										{
											goto IL_061D;
										}
									}
									else if (!(text3 == "2402"))
									{
										goto IL_061D;
									}
								}
								else if (num <= 2610132618U)
								{
									if (num != 2593354999U)
									{
										if (num != 2610132618U)
										{
											goto IL_061D;
										}
										if (!(text3 == "2407"))
										{
											goto IL_061D;
										}
									}
									else if (!(text3 == "2404"))
									{
										goto IL_061D;
									}
								}
								else if (num != 2626910237U)
								{
									if (num != 2856875300U)
									{
										goto IL_061D;
									}
									if (!(text3 == "2210"))
									{
										goto IL_061D;
									}
									this.EquipGhostReduceCritDamage = 50;
									goto IL_061D;
								}
								else if (!(text3 == "2406"))
								{
									goto IL_061D;
								}
								new AddDefence(text).Start(this);
							}
							IL_061D:
							i++;
							continue;
							IL_059A:
							new AddDanderDamage(text).Start(this);
							goto IL_061D;
						}
					}
				}
			}
		}

		// Token: 0x060076DF RID: 30431 RVA: 0x00275A90 File Offset: 0x00273C90
		public void InitPetSkillEffect()
		{
			string[] array = this.m_pet.SkillEquip.Split(new char[] { '|' });
			string[] array2 = array;
			string[] array3 = array2;
			foreach (string text in array3)
			{
				int num = int.Parse(text.Split(new char[] { ',' })[0]);
				PetSkillInfo petSkillInfo = PetMgr.FindPetSkill(num);
				bool flag = petSkillInfo == null;
				if (!flag)
				{
					string[] array5 = petSkillInfo.ElementIDs.Split(new char[] { ',' });
					int coldDown = petSkillInfo.ColdDown;
					int probability = petSkillInfo.Probability;
					int delay = petSkillInfo.Delay;
					int gameType = petSkillInfo.GameType;
					bool flag2 = !this._petSkillCd.ContainsKey(num);
					if (flag2)
					{
						this._petSkillCd.Add(num, petSkillInfo);
					}
					Console.WriteLine("加载宠物技能, 技能ID: " + petSkillInfo.ElementIDs);
					string[] array6 = array5;
					string[] array7 = array6;
					foreach (string text2 in array7)
					{
						bool flag3 = !string.IsNullOrEmpty(text2);
						if (flag3)
						{
							string text3 = text2;
							string text4 = text3;
							uint num2 = <PrivateImplementationDetails>.ComputeStringHash(text4);
							if (num2 <= 2375872611U)
							{
								if (num2 <= 1306905010U)
								{
									if (num2 <= 530187426U)
									{
										if (num2 <= 287806074U)
										{
											if (num2 <= 120029884U)
											{
												if (num2 <= 35847599U)
												{
													if (num2 <= 2439456U)
													{
														if (num2 != 2292361U)
														{
															if (num2 != 2439456U)
															{
																goto IL_5826;
															}
															if (!(text4 == "1149"))
															{
																goto IL_5826;
															}
															new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 40).Start(this);
															goto IL_584C;
														}
														else
														{
															if (!(text4 == "1155"))
															{
																goto IL_5826;
															}
															new AE1155(coldDown, probability, gameType, num, delay, text2).Start(this);
															goto IL_584C;
														}
													}
													else if (num2 != 13002317U)
													{
														if (num2 != 19069980U)
														{
															if (num2 != 35847599U)
															{
																goto IL_5826;
															}
															if (!(text4 == "1153"))
															{
																goto IL_5826;
															}
															new AE1153(coldDown, probability, gameType, num, delay, text2).Start(this);
															goto IL_584C;
														}
														else
														{
															if (!(text4 == "1152"))
															{
																goto IL_5826;
															}
															new AE1152(coldDown, probability, gameType, num, delay, text2).Start(this);
															goto IL_584C;
														}
													}
													else
													{
														if (!(text4 == "1201"))
														{
															goto IL_5826;
														}
														new AE1201(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else if (num2 <= 52625218U)
												{
													if (num2 != 41156218U)
													{
														if (num2 != 52625218U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1150"))
														{
															goto IL_5826;
														}
														new AE1150(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "4261"))
														{
															goto IL_5826;
														}
														new AddAllDamage(num, text2).Start(this);
														goto IL_584C;
													}
												}
												else if (num2 != 57933837U)
												{
													if (num2 != 69402837U)
													{
														if (num2 != 120029884U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1138"))
														{
															goto IL_5826;
														}
														new AE1138(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1151"))
														{
															goto IL_5826;
														}
														new AE1151(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else
												{
													if (!(text4 == "4260"))
													{
														goto IL_5826;
													}
													new PetReduceTakeDamage_Passive(probability, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 <= 237620312U)
											{
												if (num2 <= 187140360U)
												{
													if (num2 != 136807503U)
													{
														if (num2 != 187140360U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1134"))
														{
															goto IL_5826;
														}
														new AE1134(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else if (!(text4 == "1139"))
													{
														goto IL_5826;
													}
												}
												else if (num2 != 190717139U)
												{
													if (num2 != 203917979U)
													{
														if (num2 != 237620312U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1127"))
														{
															goto IL_5826;
														}
														new AE1127(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1135"))
														{
															goto IL_5826;
														}
														new AE1135(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else
												{
													if (!(text4 == "4554"))
													{
														goto IL_5826;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 100).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 <= 262878355U)
											{
												if (num2 != 246100736U)
												{
													if (num2 != 254397931U)
													{
														if (num2 != 262878355U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1085"))
														{
															goto IL_5826;
														}
														new AE1085(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1126"))
														{
															goto IL_5826;
														}
														new AE1126(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else
												{
													if (!(text4 == "1084"))
													{
														goto IL_5826;
													}
													new AE1084(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 271175550U)
											{
												if (num2 != 279655974U)
												{
													if (num2 != 287806074U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1132"))
													{
														goto IL_5826;
													}
													new AE1132(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1086"))
													{
														goto IL_5826;
													}
													new AE1086(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1125"))
												{
													goto IL_5826;
												}
												new AE1125(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 345339427U)
										{
											if (num2 <= 313211212U)
											{
												if (num2 <= 296433593U)
												{
													if (num2 != 287953169U)
													{
														if (num2 != 296433593U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1087"))
														{
															goto IL_5826;
														}
														new AE1087(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1124"))
														{
															goto IL_5826;
														}
														new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 30).Start(this);
														goto IL_584C;
													}
												}
												else if (num2 != 304583693U)
												{
													if (num2 != 304730788U)
													{
														if (num2 != 313211212U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1080"))
														{
															goto IL_5826;
														}
														new PE1080(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else if (!(text4 == "1123"))
													{
														goto IL_5826;
													}
												}
												else
												{
													if (!(text4 == "1133"))
													{
														goto IL_5826;
													}
													new AE1133(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 <= 328561808U)
											{
												if (num2 != 321508407U)
												{
													if (num2 != 328561808U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1079"))
													{
														goto IL_5826;
													}
													new PE1079(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1122"))
													{
														goto IL_5826;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 10).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 329988831U)
											{
												if (num2 != 338286026U)
												{
													if (num2 != 345339427U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1078"))
													{
														goto IL_5826;
													}
													new PE1078(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1121"))
													{
														goto IL_5826;
													}
													new PE1121(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1081"))
												{
													goto IL_5826;
												}
												new PE1081(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 447432164U)
										{
											if (num2 <= 355063645U)
											{
												if (num2 != 346766450U)
												{
													if (num2 != 355063645U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1120"))
													{
														goto IL_5826;
													}
													new PE1120(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1082"))
													{
														goto IL_5826;
													}
													new AE1082(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 363544069U)
											{
												if (num2 != 375270948U)
												{
													if (num2 != 447432164U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1088"))
													{
														goto IL_5826;
													}
													new AE1088(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "4559"))
													{
														goto IL_5826;
													}
													new AddBlood(3, num, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1083"))
												{
													goto IL_5826;
												}
												new AE1083(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 496337998U)
										{
											if (num2 != 464209783U)
											{
												if (num2 != 479560379U)
												{
													if (num2 != 496337998U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1073"))
													{
														goto IL_5826;
													}
													new PE1073(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1070"))
													{
														goto IL_5826;
													}
													goto IL_584A;
												}
											}
											else
											{
												if (!(text4 == "1089"))
												{
													goto IL_5826;
												}
												new AE1090(coldDown, probability, gameType, num, delay, "1090").Start(this);
												new AE1091(coldDown, probability, gameType, num, delay, "1091").Start(this);
												goto IL_584C;
											}
										}
										else if (num2 != 513115617U)
										{
											if (num2 != 529893236U)
											{
												if (num2 != 530187426U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1019"))
												{
													goto IL_5826;
												}
												new AE1019(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
											else
											{
												if (!(text4 == "1075"))
												{
													goto IL_5826;
												}
												new PE1075(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else
										{
											if (!(text4 == "1072"))
											{
												goto IL_5826;
											}
											new PE1072(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_584C;
										}
										new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 20).Start(this);
									}
									else if (num2 <= 996397199U)
									{
										if (num2 <= 852299045U)
										{
											if (num2 <= 563742664U)
											{
												if (num2 <= 546965045U)
												{
													if (num2 != 546670855U)
													{
														if (num2 != 546965045U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1018"))
														{
															goto IL_5826;
														}
														new AE1018(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1074"))
														{
															goto IL_5826;
														}
														new PE1074(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else if (num2 != 550448998U)
												{
													if (num2 != 563448474U)
													{
														if (num2 != 563742664U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1017"))
														{
															goto IL_5826;
														}
														new AE1017(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1077"))
														{
															goto IL_5826;
														}
														new PE1077(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else
												{
													if (!(text4 == "1770"))
													{
														goto IL_5826;
													}
													new PetChangeSuit(3, num, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 <= 751486236U)
											{
												if (num2 != 580226093U)
												{
													if (num2 != 751486236U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1758"))
													{
														goto IL_5826;
													}
													new PetProhibitionAddBloodWithFire(3, num, text).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1076"))
													{
														goto IL_5826;
													}
													new PE1076(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 818596712U)
											{
												if (num2 != 835521426U)
												{
													if (num2 != 852299045U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1748"))
													{
														goto IL_5826;
													}
													new PetAddMpAfterCount_Passive(text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1749"))
													{
														goto IL_5826;
													}
													new PetBlockUseTrident(2, probability, num, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (!(text4 == "1754"))
											{
												goto IL_5826;
											}
										}
										else if (num2 <= 919262426U)
										{
											if (num2 <= 902484807U)
											{
												if (num2 != 885707188U)
												{
													if (num2 != 902484807U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1751"))
													{
														goto IL_5826;
													}
													new PetShowFireOfHatred(2, num, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1750"))
													{
														goto IL_5826;
													}
													new PetShowFireOfResentment(2, num, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 902631902U)
											{
												if (num2 != 912509104U)
												{
													if (num2 != 919262426U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1752"))
													{
														goto IL_5826;
													}
												}
												else
												{
													if (!(text4 == "2953"))
													{
														goto IL_5826;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 30).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1745"))
												{
													goto IL_5826;
												}
												new PetReduceBloodAfterFinish(2, probability, num, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 936187140U)
										{
											if (num2 != 919409521U)
											{
												if (num2 != 936040045U)
												{
													if (num2 != 936187140U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1743"))
													{
														goto IL_5826;
													}
													new PetAddMagicAttack(2, probability, num, text2).Start(this);
													goto IL_584C;
												}
												else if (!(text4 == "1753"))
												{
													goto IL_5826;
												}
											}
											else
											{
												if (!(text4 == "1744"))
												{
													goto IL_5826;
												}
												new PetAddBaseDamage(2, probability, num, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 != 969742378U)
										{
											if (num2 != 979619580U)
											{
												if (num2 != 996397199U)
												{
													goto IL_5826;
												}
												if (!(text4 == "2956"))
												{
													goto IL_5826;
												}
												new PetAddTurn(120, probability, num, text2).Start(this);
												goto IL_584C;
											}
											else
											{
												if (!(text4 == "2957"))
												{
													goto IL_5826;
												}
												new PetCritRate100(probability, num, text2).Start(this);
												goto IL_584C;
											}
										}
										else
										{
											if (!(text4 == "1741"))
											{
												goto IL_5826;
											}
											new PetReduceEnergy(3, probability, num, text2).Start(this);
											goto IL_584C;
										}
										new PetReduceTakeDamageForTeamWithFire(2, probability, num, text2).Start(this);
									}
									else
									{
										if (num2 <= 1185682086U)
										{
											if (num2 <= 1053131468U)
											{
												if (num2 <= 1013321913U)
												{
													if (num2 != 1013174818U)
													{
														if (num2 != 1013321913U)
														{
															goto IL_5826;
														}
														if (!(text4 == "2965"))
														{
															goto IL_5826;
														}
														new CE2965(3, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "2955"))
														{
															goto IL_5826;
														}
														new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 50).Start(this);
														goto IL_584C;
													}
												}
												else if (num2 != 1019429135U)
												{
													if (num2 != 1029952437U)
													{
														if (num2 != 1053131468U)
														{
															goto IL_5826;
														}
														if (!(text4 == "70011"))
														{
															goto IL_5826;
														}
														new PetHavocInHeaven(probability, num, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "2954"))
														{
															goto IL_5826;
														}
														new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 40).Start(this);
														goto IL_584C;
													}
												}
												else
												{
													if (!(text4 == "70007"))
													{
														goto IL_5826;
													}
													new xingling1(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 <= 1103317230U)
											{
												if (num2 != 1063654770U)
												{
													if (num2 != 1103317230U)
													{
														goto IL_5826;
													}
													if (!(text4 == "70008"))
													{
														goto IL_5826;
													}
													new xingling2(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "2960"))
													{
														goto IL_5826;
													}
													new PetMakeDamagePercent(probability, num, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 1120094849U)
											{
												if (num2 != 1168904467U)
												{
													if (num2 != 1185682086U)
													{
														goto IL_5826;
													}
													if (!(text4 == "3211"))
													{
														goto IL_5826;
													}
													new AE1138(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else if (!(text4 == "3212"))
												{
													goto IL_5826;
												}
											}
											else
											{
												if (!(text4 == "70009"))
												{
													goto IL_5826;
												}
												new PetGetshield_Passive(4, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 1235867848U)
										{
											if (num2 <= 1206239296U)
											{
												if (num2 != 1202459705U)
												{
													if (num2 != 1206239296U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1574"))
													{
														goto IL_5826;
													}
													new PetKungFuSpecialization(probability, num, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "3210"))
													{
														goto IL_5826;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 50).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 1219237324U)
											{
												if (num2 != 1223016915U)
												{
													if (num2 != 1235867848U)
													{
														goto IL_5826;
													}
													if (!(text4 == "3208"))
													{
														goto IL_5826;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 30).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1575"))
													{
														goto IL_5826;
													}
													new PetKungFuSpecialization_AddMPPassive(probability, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "3217"))
												{
													goto IL_5826;
												}
												new PetDamageAllEnemy(probability, num, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 1252792562U)
										{
											if (num2 != 1239794534U)
											{
												if (num2 != 1252645467U)
												{
													if (num2 != 1252792562U)
													{
														goto IL_5826;
													}
													if (!(text4 == "3215"))
													{
														goto IL_5826;
													}
												}
												else
												{
													if (!(text4 == "3209"))
													{
														goto IL_5826;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 40).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1576"))
												{
													goto IL_5826;
												}
												new GongfuSpecial(num, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 != 1256572153U)
										{
											if (num2 != 1269570181U)
											{
												if (num2 != 1306905010U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1572"))
												{
													goto IL_5826;
												}
												new PetViolentPosture_ReduceBlood(probability, num, text2).Start(this);
												goto IL_584C;
											}
											else if (!(text4 == "3214"))
											{
												goto IL_5826;
											}
										}
										else
										{
											if (!(text4 == "1577"))
											{
												goto IL_5826;
											}
											new PetAttackPosture_ReduceDander(probability, num, text2).Start(this);
											new PetAttackPosture_AddAgilityAndSpeed(probability, num, text2).Start(this);
											goto IL_584C;
										}
										new PetMakeDamagePercent(probability, num, text2).Start(this);
									}
								}
								else
								{
									if (num2 <= 2082319759U)
									{
										if (num2 <= 1892536930U)
										{
											if (num2 <= 1774946502U)
											{
												if (num2 <= 1724466550U)
												{
													if (num2 <= 1336680657U)
													{
														if (num2 != 1319903038U)
														{
															if (num2 != 1336680657U)
															{
																goto IL_5826;
															}
															if (!(text4 == "3218"))
															{
																goto IL_5826;
															}
															new PetAddBaseDamageOnPlayerBuffSkill_Passive(text2).Start(this);
															goto IL_584C;
														}
														else
														{
															if (!(text4 == "3219"))
															{
																goto IL_5826;
															}
															new PetGetshield_Passive(4, text2).Start(this);
															goto IL_584C;
														}
													}
													else if (num2 != 1690911312U)
													{
														if (num2 != 1707688931U)
														{
															if (num2 != 1724466550U)
															{
																goto IL_5826;
															}
															if (!(text4 == "1213"))
															{
																goto IL_5826;
															}
															new AE1213(coldDown, probability, gameType, num, delay, text2).Start(this);
															goto IL_584C;
														}
														else
														{
															if (!(text4 == "1210"))
															{
																goto IL_5826;
															}
															new AE1210(coldDown, probability, gameType, num, delay, text2).Start(this);
															goto IL_584C;
														}
													}
													else
													{
														if (!(text4 == "1211"))
														{
															goto IL_5826;
														}
														new AE1211(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else if (num2 <= 1741244169U)
												{
													if (num2 != 1735842832U)
													{
														if (num2 != 1741244169U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1212"))
														{
															goto IL_5826;
														}
														new AE1212(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "4252"))
														{
															goto IL_5826;
														}
														new PetMakeDamagePercent(probability, num, text2).Start(this);
														goto IL_584C;
													}
												}
												else if (num2 != 1758021788U)
												{
													if (num2 != 1774799407U)
													{
														if (num2 != 1774946502U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1266"))
														{
															goto IL_5826;
														}
														new AE1266(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1214"))
														{
															goto IL_5826;
														}
														new PE1214(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else
												{
													if (!(text4 == "1215"))
													{
														goto IL_5826;
													}
													new PE1215(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 <= 1825132264U)
											{
												if (num2 <= 1791724121U)
												{
													if (num2 != 1791577026U)
													{
														if (num2 != 1791724121U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1267"))
														{
															goto IL_5826;
														}
														new AE1267(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1217"))
														{
															goto IL_5826;
														}
														new PE1217(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else if (num2 != 1808354645U)
												{
													if (num2 != 1808501740U)
													{
														if (num2 != 1825132264U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1219"))
														{
															goto IL_5826;
														}
														goto IL_584A;
													}
													else
													{
														if (!(text4 == "1260"))
														{
															goto IL_5826;
														}
														new AE1260(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else
												{
													if (!(text4 == "1216"))
													{
														goto IL_5826;
													}
													new PE1216(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 <= 1842204073U)
											{
												if (num2 != 1836508546U)
												{
													if (num2 != 1841909883U)
													{
														if (num2 != 1842204073U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1274"))
														{
															goto IL_5826;
														}
														new PE1274(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1218"))
														{
															goto IL_5826;
														}
														goto IL_584A;
													}
												}
												else
												{
													if (!(text4 == "4254"))
													{
														goto IL_5826;
													}
													new PetReduceTargetMaxBlood(3, probability, num, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 1858834597U)
											{
												if (num2 != 1875906406U)
												{
													if (num2 != 1892536930U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1271"))
													{
														goto IL_5826;
													}
													new AE1271(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1248"))
													{
														goto IL_5826;
													}
													goto IL_4700;
												}
											}
											else
											{
												if (!(text4 == "1263"))
												{
													goto IL_5826;
												}
												new PE1263(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 2026904977U)
										{
											if (num2 <= 1976572120U)
											{
												if (num2 <= 1909314549U)
												{
													if (num2 != 1892684025U)
													{
														if (num2 != 1909314549U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1270"))
														{
															goto IL_5826;
														}
														new AE1270(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1249"))
														{
															goto IL_5826;
														}
														goto IL_4720;
													}
												}
												else if (num2 != 1942722692U)
												{
													if (num2 != 1959500311U)
													{
														if (num2 != 1976572120U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1242"))
														{
															goto IL_5826;
														}
														new PE1242(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1269"))
														{
															goto IL_5826;
														}
														new AE1269(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else
												{
													if (!(text4 == "1268"))
													{
														goto IL_5826;
													}
													new AE1268(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 <= 1998431664U)
											{
												if (num2 != 1993349739U)
												{
													if (num2 != 1998431664U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1170"))
													{
														goto IL_5826;
													}
													new AE1170(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1243"))
													{
														goto IL_5826;
													}
													new AE1243(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 2010127358U)
											{
												if (num2 != 2015209283U)
												{
													if (num2 != 2026904977U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1241"))
													{
														goto IL_5826;
													}
													new PE1241(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1171"))
													{
														goto IL_5826;
													}
													new AE1171(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1240"))
												{
													goto IL_5826;
												}
												new AE1240(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 2049750259U)
										{
											if (num2 <= 2043682596U)
											{
												if (num2 != 2031986902U)
												{
													if (num2 != 2043682596U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1246"))
													{
														goto IL_5826;
													}
													new AE1246(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1172"))
													{
														goto IL_5826;
													}
													new AE1172(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 2048764521U)
											{
												if (num2 != 2048911616U)
												{
													if (num2 != 2049750259U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1113"))
													{
														goto IL_5826;
													}
													new PE1113(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1163"))
													{
														goto IL_5826;
													}
													new PE1163(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1173"))
												{
													goto IL_5826;
												}
												new AE1173(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 2065689235U)
										{
											if (num2 != 2060460215U)
											{
												if (num2 != 2065542140U)
												{
													if (num2 != 2065689235U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1162"))
													{
														goto IL_5826;
													}
													new AE1162(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1174"))
													{
														goto IL_5826;
													}
													new AE1174(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1247"))
												{
													goto IL_5826;
												}
												new AE1247(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 != 2066527878U)
										{
											if (num2 != 2077237834U)
											{
												if (num2 != 2082319759U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1175"))
												{
													goto IL_5826;
												}
												new AE1175(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
											else
											{
												if (!(text4 == "1244"))
												{
													goto IL_5826;
												}
												new AE1244(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (!(text4 == "1110"))
										{
											goto IL_5826;
										}
									}
									else if (num2 <= 2217526449U)
									{
										if (num2 <= 2132799711U)
										{
											if (num2 <= 2100230211U)
											{
												if (num2 <= 2083452592U)
												{
													if (num2 != 2082466854U)
													{
														if (num2 != 2083452592U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1105"))
														{
															goto IL_5826;
														}
														new AE1105(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1161"))
														{
															goto IL_5826;
														}
														new AE1161(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else if (num2 != 2099097378U)
												{
													if (num2 != 2100083116U)
													{
														if (num2 != 2100230211U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1104"))
														{
															goto IL_5826;
														}
														new AE1104(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1116"))
														{
															goto IL_5826;
														}
														new AE1116(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else
												{
													if (!(text4 == "1176"))
													{
														goto IL_5826;
													}
													new AE1176(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 <= 2116860735U)
											{
												if (num2 != 2115874997U)
												{
													if (num2 != 2116860735U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1117"))
													{
														goto IL_5826;
													}
													new AE1117(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1177"))
													{
														goto IL_5826;
													}
													new AE1177(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 2117007830U)
											{
												if (num2 != 2132652616U)
												{
													if (num2 != 2132799711U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1166"))
													{
														goto IL_5826;
													}
													new PE1166(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1178"))
													{
														goto IL_5826;
													}
													new AE1178(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1107"))
												{
													goto IL_5826;
												}
												new PE1107(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 2150415973U)
										{
											if (num2 <= 2133785449U)
											{
												if (num2 != 2133638354U)
												{
													if (num2 != 2133785449U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1106"))
													{
														goto IL_5826;
													}
													new PE1106(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1114"))
													{
														goto IL_5826;
													}
													new PE1114(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 2149430235U)
											{
												if (num2 != 2149577330U)
												{
													if (num2 != 2150415973U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1115"))
													{
														goto IL_5826;
													}
													new AE1115(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1165"))
													{
														goto IL_5826;
													}
													new PE1165(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1179"))
												{
													goto IL_5826;
												}
												new AE1179(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else
										{
											if (num2 > 2167340687U)
											{
												if (num2 != 2200748830U)
												{
													if (num2 != 2202028758U)
													{
														if (num2 != 2217526449U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1119"))
														{
															goto IL_5826;
														}
													}
													else
													{
														if (!(text4 == "1198"))
														{
															goto IL_5826;
														}
														new AE1198(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else if (!(text4 == "1118"))
												{
													goto IL_5826;
												}
												new AE1118(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
											if (num2 != 2150563068U)
											{
												if (num2 != 2166354949U)
												{
													if (num2 != 2167340687U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1100"))
													{
														goto IL_5826;
													}
													new AE1100(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1164"))
													{
														goto IL_5826;
													}
													new PE1164(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1101"))
												{
													goto IL_5826;
												}
												new AE1101(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
									}
									else
									{
										if (num2 <= 2336249710U)
										{
											if (num2 <= 2302694472U)
											{
												if (num2 <= 2284784020U)
												{
													if (num2 != 2218806377U)
													{
														if (num2 != 2284784020U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1109"))
														{
															goto IL_5826;
														}
														goto IL_4119;
													}
													else
													{
														if (!(text4 == "1199"))
														{
															goto IL_5826;
														}
														new AE1199(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else if (num2 != 2286063948U)
												{
													if (num2 != 2301561639U)
													{
														if (num2 != 2302694472U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1192"))
														{
															goto IL_5826;
														}
														new PE1192(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
													else
													{
														if (!(text4 == "1108"))
														{
															goto IL_5826;
														}
														new PE1108(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else if (!(text4 == "1189"))
												{
													goto IL_5826;
												}
											}
											else if (num2 <= 2319472091U)
											{
												if (num2 != 2302841567U)
												{
													if (num2 != 2319472091U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1193"))
													{
														goto IL_5826;
													}
													new PE1193(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else if (!(text4 == "1188"))
												{
													goto IL_5826;
												}
											}
											else if (num2 != 2324554016U)
											{
												if (num2 != 2326837640U)
												{
													if (num2 != 2336249710U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1190"))
													{
														goto IL_5826;
													}
													new PE1190(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "4393"))
													{
														goto IL_5826;
													}
													new PetChangeSuit(4, num, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1040"))
												{
													goto IL_5826;
												}
												new AE1040(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
											new AE1189(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_584C;
										}
										if (num2 <= 2358860995U)
										{
											if (num2 <= 2353027329U)
											{
												if (num2 != 2341331635U)
												{
													if (num2 != 2353027329U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1191"))
													{
														goto IL_5826;
													}
													new PE1191(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1041"))
													{
														goto IL_5826;
													}
													new AE1041(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 2353174424U)
											{
												if (num2 != 2358109254U)
												{
													if (num2 != 2358860995U)
													{
														goto IL_5826;
													}
													if (!(text4 == "3182"))
													{
														goto IL_5826;
													}
													new PE3182(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1042"))
													{
														goto IL_5826;
													}
													new AE1042(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1185"))
												{
													goto IL_5826;
												}
												new AE1185(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 2369952043U)
										{
											if (num2 != 2359094992U)
											{
												if (num2 != 2369804948U)
												{
													if (num2 != 2369952043U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1184"))
													{
														goto IL_5826;
													}
													new AE1184(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1196"))
													{
														goto IL_5826;
													}
													new AE1196(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1022"))
												{
													goto IL_5826;
												}
												new AE1022(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 != 2374886873U)
										{
											if (num2 != 2375638614U)
											{
												if (num2 != 2375872611U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1023"))
												{
													goto IL_5826;
												}
												new AE1023(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
											else
											{
												if (!(text4 == "3181"))
												{
													goto IL_5826;
												}
												new PE3181(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else
										{
											if (!(text4 == "1043"))
											{
												goto IL_5826;
											}
											new AE1043(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_584C;
										}
									}
									IL_4119:
									new PE1110(coldDown, probability, gameType, num, delay, "1110").Start(this);
								}
								IL_584A:;
							}
							else if (num2 <= 2999436919U)
							{
								if (num2 <= 2527018277U)
								{
									if (num2 <= 2442291539U)
									{
										if (num2 <= 2409427849U)
										{
											if (num2 <= 2392650230U)
											{
												if (num2 <= 2386729662U)
												{
													if (num2 != 2386582567U)
													{
														if (num2 != 2386729662U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1187"))
														{
															goto IL_5826;
														}
														new AE1187(coldDown, probability, gameType, num, delay, text2).Start(this);
													}
													else
													{
														if (!(text4 == "1197"))
														{
															goto IL_5826;
														}
														new AE1197(coldDown, probability, gameType, num, delay, text2).Start(this);
													}
												}
												else if (num2 != 2391664492U)
												{
													if (num2 != 2392416233U)
													{
														if (num2 != 2392650230U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1020"))
														{
															goto IL_5826;
														}
														new AE1020(coldDown, probability, gameType, num, delay, text2).Start(this);
													}
													else
													{
														if (!(text4 == "3180"))
														{
															goto IL_5826;
														}
														new PE3180(coldDown, probability, gameType, num, delay, text2).Start(this);
													}
												}
												else
												{
													if (!(text4 == "1044"))
													{
														goto IL_5826;
													}
													new AE1044(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
											}
											else if (num2 <= 2403360186U)
											{
												if (num2 != 2393948116U)
												{
													if (num2 != 2403360186U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1194"))
													{
														goto IL_5826;
													}
													new AE1194(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
												else
												{
													if (!(text4 == "4397"))
													{
														goto IL_5826;
													}
													new PetAddMaxBlood(3, probability, num, text2).Start(this);
												}
											}
											else if (num2 != 2403507281U)
											{
												if (num2 != 2408442111U)
												{
													if (num2 != 2409427849U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1021"))
													{
														goto IL_5826;
													}
													new AE1021(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
												else
												{
													if (!(text4 == "1045"))
													{
														goto IL_5826;
													}
													new AE1045(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
											}
											else
											{
												if (!(text4 == "1186"))
												{
													goto IL_5826;
												}
												new AE1186(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
										}
										else if (num2 <= 2426205468U)
										{
											if (num2 <= 2420284900U)
											{
												if (num2 != 2420137805U)
												{
													if (num2 != 2420284900U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1181"))
													{
														goto IL_5826;
													}
													new AE1181(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
												else
												{
													if (!(text4 == "1195"))
													{
														goto IL_5826;
													}
													new AE1195(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
											}
											else if (num2 != 2425219730U)
											{
												if (num2 != 2425366825U)
												{
													if (num2 != 2426205468U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1026"))
													{
														goto IL_5826;
													}
													new AE1026(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
												else
												{
													if (!(text4 == "1050"))
													{
														goto IL_5826;
													}
													new AE1050(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
											}
											else
											{
												if (!(text4 == "1046"))
												{
													goto IL_5826;
												}
												new AE1046(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
										}
										else if (num2 <= 2437062519U)
										{
											if (num2 != 2426352563U)
											{
												if (num2 != 2427503354U)
												{
													if (num2 != 2437062519U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1180"))
													{
														goto IL_5826;
													}
													new AE1180(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
												else
												{
													if (!(text4 == "4395"))
													{
														goto IL_5826;
													}
													new PetChangeWhiteTiger(4, num, text2).Start(this);
												}
											}
											else
											{
												if (!(text4 == "1034"))
												{
													goto IL_5826;
												}
												new AE1034(coldDown, probability, gameType, num, delay, "1031").Start(this);
											}
										}
										else if (num2 != 2441997349U)
										{
											if (num2 != 2442144444U)
											{
												if (num2 != 2442291539U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1067"))
												{
													goto IL_5826;
												}
												new AE1067(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
											else
											{
												if (!(text4 == "1057"))
												{
													goto IL_5826;
												}
												new AE1057(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
										}
										else
										{
											if (!(text4 == "1047"))
											{
												goto IL_5826;
											}
											new AE1047(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
									}
									else if (num2 <= 2475552587U)
									{
										if (num2 <= 2458922063U)
										{
											if (num2 <= 2444115920U)
											{
												if (num2 != 2442983087U)
												{
													if (num2 != 2444115920U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1097"))
													{
														goto IL_5826;
													}
													new AE1097(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
												else
												{
													if (!(text4 == "1027"))
													{
														goto IL_5826;
													}
													new AE1027(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
											}
											else if (num2 != 2453840138U)
											{
												if (num2 != 2458774968U)
												{
													if (num2 != 2458922063U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1056"))
													{
														goto IL_5826;
													}
													new AE1056(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
												else
												{
													if (!(text4 == "1048"))
													{
														goto IL_5826;
													}
													new AE1048(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
											}
											else
											{
												if (!(text4 == "1183"))
												{
													goto IL_5826;
												}
												new AE1183(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
										}
										else if (num2 <= 2459760706U)
										{
											if (num2 != 2459069158U)
											{
												if (num2 != 2459760706U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1024"))
												{
													goto IL_5826;
												}
												new AE1024(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
											else
											{
												if (!(text4 == "1064"))
												{
													goto IL_5826;
												}
												new AE1064(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
										}
										else if (num2 != 2460893539U)
										{
											if (num2 != 2470617757U)
											{
												if (num2 != 2475552587U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1049"))
												{
													goto IL_5826;
												}
												new AE1049(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
											else
											{
												if (!(text4 == "1182"))
												{
													goto IL_5826;
												}
												new AE1182(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
										}
										else
										{
											if (!(text4 == "1096"))
											{
												goto IL_5826;
											}
											new AE1096(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
									}
									else
									{
										if (num2 <= 2495200518U)
										{
											if (num2 <= 2476538325U)
											{
												if (num2 != 2475699682U)
												{
													if (num2 != 2476538325U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1025"))
													{
														goto IL_5826;
													}
													new AE1025(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1055"))
													{
														goto IL_5826;
													}
													new AE1053(coldDown, probability, gameType, num, delay, "1053").Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 2477671158U)
											{
												if (num2 != 2494448777U)
												{
													if (num2 != 2495200518U)
													{
														goto IL_5826;
													}
													if (!(text4 == "3178"))
													{
														goto IL_5826;
													}
												}
												else
												{
													if (!(text4 == "1094"))
													{
														goto IL_5826;
													}
													new AE1094(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1095"))
												{
													goto IL_5826;
												}
												new AE1095(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 2511226396U)
										{
											if (num2 != 2509402015U)
											{
												if (num2 != 2510240658U)
												{
													if (num2 != 2511226396U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1093"))
													{
														goto IL_5826;
													}
													new AE1093(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1033"))
													{
														goto IL_5826;
													}
													new AE1033(coldDown, probability, gameType, num, delay, "1030").Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1063"))
												{
													goto IL_5826;
												}
												new AE1063(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 != 2511978137U)
										{
											if (num2 != 2526871182U)
											{
												if (num2 != 2527018277U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1032"))
												{
													goto IL_5826;
												}
												new AE1032(coldDown, probability, gameType, num, delay, "1029").Start(this);
												goto IL_584C;
											}
											else
											{
												if (!(text4 == "1028"))
												{
													goto IL_5826;
												}
												new AE1028(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (!(text4 == "3179"))
										{
											goto IL_5826;
										}
										new AE3179(coldDown, probability, gameType, num, delay, "3179").Start(this);
									}
								}
								else
								{
									if (num2 > 2696998492U)
									{
										if (num2 <= 2781327872U)
										{
											if (num2 <= 2747331349U)
											{
												if (num2 <= 2713776111U)
												{
													if (num2 != 2713309565U)
													{
														if (num2 != 2713776111U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1715"))
														{
															goto IL_5826;
														}
														goto IL_50E4;
													}
													else
													{
														if (!(text4 == "3175"))
														{
															goto IL_5826;
														}
														new AE3175(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_584C;
													}
												}
												else if (num2 != 2714908944U)
												{
													if (num2 != 2730553730U)
													{
														if (num2 != 2747331349U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1717"))
														{
															goto IL_5826;
														}
													}
													else
													{
														if (!(text4 == "1716"))
														{
															goto IL_5826;
														}
														goto IL_50E4;
													}
												}
												else
												{
													if (!(text4 == "1765"))
													{
														goto IL_5826;
													}
													new PetAddMpAfterUseSkillCount_Passive(text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 <= 2764108968U)
											{
												if (num2 != 2748464182U)
												{
													if (num2 != 2764108968U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1718"))
													{
														goto IL_5826;
													}
												}
												else
												{
													if (!(text4 == "1767"))
													{
														goto IL_5826;
													}
													goto IL_5142;
												}
											}
											else if (num2 != 2780886587U)
											{
												if (num2 != 2781180777U)
												{
													if (num2 != 2781327872U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1729"))
													{
														goto IL_5826;
													}
													new PetChangeWindDirection(num, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1735"))
													{
														goto IL_5826;
													}
													new PetAddMaxBlood(3, probability, num, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (!(text4 == "1719"))
											{
												goto IL_5826;
											}
											new PetReduceEnemyBlood(3, probability, num, text2).Start(this);
											goto IL_584C;
										}
										if (num2 <= 2899609848U)
										{
											if (num2 <= 2831513634U)
											{
												if (num2 != 2797958396U)
												{
													if (num2 != 2831513634U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1730"))
													{
														goto IL_5826;
													}
													new PetAddShootCount(num, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1732"))
													{
														goto IL_5826;
													}
													new PetReduceSpeed(2, probability, num, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 2848291253U)
											{
												if (num2 != 2849424086U)
												{
													if (num2 != 2899609848U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1790"))
													{
														goto IL_5826;
													}
													new PetBlackFlameBomb(2, probability, num, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1789"))
													{
														goto IL_5826;
													}
													new PetAddMpBeginSelfTurn(3, probability, num, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1731"))
												{
													goto IL_5826;
												}
												new PetIceImmunity(2, probability, num, text2).Start(this);
												goto IL_584C;
											}
										}
										else
										{
											if (num2 > 2934755470U)
											{
												if (num2 != 2949104062U)
												{
													if (num2 != 2982659300U)
													{
														if (num2 != 2999436919U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1724"))
														{
															goto IL_5826;
														}
													}
													else if (!(text4 == "1725"))
													{
														goto IL_5826;
													}
												}
												else if (!(text4 == "1723"))
												{
													goto IL_5826;
												}
												new PetAddDamageOnPlayerAboveOrBellowOfMe_Passive(probability, text2).Start(this);
												goto IL_584C;
											}
											if (num2 != 2916240372U)
											{
												if (num2 != 2933017991U)
												{
													if (num2 != 2934755470U)
													{
														goto IL_5826;
													}
													if (!(text4 == "3080"))
													{
														goto IL_5826;
													}
													new PE3080(text2).Start(this);
													goto IL_584C;
												}
												else if (!(text4 == "1768"))
												{
													goto IL_5826;
												}
											}
											else if (!(text4 == "1769"))
											{
												goto IL_5826;
											}
										}
										IL_5142:
										new PetReduceTakeDamageEquip(probability, text2).Start(this);
										goto IL_584C;
									}
									if (num2 <= 2627683991U)
									{
										if (num2 <= 2595866232U)
										{
											if (num2 <= 2542810158U)
											{
												if (num2 != 2528004015U)
												{
													if (num2 != 2542810158U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1059"))
													{
														goto IL_5826;
													}
													new AE1059(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1092"))
													{
														goto IL_5826;
													}
													new AE1092(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 2559587777U)
											{
												if (num2 != 2579235708U)
												{
													if (num2 != 2595866232U)
													{
														goto IL_5826;
													}
													if (!(text4 == "3172"))
													{
														goto IL_5826;
													}
													new AE3173(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "3169"))
													{
														goto IL_5826;
													}
													new AE3169(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "1058"))
												{
													goto IL_5826;
												}
												new AE1058(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 2610906372U)
										{
											if (num2 != 2596013327U)
											{
												if (num2 != 2610906372U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1039"))
												{
													goto IL_5826;
												}
												new AE1039(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
											else
											{
												if (!(text4 == "3168"))
												{
													goto IL_5826;
												}
												new PE3168(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 != 2612643851U)
										{
											if (num2 != 2622927441U)
											{
												if (num2 != 2627683991U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1038"))
												{
													goto IL_5826;
												}
												new AE1038(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
											else
											{
												if (!(text4 == "4567"))
												{
													goto IL_5826;
												}
												new HeihuBigPower(3, probability, num, text2).Start(this);
												goto IL_584C;
											}
										}
										else
										{
											if (!(text4 == "3173"))
											{
												goto IL_5826;
											}
											new AE3173(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_584C;
										}
									}
									else if (num2 <= 2679002586U)
									{
										if (num2 <= 2646199089U)
										{
											if (num2 != 2629421470U)
											{
												if (num2 != 2646199089U)
												{
													goto IL_5826;
												}
												if (!(text4 == "3171"))
												{
													goto IL_5826;
												}
												new AE3171(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
											else
											{
												if (!(text4 == "3170"))
												{
													goto IL_5826;
												}
												new AE3170(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 != 2660400586U)
										{
											if (num2 != 2662976708U)
											{
												if (num2 != 2679002586U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1099"))
												{
													goto IL_5826;
												}
												new AE1099(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
											else
											{
												if (!(text4 == "3176"))
												{
													goto IL_5826;
												}
												new AE3176(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else
										{
											if (!(text4 == "1068"))
											{
												goto IL_5826;
											}
											new AE1068(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_584C;
										}
									}
									else if (num2 <= 2695780205U)
									{
										if (num2 != 2679754327U)
										{
											if (num2 != 2679901422U)
											{
												if (num2 != 2695780205U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1098"))
												{
													goto IL_5826;
												}
												new AE1098(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
											else
											{
												if (!(text4 == "3167"))
												{
													goto IL_5826;
												}
												new PE3167(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else
										{
											if (!(text4 == "3177"))
											{
												goto IL_5826;
											}
											new AE3177(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_584C;
										}
									}
									else if (num2 != 2696531946U)
									{
										if (num2 != 2696679041U)
										{
											if (num2 != 2696998492U)
											{
												goto IL_5826;
											}
											if (!(text4 == "1714"))
											{
												goto IL_5826;
											}
										}
										else
										{
											if (!(text4 == "3166"))
											{
												goto IL_5826;
											}
											new PE3166(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_584C;
										}
									}
									else
									{
										if (!(text4 == "3174"))
										{
											goto IL_5826;
										}
										new PE3174(coldDown, probability, gameType, num, delay, text2).Start(this);
										goto IL_584C;
									}
									IL_50E4:
									new PetShareTakeDamageToTeam(2, probability, num, text2).Start(this);
								}
							}
							else if (num2 <= 3756878669U)
							{
								if (num2 <= 3395890906U)
								{
									if (num2 <= 3178867761U)
									{
										if (num2 <= 3145459618U)
										{
											if (num2 <= 3032992157U)
											{
												if (num2 != 3016214538U)
												{
													if (num2 != 3032992157U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1726"))
													{
														goto IL_5826;
													}
												}
												else if (!(text4 == "1727"))
												{
													goto IL_5826;
												}
												new PetChangeDamageWind(num, text2).Start(this);
											}
											else if (num2 != 3061424428U)
											{
												if (num2 != 3066570302U)
												{
													if (num2 != 3145459618U)
													{
														goto IL_5826;
													}
													if (!(text4 == "4849"))
													{
														goto IL_5826;
													}
													new PE4849(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
												else
												{
													if (!(text4 == "1820"))
													{
														goto IL_5826;
													}
													new ShowPic(text2).Start(this);
												}
											}
											else
											{
												if (!(text4 == "4858"))
												{
													goto IL_5826;
												}
												new AE4858(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
										}
										else if (num2 <= 3162237237U)
										{
											if (num2 != 3162090142U)
											{
												if (num2 != 3162237237U)
												{
													goto IL_5826;
												}
												if (!(text4 == "4848"))
												{
													goto IL_5826;
												}
												new AE4848(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
											else
											{
												if (!(text4 == "4856"))
												{
													goto IL_5826;
												}
												new AE4856(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
										}
										else if (num2 != 3162431263U)
										{
											if (num2 != 3177781859U)
											{
												if (num2 != 3178867761U)
												{
													goto IL_5826;
												}
												if (!(text4 == "4857"))
												{
													goto IL_5826;
												}
												new AE4857(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
											else
											{
												if (!(text4 == "2978"))
												{
													goto IL_5826;
												}
												new PetAddBaseGuard_Passive(probability, text2).Start(this);
											}
										}
										else
										{
											if (!(text4 == "2981"))
											{
												goto IL_5826;
											}
											new huoniao(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
									}
									else if (num2 <= 3246125332U)
									{
										if (num2 <= 3229200618U)
										{
											if (num2 != 3212570094U)
											{
												if (num2 != 3229200618U)
												{
													goto IL_5826;
												}
												if (!(text4 == "4852"))
												{
													goto IL_5826;
												}
												new AE4852(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
											else
											{
												if (!(text4 == "4845"))
												{
													goto IL_5826;
												}
												new AE4845(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
										}
										else if (num2 != 3229347713U)
										{
											if (num2 != 3245978237U)
											{
												if (num2 != 3246125332U)
												{
													goto IL_5826;
												}
												if (!(text4 == "4843"))
												{
													goto IL_5826;
												}
												new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 50).Start(this);
											}
											else
											{
												if (!(text4 == "4853"))
												{
													goto IL_5826;
												}
												new PE4853(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
										}
										else
										{
											if (!(text4 == "4844"))
											{
												goto IL_5826;
											}
											new AE4844(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
									}
									else if (num2 <= 3328780430U)
									{
										if (num2 != 3295225192U)
										{
											if (num2 != 3312002811U)
											{
												if (num2 != 3328780430U)
												{
													goto IL_5826;
												}
												if (!(text4 == "2973"))
												{
													goto IL_5826;
												}
											}
											else if (!(text4 == "2970"))
											{
												goto IL_5826;
											}
											new AE2970(1, probability, gameType, num, delay, text2).Start(this);
										}
										else
										{
											if (!(text4 == "2971"))
											{
												goto IL_5826;
											}
											new PetFatalEffect(0, probability, num, delay, text2).Start(this);
										}
									}
									else if (num2 != 3345558049U)
									{
										if (num2 != 3383697270U)
										{
											if (num2 != 3395890906U)
											{
												goto IL_5826;
											}
											if (!(text4 == "2977"))
											{
												goto IL_5826;
											}
											new PetAddBaseDamage_Passive(probability, text2).Start(this);
										}
										else
										{
											if (!(text4 == "3224"))
											{
												goto IL_5826;
											}
											new PetReduceTakeCritDamage(-1, probability, num, text2).Start(this);
										}
									}
									else
									{
										if (!(text4 == "2972"))
										{
											goto IL_5826;
										}
										new PetAddCritRate(1, probability, num, text2).Start(this);
									}
								}
								else
								{
									if (num2 <= 3588955384U)
									{
										if (num2 <= 3450807746U)
										{
											if (num2 <= 3412668525U)
											{
												if (num2 != 3404254480U)
												{
													if (num2 != 3412668525U)
													{
														goto IL_5826;
													}
													if (!(text4 == "2976"))
													{
														goto IL_5826;
													}
													new PetUseMPToAddBlood(probability, num, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1567"))
													{
														goto IL_5826;
													}
													new PetViolentPosture_ReduceEnemyBaseGuard(1, probability, num, text2).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 3417252508U)
											{
												if (num2 != 3434030127U)
												{
													if (num2 != 3450807746U)
													{
														goto IL_5826;
													}
													if (!(text4 == "3220"))
													{
														goto IL_5826;
													}
													new PetReduceTakeDamageOnGameStarted_Passive(2, text2).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "3223"))
													{
														goto IL_5826;
													}
													new PetReduceTakeDamage_Passive(probability, text2).Start(this);
													goto IL_584C;
												}
											}
											else
											{
												if (!(text4 == "3222"))
												{
													goto IL_5826;
												}
												new binglongfuhuo(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 <= 3488289670U)
										{
											if (num2 != 3488142575U)
											{
												if (num2 != 3488289670U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1558"))
												{
													goto IL_5826;
												}
												new PetStealthPosture_HidePassive(probability, text2).Start(this);
												goto IL_584C;
											}
											else
											{
												if (!(text4 == "1562"))
												{
													goto IL_5826;
												}
												new PetControlPosture_LockDirection(2, probability, num, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (num2 != 3521697813U)
										{
											if (num2 != 3572324860U)
											{
												if (num2 != 3588955384U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1552"))
												{
													goto IL_5826;
												}
												goto IL_53B8;
											}
											else if (!(text4 == "1549"))
											{
												goto IL_5826;
											}
										}
										else
										{
											if (!(text4 == "1560"))
											{
												goto IL_5826;
											}
											new PetClonePosture(1, probability, num, delay, text2).Start(this);
											goto IL_584C;
										}
									}
									else
									{
										if (num2 > 3656065860U)
										{
											if (num2 <= 3689621098U)
											{
												if (num2 != 3656212955U)
												{
													if (num2 != 3672990574U)
													{
														if (num2 != 3689621098U)
														{
															goto IL_5826;
														}
														if (!(text4 == "1554"))
														{
															goto IL_5826;
														}
													}
													else
													{
														if (!(text4 == "1547"))
														{
															goto IL_5826;
														}
														new PetReduceTargetBaseDamage(2, probability, num, text2).Start(this);
														goto IL_584C;
													}
												}
												else
												{
													if (!(text4 == "1544"))
													{
														goto IL_5826;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 50).Start(this);
													goto IL_584C;
												}
											}
											else if (num2 != 3706398717U)
											{
												if (num2 != 3740101050U)
												{
													if (num2 != 3756878669U)
													{
														goto IL_5826;
													}
													if (!(text4 == "1542"))
													{
														goto IL_5826;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 30).Start(this);
													goto IL_584C;
												}
												else
												{
													if (!(text4 == "1543"))
													{
														goto IL_5826;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 40).Start(this);
													goto IL_584C;
												}
											}
											else if (!(text4 == "1555"))
											{
												goto IL_5826;
											}
											new PetDefensivePosture_Damage(2, probability, num, text2).Start(this);
											goto IL_584C;
										}
										if (num2 <= 3605733003U)
										{
											if (num2 != 3589102479U)
											{
												if (num2 != 3605733003U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1553"))
												{
													goto IL_5826;
												}
												goto IL_53B8;
											}
											else if (!(text4 == "1548"))
											{
												goto IL_5826;
											}
										}
										else if (num2 != 3622510622U)
										{
											if (num2 != 3639288241U)
											{
												if (num2 != 3656065860U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1556"))
												{
													goto IL_5826;
												}
												new PetAddTurn(120, probability, num, text2).Start(this);
												goto IL_584C;
											}
											else
											{
												if (!(text4 == "1551"))
												{
													goto IL_5826;
												}
												new PetCritRate100(probability, num, text2).Start(this);
												goto IL_584C;
											}
										}
										else if (!(text4 == "1550"))
										{
											goto IL_5826;
										}
									}
									new PetMakeDamagePercent(probability, num, text2).Start(this);
									goto IL_584C;
									IL_53B8:
									new PetDefensivePosture_CritDamage(2, probability, num, text2).Start(this);
								}
							}
							else if (num2 <= 4174587304U)
							{
								if (num2 <= 4117867372U)
								{
									if (num2 <= 4056158233U)
									{
										if (num2 <= 3955345424U)
										{
											if (num2 != 3849681832U)
											{
												if (num2 != 3955345424U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1228"))
												{
													goto IL_5826;
												}
												new AE1228(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
											else
											{
												if (!(text4 == "3486"))
												{
													goto IL_5826;
												}
												new PetSpikeEnemy(probability, num, text2).Start(this);
											}
										}
										else if (num2 != 3972123043U)
										{
											if (num2 != 4039380614U)
											{
												if (num2 != 4056158233U)
												{
													goto IL_5826;
												}
												if (!(text4 == "1238"))
												{
													goto IL_5826;
												}
												new AE1238(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
											else
											{
												if (!(text4 == "1239"))
												{
													goto IL_5826;
												}
												new AE1239(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
										}
										else
										{
											if (!(text4 == "1229"))
											{
												goto IL_5826;
											}
											new AE1229(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
									}
									else if (num2 <= 4089566376U)
									{
										if (num2 != 4077011474U)
										{
											if (num2 != 4089566376U)
											{
												goto IL_5826;
											}
											if (!(text4 == "1220"))
											{
												goto IL_5826;
											}
											new AE1220(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
										else
										{
											if (!(text4 == "2638"))
											{
												goto IL_5826;
											}
											new FireDefenceSkill(this.m_player.PlayerCharacter.EmblemInfoAttackSKill, (int)this.FireDefence).Start(this);
										}
									}
									else if (num2 != 4106343995U)
									{
										if (num2 != 4107476828U)
										{
											if (num2 != 4117867372U)
											{
												goto IL_5826;
											}
											if (!(text4 == "4278"))
											{
												goto IL_5826;
											}
											new CE4278(1, num, text2).Start(this);
										}
										else
										{
											if (!(text4 == "1259"))
											{
												goto IL_5826;
											}
											new AE1259(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
									}
									else
									{
										if (!(text4 == "1221"))
										{
											goto IL_5826;
										}
										new AE1221(coldDown, probability, gameType, num, delay, text2).Start(this);
									}
								}
								else if (num2 <= 4156676852U)
								{
									if (num2 <= 4124254447U)
									{
										if (num2 != 4123121614U)
										{
											if (num2 != 4124254447U)
											{
												goto IL_5826;
											}
											if (!(text4 == "1258"))
											{
												goto IL_5826;
											}
											new AE1258(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
										else
										{
											if (!(text4 == "1222"))
											{
												goto IL_5826;
											}
											new AE1222(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
									}
									else if (num2 != 4139899233U)
									{
										if (num2 != 4140046328U)
										{
											if (num2 != 4156676852U)
											{
												goto IL_5826;
											}
											if (!(text4 == "1224"))
											{
												goto IL_5826;
											}
											new AE1224(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
										else
										{
											if (!(text4 == "1233"))
											{
												goto IL_5826;
											}
											goto IL_4720;
										}
									}
									else
									{
										if (!(text4 == "1223"))
										{
											goto IL_5826;
										}
										new PE1223(coldDown, probability, gameType, num, delay, text2).Start(this);
									}
								}
								else if (num2 <= 4173454471U)
								{
									if (num2 != 4156823947U)
									{
										if (num2 != 4156971042U)
										{
											if (num2 != 4173454471U)
											{
												goto IL_5826;
											}
											if (!(text4 == "1225"))
											{
												goto IL_5826;
											}
											new AE1225(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
										else
										{
											if (!(text4 == "1208"))
											{
												goto IL_5826;
											}
											new AE1208(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
									}
									else
									{
										if (!(text4 == "1232"))
										{
											goto IL_5826;
										}
										goto IL_4700;
									}
								}
								else if (num2 != 4173601566U)
								{
									if (num2 != 4173748661U)
									{
										if (num2 != 4174587304U)
										{
											goto IL_5826;
										}
										if (!(text4 == "1255"))
										{
											goto IL_5826;
										}
										new AE1255(coldDown, probability, gameType, num, delay, text2).Start(this);
									}
									else
									{
										if (!(text4 == "1209"))
										{
											goto IL_5826;
										}
										new AE1209(coldDown, probability, gameType, num, delay, text2).Start(this);
									}
								}
								else
								{
									if (!(text4 == "1231"))
									{
										goto IL_5826;
									}
									new AE1231(coldDown, probability, gameType, num, delay, text2).Start(this);
								}
							}
							else if (num2 <= 4224081518U)
							{
								if (num2 <= 4207009709U)
								{
									if (num2 <= 4190379185U)
									{
										if (num2 != 4190232090U)
										{
											if (num2 != 4190379185U)
											{
												goto IL_5826;
											}
											if (!(text4 == "1230"))
											{
												goto IL_5826;
											}
											new AE1230(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
										else
										{
											if (!(text4 == "1226"))
											{
												goto IL_5826;
											}
											new AE1226(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
									}
									else if (num2 != 4190526280U)
									{
										if (num2 != 4191364923U)
										{
											if (num2 != 4207009709U)
											{
												goto IL_5826;
											}
											if (!(text4 == "1227"))
											{
												goto IL_5826;
											}
											new AE1227(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
										else
										{
											if (!(text4 == "1254"))
											{
												goto IL_5826;
											}
											new AE1254(coldDown, probability, gameType, num, delay, text2).Start(this);
										}
									}
									else
									{
										if (!(text4 == "1206"))
										{
											goto IL_5826;
										}
										new AE1206(coldDown, probability, gameType, num, delay, text2).Start(this);
									}
								}
								else if (num2 <= 4207303899U)
								{
									if (num2 != 4207156804U)
									{
										if (num2 != 4207303899U)
										{
											goto IL_5826;
										}
										if (!(text4 == "1207"))
										{
											goto IL_5826;
										}
										new AE1207(coldDown, probability, gameType, num, delay, text2).Start(this);
									}
									else
									{
										if (!(text4 == "1237"))
										{
											goto IL_5826;
										}
										new PE1237(coldDown, probability, gameType, num, delay, text2).Start(this);
									}
								}
								else if (num2 != 4208142542U)
								{
									if (num2 != 4223934423U)
									{
										if (num2 != 4224081518U)
										{
											goto IL_5826;
										}
										if (!(text4 == "1204"))
										{
											goto IL_5826;
										}
										new AE1204(coldDown, probability, gameType, num, delay, text2).Start(this);
									}
									else
									{
										if (!(text4 == "1236"))
										{
											goto IL_5826;
										}
										new PE1236(coldDown, probability, gameType, num, delay, text2).Start(this);
									}
								}
								else
								{
									if (!(text4 == "1257"))
									{
										goto IL_5826;
									}
									new AE1257(coldDown, probability, gameType, num, delay, text2).Start(this);
								}
							}
							else
							{
								if (num2 <= 4257489661U)
								{
									if (num2 <= 4240712042U)
									{
										if (num2 != 4224920161U)
										{
											if (num2 != 4240712042U)
											{
												goto IL_5826;
											}
											if (!(text4 == "1235"))
											{
												goto IL_5826;
											}
											new PE1235(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_584C;
										}
										else
										{
											if (!(text4 == "1256"))
											{
												goto IL_5826;
											}
											new AE1256(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_584C;
										}
									}
									else if (num2 != 4240859137U)
									{
										if (num2 != 4246926800U)
										{
											if (num2 != 4257489661U)
											{
												goto IL_5826;
											}
											if (!(text4 == "1234"))
											{
												goto IL_5826;
											}
										}
										else
										{
											if (!(text4 == "1156"))
											{
												goto IL_5826;
											}
											new AE1156(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_584C;
										}
									}
									else
									{
										if (!(text4 == "1205"))
										{
											goto IL_5826;
										}
										new AE1205(coldDown, probability, gameType, num, delay, text2).Start(this);
										goto IL_584C;
									}
								}
								else if (num2 <= 4274414375U)
								{
									if (num2 != 4257636756U)
									{
										if (num2 != 4258475399U)
										{
											if (num2 != 4274414375U)
											{
												goto IL_5826;
											}
											if (!(text4 == "1203"))
											{
												goto IL_5826;
											}
											new AE1203(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_584C;
										}
										else if (!(text4 == "1250"))
										{
											goto IL_5826;
										}
									}
									else
									{
										if (!(text4 == "1202"))
										{
											goto IL_5826;
										}
										new AE1202(coldDown, probability, gameType, num, delay, text2).Start(this);
										goto IL_584C;
									}
								}
								else if (num2 != 4275253018U)
								{
									if (num2 != 4280482038U)
									{
										if (num2 != 4291191994U)
										{
											goto IL_5826;
										}
										if (!(text4 == "1200"))
										{
											goto IL_5826;
										}
										new PE1200(coldDown, probability, gameType, num, delay, text2).Start(this);
										goto IL_584C;
									}
									else
									{
										if (!(text4 == "1154"))
										{
											goto IL_5826;
										}
										new AE1154(coldDown, probability, gameType, num, delay, text2).Start(this);
										goto IL_584C;
									}
								}
								else
								{
									if (!(text4 == "1253"))
									{
										goto IL_5826;
									}
									new PE1253(coldDown, probability, gameType, num, delay, text2).Start(this);
									goto IL_584C;
								}
								new AE1250(coldDown, probability, gameType, num, delay, "1250").Start(this);
							}
							IL_584C:
							goto IL_584D;
							IL_4700:
							new AE1248(coldDown, probability, gameType, num, delay, "1248").Start(this);
							goto IL_584C;
							IL_4720:
							new AE1249(coldDown, probability, gameType, num, delay, "1249").Start(this);
							goto IL_584C;
							IL_5826:
							Console.WriteLine("没有找到宠物技能: " + text2 + ", 宠物名字: " + this.m_pet.Name);
						}
						IL_584D:;
					}
				}
			}
		}

		// Token: 0x060076E0 RID: 30432 RVA: 0x0027B310 File Offset: 0x00279510
		public UserBufferInfo GetFightBuffByType(eBuffType buff)
		{
			foreach (UserBufferInfo userBufferInfo in this.m_player.FightBuffs)
			{
				bool flag = userBufferInfo.Type == (int)buff;
				if (flag)
				{
					return userBufferInfo;
				}
			}
			return null;
		}

		// Token: 0x060076E1 RID: 30433 RVA: 0x0027B380 File Offset: 0x00279580
		public bool ReduceEnergy(int value)
		{
			bool flag = value > this.m_energy;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				this.m_energy -= value;
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x060076E2 RID: 30434 RVA: 0x0027B3B4 File Offset: 0x002795B4
		public bool AddEnergy(int value)
		{
			this.m_energy += value;
			return true;
		}

		// Token: 0x060076E3 RID: 30435 RVA: 0x0027B3D8 File Offset: 0x002795D8
		public override bool TakeDamage(Living source, ref int damageAmount, ref int criticalAmount, string msg)
		{
			bool flag = (source == this || source.Team == base.Team) && damageAmount + criticalAmount >= this.m_blood;
			if (flag)
			{
				damageAmount = this.m_blood - 1;
				criticalAmount = 0;
			}
			bool flag2 = base.TakeDamage(source, ref damageAmount, ref criticalAmount, msg);
			bool isLiving = base.IsLiving;
			if (isLiving)
			{
				base.AddDander(damageAmount * 500 / base.MaxBlood);
				bool flag3 = !base.Game.IsSpecialPVE() && base.Blood < base.MaxBlood / 100 * 30;
				if (flag3)
				{
					UserBufferInfo fightBuffByType = this.GetFightBuffByType(eBuffType.Save_Life);
					bool flag4 = fightBuffByType != null && this.m_player.UsePayBuff(eBuffType.Save_Life) && this.m_game.RoomType == eRoomType.Dungeon;
					if (flag4)
					{
						int num = base.MaxBlood / 100 * fightBuffByType.Value;
						this.AddBlood(num);
						this.m_game.SendEquipEffect(this, LanguageMgr.GetTranslation("GameServer.PayBuff.ReLife.UseNotice", new object[]
						{
							this.PlayerDetail.PlayerCharacter.NickName,
							num
						}));
					}
				}
			}
			return flag2;
		}

		// Token: 0x060076E4 RID: 30436 RVA: 0x0027B514 File Offset: 0x00279714
		public void UseSpecialSkill()
		{
			bool flag = base.PetEffects.CurrentUseSkill != 0;
			if (flag)
			{
				this.m_game.SendGameUpdateDander(this);
			}
			else
			{
				bool flag2 = base.Dander >= 200;
				if (flag2)
				{
					this.SetBall(this.m_spBallId, true);
					this.m_ballCount = this.m_currentBall.Amount;
					base.SetDander(0);
					this.OnPlayerUseDander();
				}
			}
		}

		// Token: 0x060076E5 RID: 30437 RVA: 0x0002BF57 File Offset: 0x0002A157
		public void SetBall(int ballId)
		{
			this.SetBall(ballId, false);
		}

		// Token: 0x060076E6 RID: 30438 RVA: 0x0027B58C File Offset: 0x0027978C
		public void SetBall(int ballId, bool special)
		{
			bool flag = ballId != this.m_currentBall.ID;
			if (flag)
			{
				bool flag2 = BallMgr.FindBall(ballId) != null;
				if (flag2)
				{
					this.m_currentBall = BallMgr.FindBall(ballId);
				}
				this.m_game.SendGameUpdateBall(this, special);
			}
		}

		// Token: 0x060076E7 RID: 30439 RVA: 0x0027B5DC File Offset: 0x002797DC
		public void SetCurrentWeapon(ItemInfo item)
		{
			this.m_weapon = item;
			BombConfigInfo bombConfigInfo = BallConfigMgr.FindBall(this.m_weapon.TemplateID);
			bool flag = this.m_weapon.GoldValidDate();
			if (flag)
			{
				bombConfigInfo = BallConfigMgr.FindBall(this.m_weapon.GoldEquip.TemplateID);
			}
			bool flag2 = this.ChangeSpecialBall > 0;
			if (flag2)
			{
				bombConfigInfo = BallConfigMgr.FindBall(70396);
			}
			this.m_mainBallId = bombConfigInfo.Common;
			this.m_spBallId = bombConfigInfo.Special;
			this.m_spsBallId = bombConfigInfo.SpecialII;
			this.m_AddWoundBallId = bombConfigInfo.CommonAddWound;
			this.m_MultiBallId = bombConfigInfo.CommonMultiBall;
			this.SetBall(this.m_mainBallId);
		}

		// Token: 0x060076E8 RID: 30440 RVA: 0x0027B68C File Offset: 0x0027988C
		public override void StartMoving()
		{
			bool flag = this.m_map == null;
			if (!flag)
			{
				Point point = this.m_map.FindYLineNotEmptyPointDown(this.m_x, this.m_y);
				bool isEmpty = point.IsEmpty;
				if (isEmpty)
				{
					bool flag2 = this.m_map.Ground != null;
					if (flag2)
					{
						this.m_y = this.m_map.Ground.Height;
					}
				}
				else
				{
					this.m_x = point.X;
					this.m_y = point.Y;
				}
				bool isEmpty2 = point.IsEmpty;
				if (isEmpty2)
				{
					this.m_syncAtTime = false;
					this.Die();
					bool flag3 = base.Game.CurrentLiving != this && base.Game.CurrentLiving is Player && this != null && base.Team != base.Game.CurrentLiving.Team;
					if (flag3)
					{
						Player player = base.Game.CurrentLiving as Player;
						player.PlayerDetail.OnKillingLiving(this.m_game, 1, base.Id, base.IsLiving, 0);
						base.Game.CurrentLiving.TotalKill++;
						player.CalculatePlayerOffer(this);
					}
				}
			}
		}

		// Token: 0x060076E9 RID: 30441 RVA: 0x0027B7D8 File Offset: 0x002799D8
		public override void StartMoving(int delay, int speed)
		{
			bool flag = this.m_map == null;
			if (!flag)
			{
				Point point = this.m_map.FindYLineNotEmptyPointDown(this.m_x, this.m_y);
				bool isEmpty = point.IsEmpty;
				if (isEmpty)
				{
					this.m_y = this.m_map.Ground.Height;
				}
				else
				{
					this.m_x = point.X;
					this.m_y = point.Y;
				}
				base.StartMoving(delay, speed);
				bool isEmpty2 = point.IsEmpty;
				if (isEmpty2)
				{
					this.m_syncAtTime = false;
					this.Die();
					bool flag2 = base.Game.CurrentLiving != this && base.Game.CurrentLiving is Player && this != null && base.Team != base.Game.CurrentLiving.Team;
					if (flag2)
					{
						Player player = base.Game.CurrentLiving as Player;
						player.PlayerDetail.OnKillingLiving(this.m_game, 1, base.Id, base.IsLiving, 0);
						base.Game.CurrentLiving.TotalKill++;
						player.CalculatePlayerOffer(this);
					}
				}
			}
		}

		// Token: 0x060076EA RID: 30442 RVA: 0x0027B918 File Offset: 0x00279B18
		public void StartSpeedMult(int x, int y, int delay)
		{
			bool flag = (base.FightBuffers.CardDoNotMoveLv1 > 0 || base.FightBuffers.CardDoNotMoveLv2 > 0 || base.FightBuffers.CardDoNotMoveLv3 > 0 || base.FightBuffers.CardDoNotMoveLv4 > 0) && (base.Game as PVEGame).Info.ID == 5;
			if (flag)
			{
				x = this.X;
			}
			Point point = new Point(x - this.X, y - this.Y);
			this.m_game.AddAction(new PlayerSpeedMultAction(this, new Point(this.X + point.X, this.Y + point.Y), delay));
		}

		// Token: 0x060076EB RID: 30443 RVA: 0x0027B9D4 File Offset: 0x00279BD4
		public void StartGhostMoving()
		{
			bool flag = !this.TargetPoint.IsEmpty;
			if (flag)
			{
				Point point = new Point(this.TargetPoint.X - this.X, this.TargetPoint.Y - this.Y);
				Point targetPoint = this.TargetPoint;
				bool flag2 = point.Length() > 160.0;
				if (flag2)
				{
					point.Normalize(160);
				}
				this.m_game.AddAction(new GhostMoveAction(this, new Point(this.X + point.X, this.Y + point.Y)));
			}
		}

		// Token: 0x060076EC RID: 30444 RVA: 0x0027BA84 File Offset: 0x00279C84
		public override void SetXY(int x, int y)
		{
			bool flag = this.m_x == x && this.m_y == y;
			if (!flag)
			{
				int num = (int)((double)Math.Abs(x - this.m_x) * ((double)this.PowerRatio / 100.0));
				this.m_x = x;
				this.m_y = y;
				bool isLiving = base.IsLiving;
				if (isLiving)
				{
					bool flag2 = this.m_game.IsPVE();
					if (flag2)
					{
						((PVEGame)this.m_game).OnMoving();
					}
					bool flag3 = !this.LimitEnergy;
					if (flag3)
					{
						this.ReduceEnergy(num);
						this.OnPlayerMoving();
					}
					bool flag4 = this.isLockEmery;
					if (flag4)
					{
						this.ReduceEnergy(0);
					}
				}
				else
				{
					bool flag5 = this.m_map == null;
					if (!flag5)
					{
						Rectangle rect = this.m_rect;
						rect.Offset(this.m_x, this.m_y);
						Physics[] array = this.m_map.FindPhysicalObjects(rect, this);
						Physics[] array2 = array;
						Physics[] array3 = array2;
						foreach (Physics physics in array3)
						{
							bool flag6 = physics is SimpleBox;
							if (flag6)
							{
								SimpleBox simpleBox = physics as SimpleBox;
								this.PickBox(simpleBox);
								this.OpenBox(simpleBox.Id);
							}
						}
					}
				}
			}
		}

		// Token: 0x060076ED RID: 30445 RVA: 0x0027BBE8 File Offset: 0x00279DE8
		public override void Die()
		{
			bool isLiving = base.IsLiving;
			if (isLiving)
			{
				this.m_y -= 70;
				base.Die();
			}
		}

		// Token: 0x060076EE RID: 30446 RVA: 0x0002BF63 File Offset: 0x0002A163
		public void StartRotate(int rotation, int speed, string endPlay, int delay)
		{
			this.m_game.AddAction(new LivingRotateTurnAction(this, rotation, speed, endPlay, delay));
		}

		// Token: 0x060076EF RID: 30447 RVA: 0x0002BF7D File Offset: 0x0002A17D
		public override void PickBox(SimpleBox box)
		{
			this.m_tempBoxes.Add(box);
			base.PickBox(box);
		}

		// Token: 0x060076F0 RID: 30448 RVA: 0x0027BC18 File Offset: 0x00279E18
		public void OpenBox(int boxId)
		{
			SimpleBox simpleBox = null;
			foreach (object obj in this.m_tempBoxes)
			{
				SimpleBox simpleBox2 = (SimpleBox)obj;
				bool flag = simpleBox2.Id == boxId;
				if (flag)
				{
					simpleBox = simpleBox2;
					break;
				}
			}
			bool flag2 = simpleBox == null || simpleBox.Item == null;
			if (!flag2)
			{
				ItemInfo item = simpleBox.Item;
				int templateID = item.TemplateID;
				int num = templateID;
				if (num != -300)
				{
					if (num != -200)
					{
						if (num != -100)
						{
							bool flag3 = item.Template.CategoryID == 10;
							if (flag3)
							{
								this.m_player.AddTemplate(item, eBageType.FightBag, item.Count);
							}
							else
							{
								this.m_player.AddTemplate(item, eBageType.TempBag, item.Count);
							}
						}
						else
						{
							this.m_player.AddGold(item.Count);
						}
					}
					else
					{
						this.m_player.AddMoney(item.Count);
						this.m_player.LogAddMoney(AddMoneyType.Box, AddMoneyType.Box_Open, this.m_player.PlayerCharacter.ID, item.Count, this.m_player.PlayerCharacter.Money);
					}
				}
				else
				{
					this.m_player.AddGiftToken(item.Count);
				}
				this.m_tempBoxes.Remove(simpleBox);
			}
		}

		// Token: 0x060076F1 RID: 30449 RVA: 0x0027BDA0 File Offset: 0x00279FA0
		public override void PrepareNewTurn()
		{
			bool currentIsHitTarget = this.CurrentIsHitTarget;
			if (currentIsHitTarget)
			{
				this.TotalHitTargetCount++;
			}
			this.m_energy = (int)this.Agility / 30 + 240;
			bool flag = base.FightBuffers.ConsortionAddEnergy > 0;
			if (flag)
			{
				this.m_energy += base.FightBuffers.ConsortionAddEnergy;
			}
			bool limitEnergy = this.LimitEnergy;
			if (limitEnergy)
			{
				this.m_energy = this.TotalCureEnergy;
			}
			this.m_shootCount = 1;
			this.m_ballCount = 1;
			this.AttackInformation = true;
			this.DefenceInformation = true;
			this.AttackEffectTrigger = false;
			this.DefenceEffectTrigger = false;
			base.PetEffects.CurrentUseSkill = 0;
			base.PetEffects.Delay = 500;
			base.SpecialSkillDelay = 0;
			this.EffectTrigger = false;
			this.PetEffectTrigger = false;
			base.PetEffects.DisibleActiveSkill = false;
			this.SetCurrentWeapon(this.PlayerDetail.MainWeapon);
			bool flag2 = this.UserPet != null;
			if (flag2)
			{
				this.UserPet.starFish_energy1++;
				this.UserPet.starFish_energy2++;
				this.OnPetEnergyUpdated(this.UserPet.starFish_energy1, this.UserPet.starFish_energy2);
			}
			bool flag3 = this.m_currentBall.ID != this.m_mainBallId;
			if (flag3)
			{
				this.m_currentBall = BallMgr.FindBall(this.m_mainBallId);
			}
			bool flag4 = !base.IsLiving;
			if (flag4)
			{
				this.SoulPropCount = 0;
				this.StartGhostMoving();
				this.TargetPoint = Point.Empty;
			}
			base.PrepareNewTurn();
		}

		// Token: 0x140000DE RID: 222
		// (add) Token: 0x060076F2 RID: 30450 RVA: 0x0027BF54 File Offset: 0x0027A154
		// (remove) Token: 0x060076F3 RID: 30451 RVA: 0x0027BF8C File Offset: 0x0027A18C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event Action<int, int> PetEnergyUpdated;

		// Token: 0x060076F4 RID: 30452 RVA: 0x0002BF95 File Offset: 0x0002A195
		protected virtual void OnPetEnergyUpdated(int energy1, int energy2)
		{
			Action<int, int> petEnergyUpdated = this.PetEnergyUpdated;
			if (petEnergyUpdated != null)
			{
				petEnergyUpdated(energy1, energy2);
			}
		}

		// Token: 0x060076F5 RID: 30453 RVA: 0x0027BFC4 File Offset: 0x0027A1C4
		public override void PrepareSelfTurn()
		{
			this.DefaultDelay = this.m_delay;
			this._flyCoolDown--;
			base.PetEffects.BallType = 0;
			bool flag = base.IsFrost || base.BlockTurn;
			if (flag)
			{
				base.AddDelay(this.GetTurnDelay());
			}
			bool flag2 = base.FightBuffers.CardTurnAddDanderLv1 > 0;
			if (flag2)
			{
				base.AddDander(base.FightBuffers.CardTurnAddDanderLv1);
			}
			bool flag3 = base.FightBuffers.CardTurnAddDanderLv2 > 0;
			if (flag3)
			{
				base.AddDander(base.FightBuffers.CardTurnAddDanderLv2);
			}
			bool flag4 = base.FightBuffers.CardTurnAddDanderLv3 > 0;
			if (flag4)
			{
				base.AddDander(base.FightBuffers.CardTurnAddDanderLv3);
			}
			bool flag5 = base.FightBuffers.CardTurnAddDanderLv4 > 0;
			if (flag5)
			{
				base.AddDander(base.FightBuffers.CardTurnAddDanderLv4);
			}
			this.m_game.SendRoundOneEnd(this);
			this.jaUsouSkill = false;
			base.PrepareSelfTurn();
			bool flag6 = this.m_pet == null;
			if (!flag6)
			{
				foreach (int num in this._petSkillCd.Keys)
				{
					bool flag7 = this._petSkillCd[num].Turn > 0;
					if (flag7)
					{
						PetSkillInfo petSkillInfo = this._petSkillCd[num];
						int turn = petSkillInfo.Turn;
						petSkillInfo.Turn = turn - 1;
					}
				}
			}
		}

		// Token: 0x060076F6 RID: 30454 RVA: 0x0027C16C File Offset: 0x0027A36C
		public override void CollidedByObject(Physics phy)
		{
			base.CollidedByObject(phy);
			bool flag = phy is SimpleBomb;
			if (flag)
			{
				this.OnCollidedByObject();
			}
		}

		// Token: 0x060076F7 RID: 30455 RVA: 0x0027C198 File Offset: 0x0027A398
		public override void StartAttacking()
		{
			bool flag = !base.IsAttacking;
			if (flag)
			{
				base.AddDelay(this.GetTurnDelay());
				base.StartAttacking();
			}
		}

		// Token: 0x060076F8 RID: 30456 RVA: 0x0027C1CC File Offset: 0x0027A3CC
		public override void Skip(int spendTime)
		{
			bool isAttacking = base.IsAttacking;
			if (isAttacking)
			{
				base.Game.SendSkipNext(this);
				this.m_prop.Clear();
				base.AddDelay(100);
				base.AddDander(40);
				base.AddPetMP(10);
				this.OnPlayerSkip();
				base.Skip(spendTime);
			}
		}

		// Token: 0x060076F9 RID: 30457 RVA: 0x0002BFAC File Offset: 0x0002A1AC
		public void SkipAttack()
		{
			base.Game.SendSkipNext(this);
			this.m_prop.Clear();
			base.AddDelay(100);
			base.Skip(1000);
		}

		// Token: 0x060076FA RID: 30458 RVA: 0x0027C22C File Offset: 0x0027A42C
		public void PrepareShoot(byte speedTime)
		{
			int turnWaitTime = this.m_game.GetTurnWaitTime();
			int num = (((int)speedTime > turnWaitTime) ? turnWaitTime : ((int)speedTime));
			base.AddDelay(num * 20);
			this.TotalShootCount++;
		}

		// Token: 0x060076FB RID: 30459 RVA: 0x0027C268 File Offset: 0x0027A468
		public bool Shoot(int x, int y, int force, int angle)
		{
			bool yhm_UseSkillPetWithProp = this.YHM_UseSkillPetWithProp;
			if (yhm_UseSkillPetWithProp)
			{
				this.m_shootCount = 1;
				this.m_ballCount = 1;
				this.YHM_UseSkillPetWithProp = false;
			}
			bool flag = this.m_shootCount > 0;
			if (flag)
			{
				this.EffectTrigger = false;
				this.OnPlayerShoot();
				int num = this.m_currentBall.ID;
				bool flag2 = !this.IsSpecialSkill && !this.IsBlackFlame;
				if (flag2)
				{
					this.buffs = new int[9];
					foreach (int num2 in this.Prop)
					{
						int num3 = num2;
						if (num3 != 10001)
						{
							if (num3 != 10004)
							{
								if (num3 == 10007)
								{
									this.buffs[7]++;
								}
							}
							else
							{
								this.buffs[4]++;
							}
						}
						else
						{
							this.buffs[1]++;
						}
					}
					bool flag3 = this.buffs[1] >= 2;
					if (flag3)
					{
						num = this.m_MultiBallId;
					}
					else
					{
						bool flag4 = this.buffs[4] >= 2;
						if (flag4)
						{
							num = this.m_AddWoundBallId;
						}
						else
						{
							bool flag5 = this.buffs[7] >= 4;
							if (flag5)
							{
								num = this.m_spsBallId;
							}
						}
					}
				}
				bool isSpecialSkill = this.IsSpecialSkill;
				if (isSpecialSkill)
				{
					this.ControlBall = false;
					base.SpecialSkillDelay = 2000;
				}
				bool flag6 = this.CurrentBall.ID != 1 && this.CurrentBall.ID != 64 && this.CurrentBall.ID != 3;
				if (flag6)
				{
					this.OnBeforePlayerShoot(this.CurrentBall.ID);
				}
				this.OnPlayerAnyShellThrow();
				bool flag7 = base.ShootImp(num, x, y, force, angle, this.m_ballCount, this.ShootCount);
				if (flag7)
				{
					bool flag8 = num == 4;
					if (flag8)
					{
						BaseGame game = this.m_game;
						eAcrobaciaType eAcrobaciaType = eAcrobaciaType.ExplosaoNucleardoSuperHomem;
						int idAcrobacias = this.IdAcrobacias;
						this.IdAcrobacias = idAcrobacias + 1;
						game.AddAction(new FightAchievementAction(this, eAcrobaciaType, idAcrobacias, 1200));
					}
					this.m_shootCount--;
					bool flag9 = this.m_shootCount <= 0 || !base.IsLiving;
					if (flag9)
					{
						this.StopAttacking();
						base.AddDelay(this.m_currentBall.Delay + this.m_weapon.Template.Property8);
						base.AddDander(40);
						base.AddPetMP(10);
						this.m_prop.Clear();
						bool canGetProp = this.CanGetProp;
						if (canGetProp)
						{
							SpecialItemDataInfo specialItemDataInfo = new SpecialItemDataInfo();
							List<ItemInfo> list = null;
							bool flag10 = DropInventory.FireDrop(this.m_game.RoomType, ref list) && list != null;
							if (flag10)
							{
								foreach (ItemInfo itemInfo in list)
								{
									ItemInfo.FindSpecialItemInfo(itemInfo, specialItemDataInfo);
									bool flag11 = itemInfo != null;
									if (flag11)
									{
										int templateID = itemInfo.TemplateID;
										this.PlayerDetail.AddTemplate(itemInfo, eBageType.FightBag, itemInfo.Count);
									}
								}
								this.PlayerDetail.AddGold(specialItemDataInfo.Gold);
								this.PlayerDetail.AddMoney(specialItemDataInfo.Money);
								this.PlayerDetail.LogAddMoney(AddMoneyType.Game, AddMoneyType.Game_Shoot, this.PlayerDetail.PlayerCharacter.ID, specialItemDataInfo.Money, this.PlayerDetail.PlayerCharacter.Money);
								this.PlayerDetail.AddGiftToken(specialItemDataInfo.GiftToken);
							}
						}
						this.OnPlayerCompleteShoot();
					}
					this.SendAttackInformation();
					this.OnAfterPlayerShoot();
					return true;
				}
			}
			return false;
		}

		// Token: 0x060076FC RID: 30460 RVA: 0x0002BFDD File Offset: 0x0002A1DD
		public void unlockProp(int templateid)
		{
			this.propsBloqueados.Remove(templateid);
		}

		// Token: 0x060076FD RID: 30461 RVA: 0x0027C67C File Offset: 0x0027A87C
		public void lockProp(int templateid)
		{
			bool flag = !this.propsBloqueados.Contains(templateid);
			if (flag)
			{
				this.propsBloqueados.Add(templateid);
			}
		}

		// Token: 0x060076FE RID: 30462 RVA: 0x0002BFED File Offset: 0x0002A1ED
		public void SetUltimateDisabled(bool disabled)
		{
			this._ultimateDisabled = disabled;
		}

		// Token: 0x060076FF RID: 30463 RVA: 0x0027C6AC File Offset: 0x0027A8AC
		public bool CanUseItem(ItemTemplateInfo item)
		{
			bool flag = item == null;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool isAutoBot = this.PlayerDetail.PlayerCharacter.IsAutoBot;
				if (isAutoBot)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = !base.IsLiving;
					if (flag3)
					{
						bool flag4 = base.PlaceProp < 0;
						if (flag4)
						{
							flag2 = base.psychic >= item.Property7 && this.SoulPropCount < this.MaxSoulPropUsedCount;
						}
						else
						{
							flag2 = this.SoulPropCount < this.MaxSoulPropUsedCount;
						}
					}
					else
					{
						bool flag5 = this.propsBloqueados.Contains(item.TemplateID);
						if (flag5)
						{
							flag2 = false;
						}
						else
						{
							bool flag6 = !base.IsAttacking;
							if (flag6)
							{
								bool flag7 = !base.IsLiving && base.Team == this.m_game.CurrentLiving.Team;
								flag2 = flag7 && this.IsActive;
							}
							else
							{
								flag2 = this.m_energy >= item.Property4 && (base.IsAttacking || (!base.IsLiving && base.Team == this.m_game.CurrentLiving.Team));
							}
						}
					}
				}
			}
			return flag2;
		}

		// Token: 0x06007700 RID: 30464 RVA: 0x0027C7E8 File Offset: 0x0027A9E8
		public bool CanUseUltimate()
		{
			bool ultimateDisabled = this._ultimateDisabled;
			bool flag;
			if (ultimateDisabled)
			{
				flag = false;
			}
			else
			{
				int num = 100;
				flag = this.m_energy >= num;
			}
			return flag;
		}

		// Token: 0x06007701 RID: 30465 RVA: 0x0027C818 File Offset: 0x0027AA18
		public bool UseUltimate()
		{
			bool flag = this.CanUseUltimate();
			bool flag2;
			if (flag)
			{
				this.m_energy -= 100;
				flag2 = true;
			}
			else
			{
				flag2 = false;
			}
			return flag2;
		}

		// Token: 0x06007702 RID: 30466 RVA: 0x0002BFF7 File Offset: 0x0002A1F7
		public void SetItemsDisabled(bool disabled)
		{
			this._itemsDisabled = disabled;
		}

		// Token: 0x06007703 RID: 30467 RVA: 0x0027C84C File Offset: 0x0027AA4C
		public bool UseItem(ItemTemplateInfo item)
		{
			bool itemsDisabled = this._itemsDisabled;
			bool flag;
			if (itemsDisabled)
			{
				flag = false;
			}
			else
			{
				bool flag2 = this.CanUseItem(item);
				if (flag2)
				{
					bool isLiving = base.IsLiving;
					if (isLiving)
					{
						this.m_energy -= item.Property4;
						bool limitEnergy = this.LimitEnergy;
						if (limitEnergy)
						{
							this.TotalCureEnergy -= item.Property4;
						}
						this.m_delay += item.Property5;
					}
					else
					{
						bool flag3 = base.PlaceProp < 0;
						if (flag3)
						{
							base.psychic -= item.Property7;
						}
						bool flag4 = base.Game.CurrentLiving != null;
						if (flag4)
						{
							base.Game.CurrentLiving.AddDelay(item.Property5);
						}
						this.SoulPropCount++;
					}
					this.m_game.SendPlayerUseProp(this, -2, -2, item.TemplateID, this);
					SpellMgr.ExecuteSpell(this.m_game, this, item);
					this.Prop.Add(item.TemplateID);
					base.OnBeginUseProp();
					flag = true;
				}
				else
				{
					flag = false;
				}
			}
			return flag;
		}

		// Token: 0x06007704 RID: 30468 RVA: 0x0027C97C File Offset: 0x0027AB7C
		public void UseFlySkill()
		{
			bool flag = this._flyCoolDown <= 0;
			if (flag)
			{
				this.m_game.SendPlayerUseProp(this, -2, -2, Player.CARRY_TEMPLATE_ID);
				this.SetBall(3);
				this._flyCoolDown = 2;
				this.m_energy -= 150;
			}
		}

		// Token: 0x06007705 RID: 30469 RVA: 0x0027C9D4 File Offset: 0x0027ABD4
		public void UseSecondWeapon()
		{
			bool flag = this.m_DeputyWeapon == null || !this.CanUseItem(this.m_DeputyWeapon.Template);
			if (!flag)
			{
				bool flag2 = this.m_DeputyWeapon.Template.Property3 == 31;
				if (flag2)
				{
					bool flag3 = false;
					bool flag4 = new List<int> { 17006, 17012, 17013 }.Contains(this.m_DeputyWeapon.TemplateID);
					if (flag4)
					{
						flag3 = true;
					}
					new AddGuardEquipEffect((int)base.GetHertAddition(this.m_DeputyWeapon), 1, flag3).Start(this);
					this.OnPlayerGuard();
				}
				else
				{
					this.SetCurrentWeapon(this.m_DeputyWeapon);
					this.OnPlayerCure();
				}
				this.ShootCount = 1;
				this.m_energy -= this.m_DeputyWeapon.Template.Property4;
				this.m_delay += this.m_DeputyWeapon.Template.Property5;
				this.m_game.SendPlayerUseProp(this, -2, -2, this.m_DeputyWeapon.Template.TemplateID);
				bool flag5 = this.deputyWeaponResCount > 0;
				if (flag5)
				{
					this.deputyWeaponResCount--;
					this.m_game.SendUseDeputyWeapon(this, this.deputyWeaponResCount);
				}
				this.OnPlayerUseSecondWeapon(this.m_DeputyWeapon.Template.Property3);
			}
		}

		// Token: 0x06007706 RID: 30470 RVA: 0x0027CB50 File Offset: 0x0027AD50
		public void DeadLink()
		{
			this.m_isActive = false;
			bool isLiving = base.IsLiving;
			if (isLiving)
			{
				this.Die();
			}
		}

		// Token: 0x06007707 RID: 30471 RVA: 0x0027CB78 File Offset: 0x0027AD78
		public bool CheckShootPoint(int x, int y)
		{
			bool flag = Math.Abs(this.X - x) > 100;
			bool flag2;
			if (flag)
			{
				string userName = this.m_player.PlayerCharacter.UserName;
				string nickName = this.m_player.PlayerCharacter.NickName;
				this.m_player.Disconnect();
				flag2 = false;
			}
			else
			{
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x06007708 RID: 30472 RVA: 0x0027CBD4 File Offset: 0x0027ADD4
		public void SendAttackInformation()
		{
			bool flag = this.AttackEffectTrigger && this.AttackInformation;
			if (flag)
			{
				base.Game.SendMessage(this.PlayerDetail, LanguageMgr.GetTranslation("PlayerEquipEffect.Success", Array.Empty<object>()), LanguageMgr.GetTranslation("PlayerEquipEffect.Success1", new object[] { this.PlayerDetail.PlayerCharacter.NickName }), 3);
				this.AttackEffectTrigger = false;
				this.AttackInformation = false;
			}
		}

		// Token: 0x06007709 RID: 30473 RVA: 0x0027CC4C File Offset: 0x0027AE4C
		public override void OnAfterKillingLiving(Living target, int damageAmount, int criticalAmount)
		{
			base.OnAfterKillingLiving(target, damageAmount, criticalAmount);
			bool flag = target is Player;
			if (flag)
			{
				this.m_player.OnKillingLiving(this.m_game, 1, target.Id, target.IsLiving, damageAmount + criticalAmount);
				this.CalculatePlayerOffer(target as Player);
			}
			else
			{
				int num = 0;
				bool flag2 = target is SimpleBoss;
				if (flag2)
				{
					SimpleBoss simpleBoss = target as SimpleBoss;
					num = simpleBoss.NpcInfo.ID;
				}
				bool flag3 = target is SimpleNpc;
				if (flag3)
				{
					SimpleNpc simpleNpc = target as SimpleNpc;
					num = simpleNpc.NpcInfo.ID;
				}
				this.m_player.OnKillingLiving(this.m_game, 2, num, target.IsLiving, damageAmount + criticalAmount);
			}
			bool flag4 = base.FightBuffers.CardAddDanderLv1 > 0;
			if (flag4)
			{
				base.AddDander(base.FightBuffers.CardAddDanderLv1);
			}
			bool flag5 = base.FightBuffers.CardAddDanderLv2 > 0;
			if (flag5)
			{
				base.AddDander(base.FightBuffers.CardAddDanderLv2);
			}
			bool flag6 = base.FightBuffers.CardAddDanderLv3 > 0;
			if (flag6)
			{
				base.AddDander(base.FightBuffers.CardAddDanderLv3);
			}
			bool flag7 = base.FightBuffers.CardAddDanderLv4 > 0;
			if (flag7)
			{
				base.AddDander(base.FightBuffers.CardAddDanderLv4);
			}
		}

		// Token: 0x0600770A RID: 30474 RVA: 0x0027CDB0 File Offset: 0x0027AFB0
		protected void OnLoadingCompleted()
		{
			bool flag = this.LoadingCompleted != null;
			if (flag)
			{
				this.LoadingCompleted(this);
			}
		}

		// Token: 0x0600770B RID: 30475 RVA: 0x0027CDDC File Offset: 0x0027AFDC
		public void OnPlayerShoot()
		{
			bool flag = this.PlayerShoot != null;
			if (flag)
			{
				this.PlayerShoot(this);
			}
		}

		// Token: 0x0600770C RID: 30476 RVA: 0x0027CE08 File Offset: 0x0027B008
		public void OnPlayerSkip()
		{
			bool flag = this.PlayerSkip != null;
			if (flag)
			{
				this.PlayerSkip(this);
			}
		}

		// Token: 0x0600770D RID: 30477 RVA: 0x0027CE34 File Offset: 0x0027B034
		public void OnPlayerAfterBuffSkillPet()
		{
			bool flag = this.PlayerAfterBuffSkillPet != null;
			if (flag)
			{
				this.PlayerAfterBuffSkillPet(this);
			}
		}

		// Token: 0x0600770E RID: 30478 RVA: 0x0027CE60 File Offset: 0x0027B060
		public void OnPlayerBuffSkill()
		{
			bool flag = this.PlayerBuffSkill != null;
			if (flag)
			{
				this.PlayerBuffSkill(this);
			}
		}

		// Token: 0x0600770F RID: 30479 RVA: 0x0027CE8C File Offset: 0x0027B08C
		public void OnPlayerAnyShellThrow()
		{
			bool flag = this.PlayerAnyShellThrow != null;
			if (flag)
			{
				this.PlayerAnyShellThrow(this);
			}
		}

		// Token: 0x06007710 RID: 30480 RVA: 0x0027CEB8 File Offset: 0x0027B0B8
		public void OnPlayerShootCure()
		{
			bool flag = this.PlayerShootCure != null;
			if (flag)
			{
				this.PlayerShootCure(this);
			}
		}

		// Token: 0x06007711 RID: 30481 RVA: 0x0027CEE4 File Offset: 0x0027B0E4
		protected void OnPlayerMoving()
		{
			bool flag = this.PlayerBeginMoving != null;
			if (flag)
			{
				this.PlayerBeginMoving(this);
			}
		}

		// Token: 0x06007712 RID: 30482 RVA: 0x0027CF10 File Offset: 0x0027B110
		protected void OnBeforePlayerShoot(int ball)
		{
			bool flag = this.BeforePlayerShoot != null;
			if (flag)
			{
				this.BeforePlayerShoot(this, ball);
			}
		}

		// Token: 0x06007713 RID: 30483 RVA: 0x0027CF3C File Offset: 0x0027B13C
		protected void OnAfterPlayerShoot()
		{
			bool flag = this.AfterPlayerShooted != null;
			if (flag)
			{
				this.AfterPlayerShooted(this);
			}
		}

		// Token: 0x06007714 RID: 30484 RVA: 0x0027CF68 File Offset: 0x0027B168
		protected void OnPlayerCompleteShoot()
		{
			bool flag = this.PlayerCompleteShoot != null;
			if (flag)
			{
				this.PlayerCompleteShoot(this);
			}
		}

		// Token: 0x06007715 RID: 30485 RVA: 0x0027CF94 File Offset: 0x0027B194
		public void OnPlayerGuard()
		{
			bool flag = this.PlayerGuard != null;
			if (flag)
			{
				this.PlayerGuard(this);
			}
		}

		// Token: 0x06007716 RID: 30486 RVA: 0x0027CFC0 File Offset: 0x0027B1C0
		public void OnPlayerCure()
		{
			bool flag = this.PlayerCure != null;
			if (flag)
			{
				this.PlayerCure(this);
			}
		}

		// Token: 0x06007717 RID: 30487 RVA: 0x0027CFEC File Offset: 0x0027B1EC
		public void OnPlayerBuffSkillPet()
		{
			this.jaUsouSkill = true;
			bool flag = this.PlayerBuffSkillPet != null;
			if (flag)
			{
				this.PlayerBuffSkillPet(this);
			}
		}

		// Token: 0x06007718 RID: 30488 RVA: 0x0027D020 File Offset: 0x0027B220
		public void OnPlayerClearBuffSkillPet()
		{
			bool flag = this.PlayerClearBuffSkillPet != null;
			if (flag)
			{
				this.PlayerClearBuffSkillPet(this);
			}
		}

		// Token: 0x06007719 RID: 30489 RVA: 0x0027D04C File Offset: 0x0027B24C
		protected void OnCollidedByObject()
		{
			bool flag = this.CollidByObject != null;
			if (flag)
			{
				this.CollidByObject(this);
			}
		}

		// Token: 0x0600771A RID: 30490 RVA: 0x0027D078 File Offset: 0x0027B278
		public void OnPlayerUseDander()
		{
			bool flag = this.PlayerUseDander != null;
			if (flag)
			{
				this.PlayerUseDander(this);
			}
		}

		// Token: 0x0600771B RID: 30491 RVA: 0x0027D0A4 File Offset: 0x0027B2A4
		public void OnPlayerBeforeReset()
		{
			bool flag = this.PlayerBeforeReset != null;
			if (flag)
			{
				this.PlayerBeforeReset(this);
			}
		}

		// Token: 0x0600771C RID: 30492 RVA: 0x0027D0D0 File Offset: 0x0027B2D0
		public void OnPlayerAfterReset()
		{
			bool flag = this.PlayerAfterReset != null;
			if (flag)
			{
				this.PlayerAfterReset(this);
			}
		}

		// Token: 0x0600771D RID: 30493 RVA: 0x0027D0FC File Offset: 0x0027B2FC
		public void OnPlayerUseSecondWeapon(int type)
		{
			bool flag = this.PlayerUseSecondWeapon != null;
			if (flag)
			{
				this.PlayerUseSecondWeapon(this, type);
			}
		}

		// Token: 0x0600771E RID: 30494 RVA: 0x0027D128 File Offset: 0x0027B328
		public void OnPlayerUsePetMP(int value)
		{
			bool flag = this.PlayerUsePetMP != null;
			if (flag)
			{
				this.PlayerUsePetMP(this, value);
			}
		}

		// Token: 0x0600771F RID: 30495 RVA: 0x0027D154 File Offset: 0x0027B354
		public bool IsSkillPet(int skillID)
		{
			if (skillID <= 126)
			{
				if (skillID - 25 > 11 && skillID - 103 > 2 && skillID - 124 > 2)
				{
					goto IL_0062;
				}
			}
			else if (skillID <= 162)
			{
				if (skillID - 143 > 2 && skillID - 160 > 2)
				{
					goto IL_0062;
				}
			}
			else if (skillID - 178 > 2 && skillID - 195 > 2)
			{
				goto IL_0062;
			}
			return true;
			IL_0062:
			return false;
		}

		// Token: 0x06007720 RID: 30496 RVA: 0x0027D1C8 File Offset: 0x0027B3C8
		public void PetUseKill(int skillId)
		{
			bool flag = !this.CanUsePetSkill || !this.PetSkillCD.ContainsKey(skillId) || base.PetEffects.DisibleActiveSkill || this.jaUsouSkill;
			if (!flag)
			{
				PetSkillInfo petSkillInfo = this._petSkillCd[skillId];
				bool flag2 = petSkillInfo.CostFlag == 0;
				if (flag2)
				{
					bool flag3 = base.PetMP > 0 && base.PetMP >= petSkillInfo.CostMP;
					if (flag3)
					{
						bool flag4 = petSkillInfo.Turn > 0;
						if (flag4)
						{
							this.m_player.SendMessage(LanguageMgr.GetTranslation("PlayerPetColdDownNotEnough", Array.Empty<object>()));
						}
						else
						{
							bool flag5 = petSkillInfo.NewBallID != -1;
							if (flag5)
							{
								base.PetEffects.Delay += petSkillInfo.Delay;
								this.SetBall(petSkillInfo.NewBallID);
							}
							base.PetMP -= petSkillInfo.CostMP;
							this.OnPlayerUsePetMP(petSkillInfo.CostMP);
							base.PetEffects.CurrentUseSkill = skillId;
							base.PetEffects.BallType = petSkillInfo.BallType;
							this.OnPlayerBuffSkillPet();
							this.m_game.SendPetUseKill(this);
							petSkillInfo.Turn = petSkillInfo.ColdDown + 1;
							this.OnPlayerAfterBuffSkillPet();
							this.OnPlayerBuffSkill();
						}
					}
					else
					{
						this.m_player.SendMessage(LanguageMgr.GetTranslation("PlayerPetMpNotEnough", Array.Empty<object>()));
					}
				}
				else
				{
					bool flag6 = base.PetFlag > 0 && base.PetFlag >= petSkillInfo.CostFlag;
					if (flag6)
					{
						bool flag7 = petSkillInfo.Turn > 0;
						if (flag7)
						{
							this.m_player.SendMessage(LanguageMgr.GetTranslation("PlayerPetColdDownNotEnough", Array.Empty<object>()));
						}
						else
						{
							bool flag8 = petSkillInfo.NewBallID != -1;
							if (flag8)
							{
								base.PetEffects.Delay += petSkillInfo.Delay;
								this.SetBall(petSkillInfo.NewBallID);
							}
							base.PetFlag -= petSkillInfo.CostFlag;
							base.PetEffects.CurrentUseSkill = skillId;
							base.PetEffects.BallType = petSkillInfo.BallType;
							this.OnPlayerBuffSkillPet();
							this.m_game.SendPetUseKill(this);
							petSkillInfo.Turn = petSkillInfo.ColdDown + 1;
							this.OnPlayerAfterBuffSkillPet();
						}
					}
					else
					{
						this.m_player.SendMessage(LanguageMgr.GetTranslation("PlayerPetMpNotEnough", Array.Empty<object>()));
					}
				}
			}
		}

		// Token: 0x06007721 RID: 30497 RVA: 0x0002C001 File Offset: 0x0002A201
		public void SendMessage(string message)
		{
			Console.WriteLine("消息发送给玩家 " + this.PlayerDetail.PlayerCharacter.NickName + ": " + message);
		}

		// Token: 0x06007722 RID: 30498 RVA: 0x0027D45C File Offset: 0x0027B65C
		public void ResetSkillCd()
		{
			bool flag = this.m_pet == null;
			if (!flag)
			{
				string[] array = this.m_pet.SkillEquip.Split(new char[] { '|' });
				string[] array2 = array;
				string[] array3 = array2;
				foreach (string text in array3)
				{
					int num = int.Parse(text.Split(new char[] { ',' })[0]);
					bool flag2 = this._petSkillCd.ContainsKey(num);
					if (flag2)
					{
						this._petSkillCd[num].Turn = this._petSkillCd[num].ColdDown;
					}
				}
			}
		}

		// Token: 0x06007723 RID: 30499 RVA: 0x0027D514 File Offset: 0x0027B714
		public bool IsCure()
		{
			int templateID = this.Weapon.TemplateID;
			int num = templateID;
			switch (num)
			{
			case 17000:
			case 17001:
			case 17002:
			case 17005:
			case 17007:
			case 17010:
			case 17011:
			case 17015:
			case 17017:
			case 17018:
			case 17019:
			case 17020:
				break;
			case 17003:
			case 17004:
			case 17006:
			case 17008:
			case 17009:
			case 17012:
			case 17016:
			case 17021:
			case 17022:
			case 17023:
				goto IL_009B;
			case 17013:
			case 17014:
				goto IL_009F;
			default:
				switch (num)
				{
				case 17100:
				case 17102:
					break;
				case 17101:
					goto IL_009B;
				default:
					goto IL_009F;
				}
				break;
			}
			return true;
			IL_009B:
			return false;
			IL_009F:
			return false;
		}

		// Token: 0x0400458B RID: 17803
		private IGamePlayer m_player;

		// Token: 0x0400458C RID: 17804
		private bool m_isActive;

		// Token: 0x0400458D RID: 17805
		private ItemInfo m_weapon;

		// Token: 0x0400458E RID: 17806
		public ItemInfo m_DeputyWeapon;

		// Token: 0x0400458F RID: 17807
		private int m_mainBallId;

		// Token: 0x04004590 RID: 17808
		private int m_spBallId;

		// Token: 0x04004591 RID: 17809
		private int m_spsBallId;

		// Token: 0x04004592 RID: 17810
		private int m_AddWoundBallId;

		// Token: 0x04004593 RID: 17811
		public bool CanMove;

		// Token: 0x04004594 RID: 17812
		private int m_MultiBallId;

		// Token: 0x04004595 RID: 17813
		private BallInfo m_currentBall;

		// Token: 0x04004596 RID: 17814
		private int m_changeSpecialball;

		// Token: 0x04004597 RID: 17815
		private int m_energy;

		// Token: 0x04004598 RID: 17816
		private List<int> m_prop;

		// Token: 0x04004599 RID: 17817
		public List<int> ListObject = new List<int>();

		// Token: 0x0400459A RID: 17818
		private int m_ratioPower;

		// Token: 0x0400459B RID: 17819
		public Point TargetPoint;

		// Token: 0x0400459C RID: 17820
		public int GainGP;

		// Token: 0x0400459D RID: 17821
		public int GainOffer;

		// Token: 0x0400459E RID: 17822
		public bool LockDirection = false;

		// Token: 0x0400459F RID: 17823
		public bool isLockEmery = false;

		// Token: 0x040045A0 RID: 17824
		public bool isLockXY = false;

		// Token: 0x040045A1 RID: 17825
		private bool m_canGetProp;

		// Token: 0x040045A2 RID: 17826
		private List<int> propsBloqueados;

		// Token: 0x040045A3 RID: 17827
		public int TotalAllHurt;

		// Token: 0x040045A4 RID: 17828
		public int TotalAllHitTargetCount;

		// Token: 0x040045A5 RID: 17829
		public int TotalAllShootCount;

		// Token: 0x040045A6 RID: 17830
		public int TotalAllKill;

		// Token: 0x040045A7 RID: 17831
		public int TotalAllExperience;

		// Token: 0x040045A8 RID: 17832
		public int TotalAllScore;

		// Token: 0x040045A9 RID: 17833
		public int TotalAllCure;

		// Token: 0x040045AA RID: 17834
		public int CanTakeOut;

		// Token: 0x040045AB RID: 17835
		public bool FinishTakeCard;

		// Token: 0x040045AC RID: 17836
		public bool HasPaymentTakeCard;

		// Token: 0x040045AD RID: 17837
		public int BossCardCount;

		// Token: 0x040045AE RID: 17838
		public bool Ready;

		// Token: 0x040045AF RID: 17839
		public bool AttackInformation;

		// Token: 0x040045B0 RID: 17840
		public bool DefenceInformation;

		// Token: 0x040045B1 RID: 17841
		public int TotalCureEnergy;

		// Token: 0x040045B2 RID: 17842
		public bool LimitEnergy;

		// Token: 0x040045B3 RID: 17843
		public bool CanUsePetSkill;

		// Token: 0x040045B4 RID: 17844
		public int MaxSoulPropUsedCount = 2;

		// Token: 0x040045B5 RID: 17845
		public int SoulPropCount;

		// Token: 0x040045B6 RID: 17846
		public bool IsShadown;

		// Token: 0x040045B7 RID: 17847
		private Dictionary<int, PetSkillInfo> _petSkillCd;

		// Token: 0x040045B8 RID: 17848
		private UserPetInfo m_pet;

		// Token: 0x040045B9 RID: 17849
		private UserBufferInfo m_bufferPoint;

		// Token: 0x040045BA RID: 17850
		private int m_killedPunishmentOffer;

		// Token: 0x040045BB RID: 17851
		private int m_loadingProcess;

		// Token: 0x040045BC RID: 17852
		private int m_shootCount;

		// Token: 0x040045BD RID: 17853
		private int m_ballCount;

		// Token: 0x040045BE RID: 17854
		private ArrayList m_tempBoxes = new ArrayList();

		// Token: 0x040045BF RID: 17855
		private int[] buffs;

		// Token: 0x040045C2 RID: 17858
		private static readonly int CARRY_TEMPLATE_ID = 10016;

		// Token: 0x040045C5 RID: 17861
		private int _flyCoolDown;

		// Token: 0x040045C6 RID: 17862
		public int deputyWeaponResCount = 0;

		// Token: 0x040045E6 RID: 17894
		private bool _ultimateDisabled = false;

		// Token: 0x040045E7 RID: 17895
		private bool _itemsDisabled = false;

		// Token: 0x02000CDC RID: 3292
		// (Invoke) Token: 0x06007726 RID: 30502
		public delegate void PlayerUsePetMPEventHandle(Player player, int value);
	}
}
