﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EE1 RID: 3809
	public class BasePlayerEffect : AbstractEffect
	{
		// Token: 0x060082F2 RID: 33522 RVA: 0x00033DBA File Offset: 0x00031FBA
		public BasePlayerEffect(eEffectType type)
			: base(type)
		{
		}

		// Token: 0x060082F3 RID: 33523 RVA: 0x002B2398 File Offset: 0x002B0598
		public override bool Start(Living living)
		{
			return living is Player && base.Start(living);
		}

		// Token: 0x060082F4 RID: 33524 RVA: 0x002B23BC File Offset: 0x002B05BC
		public sealed override void OnAttached(Living living)
		{
			bool flag = living is Player;
			if (flag)
			{
				this.OnAttachedToPlayer(living as Player);
			}
		}

		// Token: 0x060082F5 RID: 33525 RVA: 0x002B23E8 File Offset: 0x002B05E8
		public sealed override void OnRemoved(Living living)
		{
			bool flag = living is Player;
			if (flag)
			{
				this.OnRemovedFromPlayer(living as Player);
			}
		}

		// Token: 0x060082F6 RID: 33526 RVA: 0x00005683 File Offset: 0x00003883
		protected virtual void OnAttachedToPlayer(Player player)
		{
		}

		// Token: 0x060082F7 RID: 33527 RVA: 0x00005683 File Offset: 0x00003883
		protected virtual void OnRemovedFromPlayer(Player player)
		{
		}
	}
}
