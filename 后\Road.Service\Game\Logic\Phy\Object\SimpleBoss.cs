﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Reflection;
using Game.Logic.Actions;
using Game.Logic.AI;
using Game.Logic.AI.Npc;
using Game.Server.Managers;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic.Phy.Object
{
	// Token: 0x02000CDE RID: 3294
	public class SimpleBoss : TurnedLiving
	{
		// Token: 0x17001478 RID: 5240
		// (get) Token: 0x0600773B RID: 30523 RVA: 0x0002C0B5 File Offset: 0x0002A2B5
		public new NpcInfo NpcInfo
		{
			get
			{
				return this.m_npcInfo;
			}
		}

		// Token: 0x17001479 RID: 5241
		// (get) Token: 0x0600773C RID: 30524 RVA: 0x0002C0BD File Offset: 0x0002A2BD
		public List<SimpleBoss> Boss
		{
			get
			{
				return this.m_boss;
			}
		}

		// Token: 0x1700147A RID: 5242
		// (get) Token: 0x0600773D RID: 30525 RVA: 0x0002C0C5 File Offset: 0x0002A2C5
		public List<SimpleNpc> Child
		{
			get
			{
				return this.m_child;
			}
		}

		// Token: 0x1700147B RID: 5243
		// (get) Token: 0x0600773E RID: 30526 RVA: 0x0027F824 File Offset: 0x0027DA24
		public int CurrentLivingNpcNum
		{
			get
			{
				int num = 0;
				foreach (SimpleNpc simpleNpc in this.Child)
				{
					bool flag = !simpleNpc.IsLiving;
					if (flag)
					{
						num++;
					}
				}
				return this.Child.Count - num;
			}
		}

		// Token: 0x1700147C RID: 5244
		// (get) Token: 0x0600773F RID: 30527 RVA: 0x0027F8A0 File Offset: 0x0027DAA0
		public int CurrentLivingBossNum
		{
			get
			{
				int num = 0;
				foreach (SimpleBoss simpleBoss in this.Boss)
				{
					bool flag = !simpleBoss.IsLiving;
					if (flag)
					{
						num++;
					}
				}
				return this.Boss.Count - num;
			}
		}

		// Token: 0x140000DF RID: 223
		// (add) Token: 0x06007740 RID: 30528 RVA: 0x0027F91C File Offset: 0x0027DB1C
		// (remove) Token: 0x06007741 RID: 30529 RVA: 0x0027F954 File Offset: 0x0027DB54
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event SimpleBoss.SimpleBossShootedEventHanld SimpleBossShooted;

		// Token: 0x06007742 RID: 30530 RVA: 0x0027F98C File Offset: 0x0027DB8C
		public SimpleBoss(int id, BaseGame game, NpcInfo npcInfo, int direction, int type, string actions)
			: base(id, game, npcInfo.Camp, npcInfo.Name, npcInfo.ModelID, npcInfo.Blood, npcInfo.Immunity, direction)
		{
			bool flag = type == 0;
			if (flag)
			{
				base.Type = eLivingType.SimpleBoss;
			}
			bool flag2 = type == 1;
			if (flag2)
			{
				base.Type = eLivingType.SimpleBossNormal;
			}
			bool flag3 = type == 2;
			if (flag3)
			{
				base.Type = eLivingType.SimpleBossHard;
			}
			bool flag4 = type == 3;
			if (flag4)
			{
				base.Type = eLivingType.SimpleLongNpc;
			}
			base.ActionStr = actions;
			this.m_mostHateful = new Dictionary<Player, int>();
			this.m_npcInfo = npcInfo;
			this.m_ai = ScriptMgr.CreateInstance(npcInfo.Script) as ABrain;
			bool flag5 = this.m_ai == null;
			if (flag5)
			{
				SimpleBoss.log.ErrorFormat("Can't create abrain :{0}", npcInfo.Script);
				this.m_ai = SimpleBrain.Simple;
			}
			this.m_ai.Game = this.m_game;
			this.m_ai.Body = this;
			this.SimpleBossShooted = (SimpleBoss.SimpleBossShootedEventHanld)Delegate.Combine(this.SimpleBossShooted, new SimpleBoss.SimpleBossShootedEventHanld(this.ShootedSay));
			try
			{
				this.m_ai.OnCreated();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss Created error:{1}", ex);
			}
		}

		// Token: 0x06007743 RID: 30531 RVA: 0x0027FB04 File Offset: 0x0027DD04
		public override void Reset()
		{
			bool isWorldBoss = base.Config.IsWorldBoss;
			if (isWorldBoss)
			{
				this.m_maxBlood = int.MaxValue;
			}
			else
			{
				this.m_maxBlood = this.m_npcInfo.Blood;
			}
			this.BaseDamage = (double)this.m_npcInfo.BaseDamage;
			this.BaseGuard = (double)this.m_npcInfo.BaseGuard;
			this.Attack = (double)this.m_npcInfo.Attack;
			this.Defence = (double)this.m_npcInfo.Defence;
			this.Agility = (double)this.m_npcInfo.Agility;
			this.Lucky = (double)this.m_npcInfo.Lucky;
			this.Grade = this.m_npcInfo.Level;
			this.Experience = this.m_npcInfo.Experience;
			bool flag = this.m_direction == 1;
			if (flag)
			{
				base.ReSetRectWithDir();
			}
			base.FireX = this.NpcInfo.FireX;
			base.FireY = this.NpcInfo.FireY;
			base.SetRect(this.m_npcInfo.X, this.m_npcInfo.Y, this.m_npcInfo.Width, this.m_npcInfo.Height);
			base.SetRelateDemagemRect(this.m_npcInfo.X, this.m_npcInfo.Y, this.m_npcInfo.Width, this.m_npcInfo.Height);
			base.Reset();
		}

		// Token: 0x06007744 RID: 30532 RVA: 0x0027FC78 File Offset: 0x0027DE78
		public List<SimpleNpc> FindChildLiving(int npcId)
		{
			List<SimpleNpc> list = new List<SimpleNpc>();
			foreach (SimpleNpc simpleNpc in this.m_child)
			{
				bool flag = simpleNpc != null && simpleNpc.IsLiving && simpleNpc.NpcInfo.ID == npcId;
				if (flag)
				{
					list.Add(simpleNpc);
				}
			}
			return list;
		}

		// Token: 0x06007745 RID: 30533 RVA: 0x0027FD00 File Offset: 0x0027DF00
		public List<SimpleNpc> FindChildLivings()
		{
			List<SimpleNpc> list = new List<SimpleNpc>();
			List<SimpleNpc> child = this.m_child;
			lock (child)
			{
				foreach (SimpleNpc simpleNpc in this.m_child)
				{
					bool flag2 = simpleNpc != null && simpleNpc.IsLiving;
					if (flag2)
					{
						list.Add(simpleNpc);
					}
				}
			}
			return list;
		}

		// Token: 0x06007746 RID: 30534 RVA: 0x0027FDAC File Offset: 0x0027DFAC
		public void RemoveAllChild()
		{
			foreach (SimpleNpc simpleNpc in this.Child)
			{
				bool isLiving = simpleNpc.IsLiving;
				if (isLiving)
				{
					simpleNpc.Die();
				}
			}
			this.m_child = new List<SimpleNpc>();
		}

		// Token: 0x06007747 RID: 30535 RVA: 0x0027FE1C File Offset: 0x0027E01C
		public void ClearDiedLiving()
		{
			List<SimpleNpc> list = new List<SimpleNpc>();
			List<SimpleNpc> child = this.m_child;
			lock (child)
			{
				foreach (SimpleNpc simpleNpc in this.m_child)
				{
					bool flag2 = !simpleNpc.IsLiving;
					if (flag2)
					{
						list.Add(simpleNpc);
					}
				}
				foreach (SimpleNpc simpleNpc2 in list)
				{
					this.m_child.Remove(simpleNpc2);
				}
			}
		}

		// Token: 0x06007748 RID: 30536 RVA: 0x0027FF08 File Offset: 0x0027E108
		public override bool TakeDamage(Living source, ref int damageAmount, ref int criticalAmount, string msg)
		{
			bool flag = base.TakeDamage(source, ref damageAmount, ref criticalAmount, msg);
			bool flag2 = source is Player;
			if (flag2)
			{
				Player player = source as Player;
				int num = damageAmount + criticalAmount;
				bool flag3 = this.m_mostHateful.ContainsKey(player);
				if (flag3)
				{
					Dictionary<Player, int> mostHateful = this.m_mostHateful;
					Player player2 = player;
					mostHateful[player2] += num;
				}
				else
				{
					this.m_mostHateful.Add(player, num);
				}
			}
			return flag;
		}

		// Token: 0x06007749 RID: 30537 RVA: 0x0027FF90 File Offset: 0x0027E190
		public Player FindMostHatefulPlayer()
		{
			bool flag = this.m_mostHateful.Count > 0;
			Player player;
			if (flag)
			{
				KeyValuePair<Player, int> keyValuePair = this.m_mostHateful.ElementAt(0);
				foreach (KeyValuePair<Player, int> keyValuePair2 in this.m_mostHateful)
				{
					bool flag2 = keyValuePair.Value < keyValuePair2.Value;
					if (flag2)
					{
						keyValuePair = keyValuePair2;
					}
				}
				player = keyValuePair.Key;
			}
			else
			{
				player = null;
			}
			return player;
		}

		// Token: 0x0600774A RID: 30538 RVA: 0x00280030 File Offset: 0x0027E230
		public void CreateChild(int id, int x, int y, int disToSecond, int maxCount, int direction)
		{
			bool flag = this.CurrentLivingNpcNum < maxCount;
			if (flag)
			{
				bool flag2 = maxCount - this.CurrentLivingNpcNum >= 2;
				if (flag2)
				{
					this.Child.Add(((PVEGame)base.Game).CreateNpc(id, x + disToSecond, y, 1, direction));
					this.Child.Add(((PVEGame)base.Game).CreateNpc(id, x, y, 1, direction));
				}
				else
				{
					bool flag3 = maxCount - this.CurrentLivingNpcNum == 1;
					if (flag3)
					{
						this.Child.Add(((PVEGame)base.Game).CreateNpc(id, x, y, 1, direction));
					}
				}
			}
		}

		// Token: 0x0600774B RID: 30539 RVA: 0x0002C0CD File Offset: 0x0002A2CD
		public void CreateChild(int id, int x, int y, int disToSecond, int maxCount)
		{
			this.CreateChild(id, x, y, disToSecond, maxCount, -1);
		}

		// Token: 0x0600774C RID: 30540 RVA: 0x002800E4 File Offset: 0x0027E2E4
		public SimpleNpc CreateChild(int id, int x, int y, bool showBlood, LivingConfig config)
		{
			return this.CreateChild(id, x, y, 1, -1, showBlood, config);
		}

		// Token: 0x0600774D RID: 30541 RVA: 0x00280108 File Offset: 0x0027E308
		public SimpleNpc CreateChild(int id, int x, int y, int dir, bool showBlood, LivingConfig config)
		{
			return this.CreateChild(id, x, y, 1, dir, showBlood, config);
		}

		// Token: 0x0600774E RID: 30542 RVA: 0x0028012C File Offset: 0x0027E32C
		public SimpleNpc CreateChild(int id, int x, int y, int type, int dir, bool showBlood, LivingConfig config)
		{
			SimpleNpc simpleNpc = ((PVEGame)base.Game).CreateNpc(id, x, y, type, dir, config);
			this.Child.Add(simpleNpc);
			bool flag = !showBlood;
			if (flag)
			{
				base.Game.SendLivingShowBlood(simpleNpc, 0);
			}
			return simpleNpc;
		}

		// Token: 0x0600774F RID: 30543 RVA: 0x0002C0DF File Offset: 0x0002A2DF
		public void CreateChild(int id, Point[] brithPoint, int maxCount, int maxCountForOnce, int type)
		{
			this.CreateChild(id, brithPoint, maxCount, maxCountForOnce, type, 0);
		}

		// Token: 0x06007750 RID: 30544 RVA: 0x00280180 File Offset: 0x0027E380
		public void CreateChild(int id, Point[] brithPoint, int maxCount, int maxCountForOnce, int type, int objtype)
		{
			Point[] array = new Point[brithPoint.Length];
			bool flag = this.CurrentLivingNpcNum >= maxCount;
			if (!flag)
			{
				int num = ((maxCount - this.CurrentLivingNpcNum < maxCountForOnce) ? (maxCount - this.CurrentLivingNpcNum) : maxCountForOnce);
				for (int i = 0; i < num; i++)
				{
					int num2 = 0;
					bool flag2 = num <= brithPoint.Length;
					if (flag2)
					{
						for (int j = 0; j < brithPoint.Length; j++)
						{
							num2 = base.Game.Random.Next(0, brithPoint.Length);
							bool flag3 = false;
							for (int k = 0; k < array.Length; k++)
							{
								bool flag4 = brithPoint[num2] == array[k];
								if (flag4)
								{
									flag3 = true;
									break;
								}
							}
							bool flag5 = !flag3;
							if (flag5)
							{
								array[num2] = brithPoint[num2];
								break;
							}
							j--;
						}
					}
					bool flag6 = objtype == 0;
					if (flag6)
					{
						this.Child.Add(((PVEGame)base.Game).CreateNpc(id, brithPoint[num2].X, brithPoint[num2].Y, type));
					}
					else
					{
						this.Boss.Add(((PVEGame)base.Game).CreateBoss(id, brithPoint[num2].X, brithPoint[num2].Y, -1, type));
					}
				}
			}
		}

		// Token: 0x06007751 RID: 30545 RVA: 0x00280314 File Offset: 0x0027E514
		public SimpleNpc CreateBoss(int id, int x, int y, int direction, int type)
		{
			SimpleNpc simpleNpc = ((PVEGame)base.Game).CreateNpc(id, x, y, type, direction);
			this.Child.Add(simpleNpc);
			return simpleNpc;
		}

		// Token: 0x06007752 RID: 30546 RVA: 0x0028034C File Offset: 0x0027E54C
		public void CreateBoss(int id, int x, int y, int direction, int disToSecond, int maxCount, string action)
		{
			this.CreateBoss(id, x, y, direction, 1, disToSecond, maxCount, action);
		}

		// Token: 0x06007753 RID: 30547 RVA: 0x00280370 File Offset: 0x0027E570
		public void CreateBoss(int id, int x, int y, int direction, int type, int disToSecond, int maxCount, string action)
		{
			bool flag = this.CurrentLivingBossNum < maxCount;
			if (flag)
			{
				bool flag2 = maxCount - this.CurrentLivingNpcNum >= 2;
				if (flag2)
				{
					this.Boss.Add(((PVEGame)base.Game).CreateBoss(id, x + disToSecond, y, direction, type, action));
					this.Boss.Add(((PVEGame)base.Game).CreateBoss(id, x, y, direction, type, action));
				}
				else
				{
					bool flag3 = maxCount - this.CurrentLivingBossNum == 1;
					if (flag3)
					{
						this.Boss.Add(((PVEGame)base.Game).CreateBoss(id, x, y, direction, type, action));
					}
				}
			}
		}

		// Token: 0x06007754 RID: 30548 RVA: 0x0028042C File Offset: 0x0027E62C
		public void RandomSay(string[] msg, int type, int delay, int finishTime)
		{
			int num = base.Game.Random.Next(0, 2);
			bool flag = num == 1;
			if (flag)
			{
				int num2 = base.Game.Random.Next(0, msg.Count<string>());
				string text = msg[num2];
				this.m_game.AddAction(new LivingSayAction(this, text, type, delay, finishTime));
			}
		}

		// Token: 0x06007755 RID: 30549 RVA: 0x0028048C File Offset: 0x0027E68C
		public override void PrepareNewTurn()
		{
			base.PrepareNewTurn();
			try
			{
				this.m_ai.OnBeginNewTurn();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss BeginNewTurn error:{1}", ex);
			}
		}

		// Token: 0x06007756 RID: 30550 RVA: 0x002804D8 File Offset: 0x0027E6D8
		public override void PrepareSelfTurn()
		{
			base.PrepareSelfTurn();
			base.AddDelay(this.m_npcInfo.Delay);
			try
			{
				this.m_ai.OnBeginSelfTurn();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss BeginSelfTurn error:{1}", ex);
			}
		}

		// Token: 0x06007757 RID: 30551 RVA: 0x00280538 File Offset: 0x0027E738
		public override void StartAttacking()
		{
			base.StartAttacking();
			try
			{
				this.m_ai.OnStartAttacking();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss StartAttacking error:{1}", ex);
			}
			bool isAttacking = base.IsAttacking;
			if (isAttacking)
			{
				this.StopAttacking();
			}
		}

		// Token: 0x06007758 RID: 30552 RVA: 0x00280598 File Offset: 0x0027E798
		public override void StopAttacking()
		{
			base.StopAttacking();
			try
			{
				this.m_ai.OnStopAttacking();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss StopAttacking error:{1}", ex);
			}
		}

		// Token: 0x06007759 RID: 30553 RVA: 0x002805E4 File Offset: 0x0027E7E4
		public override void BeforeTakedDamage(Living source, ref int damageAmount, ref int criticalAmount)
		{
			try
			{
				this.m_ai.OnBeforeTakedDamage(source, ref damageAmount, ref criticalAmount);
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss BeforeTakeDamage error:{0}", ex);
			}
			base.BeforeTakedDamage(source, ref damageAmount, ref criticalAmount);
		}

		// Token: 0x0600775A RID: 30554 RVA: 0x00280638 File Offset: 0x0027E838
		public override void OnAfterTakedBomb()
		{
			try
			{
				this.m_ai.OnAfterTakedBomb();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss OnAfterTakedBomb error:{1}", ex);
			}
		}

		// Token: 0x0600775B RID: 30555 RVA: 0x0028067C File Offset: 0x0027E87C
		public override void OnAfterTakeDamage(Living source)
		{
			try
			{
				this.m_ai.OnAfterTakeDamage(source);
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss OnAfterTakedDamage error:{1}", ex);
			}
		}

		// Token: 0x0600775C RID: 30556 RVA: 0x002806C4 File Offset: 0x0027E8C4
		public override void OnAfterTakedFrozen()
		{
			try
			{
				this.m_ai.OnAfterTakedFrozen();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss OnAfterTakedFrozen error:{0}", ex);
			}
			base.OnAfterTakedFrozen();
		}

		// Token: 0x0600775D RID: 30557 RVA: 0x00280710 File Offset: 0x0027E910
		public override void Die()
		{
			base.Die();
			try
			{
				this.m_ai.OnDie();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss Die error:{1}", ex);
			}
		}

		// Token: 0x0600775E RID: 30558 RVA: 0x0028075C File Offset: 0x0027E95C
		public override void Die(int delay)
		{
			base.Die(delay);
			try
			{
				this.m_ai.OnDie();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss Die error:{1}", ex);
			}
		}

		// Token: 0x0600775F RID: 30559 RVA: 0x002807A8 File Offset: 0x0027E9A8
		public void KillPlayerSay()
		{
			try
			{
				this.m_ai.OnKillPlayerSay();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss Say error:{0}", ex);
			}
		}

		// Token: 0x06007760 RID: 30560 RVA: 0x002807EC File Offset: 0x0027E9EC
		public void DiedSay()
		{
			try
			{
				this.m_ai.OnDiedSay();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss DiedSay error {0}", ex);
			}
		}

		// Token: 0x06007761 RID: 30561 RVA: 0x00280830 File Offset: 0x0027EA30
		public void DiedEvent()
		{
			try
			{
				this.m_ai.OnDiedEvent();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss DiedEvent error {0}", ex);
			}
		}

		// Token: 0x06007762 RID: 30562 RVA: 0x00280874 File Offset: 0x0027EA74
		public void ShootedSay()
		{
			try
			{
				this.m_ai.OnShootedSay();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss ShootedSay error {0}", ex);
			}
		}

		// Token: 0x06007763 RID: 30563 RVA: 0x002808B8 File Offset: 0x0027EAB8
		public void TowardsToPlayer(int playerX, int delay)
		{
			bool flag = playerX > this.X;
			if (flag)
			{
				base.ChangeDirection(1, delay);
			}
			else
			{
				base.ChangeDirection(-1, delay);
			}
		}

		// Token: 0x06007764 RID: 30564 RVA: 0x002808EC File Offset: 0x0027EAEC
		public void SetInitBlood(int initBlood)
		{
			bool flag = initBlood < 0;
			if (flag)
			{
				initBlood = 0;
			}
			bool flag2 = initBlood > this.m_maxBlood;
			if (flag2)
			{
				initBlood = this.m_maxBlood;
			}
			this.m_blood = initBlood;
		}

		// Token: 0x06007765 RID: 30565 RVA: 0x00280928 File Offset: 0x0027EB28
		public override void Dispose()
		{
			base.Dispose();
			try
			{
				this.m_ai.Dispose();
			}
			catch (Exception ex)
			{
				SimpleBoss.log.ErrorFormat("SimpleBoss Dispose error:{1}", ex);
			}
		}

		// Token: 0x040045F9 RID: 17913
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040045FA RID: 17914
		private NpcInfo m_npcInfo;

		// Token: 0x040045FB RID: 17915
		private ABrain m_ai;

		// Token: 0x040045FC RID: 17916
		private List<SimpleNpc> m_child = new List<SimpleNpc>();

		// Token: 0x040045FD RID: 17917
		private List<SimpleBoss> m_boss = new List<SimpleBoss>();

		// Token: 0x040045FE RID: 17918
		private Dictionary<Player, int> m_mostHateful;

		// Token: 0x02000CDF RID: 3295
		// (Invoke) Token: 0x06007768 RID: 30568
		public delegate void SimpleBossShootedEventHanld();
	}
}
