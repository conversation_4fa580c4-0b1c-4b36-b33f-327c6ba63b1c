﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DAA RID: 3498
	public class AE1048 : BasePetEffect
	{
		// Token: 0x06007BF2 RID: 31730 RVA: 0x002952B4 File Offset: 0x002934B4
		public AE1048(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1048, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BF3 RID: 31731 RVA: 0x00295330 File Offset: 0x00293530
		public override bool Start(Living living)
		{
			AE1048 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1048) as AE1048;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BF4 RID: 31732 RVA: 0x0002F6AF File Offset: 0x0002D8AF
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BF5 RID: 31733 RVA: 0x0002F6C5 File Offset: 0x0002D8C5
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BF6 RID: 31734 RVA: 0x0029538C File Offset: 0x0029358C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1048(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x040049E0 RID: 18912
		private int m_type = 0;

		// Token: 0x040049E1 RID: 18913
		private int m_count = 0;

		// Token: 0x040049E2 RID: 18914
		private int m_probability = 0;

		// Token: 0x040049E3 RID: 18915
		private int m_delay = 0;

		// Token: 0x040049E4 RID: 18916
		private int m_coldDown = 0;

		// Token: 0x040049E5 RID: 18917
		private int m_currentId;

		// Token: 0x040049E6 RID: 18918
		private int m_added = 0;
	}
}
