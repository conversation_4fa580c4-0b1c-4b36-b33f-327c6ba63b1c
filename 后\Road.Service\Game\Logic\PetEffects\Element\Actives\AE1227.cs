﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E0D RID: 3597
	public class AE1227 : BasePetEffect
	{
		// Token: 0x06007DF6 RID: 32246 RVA: 0x0029E680 File Offset: 0x0029C880
		public AE1227(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1227, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DF7 RID: 32247 RVA: 0x0029E700 File Offset: 0x0029C900
		public override bool Start(Living living)
		{
			AE1227 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1227) as AE1227;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DF8 RID: 32248 RVA: 0x00030A45 File Offset: 0x0002EC45
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DF9 RID: 32249 RVA: 0x00030A5B File Offset: 0x0002EC5B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DFA RID: 32250 RVA: 0x0029E760 File Offset: 0x0029C960
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && !this.IsTrigger;
			if (flag)
			{
				this.m_added = 1000;
				player.AddBlood(-this.m_added, 1);
				bool flag2 = player.Blood <= 0;
				if (flag2)
				{
					player.Die();
				}
				this.IsTrigger = true;
			}
		}

		// Token: 0x04004C93 RID: 19603
		private int m_type = 0;

		// Token: 0x04004C94 RID: 19604
		private int m_count = 0;

		// Token: 0x04004C95 RID: 19605
		private int m_probability = 0;

		// Token: 0x04004C96 RID: 19606
		private int m_delay = 0;

		// Token: 0x04004C97 RID: 19607
		private int m_coldDown = 0;

		// Token: 0x04004C98 RID: 19608
		private int m_currentId;

		// Token: 0x04004C99 RID: 19609
		private int m_added = 0;
	}
}
