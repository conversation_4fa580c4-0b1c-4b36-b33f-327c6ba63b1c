﻿using System;
using Game.Logic.Effects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D10 RID: 3344
	public class PetReduceBaseGuardEquip : BasePlayerEffect
	{
		// Token: 0x060078B1 RID: 30897 RVA: 0x002872B4 File Offset: 0x002854B4
		public PetReduceBaseGuardEquip(int count, string elementID)
			: base(eEffectType.PetReduceBaseGuardEquip)
		{
			this.m_count = count;
			if (!(elementID == "1567"))
			{
				if (!(elementID == "1603"))
				{
					if (elementID == "1628")
					{
						this.m_value = 70;
						this.m_percent = true;
					}
				}
				else
				{
					this.m_value = 40;
					this.m_percent = true;
				}
			}
			else
			{
				this.m_value = 60;
				this.m_percent = true;
			}
		}

		// Token: 0x060078B2 RID: 30898 RVA: 0x00287334 File Offset: 0x00285534
		public override bool Start(Living living)
		{
			PetReduceBaseGuardEquip petReduceBaseGuardEquip = living.EffectList.GetOfType(eEffectType.PetReduceBaseGuardEquip) as PetReduceBaseGuardEquip;
			bool flag = petReduceBaseGuardEquip != null;
			bool flag2;
			if (flag)
			{
				petReduceBaseGuardEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078B3 RID: 30899 RVA: 0x0028737C File Offset: 0x0028557C
		protected override void OnAttachedToPlayer(Player player)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = this.m_value;
				player.IgnoreGuard += this.m_added;
				bool flag2 = player.IgnoreGuard >= 90;
				if (flag2)
				{
					player.IgnoreGuard = 90;
				}
			}
			player.BeginSelfTurn += this.player_BeginFitting;
		}

		// Token: 0x060078B4 RID: 30900 RVA: 0x0002CDE3 File Offset: 0x0002AFE3
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.IgnoreGuard -= this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.player_BeginFitting;
		}

		// Token: 0x060078B5 RID: 30901 RVA: 0x002873E8 File Offset: 0x002855E8
		private void player_BeginFitting(Living living)
		{
			this.m_count--;
			living.IgnoreGuard += this.m_added;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Game.SendPlayerPicture(living, 29, false);
				this.Stop();
			}
		}

		// Token: 0x040046B9 RID: 18105
		private int m_count;

		// Token: 0x040046BA RID: 18106
		private int m_value;

		// Token: 0x040046BB RID: 18107
		private bool m_percent;

		// Token: 0x040046BC RID: 18108
		private int m_added;
	}
}
