﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C74 RID: 3188
	public class UserVipUpdateCondition : BaseCondition
	{
		// Token: 0x060070CD RID: 28877 RVA: 0x0002A421 File Offset: 0x00028621
		public UserVipUpdateCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x060070CE RID: 28878 RVA: 0x0002A7AC File Offset: 0x000289AC
		public override void AddTrigger(GamePlayer player)
		{
			player.VIPUpgrade += this.player_PlayerVIPUpgrade;
		}

		// Token: 0x060070CF RID: 28879 RVA: 0x002505EC File Offset: 0x0024E7EC
		private void player_PlayerVIPUpgrade(int level, int exp)
		{
			bool flag = base.Value < level;
			if (flag)
			{
				base.Value = level;
			}
		}

		// Token: 0x060070D0 RID: 28880 RVA: 0x0002A7C2 File Offset: 0x000289C2
		public override void RemoveTrigger(GamePlayer player)
		{
			player.VIPUpgrade -= this.player_PlayerVIPUpgrade;
		}

		// Token: 0x060070D1 RID: 28881 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
