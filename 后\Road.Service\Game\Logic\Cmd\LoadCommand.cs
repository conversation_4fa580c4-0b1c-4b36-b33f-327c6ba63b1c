﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F07 RID: 3847
	[GameCommand(16, "游戏加载进度")]
	public class LoadCommand : ICommandHandler
	{
		// Token: 0x060083A6 RID: 33702 RVA: 0x002B50E0 File Offset: 0x002B32E0
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			player.LoadingProcess = packet.ReadInt();
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(16);
			bool flag = game.GameState == eGameState.Loading;
			if (flag)
			{
				bool flag2 = player.LoadingProcess >= 100;
				if (flag2)
				{
					game.CheckState(0);
				}
				gspacketIn.WriteInt(player.LoadingProcess);
				gspacketIn.WriteInt(player.PlayerDetail.AreaID);
				gspacketIn.WriteInt(player.PlayerDetail.PlayerCharacter.ID);
				game.SendToAll(gspacketIn);
			}
		}
	}
}
