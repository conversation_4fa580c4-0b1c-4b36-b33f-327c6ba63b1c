﻿using System;

namespace Game.Logic.Actions
{
	// Token: 0x02000F4A RID: 3914
	public class FocusFreeAction : BaseAction
	{
		// Token: 0x060084EB RID: 34027 RVA: 0x0003521D File Offset: 0x0003341D
		public FocusFreeAction(int x, int y, int type, int delay, int finishTime)
			: base(delay, finishTime)
		{
			this.m_x = x;
			this.m_y = y;
			this.m_type = type;
		}

		// Token: 0x060084EC RID: 34028 RVA: 0x00035240 File Offset: 0x00033440
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			game.SendPhysicalObjFocus(this.m_x, this.m_y, this.m_type);
			base.Finish(tick);
		}

		// Token: 0x040052CD RID: 21197
		private int m_x;

		// Token: 0x040052CE RID: 21198
		private int m_y;

		// Token: 0x040052CF RID: 21199
		private int m_type;
	}
}
