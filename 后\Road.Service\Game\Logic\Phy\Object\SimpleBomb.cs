﻿using System;
using System.Collections.Generic;
using System.Drawing;
using Game.Logic.Actions;
using Game.Logic.Effects;
using Game.Logic.Phy.Actions;
using Game.Logic.Phy.Maps;
using Game.Logic.Phy.Maths;
using SqlDataProvider.Data;

namespace Game.Logic.Phy.Object
{
	// Token: 0x02000CDD RID: 3293
	public class SimpleBomb : BombObject
	{
		// Token: 0x17001472 RID: 5234
		// (get) Token: 0x06007729 RID: 30505 RVA: 0x0002C036 File Offset: 0x0002A236
		public bool DigMap
		{
			get
			{
				return this.digMap;
			}
		}

		// Token: 0x17001473 RID: 5235
		// (get) Token: 0x0600772A RID: 30506 RVA: 0x0002C03E File Offset: 0x0002A23E
		public BallInfo BallInfo
		{
			get
			{
				return this.m_info;
			}
		}

		// Token: 0x17001474 RID: 5236
		// (get) Token: 0x0600772B RID: 30507 RVA: 0x0002C046 File Offset: 0x0002A246
		public Living Owner
		{
			get
			{
				return this.m_owner;
			}
		}

		// Token: 0x17001475 RID: 5237
		// (get) Token: 0x0600772C RID: 30508 RVA: 0x0002C04E File Offset: 0x0002A24E
		public List<BombAction> PetActions
		{
			get
			{
				return this.m_petActions;
			}
		}

		// Token: 0x17001476 RID: 5238
		// (get) Token: 0x0600772D RID: 30509 RVA: 0x0002C056 File Offset: 0x0002A256
		public List<BombAction> Actions
		{
			get
			{
				return this.m_actions;
			}
		}

		// Token: 0x17001477 RID: 5239
		// (get) Token: 0x0600772E RID: 30510 RVA: 0x0002C05E File Offset: 0x0002A25E
		public float LifeTime
		{
			get
			{
				return this.m_lifeTime;
			}
		}

		// Token: 0x0600772F RID: 30511 RVA: 0x0027D5C8 File Offset: 0x0027B7C8
		public SimpleBomb(int id, BombType type, Living owner, BaseGame game, BallInfo info, Tile shape, bool controled, int angle, bool isPlatom)
			: base(id, (float)info.Mass, (float)info.Weight, (float)info.Wind, (float)info.DragIndex)
		{
			this.m_owner = owner;
			this.m_game = game;
			this.m_info = info;
			this.m_shape = shape;
			this.m_type = type;
			this.m_power = info.Power;
			this.m_radius = info.Radii;
			this.m_controled = controled;
			this.m_bombed = false;
			this.m_petRadius = 80;
			this.m_lifeTime = 0f;
			this.m_angle = Math.Abs(angle);
			this.m_isPlatom = isPlatom;
			bool flag = this.m_info.IsSpecial() && this.m_info.HasTunnel;
			if (flag)
			{
				this.digMap = false;
			}
			else
			{
				this.digMap = true;
			}
		}

		// Token: 0x06007730 RID: 30512 RVA: 0x0027D6A8 File Offset: 0x0027B8A8
		public override void StartMoving()
		{
			base.StartMoving();
			this.m_actions = new List<BombAction>();
			this.m_petActions = new List<BombAction>();
			int lifeTime = this.m_game.LifeTime;
			while (this.m_isMoving && this.m_isLiving)
			{
				this.m_lifeTime += 0.04f;
				Point point = this.CompleteNextMovePoint(0.04f);
				this.MoveTo(point.X, point.Y);
				bool isLiving = this.m_isLiving;
				if (isLiving)
				{
					bool flag = Math.Round((double)(this.m_lifeTime * 100f)) % 40.0 == 0.0 && point.Y > 0;
					if (flag)
					{
						this.m_game.AddTempPoint(point.X, point.Y);
					}
					bool flag2 = this.m_controled && base.vY > 0f;
					if (flag2)
					{
						Living living = this.m_map.FindNearestEnemy(this.m_x, this.m_y, 150.0, this.m_owner);
						bool flag3 = living != null;
						if (flag3)
						{
							bool flag4 = living is SimpleBoss;
							Point point2;
							if (flag4)
							{
								Rectangle directDemageRect = living.GetDirectDemageRect();
								point2 = new Point(directDemageRect.X - this.m_x, directDemageRect.Y - this.m_y);
							}
							else
							{
								point2 = new Point(living.X - this.m_x, living.Y - this.m_y);
							}
							point2 = point2.Normalize(1000);
							base.setSpeedXY(point2.X, point2.Y);
							base.UpdateForceFactor(0f, 0f, 0f);
							this.m_controled = false;
							this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.CHANGE_SPEED, point2.X, point2.Y, 0, 0));
						}
					}
				}
				bool bombed = this.m_bombed;
				if (bombed)
				{
					this.m_bombed = false;
					this.BombImp();
				}
			}
		}

		// Token: 0x06007731 RID: 30513 RVA: 0x0027D8DC File Offset: 0x0027BADC
		protected override void CollideObjects(Physics[] list)
		{
			foreach (Physics physics in list)
			{
				physics.CollidedByObject(this);
				this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.PICK, physics.Id, 0, 0, 0));
			}
		}

		// Token: 0x06007732 RID: 30514 RVA: 0x0002C066 File Offset: 0x0002A266
		protected override void CollideGround()
		{
			base.CollideGround();
			this.Bomb();
		}

		// Token: 0x06007733 RID: 30515 RVA: 0x0002C077 File Offset: 0x0002A277
		public void Bomb()
		{
			this.StopMoving();
			this.m_isLiving = false;
			this.m_bombed = true;
		}

		// Token: 0x06007734 RID: 30516 RVA: 0x0027D92C File Offset: 0x0027BB2C
		private void BombImp()
		{
			Point collidePoint = this.GetCollidePoint();
			List<Living> list = this.m_map.FindHitByHitPiont(collidePoint, this.m_radius);
			foreach (Living living in list)
			{
				bool flag = living.IsNoHole || living.NoHoleTurn;
				if (flag)
				{
					living.NoHoleTurn = true;
					this.digMap = false;
				}
				living.SyncAtTime = false;
			}
			this.m_owner.SyncAtTime = false;
			try
			{
				bool flag2 = this.digMap && !this.m_isPlatom;
				if (flag2)
				{
					this.m_map.Dig(this.m_x, this.m_y, this.m_shape, null);
				}
				this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.BOMB, this.m_x, this.m_y, this.digMap ? 1 : 0, 0));
				switch (this.m_type)
				{
				case BombType.FORZEN:
					foreach (Living living2 in list)
					{
						bool flag3 = this.m_owner is SimpleBoss && new IceFronzeEffect(100).Start(living2);
						if (flag3)
						{
							this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.FORZEN, living2.Id, 0, 0, 0));
						}
						else
						{
							bool flag4 = this.m_owner is Player && !(living2 is Player) && living2.Config.DamageForzen;
							if (flag4)
							{
								living2.Properties2 = (int)living2.Properties2 - 1;
								bool flag5 = (int)living2.Properties2 <= 0;
								if (flag5)
								{
									living2.PlayMovie("die", (int)Math.Round((double)((this.m_lifeTime + 1f) * 1000f)), 0);
									living2.Die((int)Math.Round((double)((this.m_lifeTime + 1f) * 1000f)));
								}
								else
								{
									living2.PlayMovie("cry", (int)Math.Round((double)((this.m_lifeTime + 1f) * 1000f)), 0);
								}
							}
							else
							{
								bool flag6 = living2 is SimpleNpc || living2 is Player || (living2 is SimpleBoss && living2.Config.IsHelper) || (living2 is SimpleBoss && living2.Config.CanFrost);
								if (flag6)
								{
									bool flag7 = living2 is SimpleNpc && living2.Config.CanFrost;
									if (flag7)
									{
										this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.DO_ACTION, living2.Id, 0, 0, 4));
										living2.IsFrost = true;
									}
									else
									{
										bool flag8 = new IceFronzeEffect(2).Start(living2);
										if (flag8)
										{
											bool flag9 = living2 is SimpleBoss && !living2.Config.IsHelper;
											if (flag9)
											{
												this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.DO_ACTION, living2.Id, 0, 0, 3));
											}
											else
											{
												this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.FORZEN, living2.Id, 0, 0, 0));
											}
										}
										else
										{
											this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.FORZEN, -1, 0, 0, 0));
											this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.CHANGE_STATE, living2.Id, 0, 0, 0));
										}
									}
								}
							}
						}
						bool flag10 = living2 is SimpleBoss || living2 is SimpleNpc;
						if (flag10)
						{
							Point point = new Point(this.X, this.Y);
							double num = living2.Distance(point);
							bool flag11 = num < (double)this.m_radius;
							if (flag11)
							{
								this.m_game.AddAction(new LivingAfterShootedFrozen(living2, (int)((this.m_lifeTime + 1f) * 1000f)));
							}
						}
					}
					break;
				case BombType.FLY:
				{
					bool flag12 = this.m_y > 10 && this.m_lifeTime > 0.04f;
					if (flag12)
					{
						bool flag13 = !this.m_map.IsEmpty(this.m_x, this.m_y);
						if (flag13)
						{
							PointF pointF = new PointF(0f - base.vX, 0f - base.vY);
							pointF = pointF.Normalize(5f);
							this.m_x -= (int)pointF.X;
							this.m_y -= (int)pointF.Y;
						}
						this.m_owner.SetXY(this.m_x, this.m_y);
						this.m_owner.StartMoving();
						this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.TRANSLATE, this.m_x, this.m_y, 0, 0));
						this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.START_MOVE, this.m_owner.Id, this.m_owner.X, this.m_owner.Y, this.m_owner.IsLiving ? 1 : 0));
						(this.m_owner as Player).OnPlayerAnyShellThrow();
					}
					break;
				}
				case BombType.CURE:
				{
					foreach (Living living3 in list)
					{
						int num2 = this.FindCureCount(list);
						double num3 = ((num2 <= 1) ? 1.0 : ((num2 >= 4) ? 0.4 : ((double)num2 * 0.1)));
						bool flag14 = this.m_info.ID == 10009;
						if (flag14)
						{
							int num4 = (int)Math.Round((double)this.m_lifeTime);
							int num5 = this.m_owner.PetEffects.AddBloodPercent;
							bool flag15 = num4 > 1;
							if (flag15)
							{
								num5 *= num4;
							}
							bool flag16 = this.m_owner.Team == living3.Team;
							if (flag16)
							{
								living3.AddBlood(num5);
								((Player)living3).TotalCure += num5;
								this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.CURE, living3.Id, living3.Blood, num5, 0));
							}
							else
							{
								living3.AddBlood(-num5, 1);
								this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.KILL_PLAYER, living3.Id, num5, 1, living3.Blood));
							}
						}
						else
						{
							bool flag17 = !living3.ForbidAidBlood;
							int num5;
							if (flag17)
							{
								bool flag18 = this.m_owner is Player && ((Player)this.m_owner).CureBombValue > 0;
								if (flag18)
								{
									num5 = ((Player)this.m_owner).CureBombValue;
								}
								else
								{
									int num6 = (int)((double)((Player)this.m_owner).PlayerDetail.SecondWeapon.Template.Property7 * Math.Pow(1.1, (double)((Player)this.m_owner).PlayerDetail.SecondWeapon.StrengthenLevel) * num3);
									num5 = num6;
									num5 += this.m_owner.FightBuffers.ConsortionAddBloodGunCount;
									num5 += this.m_owner.PetEffects.IncreaseAngelicPoint;
									bool flag19 = this.m_owner.FightBuffers.CardAddBloodGunCountLv1 > 0;
									if (flag19)
									{
										num5 += this.m_owner.FightBuffers.CardAddBloodGunCountLv1;
									}
									bool flag20 = this.m_owner.FightBuffers.CardAddBloodGunCountLv2 > 0;
									if (flag20)
									{
										num5 += this.m_owner.FightBuffers.CardAddBloodGunCountLv2;
									}
									bool flag21 = this.m_owner.FightBuffers.CardAddBloodGunCountLv3 > 0;
									if (flag21)
									{
										num5 += this.m_owner.FightBuffers.CardAddBloodGunCountLv3;
									}
									bool flag22 = this.m_owner.FightBuffers.CardAddBloodGunCountLv4 > 0;
									if (flag22)
									{
										num5 += this.m_owner.FightBuffers.CardAddBloodGunCountLv4;
									}
								}
							}
							else
							{
								num5 = 0;
							}
							bool flag23 = living3 is Player;
							if (flag23)
							{
								living3.AddBlood(num5);
								((Player)living3).TotalCure += num5;
								this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.CURE, living3.Id, living3.Blood, num5, 0));
							}
							bool flag24 = (living3 is SimpleBoss || living3 is SimpleNpc) && living3.Config.IsHelper;
							if (flag24)
							{
								living3.AddBlood(num5);
								((SimpleBoss)living3).TotalCure += num5;
								this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.CURE, living3.Id, living3.Blood, num5, 0));
							}
							bool flag25 = (living3 is SimpleBoss || living3 is SimpleNpc) && living3.Config.IsHelperVortex;
							if (flag25)
							{
								living3.AddBlood(num5);
								this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.CURE, living3.Id, living3.Blood, num5, 0));
							}
						}
					}
					bool flag26 = this.m_info.ID != 10009;
					if (flag26)
					{
						(this.m_owner as Player).OnPlayerShootCure();
					}
					(this.m_owner as Player).OnPlayerAnyShellThrow();
					break;
				}
				default:
				{
					int num7 = 0;
					int num8 = 0;
					foreach (Living living4 in list)
					{
						bool flag27 = this.m_owner.IsFriendly(living4) || living4.Config.IsDown || (!(living4 is Player) && !living4.Config.CanTakeDamage);
						if (!flag27)
						{
							bool clearBuff = this.m_owner.ClearBuff;
							if (clearBuff)
							{
								living4.EffectList.StopAllEffect();
							}
							else
							{
								bool flag28 = (living4 is SimpleBoss || living4 is SimpleNpc) && (living4.Config.IsShield || living4.Config.IsGoal);
								if (flag28)
								{
									this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.DO_ACTION, living4.Id, 0, 0, 2));
								}
								else
								{
									bool flag29 = (living4 is SimpleBoss || living4 is SimpleNpc) && (living4.Config.IsShield || (living4.Config.BallCanDamage > 0 && living4.Config.BallCanDamage != this.m_info.ID));
									if (flag29)
									{
										num7 = 0;
									}
									num7 = this.MakeDamage(living4);
									num8 = 0;
									bool flag30 = num7 != 0;
									if (flag30)
									{
										num8 = this.m_owner.MakeCriticalDamage(living4, num7);
										bool flag31 = this.m_owner.FightBuffers.CardSacredBlessingLv1 > 0 && this.m_owner != living4;
										if (flag31)
										{
											this.m_owner.AddBlood(num7 * this.m_owner.FightBuffers.CardSacredBlessingLv1);
										}
										bool flag32 = this.m_owner.FightBuffers.CardSacredBlessingLv2 > 0 && this.m_owner != living4;
										if (flag32)
										{
											this.m_owner.AddBlood(num7 * this.m_owner.FightBuffers.CardSacredBlessingLv2);
										}
										bool flag33 = this.m_owner.FightBuffers.CardSacredBlessingLv3 > 0 && this.m_owner != living4;
										if (flag33)
										{
											this.m_owner.AddBlood(num7 * this.m_owner.FightBuffers.CardSacredBlessingLv3);
										}
										bool flag34 = this.m_owner.FightBuffers.CardSacredBlessingLv4 > 0 && this.m_owner != living4;
										if (flag34)
										{
											this.m_owner.AddBlood(num7 * this.m_owner.FightBuffers.CardSacredBlessingLv4);
										}
										bool flag35 = this.m_owner.Team != living4.Team;
										if (flag35)
										{
											living4.TotalDamageForMatch += num7 + num8;
										}
										this.m_owner.OnTakedDamage(this.m_owner, ref num7, ref num8);
										this.m_owner.OnMakeDamage(this.m_owner, living4, ref num7, ref num8);
										bool flag36 = living4.TakeDamage(this.m_owner, ref num7, ref num8, "Fire");
										if (flag36)
										{
											bool flag37 = (living4 is SimpleBoss || living4 is SimpleNpc) && living4.Config.KeepLife && living4.Blood == 1;
											if (flag37)
											{
												this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.DO_ACTION, living4.Id, 0, 0, 2));
											}
											else
											{
												this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.KILL_PLAYER, living4.Id, num7 + num8, (num8 == 0) ? 1 : 2, living4.Blood));
											}
										}
										else
										{
											this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.UNFORZEN, living4.Id, 0, 0, 0));
										}
										bool flag38 = this.m_owner is Player && living4 is SimpleBoss;
										if (flag38)
										{
											this.m_owner.TotalDameLiving += num8 + num7;
										}
										else
										{
											bool flag39 = this.m_owner is Player && living4 is Player;
											if (flag39)
											{
												this.m_owner.TotalDamagePlayer += num8 + num7;
											}
										}
										bool flag40 = living4 is Player;
										if (flag40)
										{
											int num9 = ((Player)living4).Dander;
											bool flag41 = this.m_owner.FightBuffers.ConsortionReduceDander > 0;
											if (flag41)
											{
												num9 -= num9 * this.m_owner.FightBuffers.ConsortionReduceDander / 100;
												((Player)living4).Dander = num9;
											}
											this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.DANER, living4.Id, num9, 0, 0));
										}
										bool flag42 = living4 is SimpleBoss || living4 is SimpleNpc;
										if (flag42)
										{
											this.m_game.AddAction(new LivingAfterShootedAction(this.m_owner, living4, (int)((this.m_lifeTime + 1f) * 1000f)));
											bool flag43 = living4.DoAction > -1;
											if (flag43)
											{
												this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.DO_ACTION, living4.Id, 0, 0, living4.DoAction));
											}
										}
									}
									else
									{
										bool flag44 = living4 is SimpleBoss || living4 is SimpleNpc;
										if (flag44)
										{
											this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.DO_ACTION, living4.Id, 0, 0, 0));
										}
									}
									bool isLiving = living4.IsLiving;
									if (isLiving)
									{
										living4.StartMoving((int)((this.m_lifeTime + 1f) * 1000f), 12);
										this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.START_MOVE, living4.Id, living4.X, living4.Y, living4.IsLiving ? 1 : 0));
										bool flag45 = this.m_owner.BombFoul && living4 != this.m_owner;
										if (flag45)
										{
											new ContinueReduceBlood(3, 5000, this.m_owner).Start(living4);
										}
										bool flag46 = this.m_owner.TiredShoot && living4 != this.m_owner;
										if (flag46)
										{
											new ReduceStrengthEffect(3, 100).Start(living4);
										}
										bool flag47 = this.m_owner.LockMove && living4 != this.m_owner;
										if (flag47)
										{
											new LockDirectionEffect(2).Start(living4);
										}
									}
									living4.SendAfterShootedAction((int)(((double)this.m_lifeTime + 1.0) * 1000.0));
								}
							}
						}
					}
					List<Living> list2 = this.m_map.FindHitByHitPiont(collidePoint, this.m_petRadius);
					bool flag48 = !(this.m_owner is Player) || ((Player)this.m_owner).ShootCount != 1 || this.m_owner.PetEffects.DamagePercent == 0;
					if (!flag48)
					{
						bool flag49 = list2.Count == 0;
						if (flag49)
						{
							this.m_petActions.Add(new BombAction(this.m_lifeTime, ActionType.PET, -1, 0, 0, 0));
						}
						else
						{
							foreach (Living living5 in list2)
							{
								bool flag50 = living5.Config.IsDown || living5.Config.IsGoal || living5.Config.IsShield || living5.Config.MinBlood > 0 || (living5.Config.KeepLife && living5.Blood == 1) || (!(living5 is Player) && !living5.Config.CanTakeDamage);
								if (flag50)
								{
									this.m_petActions.Add(new BombAction(this.m_lifeTime, ActionType.PET, -1, 0, 0, 0));
								}
								else
								{
									bool flag51 = living5 == this.m_owner;
									if (!flag51)
									{
										num7 = this.MakePetDamage(living5, collidePoint);
										bool flag52 = num7 > 0;
										if (flag52)
										{
											num7 = num7 * this.m_owner.PetEffects.DamagePercent / 100;
											num8 = this.m_owner.MakeCriticalDamage(living5, num7);
											bool flag53 = this.m_owner is Player;
											if (flag53)
											{
												this.m_owner.OnTakedPetDamage(this.m_owner, ref num7, ref num8);
											}
											bool flag54 = living5.PetTakeDamage(this.m_owner, ref num7, ref num8, "PetFire");
											if (flag54)
											{
												bool flag55 = living5 is Player;
												if (flag55)
												{
													this.m_petActions.Add(new BombAction(this.m_lifeTime, ActionType.PET, living5.Id, num7 + num8, ((Player)living5).Dander, living5.Blood));
												}
												else
												{
													this.m_petActions.Add(new BombAction(this.m_lifeTime, ActionType.PET, living5.Id, num7 + num8, 0, living5.Blood));
												}
											}
										}
										else
										{
											this.m_petActions.Add(new BombAction(this.m_lifeTime, ActionType.PET, -1, 0, 0, 0));
										}
									}
								}
							}
						}
					}
					break;
				}
				}
				this.Die();
			}
			finally
			{
				this.m_owner.SyncAtTime = true;
				foreach (Living living6 in list)
				{
					living6.SyncAtTime = true;
				}
			}
		}

		// Token: 0x06007735 RID: 30517 RVA: 0x0027EDDC File Offset: 0x0027CFDC
		public int FindCureCount(List<Living> livs)
		{
			int num = 0;
			foreach (Living living in livs)
			{
				bool flag = living is Player || living.Config.IsHelper;
				if (flag)
				{
					num++;
				}
			}
			return num;
		}

		// Token: 0x06007736 RID: 30518 RVA: 0x0027EE54 File Offset: 0x0027D054
		protected int MakeDamage(Living target)
		{
			bool isChristmasBoss = target.Config.IsChristmasBoss;
			int num;
			if (isChristmasBoss)
			{
				num = 1;
			}
			else
			{
				double num2 = this.m_owner.BaseDamage;
				double num3 = target.BaseGuard;
				double num4 = target.Defence;
				double attack = this.m_owner.Attack;
				bool flag = target.AddArmor && (target as Player).DeputyWeapon != null;
				if (flag)
				{
					int num5 = (int)target.GetHertAddition((target as Player).DeputyWeapon);
					num3 += (double)num5;
					num4 += (double)num5;
				}
				bool ignoreArmor = this.m_owner.IgnoreArmor;
				if (ignoreArmor)
				{
					num3 *= 0.6;
					num4 *= 0.6;
				}
				bool flag2 = this.m_owner.IgnoreGuard > 0 && !this.m_owner.IgnoreArmor;
				if (flag2)
				{
					num4 -= num4 / 100.0 * (double)this.m_owner.IgnoreGuard;
					num3 -= num3 / 100.0 * (double)this.m_owner.IgnoreGuard;
				}
				bool flag3 = this.m_owner.AddDamage > 0;
				if (flag3)
				{
					num2 += num2 / 100.0 * (double)this.m_owner.AddDamage;
				}
				float currentDamagePlus = this.m_owner.CurrentDamagePlus;
				float currentShootMinus = this.m_owner.CurrentShootMinus;
				double num6 = 0.95 * (num3 - (double)(3 * this.m_owner.Grade)) / (500.0 + num3 - (double)(3 * this.m_owner.Grade));
				double num7 = ((num4 - this.m_owner.Lucky >= 0.0) ? (0.95 * (num4 - this.m_owner.Lucky) / (600.0 + num4 - this.m_owner.Lucky)) : 0.0);
				double num8 = (double)this.m_owner.FightBuffers.WorldBossAddDamage * (1.0 - (num3 / 200.0 + num4 * 0.003));
				double num9 = (num8 + num2 * (1.0 + attack * 0.001) * (1.0 - (num6 + num7 - num6 * num7))) * (double)currentDamagePlus * (double)currentShootMinus;
				Point point = new Point(this.X, this.Y);
				double num10 = target.Distance(point);
				bool flag4 = num10 < (double)this.m_radius;
				if (flag4)
				{
					num9 *= 1.0 - num10 / (double)this.m_radius / 4.0;
					bool flag5 = this.m_owner.FightBuffers.CardAddDamageLv1 > 0;
					if (flag5)
					{
						num9 += (double)this.m_owner.FightBuffers.CardAddDamageLv2;
					}
					bool flag6 = this.m_owner.FightBuffers.CardAddDamageLv2 > 0;
					if (flag6)
					{
						num9 += (double)this.m_owner.FightBuffers.CardAddDamageLv3;
					}
					bool flag7 = this.m_owner.FightBuffers.CardAddDamageLv3 > 0;
					if (flag7)
					{
						num9 += (double)this.m_owner.FightBuffers.CardAddDamageLv4;
					}
					bool flag8 = this.m_owner.FightBuffers.CardAddDamageLv4 > 0;
					if (flag8)
					{
						num9 += (double)this.m_owner.FightBuffers.CardAddDamageLv1;
					}
					bool flag9 = this.m_owner.FightBuffers.CardAddPercentDamageLv1 > 0;
					if (flag9)
					{
						num9 += num9 * (double)this.m_owner.FightBuffers.CardAddPercentDamageLv1;
					}
					bool flag10 = this.m_owner.FightBuffers.CardAddPercentDamageLv2 > 0;
					if (flag10)
					{
						num9 += num9 * (double)this.m_owner.FightBuffers.CardAddPercentDamageLv2;
					}
					bool flag11 = this.m_owner.FightBuffers.CardAddPercentDamageLv3 > 0;
					if (flag11)
					{
						num9 += num9 * (double)this.m_owner.FightBuffers.CardAddPercentDamageLv3;
					}
					bool flag12 = this.m_owner.FightBuffers.CardAddPercentDamageLv4 > 0;
					if (flag12)
					{
						num9 += num9 * (double)this.m_owner.FightBuffers.CardAddPercentDamageLv4;
					}
					bool flag13 = this.m_owner is Player && target is Player && target != this.m_owner && target != this.m_owner;
					if (flag13)
					{
						int num11 = 0;
						bool flag14 = (this.m_owner.Direction == 1 && this.angle >= 91) || (this.m_owner.Direction == -1 && this.angle <= 89);
						if (flag14)
						{
							BaseGame game = this.m_game;
							Living owner = this.m_owner;
							eAcrobaciaType eAcrobaciaType = eAcrobaciaType.ImperadorDejogarDeVolta;
							Living owner2 = this.m_owner;
							int num12 = owner2.IdAcrobacias;
							owner2.IdAcrobacias = num12 + 1;
							game.AddAction(new FightAchievementAction(owner, eAcrobaciaType, num12, 1200));
						}
						bool flag15 = this.m_owner.lastShots.ContainsKey(target.Id);
						if (flag15)
						{
							Dictionary<int, int> lastShots = this.m_owner.lastShots;
							int num12 = target.Id;
							int num13 = lastShots[num12];
							lastShots[num12] = num13 + 1;
						}
						else
						{
							this.m_owner.lastShots.Add(target.Id, 1);
						}
						bool flag16 = this.m_owner.Prop1 >= 1 && this.m_owner.Prop3 >= 1 && this.m_owner.lastShots.ContainsKey(target.Id) && this.m_owner.lastShots[target.Id] >= 9;
						if (flag16)
						{
							BaseGame game2 = this.m_game;
							Living owner3 = this.m_owner;
							eAcrobaciaType eAcrobaciaType2 = eAcrobaciaType.DeusDaPrecisao;
							Living owner4 = this.m_owner;
							int num13 = owner4.IdAcrobacias;
							owner4.IdAcrobacias = num13 + 1;
							game2.AddAction(new FightAchievementAction(owner3, eAcrobaciaType2, num13, 1200));
						}
						foreach (int num14 in this.m_owner.lastShots.Keys)
						{
							bool flag17 = this.m_owner.lastShots[num14] >= 1;
							if (flag17)
							{
								num11++;
							}
						}
						bool flag18 = num11 >= 2;
						if (flag18)
						{
							BaseGame game3 = this.m_game;
							Living owner5 = this.m_owner;
							eAcrobaciaType eAcrobaciaType3 = eAcrobaciaType.MestreDeAcrobacia;
							Living owner6 = this.m_owner;
							int num13 = owner6.IdAcrobacias;
							owner6.IdAcrobacias = num13 + 1;
							game3.AddAction(new FightAchievementAction(owner5, eAcrobaciaType3, num13, 1200));
						}
					}
					bool flag19 = num9 < 0.0;
					if (flag19)
					{
						num = 1;
					}
					else
					{
						num = (int)num9;
					}
				}
				else
				{
					num = 0;
				}
			}
			return num;
		}

		// Token: 0x06007737 RID: 30519 RVA: 0x0027F524 File Offset: 0x0027D724
		protected int MakePetDamage(Living target, Point p)
		{
			bool isWorldBoss = target.Config.IsWorldBoss;
			int num;
			if (isWorldBoss)
			{
				num = 0;
			}
			else
			{
				bool isChristmasBoss = target.Config.IsChristmasBoss;
				if (isChristmasBoss)
				{
					num = 1;
				}
				else
				{
					double baseDamage = this.m_owner.BaseDamage;
					double num2 = target.BaseGuard;
					double num3 = target.Defence;
					double attack = this.m_owner.Attack;
					bool isWorldBoss2 = target.Config.IsWorldBoss;
					if (isWorldBoss2)
					{
						num = 0;
					}
					else
					{
						bool flag = target.Team == this.m_owner.Team;
						if (flag)
						{
							num = 0;
						}
						else
						{
							bool flag2 = target.AddArmor && (target as Player).DeputyWeapon != null;
							if (flag2)
							{
								int num4 = (int)target.GetHertAddition((target as Player).DeputyWeapon);
								num2 += (double)num4;
								num3 += (double)num4;
							}
							bool ignoreArmor = this.m_owner.IgnoreArmor;
							if (ignoreArmor)
							{
								num2 *= 0.6;
								num3 *= 0.6;
							}
							float currentDamagePlus = this.m_owner.CurrentDamagePlus;
							float currentShootMinus = this.m_owner.CurrentShootMinus;
							double num5 = 0.95 * (num2 - (double)(3 * this.m_owner.Grade)) / (500.0 + num2 - (double)(3 * this.m_owner.Grade));
							double num6 = ((num3 - this.m_owner.Lucky >= 0.0) ? (0.95 * (num3 - this.m_owner.Lucky) / (600.0 + num3 - this.m_owner.Lucky)) : (0.357 + num3 * 1E-05));
							double num7 = 0.0;
							double num8 = (num7 + baseDamage * (1.0 + attack * 0.001) * (1.0 - (num5 + num6 - num5 * num6))) * (double)currentDamagePlus * (double)currentShootMinus;
							num = ((num8 < 0.0) ? 1 : ((int)num8));
						}
					}
				}
			}
			return num;
		}

		// Token: 0x06007738 RID: 30520 RVA: 0x0027F744 File Offset: 0x0027D944
		protected int GetFightFootballPoint(int livingID)
		{
			if (!true)
			{
			}
			int num;
			switch (livingID)
			{
			case 10005:
				num = 1;
				break;
			case 10006:
				num = 2;
				break;
			case 10007:
				num = 3;
				break;
			case 10008:
				num = 4;
				break;
			case 10009:
				num = 5;
				break;
			default:
				num = 1;
				break;
			}
			if (!true)
			{
			}
			return num;
		}

		// Token: 0x06007739 RID: 30521 RVA: 0x0002C08F File Offset: 0x0002A28F
		protected override void FlyoutMap()
		{
			this.m_actions.Add(new BombAction(this.m_lifeTime, ActionType.FLY_OUT, 0, 0, 0, 0));
			base.FlyoutMap();
		}

		// Token: 0x0600773A RID: 30522 RVA: 0x0027F7B4 File Offset: 0x0027D9B4
		internal void setAngle(int angle)
		{
			this.angle = Math.Abs(Convert.ToInt32(Math.Atan2(Math.Sin((double)angle * 3.141592653589793 / 180.0), Math.Cos((double)angle * 3.141592653589793 / 180.0)) * 180.0 / 3.141592653589793));
		}

		// Token: 0x040045E8 RID: 17896
		private Living m_owner;

		// Token: 0x040045E9 RID: 17897
		private BaseGame m_game;

		// Token: 0x040045EA RID: 17898
		protected Tile m_shape;

		// Token: 0x040045EB RID: 17899
		protected int m_radius;

		// Token: 0x040045EC RID: 17900
		protected int m_petRadius;

		// Token: 0x040045ED RID: 17901
		protected double m_power;

		// Token: 0x040045EE RID: 17902
		protected List<BombAction> m_actions;

		// Token: 0x040045EF RID: 17903
		protected List<BombAction> m_petActions;

		// Token: 0x040045F0 RID: 17904
		protected BombType m_type;

		// Token: 0x040045F1 RID: 17905
		protected bool m_controled;

		// Token: 0x040045F2 RID: 17906
		private float m_lifeTime;

		// Token: 0x040045F3 RID: 17907
		private BallInfo m_info;

		// Token: 0x040045F4 RID: 17908
		private bool m_bombed;

		// Token: 0x040045F5 RID: 17909
		private bool digMap;

		// Token: 0x040045F6 RID: 17910
		private int m_angle;

		// Token: 0x040045F7 RID: 17911
		private bool m_isPlatom;

		// Token: 0x040045F8 RID: 17912
		private int angle;
	}
}
