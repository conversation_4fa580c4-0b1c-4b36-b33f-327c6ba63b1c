﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F39 RID: 3897
	public class WarriorsArena3Effect : BaseCardEffect
	{
		// Token: 0x06008484 RID: 33924 RVA: 0x002B876C File Offset: 0x002B696C
		public WarriorsArena3Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.WarriorsArena3, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008485 RID: 33925 RVA: 0x002B87DC File Offset: 0x002B69DC
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.WarriorsArena3) is WarriorsArena3Effect;
			return flag || base.Start(living);
		}

		// Token: 0x06008486 RID: 33926 RVA: 0x00034F94 File Offset: 0x00033194
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x06008487 RID: 33927 RVA: 0x00034FAA File Offset: 0x000331AA
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x06008488 RID: 33928 RVA: 0x002B8814 File Offset: 0x002B6A14
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.AddMaxBlood(-this.m_value);
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 13;
			if (flag2)
			{
				player.AddMaxBlood(this.m_value);
				this.m_added = this.m_value;
			}
		}

		// Token: 0x0400529F RID: 21151
		private int m_indexValue = 0;

		// Token: 0x040052A0 RID: 21152
		private int m_value = 0;

		// Token: 0x040052A1 RID: 21153
		private int m_added = 0;
	}
}
