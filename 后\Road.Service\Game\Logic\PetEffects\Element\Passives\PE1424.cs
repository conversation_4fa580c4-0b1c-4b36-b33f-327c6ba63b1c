﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D84 RID: 3460
	public class PE1424 : BasePetEffect
	{
		// Token: 0x06007B29 RID: 31529 RVA: 0x00291EC0 File Offset: 0x002900C0
		public PE1424(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1424, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B2A RID: 31530 RVA: 0x00291F40 File Offset: 0x00290140
		public override bool Start(Living living)
		{
			PE1424 pe = living.PetEffectList.GetOfType(ePetEffectType.AE1424) as PE1424;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B2B RID: 31531 RVA: 0x0002EDA0 File Offset: 0x0002CFA0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.player_beginNextTurn;
		}

		// Token: 0x06007B2C RID: 31532 RVA: 0x0002EDB6 File Offset: 0x0002CFB6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.player_beginNextTurn;
		}

		// Token: 0x06007B2D RID: 31533 RVA: 0x00291FA0 File Offset: 0x002901A0
		public void player_beginNextTurn(Living living)
		{
			bool flag = (living as Player).ReloadPointDietYeu != 0;
			if (!flag)
			{
				List<Player> allTeamPlayers = living.Game.GetAllTeamPlayers(living);
				foreach (Player player in allTeamPlayers)
				{
					bool flag2 = player.Attack != 0.0;
					if (flag2)
					{
						this.m_added = (int)(player.Attack * 10.0 / 100.0);
						player.Attack += (double)this.m_added;
						player.ReloadPointDietYeu += this.m_added;
						player.Game.SendPetBuff(player, base.ElementInfo, true);
					}
				}
			}
		}

		// Token: 0x040048DC RID: 18652
		private int m_type = 0;

		// Token: 0x040048DD RID: 18653
		private int m_count = 0;

		// Token: 0x040048DE RID: 18654
		private int m_probability = 0;

		// Token: 0x040048DF RID: 18655
		private int m_delay = 0;

		// Token: 0x040048E0 RID: 18656
		private int m_coldDown = 0;

		// Token: 0x040048E1 RID: 18657
		private int m_currentId;

		// Token: 0x040048E2 RID: 18658
		private int m_added = 0;
	}
}
