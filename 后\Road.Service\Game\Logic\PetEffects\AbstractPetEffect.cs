﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.PetEffects
{
	// Token: 0x02000D50 RID: 3408
	public abstract class AbstractPetEffect
	{
		// Token: 0x17001497 RID: 5271
		// (get) Token: 0x06007A09 RID: 31241 RVA: 0x0002E330 File Offset: 0x0002C530
		protected PetSkillElementInfo Info
		{
			get
			{
				return this.m_eleInfo;
			}
		}

		// Token: 0x17001498 RID: 5272
		// (get) Token: 0x06007A0A RID: 31242 RVA: 0x0002E330 File Offset: 0x0002C530
		public PetSkillElementInfo ElementInfo
		{
			get
			{
				return this.m_eleInfo;
			}
		}

		// Token: 0x17001499 RID: 5273
		// (get) Token: 0x06007A0B RID: 31243 RVA: 0x0002E338 File Offset: 0x0002C538
		public ePetEffectType Type
		{
			get
			{
				return this.m_type;
			}
		}

		// Token: 0x1700149A RID: 5274
		// (get) Token: 0x06007A0C RID: 31244 RVA: 0x0002E338 File Offset: 0x0002C538
		public int TypeValue
		{
			get
			{
				return (int)this.m_type;
			}
		}

		// Token: 0x06007A0D RID: 31245 RVA: 0x0028D61C File Offset: 0x0028B81C
		public AbstractPetEffect(ePetEffectType type, string ElementID)
		{
			this.rand = new Random();
			this.m_type = type;
			this.m_eleInfo = PetMgr.FindPetSkillElement(int.Parse(ElementID));
			bool flag = this.m_eleInfo == null;
			if (flag)
			{
				this.m_eleInfo = new PetSkillElementInfo();
				this.m_eleInfo.EffectPic = "";
				this.m_eleInfo.Pic = -1;
				this.m_eleInfo.Value = 1;
			}
		}

		// Token: 0x06007A0E RID: 31246 RVA: 0x0028D69C File Offset: 0x0028B89C
		public virtual bool Start(Living living)
		{
			this.m_living = living;
			return this.m_living.PetEffectList.Add(this);
		}

		// Token: 0x06007A0F RID: 31247 RVA: 0x0028D6D0 File Offset: 0x0028B8D0
		public bool CheckPVP(Living living, bool IsPVP)
		{
			bool flag = living.Game is PVEGame && IsPVP;
			return !flag || true;
		}

		// Token: 0x06007A10 RID: 31248 RVA: 0x0028D6FC File Offset: 0x0028B8FC
		public virtual bool Stop()
		{
			bool flag = this.m_living != null;
			return flag && this.m_living.PetEffectList.Remove(this);
		}

		// Token: 0x06007A11 RID: 31249 RVA: 0x0028D734 File Offset: 0x0028B934
		public virtual bool Pause()
		{
			bool flag = this.m_living != null;
			return flag && this.m_living.PetEffectList.Pause(this);
		}

		// Token: 0x06007A12 RID: 31250 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnAttached(Living living)
		{
		}

		// Token: 0x06007A13 RID: 31251 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnRemoved(Living living)
		{
		}

		// Token: 0x06007A14 RID: 31252 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnPaused(Living living)
		{
		}

		// Token: 0x0400477D RID: 18301
		private ePetEffectType m_type;

		// Token: 0x0400477E RID: 18302
		protected Living m_living;

		// Token: 0x0400477F RID: 18303
		public Random rand;

		// Token: 0x04004780 RID: 18304
		public bool IsTrigger;

		// Token: 0x04004781 RID: 18305
		private PetSkillElementInfo m_eleInfo;
	}
}
