﻿using System;

namespace Game.Logic
{
	// Token: 0x02000C9D RID: 3229
	public class FightBufferInfo
	{
		// Token: 0x17001344 RID: 4932
		// (get) Token: 0x06007273 RID: 29299 RVA: 0x002630C4 File Offset: 0x002612C4
		// (set) Token: 0x06007274 RID: 29300 RVA: 0x0002AB39 File Offset: 0x00028D39
		public int ConsortionAddBloodGunCount
		{
			get
			{
				return this.m_ConsortionAddBloodGunCount;
			}
			set
			{
				this.m_ConsortionAddBloodGunCount = value;
			}
		}

		// Token: 0x17001345 RID: 4933
		// (get) Token: 0x06007275 RID: 29301 RVA: 0x002630DC File Offset: 0x002612DC
		// (set) Token: 0x06007276 RID: 29302 RVA: 0x0002AB43 File Offset: 0x00028D43
		public int ConsortionAddDamage
		{
			get
			{
				return this.m_ConsortionAddDamage;
			}
			set
			{
				this.m_ConsortionAddDamage = value;
			}
		}

		// Token: 0x17001346 RID: 4934
		// (get) Token: 0x06007277 RID: 29303 RVA: 0x002630F4 File Offset: 0x002612F4
		// (set) Token: 0x06007278 RID: 29304 RVA: 0x0002AB4D File Offset: 0x00028D4D
		public int ConsortionAddCritical
		{
			get
			{
				return this.m_ConsortionAddCritical;
			}
			set
			{
				this.m_ConsortionAddCritical = value;
			}
		}

		// Token: 0x17001347 RID: 4935
		// (get) Token: 0x06007279 RID: 29305 RVA: 0x0026310C File Offset: 0x0026130C
		// (set) Token: 0x0600727A RID: 29306 RVA: 0x0002AB57 File Offset: 0x00028D57
		public int ConsortionAddMaxBlood
		{
			get
			{
				return this.m_ConsortionAddMaxBlood;
			}
			set
			{
				this.m_ConsortionAddMaxBlood = value;
			}
		}

		// Token: 0x17001348 RID: 4936
		// (get) Token: 0x0600727B RID: 29307 RVA: 0x00263124 File Offset: 0x00261324
		// (set) Token: 0x0600727C RID: 29308 RVA: 0x0002AB61 File Offset: 0x00028D61
		public int ConsortionAddProperty
		{
			get
			{
				return this.m_ConsortionAddProperty;
			}
			set
			{
				this.m_ConsortionAddProperty = value;
			}
		}

		// Token: 0x17001349 RID: 4937
		// (get) Token: 0x0600727D RID: 29309 RVA: 0x0026313C File Offset: 0x0026133C
		// (set) Token: 0x0600727E RID: 29310 RVA: 0x0002AB6B File Offset: 0x00028D6B
		public int ConsortionReduceEnergyUse
		{
			get
			{
				return this.m_ConsortionReduceEnergyUse;
			}
			set
			{
				this.m_ConsortionReduceEnergyUse = value;
			}
		}

		// Token: 0x1700134A RID: 4938
		// (get) Token: 0x0600727F RID: 29311 RVA: 0x00263154 File Offset: 0x00261354
		// (set) Token: 0x06007280 RID: 29312 RVA: 0x0002AB75 File Offset: 0x00028D75
		public int ConsortionAddEnergy
		{
			get
			{
				return this.m_ConsortionAddEnergy;
			}
			set
			{
				this.m_ConsortionAddEnergy = value;
			}
		}

		// Token: 0x1700134B RID: 4939
		// (get) Token: 0x06007281 RID: 29313 RVA: 0x0026316C File Offset: 0x0026136C
		// (set) Token: 0x06007282 RID: 29314 RVA: 0x0002AB7F File Offset: 0x00028D7F
		public int ConsortionAddEffectTurn
		{
			get
			{
				return this.m_ConsortionAddEffectTurn;
			}
			set
			{
				this.m_ConsortionAddEffectTurn = value;
			}
		}

		// Token: 0x1700134C RID: 4940
		// (get) Token: 0x06007283 RID: 29315 RVA: 0x00263184 File Offset: 0x00261384
		// (set) Token: 0x06007284 RID: 29316 RVA: 0x0002AB89 File Offset: 0x00028D89
		public int ConsortionAddOfferRate
		{
			get
			{
				return this.m_ConsortionAddOfferRate;
			}
			set
			{
				this.m_ConsortionAddOfferRate = value;
			}
		}

		// Token: 0x1700134D RID: 4941
		// (get) Token: 0x06007285 RID: 29317 RVA: 0x0026319C File Offset: 0x0026139C
		// (set) Token: 0x06007286 RID: 29318 RVA: 0x0002AB93 File Offset: 0x00028D93
		public int ConsortionAddPercentGoldOrGP
		{
			get
			{
				return this.m_ConsortionAddPercentGoldOrGP;
			}
			set
			{
				this.m_ConsortionAddPercentGoldOrGP = value;
			}
		}

		// Token: 0x1700134E RID: 4942
		// (get) Token: 0x06007287 RID: 29319 RVA: 0x002631B4 File Offset: 0x002613B4
		// (set) Token: 0x06007288 RID: 29320 RVA: 0x0002AB9D File Offset: 0x00028D9D
		public int ConsortionAddSpellCount
		{
			get
			{
				return this.m_ConsortionAddSpellCount;
			}
			set
			{
				this.m_ConsortionAddSpellCount = value;
			}
		}

		// Token: 0x1700134F RID: 4943
		// (get) Token: 0x06007289 RID: 29321 RVA: 0x002631CC File Offset: 0x002613CC
		// (set) Token: 0x0600728A RID: 29322 RVA: 0x0002ABA7 File Offset: 0x00028DA7
		public int ConsortionReduceDander
		{
			get
			{
				return this.m_ConsortionReduceDander;
			}
			set
			{
				this.m_ConsortionReduceDander = value;
			}
		}

		// Token: 0x17001350 RID: 4944
		// (get) Token: 0x0600728B RID: 29323 RVA: 0x002631E4 File Offset: 0x002613E4
		// (set) Token: 0x0600728C RID: 29324 RVA: 0x0002ABB1 File Offset: 0x00028DB1
		public int CardAddMaxBloodLv1
		{
			get
			{
				return this.m_CardAddMaxBloodLv1;
			}
			set
			{
				this.m_CardAddMaxBloodLv1 = value;
			}
		}

		// Token: 0x17001351 RID: 4945
		// (get) Token: 0x0600728D RID: 29325 RVA: 0x002631FC File Offset: 0x002613FC
		// (set) Token: 0x0600728E RID: 29326 RVA: 0x0002ABBB File Offset: 0x00028DBB
		public int CardAddMaxBloodLv2
		{
			get
			{
				return this.m_CardAddMaxBloodLv2;
			}
			set
			{
				this.m_CardAddMaxBloodLv2 = value;
			}
		}

		// Token: 0x17001352 RID: 4946
		// (get) Token: 0x0600728F RID: 29327 RVA: 0x00263214 File Offset: 0x00261414
		// (set) Token: 0x06007290 RID: 29328 RVA: 0x0002ABC5 File Offset: 0x00028DC5
		public int CardAddMaxBloodLv3
		{
			get
			{
				return this.m_CardAddMaxBloodLv3;
			}
			set
			{
				this.m_CardAddMaxBloodLv3 = value;
			}
		}

		// Token: 0x17001353 RID: 4947
		// (get) Token: 0x06007291 RID: 29329 RVA: 0x0026322C File Offset: 0x0026142C
		// (set) Token: 0x06007292 RID: 29330 RVA: 0x0002ABCF File Offset: 0x00028DCF
		public int CardAddMaxBloodLv4
		{
			get
			{
				return this.m_CardAddMaxBloodLv4;
			}
			set
			{
				this.m_CardAddMaxBloodLv4 = value;
			}
		}

		// Token: 0x17001354 RID: 4948
		// (get) Token: 0x06007293 RID: 29331 RVA: 0x00263244 File Offset: 0x00261444
		// (set) Token: 0x06007294 RID: 29332 RVA: 0x0002ABD9 File Offset: 0x00028DD9
		public int CardTurnAddDanderLv1
		{
			get
			{
				return this.m_CardTurnAddDanderLv1;
			}
			set
			{
				this.m_CardTurnAddDanderLv1 = value;
			}
		}

		// Token: 0x17001355 RID: 4949
		// (get) Token: 0x06007295 RID: 29333 RVA: 0x0026325C File Offset: 0x0026145C
		// (set) Token: 0x06007296 RID: 29334 RVA: 0x0002ABE3 File Offset: 0x00028DE3
		public int CardTurnAddDanderLv2
		{
			get
			{
				return this.m_CardTurnAddDanderLv2;
			}
			set
			{
				this.m_CardTurnAddDanderLv2 = value;
			}
		}

		// Token: 0x17001356 RID: 4950
		// (get) Token: 0x06007297 RID: 29335 RVA: 0x00263274 File Offset: 0x00261474
		// (set) Token: 0x06007298 RID: 29336 RVA: 0x0002ABED File Offset: 0x00028DED
		public int CardTurnAddDanderLv3
		{
			get
			{
				return this.m_CardTurnAddDanderLv3;
			}
			set
			{
				this.m_CardTurnAddDanderLv3 = value;
			}
		}

		// Token: 0x17001357 RID: 4951
		// (get) Token: 0x06007299 RID: 29337 RVA: 0x0026328C File Offset: 0x0026148C
		// (set) Token: 0x0600729A RID: 29338 RVA: 0x0002ABF7 File Offset: 0x00028DF7
		public int CardTurnAddDanderLv4
		{
			get
			{
				return this.m_CardTurnAddDanderLv4;
			}
			set
			{
				this.m_CardTurnAddDanderLv4 = value;
			}
		}

		// Token: 0x17001358 RID: 4952
		// (get) Token: 0x0600729B RID: 29339 RVA: 0x002632A4 File Offset: 0x002614A4
		// (set) Token: 0x0600729C RID: 29340 RVA: 0x0002AC01 File Offset: 0x00028E01
		public int CardAddDamageLv1
		{
			get
			{
				return this.m_CardAddDamageLv1;
			}
			set
			{
				this.m_CardAddDamageLv1 = value;
			}
		}

		// Token: 0x17001359 RID: 4953
		// (get) Token: 0x0600729D RID: 29341 RVA: 0x002632BC File Offset: 0x002614BC
		// (set) Token: 0x0600729E RID: 29342 RVA: 0x0002AC0B File Offset: 0x00028E0B
		public int CardAddDamageLv2
		{
			get
			{
				return this.m_CardAddDamageLv2;
			}
			set
			{
				this.m_CardAddDamageLv2 = value;
			}
		}

		// Token: 0x1700135A RID: 4954
		// (get) Token: 0x0600729F RID: 29343 RVA: 0x002632D4 File Offset: 0x002614D4
		// (set) Token: 0x060072A0 RID: 29344 RVA: 0x0002AC15 File Offset: 0x00028E15
		public int CardAddDamageLv3
		{
			get
			{
				return this.m_CardAddDamageLv3;
			}
			set
			{
				this.m_CardAddDamageLv3 = value;
			}
		}

		// Token: 0x1700135B RID: 4955
		// (get) Token: 0x060072A1 RID: 29345 RVA: 0x002632EC File Offset: 0x002614EC
		// (set) Token: 0x060072A2 RID: 29346 RVA: 0x0002AC1F File Offset: 0x00028E1F
		public int CardAddDamageLv4
		{
			get
			{
				return this.m_CardAddDamageLv4;
			}
			set
			{
				this.m_CardAddDamageLv4 = value;
			}
		}

		// Token: 0x1700135C RID: 4956
		// (get) Token: 0x060072A3 RID: 29347 RVA: 0x00263304 File Offset: 0x00261504
		// (set) Token: 0x060072A4 RID: 29348 RVA: 0x0002AC29 File Offset: 0x00028E29
		public int CardAddCriticalLv1
		{
			get
			{
				return this.m_CardAddCriticalLv1;
			}
			set
			{
				this.m_CardAddCriticalLv1 = value;
			}
		}

		// Token: 0x1700135D RID: 4957
		// (get) Token: 0x060072A5 RID: 29349 RVA: 0x0026331C File Offset: 0x0026151C
		// (set) Token: 0x060072A6 RID: 29350 RVA: 0x0002AC33 File Offset: 0x00028E33
		public int CardAddCriticalLv2
		{
			get
			{
				return this.m_CardAddCriticalLv2;
			}
			set
			{
				this.m_CardAddCriticalLv2 = value;
			}
		}

		// Token: 0x1700135E RID: 4958
		// (get) Token: 0x060072A7 RID: 29351 RVA: 0x00263334 File Offset: 0x00261534
		// (set) Token: 0x060072A8 RID: 29352 RVA: 0x0002AC3D File Offset: 0x00028E3D
		public int CardAddCriticalLv3
		{
			get
			{
				return this.m_CardAddCriticalLv3;
			}
			set
			{
				this.m_CardAddCriticalLv3 = value;
			}
		}

		// Token: 0x1700135F RID: 4959
		// (get) Token: 0x060072A9 RID: 29353 RVA: 0x0026334C File Offset: 0x0026154C
		// (set) Token: 0x060072AA RID: 29354 RVA: 0x0002AC47 File Offset: 0x00028E47
		public int CardAddCriticalLv4
		{
			get
			{
				return this.m_CardAddCriticalLv4;
			}
			set
			{
				this.m_CardAddCriticalLv4 = value;
			}
		}

		// Token: 0x17001360 RID: 4960
		// (get) Token: 0x060072AB RID: 29355 RVA: 0x00263364 File Offset: 0x00261564
		// (set) Token: 0x060072AC RID: 29356 RVA: 0x0002AC51 File Offset: 0x00028E51
		public int CardExemptEnergyLv1
		{
			get
			{
				return this.m_CardExemptEnergyLv1;
			}
			set
			{
				this.m_CardExemptEnergyLv1 = value;
			}
		}

		// Token: 0x17001361 RID: 4961
		// (get) Token: 0x060072AD RID: 29357 RVA: 0x0026337C File Offset: 0x0026157C
		// (set) Token: 0x060072AE RID: 29358 RVA: 0x0002AC5B File Offset: 0x00028E5B
		public int CardExemptEnergyLv2
		{
			get
			{
				return this.m_CardExemptEnergyLv2;
			}
			set
			{
				this.m_CardExemptEnergyLv2 = value;
			}
		}

		// Token: 0x17001362 RID: 4962
		// (get) Token: 0x060072AF RID: 29359 RVA: 0x00263394 File Offset: 0x00261594
		// (set) Token: 0x060072B0 RID: 29360 RVA: 0x0002AC65 File Offset: 0x00028E65
		public int CardExemptEnergyLv3
		{
			get
			{
				return this.m_CardExemptEnergyLv3;
			}
			set
			{
				this.m_CardExemptEnergyLv3 = value;
			}
		}

		// Token: 0x17001363 RID: 4963
		// (get) Token: 0x060072B1 RID: 29361 RVA: 0x002633AC File Offset: 0x002615AC
		// (set) Token: 0x060072B2 RID: 29362 RVA: 0x0002AC6F File Offset: 0x00028E6F
		public int CardExemptEnergyLv4
		{
			get
			{
				return this.m_CardExemptEnergyLv4;
			}
			set
			{
				this.m_CardExemptEnergyLv4 = value;
			}
		}

		// Token: 0x17001364 RID: 4964
		// (get) Token: 0x060072B3 RID: 29363 RVA: 0x002633C4 File Offset: 0x002615C4
		// (set) Token: 0x060072B4 RID: 29364 RVA: 0x0002AC79 File Offset: 0x00028E79
		public int CardAddDanderLv1
		{
			get
			{
				return this.m_CardAddDanderLv1;
			}
			set
			{
				this.m_CardAddDanderLv1 = value;
			}
		}

		// Token: 0x17001365 RID: 4965
		// (get) Token: 0x060072B5 RID: 29365 RVA: 0x002633DC File Offset: 0x002615DC
		// (set) Token: 0x060072B6 RID: 29366 RVA: 0x0002AC83 File Offset: 0x00028E83
		public int CardAddDanderLv2
		{
			get
			{
				return this.m_CardAddDanderLv2;
			}
			set
			{
				this.m_CardAddDanderLv2 = value;
			}
		}

		// Token: 0x17001366 RID: 4966
		// (get) Token: 0x060072B7 RID: 29367 RVA: 0x002633F4 File Offset: 0x002615F4
		// (set) Token: 0x060072B8 RID: 29368 RVA: 0x0002AC8D File Offset: 0x00028E8D
		public int CardAddDanderLv3
		{
			get
			{
				return this.m_CardAddDanderLv3;
			}
			set
			{
				this.m_CardAddDanderLv3 = value;
			}
		}

		// Token: 0x17001367 RID: 4967
		// (get) Token: 0x060072B9 RID: 29369 RVA: 0x0026340C File Offset: 0x0026160C
		// (set) Token: 0x060072BA RID: 29370 RVA: 0x0002AC97 File Offset: 0x00028E97
		public int CardAddDanderLv4
		{
			get
			{
				return this.m_CardAddDanderLv4;
			}
			set
			{
				this.m_CardAddDanderLv4 = value;
			}
		}

		// Token: 0x17001368 RID: 4968
		// (get) Token: 0x060072BB RID: 29371 RVA: 0x00263424 File Offset: 0x00261624
		// (set) Token: 0x060072BC RID: 29372 RVA: 0x0002ACA1 File Offset: 0x00028EA1
		public int CardAddPropertyLv1
		{
			get
			{
				return this.m_CardAddPropertyLv1;
			}
			set
			{
				this.m_CardAddPropertyLv1 = value;
			}
		}

		// Token: 0x17001369 RID: 4969
		// (get) Token: 0x060072BD RID: 29373 RVA: 0x0026343C File Offset: 0x0026163C
		// (set) Token: 0x060072BE RID: 29374 RVA: 0x0002ACAB File Offset: 0x00028EAB
		public int CardAddPropertyLv2
		{
			get
			{
				return this.m_CardAddPropertyLv2;
			}
			set
			{
				this.m_CardAddPropertyLv2 = value;
			}
		}

		// Token: 0x1700136A RID: 4970
		// (get) Token: 0x060072BF RID: 29375 RVA: 0x00263454 File Offset: 0x00261654
		// (set) Token: 0x060072C0 RID: 29376 RVA: 0x0002ACB5 File Offset: 0x00028EB5
		public int CardAddPropertyLv3
		{
			get
			{
				return this.m_CardAddPropertyLv3;
			}
			set
			{
				this.m_CardAddPropertyLv3 = value;
			}
		}

		// Token: 0x1700136B RID: 4971
		// (get) Token: 0x060072C1 RID: 29377 RVA: 0x0026346C File Offset: 0x0026166C
		// (set) Token: 0x060072C2 RID: 29378 RVA: 0x0002ACBF File Offset: 0x00028EBF
		public int CardAddPropertyLv4
		{
			get
			{
				return this.m_CardAddPropertyLv4;
			}
			set
			{
				this.m_CardAddPropertyLv4 = value;
			}
		}

		// Token: 0x1700136C RID: 4972
		// (get) Token: 0x060072C3 RID: 29379 RVA: 0x00263484 File Offset: 0x00261684
		// (set) Token: 0x060072C4 RID: 29380 RVA: 0x0002ACC9 File Offset: 0x00028EC9
		public int CardReduceDamageLv1
		{
			get
			{
				return this.m_CardReduceDamageLv1;
			}
			set
			{
				this.m_CardReduceDamageLv1 = value;
			}
		}

		// Token: 0x1700136D RID: 4973
		// (get) Token: 0x060072C5 RID: 29381 RVA: 0x0026349C File Offset: 0x0026169C
		// (set) Token: 0x060072C6 RID: 29382 RVA: 0x0002ACD3 File Offset: 0x00028ED3
		public int CardReduceDamageLv2
		{
			get
			{
				return this.m_CardReduceDamageLv2;
			}
			set
			{
				this.m_CardReduceDamageLv2 = value;
			}
		}

		// Token: 0x1700136E RID: 4974
		// (get) Token: 0x060072C7 RID: 29383 RVA: 0x002634B4 File Offset: 0x002616B4
		// (set) Token: 0x060072C8 RID: 29384 RVA: 0x0002ACDD File Offset: 0x00028EDD
		public int CardReduceDamageLv3
		{
			get
			{
				return this.m_CardReduceDamageLv3;
			}
			set
			{
				this.m_CardReduceDamageLv3 = value;
			}
		}

		// Token: 0x1700136F RID: 4975
		// (get) Token: 0x060072C9 RID: 29385 RVA: 0x002634CC File Offset: 0x002616CC
		// (set) Token: 0x060072CA RID: 29386 RVA: 0x0002ACE7 File Offset: 0x00028EE7
		public int CardReduceDamageLv4
		{
			get
			{
				return this.m_CardReduceDamageLv4;
			}
			set
			{
				this.m_CardReduceDamageLv4 = value;
			}
		}

		// Token: 0x17001370 RID: 4976
		// (get) Token: 0x060072CB RID: 29387 RVA: 0x002634E4 File Offset: 0x002616E4
		// (set) Token: 0x060072CC RID: 29388 RVA: 0x0002ACF1 File Offset: 0x00028EF1
		public int CardAddPercentDamageLv1
		{
			get
			{
				return this.m_CardAddPercentDamageLv1;
			}
			set
			{
				this.m_CardAddPercentDamageLv1 = value;
			}
		}

		// Token: 0x17001371 RID: 4977
		// (get) Token: 0x060072CD RID: 29389 RVA: 0x002634FC File Offset: 0x002616FC
		// (set) Token: 0x060072CE RID: 29390 RVA: 0x0002ACFB File Offset: 0x00028EFB
		public int CardAddPercentDamageLv2
		{
			get
			{
				return this.m_CardAddPercentDamageLv2;
			}
			set
			{
				this.m_CardAddPercentDamageLv2 = value;
			}
		}

		// Token: 0x17001372 RID: 4978
		// (get) Token: 0x060072CF RID: 29391 RVA: 0x00263514 File Offset: 0x00261714
		// (set) Token: 0x060072D0 RID: 29392 RVA: 0x0002AD05 File Offset: 0x00028F05
		public int CardAddPercentDamageLv3
		{
			get
			{
				return this.m_CardAddPercentDamageLv3;
			}
			set
			{
				this.m_CardAddPercentDamageLv3 = value;
			}
		}

		// Token: 0x17001373 RID: 4979
		// (get) Token: 0x060072D1 RID: 29393 RVA: 0x0026352C File Offset: 0x0026172C
		// (set) Token: 0x060072D2 RID: 29394 RVA: 0x0002AD0F File Offset: 0x00028F0F
		public int CardAddPercentDamageLv4
		{
			get
			{
				return this.m_CardAddPercentDamageLv4;
			}
			set
			{
				this.m_CardAddPercentDamageLv4 = value;
			}
		}

		// Token: 0x17001374 RID: 4980
		// (get) Token: 0x060072D3 RID: 29395 RVA: 0x00263544 File Offset: 0x00261744
		// (set) Token: 0x060072D4 RID: 29396 RVA: 0x0002AD19 File Offset: 0x00028F19
		public int CardSetDefaultDanderLv1
		{
			get
			{
				return this.m_CardSetDefaultDanderLv1;
			}
			set
			{
				this.m_CardSetDefaultDanderLv1 = value;
			}
		}

		// Token: 0x17001375 RID: 4981
		// (get) Token: 0x060072D5 RID: 29397 RVA: 0x0026355C File Offset: 0x0026175C
		// (set) Token: 0x060072D6 RID: 29398 RVA: 0x0002AD23 File Offset: 0x00028F23
		public int CardSetDefaultDanderLv2
		{
			get
			{
				return this.m_CardSetDefaultDanderLv2;
			}
			set
			{
				this.m_CardSetDefaultDanderLv2 = value;
			}
		}

		// Token: 0x17001376 RID: 4982
		// (get) Token: 0x060072D7 RID: 29399 RVA: 0x00263574 File Offset: 0x00261774
		// (set) Token: 0x060072D8 RID: 29400 RVA: 0x0002AD2D File Offset: 0x00028F2D
		public int CardSetDefaultDanderLv3
		{
			get
			{
				return this.m_CardSetDefaultDanderLv3;
			}
			set
			{
				this.m_CardSetDefaultDanderLv3 = value;
			}
		}

		// Token: 0x17001377 RID: 4983
		// (get) Token: 0x060072D9 RID: 29401 RVA: 0x0026358C File Offset: 0x0026178C
		// (set) Token: 0x060072DA RID: 29402 RVA: 0x0002AD37 File Offset: 0x00028F37
		public int CardSetDefaultDanderLv4
		{
			get
			{
				return this.m_CardSetDefaultDanderLv4;
			}
			set
			{
				this.m_CardSetDefaultDanderLv4 = value;
			}
		}

		// Token: 0x17001378 RID: 4984
		// (get) Token: 0x060072DB RID: 29403 RVA: 0x002635A4 File Offset: 0x002617A4
		// (set) Token: 0x060072DC RID: 29404 RVA: 0x0002AD41 File Offset: 0x00028F41
		public int CardReduceContinueDamageLv1
		{
			get
			{
				return this.m_CardReduceContinueDamageLv1;
			}
			set
			{
				this.m_CardReduceContinueDamageLv1 = value;
			}
		}

		// Token: 0x17001379 RID: 4985
		// (get) Token: 0x060072DD RID: 29405 RVA: 0x002635BC File Offset: 0x002617BC
		// (set) Token: 0x060072DE RID: 29406 RVA: 0x0002AD4B File Offset: 0x00028F4B
		public int CardReduceContinueDamageLv2
		{
			get
			{
				return this.m_CardReduceContinueDamageLv2;
			}
			set
			{
				this.m_CardReduceContinueDamageLv2 = value;
			}
		}

		// Token: 0x1700137A RID: 4986
		// (get) Token: 0x060072DF RID: 29407 RVA: 0x002635D4 File Offset: 0x002617D4
		// (set) Token: 0x060072E0 RID: 29408 RVA: 0x0002AD55 File Offset: 0x00028F55
		public int CardReduceContinueDamageLv3
		{
			get
			{
				return this.m_CardReduceContinueDamageLv3;
			}
			set
			{
				this.m_CardReduceContinueDamageLv3 = value;
			}
		}

		// Token: 0x1700137B RID: 4987
		// (get) Token: 0x060072E1 RID: 29409 RVA: 0x002635EC File Offset: 0x002617EC
		// (set) Token: 0x060072E2 RID: 29410 RVA: 0x0002AD5F File Offset: 0x00028F5F
		public int CardReduceContinueDamageLv4
		{
			get
			{
				return this.m_CardReduceContinueDamageLv4;
			}
			set
			{
				this.m_CardReduceContinueDamageLv4 = value;
			}
		}

		// Token: 0x1700137C RID: 4988
		// (get) Token: 0x060072E3 RID: 29411 RVA: 0x00263604 File Offset: 0x00261804
		// (set) Token: 0x060072E4 RID: 29412 RVA: 0x0002AD69 File Offset: 0x00028F69
		public int CardDoNotMoveLv1
		{
			get
			{
				return this.m_CardDoNotMoveLv1;
			}
			set
			{
				this.m_CardDoNotMoveLv1 = value;
			}
		}

		// Token: 0x1700137D RID: 4989
		// (get) Token: 0x060072E5 RID: 29413 RVA: 0x0026361C File Offset: 0x0026181C
		// (set) Token: 0x060072E6 RID: 29414 RVA: 0x0002AD73 File Offset: 0x00028F73
		public int CardDoNotMoveLv2
		{
			get
			{
				return this.m_CardDoNotMoveLv2;
			}
			set
			{
				this.m_CardDoNotMoveLv2 = value;
			}
		}

		// Token: 0x1700137E RID: 4990
		// (get) Token: 0x060072E7 RID: 29415 RVA: 0x00263634 File Offset: 0x00261834
		// (set) Token: 0x060072E8 RID: 29416 RVA: 0x0002AD7D File Offset: 0x00028F7D
		public int CardDoNotMoveLv3
		{
			get
			{
				return this.m_CardDoNotMoveLv3;
			}
			set
			{
				this.m_CardDoNotMoveLv3 = value;
			}
		}

		// Token: 0x1700137F RID: 4991
		// (get) Token: 0x060072E9 RID: 29417 RVA: 0x0026364C File Offset: 0x0026184C
		// (set) Token: 0x060072EA RID: 29418 RVA: 0x0002AD87 File Offset: 0x00028F87
		public int CardDoNotMoveLv4
		{
			get
			{
				return this.m_CardDoNotMoveLv4;
			}
			set
			{
				this.m_CardDoNotMoveLv4 = value;
			}
		}

		// Token: 0x17001380 RID: 4992
		// (get) Token: 0x060072EB RID: 29419 RVA: 0x00263664 File Offset: 0x00261864
		// (set) Token: 0x060072EC RID: 29420 RVA: 0x0002AD91 File Offset: 0x00028F91
		public int CardAddPercentDefanceLv1
		{
			get
			{
				return this.m_CardAddPercentDefanceLv1;
			}
			set
			{
				this.m_CardAddPercentDefanceLv1 = value;
			}
		}

		// Token: 0x17001381 RID: 4993
		// (get) Token: 0x060072ED RID: 29421 RVA: 0x0026367C File Offset: 0x0026187C
		// (set) Token: 0x060072EE RID: 29422 RVA: 0x0002AD9B File Offset: 0x00028F9B
		public int CardAddPercentDefanceLv2
		{
			get
			{
				return this.m_CardAddPercentDefanceLv2;
			}
			set
			{
				this.m_CardAddPercentDefanceLv2 = value;
			}
		}

		// Token: 0x17001382 RID: 4994
		// (get) Token: 0x060072EF RID: 29423 RVA: 0x00263694 File Offset: 0x00261894
		// (set) Token: 0x060072F0 RID: 29424 RVA: 0x0002ADA5 File Offset: 0x00028FA5
		public int CardAddPercentDefanceLv3
		{
			get
			{
				return this.m_CardAddPercentDefanceLv3;
			}
			set
			{
				this.m_CardAddPercentDefanceLv3 = value;
			}
		}

		// Token: 0x17001383 RID: 4995
		// (get) Token: 0x060072F1 RID: 29425 RVA: 0x002636AC File Offset: 0x002618AC
		// (set) Token: 0x060072F2 RID: 29426 RVA: 0x0002ADAF File Offset: 0x00028FAF
		public int CardAddPercentDefanceLv4
		{
			get
			{
				return this.m_CardAddPercentDefanceLv4;
			}
			set
			{
				this.m_CardAddPercentDefanceLv4 = value;
			}
		}

		// Token: 0x17001384 RID: 4996
		// (get) Token: 0x060072F3 RID: 29427 RVA: 0x002636C4 File Offset: 0x002618C4
		// (set) Token: 0x060072F4 RID: 29428 RVA: 0x0002ADB9 File Offset: 0x00028FB9
		public int CardReducePoisoningLv1
		{
			get
			{
				return this.m_CardReducePoisoningLv1;
			}
			set
			{
				this.m_CardReducePoisoningLv1 = value;
			}
		}

		// Token: 0x17001385 RID: 4997
		// (get) Token: 0x060072F5 RID: 29429 RVA: 0x002636DC File Offset: 0x002618DC
		// (set) Token: 0x060072F6 RID: 29430 RVA: 0x0002ADC3 File Offset: 0x00028FC3
		public int CardReducePoisoningLv2
		{
			get
			{
				return this.m_CardReducePoisoningLv2;
			}
			set
			{
				this.m_CardReducePoisoningLv2 = value;
			}
		}

		// Token: 0x17001386 RID: 4998
		// (get) Token: 0x060072F7 RID: 29431 RVA: 0x002636F4 File Offset: 0x002618F4
		// (set) Token: 0x060072F8 RID: 29432 RVA: 0x0002ADCD File Offset: 0x00028FCD
		public int CardReducePoisoningLv3
		{
			get
			{
				return this.m_CardReducePoisoningLv3;
			}
			set
			{
				this.m_CardReducePoisoningLv3 = value;
			}
		}

		// Token: 0x17001387 RID: 4999
		// (get) Token: 0x060072F9 RID: 29433 RVA: 0x0026370C File Offset: 0x0026190C
		// (set) Token: 0x060072FA RID: 29434 RVA: 0x0002ADD7 File Offset: 0x00028FD7
		public int CardReducePoisoningLv4
		{
			get
			{
				return this.m_CardReducePoisoningLv4;
			}
			set
			{
				this.m_CardReducePoisoningLv4 = value;
			}
		}

		// Token: 0x17001388 RID: 5000
		// (get) Token: 0x060072FB RID: 29435 RVA: 0x00263724 File Offset: 0x00261924
		// (set) Token: 0x060072FC RID: 29436 RVA: 0x0002ADE1 File Offset: 0x00028FE1
		public int CardAddBloodGunCountLv1
		{
			get
			{
				return this.m_CardAddBloodGunCountLv1;
			}
			set
			{
				this.m_CardAddBloodGunCountLv1 = value;
			}
		}

		// Token: 0x17001389 RID: 5001
		// (get) Token: 0x060072FD RID: 29437 RVA: 0x0026373C File Offset: 0x0026193C
		// (set) Token: 0x060072FE RID: 29438 RVA: 0x0002ADEB File Offset: 0x00028FEB
		public int CardAddBloodGunCountLv2
		{
			get
			{
				return this.m_CardAddBloodGunCountLv2;
			}
			set
			{
				this.m_CardAddBloodGunCountLv2 = value;
			}
		}

		// Token: 0x1700138A RID: 5002
		// (get) Token: 0x060072FF RID: 29439 RVA: 0x00263754 File Offset: 0x00261954
		// (set) Token: 0x06007300 RID: 29440 RVA: 0x0002ADF5 File Offset: 0x00028FF5
		public int CardAddBloodGunCountLv3
		{
			get
			{
				return this.m_CardAddBloodGunCountLv3;
			}
			set
			{
				this.m_CardAddBloodGunCountLv3 = value;
			}
		}

		// Token: 0x1700138B RID: 5003
		// (get) Token: 0x06007301 RID: 29441 RVA: 0x0026376C File Offset: 0x0026196C
		// (set) Token: 0x06007302 RID: 29442 RVA: 0x0002ADFF File Offset: 0x00028FFF
		public int CardAddBloodGunCountLv4
		{
			get
			{
				return this.m_CardAddBloodGunCountLv4;
			}
			set
			{
				this.m_CardAddBloodGunCountLv4 = value;
			}
		}

		// Token: 0x1700138C RID: 5004
		// (get) Token: 0x06007303 RID: 29443 RVA: 0x00263784 File Offset: 0x00261984
		// (set) Token: 0x06007304 RID: 29444 RVA: 0x0002AE09 File Offset: 0x00029009
		public int CardResistAttackLv1
		{
			get
			{
				return this.m_CardResistAttackLv1;
			}
			set
			{
				this.m_CardResistAttackLv1 = value;
			}
		}

		// Token: 0x1700138D RID: 5005
		// (get) Token: 0x06007305 RID: 29445 RVA: 0x0026379C File Offset: 0x0026199C
		// (set) Token: 0x06007306 RID: 29446 RVA: 0x0002AE13 File Offset: 0x00029013
		public int CardResistAttackLv2
		{
			get
			{
				return this.m_CardResistAttackLv2;
			}
			set
			{
				this.m_CardResistAttackLv2 = value;
			}
		}

		// Token: 0x1700138E RID: 5006
		// (get) Token: 0x06007307 RID: 29447 RVA: 0x002637B4 File Offset: 0x002619B4
		// (set) Token: 0x06007308 RID: 29448 RVA: 0x0002AE1D File Offset: 0x0002901D
		public int CardResistAttackLv3
		{
			get
			{
				return this.m_CardResistAttackLv3;
			}
			set
			{
				this.m_CardResistAttackLv3 = value;
			}
		}

		// Token: 0x1700138F RID: 5007
		// (get) Token: 0x06007309 RID: 29449 RVA: 0x002637CC File Offset: 0x002619CC
		// (set) Token: 0x0600730A RID: 29450 RVA: 0x0002AE27 File Offset: 0x00029027
		public int CardResistAttackLv4
		{
			get
			{
				return this.m_CardResistAttackLv4;
			}
			set
			{
				this.m_CardResistAttackLv4 = value;
			}
		}

		// Token: 0x17001390 RID: 5008
		// (get) Token: 0x0600730B RID: 29451 RVA: 0x002637E4 File Offset: 0x002619E4
		// (set) Token: 0x0600730C RID: 29452 RVA: 0x0002AE31 File Offset: 0x00029031
		public int CardSacredBlessingLv1
		{
			get
			{
				return this.m_CardSacredBlessingLv1;
			}
			set
			{
				this.m_CardSacredBlessingLv1 = value;
			}
		}

		// Token: 0x17001391 RID: 5009
		// (get) Token: 0x0600730D RID: 29453 RVA: 0x002637FC File Offset: 0x002619FC
		// (set) Token: 0x0600730E RID: 29454 RVA: 0x0002AE3B File Offset: 0x0002903B
		public int CardSacredBlessingLv2
		{
			get
			{
				return this.m_CardSacredBlessingLv2;
			}
			set
			{
				this.m_CardSacredBlessingLv2 = value;
			}
		}

		// Token: 0x17001392 RID: 5010
		// (get) Token: 0x0600730F RID: 29455 RVA: 0x00263814 File Offset: 0x00261A14
		// (set) Token: 0x06007310 RID: 29456 RVA: 0x0002AE45 File Offset: 0x00029045
		public int CardSacredBlessingLv3
		{
			get
			{
				return this.m_CardSacredBlessingLv3;
			}
			set
			{
				this.m_CardSacredBlessingLv3 = value;
			}
		}

		// Token: 0x17001393 RID: 5011
		// (get) Token: 0x06007311 RID: 29457 RVA: 0x0026382C File Offset: 0x00261A2C
		// (set) Token: 0x06007312 RID: 29458 RVA: 0x0002AE4F File Offset: 0x0002904F
		public int CardSacredBlessingLv4
		{
			get
			{
				return this.m_CardSacredBlessingLv4;
			}
			set
			{
				this.m_CardSacredBlessingLv4 = value;
			}
		}

		// Token: 0x17001394 RID: 5012
		// (get) Token: 0x06007313 RID: 29459 RVA: 0x00263844 File Offset: 0x00261A44
		// (set) Token: 0x06007314 RID: 29460 RVA: 0x0002AE59 File Offset: 0x00029059
		public int CardSacredShieldLv1
		{
			get
			{
				return this.m_CardSacredShieldLv1;
			}
			set
			{
				this.m_CardSacredShieldLv1 = value;
			}
		}

		// Token: 0x17001395 RID: 5013
		// (get) Token: 0x06007315 RID: 29461 RVA: 0x0026385C File Offset: 0x00261A5C
		// (set) Token: 0x06007316 RID: 29462 RVA: 0x0002AE63 File Offset: 0x00029063
		public int CardSacredShieldLv2
		{
			get
			{
				return this.m_CardSacredShieldLv2;
			}
			set
			{
				this.m_CardSacredShieldLv2 = value;
			}
		}

		// Token: 0x17001396 RID: 5014
		// (get) Token: 0x06007317 RID: 29463 RVA: 0x00263874 File Offset: 0x00261A74
		// (set) Token: 0x06007318 RID: 29464 RVA: 0x0002AE6D File Offset: 0x0002906D
		public int CardSacredShieldLv3
		{
			get
			{
				return this.m_CardSacredShieldLv3;
			}
			set
			{
				this.m_CardSacredShieldLv3 = value;
			}
		}

		// Token: 0x17001397 RID: 5015
		// (get) Token: 0x06007319 RID: 29465 RVA: 0x0026388C File Offset: 0x00261A8C
		// (set) Token: 0x0600731A RID: 29466 RVA: 0x0002AE77 File Offset: 0x00029077
		public int CardSacredShieldLv4
		{
			get
			{
				return this.m_CardSacredShieldLv4;
			}
			set
			{
				this.m_CardSacredShieldLv4 = value;
			}
		}

		// Token: 0x17001398 RID: 5016
		// (get) Token: 0x0600731B RID: 29467 RVA: 0x002638A4 File Offset: 0x00261AA4
		// (set) Token: 0x0600731C RID: 29468 RVA: 0x0002AE81 File Offset: 0x00029081
		public int CardAddSpellCountLv1
		{
			get
			{
				return this.m_CardAddSpellCountLv1;
			}
			set
			{
				this.m_CardAddSpellCountLv1 = value;
			}
		}

		// Token: 0x17001399 RID: 5017
		// (get) Token: 0x0600731D RID: 29469 RVA: 0x002638BC File Offset: 0x00261ABC
		// (set) Token: 0x0600731E RID: 29470 RVA: 0x0002AE8B File Offset: 0x0002908B
		public int CardAddSpellCountLv2
		{
			get
			{
				return this.m_CardAddSpellCountLv2;
			}
			set
			{
				this.m_CardAddSpellCountLv2 = value;
			}
		}

		// Token: 0x1700139A RID: 5018
		// (get) Token: 0x0600731F RID: 29471 RVA: 0x002638D4 File Offset: 0x00261AD4
		// (set) Token: 0x06007320 RID: 29472 RVA: 0x0002AE95 File Offset: 0x00029095
		public int CardAddSpellCountLv3
		{
			get
			{
				return this.m_CardAddSpellCountLv3;
			}
			set
			{
				this.m_CardAddSpellCountLv3 = value;
			}
		}

		// Token: 0x1700139B RID: 5019
		// (get) Token: 0x06007321 RID: 29473 RVA: 0x002638EC File Offset: 0x00261AEC
		// (set) Token: 0x06007322 RID: 29474 RVA: 0x0002AE9F File Offset: 0x0002909F
		public int CardAddSpellCountLv4
		{
			get
			{
				return this.m_CardAddSpellCountLv4;
			}
			set
			{
				this.m_CardAddSpellCountLv4 = value;
			}
		}

		// Token: 0x1700139C RID: 5020
		// (get) Token: 0x06007323 RID: 29475 RVA: 0x00263904 File Offset: 0x00261B04
		// (set) Token: 0x06007324 RID: 29476 RVA: 0x0002AEA9 File Offset: 0x000290A9
		public int WorldBossHP
		{
			get
			{
				return this.m_WorldBossHP;
			}
			set
			{
				this.m_WorldBossHP = value;
			}
		}

		// Token: 0x1700139D RID: 5021
		// (get) Token: 0x06007325 RID: 29477 RVA: 0x0026391C File Offset: 0x00261B1C
		// (set) Token: 0x06007326 RID: 29478 RVA: 0x0002AEB3 File Offset: 0x000290B3
		public int WorldBossHP_MoneyBuff
		{
			get
			{
				return this.m_WorldBossHP_MoneyBuff;
			}
			set
			{
				this.m_WorldBossHP_MoneyBuff = value;
			}
		}

		// Token: 0x1700139E RID: 5022
		// (get) Token: 0x06007327 RID: 29479 RVA: 0x00263934 File Offset: 0x00261B34
		// (set) Token: 0x06007328 RID: 29480 RVA: 0x0002AEBD File Offset: 0x000290BD
		public int WorldBossAttrack
		{
			get
			{
				return this.m_WorldBossAttrack;
			}
			set
			{
				this.m_WorldBossAttrack = value;
			}
		}

		// Token: 0x1700139F RID: 5023
		// (get) Token: 0x06007329 RID: 29481 RVA: 0x0026394C File Offset: 0x00261B4C
		// (set) Token: 0x0600732A RID: 29482 RVA: 0x0002AEC7 File Offset: 0x000290C7
		public int WorldBossAttrack_MoneyBuff
		{
			get
			{
				return this.m_WorldBossAttrack_MoneyBuff;
			}
			set
			{
				this.m_WorldBossAttrack_MoneyBuff = value;
			}
		}

		// Token: 0x170013A0 RID: 5024
		// (get) Token: 0x0600732B RID: 29483 RVA: 0x00263964 File Offset: 0x00261B64
		// (set) Token: 0x0600732C RID: 29484 RVA: 0x0002AED1 File Offset: 0x000290D1
		public int WorldBossMetalSlug
		{
			get
			{
				return this.m_WorldBossMetalSlug;
			}
			set
			{
				this.m_WorldBossMetalSlug = value;
			}
		}

		// Token: 0x170013A1 RID: 5025
		// (get) Token: 0x0600732D RID: 29485 RVA: 0x0026397C File Offset: 0x00261B7C
		// (set) Token: 0x0600732E RID: 29486 RVA: 0x0002AEDB File Offset: 0x000290DB
		public int WorldBossAncientBlessings
		{
			get
			{
				return this.m_WorldBossAncientBlessings;
			}
			set
			{
				this.m_WorldBossAncientBlessings = value;
			}
		}

		// Token: 0x170013A2 RID: 5026
		// (get) Token: 0x0600732F RID: 29487 RVA: 0x00263994 File Offset: 0x00261B94
		// (set) Token: 0x06007330 RID: 29488 RVA: 0x0002AEE5 File Offset: 0x000290E5
		public int WorldBossAddDamage
		{
			get
			{
				return this.m_WorldBossAddDamage;
			}
			set
			{
				this.m_WorldBossAddDamage = value;
			}
		}

		// Token: 0x0400434E RID: 17230
		private int m_ConsortionAddBloodGunCount;

		// Token: 0x0400434F RID: 17231
		private int m_ConsortionAddDamage;

		// Token: 0x04004350 RID: 17232
		private int m_ConsortionAddCritical;

		// Token: 0x04004351 RID: 17233
		private int m_ConsortionAddMaxBlood;

		// Token: 0x04004352 RID: 17234
		private int m_ConsortionAddProperty;

		// Token: 0x04004353 RID: 17235
		private int m_ConsortionReduceEnergyUse;

		// Token: 0x04004354 RID: 17236
		private int m_ConsortionAddEnergy;

		// Token: 0x04004355 RID: 17237
		private int m_ConsortionAddEffectTurn;

		// Token: 0x04004356 RID: 17238
		private int m_ConsortionAddOfferRate;

		// Token: 0x04004357 RID: 17239
		private int m_ConsortionAddPercentGoldOrGP;

		// Token: 0x04004358 RID: 17240
		private int m_ConsortionAddSpellCount;

		// Token: 0x04004359 RID: 17241
		private int m_ConsortionReduceDander;

		// Token: 0x0400435A RID: 17242
		private int m_CardAddMaxBloodLv1;

		// Token: 0x0400435B RID: 17243
		private int m_CardAddMaxBloodLv2;

		// Token: 0x0400435C RID: 17244
		private int m_CardAddMaxBloodLv3;

		// Token: 0x0400435D RID: 17245
		private int m_CardAddMaxBloodLv4;

		// Token: 0x0400435E RID: 17246
		private int m_CardTurnAddDanderLv1;

		// Token: 0x0400435F RID: 17247
		private int m_CardTurnAddDanderLv2;

		// Token: 0x04004360 RID: 17248
		private int m_CardTurnAddDanderLv3;

		// Token: 0x04004361 RID: 17249
		private int m_CardTurnAddDanderLv4;

		// Token: 0x04004362 RID: 17250
		private int m_CardAddDamageLv1;

		// Token: 0x04004363 RID: 17251
		private int m_CardAddDamageLv2;

		// Token: 0x04004364 RID: 17252
		private int m_CardAddDamageLv3;

		// Token: 0x04004365 RID: 17253
		private int m_CardAddDamageLv4;

		// Token: 0x04004366 RID: 17254
		private int m_CardAddCriticalLv1;

		// Token: 0x04004367 RID: 17255
		private int m_CardAddCriticalLv2;

		// Token: 0x04004368 RID: 17256
		private int m_CardAddCriticalLv3;

		// Token: 0x04004369 RID: 17257
		private int m_CardAddCriticalLv4;

		// Token: 0x0400436A RID: 17258
		private int m_CardExemptEnergyLv1;

		// Token: 0x0400436B RID: 17259
		private int m_CardExemptEnergyLv2;

		// Token: 0x0400436C RID: 17260
		private int m_CardExemptEnergyLv3;

		// Token: 0x0400436D RID: 17261
		private int m_CardExemptEnergyLv4;

		// Token: 0x0400436E RID: 17262
		private int m_CardAddDanderLv1;

		// Token: 0x0400436F RID: 17263
		private int m_CardAddDanderLv2;

		// Token: 0x04004370 RID: 17264
		private int m_CardAddDanderLv3;

		// Token: 0x04004371 RID: 17265
		private int m_CardAddDanderLv4;

		// Token: 0x04004372 RID: 17266
		private int m_CardAddPropertyLv1;

		// Token: 0x04004373 RID: 17267
		private int m_CardAddPropertyLv2;

		// Token: 0x04004374 RID: 17268
		private int m_CardAddPropertyLv3;

		// Token: 0x04004375 RID: 17269
		private int m_CardAddPropertyLv4;

		// Token: 0x04004376 RID: 17270
		private int m_CardReduceDamageLv1;

		// Token: 0x04004377 RID: 17271
		private int m_CardReduceDamageLv2;

		// Token: 0x04004378 RID: 17272
		private int m_CardReduceDamageLv3;

		// Token: 0x04004379 RID: 17273
		private int m_CardReduceDamageLv4;

		// Token: 0x0400437A RID: 17274
		private int m_CardAddPercentDamageLv1;

		// Token: 0x0400437B RID: 17275
		private int m_CardAddPercentDamageLv2;

		// Token: 0x0400437C RID: 17276
		private int m_CardAddPercentDamageLv3;

		// Token: 0x0400437D RID: 17277
		private int m_CardAddPercentDamageLv4;

		// Token: 0x0400437E RID: 17278
		private int m_CardSetDefaultDanderLv1;

		// Token: 0x0400437F RID: 17279
		private int m_CardSetDefaultDanderLv2;

		// Token: 0x04004380 RID: 17280
		private int m_CardSetDefaultDanderLv3;

		// Token: 0x04004381 RID: 17281
		private int m_CardSetDefaultDanderLv4;

		// Token: 0x04004382 RID: 17282
		private int m_CardReduceContinueDamageLv1;

		// Token: 0x04004383 RID: 17283
		private int m_CardReduceContinueDamageLv2;

		// Token: 0x04004384 RID: 17284
		private int m_CardReduceContinueDamageLv3;

		// Token: 0x04004385 RID: 17285
		private int m_CardReduceContinueDamageLv4;

		// Token: 0x04004386 RID: 17286
		private int m_CardDoNotMoveLv1;

		// Token: 0x04004387 RID: 17287
		private int m_CardDoNotMoveLv2;

		// Token: 0x04004388 RID: 17288
		private int m_CardDoNotMoveLv3;

		// Token: 0x04004389 RID: 17289
		private int m_CardDoNotMoveLv4;

		// Token: 0x0400438A RID: 17290
		private int m_CardAddPercentDefanceLv1;

		// Token: 0x0400438B RID: 17291
		private int m_CardAddPercentDefanceLv2;

		// Token: 0x0400438C RID: 17292
		private int m_CardAddPercentDefanceLv3;

		// Token: 0x0400438D RID: 17293
		private int m_CardAddPercentDefanceLv4;

		// Token: 0x0400438E RID: 17294
		private int m_CardReducePoisoningLv1;

		// Token: 0x0400438F RID: 17295
		private int m_CardReducePoisoningLv2;

		// Token: 0x04004390 RID: 17296
		private int m_CardReducePoisoningLv3;

		// Token: 0x04004391 RID: 17297
		private int m_CardReducePoisoningLv4;

		// Token: 0x04004392 RID: 17298
		private int m_CardAddBloodGunCountLv1;

		// Token: 0x04004393 RID: 17299
		private int m_CardAddBloodGunCountLv2;

		// Token: 0x04004394 RID: 17300
		private int m_CardAddBloodGunCountLv3;

		// Token: 0x04004395 RID: 17301
		private int m_CardAddBloodGunCountLv4;

		// Token: 0x04004396 RID: 17302
		private int m_CardResistAttackLv1;

		// Token: 0x04004397 RID: 17303
		private int m_CardResistAttackLv2;

		// Token: 0x04004398 RID: 17304
		private int m_CardResistAttackLv3;

		// Token: 0x04004399 RID: 17305
		private int m_CardResistAttackLv4;

		// Token: 0x0400439A RID: 17306
		private int m_CardSacredBlessingLv1;

		// Token: 0x0400439B RID: 17307
		private int m_CardSacredBlessingLv2;

		// Token: 0x0400439C RID: 17308
		private int m_CardSacredBlessingLv3;

		// Token: 0x0400439D RID: 17309
		private int m_CardSacredBlessingLv4;

		// Token: 0x0400439E RID: 17310
		private int m_CardSacredShieldLv1;

		// Token: 0x0400439F RID: 17311
		private int m_CardSacredShieldLv2;

		// Token: 0x040043A0 RID: 17312
		private int m_CardSacredShieldLv3;

		// Token: 0x040043A1 RID: 17313
		private int m_CardSacredShieldLv4;

		// Token: 0x040043A2 RID: 17314
		private int m_CardAddSpellCountLv1;

		// Token: 0x040043A3 RID: 17315
		private int m_CardAddSpellCountLv2;

		// Token: 0x040043A4 RID: 17316
		private int m_CardAddSpellCountLv3;

		// Token: 0x040043A5 RID: 17317
		private int m_CardAddSpellCountLv4;

		// Token: 0x040043A6 RID: 17318
		private int m_WorldBossHP;

		// Token: 0x040043A7 RID: 17319
		private int m_WorldBossHP_MoneyBuff;

		// Token: 0x040043A8 RID: 17320
		private int m_WorldBossAttrack;

		// Token: 0x040043A9 RID: 17321
		private int m_WorldBossMetalSlug;

		// Token: 0x040043AA RID: 17322
		private int m_WorldBossAttrack_MoneyBuff;

		// Token: 0x040043AB RID: 17323
		private int m_WorldBossAncientBlessings;

		// Token: 0x040043AC RID: 17324
		private int m_WorldBossAddDamage;
	}
}
