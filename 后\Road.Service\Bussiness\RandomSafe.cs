﻿using System;
using System.Security.Cryptography;

namespace Bussiness
{
	// Token: 0x02000FB7 RID: 4023
	public class RandomSafe : Random
	{
		// Token: 0x06008A2E RID: 35374 RVA: 0x00036360 File Offset: 0x00034560
		public RandomSafe()
			: base(RandomSafe.random())
		{
		}

		// Token: 0x06008A2F RID: 35375 RVA: 0x002F5700 File Offset: 0x002F3900
		public override int Next(int max)
		{
			return this.Next(0, max);
		}

		// Token: 0x06008A30 RID: 35376 RVA: 0x002F571C File Offset: 0x002F391C
		public override int Next(int min, int max)
		{
			int num = base.Next(1, 50);
			int num2 = max - 1;
			for (int i = 0; i < num; i++)
			{
				num2 = base.Next(min, max);
			}
			return num2;
		}

		// Token: 0x06008A31 RID: 35377 RVA: 0x002F575C File Offset: 0x002F395C
		public int NextSmallValue(int min, int max)
		{
			int num = Math.Abs(this.Next(min, max) - max);
			bool flag = num > max;
			if (flag)
			{
				num = max;
			}
			else
			{
				bool flag2 = num < min;
				if (flag2)
				{
					num = min;
				}
			}
			return num;
		}

		// Token: 0x06008A32 RID: 35378 RVA: 0x002F579C File Offset: 0x002F399C
		private static int random()
		{
			byte[] array = new byte[4];
			new RNGCryptoServiceProvider().GetBytes(array);
			return BitConverter.ToInt32(array, 0);
		}
	}
}
