﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E12 RID: 3602
	public class AE1238 : BasePetEffect
	{
		// Token: 0x06007E0F RID: 32271 RVA: 0x0029ECE4 File Offset: 0x0029CEE4
		public AE1238(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1238, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E10 RID: 32272 RVA: 0x0029ED64 File Offset: 0x0029CF64
		public override bool Start(Living living)
		{
			AE1238 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1238) as AE1238;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E11 RID: 32273 RVA: 0x00030B21 File Offset: 0x0002ED21
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E12 RID: 32274 RVA: 0x00030B37 File Offset: 0x0002ED37
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E13 RID: 32275 RVA: 0x0029EDC4 File Offset: 0x0029CFC4
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1238(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004CB6 RID: 19638
		private int m_type = 0;

		// Token: 0x04004CB7 RID: 19639
		private int m_count = 0;

		// Token: 0x04004CB8 RID: 19640
		private int m_probability = 0;

		// Token: 0x04004CB9 RID: 19641
		private int m_delay = 0;

		// Token: 0x04004CBA RID: 19642
		private int m_coldDown = 0;

		// Token: 0x04004CBB RID: 19643
		private int m_currentId;

		// Token: 0x04004CBC RID: 19644
		private int m_added = 0;
	}
}
