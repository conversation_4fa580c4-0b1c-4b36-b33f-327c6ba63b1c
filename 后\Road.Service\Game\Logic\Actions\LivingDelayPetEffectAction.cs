﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F55 RID: 3925
	public class LivingDelayPetEffectAction : BaseAction
	{
		// Token: 0x06008502 RID: 34050 RVA: 0x000353B2 File Offset: 0x000335B2
		public LivingDelayPetEffectAction(Living living, AbstractPetEffect effect, int delay)
			: base(delay)
		{
			this.m_effect = effect;
			this.m_living = living;
		}

		// Token: 0x06008503 RID: 34051 RVA: 0x000353CB File Offset: 0x000335CB
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			this.m_effect.Start(this.m_living);
			base.Finish(tick);
		}

		// Token: 0x040052ED RID: 21229
		private AbstractPetEffect m_effect;

		// Token: 0x040052EE RID: 21230
		private Living m_living;
	}
}
