﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D4C RID: 3404
	public class PetUseMPToAddBlood : BasePetEffect
	{
		// Token: 0x060079F4 RID: 31220 RVA: 0x0002E1CE File Offset: 0x0002C3CE
		public PetUseMPToAddBlood(int probability, int skilled, string elementID)
			: base(ePetEffectType.PetUseMPToAddBlood, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentid = skilled;
		}

		// Token: 0x060079F5 RID: 31221 RVA: 0x0028D10C File Offset: 0x0028B30C
		public override bool Start(Living living)
		{
			PetUseMPToAddBlood petUseMPToAddBlood = living.PetEffectList.GetOfType(ePetEffectType.PetUseMPToAddBlood) as PetUseMPToAddBlood;
			bool flag = petUseMPToAddBlood != null;
			bool flag2;
			if (flag)
			{
				petUseMPToAddBlood.m_probability = ((this.m_probability > petUseMPToAddBlood.m_probability) ? this.m_probability : petUseMPToAddBlood.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079F6 RID: 31222 RVA: 0x0002E1F7 File Offset: 0x0002C3F7
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079F7 RID: 31223 RVA: 0x0002E20D File Offset: 0x0002C40D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079F8 RID: 31224 RVA: 0x0028D16C File Offset: 0x0028B36C
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_currentid;
			if (flag)
			{
				int petMP = (living as Player).PetMP;
				(living as Player).RemovePetMP(petMP);
				int num = petMP / 3 * living.MaxBlood / 100;
				living.SyncAtTime = true;
				living.AddBlood(num);
				living.SyncAtTime = false;
			}
		}

		// Token: 0x04004772 RID: 18290
		private int m_probability;

		// Token: 0x04004773 RID: 18291
		private int m_currentid;
	}
}
