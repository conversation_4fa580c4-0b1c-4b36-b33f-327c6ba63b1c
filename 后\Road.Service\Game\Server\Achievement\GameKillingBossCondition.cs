﻿using System;
using System.Linq;
using Game.Logic;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C67 RID: 3175
	public class GameKillingBossCondition : BaseCondition
	{
		// Token: 0x06007090 RID: 28816 RVA: 0x0002A548 File Offset: 0x00028748
		public GameKillingBossCondition(BaseAchievement quest, AchievementConditionInfo info, int value, int[] arrayId)
			: base(quest, info, value)
		{
			this.int_1 = arrayId;
		}

		// Token: 0x06007091 RID: 28817 RVA: 0x0002A55D File Offset: 0x0002875D
		public override void AddTrigger(GamePlayer player)
		{
			player.GameKillDrop += this.player_GameKillDropEventHandel;
		}

		// Token: 0x06007092 RID: 28818 RVA: 0x00250160 File Offset: 0x0024E360
		private void player_GameKillDropEventHandel(AbstractGame game, int npcId, bool playResult)
		{
			bool flag = this.int_1.Contains(npcId);
			if (flag)
			{
				int value = base.Value;
				base.Value = value + 1;
			}
		}

		// Token: 0x06007093 RID: 28819 RVA: 0x0002A573 File Offset: 0x00028773
		public override void RemoveTrigger(GamePlayer player)
		{
			player.GameKillDrop -= this.player_GameKillDropEventHandel;
		}

		// Token: 0x06007094 RID: 28820 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}

		// Token: 0x04003C60 RID: 15456
		private int[] int_1;
	}
}
