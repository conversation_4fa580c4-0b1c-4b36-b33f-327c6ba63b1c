﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D65 RID: 3429
	public class PE1114 : BasePetEffect
	{
		// Token: 0x06007A90 RID: 31376 RVA: 0x0028F5F0 File Offset: 0x0028D7F0
		public PE1114(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1114, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A91 RID: 31377 RVA: 0x0028F66C File Offset: 0x0028D86C
		public override bool Start(Living living)
		{
			PE1114 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1114) as PE1114;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A92 RID: 31378 RVA: 0x0002E894 File Offset: 0x0002CA94
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
		}

		// Token: 0x06007A93 RID: 31379 RVA: 0x0028F6C8 File Offset: 0x0028D8C8
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = this.rand.Next(100) > 20;
			if (!flag)
			{
				this.m_added = 500;
				bool flag2 = this.m_added <= 0;
				if (!flag2)
				{
					target.SyncAtTime = true;
					target.AddBlood(-this.m_added, 1);
					target.SyncAtTime = false;
					bool flag3 = target.Blood <= 0;
					if (flag3)
					{
						target.Die();
						bool flag4 = living != null && living is Player;
						if (flag4)
						{
							(living as Player).PlayerDetail.OnKillingLiving(living.Game, 2, living.Id, living.IsLiving, this.m_added);
						}
					}
					else
					{
						target.AddPetEffect(new CE1114(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
					}
					living.PetEffects.ActiveEffect = false;
				}
			}
		}

		// Token: 0x06007A94 RID: 31380 RVA: 0x0002E8AA File Offset: 0x0002CAAA
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x04004803 RID: 18435
		private int m_type = 0;

		// Token: 0x04004804 RID: 18436
		private int m_count = 0;

		// Token: 0x04004805 RID: 18437
		private int m_probability = 0;

		// Token: 0x04004806 RID: 18438
		private int m_delay = 0;

		// Token: 0x04004807 RID: 18439
		private int m_coldDown = 0;

		// Token: 0x04004808 RID: 18440
		private int m_currentId;

		// Token: 0x04004809 RID: 18441
		private int m_added = 0;
	}
}
