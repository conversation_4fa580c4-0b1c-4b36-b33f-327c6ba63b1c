﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E28 RID: 3624
	public class AE1271 : BasePetEffect
	{
		// Token: 0x06007E84 RID: 32388 RVA: 0x002A0CF0 File Offset: 0x0029EEF0
		public AE1271(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1271, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E85 RID: 32389 RVA: 0x002A0D70 File Offset: 0x0029EF70
		public override bool Start(Living living)
		{
			AE1271 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1271) as AE1271;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E86 RID: 32390 RVA: 0x00030FF3 File Offset: 0x0002F1F3
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
		}

		// Token: 0x06007E87 RID: 32391 RVA: 0x0003097B File Offset: 0x0002EB7B
		private void Player_BeginSelfTurn(Living living)
		{
			this.IsTrigger = false;
		}

		// Token: 0x06007E88 RID: 32392 RVA: 0x002A0DD0 File Offset: 0x0029EFD0
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				target.AddPetEffect(new CE1271(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString(), living), 0);
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007E89 RID: 32393 RVA: 0x0003102F File Offset: 0x0002F22F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x06007E8A RID: 32394 RVA: 0x002A0E2C File Offset: 0x0029F02C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x04004D50 RID: 19792
		private int m_type = 0;

		// Token: 0x04004D51 RID: 19793
		private int m_count = 0;

		// Token: 0x04004D52 RID: 19794
		private int m_probability = 0;

		// Token: 0x04004D53 RID: 19795
		private int m_delay = 0;

		// Token: 0x04004D54 RID: 19796
		private int m_coldDown = 0;

		// Token: 0x04004D55 RID: 19797
		private int m_currentId;

		// Token: 0x04004D56 RID: 19798
		private int m_added = 0;
	}
}
