﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EAA RID: 3754
	public class CE1239 : BasePetEffect
	{
		// Token: 0x060081A2 RID: 33186 RVA: 0x002ADA38 File Offset: 0x002ABC38
		public CE1239(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081A3 RID: 33187 RVA: 0x002ADAB4 File Offset: 0x002ABCB4
		public override bool Start(Living living)
		{
			CE1239 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1239) as CE1239;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081A4 RID: 33188 RVA: 0x002ADB14 File Offset: 0x002ABD14
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 75;
				player.BaseDamage += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081A5 RID: 33189 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081A6 RID: 33190 RVA: 0x002ADB74 File Offset: 0x002ABD74
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060081A7 RID: 33191 RVA: 0x002ADBA8 File Offset: 0x002ABDA8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, false);
				player.BaseDamage -= (double)this.m_added;
				this.m_added = 0;
			}
		}

		// Token: 0x040050E3 RID: 20707
		private int m_type = 0;

		// Token: 0x040050E4 RID: 20708
		private int m_count = 0;

		// Token: 0x040050E5 RID: 20709
		private int m_probability = 0;

		// Token: 0x040050E6 RID: 20710
		private int m_delay = 0;

		// Token: 0x040050E7 RID: 20711
		private int m_coldDown = 0;

		// Token: 0x040050E8 RID: 20712
		private int m_currentId;

		// Token: 0x040050E9 RID: 20713
		private int m_added = 0;
	}
}
