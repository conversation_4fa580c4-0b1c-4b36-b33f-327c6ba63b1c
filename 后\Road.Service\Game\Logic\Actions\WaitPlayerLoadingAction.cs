﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F6B RID: 3947
	public class WaitPlayerLoadingAction : IAction
	{
		// Token: 0x06008534 RID: 34100 RVA: 0x00035746 File Offset: 0x00033946
		public WaitPlayerLoadingAction(BaseGame game, int maxTime)
		{
			this.m_time = TickHelper.GetTickCount() + (long)maxTime;
			game.GameStarted += this.game_GameStarted;
		}

		// Token: 0x06008535 RID: 34101 RVA: 0x00035771 File Offset: 0x00033971
		private void game_GameStarted(AbstractGame game)
		{
			game.GameStarted -= this.game_GameStarted;
			this.m_isFinished = true;
		}

		// Token: 0x06008536 RID: 34102 RVA: 0x002BA6B4 File Offset: 0x002B88B4
		public void Execute(BaseGame game, long tick)
		{
			bool flag = this.m_isFinished || tick <= this.m_time || game.GameState != eGameState.Loading;
			if (!flag)
			{
				bool flag2 = game.GameState == eGameState.Loading;
				if (flag2)
				{
					List<Player> allFightPlayers = game.GetAllFightPlayers();
					foreach (Player player in allFightPlayers)
					{
						bool flag3 = player.LoadingProcess < 100;
						if (flag3)
						{
							game.SendPlayerRemove(player);
							game.RemovePlayer(player.PlayerDetail, false);
						}
					}
					game.CheckState(0);
				}
				this.m_isFinished = true;
			}
		}

		// Token: 0x06008537 RID: 34103 RVA: 0x002BA778 File Offset: 0x002B8978
		public bool IsFinished(BaseGame game, long tick)
		{
			return this.m_isFinished;
		}

		// Token: 0x0400533F RID: 21311
		private long m_time;

		// Token: 0x04005340 RID: 21312
		private bool m_isFinished;
	}
}
