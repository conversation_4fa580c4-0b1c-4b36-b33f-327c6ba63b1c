﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E02 RID: 3586
	public class AE1209 : BasePetEffect
	{
		// Token: 0x06007DBD RID: 32189 RVA: 0x0029D5CC File Offset: 0x0029B7CC
		public AE1209(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1209, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DBE RID: 32190 RVA: 0x0029D64C File Offset: 0x0029B84C
		public override bool Start(Living living)
		{
			AE1209 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1209) as AE1209;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DBF RID: 32191 RVA: 0x0003080B File Offset: 0x0002EA0B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DC0 RID: 32192 RVA: 0x00030821 File Offset: 0x0002EA21
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DC1 RID: 32193 RVA: 0x0029D6AC File Offset: 0x0029B8AC
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.RemovePetMP(30);
					player2.AddPetEffect(new CE1209(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004C46 RID: 19526
		private int m_type = 0;

		// Token: 0x04004C47 RID: 19527
		private int m_count = 0;

		// Token: 0x04004C48 RID: 19528
		private int m_probability = 0;

		// Token: 0x04004C49 RID: 19529
		private int m_delay = 0;

		// Token: 0x04004C4A RID: 19530
		private int m_coldDown = 0;

		// Token: 0x04004C4B RID: 19531
		private int m_currentId;

		// Token: 0x04004C4C RID: 19532
		private int m_added = 0;
	}
}
