﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E64 RID: 3684
	public class CE1094 : BasePetEffect
	{
		// Token: 0x06007FE4 RID: 32740 RVA: 0x002A70DC File Offset: 0x002A52DC
		public CE1094(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1094, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FE5 RID: 32741 RVA: 0x002A715C File Offset: 0x002A535C
		public override bool Start(Living living)
		{
			CE1094 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1094) as CE1094;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FE6 RID: 32742 RVA: 0x002A71BC File Offset: 0x002A53BC
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.Defence += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007FE7 RID: 32743 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FE8 RID: 32744 RVA: 0x002A721C File Offset: 0x002A541C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007FE9 RID: 32745 RVA: 0x00031C93 File Offset: 0x0002FE93
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Defence -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004EF7 RID: 20215
		private int m_type = 0;

		// Token: 0x04004EF8 RID: 20216
		private int m_count = 0;

		// Token: 0x04004EF9 RID: 20217
		private int m_probability = 0;

		// Token: 0x04004EFA RID: 20218
		private int m_delay = 0;

		// Token: 0x04004EFB RID: 20219
		private int m_coldDown = 0;

		// Token: 0x04004EFC RID: 20220
		private int m_currentId;

		// Token: 0x04004EFD RID: 20221
		private int m_added = 0;
	}
}
