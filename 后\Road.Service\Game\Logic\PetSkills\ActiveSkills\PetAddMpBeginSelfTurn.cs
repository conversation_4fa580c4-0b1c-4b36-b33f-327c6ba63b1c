﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D29 RID: 3369
	public class PetAddMpBeginSelfTurn : BasePetEffect
	{
		// Token: 0x06007935 RID: 31029 RVA: 0x0002D5BC File Offset: 0x0002B7BC
		public PetAddMpBeginSelfTurn(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetAddMpBeginSelfTurn, elementID)
		{
			this.m_count = count;
			this.m_skillId = skillId;
			this.m_probability = ((probability == -1) ? 10000 : probability);
		}

		// Token: 0x06007936 RID: 31030 RVA: 0x00289734 File Offset: 0x00287934
		public override bool Start(Living living)
		{
			PetAddMpBeginSelfTurn petAddMpBeginSelfTurn = living.PetEffectList.GetOfType(ePetEffectType.PetAddMpBeginSelfTurn) as PetAddMpBeginSelfTurn;
			bool flag = petAddMpBeginSelfTurn != null;
			bool flag2;
			if (flag)
			{
				petAddMpBeginSelfTurn.m_probability = ((this.m_probability > petAddMpBeginSelfTurn.m_probability) ? this.m_probability : petAddMpBeginSelfTurn.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007937 RID: 31031 RVA: 0x0002D5ED File Offset: 0x0002B7ED
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x06007938 RID: 31032 RVA: 0x0002D603 File Offset: 0x0002B803
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x06007939 RID: 31033 RVA: 0x00289794 File Offset: 0x00287994
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_skillId;
			if (flag)
			{
				for (int i = 0; i < this.m_count; i++)
				{
					living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				}
				living.AddPetEffect(new PetAddMpBeginSelfTurnEquip(this.m_count, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004705 RID: 18181
		private int m_count;

		// Token: 0x04004706 RID: 18182
		private int m_skillId;

		// Token: 0x04004707 RID: 18183
		private int m_probability;
	}
}
