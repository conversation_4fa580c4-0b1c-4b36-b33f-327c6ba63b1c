﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D9E RID: 3486
	public class AE1034 : BasePetEffect
	{
		// Token: 0x06007BB4 RID: 31668 RVA: 0x00294280 File Offset: 0x00292480
		public AE1034(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1034, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BB5 RID: 31669 RVA: 0x002942FC File Offset: 0x002924FC
		public override bool Start(Living living)
		{
			AE1034 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1034) as AE1034;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BB6 RID: 31670 RVA: 0x0002F453 File Offset: 0x0002D653
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BB7 RID: 31671 RVA: 0x0002F469 File Offset: 0x0002D669
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BB8 RID: 31672 RVA: 0x00294358 File Offset: 0x00292558
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1034(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x0400498C RID: 18828
		private int m_type = 0;

		// Token: 0x0400498D RID: 18829
		private int m_count = 0;

		// Token: 0x0400498E RID: 18830
		private int m_probability = 0;

		// Token: 0x0400498F RID: 18831
		private int m_delay = 0;

		// Token: 0x04004990 RID: 18832
		private int m_coldDown = 0;

		// Token: 0x04004991 RID: 18833
		private int m_currentId;

		// Token: 0x04004992 RID: 18834
		private int m_added = 0;
	}
}
