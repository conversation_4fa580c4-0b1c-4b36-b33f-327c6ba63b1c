﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.GhostEffect
{
	// Token: 0x02000EC9 RID: 3785
	public class AddDefence : BasePetEffect
	{
		// Token: 0x0600826A RID: 33386 RVA: 0x002B0C74 File Offset: 0x002AEE74
		public AddDefence(string element)
			: base(ePetEffectType.EquipGhostSkill_AddDefence, element)
		{
			this.m_coldDown = 0;
			bool flag = element == null;
			if (!flag)
			{
				int length = element.Length;
				bool flag2 = length != 4;
				if (!flag2)
				{
					switch (element[3])
					{
					case '0':
					{
						bool flag3 = element == "2410";
						if (flag3)
						{
							this.m_value = 20;
						}
						break;
					}
					case '1':
					{
						bool flag4 = element == "2401";
						if (flag4)
						{
							this.m_value = 2;
						}
						break;
					}
					case '2':
					{
						bool flag5 = element == "2402";
						if (flag5)
						{
							this.m_value = 4;
						}
						break;
					}
					case '3':
					{
						bool flag6 = element == "2403";
						if (flag6)
						{
							this.m_value = 6;
						}
						break;
					}
					case '4':
					{
						bool flag7 = element == "2404";
						if (flag7)
						{
							this.m_value = 8;
						}
						break;
					}
					case '5':
					{
						bool flag8 = element == "2405";
						if (flag8)
						{
							this.m_value = 10;
						}
						break;
					}
					case '6':
					{
						bool flag9 = element == "2406";
						if (flag9)
						{
							this.m_value = 12;
						}
						break;
					}
					case '7':
					{
						bool flag10 = element == "2407";
						if (flag10)
						{
							this.m_value = 14;
						}
						break;
					}
					case '8':
					{
						bool flag11 = element == "2408";
						if (flag11)
						{
							this.m_value = 16;
						}
						break;
					}
					case '9':
					{
						bool flag12 = element == "2409";
						if (flag12)
						{
							this.m_value = 18;
						}
						break;
					}
					}
				}
			}
		}

		// Token: 0x0600826B RID: 33387 RVA: 0x002B0E48 File Offset: 0x002AF048
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.EquipGhostSkill_AddDefence) is AddDefence;
			return flag || base.Start(living);
		}

		// Token: 0x0600826C RID: 33388 RVA: 0x000334E1 File Offset: 0x000316E1
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
			player.AfterKilledByLiving += this.player_AfterKilledByLiving;
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x0600826D RID: 33389 RVA: 0x0003351D File Offset: 0x0003171D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.player_AfterKilledByLiving;
		}

		// Token: 0x0600826E RID: 33390 RVA: 0x002B0E84 File Offset: 0x002AF084
		private void player_BeforeTakeDamage(Living living, Living target, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.m_coldDown < 10;
			if (flag)
			{
				this.m_added += living.Defence * (double)this.m_value / 100.0;
				living.Defence += this.m_added;
				this.IsTrigger = true;
				this.m_coldDown++;
			}
		}

		// Token: 0x0600826F RID: 33391 RVA: 0x00033533 File Offset: 0x00031733
		private void player_BeginSelfTurn(Living living)
		{
			living.Defence -= this.m_added;
			this.m_added = 0.0;
			this.m_coldDown = 0;
		}

		// Token: 0x06008270 RID: 33392 RVA: 0x002B0EF0 File Offset: 0x002AF0F0
		private void player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				this.IsTrigger = false;
			}
		}

		// Token: 0x040051B8 RID: 20920
		private int m_coldDown = 0;

		// Token: 0x040051B9 RID: 20921
		private int m_value = 0;

		// Token: 0x040051BA RID: 20922
		private double m_added = 0.0;
	}
}
