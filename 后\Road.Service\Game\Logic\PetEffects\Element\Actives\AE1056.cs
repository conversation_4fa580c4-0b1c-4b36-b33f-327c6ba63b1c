﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DAE RID: 3502
	public class AE1056 : BasePetEffect
	{
		// Token: 0x06007C07 RID: 31751 RVA: 0x00295820 File Offset: 0x00293A20
		public AE1056(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1056, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C08 RID: 31752 RVA: 0x002958A0 File Offset: 0x00293AA0
		public override bool Start(Living living)
		{
			AE1056 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1056) as AE1056;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C09 RID: 31753 RVA: 0x0002F772 File Offset: 0x0002D972
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007C0A RID: 31754 RVA: 0x00295900 File Offset: 0x00293B00
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					this.m_added = 1500;
					player2.SyncAtTime = true;
					player2.AddBlood(this.m_added);
					player2.SyncAtTime = false;
				}
			}
		}

		// Token: 0x06007C0B RID: 31755 RVA: 0x0002F788 File Offset: 0x0002D988
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x040049FC RID: 18940
		private int m_type = 0;

		// Token: 0x040049FD RID: 18941
		private int m_count = 0;

		// Token: 0x040049FE RID: 18942
		private int m_probability = 0;

		// Token: 0x040049FF RID: 18943
		private int m_delay = 0;

		// Token: 0x04004A00 RID: 18944
		private int m_coldDown = 0;

		// Token: 0x04004A01 RID: 18945
		private int m_currentId;

		// Token: 0x04004A02 RID: 18946
		private int m_added = 0;
	}
}
