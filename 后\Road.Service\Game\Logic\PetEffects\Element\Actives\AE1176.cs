﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DE7 RID: 3559
	public class AE1176 : BasePetEffect
	{
		// Token: 0x06007D32 RID: 32050 RVA: 0x0029AC2C File Offset: 0x00298E2C
		public AE1176(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1176, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D33 RID: 32051 RVA: 0x0029ACAC File Offset: 0x00298EAC
		public override bool Start(Living living)
		{
			AE1176 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1176) as AE1176;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D34 RID: 32052 RVA: 0x000302E2 File Offset: 0x0002E4E2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007D35 RID: 32053 RVA: 0x0029AD0C File Offset: 0x00298F0C
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddPetEffect(new CE1176(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString(), player), 0);
				}
			}
		}

		// Token: 0x06007D36 RID: 32054 RVA: 0x000302F8 File Offset: 0x0002E4F8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004B89 RID: 19337
		private int m_type = 0;

		// Token: 0x04004B8A RID: 19338
		private int m_count = 0;

		// Token: 0x04004B8B RID: 19339
		private int m_probability = 0;

		// Token: 0x04004B8C RID: 19340
		private int m_delay = 0;

		// Token: 0x04004B8D RID: 19341
		private int m_coldDown = 0;

		// Token: 0x04004B8E RID: 19342
		private int m_currentId;

		// Token: 0x04004B8F RID: 19343
		private int m_added = 0;
	}
}
