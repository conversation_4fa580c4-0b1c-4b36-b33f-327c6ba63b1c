﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D4F RID: 3407
	public class xingling1 : BasePetEffect
	{
		// Token: 0x06007A03 RID: 31235 RVA: 0x0002E2AC File Offset: 0x0002C4AC
		public xingling1(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.xingling1, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentid = skillId;
			this.m_turnsRemaining = 0;
		}

		// Token: 0x06007A04 RID: 31236 RVA: 0x0028D4A0 File Offset: 0x0028B6A0
		public override bool Start(Living living)
		{
			xingling1 xingling = living.PetEffectList.GetOfType(ePetEffectType.xingling1) as xingling1;
			bool flag = xingling != null;
			bool flag2;
			if (flag)
			{
				xingling.m_probability = ((this.m_probability > xingling.m_probability) ? this.m_probability : xingling.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A05 RID: 31237 RVA: 0x0002E2DE File Offset: 0x0002C4DE
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
			player.PlayerAfterReset += this.player_PlayerAfterReset;
		}

		// Token: 0x06007A06 RID: 31238 RVA: 0x0002E307 File Offset: 0x0002C507
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
			player.PlayerAfterReset -= this.player_PlayerAfterReset;
		}

		// Token: 0x06007A07 RID: 31239 RVA: 0x0028D500 File Offset: 0x0028B700
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_currentid;
			if (flag)
			{
				Player player = living as Player;
				int petMP = player.PetMP;
				player.RemovePetMP(petMP);
				bool flag2 = petMP >= 50;
				if (flag2)
				{
					int num = (int)(living.BaseDamage * 0.05);
					living.SyncAtTime = true;
					living.ChangeDamage((double)num);
					living.SyncAtTime = false;
					living.Game.SendEquipEffect(living, "能量大于50，增加额外5%攻击属性！");
					this.m_turnsRemaining = 2;
				}
			}
		}

		// Token: 0x06007A08 RID: 31240 RVA: 0x0028D594 File Offset: 0x0028B794
		private void player_PlayerAfterReset(Player player)
		{
			Console.WriteLine("回合结束事件被触发");
			bool flag = this.m_turnsRemaining > 0;
			if (flag)
			{
				this.m_turnsRemaining--;
				Console.WriteLine(string.Format("剩余回合: {0}", this.m_turnsRemaining));
				bool flag2 = this.m_turnsRemaining == 0;
				if (flag2)
				{
					string text = "额外攻击属性效果已消失。";
					player.Game.MessageGame(player, text);
					Console.WriteLine("发送消息: " + text);
				}
			}
		}

		// Token: 0x0400477A RID: 18298
		private int m_probability;

		// Token: 0x0400477B RID: 18299
		private int m_currentid;

		// Token: 0x0400477C RID: 18300
		private int m_turnsRemaining;
	}
}
