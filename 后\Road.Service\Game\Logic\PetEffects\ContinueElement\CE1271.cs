﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EBC RID: 3772
	public class CE1271 : BasePetEffect
	{
		// Token: 0x06008218 RID: 33304 RVA: 0x002AF534 File Offset: 0x002AD734
		public CE1271(int count, int probability, int type, int skillId, int delay, string elementID, Living liv)
			: base(ePetEffectType.CE1271, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
			this.m_liv = liv;
		}

		// Token: 0x06008219 RID: 33305 RVA: 0x002AF5BC File Offset: 0x002AD7BC
		public override bool Start(Living living)
		{
			CE1271 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1271) as CE1271;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600821A RID: 33306 RVA: 0x0003327E File Offset: 0x0003147E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600821B RID: 33307 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600821C RID: 33308 RVA: 0x000332A7 File Offset: 0x000314A7
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x0600821D RID: 33309 RVA: 0x002AF61C File Offset: 0x002AD81C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 1000;
				living.SyncAtTime = true;
				living.AddBlood(-this.m_added, 1);
				living.SyncAtTime = false;
				bool flag2 = living.Blood <= 0;
				if (flag2)
				{
					living.Die();
					bool flag3 = this.m_liv != null && this.m_liv is Player;
					if (flag3)
					{
						(this.m_liv as Player).PlayerDetail.OnKillingLiving(this.m_liv.Game, 2, living.Id, living.IsLiving, this.m_added);
					}
				}
			}
		}

		// Token: 0x04005162 RID: 20834
		private int m_type = 0;

		// Token: 0x04005163 RID: 20835
		private int m_count = 0;

		// Token: 0x04005164 RID: 20836
		private int m_probability = 0;

		// Token: 0x04005165 RID: 20837
		private int m_delay = 0;

		// Token: 0x04005166 RID: 20838
		private int m_coldDown = 0;

		// Token: 0x04005167 RID: 20839
		private int m_currentId;

		// Token: 0x04005168 RID: 20840
		private int m_added = 0;

		// Token: 0x04005169 RID: 20841
		private Living m_liv;
	}
}
