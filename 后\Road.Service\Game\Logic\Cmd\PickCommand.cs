﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F0F RID: 3855
	[GameCommand(49, "战斗中拾取箱子")]
	public class PickCommand : ICommandHandler
	{
		// Token: 0x060083B7 RID: 33719 RVA: 0x000347ED File Offset: 0x000329ED
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			player.OpenBox(packet.ReadInt());
		}
	}
}
