﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F05 RID: 3845
	[GameCommand(23, "触发关卡事件")]
	public class GeneralCommand : ICommandHandler
	{
		// Token: 0x060083A3 RID: 33699 RVA: 0x002B50B4 File Offset: 0x002B32B4
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool flag = game is PVEGame;
			if (flag)
			{
				(game as PVEGame).GeneralCommand(packet);
			}
		}
	}
}
