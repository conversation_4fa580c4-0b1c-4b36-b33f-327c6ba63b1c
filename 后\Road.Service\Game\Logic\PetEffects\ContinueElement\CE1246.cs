﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EAE RID: 3758
	public class CE1246 : BasePetEffect
	{
		// Token: 0x060081BC RID: 33212 RVA: 0x002AE09C File Offset: 0x002AC29C
		public CE1246(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1246, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081BD RID: 33213 RVA: 0x002AE11C File Offset: 0x002AC31C
		public override bool Start(Living living)
		{
			CE1246 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1246) as CE1246;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081BE RID: 33214 RVA: 0x00032E5D File Offset: 0x0003105D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081BF RID: 33215 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081C0 RID: 33216 RVA: 0x002AE17C File Offset: 0x002AC37C
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			living.Game.SendPetBuff(living, base.ElementInfo, true);
			this.m_added = 650;
			damageAmount -= this.m_added;
			bool flag = damageAmount < 0;
			if (flag)
			{
				damageAmount = 1;
			}
		}

		// Token: 0x060081C1 RID: 33217 RVA: 0x002AE1C4 File Offset: 0x002AC3C4
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060081C2 RID: 33218 RVA: 0x00032E99 File Offset: 0x00031099
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
		}

		// Token: 0x040050FF RID: 20735
		private int m_type = 0;

		// Token: 0x04005100 RID: 20736
		private int m_count = 0;

		// Token: 0x04005101 RID: 20737
		private int m_probability = 0;

		// Token: 0x04005102 RID: 20738
		private int m_delay = 0;

		// Token: 0x04005103 RID: 20739
		private int m_coldDown = 0;

		// Token: 0x04005104 RID: 20740
		private int m_currentId;

		// Token: 0x04005105 RID: 20741
		private int m_added = 0;
	}
}
