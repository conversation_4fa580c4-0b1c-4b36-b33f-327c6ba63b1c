﻿using System;
using Game.Logic.Effects;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D0D RID: 3341
	public class PetIceImmunityEquip : BasePetEffect
	{
		// Token: 0x060078A1 RID: 30881 RVA: 0x0002CCDF File Offset: 0x0002AEDF
		public PetIceImmunityEquip(int count, string elementID)
			: base(ePetEffectType.PetIceImmunityEquip, elementID)
		{
			this.m_count = count;
		}

		// Token: 0x060078A2 RID: 30882 RVA: 0x00286FF8 File Offset: 0x002851F8
		public override bool Start(Living living)
		{
			PetIceImmunityEquip petIceImmunityEquip = living.PetEffectList.GetOfType(ePetEffectType.PetIceImmunityEquip) as PetIceImmunityEquip;
			bool flag = petIceImmunityEquip != null;
			bool flag2;
			if (flag)
			{
				petIceImmunityEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078A3 RID: 30883 RVA: 0x0002CCF6 File Offset: 0x0002AEF6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.player_BeginNextTurn;
			player.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x060078A4 RID: 30884 RVA: 0x0002CD1F File Offset: 0x0002AF1F
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
			player.BeginNextTurn -= this.player_BeginNextTurn;
		}

		// Token: 0x060078A5 RID: 30885 RVA: 0x00287040 File Offset: 0x00285240
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = base.ElementInfo.Pic != -1;
			if (flag)
			{
				living.Game.sendShowPicSkil(living, base.Info, false);
				living.Game.SendGameUpdateFrozenState(living);
			}
			bool flag2 = this.m_count < 0;
			if (flag2)
			{
				living.Game.sendShowPicSkil(living, base.Info, false);
				this.Stop();
			}
		}

		// Token: 0x060078A6 RID: 30886 RVA: 0x0002CD48 File Offset: 0x0002AF48
		private void player_BeginNextTurn(Living living)
		{
			living.EffectList.StopEffect(typeof(IceFronzeEffect));
		}

		// Token: 0x040046B3 RID: 18099
		private int m_count;
	}
}
