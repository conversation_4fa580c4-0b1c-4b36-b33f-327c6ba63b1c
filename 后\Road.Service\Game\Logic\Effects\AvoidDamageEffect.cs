﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EE0 RID: 3808
	public class AvoidDamageEffect : BasePlayerEffect
	{
		// Token: 0x060082ED RID: 33517 RVA: 0x00033D66 File Offset: 0x00031F66
		public AvoidDamageEffect(int count, int probability)
			: base(eEffectType.AvoidDamageEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x060082EE RID: 33518 RVA: 0x002B22BC File Offset: 0x002B04BC
		public override bool Start(Living living)
		{
			AvoidDamageEffect avoidDamageEffect = living.EffectList.GetOfType(eEffectType.AvoidDamageEffect) as AvoidDamageEffect;
			bool flag = avoidDamageEffect != null;
			bool flag2;
			if (flag)
			{
				avoidDamageEffect.m_probability = ((this.m_probability > avoidDamageEffect.m_probability) ? this.m_probability : avoidDamageEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082EF RID: 33519 RVA: 0x00033D8E File Offset: 0x00031F8E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
		}

		// Token: 0x060082F0 RID: 33520 RVA: 0x00033DA4 File Offset: 0x00031FA4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
		}

		// Token: 0x060082F1 RID: 33521 RVA: 0x002B2318 File Offset: 0x002B0518
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				this.IsTrigger = true;
				living.DefenceEffectTrigger = true;
				damageAmount = damageAmount * (100 - this.m_count) / 100;
				living.Game.AddAction(new LivingSayAction(living, LanguageMgr.GetTranslation("AvoidDamageEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051EB RID: 20971
		private int m_count = 0;

		// Token: 0x040051EC RID: 20972
		private int m_probability = 0;
	}
}
