﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D60 RID: 3424
	public class PE1106 : BasePetEffect
	{
		// Token: 0x06007A76 RID: 31350 RVA: 0x0028EFB4 File Offset: 0x0028D1B4
		public PE1106(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1106, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A77 RID: 31351 RVA: 0x0028F034 File Offset: 0x0028D234
		public override bool Start(Living living)
		{
			PE1106 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1106) as PE1106;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A78 RID: 31352 RVA: 0x0002E782 File Offset: 0x0002C982
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerShoot += this.player_Shooted;
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
		}

		// Token: 0x06007A79 RID: 31353 RVA: 0x0028F094 File Offset: 0x0028D294
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = this.m_currentId == 99;
			if (flag)
			{
				((Player)living).AddPetMP(1);
			}
		}

		// Token: 0x06007A7A RID: 31354 RVA: 0x0028F0C0 File Offset: 0x0028D2C0
		private void player_Shooted(Player player)
		{
			bool flag = this.m_currentId == 95 || this.m_currentId == 116;
			if (flag)
			{
				player.AddPetMP(1);
			}
		}

		// Token: 0x06007A7B RID: 31355 RVA: 0x0002E7AB File Offset: 0x0002C9AB
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerShoot -= this.player_Shooted;
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x040047E0 RID: 18400
		private int m_type = 0;

		// Token: 0x040047E1 RID: 18401
		private int m_count = 0;

		// Token: 0x040047E2 RID: 18402
		private int m_probability = 0;

		// Token: 0x040047E3 RID: 18403
		private int m_delay = 0;

		// Token: 0x040047E4 RID: 18404
		private int m_coldDown = 0;

		// Token: 0x040047E5 RID: 18405
		private int m_currentId;

		// Token: 0x040047E6 RID: 18406
		private int m_added = 0;
	}
}
