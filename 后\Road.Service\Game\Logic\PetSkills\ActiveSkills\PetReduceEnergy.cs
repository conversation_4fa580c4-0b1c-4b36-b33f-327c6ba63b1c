﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D42 RID: 3394
	public class PetReduceEnergy : BasePetEffect
	{
		// Token: 0x060079BF RID: 31167 RVA: 0x0002DE30 File Offset: 0x0002C030
		public PetReduceEnergy(int count, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetReduceEnergy, elementID)
		{
			this.m_count = count;
			this.m_skillId = skillId;
			this.m_probability = ((probability == -1) ? 10000 : probability);
		}

		// Token: 0x060079C0 RID: 31168 RVA: 0x0028C680 File Offset: 0x0028A880
		public override bool Start(Living living)
		{
			PetReduceEnergy petReduceEnergy = living.PetEffectList.GetOfType(ePetEffectType.PetReduceEnergy) as PetReduceEnergy;
			bool flag = petReduceEnergy != null;
			bool flag2;
			if (flag)
			{
				petReduceEnergy.m_probability = ((this.m_probability > petReduceEnergy.m_probability) ? this.m_probability : petReduceEnergy.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079C1 RID: 31169 RVA: 0x0002DE61 File Offset: 0x0002C061
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079C2 RID: 31170 RVA: 0x0002DE77 File Offset: 0x0002C077
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079C3 RID: 31171 RVA: 0x0028C6E0 File Offset: 0x0028A8E0
		private void player_PlayerBuffSkillPet(Living living_0)
		{
			bool flag = living_0.PetEffects.CurrentUseSkill == this.m_skillId;
			if (flag)
			{
				for (int i = 0; i < this.m_count; i++)
				{
					living_0.Game.sendShowPicSkil(living_0, base.ElementInfo, true);
				}
				living_0.AddPetEffect(new PetReduceEnergyEquip(this.m_count, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004755 RID: 18261
		private int m_count;

		// Token: 0x04004756 RID: 18262
		private int m_skillId;

		// Token: 0x04004757 RID: 18263
		private int m_probability;
	}
}
