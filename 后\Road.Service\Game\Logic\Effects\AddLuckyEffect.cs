﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000ED9 RID: 3801
	public class AddLuckyEffect : BasePlayerEffect
	{
		// Token: 0x060082C5 RID: 33477 RVA: 0x00033A4F File Offset: 0x00031C4F
		public AddLuckyEffect(int count, int probability)
			: base(eEffectType.AddLuckyEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
			this.m_added = 0;
		}

		// Token: 0x060082C6 RID: 33478 RVA: 0x002B1A04 File Offset: 0x002AFC04
		public override bool Start(Living living)
		{
			AddLuckyEffect addLuckyEffect = living.EffectList.GetOfType(eEffectType.AddLuckyEffect) as AddLuckyEffect;
			bool flag = addLuckyEffect != null;
			bool flag2;
			if (flag)
			{
				addLuckyEffect.m_probability = ((this.m_probability > addLuckyEffect.m_probability) ? this.m_probability : addLuckyEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082C7 RID: 33479 RVA: 0x00033A84 File Offset: 0x00031C84
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.ChangeProperty;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x060082C8 RID: 33480 RVA: 0x000335DB File Offset: 0x000317DB
		private void player_AfterPlayerShooted(Player player)
		{
			player.FlyingPartical = 0;
		}

		// Token: 0x060082C9 RID: 33481 RVA: 0x00033AAD File Offset: 0x00031CAD
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.ChangeProperty;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x060082CA RID: 33482 RVA: 0x002B1A60 File Offset: 0x002AFC60
		private void ChangeProperty(Player player, int ball)
		{
			player.Lucky -= (double)this.m_added;
			this.m_added = 0;
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				player.FlyingPartical = 65;
				this.IsTrigger = true;
				player.Lucky += (double)this.m_count;
				player.AttackEffectTrigger = true;
				this.m_added = this.m_count;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("AddLuckyEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051D9 RID: 20953
		private int m_count = 0;

		// Token: 0x040051DA RID: 20954
		private int m_probability = 0;

		// Token: 0x040051DB RID: 20955
		private int m_added = 0;
	}
}
