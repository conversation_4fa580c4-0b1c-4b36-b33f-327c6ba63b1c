﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E96 RID: 3734
	public class CE1203 : BasePetEffect
	{
		// Token: 0x06008120 RID: 33056 RVA: 0x002ABC20 File Offset: 0x002A9E20
		public CE1203(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1203, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008121 RID: 33057 RVA: 0x002ABCA0 File Offset: 0x002A9EA0
		public override bool Start(Living living)
		{
			CE1203 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1203) as CE1203;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008122 RID: 33058 RVA: 0x0003284E File Offset: 0x00030A4E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008123 RID: 33059 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008124 RID: 33060 RVA: 0x002ABD00 File Offset: 0x002A9F00
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				bool flag2 = living.BaseDamage < (double)this.m_added;
				if (flag2)
				{
					this.m_added = (int)living.BaseDamage - 1;
				}
				living.BaseDamage -= (double)this.m_added;
			}
		}

		// Token: 0x06008125 RID: 33061 RVA: 0x002ABD60 File Offset: 0x002A9F60
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008126 RID: 33062 RVA: 0x002ABD94 File Offset: 0x002A9F94
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BaseDamage += (double)this.m_added;
			this.m_added = 0;
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005057 RID: 20567
		private int m_type = 0;

		// Token: 0x04005058 RID: 20568
		private int m_count = 0;

		// Token: 0x04005059 RID: 20569
		private int m_probability = 0;

		// Token: 0x0400505A RID: 20570
		private int m_delay = 0;

		// Token: 0x0400505B RID: 20571
		private int m_coldDown = 0;

		// Token: 0x0400505C RID: 20572
		private int m_currentId;

		// Token: 0x0400505D RID: 20573
		private int m_added = 0;
	}
}
