﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F0A RID: 3850
	[GameCommand(9, "开始移动")]
	public class MoveStartCommand : ICommandHandler
	{
		// Token: 0x060083AC RID: 33708 RVA: 0x002B5208 File Offset: 0x002B3408
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool flag = !player.IsAttacking;
			if (!flag)
			{
				GSPacketIn gspacketIn = packet.Clone();
				gspacketIn.ClientID = player.PlayerDetail.PlayerCharacter.ID;
				gspacketIn.Parameter1 = player.Id;
				game.SendToAll(gspacketIn, player.PlayerDetail);
				byte b = packet.ReadByte();
				int num = packet.ReadInt();
				int num2 = packet.ReadInt();
				byte b2 = packet.ReadByte();
				bool flag2 = packet.ReadBoolean();
				byte b3 = b;
				byte b4 = b3;
				bool flag3 = b4 <= 1;
				if (flag3)
				{
					player.SetXY(num, num2);
					player.StartMoving();
					bool flag4 = player.Y - num2 > 1 || player.IsLiving != flag2;
					if (flag4)
					{
						Console.WriteLine("玩家移动掉落：" + player.IsLiving.ToString());
						game.SendPlayerMove(player, 3, player.X, player.Y, b2, flag2);
					}
				}
			}
		}
	}
}
