﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D13 RID: 3347
	public class PetReduceDamage1 : AbstractPetEffect
	{
		// Token: 0x060078C0 RID: 30912 RVA: 0x0002CE6B File Offset: 0x0002B06B
		public PetReduceDamage1(int count, int value)
			: base(ePetEffectType.PetReduceDamage1, "")
		{
			this.m_count = count;
			this.m_value = (double)value;
		}

		// Token: 0x060078C1 RID: 30913 RVA: 0x00287A2C File Offset: 0x00285C2C
		public override bool Start(Living living)
		{
			PetReduceDamage1 petReduceDamage = living.PetEffectList.GetOfType(ePetEffectType.PetReduceDamage1) as PetReduceDamage1;
			bool flag = petReduceDamage != null;
			bool flag2;
			if (flag)
			{
				petReduceDamage.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060078C2 RID: 30914 RVA: 0x00287A74 File Offset: 0x00285C74
		public override void OnAttached(Living living)
		{
			bool flag = this.m_added == 0.0;
			if (flag)
			{
				this.m_added = living.BaseDamage * this.m_value / 100.0;
				this.m_added = living.MagicAttack * this.m_value / 100.0;
				living.BaseDamage -= this.m_added;
				living.MagicAttack -= this.m_added;
				living.BeginSelfTurn += this.player_BeginSelfTurn;
			}
		}

		// Token: 0x060078C3 RID: 30915 RVA: 0x0002CE8E File Offset: 0x0002B08E
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x060078C4 RID: 30916 RVA: 0x00287B0C File Offset: 0x00285D0C
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count <= 0;
			if (flag)
			{
				living.BaseDamage += this.m_added;
				living.MagicAttack += this.m_added;
				this.m_added = 0.0;
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				living.Game.SendPlayerPicture(living, 29, false);
				this.Stop();
			}
		}

		// Token: 0x040046C4 RID: 18116
		private int m_count;

		// Token: 0x040046C5 RID: 18117
		private double m_value;

		// Token: 0x040046C6 RID: 18118
		private bool m_percent;

		// Token: 0x040046C7 RID: 18119
		private double m_added;
	}
}
