﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CC2 RID: 3266
	[SpellAttibute(11)]
	public class AttackUpSpell : ISpellHandler
	{
		// Token: 0x06007506 RID: 29958 RVA: 0x0026E674 File Offset: 0x0026C874
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.AddDander(item.Property2);
			}
			else
			{
				bool flag = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
				if (flag)
				{
					game.CurrentLiving.AddDander(item.Property2);
				}
			}
		}
	}
}
