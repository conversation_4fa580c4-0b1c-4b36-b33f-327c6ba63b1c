﻿using System;
using System.Reflection;
using System.Threading;
using Game.Base.Events;
using Game.Server;
using Game.Server.Packets;
using Game.Server.Packets.Client;
using log4net;

namespace Game.Base.Packets
{
	// Token: 0x02000F88 RID: 3976
	public class PacketProcessor
	{
		// Token: 0x06008766 RID: 34662 RVA: 0x00035D6B File Offset: 0x00033F6B
		public PacketProcessor(GameClient client)
		{
			this.Client = client;
		}

		// Token: 0x06008767 RID: 34663 RVA: 0x002C64F0 File Offset: 0x002C46F0
		public void HandlePacket(GSPacketIn packet)
		{
			int code = (int)packet.Code;
			Statistics.BytesIn += (long)packet.Length;
			Statistics.PacketsIn += 1L;
			IPacketHandler packetHandler = null;
			bool flag = code < PacketProcessor.PacketHandlers.Length;
			if (flag)
			{
				packetHandler = PacketProcessor.PacketHandlers[code];
				bool flag2 = packetHandler == null;
				if (flag2)
				{
					Console.WriteLine("______________ERROR______________");
					Console.WriteLine(string.Concat(new object[]
					{
						"PacketProcessor Received ePackageType.",
						(ePackageType)code,
						" <",
						string.Format("0x{0:x}", code),
						">"
					}));
					Console.WriteLine("_________________________________");
					return;
				}
			}
			else
			{
				bool isErrorEnabled = PacketProcessor.Log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ILog log = PacketProcessor.Log;
					string text = "Received packet code is outside of m_packetHandlers array bounds! ";
					GameClient client = this.Client;
					log.ErrorFormat(text + ((client != null) ? client.ToString() : null), Array.Empty<object>());
					PacketProcessor.Log.Error(Marshal.ToHexDump(string.Format("===> <{2}> Packet 0x{0:X2} (0x{1:X2}) length: {3} (ThreadId={4})", new object[]
					{
						code,
						code ^ 168,
						this.Client.TcpEndpoint,
						packet.Length,
						Thread.CurrentThread.ManagedThreadId
					}), packet.Buffer));
				}
			}
			bool flag3 = packetHandler == null;
			if (!flag3)
			{
				long num = (long)Environment.TickCount;
				try
				{
					bool flag4 = this.Client != null && this.Client.TcpEndpoint != "not connected";
					if (flag4)
					{
						bool debugMode = this.Client.Server.Configuration.DebugMode;
						if (debugMode)
						{
							int num2 = code;
							int num3 = num2;
							bool flag5 = num3 != 176 && num3 != 179 && num3 != 181;
							if (flag5)
							{
								Console.WriteLine(packetHandler);
							}
						}
						packetHandler.HandlePacket(this.Client, packet);
					}
				}
				catch (Exception ex)
				{
					bool isErrorEnabled2 = PacketProcessor.Log.IsErrorEnabled;
					if (isErrorEnabled2)
					{
						bool flag6 = this.Client != null;
						if (flag6)
						{
							string tcpEndpoint = this.Client.TcpEndpoint;
							PacketProcessor.Log.Error(string.Concat(new string[]
							{
								"Error while processing packet (handler=",
								packetHandler.GetType().FullName,
								"  client: ",
								tcpEndpoint,
								")"
							}), ex);
						}
						PacketProcessor.Log.Error(Marshal.ToHexDump("Package Buffer:", packet.Buffer, 0, packet.Length));
					}
				}
				long num4 = (long)Environment.TickCount - num;
				this.ActivePacketHandler = null;
				bool isDebugEnabled = PacketProcessor.Log.IsDebugEnabled;
				if (isDebugEnabled)
				{
					PacketProcessor.Log.Debug("Package process Time:" + num4.ToString() + "ms!");
				}
				bool flag7 = num4 > 1500L && this.Client != null;
				if (flag7)
				{
					string tcpEndpoint2 = this.Client.TcpEndpoint;
					bool isWarnEnabled = PacketProcessor.Log.IsWarnEnabled;
					if (isWarnEnabled)
					{
						PacketProcessor.Log.Warn(string.Concat(new string[]
						{
							"(",
							tcpEndpoint2,
							") Handle packet Thread ",
							Thread.CurrentThread.ManagedThreadId.ToString(),
							" ",
							(packetHandler != null) ? packetHandler.ToString() : null,
							" took ",
							num4.ToString(),
							"ms!"
						}));
					}
				}
			}
		}

		// Token: 0x06008768 RID: 34664 RVA: 0x002C68A8 File Offset: 0x002C4AA8
		[ScriptLoadedEvent]
		public static void OnScriptCompiled(RoadEvent ev, object sender, EventArgs args)
		{
			Array.Clear(PacketProcessor.PacketHandlers, 0, PacketProcessor.PacketHandlers.Length);
			int num = PacketProcessor.SearchPacketHandlers("v168", Assembly.GetAssembly(typeof(GameServer)));
			bool isInfoEnabled = PacketProcessor.Log.IsInfoEnabled;
			if (isInfoEnabled)
			{
				PacketProcessor.Log.Info("PacketProcessor: Loaded " + num.ToString() + " handlers from GameServer Assembly!");
			}
		}

		// Token: 0x06008769 RID: 34665 RVA: 0x00035D83 File Offset: 0x00033F83
		public static void RegisterPacketHandler(int packetCode, IPacketHandler handler)
		{
			PacketProcessor.PacketHandlers[packetCode] = handler;
		}

		// Token: 0x0600876A RID: 34666 RVA: 0x002C6914 File Offset: 0x002C4B14
		protected static int SearchPacketHandlers(string version, Assembly assembly)
		{
			int num = 0;
			Type[] types = assembly.GetTypes();
			Type[] array = types;
			Type[] array2 = array;
			foreach (Type type in array2)
			{
				bool flag = type.IsClass && !(type.GetInterface("Game.Server.Packets.Client.IPacketHandler") == null);
				if (flag)
				{
					PacketHandlerAttribute[] array4 = (PacketHandlerAttribute[])type.GetCustomAttributes(typeof(PacketHandlerAttribute), true);
					bool flag2 = array4.Length != 0;
					if (flag2)
					{
						num++;
						PacketProcessor.RegisterPacketHandler(array4[0].Code, (IPacketHandler)Activator.CreateInstance(type));
					}
				}
			}
			return num;
		}

		// Token: 0x0400539C RID: 21404
		private static readonly ILog Log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x0400539D RID: 21405
		protected IPacketHandler ActivePacketHandler;

		// Token: 0x0400539E RID: 21406
		protected int HandlerThreadId = 0;

		// Token: 0x0400539F RID: 21407
		protected GameClient Client;

		// Token: 0x040053A0 RID: 21408
		protected static readonly IPacketHandler[] PacketHandlers = new IPacketHandler[32767];
	}
}
