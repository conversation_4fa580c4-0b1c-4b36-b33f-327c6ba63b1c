﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D5F RID: 3423
	public class PE1081 : BasePetEffect
	{
		// Token: 0x06007A71 RID: 31345 RVA: 0x0028EE48 File Offset: 0x0028D048
		public PE1081(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1081, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A72 RID: 31346 RVA: 0x0028EEC4 File Offset: 0x0028D0C4
		public override bool Start(Living living)
		{
			PE1081 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1081) as PE1081;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A73 RID: 31347 RVA: 0x0002E756 File Offset: 0x0002C956
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerShootCure += this.Player_AfterPlayerShootCure;
		}

		// Token: 0x06007A74 RID: 31348 RVA: 0x0028EF20 File Offset: 0x0028D120
		private void Player_AfterPlayerShootCure(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.SyncAtTime = true;
					player2.AddBlood(600);
					player2.SyncAtTime = false;
				}
			}
		}

		// Token: 0x06007A75 RID: 31349 RVA: 0x0002E76C File Offset: 0x0002C96C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerShootCure -= this.Player_AfterPlayerShootCure;
		}

		// Token: 0x040047D9 RID: 18393
		private int m_type = 0;

		// Token: 0x040047DA RID: 18394
		private int m_count = 0;

		// Token: 0x040047DB RID: 18395
		private int m_probability = 0;

		// Token: 0x040047DC RID: 18396
		private int m_delay = 0;

		// Token: 0x040047DD RID: 18397
		private int m_coldDown = 0;

		// Token: 0x040047DE RID: 18398
		private int m_currentId;

		// Token: 0x040047DF RID: 18399
		private int m_added = 0;
	}
}
