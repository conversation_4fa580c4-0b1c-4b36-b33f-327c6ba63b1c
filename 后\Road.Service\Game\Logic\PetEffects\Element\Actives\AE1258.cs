﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E20 RID: 3616
	public class AE1258 : BasePetEffect
	{
		// Token: 0x06007E58 RID: 32344 RVA: 0x0029FFC4 File Offset: 0x0029E1C4
		public AE1258(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1258, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E59 RID: 32345 RVA: 0x002A0044 File Offset: 0x0029E244
		public override bool Start(Living living)
		{
			AE1258 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1258) as AE1258;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E5A RID: 32346 RVA: 0x00030DFB File Offset: 0x0002EFFB
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E5B RID: 32347 RVA: 0x00030E11 File Offset: 0x0002F011
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E5C RID: 32348 RVA: 0x002A00A4 File Offset: 0x0029E2A4
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddPetEffect(new CE1258(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004D18 RID: 19736
		private int m_type = 0;

		// Token: 0x04004D19 RID: 19737
		private int m_count = 0;

		// Token: 0x04004D1A RID: 19738
		private int m_probability = 0;

		// Token: 0x04004D1B RID: 19739
		private int m_delay = 0;

		// Token: 0x04004D1C RID: 19740
		private int m_coldDown = 0;

		// Token: 0x04004D1D RID: 19741
		private int m_currentId;

		// Token: 0x04004D1E RID: 19742
		private int m_added = 0;
	}
}
