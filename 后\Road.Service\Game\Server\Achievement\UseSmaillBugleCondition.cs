﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C75 RID: 3189
	public class UseSmaillBugleCondition : BaseCondition
	{
		// Token: 0x060070D2 RID: 28882 RVA: 0x0002A421 File Offset: 0x00028621
		public UseSmaillBugleCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x060070D3 RID: 28883 RVA: 0x0002A7D8 File Offset: 0x000289D8
		public override void AddTrigger(GamePlayer player)
		{
			player.AfterUsingItem += this.player_AfterUsingItem;
		}

		// Token: 0x060070D4 RID: 28884 RVA: 0x00250614 File Offset: 0x0024E814
		private void player_AfterUsingItem(int template)
		{
			bool flag = template == 11101;
			if (flag)
			{
				int value = base.Value;
				base.Value = value + 1;
			}
		}

		// Token: 0x060070D5 RID: 28885 RVA: 0x0002A7D8 File Offset: 0x000289D8
		public override void RemoveTrigger(GamePlayer player)
		{
			player.AfterUsingItem += this.player_AfterUsingItem;
		}

		// Token: 0x060070D6 RID: 28886 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
