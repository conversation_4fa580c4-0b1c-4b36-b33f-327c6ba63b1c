﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DD3 RID: 3539
	public class AE1132 : BasePetEffect
	{
		// Token: 0x06007CC7 RID: 31943 RVA: 0x00298F08 File Offset: 0x00297108
		public AE1132(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1132, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CC8 RID: 31944 RVA: 0x00298F88 File Offset: 0x00297188
		public override bool Start(Living living)
		{
			AE1132 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1132) as AE1132;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CC9 RID: 31945 RVA: 0x0002FE7B File Offset: 0x0002E07B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007CCA RID: 31946 RVA: 0x0002FE91 File Offset: 0x0002E091
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007CCB RID: 31947 RVA: 0x00298FE8 File Offset: 0x002971E8
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1132(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004AFD RID: 19197
		private int m_type = 0;

		// Token: 0x04004AFE RID: 19198
		private int m_count = 0;

		// Token: 0x04004AFF RID: 19199
		private int m_probability = 0;

		// Token: 0x04004B00 RID: 19200
		private int m_delay = 0;

		// Token: 0x04004B01 RID: 19201
		private int m_coldDown = 0;

		// Token: 0x04004B02 RID: 19202
		private int m_currentId;

		// Token: 0x04004B03 RID: 19203
		private int m_added = 0;
	}
}
