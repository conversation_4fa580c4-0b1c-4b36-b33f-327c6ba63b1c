﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E9D RID: 3741
	public class CE1210 : BasePetEffect
	{
		// Token: 0x0600814F RID: 33103 RVA: 0x002AC6DC File Offset: 0x002AA8DC
		public CE1210(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1210, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008150 RID: 33104 RVA: 0x002AC75C File Offset: 0x002AA95C
		public override bool Start(Living living)
		{
			CE1210 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1210) as CE1210;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008151 RID: 33105 RVA: 0x00032A20 File Offset: 0x00030C20
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008152 RID: 33106 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008153 RID: 33107 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginNextTurn(Living living)
		{
		}

		// Token: 0x06008154 RID: 33108 RVA: 0x002AC7BC File Offset: 0x002AA9BC
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			this.m_added = 500;
			living.AddBlood(-this.m_added, 1);
			bool flag2 = living.Blood <= 0;
			if (flag2)
			{
				living.Die();
				bool flag3 = living.Game.CurrentLiving != null && living.Game.CurrentLiving is Player;
				if (flag3)
				{
					(living.Game.CurrentLiving as Player).PlayerDetail.OnKillingLiving(living.Game, 2, living.Id, living.IsLiving, this.m_added);
				}
			}
		}

		// Token: 0x06008155 RID: 33109 RVA: 0x00032A5C File Offset: 0x00030C5C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005088 RID: 20616
		private int m_type = 0;

		// Token: 0x04005089 RID: 20617
		private int m_count = 0;

		// Token: 0x0400508A RID: 20618
		private int m_probability = 0;

		// Token: 0x0400508B RID: 20619
		private int m_delay = 0;

		// Token: 0x0400508C RID: 20620
		private int m_coldDown = 0;

		// Token: 0x0400508D RID: 20621
		private int m_currentId;

		// Token: 0x0400508E RID: 20622
		private int m_added = 0;
	}
}
