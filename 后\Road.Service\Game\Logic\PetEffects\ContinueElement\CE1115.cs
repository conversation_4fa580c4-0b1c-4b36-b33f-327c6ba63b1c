﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E6E RID: 3694
	public class CE1115 : BasePetEffect
	{
		// Token: 0x06008020 RID: 32800 RVA: 0x002A7EA8 File Offset: 0x002A60A8
		public CE1115(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1115, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008021 RID: 32801 RVA: 0x002A7F24 File Offset: 0x002A6124
		public override bool Start(Living living)
		{
			CE1115 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1115) as CE1115;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008022 RID: 32802 RVA: 0x002A7F80 File Offset: 0x002A6180
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
			this.IsTrigger = true;
		}

		// Token: 0x06008023 RID: 32803 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008024 RID: 32804 RVA: 0x002A7FD0 File Offset: 0x002A61D0
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = living != target && this.IsTrigger;
			if (flag)
			{
				this.m_added = 500;
				bool isLiving = living.IsLiving;
				if (isLiving)
				{
					living.SyncAtTime = true;
					living.AddBlood(this.m_added);
					living.SyncAtTime = false;
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x06008025 RID: 32805 RVA: 0x002A802C File Offset: 0x002A622C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			this.IsTrigger = true;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.m_added = 0;
				this.Stop();
			}
		}

		// Token: 0x06008026 RID: 32806 RVA: 0x00031E93 File Offset: 0x00030093
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x04004F3D RID: 20285
		private int m_type = 0;

		// Token: 0x04004F3E RID: 20286
		private int m_count = 0;

		// Token: 0x04004F3F RID: 20287
		private int m_probability = 0;

		// Token: 0x04004F40 RID: 20288
		private int m_delay = 0;

		// Token: 0x04004F41 RID: 20289
		private int m_coldDown = 0;

		// Token: 0x04004F42 RID: 20290
		private int m_currentId;

		// Token: 0x04004F43 RID: 20291
		private int m_added = 0;
	}
}
