﻿using System;
using System.Net;
using System.Net.Sockets;
using System.Reflection;
using System.Threading;
using log4net;

namespace Game.Base
{
	// Token: 0x02000F6E RID: 3950
	public class BaseConnector : BaseClient
	{
		// Token: 0x170014BA RID: 5306
		// (get) Token: 0x06008560 RID: 34144 RVA: 0x00035879 File Offset: 0x00033A79
		public IPEndPoint RemoteEP
		{
			get
			{
				return this._remoteEP;
			}
		}

		// Token: 0x06008561 RID: 34145 RVA: 0x002BAE10 File Offset: 0x002B9010
		public bool Connect()
		{
			try
			{
				this.m_sock = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
				this.m_readBufEnd = 0;
				this.m_sock.Connect(this._remoteEP);
				bool flag = !this.flag;
				if (flag)
				{
					BaseConnector.log.InfoFormat("Connected to {0}", this._remoteEP);
				}
			}
			catch
			{
				bool flag2 = !this.flag;
				if (flag2)
				{
					BaseConnector.log.ErrorFormat("Connect {0} failed!", this._remoteEP);
				}
				this.m_sock = null;
				return false;
			}
			this.OnConnect();
			base.ReceiveAsync();
			return true;
		}

		// Token: 0x06008562 RID: 34146 RVA: 0x002BAEC4 File Offset: 0x002B90C4
		private void TryReconnect()
		{
			bool flag = this.Connect();
			if (flag)
			{
				bool flag2 = this.timer != null;
				if (flag2)
				{
					this.timer.Dispose();
					this.timer = null;
				}
				base.ReceiveAsync();
			}
			else
			{
				bool flag3 = !this.flag;
				if (flag3)
				{
					BaseConnector.log.ErrorFormat("Reconnect {0} failed:", this._remoteEP);
					BaseConnector.log.ErrorFormat("Retry after {0} ms!", BaseConnector.RECONNECT_INTERVAL);
				}
				bool flag4 = this.timer == null;
				if (flag4)
				{
					this.timer = new Timer(new TimerCallback(BaseConnector.RetryTimerCallBack), this, -1, -1);
				}
				this.timer.Change(BaseConnector.RECONNECT_INTERVAL, -1);
			}
		}

		// Token: 0x06008563 RID: 34147 RVA: 0x002BAF84 File Offset: 0x002B9184
		private static void RetryTimerCallBack(object target)
		{
			BaseConnector baseConnector = target as BaseConnector;
			bool flag = baseConnector != null;
			if (flag)
			{
				baseConnector.TryReconnect();
			}
			else
			{
				BaseConnector.log.Error("BaseConnector retryconnect timer return NULL!");
			}
		}

		// Token: 0x06008564 RID: 34148 RVA: 0x00035881 File Offset: 0x00033A81
		public BaseConnector(string ip, int port, bool autoReconnect, byte[] readBuffer, byte[] sendBuffer)
			: base(readBuffer, sendBuffer)
		{
			this._remoteEP = new IPEndPoint(IPAddress.Parse(ip), port);
			this._autoReconnect = autoReconnect;
			this.e = new SocketAsyncEventArgs();
		}

		// Token: 0x06008565 RID: 34149 RVA: 0x000358B3 File Offset: 0x00033AB3
		public BaseConnector(string ip, int port, bool autoReconnect, byte[] readBuffer, byte[] sendBuffer, bool flag)
			: this(ip, port, autoReconnect, readBuffer, sendBuffer)
		{
			this.flag = flag;
		}

		// Token: 0x04005353 RID: 21331
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005354 RID: 21332
		private static readonly int RECONNECT_INTERVAL = 10000;

		// Token: 0x04005355 RID: 21333
		private SocketAsyncEventArgs e;

		// Token: 0x04005356 RID: 21334
		private IPEndPoint _remoteEP;

		// Token: 0x04005357 RID: 21335
		private bool _autoReconnect;

		// Token: 0x04005358 RID: 21336
		private Timer timer;

		// Token: 0x04005359 RID: 21337
		private bool flag;
	}
}
