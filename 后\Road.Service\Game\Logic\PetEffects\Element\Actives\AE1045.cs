﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DA7 RID: 3495
	public class AE1045 : BasePetEffect
	{
		// Token: 0x06007BE3 RID: 31715 RVA: 0x00294EC0 File Offset: 0x002930C0
		public AE1045(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1045, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BE4 RID: 31716 RVA: 0x00294F40 File Offset: 0x00293140
		public override bool Start(Living living)
		{
			AE1045 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1045) as AE1045;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BE5 RID: 31717 RVA: 0x0002F62B File Offset: 0x0002D82B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007BE6 RID: 31718 RVA: 0x00294FA0 File Offset: 0x002931A0
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1045(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007BE7 RID: 31719 RVA: 0x0002F641 File Offset: 0x0002D841
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x040049CB RID: 18891
		private int m_type = 0;

		// Token: 0x040049CC RID: 18892
		private int m_count = 0;

		// Token: 0x040049CD RID: 18893
		private int m_probability = 0;

		// Token: 0x040049CE RID: 18894
		private int m_delay = 0;

		// Token: 0x040049CF RID: 18895
		private int m_coldDown = 0;

		// Token: 0x040049D0 RID: 18896
		private int m_currentId;

		// Token: 0x040049D1 RID: 18897
		private int m_added = 0;
	}
}
