﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FE5 RID: 4069
	public class QuestMgr
	{
		// Token: 0x06008B3D RID: 35645 RVA: 0x002FB5C4 File Offset: 0x002F97C4
		public static bool Init()
		{
			return QuestMgr.ReLoad();
		}

		// Token: 0x06008B3E RID: 35646 RVA: 0x002FB5DC File Offset: 0x002F97DC
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, QuestInfo> dictionary = QuestMgr.LoadQuestInfoDb();
				Dictionary<int, List<QuestConditionInfo>> dictionary2 = QuestMgr.LoadQuestCondictionDb(dictionary);
				Dictionary<int, List<QuestAwardInfo>> dictionary3 = QuestMgr.LoadQuestGoodDb(dictionary);
				bool flag = dictionary.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, QuestInfo>>(ref QuestMgr.m_questinfo, dictionary);
					Interlocked.Exchange<Dictionary<int, List<QuestConditionInfo>>>(ref QuestMgr.m_questcondiction, dictionary2);
					Interlocked.Exchange<Dictionary<int, List<QuestAwardInfo>>>(ref QuestMgr.m_questgoods, dictionary3);
				}
				return true;
			}
			catch (Exception ex)
			{
				QuestMgr.log.Error("QuestMgr", ex);
			}
			return false;
		}

		// Token: 0x06008B3F RID: 35647 RVA: 0x002FB668 File Offset: 0x002F9868
		public static Dictionary<int, QuestInfo> LoadQuestInfoDb()
		{
			Dictionary<int, QuestInfo> dictionary = new Dictionary<int, QuestInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				QuestInfo[] allQuest = produceBussiness.GetALlQuest();
				QuestInfo[] array = allQuest;
				QuestInfo[] array2 = array;
				QuestInfo[] array3 = array2;
				foreach (QuestInfo questInfo in array3)
				{
					bool flag = !dictionary.ContainsKey(questInfo.ID);
					if (flag)
					{
						dictionary.Add(questInfo.ID, questInfo);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008B40 RID: 35648 RVA: 0x002FB704 File Offset: 0x002F9904
		public static Dictionary<int, List<QuestConditionInfo>> LoadQuestCondictionDb(Dictionary<int, QuestInfo> quests)
		{
			Dictionary<int, List<QuestConditionInfo>> dictionary = new Dictionary<int, List<QuestConditionInfo>>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				QuestConditionInfo[] allQuestCondiction = produceBussiness.GetAllQuestCondiction();
				using (Dictionary<int, QuestInfo>.ValueCollection.Enumerator enumerator = quests.Values.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						QuestInfo quest = enumerator.Current;
						IEnumerable<QuestConditionInfo> enumerable = allQuestCondiction.Where((QuestConditionInfo s) => s.QuestID == quest.ID);
						dictionary.Add(quest.ID, enumerable.ToList<QuestConditionInfo>());
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008B41 RID: 35649 RVA: 0x002FB7C8 File Offset: 0x002F99C8
		public static Dictionary<int, List<QuestAwardInfo>> LoadQuestGoodDb(Dictionary<int, QuestInfo> quests)
		{
			Dictionary<int, List<QuestAwardInfo>> dictionary = new Dictionary<int, List<QuestAwardInfo>>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				QuestAwardInfo[] allQuestGoods = produceBussiness.GetAllQuestGoods();
				using (Dictionary<int, QuestInfo>.ValueCollection.Enumerator enumerator = quests.Values.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						QuestInfo quest = enumerator.Current;
						IEnumerable<QuestAwardInfo> enumerable = allQuestGoods.Where((QuestAwardInfo s) => s.QuestID == quest.ID);
						dictionary.Add(quest.ID, enumerable.ToList<QuestAwardInfo>());
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008B42 RID: 35650 RVA: 0x002FB88C File Offset: 0x002F9A8C
		public static QuestInfo GetSingleQuest(int id)
		{
			bool flag = QuestMgr.m_questinfo.ContainsKey(id);
			QuestInfo questInfo;
			if (flag)
			{
				questInfo = QuestMgr.m_questinfo[id];
			}
			else
			{
				questInfo = null;
			}
			return questInfo;
		}

		// Token: 0x06008B43 RID: 35651 RVA: 0x002FB8C0 File Offset: 0x002F9AC0
		public static List<QuestAwardInfo> GetQuestGoods(QuestInfo info)
		{
			bool flag = QuestMgr.m_questgoods.ContainsKey(info.ID);
			List<QuestAwardInfo> list;
			if (flag)
			{
				list = QuestMgr.m_questgoods[info.ID];
			}
			else
			{
				list = null;
			}
			return list;
		}

		// Token: 0x06008B44 RID: 35652 RVA: 0x002FB8FC File Offset: 0x002F9AFC
		public static List<QuestConditionInfo> GetQuestCondiction(QuestInfo info)
		{
			bool flag = QuestMgr.m_questcondiction.ContainsKey(info.ID);
			List<QuestConditionInfo> list;
			if (flag)
			{
				list = QuestMgr.m_questcondiction[info.ID];
			}
			else
			{
				list = null;
			}
			return list;
		}

		// Token: 0x0400553A RID: 21818
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x0400553B RID: 21819
		private static Dictionary<int, QuestInfo> m_questinfo = new Dictionary<int, QuestInfo>();

		// Token: 0x0400553C RID: 21820
		private static Dictionary<int, List<QuestConditionInfo>> m_questcondiction = new Dictionary<int, List<QuestConditionInfo>>();

		// Token: 0x0400553D RID: 21821
		private static Dictionary<int, List<QuestAwardInfo>> m_questgoods = new Dictionary<int, List<QuestAwardInfo>>();
	}
}
