﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;

namespace Game.Manager
{
	// Token: 0x02000C77 RID: 3191
	internal class ConnectDataBase
	{
		// Token: 0x060070DC RID: 28892 RVA: 0x00250674 File Offset: 0x0024E874
		public static SqlConnection getSqlConnection()
		{
			SqlConnection sqlConnection = new SqlConnection();
			try
			{
				sqlConnection.ConnectionString = ConfigurationManager.AppSettings["conString"];
				sqlConnection.Open();
				sqlConnection.Close();
			}
			catch
			{
				throw new Exception("无法连接数据库服务器。");
			}
			return sqlConnection;
		}

		// Token: 0x060070DD RID: 28893 RVA: 0x002506D4 File Offset: 0x0024E8D4
		public static int GetNonQueryEffectedRow(string sqlstr)
		{
			SqlConnection sqlConnection = ConnectDataBase.getSqlConnection();
			int num;
			try
			{
				sqlConnection.Open();
				SqlCommand sqlCommand = new SqlCommand(sqlstr, sqlConnection);
				num = sqlCommand.ExecuteNonQuery();
			}
			catch (Exception ex)
			{
				throw new Exception(ex.ToString());
			}
			finally
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
			return num;
		}

		// Token: 0x060070DE RID: 28894 RVA: 0x00250740 File Offset: 0x0024E940
		public static DataSet GetDataSet(string sqlstr)
		{
			SqlConnection sqlConnection = ConnectDataBase.getSqlConnection();
			DataSet dataSet2;
			try
			{
				sqlConnection.Open();
				SqlDataAdapter sqlDataAdapter = new SqlDataAdapter(sqlstr, sqlConnection);
				DataSet dataSet = new DataSet();
				sqlDataAdapter.Fill(dataSet);
				dataSet2 = dataSet;
			}
			catch (Exception ex)
			{
				throw new Exception(ex.ToString());
			}
			finally
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
			return dataSet2;
		}

		// Token: 0x060070DF RID: 28895 RVA: 0x002507B4 File Offset: 0x0024E9B4
		public static DataTable GetQueryList(string sqlstr)
		{
			SqlConnection sqlConnection = ConnectDataBase.getSqlConnection();
			DataTable dataTable = new DataTable();
			try
			{
				sqlConnection.Open();
				SqlDataAdapter sqlDataAdapter = new SqlDataAdapter(sqlstr, sqlConnection);
				sqlDataAdapter.Fill(dataTable);
			}
			catch (Exception ex)
			{
				throw new Exception(ex.ToString());
			}
			finally
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
			return dataTable;
		}

		// Token: 0x060070E0 RID: 28896 RVA: 0x00250830 File Offset: 0x0024EA30
		public static DataTable GetBoxList()
		{
			string text = "SELECT * FROM [Shop_Goods_Box]";
			return ConnectDataBase.GetQueryList(text);
		}

		// Token: 0x060070E1 RID: 28897 RVA: 0x00250850 File Offset: 0x0024EA50
		public static DataTable GetDropList()
		{
			string text = "SELECT * FROM [Drop_Item]";
			return ConnectDataBase.GetQueryList(text);
		}

		// Token: 0x060070E2 RID: 28898 RVA: 0x00250870 File Offset: 0x0024EA70
		public static DataTable GetShopList()
		{
			string text = "SELECT ID,ShopID,TemplateID,BuyType,IsBind,Label,Beat,AUnit,APrice1,AValue1,BUnit,BPrice1,BValue1,CUnit,CPrice1,CValue1,LimitCount,IsCheap FROM [Shop]";
			return ConnectDataBase.GetQueryList(text);
		}

		// Token: 0x060070E3 RID: 28899 RVA: 0x00250890 File Offset: 0x0024EA90
		public static DataTable GetTreasureList()
		{
			string text = "SELECT * FROM [Treasure_Award]";
			return ConnectDataBase.GetQueryList(text);
		}
	}
}
