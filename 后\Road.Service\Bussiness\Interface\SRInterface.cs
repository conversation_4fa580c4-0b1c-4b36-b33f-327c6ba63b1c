﻿using System;
using Bussiness.WebLogin;

namespace Bussiness.Interface
{
	// Token: 0x02001003 RID: 4099
	public class SRInterface : BaseInterface
	{
		// Token: 0x06008BE5 RID: 35813 RVA: 0x002FECF8 File Offset: 0x002FCEF8
		public override bool GetUserSex(string name)
		{
			bool flag;
			try
			{
				PassPortSoapClient passPortSoapClient = new PassPortSoapClient();
				flag = passPortSoapClient.Get_UserSex(name).Value;
			}
			catch (Exception ex)
			{
				BaseInterface.log.Error("获取性别失败", ex);
				flag = true;
			}
			return flag;
		}
	}
}
