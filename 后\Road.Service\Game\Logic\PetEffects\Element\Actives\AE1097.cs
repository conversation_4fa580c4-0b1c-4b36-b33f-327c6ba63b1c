﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DC5 RID: 3525
	public class AE1097 : BasePetEffect
	{
		// Token: 0x06007C7C RID: 31868 RVA: 0x00297BE4 File Offset: 0x00295DE4
		public AE1097(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1097, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C7D RID: 31869 RVA: 0x00297C64 File Offset: 0x00295E64
		public override bool Start(Living living)
		{
			AE1097 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1097) as AE1097;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C7E RID: 31870 RVA: 0x0002FB08 File Offset: 0x0002DD08
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C7F RID: 31871 RVA: 0x0002FB1E File Offset: 0x0002DD1E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C80 RID: 31872 RVA: 0x00297CC4 File Offset: 0x00295EC4
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.AddPetEffect(new CE1097(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004A9D RID: 19101
		private int m_type = 0;

		// Token: 0x04004A9E RID: 19102
		private int m_count = 0;

		// Token: 0x04004A9F RID: 19103
		private int m_probability = 0;

		// Token: 0x04004AA0 RID: 19104
		private int m_delay = 0;

		// Token: 0x04004AA1 RID: 19105
		private int m_coldDown = 0;

		// Token: 0x04004AA2 RID: 19106
		private int m_currentId;

		// Token: 0x04004AA3 RID: 19107
		private int m_added = 0;
	}
}
