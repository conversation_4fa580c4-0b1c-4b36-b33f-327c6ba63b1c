﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DB4 RID: 3508
	public class AE1067 : BasePetEffect
	{
		// Token: 0x06007C25 RID: 31781 RVA: 0x00296138 File Offset: 0x00294338
		public AE1067(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1067, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C26 RID: 31782 RVA: 0x002961B8 File Offset: 0x002943B8
		public override bool Start(Living living)
		{
			AE1067 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1067) as AE1067;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C27 RID: 31783 RVA: 0x0002F87A File Offset: 0x0002DA7A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C28 RID: 31784 RVA: 0x0002F890 File Offset: 0x0002DA90
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C29 RID: 31785 RVA: 0x00296218 File Offset: 0x00294418
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && player.Game is PVPGame;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1067(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004A26 RID: 18982
		private int m_type = 0;

		// Token: 0x04004A27 RID: 18983
		private int m_count = 0;

		// Token: 0x04004A28 RID: 18984
		private int m_probability = 0;

		// Token: 0x04004A29 RID: 18985
		private int m_delay = 0;

		// Token: 0x04004A2A RID: 18986
		private int m_coldDown = 0;

		// Token: 0x04004A2B RID: 18987
		private int m_currentId;

		// Token: 0x04004A2C RID: 18988
		private int m_added = 0;
	}
}
