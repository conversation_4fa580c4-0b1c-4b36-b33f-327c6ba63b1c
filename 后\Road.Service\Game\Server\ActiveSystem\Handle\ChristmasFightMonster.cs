﻿using System;
using Bussiness;
using Game.Base.Packets;
using Game.Server.GameObjects;
using Game.Server.Rooms;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C52 RID: 3154
	[ActiveSystemHandleAttbute(22)]
	public class ChristmasFightMonster : IActiveSystemCommandHadler
	{
		// Token: 0x06007027 RID: 28711 RVA: 0x0024D870 File Offset: 0x0024BA70
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			BaseChristmasRoom christmasRoom = RoomMgr.ChristmasRoom;
			int num = packet.ReadInt();
			Console.WriteLine("monterId" + num.ToString());
			bool flag = Player.MainWeapon == null;
			bool flag2;
			if (flag)
			{
				Player.SendMessage(LanguageMgr.GetTranslation("Game.Server.SceneGames.NoEquip", Array.Empty<object>()));
				flag2 = false;
			}
			else
			{
				bool flag3 = !Player.Actives.AvailTime();
				if (flag3)
				{
					Player.SendMessage(LanguageMgr.GetTranslation("ActiveSystemHandler.Msg1", Array.Empty<object>()));
					flag2 = false;
				}
				else
				{
					bool flag4 = christmasRoom.SetFightMonter(num, Player.PlayerCharacter.ID);
					if (flag4)
					{
						ChrismasMonterInfo chrismasMonterInfo = christmasRoom.Monters[num];
						gspacketIn.WriteByte(22);
						gspacketIn.WriteByte(3);
						gspacketIn.WriteInt(num);
						gspacketIn.WriteInt(chrismasMonterInfo.state);
						christmasRoom.SendToALL(gspacketIn);
						switch (chrismasMonterInfo.type)
						{
						case 0:
							RoomMgr.CreateChristmasRoom(Player, 40001);
							break;
						case 1:
							RoomMgr.CreateChristmasRoom(Player, 40002);
							break;
						case 2:
							RoomMgr.CreateChristmasRoom(Player, 40003);
							break;
						}
					}
					flag2 = true;
				}
			}
			return flag2;
		}
	}
}
