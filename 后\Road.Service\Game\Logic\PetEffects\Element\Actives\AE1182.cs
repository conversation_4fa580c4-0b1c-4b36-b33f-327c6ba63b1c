﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DED RID: 3565
	public class AE1182 : BasePetEffect
	{
		// Token: 0x06007D50 RID: 32080 RVA: 0x0029B5E0 File Offset: 0x002997E0
		public AE1182(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1182, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D51 RID: 32081 RVA: 0x0029B660 File Offset: 0x00299860
		public override bool Start(Living living)
		{
			AE1182 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1182) as AE1182;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D52 RID: 32082 RVA: 0x000303EA File Offset: 0x0002E5EA
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D53 RID: 32083 RVA: 0x00030400 File Offset: 0x0002E600
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D54 RID: 32084 RVA: 0x0029B6C0 File Offset: 0x002998C0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					int num = 2;
					CE1181 ce = player2.PetEffectList.GetOfType(ePetEffectType.CE1181) as CE1181;
					CE1182 ce2 = player2.PetEffectList.GetOfType(ePetEffectType.CE1182) as CE1182;
					CE1183 ce3 = player2.PetEffectList.GetOfType(ePetEffectType.CE1183) as CE1183;
					bool flag2 = ce != null;
					if (flag2)
					{
						num = ce.Count;
						ce.Stop();
					}
					bool flag3 = ce2 != null;
					if (flag3)
					{
						num = ce2.Count;
						ce2.Stop();
					}
					bool flag4 = ce3 != null;
					if (flag4)
					{
						num = ce3.Count;
						ce3.Stop();
					}
					player2.AddPetEffect(new CE1182(num, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004BB3 RID: 19379
		private int m_type = 0;

		// Token: 0x04004BB4 RID: 19380
		private int m_count = 0;

		// Token: 0x04004BB5 RID: 19381
		private int m_probability = 0;

		// Token: 0x04004BB6 RID: 19382
		private int m_delay = 0;

		// Token: 0x04004BB7 RID: 19383
		private int m_coldDown = 0;

		// Token: 0x04004BB8 RID: 19384
		private int m_currentId;

		// Token: 0x04004BB9 RID: 19385
		private int m_added = 0;
	}
}
