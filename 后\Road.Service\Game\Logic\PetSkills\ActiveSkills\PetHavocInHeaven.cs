﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D3B RID: 3387
	public class PetHavocInHeaven : BasePetEffect
	{
		// Token: 0x06007998 RID: 31128 RVA: 0x0028B5E8 File Offset: 0x002897E8
		public PetHavocInHeaven(int probability, int skillId, string elementID)
			: base(ePetEffectType.PetHavocInHeaven, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
			bool flag = !(elementID == "70011");
			if (flag)
			{
				bool flag2 = elementID == "70012";
				if (flag2)
				{
					this.m_value = 150;
				}
			}
			else
			{
				this.m_value = 100;
			}
		}

		// Token: 0x06007999 RID: 31129 RVA: 0x0028B674 File Offset: 0x00289874
		public override bool Start(Living living)
		{
			PetHavocInHeaven petHavocInHeaven = living.PetEffectList.GetOfType(ePetEffectType.PetHavocInHeaven) as PetHavocInHeaven;
			bool flag = petHavocInHeaven != null;
			bool flag2;
			if (flag)
			{
				petHavocInHeaven.m_probability = ((this.m_probability > petHavocInHeaven.m_probability) ? this.m_probability : petHavocInHeaven.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600799A RID: 31130 RVA: 0x0002DC1B File Offset: 0x0002BE1B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
		}

		// Token: 0x0600799B RID: 31131 RVA: 0x0002DC31 File Offset: 0x0002BE31
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
		}

		// Token: 0x0600799C RID: 31132 RVA: 0x0028B6D4 File Offset: 0x002898D4
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				bool flag2 = false;
				foreach (Player player2 in allEnemyPlayers)
				{
					for (int i = 0; i < 5; i++)
					{
						player2.SyncAtTime = true;
						int num = this.MakeDamage(player, player2);
						num = num * this.m_value / 600;
						player2.AddBlood(-num, 1);
						player2.SyncAtTime = false;
						bool flag3 = player2.Blood <= 0 && !player2.KeepLife;
						if (flag3)
						{
							player2.Die();
							bool flag4 = player != null;
							if (flag4)
							{
								if (player != null)
								{
									player.OnAfterKillingLiving(player2, num, 0);
								}
							}
							flag2 = true;
						}
					}
				}
				SimpleNpc[] array = player.Game.FindAllNpc();
				foreach (SimpleNpc living in array)
				{
					for (int k = 0; k < 5; k++)
					{
						int num2 = this.MakeDamage(player, living);
						num2 = num2 * this.m_value / 100;
						living.RrmoveBlood(num2, 1);
					}
					bool flag5 = living.Blood <= 0;
					if (flag5)
					{
						living.Die();
						flag2 = true;
					}
				}
				foreach (Living living2 in player.Game.GetBossLivings())
				{
					for (int l = 0; l < 5; l++)
					{
						int num3 = this.MakeDamage(player, living2);
						num3 = num3 * this.m_value / 100;
						living2.RrmoveBlood(num3, 1);
					}
					bool flag6 = living2.Blood <= 0;
					if (flag6)
					{
						living2.Die();
						flag2 = true;
					}
				}
				bool flag7 = !flag2 || this.m_currentId != 276;
				if (!flag7)
				{
					List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
					foreach (Player player3 in allTeamPlayers)
					{
						player3.SyncAtTime = true;
						player3.AddBlood(player3.MaxBlood * 5 / 100);
						player3.SyncAtTime = false;
					}
				}
			}
		}

		// Token: 0x0600799D RID: 31133 RVA: 0x0028B9B0 File Offset: 0x00289BB0
		private int MakeDamage(Player player, Living target)
		{
			double num = player.BaseDamage;
			double num2 = target.BaseGuard;
			double num3 = target.Defence;
			double attack = player.Attack;
			bool flag = target.AddArmor && (target as Player).DeputyWeapon != null;
			if (flag)
			{
				int num4 = (int)target.getHertAddition((target as Player).DeputyWeapon);
				num2 += (double)num4;
				num3 += (double)num4;
			}
			bool ignoreArmor = player.IgnoreArmor;
			if (ignoreArmor)
			{
				num2 *= 0.6;
				num3 *= 0.6;
			}
			bool flag2 = player.AddDamage > 0;
			if (flag2)
			{
				num += num / 100.0 * (double)player.AddDamage;
			}
			bool flag3 = player.IgnoreGuard > 0 && !player.IgnoreArmor;
			if (flag3)
			{
				num3 -= num3 / 100.0 * (double)player.IgnoreGuard;
				num2 -= num2 / 100.0 * (double)player.IgnoreGuard;
			}
			double num5 = num * (1.0 + attack * 0.0005) * (1.0 - num2 / 12000.0) * (1.0 - num3 / 80000.0);
			double num6 = player.MagicAttack * 0.8 - target.MagicDefence * 0.7;
			bool flag4 = num6 <= 0.0;
			if (flag4)
			{
				num6 = 0.0;
			}
			num5 += num6;
			bool keepLife = target.KeepLife;
			int num7;
			if (keepLife)
			{
				num7 = 0;
			}
			else
			{
				bool flag5 = num5 <= 0.0;
				if (flag5)
				{
					num7 = 1;
				}
				else
				{
					num7 = (int)num5;
				}
			}
			return num7;
		}

		// Token: 0x0400473D RID: 18237
		private int m_probability = 0;

		// Token: 0x0400473E RID: 18238
		private int m_currentId;

		// Token: 0x0400473F RID: 18239
		private int m_value = 0;

		// Token: 0x04004740 RID: 18240
		private int m_fx;

		// Token: 0x04004741 RID: 18241
		private int m_tx;

		// Token: 0x04004742 RID: 18242
		private int BaseDistant = 500;
	}
}
