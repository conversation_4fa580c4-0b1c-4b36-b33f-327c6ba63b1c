﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DBE RID: 3518
	public class AE1090 : BasePetEffect
	{
		// Token: 0x06007C57 RID: 31831 RVA: 0x00297040 File Offset: 0x00295240
		public AE1090(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1090, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C58 RID: 31832 RVA: 0x002970C0 File Offset: 0x002952C0
		public override bool Start(Living living)
		{
			AE1090 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1090) as AE1090;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C59 RID: 31833 RVA: 0x0002F9AE File Offset: 0x0002DBAE
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBeginMoving += this.Player_PlayerBeginMoving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007C5A RID: 31834 RVA: 0x00297120 File Offset: 0x00295320
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007C5B RID: 31835 RVA: 0x00297150 File Offset: 0x00295350
		private void Player_PlayerBeginMoving(Player player)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				AE1082 ae = player.PetEffectList.GetOfType(ePetEffectType.AE1082) as AE1082;
				bool flag = ae != null;
				if (flag)
				{
					ae.Pause();
				}
				AE1083 ae2 = player.PetEffectList.GetOfType(ePetEffectType.AE1083) as AE1083;
				bool flag2 = ae2 != null;
				if (flag2)
				{
					ae2.Pause();
				}
				AE1084 ae3 = player.PetEffectList.GetOfType(ePetEffectType.AE1084) as AE1084;
				bool flag3 = ae3 != null;
				if (flag3)
				{
					ae3.Pause();
				}
				AE1085 ae4 = player.PetEffectList.GetOfType(ePetEffectType.AE1085) as AE1085;
				bool flag4 = ae4 != null;
				if (flag4)
				{
					ae4.Pause();
				}
				player.Game.SendPlayerPicture(player, 5, false);
				player.IsNoHole = false;
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007C5C RID: 31836 RVA: 0x0002F9D7 File Offset: 0x0002DBD7
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBeginMoving -= this.Player_PlayerBeginMoving;
		}

		// Token: 0x04004A6C RID: 19052
		private int m_type = 0;

		// Token: 0x04004A6D RID: 19053
		private int m_count = 0;

		// Token: 0x04004A6E RID: 19054
		private int m_probability = 0;

		// Token: 0x04004A6F RID: 19055
		private int m_delay = 0;

		// Token: 0x04004A70 RID: 19056
		private int m_coldDown = 0;

		// Token: 0x04004A71 RID: 19057
		private int m_currentId;

		// Token: 0x04004A72 RID: 19058
		private int m_added = 0;
	}
}
