﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D80 RID: 3456
	public class PE1265 : BasePetEffect
	{
		// Token: 0x06007B15 RID: 31509 RVA: 0x00291890 File Offset: 0x0028FA90
		public PE1265(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1265, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B16 RID: 31510 RVA: 0x00291910 File Offset: 0x0028FB10
		public override bool Start(Living living)
		{
			PE1265 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1265) as PE1265;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B17 RID: 31511 RVA: 0x0002ECF0 File Offset: 0x0002CEF0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007B18 RID: 31512 RVA: 0x00291970 File Offset: 0x0028FB70
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = living.PetEffects.AddDameValue != 0;
			if (!flag)
			{
				this.m_added = 200;
				List<Player> allTeamPlayers = living.Game.GetAllTeamPlayers(living);
				foreach (Player player in allTeamPlayers)
				{
					player.BaseDamage += (double)this.m_added;
					player.PetEffects.AddDameValue += this.m_added;
					player.Game.SendPetBuff(player, base.Info, true);
				}
			}
		}

		// Token: 0x06007B19 RID: 31513 RVA: 0x0002ED06 File Offset: 0x0002CF06
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x040048C0 RID: 18624
		private int m_type = 0;

		// Token: 0x040048C1 RID: 18625
		private int m_count = 0;

		// Token: 0x040048C2 RID: 18626
		private int m_probability = 0;

		// Token: 0x040048C3 RID: 18627
		private int m_delay = 0;

		// Token: 0x040048C4 RID: 18628
		private int m_coldDown = 0;

		// Token: 0x040048C5 RID: 18629
		private int m_currentId;

		// Token: 0x040048C6 RID: 18630
		private int m_added = 0;
	}
}
