﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EA5 RID: 3749
	public class CE1229 : BasePetEffect
	{
		// Token: 0x06008182 RID: 33154 RVA: 0x002AD334 File Offset: 0x002AB534
		public CE1229(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1229, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008183 RID: 33155 RVA: 0x002AD3B4 File Offset: 0x002AB5B4
		public override bool Start(Living living)
		{
			CE1229 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1229) as CE1229;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008184 RID: 33156 RVA: 0x00032C0F File Offset: 0x00030E0F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008185 RID: 33157 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008186 RID: 33158 RVA: 0x002AD414 File Offset: 0x002AB614
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			this.m_added = 1200;
			target.SyncAtTime = true;
			target.AddBlood(-this.m_added, 1);
			target.SyncAtTime = false;
			bool flag = target.Blood <= 0;
			if (flag)
			{
				target.Die();
				bool flag2 = living != null && living is Player;
				if (flag2)
				{
					(living as Player).PlayerDetail.OnKillingLiving(living.Game, 2, target.Id, target.IsLiving, this.m_added);
				}
			}
		}

		// Token: 0x06008187 RID: 33159 RVA: 0x002AD4A4 File Offset: 0x002AB6A4
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.m_added = 0;
				this.Stop();
			}
		}

		// Token: 0x06008188 RID: 33160 RVA: 0x00032C4B File Offset: 0x00030E4B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x040050C0 RID: 20672
		private int m_type = 0;

		// Token: 0x040050C1 RID: 20673
		private int m_count = 0;

		// Token: 0x040050C2 RID: 20674
		private int m_probability = 0;

		// Token: 0x040050C3 RID: 20675
		private int m_delay = 0;

		// Token: 0x040050C4 RID: 20676
		private int m_coldDown = 0;

		// Token: 0x040050C5 RID: 20677
		private int m_currentId;

		// Token: 0x040050C6 RID: 20678
		private int m_added = 0;
	}
}
