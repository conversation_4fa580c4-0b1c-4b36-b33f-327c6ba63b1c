﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F63 RID: 3939
	public class PhysicalObjDoAction : BaseAction
	{
		// Token: 0x06008520 RID: 34080 RVA: 0x000355B7 File Offset: 0x000337B7
		public PhysicalObjDoAction(PhysicalObj obj, string action, int delay, int movieTime)
			: base(delay, movieTime)
		{
			this.m_obj = obj;
			this.m_action = action;
		}

		// Token: 0x06008521 RID: 34081 RVA: 0x000355D2 File Offset: 0x000337D2
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			this.m_obj.CurrentAction = this.m_action;
			game.SendPhysicalObjPlayAction(this.m_obj);
			base.Finish(tick);
		}

		// Token: 0x0400532F RID: 21295
		private PhysicalObj m_obj;

		// Token: 0x04005330 RID: 21296
		private string m_action;
	}
}
