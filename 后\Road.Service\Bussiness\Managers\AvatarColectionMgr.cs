﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FC7 RID: 4039
	public class AvatarColectionMgr
	{
		// Token: 0x06008A76 RID: 35446 RVA: 0x002F70B4 File Offset: 0x002F52B4
		public static bool Init()
		{
			return AvatarColectionMgr.ReLoad();
		}

		// Token: 0x06008A77 RID: 35447 RVA: 0x002F70CC File Offset: 0x002F52CC
		public static bool ReLoad()
		{
			try
			{
				List<ClothPropertyTemplateInfo> list = AvatarColectionMgr.LoadClothPropertyTemplateDb();
				Dictionary<int, List<ClothGroupTemplateInfo>> dictionary = AvatarColectionMgr.LoadClothGroupTemplateInfoDb(list);
				bool flag = list.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<List<ClothPropertyTemplateInfo>>(ref AvatarColectionMgr.m_clothPropertyTemplateInfo, list);
					Interlocked.Exchange<Dictionary<int, List<ClothGroupTemplateInfo>>>(ref AvatarColectionMgr.m_clothGroupTemplateInfo, dictionary);
				}
				return true;
			}
			catch (Exception ex)
			{
				AvatarColectionMgr.log.Error("AvatarColectionMgr", ex);
			}
			return false;
		}

		// Token: 0x06008A78 RID: 35448 RVA: 0x002F7140 File Offset: 0x002F5340
		public static List<ClothPropertyTemplateInfo> LoadClothPropertyTemplateDb()
		{
			List<ClothPropertyTemplateInfo> list;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				ClothPropertyTemplateInfo[] allClothPropertyTemplateInfos = produceBussiness.GetAllClothPropertyTemplateInfos();
				ClothPropertyTemplateInfo[] array = allClothPropertyTemplateInfos;
				ClothPropertyTemplateInfo[] array2 = array;
				ClothPropertyTemplateInfo[] array3 = array2;
				foreach (ClothPropertyTemplateInfo clothPropertyTemplateInfo in array3)
				{
					bool flag = !AvatarColectionMgr.m_clothPropertyTemplates.ContainsKey(clothPropertyTemplateInfo.ID);
					if (flag)
					{
						AvatarColectionMgr.m_clothPropertyTemplates.Add(clothPropertyTemplateInfo.ID, clothPropertyTemplateInfo);
					}
				}
				list = ((allClothPropertyTemplateInfos != null) ? allClothPropertyTemplateInfos.ToList<ClothPropertyTemplateInfo>() : null);
			}
			return list;
		}

		// Token: 0x06008A79 RID: 35449 RVA: 0x002F71E0 File Offset: 0x002F53E0
		public static Dictionary<int, List<ClothGroupTemplateInfo>> LoadClothGroupTemplateInfoDb(List<ClothPropertyTemplateInfo> tempClothPropertyTemplates)
		{
			Dictionary<int, List<ClothGroupTemplateInfo>> dictionary = new Dictionary<int, List<ClothGroupTemplateInfo>>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				ClothGroupTemplateInfo[] allClothGroupTemplateInfos = produceBussiness.GetAllClothGroupTemplateInfos();
				using (List<ClothPropertyTemplateInfo>.Enumerator enumerator = tempClothPropertyTemplates.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						ClothPropertyTemplateInfo info = enumerator.Current;
						IEnumerable<ClothGroupTemplateInfo> enumerable = allClothGroupTemplateInfos.Where((ClothGroupTemplateInfo s) => s.ID == info.ID);
						dictionary.Add(info.ID, enumerable.ToList<ClothGroupTemplateInfo>());
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06008A7A RID: 35450 RVA: 0x002F72A0 File Offset: 0x002F54A0
		public static List<ClothGroupTemplateInfo> FindClothGroupTemplateInfo(int groupId)
		{
			bool flag = AvatarColectionMgr.m_clothGroupTemplateInfo.ContainsKey(groupId);
			List<ClothGroupTemplateInfo> list;
			if (flag)
			{
				list = AvatarColectionMgr.m_clothGroupTemplateInfo[groupId];
			}
			else
			{
				list = new List<ClothGroupTemplateInfo>();
			}
			return list;
		}

		// Token: 0x06008A7B RID: 35451 RVA: 0x002F72D8 File Offset: 0x002F54D8
		public static ClothGroupTemplateInfo FindClothGroupTemplateInfo(int groupId, int itemId, int sex)
		{
			bool flag = AvatarColectionMgr.m_clothGroupTemplateInfo.ContainsKey(groupId);
			if (flag)
			{
				foreach (ClothGroupTemplateInfo clothGroupTemplateInfo in AvatarColectionMgr.m_clothGroupTemplateInfo[groupId])
				{
					bool flag2 = (clothGroupTemplateInfo.TemplateID == itemId || clothGroupTemplateInfo.OtherTemplateID.Contains(itemId.ToString())) && clothGroupTemplateInfo.Sex == sex;
					if (flag2)
					{
						return clothGroupTemplateInfo;
					}
				}
			}
			return null;
		}

		// Token: 0x06008A7C RID: 35452 RVA: 0x002F737C File Offset: 0x002F557C
		public static List<ClothPropertyTemplateInfo> GetClothPropertyTemplate()
		{
			bool flag = AvatarColectionMgr.m_clothPropertyTemplateInfo != null;
			List<ClothPropertyTemplateInfo> list;
			if (flag)
			{
				list = AvatarColectionMgr.m_clothPropertyTemplateInfo;
			}
			else
			{
				list = null;
			}
			return list;
		}

		// Token: 0x06008A7D RID: 35453 RVA: 0x002F73A4 File Offset: 0x002F55A4
		public static ClothPropertyTemplateInfo FindClothPropertyTemplate(int dataId)
		{
			bool flag = AvatarColectionMgr.m_clothPropertyTemplates.ContainsKey(dataId);
			ClothPropertyTemplateInfo clothPropertyTemplateInfo;
			if (flag)
			{
				clothPropertyTemplateInfo = AvatarColectionMgr.m_clothPropertyTemplates[dataId];
			}
			else
			{
				clothPropertyTemplateInfo = null;
			}
			return clothPropertyTemplateInfo;
		}

		// Token: 0x040054DF RID: 21727
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040054E0 RID: 21728
		private static List<ClothPropertyTemplateInfo> m_clothPropertyTemplateInfo = new List<ClothPropertyTemplateInfo>();

		// Token: 0x040054E1 RID: 21729
		private static Dictionary<int, List<ClothGroupTemplateInfo>> m_clothGroupTemplateInfo = new Dictionary<int, List<ClothGroupTemplateInfo>>();

		// Token: 0x040054E2 RID: 21730
		private static Dictionary<int, ClothPropertyTemplateInfo> m_clothPropertyTemplates = new Dictionary<int, ClothPropertyTemplateInfo>();
	}
}
