﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DFD RID: 3581
	public class AE1204 : BasePetEffect
	{
		// Token: 0x06007DA4 RID: 32164 RVA: 0x0029CD50 File Offset: 0x0029AF50
		public AE1204(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1204, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DA5 RID: 32165 RVA: 0x0029CDD0 File Offset: 0x0029AFD0
		public override bool Start(Living living)
		{
			AE1204 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1204) as AE1204;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DA6 RID: 32166 RVA: 0x0003072F File Offset: 0x0002E92F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DA7 RID: 32167 RVA: 0x00030745 File Offset: 0x0002E945
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DA8 RID: 32168 RVA: 0x0029CE30 File Offset: 0x0029B030
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddPetEffect(new CE1204(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004C23 RID: 19491
		private int m_type = 0;

		// Token: 0x04004C24 RID: 19492
		private int m_count = 0;

		// Token: 0x04004C25 RID: 19493
		private int m_probability = 0;

		// Token: 0x04004C26 RID: 19494
		private int m_delay = 0;

		// Token: 0x04004C27 RID: 19495
		private int m_coldDown = 0;

		// Token: 0x04004C28 RID: 19496
		private int m_currentId;

		// Token: 0x04004C29 RID: 19497
		private int m_added = 0;
	}
}
