﻿using System;
using System.Collections.Generic;
using System.Linq;
using Bussiness.Managers;
using Game.Base.Packets;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Cmd
{
	// Token: 0x02000EFC RID: 3836
	[GameCommand(143, "战胜关卡中Boss翻牌")]
	public class BotCommand : ICommandHandler
	{
		// Token: 0x0600838A RID: 33674 RVA: 0x002B3FE0 File Offset: 0x002B21E0
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			PVPGame pvpgame = game as PVPGame;
			bool flag = pvpgame == null;
			if (!flag)
			{
				Player[] allPlayers = game.GetAllPlayers();
				List<Player> list = allPlayers.Where((Player child) => child.Team != player.Team).ToList<Player>();
				int num = game.Random.Next(0, list.Count);
				Player player2 = list.ElementAt(num);
				bool flag2 = player2.X > player.X;
				if (flag2)
				{
					player.ChangeDirection(1, 500);
				}
				else
				{
					player.ChangeDirection(-1, 500);
				}
				int num2 = game.Random.Next(0, 3);
				int num3 = 1;
				float num4 = 1f;
				int num5 = 1;
				int num6 = 1000;
				bool flag3 = Math.Abs(player.X - player2.X) > 60;
				int num10;
				int num11;
				if (flag3)
				{
					bool flag4 = num2 == 0;
					int num7;
					int num8;
					int num9;
					if (flag4)
					{
						num7 = 10002;
						num8 = 10004;
						num9 = 10004;
						num10 = player2.X;
						num11 = player2.Y;
					}
					else
					{
						bool flag5 = player2.X < player.X && player.X - player2.X > 200 && player.X - player2.X < 800;
						if (flag5)
						{
							num7 = 10001;
							num8 = 10003;
							num9 = 10004;
							num10 = player2.X + 20;
							num11 = player2.Y;
						}
						else
						{
							bool flag6 = Math.Abs(player.X - player2.X) > 1200 && player.flyCount <= 0;
							if (flag6)
							{
								num10 = game.Random.Next(100, game.Map.Bound.Width - 100);
								num7 = 0;
								num8 = 10010;
								num9 = 10016;
								num11 = player.X - 200;
							}
							else
							{
								num7 = 10001;
								num8 = 10004;
								num9 = 10004;
								num10 = player2.X;
								num11 = player2.Y;
							}
						}
					}
					bool flag7 = num7 != 0;
					ItemTemplateInfo itemTemplateInfo;
					if (flag7)
					{
						itemTemplateInfo = ItemMgr.FindItemTemplate(num7);
						bool flag8 = itemTemplateInfo != null && this.CanUseItem(player, itemTemplateInfo);
						if (flag8)
						{
							num3 = ((itemTemplateInfo.TemplateID == 10002) ? 2 : 3);
							game.AddAction(new BotBuffItemsAction(player, itemTemplateInfo, num6));
						}
					}
					bool flag9 = num8 != 0;
					if (flag9)
					{
						itemTemplateInfo = ItemMgr.FindItemTemplate(num8);
						bool flag10 = itemTemplateInfo != null && this.CanUseItem(player, itemTemplateInfo);
						if (flag10)
						{
							num5 = ((itemTemplateInfo.TemplateID != 10003) ? 1 : 3);
							num6 += 1000;
							game.AddAction(new BotBuffItemsAction(player, itemTemplateInfo, num6));
						}
					}
					itemTemplateInfo = ItemMgr.FindItemTemplate(num9);
					bool flag11 = itemTemplateInfo != null && this.CanUseItem(player, itemTemplateInfo);
					if (flag11)
					{
						num6 += 1000;
						bool flag12 = num9 == 10016;
						if (flag12)
						{
							player.SetBall(3);
						}
						game.AddAction(new BotBuffItemsAction(player, itemTemplateInfo, num6));
					}
					num4 = ((Math.Abs(player.X - player2.X) < 200) ? 1f : ((Math.Abs(player.X - player2.X) < 400) ? 1.5f : ((Math.Abs(player.X - player2.X) < 700) ? 2f : ((Math.Abs(player.X - player2.X) < 1000) ? 2.5f : ((Math.Abs(player.X - player2.X) >= 1100) ? 3.5f : 3f)))));
				}
				else
				{
					bool flag13 = num2 != 0 && player.flyCount <= 0;
					if (flag13)
					{
						num6 = 1000;
						num5 = 1;
						int num8 = 10010;
						int num9 = 10016;
						num3 = 1;
						num4 = 4f;
						num10 = game.Random.Next(100, game.Map.Bound.Width - 100);
						num11 = player.Y;
						ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(num8);
						bool flag14 = itemTemplateInfo != null && this.CanUseItem(player, itemTemplateInfo);
						if (flag14)
						{
							game.AddAction(new BotBuffItemsAction(player, itemTemplateInfo, num6));
						}
						itemTemplateInfo = ItemMgr.FindItemTemplate(num9);
						bool flag15 = itemTemplateInfo != null && this.CanUseItem(player, itemTemplateInfo);
						if (flag15)
						{
							num6 += 1000;
							bool flag16 = num9 == 10016;
							if (flag16)
							{
								player.SetBall(3);
							}
							game.AddAction(new BotBuffItemsAction(player, itemTemplateInfo, num6));
						}
					}
					else
					{
						num5 = 1;
						int num7 = 10001;
						int num8 = 10004;
						int num9 = 10004;
						num10 = player2.X;
						num11 = player2.Y;
						num3 = 1;
						num6 = 1000;
						ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(num7);
						bool flag17 = itemTemplateInfo != null && this.CanUseItem(player, itemTemplateInfo);
						if (flag17)
						{
							num3 = ((itemTemplateInfo.TemplateID != 10002) ? 1 : 3);
							game.AddAction(new BotBuffItemsAction(player, itemTemplateInfo, num6));
						}
						itemTemplateInfo = ItemMgr.FindItemTemplate(num8);
						bool flag18 = itemTemplateInfo != null && this.CanUseItem(player, itemTemplateInfo);
						if (flag18)
						{
							num6 += 1000;
							game.AddAction(new BotBuffItemsAction(player, itemTemplateInfo, num6));
						}
						itemTemplateInfo = ItemMgr.FindItemTemplate(num9);
						bool flag19 = itemTemplateInfo != null && this.CanUseItem(player, itemTemplateInfo);
						if (flag19)
						{
							num6 += 1000;
							game.AddAction(new BotBuffItemsAction(player, itemTemplateInfo, num6));
						}
						bool flag20 = player2.PlayerDetail.PlayerCharacter.Grade > 30;
						if (flag20)
						{
							itemTemplateInfo = ItemMgr.FindItemTemplate(10017);
							num6 += 1000;
							game.AddAction(new BotBuffItemsAction(player, itemTemplateInfo, num6));
						}
					}
				}
				for (int i = 0; i < num3; i++)
				{
					num6 += 1000;
					game.AddAction(new BotShootAction(player, num10, num11, 0, 0, num5, 1001, 10001, num4, num6));
				}
				bool isAttacking = player.IsAttacking;
				if (isAttacking)
				{
					player.StopAttacking();
				}
				GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
				gspacketIn.WriteByte(143);
				pvpgame.SendToAll(gspacketIn);
			}
		}

		// Token: 0x0600838B RID: 33675 RVA: 0x002B476C File Offset: 0x002B296C
		private bool CanUseItem(Player player, ItemTemplateInfo item)
		{
			bool flag = player.Energy >= item.Property4;
			bool flag2;
			if (flag)
			{
				player.Energy -= item.Property4;
				flag2 = true;
			}
			else
			{
				flag2 = false;
			}
			return flag2;
		}
	}
}
