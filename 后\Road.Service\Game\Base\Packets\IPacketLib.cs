﻿using System;
using System.Collections.Generic;
using Game.Server.Buffer;
using Game.Server.ConsortiaTask;
using Game.Server.GameObjects;
using Game.Server.GameUtils;
using Game.Server.GMActives;
using Game.Server.Packets;
using Game.Server.Quests;
using Game.Server.Rooms;
using Game.Server.SceneMarryRooms;
using Game.Server.SpaRooms;
using SqlDataProvider.Data;

namespace Game.Base.Packets
{
	// Token: 0x02000F86 RID: 3974
	public interface IPacketLib
	{
		// Token: 0x060086C5 RID: 34501
		GSPacketIn SendZhanling(GamePlayer player);

		// Token: 0x060086C6 RID: 34502
		void SendTCP(GSPacketIn packet);

		// Token: 0x060086C7 RID: 34503
		GSPacketIn SendUserRelicItem(PlayerInfo player);

		// Token: 0x060086C8 RID: 34504
		void SendLoginSuccess();

		// Token: 0x060086C9 RID: 34505
		void SendCheckCode();

		// Token: 0x060086CA RID: 34506
		void SendLoginFailed(string msg);

		// Token: 0x060086CB RID: 34507
		void SendKitoff(string msg);

		// Token: 0x060086CC RID: 34508
		void SendEditionError(string msg);

		// Token: 0x060086CD RID: 34509
		void SendDateTime();

		// Token: 0x060086CE RID: 34510
		void SendFirstKillOpen();

		// Token: 0x060086CF RID: 34511
		GSPacketIn SendDailyAward(GamePlayer player, int type);

		// Token: 0x060086D0 RID: 34512
		void SendPingTime(GamePlayer player);

		// Token: 0x060086D1 RID: 34513
		void SendUpdatePrivateInfo(PlayerInfo info, int medal);

		// Token: 0x060086D2 RID: 34514
		GSPacketIn SendUpdatePublicPlayer(PlayerInfo info);

		// Token: 0x060086D3 RID: 34515
		GSPacketIn SendNetWork(int id, long delay);

		// Token: 0x060086D4 RID: 34516
		GSPacketIn SendUserEquip(PlayerInfo info, List<ItemInfo> items);

		// Token: 0x060086D5 RID: 34517
		GSPacketIn SendMessage(eMessageType type, string message);

		// Token: 0x060086D6 RID: 34518
		void SendWaitingRoom(bool result);

		// Token: 0x060086D7 RID: 34519
		GSPacketIn SendUpdateRoomList(List<BaseRoom> room);

		// Token: 0x060086D8 RID: 34520
		GSPacketIn SendSceneAddPlayer(GamePlayer player);

		// Token: 0x060086D9 RID: 34521
		GSPacketIn SendSceneRemovePlayer(GamePlayer player);

		// Token: 0x060086DA RID: 34522
		GSPacketIn SendRoomCreate(BaseRoom room);

		// Token: 0x060086DB RID: 34523
		GSPacketIn SendRoomLoginResult(bool result);

		// Token: 0x060086DC RID: 34524
		GSPacketIn SendRoomPlayerAdd(GamePlayer player);

		// Token: 0x060086DD RID: 34525
		GSPacketIn SendRoomPlayerRemove(GamePlayer player);

		// Token: 0x060086DE RID: 34526
		GSPacketIn SendRoomUpdatePlayerStates(byte[] states);

		// Token: 0x060086DF RID: 34527
		GSPacketIn SendRoomUpdatePlacesStates(int[] states);

		// Token: 0x060086E0 RID: 34528
		GSPacketIn SendRoomPlayerChangedTeam(GamePlayer player);

		// Token: 0x060086E1 RID: 34529
		GSPacketIn SendRoomPairUpStart(BaseRoom room);

		// Token: 0x060086E2 RID: 34530
		GSPacketIn SendRoomPairUpCancel(BaseRoom room);

		// Token: 0x060086E3 RID: 34531
		GSPacketIn SendEquipChange(GamePlayer player, int place, int goodsID, string style);

		// Token: 0x060086E4 RID: 34532
		GSPacketIn SendGameRoomSetupChange(BaseRoom room);

		// Token: 0x060086E5 RID: 34533
		GSPacketIn SendFusionPreview(GamePlayer player, Dictionary<int, double> previewItemList, bool isBind, int MinValid);

		// Token: 0x060086E6 RID: 34534
		GSPacketIn SendFusionResult(GamePlayer player, bool result);

		// Token: 0x060086E7 RID: 34535
		GSPacketIn SendRefineryPreview(GamePlayer player, int templateid, bool isbind, ItemInfo item);

		// Token: 0x060086E8 RID: 34536
		void SendUpdateInventorySlot(PlayerInventory bag, int[] updatedSlots);

		// Token: 0x060086E9 RID: 34537
		GSPacketIn SendAddFriend(PlayerInfo user, int relation, bool state);

		// Token: 0x060086EA RID: 34538
		GSPacketIn SendFriendRemove(int FriendID);

		// Token: 0x060086EB RID: 34539
		GSPacketIn SendFriendState(int playerID, int state, byte typeVip, int viplevel);

		// Token: 0x060086EC RID: 34540
		GSPacketIn SendOneOnOneTalk(int receiverID, bool isAutoReply, string SenderNickName, string msg, int playerid);

		// Token: 0x060086ED RID: 34541
		GSPacketIn SendUpdateBuffer(GamePlayer player, AbstractBuffer[] infos);

		// Token: 0x060086EE RID: 34542
		GSPacketIn SendBufferList(GamePlayer player, List<AbstractBuffer> infos);

		// Token: 0x060086EF RID: 34543
		GSPacketIn SendUpdateQuests(GamePlayer player, byte[] states, BaseQuest[] quests);

		// Token: 0x060086F0 RID: 34544
		GSPacketIn SendMailResponse(int playerID, eMailRespose type);

		// Token: 0x060086F1 RID: 34545
		GSPacketIn SendAuctionRefresh(AuctionInfo info, int auctionID, bool isExist, ItemInfo item);

		// Token: 0x060086F2 RID: 34546
		GSPacketIn SendIDNumberCheck(bool result);

		// Token: 0x060086F3 RID: 34547
		GSPacketIn SendAASState(bool result);

		// Token: 0x060086F4 RID: 34548
		GSPacketIn SendAASInfoSet(bool result);

		// Token: 0x060086F5 RID: 34549
		GSPacketIn SendAASControl(bool result, bool IsAASInfo, bool IsMinor);

		// Token: 0x060086F6 RID: 34550
		GSPacketIn SendMarryInfoRefresh(MarryInfo info, int ID, bool isExist);

		// Token: 0x060086F7 RID: 34551
		GSPacketIn SendMarryRoomInfo(GamePlayer player, MarryRoom room);

		// Token: 0x060086F8 RID: 34552
		GSPacketIn SendPlayerEnterMarryRoom(GamePlayer player);

		// Token: 0x060086F9 RID: 34553
		GSPacketIn SendPlayerMarryStatus(GamePlayer player, int userID, bool isMarried);

		// Token: 0x060086FA RID: 34554
		GSPacketIn SendPlayerMarryApply(GamePlayer player, int userID, string userName, string loveProclamation, int ID);

		// Token: 0x060086FB RID: 34555
		GSPacketIn SendPlayerDivorceApply(GamePlayer player, bool result, bool isProposer);

		// Token: 0x060086FC RID: 34556
		GSPacketIn SendMarryApplyReply(GamePlayer player, int UserID, string UserName, bool result, bool isApplicant, int ID);

		// Token: 0x060086FD RID: 34557
		GSPacketIn SendBigSpeakerMsg(GamePlayer player, string msg);

		// Token: 0x060086FE RID: 34558
		GSPacketIn SendPlayerLeaveMarryRoom(GamePlayer player);

		// Token: 0x060086FF RID: 34559
		GSPacketIn SendMarryRoomLogin(GamePlayer player, bool result);

		// Token: 0x06008700 RID: 34560
		GSPacketIn SendMarryRoomInfoToPlayer(GamePlayer player, bool state, MarryRoomInfo info);

		// Token: 0x06008701 RID: 34561
		GSPacketIn SendMarryInfo(GamePlayer player, MarryInfo info);

		// Token: 0x06008702 RID: 34562
		GSPacketIn SendContinuation(GamePlayer player, MarryRoomInfo info);

		// Token: 0x06008703 RID: 34563
		GSPacketIn SendMarryProp(GamePlayer player, MarryProp info);

		// Token: 0x06008704 RID: 34564
		GSPacketIn SendRoomType(GamePlayer player, BaseRoom game);

		// Token: 0x06008705 RID: 34565
		GSPacketIn SendOpenVIP(GamePlayer Player);

		// Token: 0x06008706 RID: 34566
		GSPacketIn SendAchievementSuccess(AchievementDataInfo data);

		// Token: 0x06008707 RID: 34567
		GSPacketIn SendUpdateAchievementInfo(List<AchievementProcessInfo> process);

		// Token: 0x06008708 RID: 34568
		GSPacketIn SendAchievementDataInfo(List<AchievementDataInfo> data);

		// Token: 0x06008709 RID: 34569
		GSPacketIn SendLookupEffort(PlayerInfo player, List<AchievementDataInfo> datas);

		// Token: 0x0600870A RID: 34570
		void SendAcademyGradute(GamePlayer app, int type);

		// Token: 0x0600870B RID: 34571
		GSPacketIn SendAcademyAppState(PlayerInfo player, int removeUserId);

		// Token: 0x0600870C RID: 34572
		GSPacketIn SendAcademySystemNotice(string text, bool isAlert);

		// Token: 0x0600870D RID: 34573
		void SendShopGoodsCountUpdate(List<ShopFreeCountInfo> list);

		// Token: 0x0600870E RID: 34574
		GSPacketIn SendUserRanks(int Id, List<UserRankInfo> ranks);

		// Token: 0x0600870F RID: 34575
		GSPacketIn SendOpenTimeBox(int condtion, bool isSuccess);

		// Token: 0x06008710 RID: 34576
		GSPacketIn SendBuyBadge(int consortiaID, int BadgeID, int ValidDate, bool result, string BadgeBuyTime, int playerid);

		// Token: 0x06008711 RID: 34577
		void SendUpdateAreaInfo();

		// Token: 0x06008712 RID: 34578
		void SendWeaklessGuildProgress(PlayerInfo player);

		// Token: 0x06008713 RID: 34579
		GSPacketIn SendGetUserGift(PlayerInfo player, UserGiftInfo[] allGifts);

		// Token: 0x06008714 RID: 34580
		void SendLittleGameActived();

		// Token: 0x06008715 RID: 34581
		void SendUpdateCardData(CardInventory bag, int[] updatedSlots);

		// Token: 0x06008716 RID: 34582
		void SendUpdateCardData(PlayerInfo player, List<UserCardInfo> userCard);

		// Token: 0x06008717 RID: 34583
		void SendQQTips(QQTipsInfo QQTips);

		// Token: 0x06008718 RID: 34584
		void SendLeftRouleteResult();

		// Token: 0x06008719 RID: 34585
		void SendLeftRouleteOpen(UserExtraInfo info);

		// Token: 0x0600871A RID: 34586
		GSPacketIn SendSystemConsortiaChat(string content, bool sendToSelf);

		// Token: 0x0600871B RID: 34587
		GSPacketIn SendConsortiaTaskInfo(BaseConsortiaTask baseTask);

		// Token: 0x0600871C RID: 34588
		GSPacketIn SendUpdateConsotiaBuffer(GamePlayer player, Dictionary<int, UserBufferInfo> bufflist);

		// Token: 0x0600871D RID: 34589
		GSPacketIn SendPetInfo(int id, int zoneId, UserPetInfo[] pets);

		// Token: 0x0600871E RID: 34590
		GSPacketIn SendUpdateUserPet(PetInventory bag, int[] slots);

		// Token: 0x0600871F RID: 34591
		GSPacketIn SendRefreshPet(GamePlayer player, UserPetInfo[] pets, ItemInfo[] items, bool refreshBtn);

		// Token: 0x06008720 RID: 34592
		GSPacketIn SendSpaRoomInfo(GamePlayer player, SpaRoom room);

		// Token: 0x06008721 RID: 34593
		GSPacketIn SendPlayerLeaveSpaRoom(GamePlayer player, string msg);

		// Token: 0x06008722 RID: 34594
		GSPacketIn SendPlayerLeaveSpaRoomForTimeOut(GamePlayer player);

		// Token: 0x06008723 RID: 34595
		GSPacketIn SendSpaRoomLogin(GamePlayer player);

		// Token: 0x06008724 RID: 34596
		GSPacketIn SendSpaRoomList(GamePlayer player, SpaRoom[] rooms);

		// Token: 0x06008725 RID: 34597
		GSPacketIn SendSpaRoomAddGuest(GamePlayer player);

		// Token: 0x06008726 RID: 34598
		GSPacketIn SendSpaRoomRemoveGuest(GamePlayer removePlayer);

		// Token: 0x06008727 RID: 34599
		GSPacketIn SendSpaRoomInfoPerMin(GamePlayer player, int leftTime);

		// Token: 0x06008728 RID: 34600
		GSPacketIn SendSpaRoomLoginRemind(SpaRoom room);

		// Token: 0x06008729 RID: 34601
		GSPacketIn SendIsContinueNextDay(GamePlayer player);

		// Token: 0x0600872A RID: 34602
		GSPacketIn SendPlayerRefreshTotem(PlayerInfo player);

		// Token: 0x0600872B RID: 34603
		GSPacketIn SendUpdateUpCount(PlayerInfo player);

		// Token: 0x0600872C RID: 34604
		GSPacketIn SendUpdatePlayerProperty(PlayerInfo info, PlayerProInventory prop);

		// Token: 0x0600872D RID: 34605
		void SendTotemUpgradeUpdate(int templateId, int level);

		// Token: 0x0600872E RID: 34606
		void SendTotemUpgradeInfo(Dictionary<int, int> infos);

		// Token: 0x0600872F RID: 34607
		void SendTreasureHunting(GamePlayer player);

		// Token: 0x06008730 RID: 34608
		void SendCaddyGetAwards(PlayerInfo player, List<ItemInfo> items);

		// Token: 0x06008731 RID: 34609
		void SendLeagueNotice(int id);

		// Token: 0x06008732 RID: 34610
		void SendEliteGameStartRoom();

		// Token: 0x06008733 RID: 34611
		void SendQQHall(GamePlayer player);

		// Token: 0x06008734 RID: 34612
		void SendOpenWorldBoss(int pX, int pY);

		// Token: 0x06008735 RID: 34613
		void WonderfulSingleActivityInit(IGMActive gmActivity, GamePlayer player);

		// Token: 0x06008736 RID: 34614
		void WonderfulActivityInit(List<IGMActive> allActions, GamePlayer player, int type);

		// Token: 0x06008737 RID: 34615
		GSPacketIn SendEmblemInfo(GamePlayer player);

		// Token: 0x06008738 RID: 34616
		GSPacketIn SendUpdateEmblemInfo(GamePlayer player, UserEmblemInfo info);

		// Token: 0x06008739 RID: 34617
		GSPacketIn SendScrollInfo(GamePlayer player);

		// Token: 0x0600873A RID: 34618
		void SendBoguAdventureInfo(GamePlayer player, int state);

		// Token: 0x0600873B RID: 34619
		void SendPairBoxInfo(GamePlayer player);

		// Token: 0x0600873C RID: 34620
		void SendPairBoxBaseInfo(GamePlayer player);

		// Token: 0x0600873D RID: 34621
		void SendUpdateCountInfo(GamePlayer player);

		// Token: 0x0600873E RID: 34622
		GSPacketIn SendUpdateCardPosInfo(GamePlayer player, PairBoxCeilInfo info);

		// Token: 0x0600873F RID: 34623
		GSPacketIn SendAvatarColectionAllInfo(int PlayerId, Dictionary<int, UserAvatarInfo> infos);

		// Token: 0x06008740 RID: 34624
		void SendOpenMoonLight(GamePlayer player, int state);

		// Token: 0x06008741 RID: 34625
		void SendMoonLightBagInfo(GamePlayer player);

		// Token: 0x06008742 RID: 34626
		void SendMoonLightPlayerInfo(GamePlayer player);

		// Token: 0x06008743 RID: 34627
		void SendOpenNoviceActive(int channel, int activeId, int condition, int awardGot, DateTime startTime, DateTime endTime);

		// Token: 0x06008744 RID: 34628
		void SendUpdateFirstRecharge(bool isRecharge, bool isGetAward);

		// Token: 0x06008745 RID: 34629
		void SendChargeActivity(GamePlayer player, int type);

		// Token: 0x06008746 RID: 34630
		void SendSignInfo(GamePlayer player);

		// Token: 0x06008747 RID: 34631
		GSPacketIn SendEnterFarm(PlayerInfo Player, UserFarmInfo farm, UserFieldInfo[] fields);

		// Token: 0x06008748 RID: 34632
		GSPacketIn SendSeeding(PlayerInfo Player, UserFieldInfo field);

		// Token: 0x06008749 RID: 34633
		GSPacketIn SenddoMature(FarmInventory farm);

		// Token: 0x0600874A RID: 34634
		GSPacketIn SendtoGather(PlayerInfo Player, UserFieldInfo field);

		// Token: 0x0600874B RID: 34635
		GSPacketIn SendPayFields(GamePlayer Player, List<int> fieldIds);

		// Token: 0x0600874C RID: 34636
		GSPacketIn SendKillCropField(PlayerInfo Player, UserFieldInfo field);

		// Token: 0x0600874D RID: 34637
		GSPacketIn SendHelperSwitchField(PlayerInfo Player, UserFarmInfo farm);

		// Token: 0x0600874E RID: 34638
		GSPacketIn SendFarmLandInfo(FarmInventory farm);

		// Token: 0x0600874F RID: 34639
		void SendSyncOrUpdateChips(GamePlayer player, UserMarkInfo mark);

		// Token: 0x06008750 RID: 34640
		void SendEquipScheme(GamePlayer player);

		// Token: 0x06008751 RID: 34641
		GSPacketIn SendEXPBottleInfo(GamePlayer player);

		// Token: 0x06008752 RID: 34642
		void SendBoGuTurnInfo(GamePlayer player);

		// Token: 0x06008753 RID: 34643
		void SendBoGuTurnPlayerInfo(GamePlayer player);

		// Token: 0x06008754 RID: 34644
		void SendHomeTempleInfo(GamePlayer player);

		// Token: 0x06008755 RID: 34645
		void SendOpenOrCloseChristmas(int lastPacks, bool isOpen);

		// Token: 0x06008756 RID: 34646
		void SendNecklaceInfo(GamePlayer player, int addExp, int needCount);

		// Token: 0x06008757 RID: 34647
		void SendNecklaceCastInfo(GamePlayer player);

		// Token: 0x06008758 RID: 34648
		GSPacketIn SendUserSyncEquipGhost(GamePlayer p);

		// Token: 0x06008759 RID: 34649
		void SendUpdateRankList(GamePlayer player);

		// Token: 0x0600875A RID: 34650
		void SendUpdateJackpot(GamePlayer player);

		// Token: 0x0600875B RID: 34651
		void SendDiceResult(GamePlayer player, byte from, byte to);

		// Token: 0x0600875C RID: 34652
		void SendUpdateInfo(GamePlayer player);

		// Token: 0x0600875D RID: 34653
		void SendUpdateBoxList(GamePlayer player);

		// Token: 0x0600875E RID: 34654
		void SendActiveInfo(GamePlayer player);

		// Token: 0x0600875F RID: 34655
		void SendCatchBeastOpen(int playerID);

		// Token: 0x06008760 RID: 34656
		void SendOpenDDPlay(PlayerInfo player);

		// Token: 0x06008761 RID: 34657
		GSPacketIn SendPlayerFigSpiritinit(int ID, List<UserGemStone> gems);

		// Token: 0x06008762 RID: 34658
		GSPacketIn SendPlayerFigSpiritUp(int ID, UserGemStone gem, bool isUp, bool isMaxLevel, bool isFall, int num, int dir);

		// Token: 0x06008763 RID: 34659
		void SendForthActiveOpen(GamePlayer player);
	}
}
