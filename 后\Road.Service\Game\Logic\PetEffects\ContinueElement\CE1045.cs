﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E58 RID: 3672
	public class CE1045 : BasePetEffect
	{
		// Token: 0x06007F9E RID: 32670 RVA: 0x002A5CD0 File Offset: 0x002A3ED0
		public CE1045(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1045, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F9F RID: 32671 RVA: 0x002A5D50 File Offset: 0x002A3F50
		public override bool Start(Living living)
		{
			CE1045 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1045) as CE1045;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FA0 RID: 32672 RVA: 0x00031ACA File Offset: 0x0002FCCA
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007FA1 RID: 32673 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FA2 RID: 32674 RVA: 0x002A5DB0 File Offset: 0x002A3FB0
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 1000;
				living.SyncAtTime = true;
				living.AddBlood(this.m_added);
				living.SyncAtTime = false;
			}
		}

		// Token: 0x06007FA3 RID: 32675 RVA: 0x00031AF3 File Offset: 0x0002FCF3
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004EA3 RID: 20131
		private int m_type = 0;

		// Token: 0x04004EA4 RID: 20132
		private int m_count = 0;

		// Token: 0x04004EA5 RID: 20133
		private int m_probability = 0;

		// Token: 0x04004EA6 RID: 20134
		private int m_delay = 0;

		// Token: 0x04004EA7 RID: 20135
		private int m_coldDown = 0;

		// Token: 0x04004EA8 RID: 20136
		private int m_currentId;

		// Token: 0x04004EA9 RID: 20137
		private int m_added = 0;
	}
}
