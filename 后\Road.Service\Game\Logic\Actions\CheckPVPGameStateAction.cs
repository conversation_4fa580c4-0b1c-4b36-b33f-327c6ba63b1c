﻿using System;

namespace Game.Logic.Actions
{
	// Token: 0x02000F47 RID: 3911
	public class CheckPVPGameStateAction : IAction
	{
		// Token: 0x060084E3 RID: 34019 RVA: 0x0003512A File Offset: 0x0003332A
		public CheckPVPGameStateAction(int delay)
		{
			this.m_isFinished = false;
			this.m_tick += TickHelper.GetTickCount() + (long)delay;
		}

		// Token: 0x060084E4 RID: 34020 RVA: 0x002B90F0 File Offset: 0x002B72F0
		public void Execute(BaseGame game, long tick)
		{
			bool flag = this.m_tick > tick;
			if (!flag)
			{
				PVPGame pvpgame = game as PVPGame;
				bool flag2 = pvpgame != null;
				if (flag2)
				{
					switch (game.GameState)
					{
					case eGameState.Inited:
						pvpgame.Prepare();
						break;
					case eGameState.Prepared:
						pvpgame.StartLoading();
						break;
					case eGameState.Loading:
					{
						bool flag3 = pvpgame.IsAllComplete();
						if (flag3)
						{
							pvpgame.StartGame();
						}
						break;
					}
					case eGameState.Playing:
					{
						bool flag4 = pvpgame.CurrentPlayer == null || !pvpgame.CurrentPlayer.IsAttacking;
						if (flag4)
						{
							bool flag5 = pvpgame.CanGameOver();
							if (flag5)
							{
								pvpgame.GameOver();
							}
							else
							{
								pvpgame.NextTurn();
							}
						}
						break;
					}
					case eGameState.GameOver:
						pvpgame.Stop();
						break;
					}
				}
				this.m_isFinished = true;
			}
		}

		// Token: 0x060084E5 RID: 34021 RVA: 0x002B91D4 File Offset: 0x002B73D4
		public bool IsFinished(BaseGame game, long tick)
		{
			return this.m_isFinished;
		}

		// Token: 0x040052C3 RID: 21187
		private long m_tick;

		// Token: 0x040052C4 RID: 21188
		private bool m_isFinished;
	}
}
