﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DCA RID: 3530
	public class AE1104 : BasePetEffect
	{
		// Token: 0x06007C95 RID: 31893 RVA: 0x002983B4 File Offset: 0x002965B4
		public AE1104(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1104, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C96 RID: 31894 RVA: 0x00298434 File Offset: 0x00296634
		public override bool Start(Living living)
		{
			AE1104 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1104) as AE1104;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C97 RID: 31895 RVA: 0x0002FBE4 File Offset: 0x0002DDE4
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
			player.PlayerCompleteShoot += this.Player_PlayerCompleteShoot;
		}

		// Token: 0x06007C98 RID: 31896 RVA: 0x0002FC0D File Offset: 0x0002DE0D
		private void Player_PlayerCompleteShoot(Player player)
		{
			player.PetEffects.AddBloodPercent = 0;
		}

		// Token: 0x06007C99 RID: 31897 RVA: 0x00298494 File Offset: 0x00296694
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 800;
				player.PetEffects.AddBloodPercent = this.m_added;
			}
		}

		// Token: 0x06007C9A RID: 31898 RVA: 0x0002FC1C File Offset: 0x0002DE1C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
			player.PlayerCompleteShoot -= this.Player_PlayerCompleteShoot;
		}

		// Token: 0x04004AC0 RID: 19136
		private int m_type = 0;

		// Token: 0x04004AC1 RID: 19137
		private int m_count = 0;

		// Token: 0x04004AC2 RID: 19138
		private int m_probability = 0;

		// Token: 0x04004AC3 RID: 19139
		private int m_delay = 0;

		// Token: 0x04004AC4 RID: 19140
		private int m_coldDown = 0;

		// Token: 0x04004AC5 RID: 19141
		private int m_currentId;

		// Token: 0x04004AC6 RID: 19142
		private int m_added = 0;
	}
}
