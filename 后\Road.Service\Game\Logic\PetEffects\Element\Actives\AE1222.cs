﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E09 RID: 3593
	public class AE1222 : BasePetEffect
	{
		// Token: 0x06007DE0 RID: 32224 RVA: 0x0029E048 File Offset: 0x0029C248
		public AE1222(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1222, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DE1 RID: 32225 RVA: 0x0029E0C8 File Offset: 0x0029C2C8
		public override bool Start(Living living)
		{
			AE1222 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1222) as AE1222;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DE2 RID: 32226 RVA: 0x0003093F File Offset: 0x0002EB3F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
		}

		// Token: 0x06007DE3 RID: 32227 RVA: 0x0003097B File Offset: 0x0002EB7B
		private void Player_BeginSelfTurn(Living living)
		{
			this.IsTrigger = false;
		}

		// Token: 0x06007DE4 RID: 32228 RVA: 0x0029E128 File Offset: 0x0029C328
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				target.Game.SendPetBuff(target, base.ElementInfo, true);
				target.AddPetEffect(new CE1222(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007DE5 RID: 32229 RVA: 0x00030985 File Offset: 0x0002EB85
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x06007DE6 RID: 32230 RVA: 0x0029E198 File Offset: 0x0029C398
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				int currentId = this.m_currentId;
				int num = currentId;
				bool flag2 = num - 154 <= 1;
				if (flag2)
				{
					List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
					foreach (Player player2 in allEnemyPlayers)
					{
						player2.Game.SendPetBuff(player2, base.ElementInfo, true);
						player2.AddPetEffect(new CE1222(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
					}
				}
				else
				{
					this.IsTrigger = true;
				}
			}
		}

		// Token: 0x04004C77 RID: 19575
		private int m_type = 0;

		// Token: 0x04004C78 RID: 19576
		private int m_count = 0;

		// Token: 0x04004C79 RID: 19577
		private int m_probability = 0;

		// Token: 0x04004C7A RID: 19578
		private int m_delay = 0;

		// Token: 0x04004C7B RID: 19579
		private int m_coldDown = 0;

		// Token: 0x04004C7C RID: 19580
		private int m_currentId;

		// Token: 0x04004C7D RID: 19581
		private int m_added = 0;
	}
}
