﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FEA RID: 4074
	public class SpiritInfoMgr
	{
		// Token: 0x06008B64 RID: 35684 RVA: 0x002FC52C File Offset: 0x002FA72C
		public static bool Init()
		{
			bool flag;
			try
			{
				SpiritInfoMgr.m_spiritList = new List<SpiritInfo>();
				flag = SpiritInfoMgr.LoadSpiritInfos(SpiritInfoMgr.m_spiritList);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = SpiritInfoMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					SpiritInfoMgr.log.Error("SpiritInfoMgr", ex);
				}
				flag = false;
			}
			return flag;
		}

		// Token: 0x06008B65 RID: 35685 RVA: 0x002FC58C File Offset: 0x002FA78C
		public static bool ReLoad()
		{
			try
			{
				List<SpiritInfo> list = new List<SpiritInfo>();
				SpiritInfoMgr.m_lock.AcquireWriterLock(-1);
				try
				{
					SpiritInfoMgr.m_spiritList = list;
					return true;
				}
				catch
				{
				}
				finally
				{
					SpiritInfoMgr.m_lock.ReleaseWriterLock();
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = SpiritInfoMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					SpiritInfoMgr.log.Error("SpiritInfoMgr", ex);
				}
			}
			return false;
		}

		// Token: 0x06008B66 RID: 35686 RVA: 0x002FC624 File Offset: 0x002FA824
		private static bool LoadSpiritInfos(List<SpiritInfo> tempCamp)
		{
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				SpiritInfo[] allSpirit = produceBussiness.GetAllSpirit();
				SpiritInfo[] array = allSpirit;
				SpiritInfo[] array2 = array;
				SpiritInfo[] array3 = array2;
				foreach (SpiritInfo spiritInfo in array3)
				{
					tempCamp.Add(spiritInfo);
				}
			}
			return true;
		}

		// Token: 0x06008B67 RID: 35687 RVA: 0x002FC698 File Offset: 0x002FA898
		public static List<SpiritInfo> GetSpirit(int categoryId)
		{
			List<SpiritInfo> list = new List<SpiritInfo>();
			List<SpiritInfo> spiritList = SpiritInfoMgr.m_spiritList;
			lock (spiritList)
			{
				foreach (SpiritInfo spiritInfo in SpiritInfoMgr.m_spiritList)
				{
					bool flag2 = spiritInfo.CategoryId == categoryId;
					if (flag2)
					{
						list.Add(spiritInfo);
					}
				}
			}
			bool flag3 = list.Count > 0;
			if (flag3)
			{
				list = list.OrderBy((SpiritInfo a) => a.Level).ToList<SpiritInfo>();
			}
			return list;
		}

		// Token: 0x06008B68 RID: 35688 RVA: 0x002FC77C File Offset: 0x002FA97C
		public static List<SpiritInfo> GetSpirit(int bagType, int place)
		{
			List<SpiritInfo> list = new List<SpiritInfo>();
			List<SpiritInfo> spiritList = SpiritInfoMgr.m_spiritList;
			lock (spiritList)
			{
				foreach (SpiritInfo spiritInfo in SpiritInfoMgr.m_spiritList)
				{
					bool flag2 = spiritInfo.BagType == bagType && spiritInfo.BagPlace == place;
					if (flag2)
					{
						list.Add(spiritInfo);
					}
				}
			}
			bool flag3 = list.Count > 0;
			if (flag3)
			{
				list = list.OrderBy((SpiritInfo a) => a.Level).ToList<SpiritInfo>();
			}
			return list;
		}

		// Token: 0x06008B69 RID: 35689 RVA: 0x002FC86C File Offset: 0x002FAA6C
		public static SpiritInfo GetSingleSpirit(int bagType, int place, int level)
		{
			List<SpiritInfo> spiritList = SpiritInfoMgr.m_spiritList;
			lock (spiritList)
			{
				foreach (SpiritInfo spiritInfo in SpiritInfoMgr.m_spiritList)
				{
					bool flag2 = spiritInfo.BagType == bagType && spiritInfo.BagPlace == place && spiritInfo.Level == level;
					if (flag2)
					{
						return spiritInfo;
					}
				}
			}
			return null;
		}

		// Token: 0x06008B6A RID: 35690 RVA: 0x002FC91C File Offset: 0x002FAB1C
		public static int GetSpiritSkill(int bagType, int place, int level)
		{
			List<SpiritInfo> spiritList = SpiritInfoMgr.m_spiritList;
			lock (spiritList)
			{
				foreach (SpiritInfo spiritInfo in SpiritInfoMgr.m_spiritList)
				{
					bool flag2 = spiritInfo.BagType == bagType && spiritInfo.BagPlace == place && spiritInfo.Level == level;
					if (flag2)
					{
						return spiritInfo.SkillId;
					}
				}
			}
			return 0;
		}

		// Token: 0x04005548 RID: 21832
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005549 RID: 21833
		private static ReaderWriterLock m_lock = new ReaderWriterLock();

		// Token: 0x0400554A RID: 21834
		private static List<SpiritInfo> m_spiritList;

		// Token: 0x0400554B RID: 21835
		public static int MAX_LEVEL = 0;
	}
}
