﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E86 RID: 3718
	public class CE1180 : BasePetEffect
	{
		// Token: 0x060080BB RID: 32955 RVA: 0x002AA230 File Offset: 0x002A8430
		public CE1180(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1180, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060080BC RID: 32956 RVA: 0x002AA2B0 File Offset: 0x002A84B0
		public override bool Start(Living living)
		{
			CE1180 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1180) as CE1180;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080BD RID: 32957 RVA: 0x002AA310 File Offset: 0x002A8510
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 500;
				player.Attack += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060080BE RID: 32958 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060080BF RID: 32959 RVA: 0x002AA374 File Offset: 0x002A8574
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060080C0 RID: 32960 RVA: 0x002AA3A8 File Offset: 0x002A85A8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Attack -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004FE7 RID: 20455
		private int m_type = 0;

		// Token: 0x04004FE8 RID: 20456
		private int m_count = 0;

		// Token: 0x04004FE9 RID: 20457
		private int m_probability = 0;

		// Token: 0x04004FEA RID: 20458
		private int m_delay = 0;

		// Token: 0x04004FEB RID: 20459
		private int m_coldDown = 0;

		// Token: 0x04004FEC RID: 20460
		private int m_currentId;

		// Token: 0x04004FED RID: 20461
		private int m_added = 0;
	}
}
