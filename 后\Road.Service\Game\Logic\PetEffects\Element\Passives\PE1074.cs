﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D58 RID: 3416
	public class PE1074 : BasePetEffect
	{
		// Token: 0x06007A4C RID: 31308 RVA: 0x0028E48C File Offset: 0x0028C68C
		public PE1074(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1074, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A4D RID: 31309 RVA: 0x0028E50C File Offset: 0x0028C70C
		public override bool Start(Living living)
		{
			PE1074 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1074) as PE1074;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A4E RID: 31310 RVA: 0x0002E5BF File Offset: 0x0002C7BF
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
		}

		// Token: 0x06007A4F RID: 31311 RVA: 0x0028E56C File Offset: 0x0028C76C
		private void Player_BeginNextTurn(Living living)
		{
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 300;
				living.PetEffects.IncreaseAngelicPoint = this.m_added;
			}
		}

		// Token: 0x06007A50 RID: 31312 RVA: 0x0002E5D5 File Offset: 0x0002C7D5
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
		}

		// Token: 0x040047A8 RID: 18344
		private int m_type = 0;

		// Token: 0x040047A9 RID: 18345
		private int m_count = 0;

		// Token: 0x040047AA RID: 18346
		private int m_probability = 0;

		// Token: 0x040047AB RID: 18347
		private int m_delay = 0;

		// Token: 0x040047AC RID: 18348
		private int m_coldDown = 0;

		// Token: 0x040047AD RID: 18349
		private int m_currentId;

		// Token: 0x040047AE RID: 18350
		private int m_added = 0;
	}
}
