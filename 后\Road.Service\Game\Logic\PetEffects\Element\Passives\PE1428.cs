﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D86 RID: 3462
	public class PE1428 : BasePetEffect
	{
		// Token: 0x06007B33 RID: 31539 RVA: 0x00292244 File Offset: 0x00290444
		public PE1428(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1428, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B34 RID: 31540 RVA: 0x002922C4 File Offset: 0x002904C4
		public override bool Start(Living living)
		{
			PE1428 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1428) as PE1428;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B35 RID: 31541 RVA: 0x0002EDF8 File Offset: 0x0002CFF8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
		}

		// Token: 0x06007B36 RID: 31542 RVA: 0x0002EE0E File Offset: 0x0002D00E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
		}

		// Token: 0x06007B37 RID: 31543 RVA: 0x00292324 File Offset: 0x00290524
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			int num = damageAmount * 10 / 100;
			damageAmount -= num;
			bool activeKimCang = (living as Player).ActiveKimCang;
			if (activeKimCang)
			{
				(living as Player).ActiveKimCang = false;
				living.Game.SendPetBuff(living, base.ElementInfo, true);
			}
		}

		// Token: 0x040048EA RID: 18666
		private int m_type = 0;

		// Token: 0x040048EB RID: 18667
		private int m_count = 0;

		// Token: 0x040048EC RID: 18668
		private int m_probability = 0;

		// Token: 0x040048ED RID: 18669
		private int m_delay = 0;

		// Token: 0x040048EE RID: 18670
		private int m_coldDown = 0;

		// Token: 0x040048EF RID: 18671
		private int m_currentId;

		// Token: 0x040048F0 RID: 18672
		private int m_added = 0;
	}
}
