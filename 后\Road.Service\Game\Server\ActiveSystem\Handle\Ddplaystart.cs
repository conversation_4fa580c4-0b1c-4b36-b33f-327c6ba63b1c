﻿using System;
using System.Linq;
using Bussiness;
using Game.Base.Packets;
using Game.Server.GameObjects;
using Game.Server.Managers;
using Game.Server.Packets;

namespace Game.Server.ActiveSystem.Handle
{
	// Token: 0x02000C5B RID: 3163
	[ActiveSystemHandleAttbute(76)]
	public class Ddplaystart : IActiveSystemCommandHadler
	{
		// Token: 0x0600703A RID: 28730 RVA: 0x0024E4B0 File Offset: 0x0024C6B0
		public bool CommandHandler(GamePlayer Player, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(145, Player.PlayerCharacter.ID);
			gspacketIn.WriteByte(76);
			int num = 0;
			bool flag = Player.PropBag.GetItemCount(201310) <= 0;
			bool flag2;
			if (flag)
			{
				Player.SendMessage(LanguageMgr.GetTranslation("DDPLayStart.NotEnoughtCoin", Array.Empty<object>()));
				flag2 = false;
			}
			else
			{
				Player.PropBag.RemoveTemplate(201310, 1);
				Player.PlayerCharacter.Info.DDPlayPoint += 10;
				num = Ddplaystart.random.Next(50);
				bool flag3 = new int[] { 2, 3, 5, 10 }.Contains(num);
				if (flag3)
				{
					int num2 = int.Parse(NewActivityMgr.GetConfigByID(8).Params1) * num;
					using (PlayerBussiness playerBussiness = new PlayerBussiness())
					{
						bool flag4 = false;
						bool flag5 = Player.SendMoneyMailToUser(playerBussiness, string.Format("恭喜你在弹弹乐活动中获得了{0}倍的点券奖励", num), LanguageMgr.GetTranslation("弹弹乐奖励", new object[] { num }), num2, eMailType.BuyItem);
						if (flag5)
						{
							flag4 = true;
						}
						bool flag6 = flag4;
						if (flag6)
						{
							Player.Out.SendMailResponse(Player.PlayerCharacter.ID, eMailRespose.Receiver);
						}
					}
					LanguageMgr.GetTranslation("DDPLayStart.Notice", new object[]
					{
						Player.PlayerCharacter.NickName,
						num
					});
				}
				else
				{
					num = 0;
				}
				gspacketIn.WriteInt(num);
				gspacketIn.WriteInt(Player.PlayerCharacter.Info.DDPlayPoint);
				Player.SendTCP(gspacketIn);
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x04003C4F RID: 15439
		public static Random random = new Random();
	}
}
