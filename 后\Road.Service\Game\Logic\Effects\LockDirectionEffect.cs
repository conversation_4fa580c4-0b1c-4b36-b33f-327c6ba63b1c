﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EEE RID: 3822
	public class LockDirectionEffect : AbstractEffect
	{
		// Token: 0x06008341 RID: 33601 RVA: 0x00034214 File Offset: 0x00032414
		public LockDirectionEffect(int count)
			: base(eEffectType.LockDirectionEffect)
		{
			this.m_count = count;
		}

		// Token: 0x06008342 RID: 33602 RVA: 0x002B34A4 File Offset: 0x002B16A4
		public override bool Start(Living living)
		{
			LockDirectionEffect lockDirectionEffect = living.EffectList.GetOfType(eEffectType.LockDirectionEffect) as LockDirectionEffect;
			bool flag = lockDirectionEffect != null;
			bool flag2;
			if (flag)
			{
				lockDirectionEffect.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008343 RID: 33603 RVA: 0x00034227 File Offset: 0x00032427
		public override void OnAttached(Living living)
		{
			living.BeginSelfTurn += this.player_BeginFitting;
			living.Game.SendPlayerPicture(living, 3, true);
		}

		// Token: 0x06008344 RID: 33604 RVA: 0x0003424C File Offset: 0x0003244C
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginFitting;
			living.Game.SendPlayerPicture(living, 3, false);
		}

		// Token: 0x06008345 RID: 33605 RVA: 0x002B34EC File Offset: 0x002B16EC
		private void player_BeginFitting(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x04005208 RID: 21000
		private int m_count;
	}
}
