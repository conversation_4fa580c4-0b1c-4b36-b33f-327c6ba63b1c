﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F48 RID: 3912
	public class FightAchievementAction : BaseAction
	{
		// Token: 0x060084E6 RID: 34022 RVA: 0x00035150 File Offset: 0x00033350
		public FightAchievementAction(Living living, eAcrobaciaType type, int num, int delay)
			: base(delay, 1500)
		{
			this.m_living = living;
			this.m_num = num;
			this.m_type = (int)type;
			this.m_delay = delay;
		}

		// Token: 0x060084E7 RID: 34023 RVA: 0x0003517E File Offset: 0x0003337E
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			game.SendFightAchievement(this.m_living, this.m_type, this.m_num, this.m_delay);
			base.Finish(tick);
		}

		// Token: 0x040052C5 RID: 21189
		private Living m_living;

		// Token: 0x040052C6 RID: 21190
		private int m_num;

		// Token: 0x040052C7 RID: 21191
		private int m_type;

		// Token: 0x040052C8 RID: 21192
		private int m_delay;
	}
}
