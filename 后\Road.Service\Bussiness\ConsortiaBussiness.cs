﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using SqlDataProvider.BaseClass;
using SqlDataProvider.Data;

namespace Bussiness
{
	// Token: 0x02000FA5 RID: 4005
	public class ConsortiaBussiness : BaseBussiness
	{
		// Token: 0x0600881C RID: 34844 RVA: 0x000360C1 File Offset: 0x000342C1
		public ConsortiaBussiness()
		{
		}

		// Token: 0x0600881D RID: 34845 RVA: 0x002CA6AC File Offset: 0x002C88AC
		public ConsortiaBussiness(AreaConfigInfo config)
		{
			this.AreaID = config.AreaID;
			this.AreaName = config.AreaName;
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("Data Source=");
			stringBuilder.Append(config.DataSource);
			stringBuilder.Append("; Initial Catalog=");
			stringBuilder.Append(config.Catalog);
			stringBuilder.Append("; Persist Security Info=True;User ID=");
			stringBuilder.Append(config.UserID);
			stringBuilder.Append("; Password=");
			stringBuilder.Append(config.Password);
			stringBuilder.Append(";");
			string text = stringBuilder.ToString();
			this.db = new Sql_DbObject("AreaConfig", text);
		}

		// Token: 0x0600881E RID: 34846 RVA: 0x002CA77C File Offset: 0x002C897C
		public bool AddConsortia(ConsortiaInfo info, ref string msg, ref ConsortiaDutyInfo dutyInfo)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[25];
				array[0] = new SqlParameter("@ConsortiaID", info.ConsortiaID);
				array[0].Direction = ParameterDirection.InputOutput;
				array[1] = new SqlParameter("@BuildDate", info.BuildDate);
				array[2] = new SqlParameter("@CelebCount", info.CelebCount);
				array[3] = new SqlParameter("@ChairmanID", info.ChairmanID);
				array[4] = new SqlParameter("@ChairmanName", (info.ChairmanName == null) ? "" : info.ChairmanName);
				array[5] = new SqlParameter("@ConsortiaName", (info.ConsortiaName == null) ? "" : info.ConsortiaName);
				array[6] = new SqlParameter("@CreatorID", info.CreatorID);
				array[7] = new SqlParameter("@CreatorName", (info.CreatorName == null) ? "" : info.CreatorName);
				array[8] = new SqlParameter("@Description", info.Description);
				array[9] = new SqlParameter("@Honor", info.Honor);
				array[10] = new SqlParameter("@IP", info.IP);
				array[11] = new SqlParameter("@IsExist", info.IsExist);
				array[12] = new SqlParameter("@Level", info.Level);
				array[13] = new SqlParameter("@MaxCount", info.MaxCount);
				array[14] = new SqlParameter("@Placard", info.Placard);
				array[15] = new SqlParameter("@Port", info.Port);
				array[16] = new SqlParameter("@Repute", info.Repute);
				array[17] = new SqlParameter("@Count", info.Count);
				array[18] = new SqlParameter("@Riches", info.Riches);
				array[19] = new SqlParameter("@Result", SqlDbType.Int);
				array[19].Direction = ParameterDirection.ReturnValue;
				array[20] = new SqlParameter("@tempDutyLevel", SqlDbType.Int);
				array[20].Direction = ParameterDirection.InputOutput;
				array[20].Value = dutyInfo.Level;
				array[21] = new SqlParameter("@tempDutyName", SqlDbType.VarChar, 100);
				array[21].Direction = ParameterDirection.InputOutput;
				array[21].Value = "";
				array[22] = new SqlParameter("@tempRight", SqlDbType.Int);
				array[22].Direction = ParameterDirection.InputOutput;
				array[22].Value = dutyInfo.Right;
				array[23] = new SqlParameter("@IsSystemCreate", info.IsSystemCreate);
				array[24] = new SqlParameter("@IsActive", info.IsActive);
				flag = this.db.RunProcedure("SP_Consortia_Add", array);
				int num = (int)array[19].Value;
				flag = num == 0;
				bool flag2 = flag;
				if (flag2)
				{
					info.ConsortiaID = (int)array[0].Value;
					dutyInfo.Level = (int)array[20].Value;
					dutyInfo.DutyName = (array[21].Value != null) ? array[21].Value.ToString() : "";
					dutyInfo.Right = (int)array[22].Value;
				}
				int num2 = num;
				int num3 = num2;
				bool flag3 = num3 == 2;
				if (flag3)
				{
					msg = "ConsortiaBussiness.AddConsortia.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600881F RID: 34847 RVA: 0x002CAB64 File Offset: 0x002C8D64
		public bool DeleteConsortia(int consortiaID, int userID, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Consortia_Delete", array);
				int num = (int)array[2].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				if (num3 != 2)
				{
					if (num3 == 3)
					{
						msg = "ConsortiaBussiness.DeleteConsortia.Msg3";
					}
				}
				else
				{
					msg = "ConsortiaBussiness.DeleteConsortia.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008820 RID: 34848 RVA: 0x002CAC48 File Offset: 0x002C8E48
		public ConsortiaInfo[] GetConsortiaPage(int page, int size, ref int total, int order, string name, int consortiaID, int level, int openApply)
		{
			List<ConsortiaInfo> list = new List<ConsortiaInfo>();
			try
			{
				string text = " IsExist=1 ";
				bool flag = !string.IsNullOrEmpty(name);
				if (flag)
				{
					text = text + " and ConsortiaName like '%" + name + "%' ";
				}
				bool flag2 = consortiaID != -1;
				if (flag2)
				{
					text = text + " and ConsortiaID =" + consortiaID.ToString() + " ";
				}
				bool flag3 = level != -1;
				if (flag3)
				{
					text = text + " and Level =" + level.ToString() + " ";
				}
				bool flag4 = openApply != -1;
				if (flag4)
				{
					text = text + " and OpenApply =" + openApply.ToString() + " ";
				}
				string text2 = "ConsortiaName";
				switch (order)
				{
				case 1:
					text2 = "ReputeSort";
					break;
				case 2:
					text2 = "ChairmanName";
					break;
				case 3:
					text2 = "Count desc";
					break;
				case 4:
					text2 = "Level desc";
					break;
				case 5:
					text2 = "Honor desc";
					break;
				case 10:
					text2 = "LastDayRiches desc";
					break;
				case 11:
					text2 = "AddDayRiches desc";
					break;
				case 12:
					text2 = "AddWeekRiches desc";
					break;
				case 13:
					text2 = "LastDayHonor desc";
					break;
				case 14:
					text2 = "AddDayHonor desc";
					break;
				case 15:
					text2 = "AddWeekHonor desc";
					break;
				case 16:
					text2 = "level desc,LastDayRiches desc";
					break;
				case 17:
					text2 = "GiftGP desc";
					break;
				case 18:
					text2 = "AddDayGiftGP desc";
					break;
				case 19:
					text2 = "AddWeekGiftGP desc";
					break;
				case 20:
					text2 = "FightPower desc";
					break;
				}
				text2 += ",ConsortiaID ";
				DataTable page2 = base.GetPage("V_Consortia", text, page, size, "*", text2, "ConsortiaID", ref total);
				foreach (object obj in page2.Rows)
				{
					DataRow dataRow = (DataRow)obj;
					list.Add(new ConsortiaInfo
					{
						ConsortiaID = (int)dataRow["ConsortiaID"],
						BuildDate = (DateTime)dataRow["BuildDate"],
						CelebCount = (int)dataRow["CelebCount"],
						ChairmanID = (int)dataRow["ChairmanID"],
						ChairmanName = dataRow["ChairmanName"].ToString(),
						ConsortiaName = dataRow["ConsortiaName"].ToString(),
						CreatorID = (int)dataRow["CreatorID"],
						CreatorName = dataRow["CreatorName"].ToString(),
						Description = dataRow["Description"].ToString(),
						Honor = (int)dataRow["Honor"],
						IsExist = (bool)dataRow["IsExist"],
						Level = (int)dataRow["Level"],
						MaxCount = (int)dataRow["MaxCount"],
						Placard = dataRow["Placard"].ToString(),
						IP = dataRow["IP"].ToString(),
						Port = (int)dataRow["Port"],
						Repute = (int)dataRow["Repute"],
						Count = (int)dataRow["Count"],
						Riches = (int)dataRow["Riches"],
						GiftGP = (int)dataRow["GiftGP"],
						DeductDate = (DateTime)dataRow["DeductDate"],
						AddDayHonor = (int)dataRow["AddDayHonor"],
						AddDayRiches = (int)dataRow["AddDayRiches"],
						AddDayGiftGP = (int)dataRow["AddDayGiftGP"],
						AddWeekHonor = (int)dataRow["AddWeekHonor"],
						AddWeekRiches = (int)dataRow["AddWeekRiches"],
						AddWeekGiftGP = (int)dataRow["AddWeekGiftGP"],
						LastDayRiches = (int)dataRow["LastDayRiches"],
						OpenApply = (bool)dataRow["OpenApply"],
						StoreLevel = (int)dataRow["StoreLevel"],
						SmithLevel = (int)dataRow["SmithLevel"],
						ShopLevel = (int)dataRow["ShopLevel"],
						BufferLevel = (int)dataRow["BufferLevel"],
						FightPower = (int)dataRow["FightPower"],
						IsSystemCreate = (bool)dataRow["IsSystemCreate"],
						IsActive = (bool)dataRow["IsActive"],
						BadgeID = (int)dataRow["BadgeID"],
						BadgeBuyTime = ((dataRow["BadgeBuyTime"] == null) ? "" : dataRow["BadgeBuyTime"].ToString()),
						ValidDate = (int)dataRow["ValidDate"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return list.ToArray();
		}

		// Token: 0x06008821 RID: 34849 RVA: 0x002CB2D8 File Offset: 0x002C94D8
		public bool UpdateConsortiaDescription(int consortiaID, int userID, string description, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Description", description),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[3].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_ConsortiaDescription_Update", array);
				int num = (int)array[3].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				bool flag2 = num3 == 2;
				if (flag2)
				{
					msg = "ConsortiaBussiness.UpdateConsortiaDescription.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008822 RID: 34850 RVA: 0x002CB3D0 File Offset: 0x002C95D0
		public bool UpdateConsortiaPlacard(int consortiaID, int userID, string placard, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Placard", placard),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[3].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_ConsortiaPlacard_Update", array);
				int num = (int)array[3].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				bool flag2 = num3 == 2;
				if (flag2)
				{
					msg = "ConsortiaBussiness.UpdateConsortiaPlacard.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008823 RID: 34851 RVA: 0x002CB4C8 File Offset: 0x002C96C8
		public bool UpdateConsortiaChairman(string nickName, int consortiaID, int userID, ref string msg, ref ConsortiaDutyInfo info, ref int tempUserID, ref string tempUserName)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[9];
				array[0] = new SqlParameter("@NickName", nickName);
				array[1] = new SqlParameter("@ConsortiaID", consortiaID);
				array[2] = new SqlParameter("@UserID", userID);
				array[3] = new SqlParameter("@Result", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[3].Direction = ParameterDirection.ReturnValue;
				array2[4] = new SqlParameter("@tempUserID", SqlDbType.Int);
				array2[4].Direction = ParameterDirection.InputOutput;
				array2[4].Value = tempUserID;
				array2[5] = new SqlParameter("@tempUserName", SqlDbType.VarChar, 100);
				array2[5].Direction = ParameterDirection.InputOutput;
				array2[5].Value = tempUserName;
				array2[6] = new SqlParameter("@tempDutyLevel", SqlDbType.Int);
				array2[6].Direction = ParameterDirection.InputOutput;
				array2[6].Value = info.Level;
				array2[7] = new SqlParameter("@tempDutyName", SqlDbType.VarChar, 100);
				array2[7].Direction = ParameterDirection.InputOutput;
				array2[7].Value = "";
				array2[8] = new SqlParameter("@tempRight", SqlDbType.Int);
				array2[8].Direction = ParameterDirection.InputOutput;
				array2[8].Value = info.Right;
				flag = this.db.RunProcedure("SP_ConsortiaChangeChairman", array2);
				int num = (int)array2[3].Value;
				flag = num == 0;
				bool flag2 = flag;
				if (flag2)
				{
					tempUserID = (int)array2[4].Value;
					tempUserName = (array2[5].Value != null) ? array2[5].Value.ToString() : "";
					info.Level = (int)array2[6].Value;
					info.DutyName = (array2[7].Value != null) ? array2[7].Value.ToString() : "";
					info.Right = (int)array2[8].Value;
				}
				int num2 = num;
				int num3 = num2;
				if (num3 != 1)
				{
					if (num3 == 2)
					{
						msg = "ConsortiaBussiness.UpdateConsortiaChairman.Msg2";
					}
				}
				else
				{
					msg = "ConsortiaBussiness.UpdateConsortiaChairman.Msg3";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008824 RID: 34852 RVA: 0x002CB728 File Offset: 0x002C9928
		public bool UpGradeConsortia(int consortiaID, int userID, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Consortia_UpGrade", array);
				int num = (int)array[2].Value;
				flag = num == 0;
				switch (num)
				{
				case 2:
					msg = "ConsortiaBussiness.UpGradeConsortia.Msg2";
					break;
				case 3:
					msg = "ConsortiaBussiness.UpGradeConsortia.Msg3";
					break;
				case 4:
					msg = "ConsortiaBussiness.UpGradeConsortia.Msg4";
					break;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008825 RID: 34853 RVA: 0x002CB820 File Offset: 0x002C9A20
		public bool UpGradeShopConsortia(int consortiaID, int userID, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Consortia_Shop_UpGrade", array);
				int num = (int)array[2].Value;
				flag = num == 0;
				switch (num)
				{
				case 2:
					msg = "ConsortiaBussiness.UpGradeShopConsortia.Msg2";
					break;
				case 3:
					msg = "ConsortiaBussiness.UpGradeShopConsortia.Msg3";
					break;
				case 4:
					msg = "ConsortiaBussiness.UpGradeShopConsortia.Msg4";
					break;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008826 RID: 34854 RVA: 0x002CB918 File Offset: 0x002C9B18
		public bool UpGradeStoreConsortia(int consortiaID, int userID, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Consortia_Store_UpGrade", array);
				int num = (int)array[2].Value;
				flag = num == 0;
				switch (num)
				{
				case 2:
					msg = "ConsortiaBussiness.UpGradeStoreConsortia.Msg2";
					break;
				case 3:
					msg = "ConsortiaBussiness.UpGradeStoreConsortia.Msg3";
					break;
				case 4:
					msg = "ConsortiaBussiness.UpGradeStoreConsortia.Msg4";
					break;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008827 RID: 34855 RVA: 0x002CBA10 File Offset: 0x002C9C10
		public bool UpGradeSmithConsortia(int consortiaID, int userID, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Consortia_Smith_UpGrade", array);
				int num = (int)array[2].Value;
				flag = num == 0;
				switch (num)
				{
				case 2:
					msg = "ConsortiaBussiness.UpGradeSmithConsortia.Msg2";
					break;
				case 3:
					msg = "ConsortiaBussiness.UpGradeSmithConsortia.Msg3";
					break;
				case 4:
					msg = "ConsortiaBussiness.UpGradeSmithConsortia.Msg4";
					break;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008828 RID: 34856 RVA: 0x002CBB08 File Offset: 0x002C9D08
		public bool UpGradeBufferConsortia(int consortiaID, int userID, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Consortia_Buffer_UpGrade", array);
				int num = (int)array[2].Value;
				flag = num == 0;
				switch (num)
				{
				case 2:
					msg = "ConsortiaBussiness.UpGradeSkillConsortia.Msg2";
					break;
				case 3:
					msg = "ConsortiaBussiness.UpGradeSkillConsortia.Msg3";
					break;
				case 4:
					msg = "ConsortiaBussiness.UpGradeSkillConsortia.Msg4";
					break;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008829 RID: 34857 RVA: 0x002CBC00 File Offset: 0x002C9E00
		public ConsortiaInfo[] GetConsortiaAll()
		{
			List<ConsortiaInfo> list = new List<ConsortiaInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Consortia_All");
				while (sqlDataReader.Read())
				{
					list.Add(new ConsortiaInfo
					{
						ConsortiaID = (int)sqlDataReader["ConsortiaID"],
						ChairmanName = ((sqlDataReader["ChairmanName"] == null) ? "" : sqlDataReader["ChairmanName"].ToString()),
						Honor = (int)sqlDataReader["Honor"],
						Level = (int)sqlDataReader["Level"],
						Riches = (int)sqlDataReader["Riches"],
						MaxCount = (int)sqlDataReader["MaxCount"],
						BuildDate = (DateTime)sqlDataReader["BuildDate"],
						IsExist = (bool)sqlDataReader["IsExist"],
						DeductDate = (DateTime)sqlDataReader["DeductDate"],
						StoreLevel = (int)sqlDataReader["StoreLevel"],
						SmithLevel = (int)sqlDataReader["SmithLevel"],
						ShopLevel = (int)sqlDataReader["ShopLevel"],
						BufferLevel = (int)sqlDataReader["BufferLevel"],
						ConsortiaName = ((sqlDataReader["ConsortiaName"] == null) ? "" : sqlDataReader["ConsortiaName"].ToString()),
						IsSystemCreate = (bool)sqlDataReader["IsSystemCreate"],
						IsActive = (bool)sqlDataReader["IsActive"],
						BadgeID = (int)sqlDataReader["BadgeID"],
						BadgeBuyTime = ((sqlDataReader["BadgeBuyTime"] == null) ? "" : sqlDataReader["BadgeBuyTime"].ToString()),
						ValidDate = (int)sqlDataReader["ValidDate"],
						IsDirty = false
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x0600882A RID: 34858 RVA: 0x002CBED8 File Offset: 0x002CA0D8
		public ConsortiaInfo GetConsortiaSingle(int id)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", id)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Consortia_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return new ConsortiaInfo
					{
						ConsortiaID = (int)sqlDataReader["ConsortiaID"],
						BuildDate = (DateTime)sqlDataReader["BuildDate"],
						CelebCount = (int)sqlDataReader["CelebCount"],
						ChairmanID = (int)sqlDataReader["ChairmanID"],
						ChairmanName = sqlDataReader["ChairmanName"].ToString(),
						ConsortiaName = sqlDataReader["ConsortiaName"].ToString(),
						CreatorID = (int)sqlDataReader["CreatorID"],
						CreatorName = sqlDataReader["CreatorName"].ToString(),
						Description = sqlDataReader["Description"].ToString(),
						Honor = (int)sqlDataReader["Honor"],
						IsExist = (bool)sqlDataReader["IsExist"],
						Level = (int)sqlDataReader["Level"],
						MaxCount = (int)sqlDataReader["MaxCount"],
						Placard = sqlDataReader["Placard"].ToString(),
						IP = sqlDataReader["IP"].ToString(),
						Port = (int)sqlDataReader["Port"],
						Repute = (int)sqlDataReader["Repute"],
						Count = (int)sqlDataReader["Count"],
						Riches = (int)sqlDataReader["Riches"],
						DeductDate = (DateTime)sqlDataReader["DeductDate"],
						StoreLevel = (int)sqlDataReader["StoreLevel"],
						SmithLevel = (int)sqlDataReader["SmithLevel"],
						ShopLevel = (int)sqlDataReader["ShopLevel"],
						BufferLevel = (int)sqlDataReader["BufferLevel"],
						IsSystemCreate = (bool)sqlDataReader["IsSystemCreate"],
						IsActive = (bool)sqlDataReader["IsActive"],
						BadgeID = (int)sqlDataReader["BadgeID"],
						BadgeBuyTime = ((sqlDataReader["BadgeBuyTime"] == DBNull.Value) ? "" : ((string)sqlDataReader["BadgeBuyTime"])),
						ValidDate = (int)sqlDataReader["ValidDate"],
						DateOpenTask = ((sqlDataReader["DateOpenTask"] == DBNull.Value) ? DateTime.Now.AddYears(-1) : ((DateTime)sqlDataReader["DateOpenTask"]))
					};
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x0600882B RID: 34859 RVA: 0x002CC2AC File Offset: 0x002CA4AC
		public bool ConsortiaFight(int consortiWin, int consortiaLose, int playerCount, out int riches, int state, int totalKillHealth, float richesRate)
		{
			bool flag = false;
			riches = 0;
			try
			{
				SqlParameter[] array = new SqlParameter[8];
				array[0] = new SqlParameter("@ConsortiaWin", consortiWin);
				array[1] = new SqlParameter("@ConsortiaLose", consortiaLose);
				array[2] = new SqlParameter("@PlayerCount", playerCount);
				array[3] = new SqlParameter("@Riches", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[3].Direction = ParameterDirection.InputOutput;
				array2[3].Value = riches;
				array2[4] = new SqlParameter("@Result", SqlDbType.Int);
				array2[4].Direction = ParameterDirection.ReturnValue;
				array2[5] = new SqlParameter("@State", state);
				array2[6] = new SqlParameter("@TotalKillHealth", totalKillHealth);
				array2[7] = new SqlParameter("@RichesRate", richesRate);
				flag = this.db.RunProcedure("SP_Consortia_Fight", array2);
				riches = (int)array2[3].Value;
				int num = (int)array2[4].Value;
				flag = num == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("ConsortiaFight", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600882C RID: 34860 RVA: 0x002CC420 File Offset: 0x002CA620
		public bool ConsortiaRichAdd(int consortiID, ref int riches)
		{
			return this.ConsortiaRichAdd(consortiID, ref riches, 0, "");
		}

		// Token: 0x0600882D RID: 34861 RVA: 0x002CC440 File Offset: 0x002CA640
		public bool ConsortiaRichAdd(int consortiID, ref int riches, int type, string username)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[5];
				array[0] = new SqlParameter("@ConsortiaID", consortiID);
				array[1] = new SqlParameter("@Riches", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[1].Direction = ParameterDirection.InputOutput;
				array2[1].Value = riches;
				array2[2] = new SqlParameter("@Result", SqlDbType.Int);
				array2[2].Direction = ParameterDirection.ReturnValue;
				array2[3] = new SqlParameter("@Type", type);
				array2[4] = new SqlParameter("@UserName", username);
				flag = this.db.RunProcedure("SP_Consortia_Riches_Add", array2);
				riches = (int)array2[1].Value;
				int num = (int)array2[2].Value;
				flag = num == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("ConsortiaRichAdd", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600882E RID: 34862 RVA: 0x002CC554 File Offset: 0x002CA754
		public bool ScanConsortia(ref string noticeID)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[2];
				array[0] = new SqlParameter("@NoticeID", SqlDbType.NVarChar, 4000);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@Result", SqlDbType.Int);
				array2[1].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_Consortia_Scan", array2);
				int num = (int)array2[1].Value;
				flag = num == 0;
				bool flag2 = flag;
				if (flag2)
				{
					noticeID = (array2[0].Value != null) ? array2[0].Value.ToString() : "";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600882F RID: 34863 RVA: 0x002CC634 File Offset: 0x002CA834
		public bool RenameConsortia(int ConsortiaID, string nickName, string newNickName)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", ConsortiaID),
					new SqlParameter("@NickName", nickName),
					new SqlParameter("@NewNickName", newNickName),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[3].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_Users_RenameConsortia", array);
				int num = (int)array[3].Value;
				flag = num == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("RenameNick", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008830 RID: 34864 RVA: 0x002CC6FC File Offset: 0x002CA8FC
		public bool GetConsortiaCheckByConsortiaName(string consortiaName)
		{
			bool flag = false;
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaName", SqlDbType.NVarChar, 200)
				};
				array[0].Value = consortiaName;
				bool reader = this.db.GetReader(ref sqlDataReader, "SP_Consortia_CheckByConsortiaName", array);
				if (reader)
				{
					while (sqlDataReader.Read())
					{
						flag = true;
					}
				}
			}
			catch
			{
				flag = true;
				throw new Exception();
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return flag;
		}

		// Token: 0x06008831 RID: 34865 RVA: 0x002CC7B4 File Offset: 0x002CA9B4
		public bool UpdateConsortiaRiches(int consortiaID, int userID, int Riches, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Riches", Riches),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[3].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_ConsortiaRiches_Update", array);
				int num = (int)array[3].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				bool flag2 = num3 == 2;
				if (flag2)
				{
					msg = "ConsortiaBussiness.UpdateConsortiaRiches.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008832 RID: 34866 RVA: 0x002CC8B4 File Offset: 0x002CAAB4
		public bool ConsortiaRichRemove(int consortiID, ref int riches)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[3];
				array[0] = new SqlParameter("@ConsortiaID", consortiID);
				array[1] = new SqlParameter("@Riches", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[1].Direction = ParameterDirection.InputOutput;
				array2[1].Value = riches;
				array2[2] = new SqlParameter("@Result", SqlDbType.Int);
				array2[2].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_Consortia_Riches_Remove", array2);
				riches = (int)array2[1].Value;
				int num = (int)array2[2].Value;
				flag = num == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Consortia_Riches_Remove", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008833 RID: 34867 RVA: 0x002CC9A8 File Offset: 0x002CABA8
		public bool UpdateConsotiaApplyState(int consortiaID, int userID, bool state, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@State", state),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[3].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Consortia_Apply_State", array);
				int num = (int)array[3].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				bool flag2 = num3 == 2;
				if (flag2)
				{
					msg = "ConsortiaBussiness.UpdateConsotiaApplyState.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008834 RID: 34868 RVA: 0x002CCA94 File Offset: 0x002CAC94
		public bool AddConsortiaApplyUsers(ConsortiaApplyUserInfo info, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[9];
				array[0] = new SqlParameter("@ID", info.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.InputOutput;
				array2[1] = new SqlParameter("@ApplyDate", info.ApplyDate);
				array2[2] = new SqlParameter("@ConsortiaID", info.ConsortiaID);
				array2[3] = new SqlParameter("@ConsortiaName", (info.ConsortiaName == null) ? "" : info.ConsortiaName);
				array2[4] = new SqlParameter("@IsExist", info.IsExist);
				array2[5] = new SqlParameter("@Remark", (info.Remark == null) ? "" : info.Remark);
				array2[6] = new SqlParameter("@UserID", info.UserID);
				array2[7] = new SqlParameter("@UserName", (info.UserName == null) ? "" : info.UserName);
				array2[8] = new SqlParameter("@Result", SqlDbType.Int);
				array2[8].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_ConsortiaApplyUser_Add", array2);
				info.ID = (int)array2[0].Value;
				int num = (int)array2[8].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				if (num3 != 2)
				{
					if (num3 != 6)
					{
						if (num3 == 7)
						{
							msg = "ConsortiaBussiness.AddConsortiaApplyUsers.Msg7";
						}
					}
					else
					{
						msg = "ConsortiaBussiness.AddConsortiaApplyUsers.Msg6";
					}
				}
				else
				{
					msg = "ConsortiaBussiness.AddConsortiaApplyUsers.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008835 RID: 34869 RVA: 0x002CCC88 File Offset: 0x002CAE88
		public bool DeleteConsortiaApplyUsers(int applyID, int userID, int consortiaID, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", applyID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[3].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_ConsortiaApplyUser_Delete", array);
				int num = (int)array[3].Value;
				if (!true)
				{
				}
				bool flag2 = num == 0 || num == 3;
				if (!true)
				{
				}
				bool flag3 = flag2;
				bool flag4 = flag3;
				flag = flag4;
				int num2 = num;
				int num3 = num2;
				bool flag5 = num3 == 2;
				if (flag5)
				{
					msg = "ConsortiaBussiness.DeleteConsortiaApplyUsers.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008836 RID: 34870 RVA: 0x002CCDB0 File Offset: 0x002CAFB0
		public bool PassConsortiaApplyUsers(int applyID, int userID, string userName, int consortiaID, ref string msg, ConsortiaUserInfo info, ref int consortiaRepute)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[25];
				array[0] = new SqlParameter("@ID", applyID);
				array[1] = new SqlParameter("@UserID", userID);
				array[2] = new SqlParameter("@UserName", userName);
				array[3] = new SqlParameter("@ConsortiaID", consortiaID);
				array[4] = new SqlParameter("@Result", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[4].Direction = ParameterDirection.ReturnValue;
				array2[5] = new SqlParameter("@tempID", SqlDbType.Int);
				array2[5].Direction = ParameterDirection.InputOutput;
				array2[5].Value = info.UserID;
				array2[6] = new SqlParameter("@tempName", SqlDbType.NVarChar, 100);
				array2[6].Direction = ParameterDirection.InputOutput;
				array2[6].Value = "";
				array2[7] = new SqlParameter("@tempDutyID", SqlDbType.Int);
				array2[7].Direction = ParameterDirection.InputOutput;
				array2[7].Value = info.DutyID;
				array2[8] = new SqlParameter("@tempDutyName", SqlDbType.NVarChar, 100);
				array2[8].Direction = ParameterDirection.InputOutput;
				array2[8].Value = "";
				array2[9] = new SqlParameter("@tempOffer", SqlDbType.Int);
				array2[9].Direction = ParameterDirection.InputOutput;
				array2[9].Value = info.Offer;
				array2[10] = new SqlParameter("@tempRichesOffer", SqlDbType.Int);
				array2[10].Direction = ParameterDirection.InputOutput;
				array2[10].Value = info.RichesOffer;
				array2[11] = new SqlParameter("@tempRichesRob", SqlDbType.Int);
				array2[11].Direction = ParameterDirection.InputOutput;
				array2[11].Value = info.RichesRob;
				array2[12] = new SqlParameter("@tempLastDate", SqlDbType.DateTime);
				array2[12].Direction = ParameterDirection.InputOutput;
				array2[12].Value = DateTime.Now;
				array2[13] = new SqlParameter("@tempWin", SqlDbType.Int);
				array2[13].Direction = ParameterDirection.InputOutput;
				array2[13].Value = info.Win;
				array2[14] = new SqlParameter("@tempTotal", SqlDbType.Int);
				array2[14].Direction = ParameterDirection.InputOutput;
				array2[14].Value = info.Total;
				array2[15] = new SqlParameter("@tempEscape", SqlDbType.Int);
				array2[15].Direction = ParameterDirection.InputOutput;
				array2[15].Value = info.Escape;
				array2[16] = new SqlParameter("@tempGrade", SqlDbType.Int);
				array2[16].Direction = ParameterDirection.InputOutput;
				array2[16].Value = info.Grade;
				array2[17] = new SqlParameter("@tempLevel", SqlDbType.Int);
				array2[17].Direction = ParameterDirection.InputOutput;
				array2[17].Value = info.Level;
				array2[18] = new SqlParameter("@tempCUID", SqlDbType.Int);
				array2[18].Direction = ParameterDirection.InputOutput;
				array2[18].Value = info.ID;
				array2[19] = new SqlParameter("@tempState", SqlDbType.Int);
				array2[19].Direction = ParameterDirection.InputOutput;
				array2[19].Value = info.State;
				array2[20] = new SqlParameter("@tempSex", SqlDbType.Bit);
				array2[20].Direction = ParameterDirection.InputOutput;
				array2[20].Value = info.Sex;
				array2[21] = new SqlParameter("@tempDutyRight", SqlDbType.Int);
				array2[21].Direction = ParameterDirection.InputOutput;
				array2[21].Value = info.Right;
				array2[22] = new SqlParameter("@tempConsortiaRepute", SqlDbType.Int);
				array2[22].Direction = ParameterDirection.InputOutput;
				array2[22].Value = consortiaRepute;
				array2[23] = new SqlParameter("@tempLoginName", SqlDbType.NVarChar, 200);
				array2[23].Direction = ParameterDirection.InputOutput;
				array2[23].Value = consortiaRepute;
				array2[24] = new SqlParameter("@tempFightPower", SqlDbType.Int);
				array2[24].Direction = ParameterDirection.InputOutput;
				array2[24].Value = info.FightPower;
				this.db.RunProcedure("SP_ConsortiaApplyUser_Pass", array2);
				int num = (int)array2[4].Value;
				flag = num == 0;
				bool flag2 = flag;
				if (flag2)
				{
					info.UserID = (int)array2[5].Value;
					info.UserName = (array2[6].Value != null) ? array2[6].Value.ToString() : "";
					info.DutyID = (int)array2[7].Value;
					info.DutyName = (array2[8].Value != null) ? array2[8].Value.ToString() : "";
					info.Offer = (int)array2[9].Value;
					info.RichesOffer = (int)array2[10].Value;
					info.RichesRob = (int)array2[11].Value;
					info.LastDate = (DateTime)array2[12].Value;
					info.Win = (int)array2[13].Value;
					info.Total = (int)array2[14].Value;
					info.Escape = (int)array2[15].Value;
					info.Grade = (int)array2[16].Value;
					info.Level = (int)array2[17].Value;
					info.ID = (int)array2[18].Value;
					info.State = (int)array2[19].Value;
					info.Sex = (bool)array2[20].Value;
					info.Right = (int)array2[21].Value;
					consortiaRepute = (int)array2[22].Value;
					info.LoginName = (array2[23].Value != null) ? array2[23].Value.ToString() : "";
					info.FightPower = (int)array2[24].Value;
				}
				switch (num)
				{
				case 2:
					msg = "ConsortiaBussiness.PassConsortiaApplyUsers.Msg2";
					break;
				case 3:
					msg = "ConsortiaBussiness.PassConsortiaApplyUsers.Msg3";
					break;
				case 6:
					msg = "ConsortiaBussiness.PassConsortiaApplyUsers.Msg6";
					break;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008837 RID: 34871 RVA: 0x002CD448 File Offset: 0x002CB648
		public ConsortiaApplyUserInfo[] GetConsortiaApplyUserPage(int page, int size, ref int total, int order, int consortiaID, int applyID, int userID)
		{
			List<ConsortiaApplyUserInfo> list = new List<ConsortiaApplyUserInfo>();
			try
			{
				string text = " IsExist=1 ";
				bool flag = consortiaID != -1;
				if (flag)
				{
					text = text + " and ConsortiaID =" + consortiaID.ToString() + " ";
				}
				bool flag2 = applyID != -1;
				if (flag2)
				{
					text = text + " and ID =" + applyID.ToString() + " ";
				}
				bool flag3 = userID != -1;
				if (flag3)
				{
					text = text + " and UserID ='" + userID.ToString() + "' ";
				}
				string text2 = "ID";
				if (order != 1)
				{
					if (order == 2)
					{
						text2 = "ApplyDate,ID";
					}
				}
				else
				{
					text2 = "UserName,ID";
				}
				foreach (object obj in base.GetPage("V_Consortia_Apply_Users", text, page, size, "*", text2, "ID", ref total).Rows)
				{
					DataRow dataRow = (DataRow)obj;
					list.Add(new ConsortiaApplyUserInfo
					{
						ID = (int)dataRow["ID"],
						ApplyDate = (DateTime)dataRow["ApplyDate"],
						ConsortiaID = (int)dataRow["ConsortiaID"],
						ConsortiaName = dataRow["ConsortiaName"].ToString(),
						ID = (int)dataRow["ID"],
						IsExist = (bool)dataRow["IsExist"],
						Remark = dataRow["Remark"].ToString(),
						UserID = (int)dataRow["UserID"],
						UserName = dataRow["UserName"].ToString(),
						TypeVIP = (int)dataRow["TypeVIP"],
						VIPLevel = (int)dataRow["VIPLevel"],
						UserLevel = (int)dataRow["Grade"],
						Win = (int)dataRow["Win"],
						Total = (int)dataRow["Total"],
						Repute = (int)dataRow["Repute"],
						FightPower = (int)dataRow["FightPower"],
						Offer = (int)dataRow["Offer"],
						ChairmanID = (int)dataRow["ChairmanID"],
						ChairmanName = dataRow["ChairmanName"].ToString()
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008838 RID: 34872 RVA: 0x002CD7C0 File Offset: 0x002CB9C0
		public bool AddConsortiaInviteUsers(ConsortiaInviteUserInfo info, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[11];
				array[0] = new SqlParameter("@ID", info.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.InputOutput;
				array2[1] = new SqlParameter("@ConsortiaID", info.ConsortiaID);
				array2[2] = new SqlParameter("@ConsortiaName", (info.ConsortiaName == null) ? "" : info.ConsortiaName);
				array2[3] = new SqlParameter("@InviteDate", info.InviteDate);
				array2[4] = new SqlParameter("@InviteID", info.InviteID);
				array2[5] = new SqlParameter("@InviteName", (info.InviteName == null) ? "" : info.InviteName);
				array2[6] = new SqlParameter("@IsExist", info.IsExist);
				array2[7] = new SqlParameter("@Remark", (info.Remark == null) ? "" : info.Remark);
				array2[8] = new SqlParameter("@UserID", info.UserID);
				array2[8].Direction = ParameterDirection.InputOutput;
				array2[9] = new SqlParameter("@UserName", (info.UserName == null) ? "" : info.UserName);
				array2[10] = new SqlParameter("@Result", SqlDbType.Int);
				array2[10].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_ConsortiaInviteUser_Add", array2);
				info.ID = (int)array2[0].Value;
				info.UserID = (int)array2[8].Value;
				int num = (int)array2[10].Value;
				flag = num == 0;
				switch (num)
				{
				case 2:
					msg = "ConsortiaBussiness.AddConsortiaInviteUsers.Msg2";
					break;
				case 4:
					msg = "ConsortiaBussiness.AddConsortiaInviteUsers.Msg4";
					break;
				case 5:
					msg = "ConsortiaBussiness.AddConsortiaInviteUsers.Msg5";
					break;
				case 6:
					msg = "ConsortiaBussiness.AddConsortiaInviteUsers.Msg6";
					break;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008839 RID: 34873 RVA: 0x002CDA28 File Offset: 0x002CBC28
		public bool DeleteConsortiaInviteUsers(int intiveID, int userID)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", intiveID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_ConsortiaInviteUser_Delete", array);
				int num = (int)array[2].Value;
				flag = num == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600883A RID: 34874 RVA: 0x002CDAE8 File Offset: 0x002CBCE8
		public bool PassConsortiaInviteUsers(int inviteID, int userID, string userName, ref int consortiaID, ref string consortiaName, ref string msg, ConsortiaUserInfo info, ref int tempID, ref string tempName, ref int consortiaRepute)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[24];
				array[0] = new SqlParameter("@ID", inviteID);
				array[1] = new SqlParameter("@UserID", userID);
				array[2] = new SqlParameter("@UserName", userName);
				array[3] = new SqlParameter("@ConsortiaID", consortiaID);
				SqlParameter[] array2 = array;
				array2[3].Direction = ParameterDirection.InputOutput;
				array2[4] = new SqlParameter("@ConsortiaName", SqlDbType.NVarChar, 100);
				array2[4].Value = consortiaName;
				array2[4].Direction = ParameterDirection.InputOutput;
				array2[5] = new SqlParameter("@Result", SqlDbType.Int);
				array2[5].Direction = ParameterDirection.ReturnValue;
				array2[6] = new SqlParameter("@tempName", SqlDbType.NVarChar, 100);
				array2[6].Direction = ParameterDirection.InputOutput;
				array2[6].Value = tempName;
				array2[7] = new SqlParameter("@tempDutyID", SqlDbType.Int);
				array2[7].Direction = ParameterDirection.InputOutput;
				array2[7].Value = info.DutyID;
				array2[8] = new SqlParameter("@tempDutyName", SqlDbType.NVarChar, 100);
				array2[8].Direction = ParameterDirection.InputOutput;
				array2[8].Value = "";
				array2[9] = new SqlParameter("@tempOffer", SqlDbType.Int);
				array2[9].Direction = ParameterDirection.InputOutput;
				array2[9].Value = info.Offer;
				array2[10] = new SqlParameter("@tempRichesOffer", SqlDbType.Int);
				array2[10].Direction = ParameterDirection.InputOutput;
				array2[10].Value = info.RichesOffer;
				array2[11] = new SqlParameter("@tempRichesRob", SqlDbType.Int);
				array2[11].Direction = ParameterDirection.InputOutput;
				array2[11].Value = info.RichesRob;
				array2[12] = new SqlParameter("@tempLastDate", SqlDbType.DateTime);
				array2[12].Direction = ParameterDirection.InputOutput;
				array2[12].Value = DateTime.Now;
				array2[13] = new SqlParameter("@tempWin", SqlDbType.Int);
				array2[13].Direction = ParameterDirection.InputOutput;
				array2[13].Value = info.Win;
				array2[14] = new SqlParameter("@tempTotal", SqlDbType.Int);
				array2[14].Direction = ParameterDirection.InputOutput;
				array2[14].Value = info.Total;
				array2[15] = new SqlParameter("@tempEscape", SqlDbType.Int);
				array2[15].Direction = ParameterDirection.InputOutput;
				array2[15].Value = info.Escape;
				array2[16] = new SqlParameter("@tempID", SqlDbType.Int);
				array2[16].Direction = ParameterDirection.InputOutput;
				array2[16].Value = tempID;
				array2[17] = new SqlParameter("@tempGrade", SqlDbType.Int);
				array2[17].Direction = ParameterDirection.InputOutput;
				array2[17].Value = info.Level;
				array2[18] = new SqlParameter("@tempLevel", SqlDbType.Int);
				array2[18].Direction = ParameterDirection.InputOutput;
				array2[18].Value = info.Level;
				array2[19] = new SqlParameter("@tempCUID", SqlDbType.Int);
				array2[19].Direction = ParameterDirection.InputOutput;
				array2[19].Value = info.ID;
				array2[20] = new SqlParameter("@tempState", SqlDbType.Int);
				array2[20].Direction = ParameterDirection.InputOutput;
				array2[20].Value = info.State;
				array2[21] = new SqlParameter("@tempSex", SqlDbType.Bit);
				array2[21].Direction = ParameterDirection.InputOutput;
				array2[21].Value = info.Sex;
				array2[22] = new SqlParameter("@tempRight", SqlDbType.Int);
				array2[22].Direction = ParameterDirection.InputOutput;
				array2[22].Value = info.Right;
				array2[23] = new SqlParameter("@tempConsortiaRepute", SqlDbType.Int);
				array2[23].Direction = ParameterDirection.InputOutput;
				array2[23].Value = consortiaRepute;
				this.db.RunProcedure("SP_ConsortiaInviteUser_Pass", array2);
				int num = (int)array2[5].Value;
				flag = num == 0;
				bool flag2 = flag;
				if (flag2)
				{
					consortiaID = (int)array2[3].Value;
					consortiaName = (array2[4].Value != null) ? array2[4].Value.ToString() : "";
					tempName = (array2[6].Value != null) ? array2[6].Value.ToString() : "";
					info.DutyID = (int)array2[7].Value;
					info.DutyName = (array2[8].Value != null) ? array2[8].Value.ToString() : "";
					info.Offer = (int)array2[9].Value;
					info.RichesOffer = (int)array2[10].Value;
					info.RichesRob = (int)array2[11].Value;
					info.LastDate = (DateTime)array2[12].Value;
					info.Win = (int)array2[13].Value;
					info.Total = (int)array2[14].Value;
					info.Escape = (int)array2[15].Value;
					tempID = (int)array2[16].Value;
					info.Grade = (int)array2[17].Value;
					info.Level = (int)array2[18].Value;
					info.ID = (int)array2[19].Value;
					info.State = (int)array2[20].Value;
					info.Sex = (bool)array2[21].Value;
					info.Right = (int)array2[22].Value;
					consortiaRepute = (int)array2[23].Value;
				}
				int num2 = num;
				int num3 = num2;
				if (num3 != 3)
				{
					if (num3 == 6)
					{
						msg = "ConsortiaBussiness.PassConsortiaInviteUsers.Msg6";
					}
				}
				else
				{
					msg = "ConsortiaBussiness.PassConsortiaInviteUsers.Msg3";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600883B RID: 34875 RVA: 0x002CE118 File Offset: 0x002CC318
		public ConsortiaInviteUserInfo[] GetConsortiaInviteUserPage(int page, int size, ref int total, int order, int userID, int inviteID)
		{
			List<ConsortiaInviteUserInfo> list = new List<ConsortiaInviteUserInfo>();
			try
			{
				string text = " IsExist=1 ";
				bool flag = userID != -1;
				if (flag)
				{
					text = text + " and UserID =" + userID.ToString() + " ";
				}
				bool flag2 = inviteID != -1;
				if (flag2)
				{
					text = text + " and UserID =" + inviteID.ToString() + " ";
				}
				string text2 = "ConsortiaName";
				switch (order)
				{
				case 1:
					text2 = "Repute";
					break;
				case 2:
					text2 = "ChairmanName";
					break;
				case 3:
					text2 = "Count";
					break;
				case 4:
					text2 = "CelebCount";
					break;
				case 5:
					text2 = "Honor";
					break;
				}
				text2 += ",ID ";
				DataTable page2 = base.GetPage("V_Consortia_Invite", text, page, size, "*", text2, "ID", ref total);
				foreach (object obj in page2.Rows)
				{
					DataRow dataRow = (DataRow)obj;
					list.Add(new ConsortiaInviteUserInfo
					{
						ID = (int)dataRow["ID"],
						CelebCount = (int)dataRow["CelebCount"],
						ChairmanName = dataRow["ChairmanName"].ToString(),
						ConsortiaID = (int)dataRow["ConsortiaID"],
						ConsortiaName = dataRow["ConsortiaName"].ToString(),
						Count = (int)dataRow["Count"],
						Honor = (int)dataRow["Honor"],
						InviteDate = (DateTime)dataRow["InviteDate"],
						InviteID = (int)dataRow["InviteID"],
						InviteName = dataRow["InviteName"].ToString(),
						IsExist = (bool)dataRow["IsExist"],
						Remark = dataRow["Remark"].ToString(),
						Repute = (int)dataRow["Repute"],
						UserID = (int)dataRow["UserID"],
						UserName = dataRow["UserName"].ToString()
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return list.ToArray();
		}

		// Token: 0x0600883C RID: 34876 RVA: 0x002CE458 File Offset: 0x002CC658
		public bool DeleteConsortiaUser(int userID, int kickUserID, int consortiaID, ref string msg, ref string nickName)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[5];
				array[0] = new SqlParameter("@UserID", userID);
				array[1] = new SqlParameter("@KickUserID", kickUserID);
				array[2] = new SqlParameter("@ConsortiaID", consortiaID);
				array[3] = new SqlParameter("@Result", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[3].Direction = ParameterDirection.ReturnValue;
				array2[4] = new SqlParameter("@NickName", SqlDbType.VarChar, 200);
				array2[4].Direction = ParameterDirection.InputOutput;
				array2[4].Value = nickName;
				this.db.RunProcedure("SP_ConsortiaUser_Delete", array2);
				int num = (int)array2[3].Value;
				bool flag2 = num == 0;
				if (flag2)
				{
					flag = true;
					nickName = (array2[4].Value != null) ? array2[4].Value.ToString() : "";
				}
				switch (num)
				{
				case 2:
					msg = "ConsortiaBussiness.DeleteConsortiaUser.Msg2";
					break;
				case 3:
					msg = "ConsortiaBussiness.DeleteConsortiaUser.Msg3";
					break;
				case 4:
					msg = "ConsortiaBussiness.DeleteConsortiaUser.Msg4";
					break;
				case 5:
					msg = "ConsortiaBussiness.DeleteConsortiaUser.Msg5";
					break;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600883D RID: 34877 RVA: 0x002CE5C4 File Offset: 0x002CC7C4
		public bool UpdateConsortiaIsBanChat(int banUserID, int consortiaID, int userID, bool isBanChat, ref int tempID, ref string tempName, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[7];
				array[0] = new SqlParameter("@ID", banUserID);
				array[1] = new SqlParameter("@ConsortiaID", consortiaID);
				array[2] = new SqlParameter("@UserID", userID);
				array[3] = new SqlParameter("@IsBanChat", isBanChat);
				array[4] = new SqlParameter("@TempID", tempID);
				SqlParameter[] array2 = array;
				array2[4].Direction = ParameterDirection.InputOutput;
				array2[5] = new SqlParameter("@TempName", SqlDbType.NVarChar, 100);
				array2[5].Value = tempName;
				array2[5].Direction = ParameterDirection.InputOutput;
				array2[6] = new SqlParameter("@Result", SqlDbType.Int);
				array2[6].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_ConsortiaIsBanChat_Update", array2);
				int num = (int)array2[6].Value;
				tempID = (int)array2[4].Value;
				tempName = (array2[5].Value != null) ? array2[5].Value.ToString() : "";
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				if (num3 != 2)
				{
					if (num3 == 3)
					{
						msg = "ConsortiaBussiness.UpdateConsortiaIsBanChat.Msg3";
					}
				}
				else
				{
					msg = "ConsortiaBussiness.UpdateConsortiaIsBanChat.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600883E RID: 34878 RVA: 0x002CE760 File Offset: 0x002CC960
		public bool UpdateConsortiaUserRemark(int id, int consortiaID, int userID, string remark, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", id),
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Remark", remark),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[4].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_ConsortiaUserRemark_Update", array);
				int num = (int)array[4].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				bool flag2 = num3 == 2;
				if (flag2)
				{
					msg = "ConsortiaBussiness.UpdateConsortiaUserRemark.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600883F RID: 34879 RVA: 0x002CE86C File Offset: 0x002CCA6C
		public bool UpdateConsortiaUserGrade(int id, int consortiaID, int userID, bool upGrade, ref string msg, ref ConsortiaDutyInfo info, ref string tempUserName)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[9];
				array[0] = new SqlParameter("@ID", id);
				array[1] = new SqlParameter("@ConsortiaID", consortiaID);
				array[2] = new SqlParameter("@UserID", userID);
				array[3] = new SqlParameter("@UpGrade", upGrade);
				array[4] = new SqlParameter("@Result", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[4].Direction = ParameterDirection.ReturnValue;
				array2[5] = new SqlParameter("@tempUserName", SqlDbType.VarChar, 100);
				array2[5].Direction = ParameterDirection.InputOutput;
				array2[5].Value = tempUserName;
				array2[6] = new SqlParameter("@tempDutyLevel", SqlDbType.Int);
				array2[6].Direction = ParameterDirection.InputOutput;
				array2[6].Value = info.Level;
				array2[7] = new SqlParameter("@tempDutyName", SqlDbType.VarChar, 100);
				array2[7].Direction = ParameterDirection.InputOutput;
				array2[7].Value = "";
				array2[8] = new SqlParameter("@tempRight", SqlDbType.Int);
				array2[8].Direction = ParameterDirection.InputOutput;
				array2[8].Value = info.Right;
				flag = this.db.RunProcedure("SP_ConsortiaUserGrade_Update", array2);
				int num = (int)array2[4].Value;
				flag = num == 0;
				bool flag2 = flag;
				if (flag2)
				{
					tempUserName = (array2[5].Value != null) ? array2[5].Value.ToString() : "";
					info.Level = (int)array2[6].Value;
					info.DutyName = (array2[7].Value != null) ? array2[7].Value.ToString() : "";
					info.Right = (int)array2[8].Value;
				}
				switch (num)
				{
				case 2:
					msg = "ConsortiaBussiness.UpdateConsortiaUserGrade.Msg2";
					break;
				case 3:
					msg = (upGrade ? "ConsortiaBussiness.UpdateConsortiaUserGrade.Msg3" : "ConsortiaBussiness.UpdateConsortiaUserGrade.Msg10");
					break;
				case 4:
					msg = "ConsortiaBussiness.UpdateConsortiaUserGrade.Msg4";
					break;
				case 5:
					msg = "ConsortiaBussiness.UpdateConsortiaUserGrade.Msg5";
					break;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008840 RID: 34880 RVA: 0x002CEAD8 File Offset: 0x002CCCD8
		public ConsortiaUserInfo[] GetConsortiaUsersPage(int page, int size, ref int total, int order, int consortiaID, int userID, int state)
		{
			List<ConsortiaUserInfo> list = new List<ConsortiaUserInfo>();
			try
			{
				string text = " IsExist=1 ";
				bool flag = consortiaID != -1;
				if (flag)
				{
					text = text + " and ConsortiaID =" + consortiaID.ToString() + " ";
				}
				bool flag2 = userID != -1;
				if (flag2)
				{
					text = text + " and UserID =" + userID.ToString() + " ";
				}
				bool flag3 = state != -1;
				if (flag3)
				{
					text = text + " and state =" + state.ToString() + " ";
				}
				string text2 = "UserName";
				switch (order)
				{
				case 1:
					text2 = "DutyID";
					break;
				case 2:
					text2 = "Grade";
					break;
				case 3:
					text2 = "Repute";
					break;
				case 4:
					text2 = "GP";
					break;
				case 5:
					text2 = "State";
					break;
				case 6:
					text2 = "Offer";
					break;
				}
				text2 += ",ID ";
				DataTable page2 = base.GetPage("V_Consortia_Users", text, page, size, "*", text2, "ID", ref total);
				foreach (object obj in page2.Rows)
				{
					DataRow dataRow = (DataRow)obj;
					ConsortiaUserInfo consortiaUserInfo = new ConsortiaUserInfo();
					consortiaUserInfo.ID = (int)dataRow["ID"];
					consortiaUserInfo.ConsortiaID = (int)dataRow["ConsortiaID"];
					consortiaUserInfo.DutyID = (int)dataRow["DutyID"];
					consortiaUserInfo.DutyName = dataRow["DutyName"].ToString();
					consortiaUserInfo.IsExist = (bool)dataRow["IsExist"];
					consortiaUserInfo.RatifierID = (int)dataRow["RatifierID"];
					consortiaUserInfo.RatifierName = dataRow["RatifierName"].ToString();
					consortiaUserInfo.Remark = dataRow["Remark"].ToString();
					consortiaUserInfo.UserID = (int)dataRow["UserID"];
					consortiaUserInfo.UserName = dataRow["UserName"].ToString();
					consortiaUserInfo.Grade = (int)dataRow["Grade"];
					consortiaUserInfo.GP = (int)dataRow["GP"];
					consortiaUserInfo.Repute = (int)dataRow["Repute"];
					consortiaUserInfo.State = (int)dataRow["State"];
					consortiaUserInfo.Right = (int)dataRow["Right"];
					consortiaUserInfo.Offer = (int)dataRow["Offer"];
					consortiaUserInfo.Colors = dataRow["Colors"].ToString();
					consortiaUserInfo.Style = dataRow["Style"].ToString();
					consortiaUserInfo.Hide = (int)dataRow["Hide"];
					consortiaUserInfo.Skin = ((dataRow["Skin"] == null) ? "" : consortiaUserInfo.Skin);
					consortiaUserInfo.Level = (int)dataRow["Level"];
					consortiaUserInfo.LastDate = (DateTime)dataRow["LastDate"];
					consortiaUserInfo.Sex = (bool)dataRow["Sex"];
					consortiaUserInfo.IsBanChat = (bool)dataRow["IsBanChat"];
					consortiaUserInfo.Win = (int)dataRow["Win"];
					consortiaUserInfo.Total = (int)dataRow["Total"];
					consortiaUserInfo.Escape = (int)dataRow["Escape"];
					consortiaUserInfo.RichesOffer = (int)dataRow["RichesOffer"];
					consortiaUserInfo.RichesRob = (int)dataRow["RichesRob"];
					consortiaUserInfo.LoginName = ((dataRow["LoginName"] == null) ? "" : dataRow["LoginName"].ToString());
					consortiaUserInfo.Nimbus = (int)dataRow["Nimbus"];
					consortiaUserInfo.Honor = ((dataRow["Honor"] == null) ? "" : dataRow["Honor"].ToString());
					consortiaUserInfo.AchievementPoint = (int)dataRow["AchievementPoint"];
					consortiaUserInfo.FightPower = (int)dataRow["FightPower"];
					consortiaUserInfo.TypeVIP = (int)dataRow["TypeVIP"];
					consortiaUserInfo.VIPLevel = (int)dataRow["VIPLevel"];
					consortiaUserInfo.LastWeekRichesOffer = (int)dataRow["LastWeekOffer"];
					list.Add(consortiaUserInfo);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return list.ToArray();
		}

		// Token: 0x06008841 RID: 34881 RVA: 0x002CF0A8 File Offset: 0x002CD2A8
		public ConsortiaUserInfo[] GetAllMemberByConsortia(int ConsortiaID)
		{
			List<ConsortiaUserInfo> list = new List<ConsortiaUserInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", SqlDbType.Int, 4)
				};
				array[0].Value = ConsortiaID;
				this.db.GetReader(ref sqlDataReader, "SP_Consortia_Users_All", array);
				while (sqlDataReader.Read())
				{
					list.Add(this.InitConsortiaUserInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008842 RID: 34882 RVA: 0x002CF18C File Offset: 0x002CD38C
		public ConsortiaUserInfo InitConsortiaUserInfo(SqlDataReader dr)
		{
			ConsortiaUserInfo consortiaUserInfo = new ConsortiaUserInfo();
			consortiaUserInfo.ID = (int)dr["ID"];
			consortiaUserInfo.ConsortiaID = (int)dr["ConsortiaID"];
			consortiaUserInfo.DutyID = (int)dr["DutyID"];
			consortiaUserInfo.DutyName = dr["DutyName"].ToString();
			consortiaUserInfo.IsExist = (bool)dr["IsExist"];
			consortiaUserInfo.RatifierID = (int)dr["RatifierID"];
			consortiaUserInfo.RatifierName = dr["RatifierName"].ToString();
			consortiaUserInfo.Remark = dr["Remark"].ToString();
			consortiaUserInfo.UserID = (int)dr["UserID"];
			consortiaUserInfo.UserName = dr["UserName"].ToString();
			consortiaUserInfo.Grade = (int)dr["Grade"];
			consortiaUserInfo.GP = (int)dr["GP"];
			consortiaUserInfo.Repute = (int)dr["Repute"];
			consortiaUserInfo.State = (int)dr["State"];
			consortiaUserInfo.Right = (int)dr["Right"];
			consortiaUserInfo.Offer = (int)dr["Offer"];
			consortiaUserInfo.Colors = dr["Colors"].ToString();
			consortiaUserInfo.Style = dr["Style"].ToString();
			consortiaUserInfo.Hide = (int)dr["Hide"];
			consortiaUserInfo.Skin = ((dr["Skin"] == null) ? "" : consortiaUserInfo.Skin);
			consortiaUserInfo.Level = (int)dr["Level"];
			consortiaUserInfo.LastDate = (DateTime)dr["LastDate"];
			consortiaUserInfo.Sex = (bool)dr["Sex"];
			consortiaUserInfo.IsBanChat = (bool)dr["IsBanChat"];
			consortiaUserInfo.Win = (int)dr["Win"];
			consortiaUserInfo.Total = (int)dr["Total"];
			consortiaUserInfo.Escape = (int)dr["Escape"];
			consortiaUserInfo.RichesOffer = (int)dr["RichesOffer"];
			consortiaUserInfo.RichesRob = (int)dr["RichesRob"];
			consortiaUserInfo.LoginName = ((dr["LoginName"] == null) ? "" : dr["LoginName"].ToString());
			consortiaUserInfo.Nimbus = (int)dr["Nimbus"];
			consortiaUserInfo.Honor = ((dr["Honor"] == null) ? "" : dr["Honor"].ToString());
			consortiaUserInfo.AchievementPoint = (int)dr["AchievementPoint"];
			consortiaUserInfo.FightPower = (int)dr["FightPower"];
			consortiaUserInfo.TypeVIP = (int)dr["TypeVIP"];
			consortiaUserInfo.VIPLevel = (int)dr["VIPLevel"];
			consortiaUserInfo.LastWeekRichesOffer = (int)dr["LastWeekOffer"];
			return consortiaUserInfo;
		}

		// Token: 0x06008843 RID: 34883 RVA: 0x002CF52C File Offset: 0x002CD72C
		public bool AddConsortiaDuty(ConsortiaDutyInfo info, int userID, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[7];
				array[0] = new SqlParameter("@DutyID", info.DutyID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.InputOutput;
				array2[1] = new SqlParameter("@ConsortiaID", info.ConsortiaID);
				array2[2] = new SqlParameter("@DutyName", info.DutyName);
				array2[3] = new SqlParameter("@Level", info.Level);
				array2[4] = new SqlParameter("@UserID", userID);
				array2[5] = new SqlParameter("@Right", info.Right);
				array2[6] = new SqlParameter("@Result", SqlDbType.Int);
				array2[6].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_ConsortiaDuty_Add", array2);
				info.DutyID = (int)array2[0].Value;
				int num = (int)array2[6].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				if (num3 != 2)
				{
					if (num3 == 3)
					{
						msg = "ConsortiaBussiness.AddConsortiaDuty.Msg3";
					}
				}
				else
				{
					msg = "ConsortiaBussiness.AddConsortiaDuty.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008844 RID: 34884 RVA: 0x002CF6B8 File Offset: 0x002CD8B8
		public bool DeleteConsortiaDuty(int dutyID, int userID, int consortiaID, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", userID),
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@DutyID", dutyID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[3].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_ConsortiaDuty_Delete", array);
				int num = (int)array[3].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				if (num3 != 2)
				{
					if (num3 == 3)
					{
						msg = "ConsortiaBussiness.DeleteConsortiaDuty.Msg3";
					}
				}
				else
				{
					msg = "ConsortiaBussiness.DeleteConsortiaDuty.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008845 RID: 34885 RVA: 0x002CF7B0 File Offset: 0x002CD9B0
		public bool UpdateConsortiaDuty(ConsortiaDutyInfo info, int userID, int updateType, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[8];
				array[0] = new SqlParameter("@DutyID", info.DutyID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.InputOutput;
				array2[1] = new SqlParameter("@ConsortiaID", info.ConsortiaID);
				array2[2] = new SqlParameter("@DutyName", SqlDbType.NVarChar, 100);
				array2[2].Direction = ParameterDirection.InputOutput;
				array2[2].Value = info.DutyName;
				array2[3] = new SqlParameter("@Right", SqlDbType.Int);
				array2[3].Direction = ParameterDirection.InputOutput;
				array2[3].Value = info.Right;
				array2[4] = new SqlParameter("@Level", SqlDbType.Int);
				array2[4].Direction = ParameterDirection.InputOutput;
				array2[4].Value = info.Level;
				array2[5] = new SqlParameter("@UserID", userID);
				array2[6] = new SqlParameter("@UpdateType", updateType);
				array2[7] = new SqlParameter("@Result", SqlDbType.Int);
				array2[7].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_ConsortiaDuty_Update", array2);
				int num = (int)array2[7].Value;
				flag = num == 0;
				bool flag2 = flag;
				if (flag2)
				{
					info.DutyID = (int)array2[0].Value;
					info.DutyName = ((array2[2].Value == null) ? "" : array2[2].Value.ToString());
					info.Right = (int)array2[3].Value;
					info.Level = (int)array2[4].Value;
				}
				switch (num)
				{
				case 2:
					msg = "ConsortiaBussiness.UpdateConsortiaDuty.Msg2";
					break;
				case 3:
				case 4:
					msg = "ConsortiaBussiness.UpdateConsortiaDuty.Msg3";
					break;
				case 5:
					msg = "ConsortiaBussiness.DeleteConsortiaDuty.Msg5";
					break;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008846 RID: 34886 RVA: 0x002CF9FC File Offset: 0x002CDBFC
		public ConsortiaDutyInfo[] GetConsortiaDutyPage(int page, int size, ref int total, int order, int consortiaID, int dutyID)
		{
			List<ConsortiaDutyInfo> list = new List<ConsortiaDutyInfo>();
			try
			{
				string text = " IsExist=1 ";
				bool flag = consortiaID != -1;
				if (flag)
				{
					text = text + " and ConsortiaID =" + consortiaID.ToString() + " ";
				}
				bool flag2 = dutyID != -1;
				if (flag2)
				{
					text = text + " and DutyID =" + dutyID.ToString() + " ";
				}
				string text2 = "Level";
				bool flag3 = order == 1;
				if (flag3)
				{
					text2 = "DutyName";
				}
				text2 += ",DutyID ";
				DataTable page2 = base.GetPage("Sys_Consortia_Duty", text, page, size, "*", text2, "DutyID", ref total);
				foreach (object obj in page2.Rows)
				{
					DataRow dataRow = (DataRow)obj;
					list.Add(new ConsortiaDutyInfo
					{
						DutyID = (int)dataRow["DutyID"],
						ConsortiaID = (int)dataRow["ConsortiaID"],
						DutyID = (int)dataRow["DutyID"],
						DutyName = dataRow["DutyName"].ToString(),
						IsExist = (bool)dataRow["IsExist"],
						Right = (int)dataRow["Right"],
						Level = (int)dataRow["Level"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return list.ToArray();
		}

		// Token: 0x06008847 RID: 34887 RVA: 0x002CFC38 File Offset: 0x002CDE38
		public ConsortiaEventInfo[] GetConsortiaEventPage(int page, int size, ref int total, int order, int consortiaID)
		{
			List<ConsortiaEventInfo> list = new List<ConsortiaEventInfo>();
			try
			{
				string text = " IsExist=1 ";
				bool flag = consortiaID != -1;
				if (flag)
				{
					text = text + " and ConsortiaID =" + consortiaID.ToString() + " ";
				}
				string text2 = "Date desc,ID ";
				DataTable page2 = base.GetPage("Sys_Consortia_Event", text, page, size, "*", text2, "ID", ref total);
				foreach (object obj in page2.Rows)
				{
					DataRow dataRow = (DataRow)obj;
					list.Add(new ConsortiaEventInfo
					{
						ID = (int)dataRow["ID"],
						ConsortiaID = (int)dataRow["ConsortiaID"],
						Date = (DateTime)dataRow["Date"],
						IsExist = (bool)dataRow["IsExist"],
						Remark = dataRow["Remark"].ToString(),
						Type = (int)dataRow["Type"],
						NickName = dataRow["NickName"].ToString(),
						EventValue = (int)dataRow["EventValue"],
						ManagerName = dataRow["ManagerName"].ToString()
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return list.ToArray();
		}

		// Token: 0x06008848 RID: 34888 RVA: 0x002CFE60 File Offset: 0x002CE060
		public ConsortiaLevelInfo[] GetAllConsortiaLevel()
		{
			List<ConsortiaLevelInfo> list = new List<ConsortiaLevelInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Consortia_Level_All");
				while (sqlDataReader.Read())
				{
					list.Add(new ConsortiaLevelInfo
					{
						Count = (int)sqlDataReader["Count"],
						Deduct = (int)sqlDataReader["Deduct"],
						Level = (int)sqlDataReader["Level"],
						NeedGold = (int)sqlDataReader["NeedGold"],
						NeedItem = (int)sqlDataReader["NeedItem"],
						Reward = (int)sqlDataReader["Reward"],
						Riches = (int)sqlDataReader["Riches"],
						ShopRiches = (int)sqlDataReader["ShopRiches"],
						SmithRiches = (int)sqlDataReader["SmithRiches"],
						StoreRiches = (int)sqlDataReader["StoreRiches"],
						BufferRiches = (int)sqlDataReader["BufferRiches"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllConsortiaLevel", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008849 RID: 34889 RVA: 0x002D003C File Offset: 0x002CE23C
		public ConsortiaBufferTempInfo[] GetAllConsortiaBuffTemp()
		{
			List<ConsortiaBufferTempInfo> list = new List<ConsortiaBufferTempInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Consortia_Buff_Temp_All");
				while (sqlDataReader.Read())
				{
					list.Add(new ConsortiaBufferTempInfo
					{
						ID = (int)sqlDataReader["ID"],
						Name = (string)sqlDataReader["Name"],
						Descript = (string)sqlDataReader["Descript"],
						Type = (int)sqlDataReader["Type"],
						Level = (int)sqlDataReader["Level"],
						Value = (int)sqlDataReader["Value"],
						Riches = (int)sqlDataReader["Riches"],
						Metal = (int)sqlDataReader["Metal"],
						Pic = (int)sqlDataReader["Pic"],
						Group = (int)sqlDataReader["Group"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllConsortiaBuffTemp", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x0600884A RID: 34890 RVA: 0x002D0200 File Offset: 0x002CE400
		public ConsortiaBufferInfo GetUserConsortiaBufferSingle(int ID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", SqlDbType.Int, 4)
				};
				array[0].Value = ID;
				this.db.GetReader(ref sqlDataReader, "SP_User_Consortia_Buff_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return new ConsortiaBufferInfo
					{
						ConsortiaID = (int)sqlDataReader["ConsortiaID"],
						BufferID = (int)sqlDataReader["BufferID"],
						IsOpen = (bool)sqlDataReader["IsOpen"],
						BeginDate = (DateTime)sqlDataReader["BeginDate"],
						ValidDate = (int)sqlDataReader["ValidDate"],
						Type = (int)sqlDataReader["Type"],
						Value = (int)sqlDataReader["Value"]
					};
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init SP_User_Consortia_Buff_Single", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x0600884B RID: 34891 RVA: 0x002D0390 File Offset: 0x002CE590
		public bool SaveConsortiaBuffer(ConsortiaBufferInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", info.ConsortiaID),
					new SqlParameter("@BufferID", info.BufferID),
					new SqlParameter("@IsOpen", info.IsOpen),
					new SqlParameter("@BeginDate", info.BeginDate),
					new SqlParameter("@ValidDate", info.ValidDate),
					new SqlParameter("@Type ", info.Type),
					new SqlParameter("@Value", info.Value)
				};
				flag = this.db.RunProcedure("SP_User_Consortia_Buff_Add", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600884C RID: 34892 RVA: 0x002D04B4 File Offset: 0x002CE6B4
		public ConsortiaBadgeConfigInfo[] GetAllConsortiaBadgeConfig()
		{
			List<ConsortiaBadgeConfigInfo> list = new List<ConsortiaBadgeConfigInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Consortia_Badge_Config_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitConsortiaBadgeConfig(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllConsortiaBadgeConfig", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x0600884D RID: 34893 RVA: 0x002D0570 File Offset: 0x002CE770
		public ConsortiaBadgeConfigInfo InitConsortiaBadgeConfig(SqlDataReader reader)
		{
			return new ConsortiaBadgeConfigInfo
			{
				BadgeID = (int)reader["BadgeID"],
				BadgeName = ((reader["BadgeName"] == null) ? "" : reader["BadgeName"].ToString()),
				Cost = (int)reader["Cost"],
				LimitLevel = (int)reader["LimitLevel"],
				ValidDate = (int)reader["ValidDate"]
			};
		}

		// Token: 0x0600884E RID: 34894 RVA: 0x002D0610 File Offset: 0x002CE810
		public bool BuyBadge(int consortiaID, int userID, ConsortiaInfo info, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@BadgeID", info.BadgeID),
					new SqlParameter("@ValidDate", info.ValidDate),
					new SqlParameter("@BadgeBuyTime", info.BadgeBuyTime),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[5].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_Consortia_Badge_Update", array);
				int num = (int)array[5].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				bool flag2 = num3 == 2;
				if (flag2)
				{
					msg = "ConsortiaBussiness.BuyBadge.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600884F RID: 34895 RVA: 0x002D0740 File Offset: 0x002CE940
		public bool AddAndUpdateConsortiaEuqipControl(ConsortiaEquipControlInfo info, int userID, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", info.ConsortiaID),
					new SqlParameter("@Level", info.Level),
					new SqlParameter("@Type", info.Type),
					new SqlParameter("@Riches", info.Riches),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[5].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_Consortia_Equip_Control_Add", array);
				int num = (int)array[2].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				bool flag2 = num3 == 2;
				if (flag2)
				{
					msg = "ConsortiaBussiness.AddAndUpdateConsortiaEuqipControl.Msg2";
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008850 RID: 34896 RVA: 0x002D0890 File Offset: 0x002CEA90
		public ConsortiaEquipControlInfo GetConsortiaEuqipRiches(int consortiaID, int Level, int type)
		{
			ConsortiaEquipControlInfo consortiaEquipControlInfo = null;
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", consortiaID),
					new SqlParameter("@Level", Level),
					new SqlParameter("@Type", type)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Consortia_Equip_Control_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					consortiaEquipControlInfo = new ConsortiaEquipControlInfo();
					consortiaEquipControlInfo.ConsortiaID = (int)sqlDataReader["ConsortiaID"];
					consortiaEquipControlInfo.Level = (int)sqlDataReader["Level"];
					consortiaEquipControlInfo.Riches = (int)sqlDataReader["Riches"];
					consortiaEquipControlInfo.Type = (int)sqlDataReader["Type"];
					return consortiaEquipControlInfo;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllConsortiaLevel", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			bool flag3 = consortiaEquipControlInfo == null;
			if (flag3)
			{
				consortiaEquipControlInfo = new ConsortiaEquipControlInfo();
				consortiaEquipControlInfo.ConsortiaID = consortiaID;
				consortiaEquipControlInfo.Level = Level;
				consortiaEquipControlInfo.Riches = 100;
				consortiaEquipControlInfo.Type = type;
			}
			return consortiaEquipControlInfo;
		}

		// Token: 0x06008851 RID: 34897 RVA: 0x002D0A14 File Offset: 0x002CEC14
		public ConsortiaEquipControlInfo[] GetConsortiaEquipControlPage(int page, int size, ref int total, int order, int consortiaID, int level, int type)
		{
			List<ConsortiaEquipControlInfo> list = new List<ConsortiaEquipControlInfo>();
			try
			{
				string text = " IsExist=1 ";
				bool flag = consortiaID != -1;
				if (flag)
				{
					text = text + " and ConsortiaID =" + consortiaID.ToString() + " ";
				}
				bool flag2 = level != -1;
				if (flag2)
				{
					text = text + " and Level =" + level.ToString() + " ";
				}
				bool flag3 = type != -1;
				if (flag3)
				{
					text = text + " and Type =" + type.ToString() + " ";
				}
				string text2 = "ConsortiaID ";
				DataTable page2 = base.GetPage("Sys_Consortia_Equip_Control", text, page, size, "*", text2, "ConsortiaID", ref total);
				foreach (object obj in page2.Rows)
				{
					DataRow dataRow = (DataRow)obj;
					list.Add(new ConsortiaEquipControlInfo
					{
						ConsortiaID = (int)dataRow["ConsortiaID"],
						Level = (int)dataRow["Level"],
						Riches = (int)dataRow["Riches"],
						Type = (int)dataRow["Type"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return list.ToArray();
		}

		// Token: 0x06008852 RID: 34898 RVA: 0x002D0C10 File Offset: 0x002CEE10
		public ConsortiaTaskInfo[] GetAllConsortiaTask()
		{
			List<ConsortiaTaskInfo> list = new List<ConsortiaTaskInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Consortia_Task_All");
				while (sqlDataReader.Read())
				{
					list.Add(new ConsortiaTaskInfo
					{
						ID = (int)sqlDataReader["ID"],
						Level = (int)sqlDataReader["Level"],
						CondictionTitle = (string)sqlDataReader["CondictionTitle"],
						CondictionType = (int)sqlDataReader["CondictionType"],
						Para1 = (int)sqlDataReader["Para1"],
						Para2 = (int)sqlDataReader["Para2"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllConsortiaTask", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008853 RID: 34899 RVA: 0x002D0D60 File Offset: 0x002CEF60
		public bool ConsortiaTaskUpdateDate(int consortiID, DateTime dateTask)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[3];
				array[0] = new SqlParameter("@ConsortiaID", consortiID);
				array[1] = new SqlParameter("@DateOpenTask", SqlDbType.DateTime);
				SqlParameter[] array2 = array;
				array2[1].Value = dateTask;
				array2[2] = new SqlParameter("@Result", SqlDbType.Int);
				array2[2].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_Consortia_UpdateTimeOpenTask", array2);
				flag = (int)array2[2].Value == 0;
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("ConsortiaTaskUpdateDate", ex);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x040053D8 RID: 21464
		private string AreaName = "欢庆元旦";

		// Token: 0x040053D9 RID: 21465
		private int AreaID = 1;
	}
}
