﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EB7 RID: 3767
	public class CE1258 : BasePetEffect
	{
		// Token: 0x060081FA RID: 33274 RVA: 0x002AEE78 File Offset: 0x002AD078
		public CE1258(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1258, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060081FB RID: 33275 RVA: 0x002AEEF8 File Offset: 0x002AD0F8
		public override bool Start(Living living)
		{
			CE1258 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1258) as CE1258;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060081FC RID: 33276 RVA: 0x002AEF58 File Offset: 0x002AD158
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 200;
				player.BaseGuard += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060081FD RID: 33277 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060081FE RID: 33278 RVA: 0x002AEFBC File Offset: 0x002AD1BC
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060081FF RID: 33279 RVA: 0x002AEFF0 File Offset: 0x002AD1F0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BaseGuard -= (double)this.m_added;
			this.m_added = 0;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x0400513E RID: 20798
		private int m_type = 0;

		// Token: 0x0400513F RID: 20799
		private int m_count = 0;

		// Token: 0x04005140 RID: 20800
		private int m_probability = 0;

		// Token: 0x04005141 RID: 20801
		private int m_delay = 0;

		// Token: 0x04005142 RID: 20802
		private int m_coldDown = 0;

		// Token: 0x04005143 RID: 20803
		private int m_currentId;

		// Token: 0x04005144 RID: 20804
		private int m_added = 0;
	}
}
