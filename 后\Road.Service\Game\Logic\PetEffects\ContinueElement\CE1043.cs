﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E56 RID: 3670
	public class CE1043 : BasePetEffect
	{
		// Token: 0x06007F92 RID: 32658 RVA: 0x002A5A58 File Offset: 0x002A3C58
		public CE1043(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1043, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F93 RID: 32659 RVA: 0x002A5AD8 File Offset: 0x002A3CD8
		public override bool Start(Living living)
		{
			CE1043 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1043) as CE1043;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F94 RID: 32660 RVA: 0x00031A24 File Offset: 0x0002FC24
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F95 RID: 32661 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F96 RID: 32662 RVA: 0x002A5B38 File Offset: 0x002A3D38
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 500;
				living.SyncAtTime = true;
				living.AddBlood(this.m_added);
				living.SyncAtTime = false;
			}
		}

		// Token: 0x06007F97 RID: 32663 RVA: 0x00031A4D File Offset: 0x0002FC4D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E95 RID: 20117
		private int m_type = 0;

		// Token: 0x04004E96 RID: 20118
		private int m_count = 0;

		// Token: 0x04004E97 RID: 20119
		private int m_probability = 0;

		// Token: 0x04004E98 RID: 20120
		private int m_delay = 0;

		// Token: 0x04004E99 RID: 20121
		private int m_coldDown = 0;

		// Token: 0x04004E9A RID: 20122
		private int m_currentId;

		// Token: 0x04004E9B RID: 20123
		private int m_added = 0;
	}
}
