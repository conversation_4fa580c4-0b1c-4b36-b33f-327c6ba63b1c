﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E38 RID: 3640
	public class AE3176 : BasePetEffect
	{
		// Token: 0x06007EE1 RID: 32481 RVA: 0x002A2BEC File Offset: 0x002A0DEC
		public AE3176(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE3176, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007EE2 RID: 32482 RVA: 0x002A2C68 File Offset: 0x002A0E68
		public override bool Start(Living living)
		{
			AE3176 ae = living.PetEffectList.GetOfType(ePetEffectType.AE3176) as AE3176;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007EE3 RID: 32483 RVA: 0x0003142C File Offset: 0x0002F62C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EE4 RID: 32484 RVA: 0x00031442 File Offset: 0x0002F642
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EE5 RID: 32485 RVA: 0x002A2CC4 File Offset: 0x002A0EC4
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.SyncAtTime = true;
				int num = player.Blood * 25 / 100;
				player.AddBlood(num);
				player.SyncAtTime = false;
				player.AddPetEffect(new CE3176(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004DC3 RID: 19907
		private int m_type = 0;

		// Token: 0x04004DC4 RID: 19908
		private int m_count = 0;

		// Token: 0x04004DC5 RID: 19909
		private int m_probability = 0;

		// Token: 0x04004DC6 RID: 19910
		private int m_delay = 0;

		// Token: 0x04004DC7 RID: 19911
		private int m_coldDown = 0;

		// Token: 0x04004DC8 RID: 19912
		private int m_currentId;

		// Token: 0x04004DC9 RID: 19913
		private int m_added = 0;
	}
}
