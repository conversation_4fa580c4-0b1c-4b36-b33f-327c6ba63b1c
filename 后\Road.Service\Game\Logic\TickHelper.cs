﻿using System;
using System.Diagnostics;

namespace Game.Logic
{
	// Token: 0x02000CAE RID: 3246
	public static class TickHelper
	{
		// Token: 0x060074D5 RID: 29909 RVA: 0x0026D5C8 File Offset: 0x0026B7C8
		public static long GetTickCount()
		{
			return Stopwatch.GetTimestamp() / TickHelper.StopwatchFrequencyMilliseconds;
		}

		// Token: 0x04004453 RID: 17491
		private static long StopwatchFrequencyMilliseconds = Stopwatch.Frequency / 1000L;
	}
}
