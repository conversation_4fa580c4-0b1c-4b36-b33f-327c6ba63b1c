﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EDA RID: 3802
	public class AddTargetEffect : BasePlayerEffect
	{
		// Token: 0x060082CB RID: 33483 RVA: 0x00033AD6 File Offset: 0x00031CD6
		public AddTargetEffect()
			: base(eEffectType.AddTargetEffect)
		{
		}

		// Token: 0x060082CC RID: 33484 RVA: 0x002B1B14 File Offset: 0x002AFD14
		public override bool Start(Living living)
		{
			bool flag = living.EffectList.GetOfType(eEffectType.AddTargetEffect) is AddTargetEffect;
			return flag || base.Start(living);
		}

		// Token: 0x060082CD RID: 33485 RVA: 0x00033AE2 File Offset: 0x00031CE2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.Game.SendPlayerPicture(player, 7, true);
		}

		// Token: 0x060082CE RID: 33486 RVA: 0x00033AF4 File Offset: 0x00031CF4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPlayerPicture(player, 7, false);
		}
	}
}
