﻿using System;
using Bussiness;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000ECD RID: 3789
	public abstract class AbstractEffect
	{
		// Token: 0x1700149F RID: 5279
		// (get) Token: 0x0600827D RID: 33405 RVA: 0x0003355F File Offset: 0x0003175F
		public eEffectType Type
		{
			get
			{
				return this.m_type;
			}
		}

		// Token: 0x170014A0 RID: 5280
		// (get) Token: 0x0600827E RID: 33406 RVA: 0x0003355F File Offset: 0x0003175F
		public int TypeValue
		{
			get
			{
				return (int)this.m_type;
			}
		}

		// Token: 0x0600827F RID: 33407 RVA: 0x00033567 File Offset: 0x00031767
		public AbstractEffect(eEffectType type)
		{
			this.m_type = type;
		}

		// Token: 0x06008280 RID: 33408 RVA: 0x002B0F14 File Offset: 0x002AF114
		public virtual bool Start(Living living)
		{
			this.m_living = living;
			return this.m_living.EffectList.Add(this);
		}

		// Token: 0x06008281 RID: 33409 RVA: 0x002B0F40 File Offset: 0x002AF140
		public virtual bool Stop()
		{
			return this.m_living != null && this.m_living.EffectList.Remove(this);
		}

		// Token: 0x06008282 RID: 33410 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnAttached(Living living)
		{
		}

		// Token: 0x06008283 RID: 33411 RVA: 0x00005683 File Offset: 0x00003883
		public virtual void OnRemoved(Living living)
		{
		}

		// Token: 0x040051BB RID: 20923
		protected static ThreadSafeRandom random = new ThreadSafeRandom();

		// Token: 0x040051BC RID: 20924
		private eEffectType m_type;

		// Token: 0x040051BD RID: 20925
		protected Living m_living;

		// Token: 0x040051BE RID: 20926
		public bool IsTrigger;
	}
}
