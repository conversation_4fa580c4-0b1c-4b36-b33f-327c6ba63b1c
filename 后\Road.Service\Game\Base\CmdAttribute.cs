﻿using System;

namespace Game.Base
{
	// Token: 0x02000F73 RID: 3955
	[AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
	public class CmdAttribute : Attribute
	{
		// Token: 0x170014C1 RID: 5313
		// (get) Token: 0x0600858E RID: 34190 RVA: 0x00035A1E File Offset: 0x00033C1E
		public string Cmd
		{
			get
			{
				return this.m_cmd;
			}
		}

		// Token: 0x170014C2 RID: 5314
		// (get) Token: 0x0600858F RID: 34191 RVA: 0x00035A26 File Offset: 0x00033C26
		public string[] Aliases
		{
			get
			{
				return this.m_cmdAliases;
			}
		}

		// Token: 0x170014C3 RID: 5315
		// (get) Token: 0x06008590 RID: 34192 RVA: 0x00035A2E File Offset: 0x00033C2E
		public uint Level
		{
			get
			{
				return this.m_lvl;
			}
		}

		// Token: 0x170014C4 RID: 5316
		// (get) Token: 0x06008591 RID: 34193 RVA: 0x00035A36 File Offset: 0x00033C36
		public string Description
		{
			get
			{
				return this.m_description;
			}
		}

		// Token: 0x170014C5 RID: 5317
		// (get) Token: 0x06008592 RID: 34194 RVA: 0x00035A3E File Offset: 0x00033C3E
		public string[] Usage
		{
			get
			{
				return this.m_usage;
			}
		}

		// Token: 0x06008593 RID: 34195 RVA: 0x00035A46 File Offset: 0x00033C46
		public CmdAttribute(string cmd, string[] alias, ePrivLevel lvl, string desc, params string[] usage)
		{
			this.m_cmd = cmd;
			this.m_cmdAliases = alias;
			this.m_lvl = (uint)lvl;
			this.m_description = desc;
			this.m_usage = usage;
		}

		// Token: 0x06008594 RID: 34196 RVA: 0x00035A75 File Offset: 0x00033C75
		public CmdAttribute(string cmd, ePrivLevel lvl, string desc, params string[] usage)
			: this(cmd, null, lvl, desc, usage)
		{
		}

		// Token: 0x04005365 RID: 21349
		private string m_cmd;

		// Token: 0x04005366 RID: 21350
		private string[] m_cmdAliases;

		// Token: 0x04005367 RID: 21351
		private uint m_lvl;

		// Token: 0x04005368 RID: 21352
		private string m_description;

		// Token: 0x04005369 RID: 21353
		private string[] m_usage;
	}
}
