﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DB0 RID: 3504
	public class AE1058 : BasePetEffect
	{
		// Token: 0x06007C11 RID: 31761 RVA: 0x00295B20 File Offset: 0x00293D20
		public AE1058(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1058, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C12 RID: 31762 RVA: 0x00295BA0 File Offset: 0x00293DA0
		public override bool Start(Living living)
		{
			AE1058 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1058) as AE1058;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C13 RID: 31763 RVA: 0x0002F7CA File Offset: 0x0002D9CA
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007C14 RID: 31764 RVA: 0x00295C00 File Offset: 0x00293E00
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				player.SyncAtTime = true;
				player.AddBlood(1000);
				player.SyncAtTime = false;
				List<Living> list = player.Game.Map.FindAllNearestSameTeam(player.X, player.Y, 250.0, player);
				foreach (Living living in list)
				{
					this.m_added = 1000;
					living.SyncAtTime = true;
					living.AddBlood(this.m_added);
					living.SyncAtTime = false;
				}
			}
		}

		// Token: 0x06007C15 RID: 31765 RVA: 0x0002F7E0 File Offset: 0x0002D9E0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004A0A RID: 18954
		private int m_type = 0;

		// Token: 0x04004A0B RID: 18955
		private int m_count = 0;

		// Token: 0x04004A0C RID: 18956
		private int m_probability = 0;

		// Token: 0x04004A0D RID: 18957
		private int m_delay = 0;

		// Token: 0x04004A0E RID: 18958
		private int m_coldDown = 0;

		// Token: 0x04004A0F RID: 18959
		private int m_currentId;

		// Token: 0x04004A10 RID: 18960
		private int m_added = 0;
	}
}
