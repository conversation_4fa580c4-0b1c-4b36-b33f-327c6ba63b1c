﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.PassiveSkills
{
	// Token: 0x02000CF1 RID: 3313
	public class PetAddBaseGuard_Passive : BasePetEffect
	{
		// Token: 0x0600780E RID: 30734 RVA: 0x0002C569 File Offset: 0x0002A769
		public PetAddBaseGuard_Passive(int probability, string elementID)
			: base(ePetEffectType.PetAddBaseGuard_Passive, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_elementID = elementID;
		}

		// Token: 0x0600780F RID: 30735 RVA: 0x00283CC0 File Offset: 0x00281EC0
		public override bool Start(Living living)
		{
			PetAddBaseGuard_Passive petAddBaseGuard_Passive = living.PetEffectList.GetOfType(ePetEffectType.PetAddBaseGuard_Passive) as PetAddBaseGuard_Passive;
			bool flag = petAddBaseGuard_Passive != null;
			bool flag2;
			if (flag)
			{
				petAddBaseGuard_Passive.m_probability = ((this.m_probability > petAddBaseGuard_Passive.m_probability) ? this.m_probability : petAddBaseGuard_Passive.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007810 RID: 30736 RVA: 0x0002C5A0 File Offset: 0x0002A7A0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.player_BeginNextTurn;
		}

		// Token: 0x06007811 RID: 30737 RVA: 0x0002C5B6 File Offset: 0x0002A7B6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.player_BeginNextTurn;
		}

		// Token: 0x06007812 RID: 30738 RVA: 0x00283D20 File Offset: 0x00281F20
		private void player_BeginNextTurn(Living living)
		{
			bool flag = this.m_value == 0;
			if (flag)
			{
				string elementID = this.m_elementID;
				string text = elementID;
				bool flag2 = text == "2978";
				if (flag2)
				{
					this.m_value = 10;
					this.m_percent = true;
				}
				living.Game.sendShowPicSkil(living, base.Info, true);
				bool percent = this.m_percent;
				if (percent)
				{
					living.BaseGuard += living.BaseGuard * (double)this.m_value / 100.0;
				}
				else
				{
					living.BaseGuard += (double)this.m_value;
				}
			}
		}

		// Token: 0x0400465C RID: 18012
		private int m_probability = 0;

		// Token: 0x0400465D RID: 18013
		private int m_value = 0;

		// Token: 0x0400465E RID: 18014
		private string m_elementID;

		// Token: 0x0400465F RID: 18015
		private bool m_percent;
	}
}
