﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D77 RID: 3447
	public class PE1236 : BasePetEffect
	{
		// Token: 0x06007AE8 RID: 31464 RVA: 0x00290D24 File Offset: 0x0028EF24
		public PE1236(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1236, elementID)
		{
			this.m_count = count;
			this.m_coldDown = 3;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007AE9 RID: 31465 RVA: 0x00290DA4 File Offset: 0x0028EFA4
		public override bool Start(Living living)
		{
			PE1236 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1236) as PE1236;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007AEA RID: 31466 RVA: 0x0002EB64 File Offset: 0x0002CD64
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
		}

		// Token: 0x06007AEB RID: 31467 RVA: 0x00290E04 File Offset: 0x0028F004
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.rand.Next(100) >= 35;
			if (!flag)
			{
				this.m_added = 500;
				source.SyncAtTime = true;
				source.AddBlood(-this.m_added, 1);
				source.SyncAtTime = false;
				bool flag2 = source.Blood < 0;
				if (flag2)
				{
					source.Die();
					bool flag3 = living != null && living is Player;
					if (flag3)
					{
						(living as Player).PlayerDetail.OnKillingLiving(living.Game, 2, source.Id, source.IsLiving, this.m_added);
					}
				}
				living.PetEffects.ReboundDamage = this.m_added;
			}
		}

		// Token: 0x06007AEC RID: 31468 RVA: 0x0002EB7A File Offset: 0x0002CD7A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
		}

		// Token: 0x04004881 RID: 18561
		private int m_type = 0;

		// Token: 0x04004882 RID: 18562
		private int m_count = 0;

		// Token: 0x04004883 RID: 18563
		private int m_probability = 0;

		// Token: 0x04004884 RID: 18564
		private int m_delay = 0;

		// Token: 0x04004885 RID: 18565
		private int m_coldDown = 0;

		// Token: 0x04004886 RID: 18566
		private int m_currentId;

		// Token: 0x04004887 RID: 18567
		private int m_added = 0;
	}
}
