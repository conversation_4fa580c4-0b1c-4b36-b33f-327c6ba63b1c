﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F20 RID: 3872
	public class DivineEffect : BaseCardEffect
	{
		// Token: 0x060083F7 RID: 33783 RVA: 0x002B62C4 File Offset: 0x002B44C4
		public DivineEffect(int index, CardBuffInfo info)
			: base(eCardEffectType.DivineDeck, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x060083F8 RID: 33784 RVA: 0x002B6334 File Offset: 0x002B4534
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.DivineDeck) is DivineEffect;
			return flag || base.Start(living);
		}

		// Token: 0x060083F9 RID: 33785 RVA: 0x000348F6 File Offset: 0x00032AF6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x060083FA RID: 33786 RVA: 0x0003490C File Offset: 0x00032B0C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x060083FB RID: 33787 RVA: 0x002B636C File Offset: 0x002B456C
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.AddMaxBlood(-this.m_value);
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVPGame && (player.Game as PVPGame).IsMatchOrFreedom();
			if (flag2)
			{
				player.AddMaxBlood(this.m_value);
				this.m_added = this.m_value;
			}
		}

		// Token: 0x04005256 RID: 21078
		private int m_indexValue = 0;

		// Token: 0x04005257 RID: 21079
		private int m_value = 0;

		// Token: 0x04005258 RID: 21080
		private int m_added = 0;
	}
}
