﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D8F RID: 3471
	public class PE4849 : BasePetEffect
	{
		// Token: 0x06007B64 RID: 31588 RVA: 0x00292BF4 File Offset: 0x00290DF4
		public PE4849(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1106, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B65 RID: 31589 RVA: 0x00292C74 File Offset: 0x00290E74
		public override bool Start(Living living)
		{
			PE4849 pe = living.PetEffectList.GetOfType(ePetEffectType.PE4849) as PE4849;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B66 RID: 31590 RVA: 0x0002F101 File Offset: 0x0002D301
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerShoot += this.player_Shooted;
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
		}

		// Token: 0x06007B67 RID: 31591 RVA: 0x00292CD4 File Offset: 0x00290ED4
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = this.m_currentId == 99;
			if (flag)
			{
				((Player)living).AddPetMP(12);
			}
			bool flag2 = this.m_count >= 0;
			if (flag2)
			{
				((Player)living).AddPetMP(8);
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
			}
		}

		// Token: 0x06007B68 RID: 31592 RVA: 0x00292D34 File Offset: 0x00290F34
		private void player_Shooted(Player player)
		{
			bool flag = this.m_currentId == 95 || this.m_currentId == 116;
			if (flag)
			{
				player.AddPetMP(1);
				player.Game.sendShowPicSkil(player, base.ElementInfo, true);
			}
		}

		// Token: 0x06007B69 RID: 31593 RVA: 0x0002F12A File Offset: 0x0002D32A
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerShoot -= this.player_Shooted;
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x04004923 RID: 18723
		private int m_type = 0;

		// Token: 0x04004924 RID: 18724
		private int m_count = 0;

		// Token: 0x04004925 RID: 18725
		private int m_probability = 0;

		// Token: 0x04004926 RID: 18726
		private int m_delay = 0;

		// Token: 0x04004927 RID: 18727
		private int m_coldDown = 0;

		// Token: 0x04004928 RID: 18728
		private int m_currentId;

		// Token: 0x04004929 RID: 18729
		private int m_added = 0;
	}
}
