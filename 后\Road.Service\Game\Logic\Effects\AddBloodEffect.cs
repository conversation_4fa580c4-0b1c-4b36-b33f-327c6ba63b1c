﻿using System;
using Game.Logic.Event;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000ED0 RID: 3792
	public class AddBloodEffect : BasePlayerEffect
	{
		// Token: 0x06008291 RID: 33425 RVA: 0x0003368E File Offset: 0x0003188E
		public AddBloodEffect(int count, int probability)
			: base(eEffectType.AddBloodEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x06008292 RID: 33426 RVA: 0x002B1190 File Offset: 0x002AF390
		public override bool Start(Living living)
		{
			AddBloodEffect addBloodEffect = living.EffectList.GetOfType(eEffectType.AddBloodEffect) as AddBloodEffect;
			bool flag = addBloodEffect != null;
			bool flag2;
			if (flag)
			{
				this.m_probability = ((this.m_probability > addBloodEffect.m_probability) ? this.m_probability : addBloodEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008293 RID: 33427 RVA: 0x000336B5 File Offset: 0x000318B5
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += new PlayerShootEventHandle(this.ChangeProperty);
		}

		// Token: 0x06008294 RID: 33428 RVA: 0x000336CB File Offset: 0x000318CB
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= new PlayerShootEventHandle(this.ChangeProperty);
		}

		// Token: 0x06008295 RID: 33429 RVA: 0x002B11EC File Offset: 0x002AF3EC
		public void ChangeProperty(Living living, int ball)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				this.IsTrigger = true;
				living.AttackEffectTrigger = true;
				living.SyncAtTime = true;
				living.AddBlood(this.m_count);
				living.SyncAtTime = false;
			}
		}

		// Token: 0x040051C5 RID: 20933
		private int m_count = 0;

		// Token: 0x040051C6 RID: 20934
		private int m_probability = 0;
	}
}
