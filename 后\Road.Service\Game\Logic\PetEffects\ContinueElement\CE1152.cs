﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E75 RID: 3701
	public class CE1152 : BasePetEffect
	{
		// Token: 0x0600804E RID: 32846 RVA: 0x002A88C8 File Offset: 0x002A6AC8
		public CE1152(int count, int probability, int type, int skillId, int delay, string elementID, Living source)
			: base(ePetEffectType.CE1152, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
			this.m_source = source;
		}

		// Token: 0x0600804F RID: 32847 RVA: 0x002A8950 File Offset: 0x002A6B50
		public override bool Start(Living living)
		{
			CE1152 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1152) as CE1152;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008050 RID: 32848 RVA: 0x00032107 File Offset: 0x00030307
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008051 RID: 32849 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008052 RID: 32850 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginNextTurn(Living living)
		{
		}

		// Token: 0x06008053 RID: 32851 RVA: 0x002A89B0 File Offset: 0x002A6BB0
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 500;
				living.AddBlood(-this.m_added, 1);
				bool flag2 = living.Blood <= 0;
				if (flag2)
				{
					living.Die();
					bool flag3 = this.m_source != null && this.m_source is Player;
					if (flag3)
					{
						(this.m_source as Player).PlayerDetail.OnKillingLiving(this.m_source.Game, 2, living.Id, living.IsLiving, this.m_added);
					}
				}
			}
		}

		// Token: 0x06008054 RID: 32852 RVA: 0x00032143 File Offset: 0x00030343
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F6B RID: 20331
		private int m_type = 0;

		// Token: 0x04004F6C RID: 20332
		private int m_count = 0;

		// Token: 0x04004F6D RID: 20333
		private int m_probability = 0;

		// Token: 0x04004F6E RID: 20334
		private int m_delay = 0;

		// Token: 0x04004F6F RID: 20335
		private int m_coldDown = 0;

		// Token: 0x04004F70 RID: 20336
		private int m_currentId;

		// Token: 0x04004F71 RID: 20337
		private int m_added = 0;

		// Token: 0x04004F72 RID: 20338
		private Living m_source;
	}
}
