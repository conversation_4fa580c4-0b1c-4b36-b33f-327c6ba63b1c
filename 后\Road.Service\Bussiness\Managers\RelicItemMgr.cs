﻿using System;
using System.Collections.Generic;
using System.Linq;
using EntityDatabase.PlayerModels;
using EntityDatabase.ServerModels;
using Newtonsoft.Json;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FF1 RID: 4081
	public static class RelicItemMgr
	{
		// Token: 0x06008B8C RID: 35724 RVA: 0x002FD8BC File Offset: 0x002FBABC
		public static bool Init()
		{
			ServerData serverData = new ServerData();
			bool flag;
			try
			{
				RelicItemMgr.RelicAdvanceList = serverData.RelicAdvanceTemplate.ToList<TS_Relic_AdvanceTemplate>();
				RelicItemMgr.RelicDegreeList = serverData.RelicDegreeTemplate.ToList<TS_Relic_DegreeTemplate>();
				RelicItemMgr.RelicItemList = serverData.RelicItemTemplate.ToList<TS_Relic_ItemTemplate>();
				RelicItemMgr.RelicUpgradeList = serverData.RelicUpgradeTemplate.ToList<TS_Relic_UpgradeTemplate>();
				RelicItemMgr.RelicAdvanceValueList = serverData.RelicAdvanceValue.ToList<TS_Relic_AdvanceValue>();
				RelicItemMgr.ManualInfo = RelicItemMgr.NewManualInfo();
				RelicItemMgr._zfCnfDic = RelicItemMgr.GetzfCnfDic();
				flag = true;
			}
			catch
			{
				flag = false;
			}
			return flag;
		}

		// Token: 0x06008B8D RID: 35725 RVA: 0x002FD950 File Offset: 0x002FBB50
		public static int GetRelicLevel(int relicID, int exp, int level)
		{
			TS_Relic_UpgradeTemplate ts_Relic_UpgradeTemplate = RelicItemMgr.RelicUpgradeList.Where((TS_Relic_UpgradeTemplate i) => i.RelicID == relicID).ToList<TS_Relic_UpgradeTemplate>().FirstOrDefault((TS_Relic_UpgradeTemplate item) => exp >= item.NeedExp && level < item.Level);
			if (ts_Relic_UpgradeTemplate == null)
			{
				return 0;
			}
			return ts_Relic_UpgradeTemplate.Level;
		}

		// Token: 0x06008B8E RID: 35726 RVA: 0x002FD9B0 File Offset: 0x002FBBB0
		public static void GetManualValue(ManualDataInfo info, ref int RelicAtt, ref int RelicDef, ref int RelicLuck, ref int RelicAgi, ref int RelicMagAtt, ref int RelicMagicDef, ref int hp)
		{
			TS_Relic_DegreeTemplate ts_Relic_DegreeTemplate = RelicItemMgr.RelicDegreeList.FirstOrDefault((TS_Relic_DegreeTemplate item) => item.Level == info.level && item.Quality == info.type);
			if (ts_Relic_DegreeTemplate != null)
			{
				RelicAtt += ts_Relic_DegreeTemplate.Attack;
				RelicDef += ts_Relic_DegreeTemplate.Defence;
				RelicLuck += ts_Relic_DegreeTemplate.Luck;
				RelicAgi += ts_Relic_DegreeTemplate.Agility;
				RelicMagAtt += ts_Relic_DegreeTemplate.MagicAttack;
				RelicMagicDef += ts_Relic_DegreeTemplate.MagicDefence;
				hp += ts_Relic_DegreeTemplate.Blood;
			}
		}

		// Token: 0x06008B8F RID: 35727 RVA: 0x002FDA3C File Offset: 0x002FBC3C
		public static void GetManualDamageValue(ManualDataInfo info, ref int RelicDamage)
		{
			TS_Relic_DegreeTemplate ts_Relic_DegreeTemplate = RelicItemMgr.RelicDegreeList.FirstOrDefault((TS_Relic_DegreeTemplate item) => item.Level == info.level && item.Quality == info.type);
			if (ts_Relic_DegreeTemplate != null)
			{
				RelicDamage += ts_Relic_DegreeTemplate.Damage;
			}
		}

		// Token: 0x06008B90 RID: 35728 RVA: 0x002FDA7C File Offset: 0x002FBC7C
		public static void GetRelicValue(Sys_User_RelicItemTemplate RelicItem, ref int RelicAtt, ref int RelicDef, ref int RelicLuck, ref int RelicAgi, ref int RelicMagAtt, ref int RelicMagicDef, ref int PhyAttack, ref int PhyDefence, ref int MagAttack, ref int MagDefence)
		{
			int num = 0;
			RelicItemMgr.GetRelicValueNew(RelicItem, ref RelicAtt, ref RelicDef, ref RelicLuck, ref RelicAgi, ref RelicMagAtt, ref RelicMagicDef, ref PhyAttack, ref PhyDefence, ref MagAttack, ref MagDefence, ref num);
		}

		// Token: 0x06008B91 RID: 35729 RVA: 0x002FDAA4 File Offset: 0x002FBCA4
		public static void GetRelicDamage(Sys_User_RelicItemTemplate RelicItem, ref int RelicDamage)
		{
			TS_Relic_UpgradeTemplate ts_Relic_UpgradeTemplate = RelicItemMgr.RelicUpgradeList.FirstOrDefault((TS_Relic_UpgradeTemplate item) => item.RelicID == RelicItem.itemID && RelicItem.level == item.Level);
			Console.WriteLine(string.Format("GetRelicDamage - 查找圣物模板: RelicID={0}, Level={1}, 找到模板={2}", RelicItem.itemID, RelicItem.level, ts_Relic_UpgradeTemplate != null));
			if (ts_Relic_UpgradeTemplate != null)
			{
				Console.WriteLine(string.Format("GetRelicDamage - 模板信息: Type={0}, Data={1}", ts_Relic_UpgradeTemplate.Type, ts_Relic_UpgradeTemplate.Data));
				if (ts_Relic_UpgradeTemplate.Type == 8)
				{
					RelicDamage += ts_Relic_UpgradeTemplate.Data;
					Console.WriteLine(string.Format("GetRelicDamage - 添加基础伤害: {0}, 当前总伤害: {1}", ts_Relic_UpgradeTemplate.Data, RelicDamage));
				}
				else
				{
					Console.WriteLine(string.Format("GetRelicDamage - Type不匹配，期望8，实际{0}", ts_Relic_UpgradeTemplate.Type));
				}
				using (List<RelicProDataInfo>.Enumerator enumerator = RelicItemMgr.ParseToList(RelicItem.ProArr).GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						RelicProDataInfo relicProDataInfo = enumerator.Current;
						if (relicProDataInfo.type == 8)
						{
							RelicDamage += relicProDataInfo.value;
							Console.WriteLine(string.Format("GetRelicDamage - 添加附加伤害: {0}, 当前总伤害: {1}", relicProDataInfo.value, RelicDamage));
						}
					}
					return;
				}
			}
			Console.WriteLine("GetRelicDamage - 未找到匹配的圣物模板！");
		}

		// Token: 0x06008B92 RID: 35730 RVA: 0x002FDC14 File Offset: 0x002FBE14
		public static void GetRelicGuard(Sys_User_RelicItemTemplate RelicItem, ref int RelicGuard)
		{
			TS_Relic_UpgradeTemplate ts_Relic_UpgradeTemplate = RelicItemMgr.RelicUpgradeList.FirstOrDefault((TS_Relic_UpgradeTemplate item) => item.RelicID == RelicItem.itemID && RelicItem.level == item.Level);
			Console.WriteLine(string.Format("GetRelicGuard - 查找圣物模板: RelicID={0}, Level={1}, 找到模板={2}", RelicItem.itemID, RelicItem.level, ts_Relic_UpgradeTemplate != null));
			if (ts_Relic_UpgradeTemplate != null)
			{
				Console.WriteLine(string.Format("GetRelicGuard - 模板信息: Type={0}, Data={1}", ts_Relic_UpgradeTemplate.Type, ts_Relic_UpgradeTemplate.Data));
				if (ts_Relic_UpgradeTemplate.Type == 7)
				{
					RelicGuard += ts_Relic_UpgradeTemplate.Data;
					Console.WriteLine(string.Format("GetRelicGuard - 添加基础护甲: {0}, 当前总护甲: {1}", ts_Relic_UpgradeTemplate.Data, RelicGuard));
				}
				else
				{
					Console.WriteLine(string.Format("GetRelicGuard - Type不匹配，期望7，实际{0}", ts_Relic_UpgradeTemplate.Type));
				}
				using (List<RelicProDataInfo>.Enumerator enumerator = RelicItemMgr.ParseToList(RelicItem.ProArr).GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						RelicProDataInfo relicProDataInfo = enumerator.Current;
						if (relicProDataInfo.type == 7)
						{
							RelicGuard += relicProDataInfo.value;
							Console.WriteLine(string.Format("GetRelicGuard - 添加附加护甲: {0}, 当前总护甲: {1}", relicProDataInfo.value, RelicGuard));
						}
					}
					return;
				}
			}
			Console.WriteLine("GetRelicGuard - 未找到匹配的圣物模板！");
		}

		// Token: 0x06008B93 RID: 35731 RVA: 0x002FDD84 File Offset: 0x002FBF84
		public static Sys_User_RelicItemTemplate GetNewRelic(int relicID, int Quality, int id)
		{
			return new Sys_User_RelicItemTemplate
			{
				UserID = id,
				itemID = relicID,
				Type = 0,
				level = 0,
				stage = 0,
				Quality = Quality,
				ShardNum = 0,
				curExp = 0,
				ProArr = "",
				_exProArr = ""
			};
		}

		// Token: 0x06008B94 RID: 35732 RVA: 0x002FDDE4 File Offset: 0x002FBFE4
		public static RelicProDataInfo NewRelicProDataInfo(int Quality)
		{
			RelicProDataInfo ProDataInfo = new RelicProDataInfo
			{
				type = 0,
				value = 0,
				attachValue = 0
			};
			ProDataInfo.type = new Random().Next(1, 13);
			TS_Relic_AdvanceValue ts_Relic_AdvanceValue = RelicItemMgr.RelicAdvanceValueList.FirstOrDefault((TS_Relic_AdvanceValue item) => item.Quality == Quality && ProDataInfo.type == item.Type);
			ProDataInfo.value = new Random().Next(ts_Relic_AdvanceValue.MinValue, ts_Relic_AdvanceValue.MaxValue + 1);
			return ProDataInfo;
		}

		// Token: 0x06008B95 RID: 35733 RVA: 0x002FDE78 File Offset: 0x002FC078
		public static Dictionary<string, Dictionary<string, RelicItemMgr.ForcesRelicZFCostCnfVo>> GetzfCnfDic()
		{
			string[] array = GameProperties.RelicBuffSubstatPrice.Split(new char[] { '|' });
			RelicItemMgr._zfCnfDic = new Dictionary<string, Dictionary<string, RelicItemMgr.ForcesRelicZFCostCnfVo>>();
			string[] array2 = array;
			for (int i = 0; i < array2.Length; i++)
			{
				string[] array3 = array2[i].Split(new char[] { ',' });
				if (array3.Length != 5)
				{
					throw new FormatException("Each item in the input string must have exactly 5 fields.");
				}
				RelicItemMgr.ForcesRelicZFCostCnfVo forcesRelicZFCostCnfVo = new RelicItemMgr.ForcesRelicZFCostCnfVo
				{
					Quantity = int.Parse(array3[0]),
					Place = int.Parse(array3[1]),
					CostNum1 = int.Parse(array3[2]),
					CostNum2 = int.Parse(array3[3]),
					CostNum3 = int.Parse(array3[4])
				};
				Dictionary<string, RelicItemMgr.ForcesRelicZFCostCnfVo> dictionary;
				if (!RelicItemMgr._zfCnfDic.TryGetValue(forcesRelicZFCostCnfVo.Quantity.ToString(), out dictionary))
				{
					RelicItemMgr._zfCnfDic[forcesRelicZFCostCnfVo.Quantity.ToString()] = new Dictionary<string, RelicItemMgr.ForcesRelicZFCostCnfVo>();
				}
				RelicItemMgr._zfCnfDic[forcesRelicZFCostCnfVo.Quantity.ToString()][forcesRelicZFCostCnfVo.Place.ToString()] = forcesRelicZFCostCnfVo;
			}
			JsonConvert.SerializeObject(RelicItemMgr._zfCnfDic);
			return RelicItemMgr._zfCnfDic;
		}

		// Token: 0x06008B96 RID: 35734 RVA: 0x002FDFA4 File Offset: 0x002FC1A4
		public static int GetZFCount(int que, int palce, int type)
		{
			RelicItemMgr.ForcesRelicZFCostCnfVo forcesRelicZFCostCnfVo = RelicItemMgr._zfCnfDic[que.ToString()][palce.ToString()];
			int num;
			if (forcesRelicZFCostCnfVo == null)
			{
				num = 0;
			}
			else if (type > 2)
			{
				num = 0;
			}
			else
			{
				num = ((type == 1) ? forcesRelicZFCostCnfVo.CostNum1 : forcesRelicZFCostCnfVo.CostNum2);
			}
			return num;
		}

		// Token: 0x06008B97 RID: 35735 RVA: 0x000369A5 File Offset: 0x00034BA5
		public static string ConvertToJsonString(List<RelicProDataInfo> relicProDataInfoList)
		{
			return JsonConvert.SerializeObject(relicProDataInfoList);
		}

		// Token: 0x06008B98 RID: 35736 RVA: 0x002FDFF4 File Offset: 0x002FC1F4
		public static List<RelicProDataInfo> ParseToList(string json)
		{
			List<RelicProDataInfo> list;
			if (string.IsNullOrEmpty(json))
			{
				list = new List<RelicProDataInfo>();
			}
			else
			{
				try
				{
					list = JsonConvert.DeserializeObject<List<RelicProDataInfo>>(json);
				}
				catch (JsonException)
				{
					list = new List<RelicProDataInfo>();
				}
			}
			return list;
		}

		// Token: 0x06008B99 RID: 35737 RVA: 0x000369AD File Offset: 0x00034BAD
		public static List<ManualDataInfo> ManualToList(string json)
		{
			return JsonConvert.DeserializeObject<List<ManualDataInfo>>(json);
		}

		// Token: 0x06008B9A RID: 35738 RVA: 0x000369A5 File Offset: 0x00034BA5
		public static string ManualToJsonString(List<ManualDataInfo> ManualDataInfoInfoList)
		{
			return JsonConvert.SerializeObject(ManualDataInfoInfoList);
		}

		// Token: 0x06008B9B RID: 35739 RVA: 0x002FE038 File Offset: 0x002FC238
		public static string NewManualInfo()
		{
			List<ManualDataInfo> list = new List<ManualDataInfo>();
			for (int i = 0; i < 6; i++)
			{
				ManualDataInfo manualDataInfo = new ManualDataInfo
				{
					type = i,
					level = 1,
					exp = 0
				};
				list.Add(manualDataInfo);
			}
			return JsonConvert.SerializeObject(list);
		}

		// Token: 0x06008B9C RID: 35740 RVA: 0x002FE080 File Offset: 0x002FC280
		public static string GetRelicInfo(string fields, int id, int position, int type)
		{
			string[] array = fields.Split(new char[] { ',' });
			if (position >= 0 && position < array.Length)
			{
				array[position] = ((type == 1) ? id.ToString() : "0");
				fields = string.Join(",", array);
			}
			return fields;
		}

		// Token: 0x06008B9D RID: 35741 RVA: 0x002FE0CC File Offset: 0x002FC2CC
		public static int GetAdvancLevel(int relicID, int needType, int level)
		{
			TS_Relic_AdvanceTemplate ts_Relic_AdvanceTemplate = RelicItemMgr.RelicAdvanceList.FirstOrDefault((TS_Relic_AdvanceTemplate i) => i.RelicID == relicID && i.Level == level);
			int num;
			if (ts_Relic_AdvanceTemplate == null)
			{
				num = 0;
			}
			else
			{
				string[] array = ts_Relic_AdvanceTemplate.ItemCost.Split(new char[] { '|' });
				for (int j = 0; j < array.Length; j++)
				{
					string[] array2 = array[j].Split(new char[] { ',' });
					int num2 = 0;
					int num3 = 0;
					if (array2.Length == 2 && int.TryParse(array2[0], out num2) && int.TryParse(array2[1], out num3) && needType == num2)
					{
						return num3;
					}
				}
				num = 0;
			}
			return num;
		}

		// Token: 0x06008B9E RID: 35742 RVA: 0x002FE17C File Offset: 0x002FC37C
		public static int GetMaxLevel(int relicID)
		{
			List<TS_Relic_UpgradeTemplate> list = RelicItemMgr.RelicUpgradeList.Where((TS_Relic_UpgradeTemplate i) => i.RelicID == relicID).ToList<TS_Relic_UpgradeTemplate>();
			if (list.Count == 0)
			{
				return 1;
			}
			return list.Max((TS_Relic_UpgradeTemplate item) => item.Level);
		}

		// Token: 0x06008B9F RID: 35743 RVA: 0x002FE1E4 File Offset: 0x002FC3E4
		public static int GetLevelExp(int relicID, int level)
		{
			TS_Relic_UpgradeTemplate ts_Relic_UpgradeTemplate = RelicItemMgr.RelicUpgradeList.FirstOrDefault((TS_Relic_UpgradeTemplate item) => item.RelicID == relicID && item.Level == level);
			if (ts_Relic_UpgradeTemplate == null)
			{
				return 0;
			}
			return ts_Relic_UpgradeTemplate.NeedExp;
		}

		// Token: 0x06008BA0 RID: 35744 RVA: 0x002FE228 File Offset: 0x002FC428
		public static void GetRelicValueNew(Sys_User_RelicItemTemplate RelicItem, ref int RelicAtt, ref int RelicDef, ref int RelicLuck, ref int RelicAgi, ref int RelicMagAtt, ref int RelicMagicDef, ref int PhyAttack, ref int PhyDefence, ref int MagAttack, ref int MagDefence, ref int Blood)
		{
			TS_Relic_UpgradeTemplate ts_Relic_UpgradeTemplate = RelicItemMgr.RelicUpgradeList.FirstOrDefault((TS_Relic_UpgradeTemplate item) => item.RelicID == RelicItem.itemID && RelicItem.level == item.Level);
			if (ts_Relic_UpgradeTemplate != null)
			{
				RelicItemMgr.GetRelicData(ts_Relic_UpgradeTemplate.Type, ts_Relic_UpgradeTemplate.Data, ref RelicAtt, ref RelicDef, ref RelicLuck, ref RelicAgi, ref RelicMagAtt, ref RelicMagicDef, ref PhyAttack, ref PhyDefence, ref MagAttack, ref MagDefence, ref Blood);
				foreach (RelicProDataInfo relicProDataInfo in RelicItemMgr.ParseToList(RelicItem.ProArr))
				{
					RelicItemMgr.GetRelicData(relicProDataInfo.type, relicProDataInfo.value, ref RelicAtt, ref RelicDef, ref RelicLuck, ref RelicAgi, ref RelicMagAtt, ref RelicMagicDef, ref PhyAttack, ref PhyDefence, ref MagAttack, ref MagDefence, ref Blood);
				}
			}
		}

		// Token: 0x06008BA1 RID: 35745 RVA: 0x002FE2F0 File Offset: 0x002FC4F0
		public static void GetRelicData(int type, int value, ref int RelicAtt, ref int RelicDef, ref int RelicLuck, ref int RelicAgi, ref int RelicMagAtt, ref int RelicMagicDef, ref int PhyAttack, ref int PhyDefence, ref int MagAttack, ref int MagDefence, ref int Blood)
		{
			switch (type)
			{
			case 1:
				RelicAtt += value;
				return;
			case 2:
				RelicDef += value;
				return;
			case 3:
				RelicAgi += value;
				return;
			case 4:
				RelicLuck += value;
				return;
			case 5:
				RelicMagAtt += value;
				return;
			case 6:
				RelicMagicDef += value;
				return;
			case 7:
				PhyDefence += value;
				return;
			case 8:
				PhyAttack += value;
				return;
			case 9:
				Blood += value;
				return;
			case 10:
				PhyAttack += value;
				return;
			case 11:
				PhyDefence += value;
				return;
			case 12:
				PhyAttack += value;
				return;
			case 13:
				PhyDefence += value;
				return;
			default:
				return;
			}
		}

		// Token: 0x04005559 RID: 21849
		public static List<TS_Relic_AdvanceTemplate> RelicAdvanceList;

		// Token: 0x0400555A RID: 21850
		public static List<TS_Relic_DegreeTemplate> RelicDegreeList;

		// Token: 0x0400555B RID: 21851
		public static List<TS_Relic_ItemTemplate> RelicItemList;

		// Token: 0x0400555C RID: 21852
		public static List<TS_Relic_UpgradeTemplate> RelicUpgradeList;

		// Token: 0x0400555D RID: 21853
		public static List<TS_Relic_AdvanceValue> RelicAdvanceValueList;

		// Token: 0x0400555E RID: 21854
		public static string ManualInfo;

		// Token: 0x0400555F RID: 21855
		private static Dictionary<string, Dictionary<string, RelicItemMgr.ForcesRelicZFCostCnfVo>> _zfCnfDic;

		// Token: 0x02000FF2 RID: 4082
		public class AdvanceLevelInfo
		{
			// Token: 0x170014E3 RID: 5347
			// (get) Token: 0x06008BA2 RID: 35746 RVA: 0x000369B5 File Offset: 0x00034BB5
			// (set) Token: 0x06008BA3 RID: 35747 RVA: 0x000369BD File Offset: 0x00034BBD
			public int ItemId { get; set; }

			// Token: 0x170014E4 RID: 5348
			// (get) Token: 0x06008BA4 RID: 35748 RVA: 0x000369C6 File Offset: 0x00034BC6
			// (set) Token: 0x06008BA5 RID: 35749 RVA: 0x000369CE File Offset: 0x00034BCE
			public int ItemCount { get; set; }
		}

		// Token: 0x02000FF3 RID: 4083
		public class ForcesRelicZFCostCnfVo
		{
			// Token: 0x170014E5 RID: 5349
			// (get) Token: 0x06008BA7 RID: 35751 RVA: 0x000369D7 File Offset: 0x00034BD7
			// (set) Token: 0x06008BA8 RID: 35752 RVA: 0x000369DF File Offset: 0x00034BDF
			public int Quantity { get; set; }

			// Token: 0x170014E6 RID: 5350
			// (get) Token: 0x06008BA9 RID: 35753 RVA: 0x000369E8 File Offset: 0x00034BE8
			// (set) Token: 0x06008BAA RID: 35754 RVA: 0x000369F0 File Offset: 0x00034BF0
			public int Place { get; set; }

			// Token: 0x170014E7 RID: 5351
			// (get) Token: 0x06008BAB RID: 35755 RVA: 0x000369F9 File Offset: 0x00034BF9
			// (set) Token: 0x06008BAC RID: 35756 RVA: 0x00036A01 File Offset: 0x00034C01
			public int CostNum1 { get; set; }

			// Token: 0x170014E8 RID: 5352
			// (get) Token: 0x06008BAD RID: 35757 RVA: 0x00036A0A File Offset: 0x00034C0A
			// (set) Token: 0x06008BAE RID: 35758 RVA: 0x00036A12 File Offset: 0x00034C12
			public int CostNum2 { get; set; }

			// Token: 0x170014E9 RID: 5353
			// (get) Token: 0x06008BAF RID: 35759 RVA: 0x00036A1B File Offset: 0x00034C1B
			// (set) Token: 0x06008BB0 RID: 35760 RVA: 0x00036A23 File Offset: 0x00034C23
			public int CostNum3 { get; set; }
		}
	}
}
