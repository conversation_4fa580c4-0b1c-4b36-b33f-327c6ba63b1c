﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Bussiness;
using Bussiness.Managers;
using Bussiness.Protocol;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000C89 RID: 3209
	public class DropInventory
	{
		// Token: 0x06007255 RID: 29269 RVA: 0x00262510 File Offset: 0x00260710
		public static bool CardDrop(eRoomType e, ref List<ItemInfo> info)
		{
			int num = (int)e;
			int dropCondiction = DropInventory.GetDropCondiction(eDropType.Cards, num.ToString(), "0");
			bool flag = dropCondiction > 0;
			if (flag)
			{
				List<ItemInfo> list = null;
				bool dropItems = DropInventory.GetDropItems(eDropType.Cards, dropCondiction, ref list);
				if (dropItems)
				{
					info = ((list != null) ? list : null);
					return true;
				}
			}
			return false;
		}

		// Token: 0x06007256 RID: 29270 RVA: 0x00262564 File Offset: 0x00260764
		public static bool BoxDrop(eRoomType e, ref List<ItemInfo> info)
		{
			int num = (int)e;
			int dropCondiction = DropInventory.GetDropCondiction(eDropType.Box, num.ToString(), "0");
			bool flag = dropCondiction > 0;
			if (flag)
			{
				List<ItemInfo> list = null;
				bool dropItems = DropInventory.GetDropItems(eDropType.Box, dropCondiction, ref list);
				if (dropItems)
				{
					info = ((list != null) ? list : null);
					return true;
				}
			}
			return false;
		}

		// Token: 0x06007257 RID: 29271 RVA: 0x002625B8 File Offset: 0x002607B8
		public static bool NPCDrop(int dropId, ref List<ItemInfo> info)
		{
			bool flag = dropId > 0;
			if (flag)
			{
				List<ItemInfo> list = null;
				bool dropItems = DropInventory.GetDropItems(eDropType.NPC, dropId, ref list);
				if (dropItems)
				{
					info = ((list != null) ? list : null);
					return true;
				}
			}
			return false;
		}

		// Token: 0x06007258 RID: 29272 RVA: 0x002625F4 File Offset: 0x002607F4
		public static bool BossDrop(int missionId, ref List<ItemInfo> info)
		{
			int dropCondiction = DropInventory.GetDropCondiction(eDropType.Boss, missionId.ToString(), "0");
			bool flag = dropCondiction > 0;
			if (flag)
			{
				List<ItemInfo> list = null;
				bool dropItems = DropInventory.GetDropItems(eDropType.Boss, dropCondiction, ref list);
				if (dropItems)
				{
					info = ((list != null) ? list : null);
					return true;
				}
			}
			return false;
		}

		// Token: 0x06007259 RID: 29273 RVA: 0x00262644 File Offset: 0x00260844
		public static bool CopyUserDrop(int copyId, ref List<ItemInfo> info)
		{
			int dropCondiction = DropInventory.GetDropCondiction(eDropType.Copy, copyId.ToString(), "1");
			bool flag = dropCondiction > 0;
			if (flag)
			{
				List<ItemInfo> list = null;
				bool dropItems = DropInventory.GetDropItems(eDropType.Copy, dropCondiction, ref list);
				if (dropItems)
				{
					info = ((list != null) ? list : null);
					return true;
				}
			}
			return false;
		}

		// Token: 0x0600725A RID: 29274 RVA: 0x00262694 File Offset: 0x00260894
		public static bool SpecialDrop(int missionId, int boxType, ref List<ItemInfo> info)
		{
			int dropCondiction = DropInventory.GetDropCondiction(eDropType.Special, missionId.ToString(), boxType.ToString());
			bool flag = dropCondiction > 0;
			if (flag)
			{
				List<ItemInfo> list = null;
				bool dropItems = DropInventory.GetDropItems(eDropType.Special, dropCondiction, ref list);
				if (dropItems)
				{
					info = ((list != null) ? list : null);
					return true;
				}
			}
			return false;
		}

		// Token: 0x0600725B RID: 29275 RVA: 0x002626E8 File Offset: 0x002608E8
		public static bool PvPQuestsDrop(int tempId, bool playResult, ref List<ItemInfo> info)
		{
			int dropCondiction = DropInventory.GetDropCondiction(eDropType.PvpQuests, tempId.ToString(), Convert.ToInt16(playResult).ToString());
			bool flag = dropCondiction > 0;
			if (flag)
			{
				List<ItemInfo> list = null;
				bool dropItems = DropInventory.GetDropItems(eDropType.PvpQuests, dropCondiction, ref list);
				if (dropItems)
				{
					info = ((list != null) ? list : null);
					return true;
				}
			}
			return false;
		}

		// Token: 0x0600725C RID: 29276 RVA: 0x00262744 File Offset: 0x00260944
		public static bool FireDrop(eRoomType e, ref List<ItemInfo> info)
		{
			int num = (int)e;
			int dropCondiction = DropInventory.GetDropCondiction(eDropType.Fire, num.ToString(), "0");
			bool flag = dropCondiction > 0;
			if (flag)
			{
				List<ItemInfo> list = null;
				bool dropItems = DropInventory.GetDropItems(eDropType.Fire, dropCondiction, ref list);
				if (dropItems)
				{
					info = ((list != null) ? list : null);
					return true;
				}
			}
			return false;
		}

		// Token: 0x0600725D RID: 29277 RVA: 0x00262798 File Offset: 0x00260998
		public static bool PvEQuestsDrop(int npcId, int tempId, ref List<ItemInfo> info)
		{
			int dropCondiction = DropInventory.GetDropCondiction(eDropType.PveQuests, npcId.ToString(), tempId.ToString());
			bool flag = dropCondiction > 0;
			if (flag)
			{
				List<ItemInfo> list = null;
				bool dropItems = DropInventory.GetDropItems(eDropType.PveQuests, dropCondiction, ref list);
				if (dropItems)
				{
					info = ((list != null) ? list : null);
					return true;
				}
			}
			return false;
		}

		// Token: 0x0600725E RID: 29278 RVA: 0x002627EC File Offset: 0x002609EC
		public static bool AnswerDrop(int answerId, ref List<ItemInfo> info)
		{
			int dropCondiction = DropInventory.GetDropCondiction(eDropType.Answer, answerId.ToString(), "0");
			bool flag = dropCondiction > 0 && dropCondiction > 0;
			if (flag)
			{
				List<ItemInfo> list = null;
				bool dropItems = DropInventory.GetDropItems(eDropType.Answer, dropCondiction, ref list);
				if (dropItems)
				{
					info = ((list != null) ? list : null);
					return true;
				}
			}
			return false;
		}

		// Token: 0x0600725F RID: 29279 RVA: 0x00262848 File Offset: 0x00260A48
		public static bool RetrieveDrop(int user, ref List<ItemInfo> info)
		{
			int dropCondiction = DropInventory.GetDropCondiction(eDropType.Retrieve, user.ToString(), "0");
			bool flag = dropCondiction > 0;
			if (flag)
			{
				List<ItemInfo> list = null;
				bool dropItems = DropInventory.GetDropItems(eDropType.Retrieve, dropCondiction, ref list);
				if (dropItems)
				{
					info = ((list != null) ? list : null);
					return true;
				}
			}
			return false;
		}

		// Token: 0x06007260 RID: 29280 RVA: 0x0026289C File Offset: 0x00260A9C
		public static bool GetPetDrop(int copyId, int user, ref List<PetTemplateInfo> info)
		{
			int dropCondiction = DropInventory.GetDropCondiction(eDropType.Pet, copyId.ToString(), user.ToString());
			bool flag = dropCondiction > 0;
			if (flag)
			{
				List<PetTemplateInfo> list = null;
				bool dropPets = DropInventory.GetDropPets(eDropType.Pet, dropCondiction, ref list);
				if (dropPets)
				{
					info = ((list != null) ? list : null);
					return true;
				}
			}
			return false;
		}

		// Token: 0x06007261 RID: 29281 RVA: 0x002628F0 File Offset: 0x00260AF0
		public static bool FightLabUserDrop(int copyId, ref List<ItemInfo> info)
		{
			int dropCondiction = DropInventory.GetDropCondiction(eDropType.FightLab, copyId.ToString(), "0");
			bool flag = dropCondiction > 0;
			bool flag4;
			if (flag)
			{
				List<DropItem> list = DropMgr.FindDropItem(dropCondiction);
				for (int i = 0; i < list.Count; i++)
				{
					int num = DropInventory.random.Next(list[i].BeginData, list[i].EndData);
					ItemInfo itemInfo = ItemInfo.CreateFromTemplate(ItemMgr.FindItemTemplate(list[i].ItemId), num, copyId);
					bool flag2 = itemInfo != null;
					if (flag2)
					{
						itemInfo.IsBinds = list[i].IsBind;
						itemInfo.ValidDate = list[i].ValueDate;
						itemInfo.IsTips = list[i].IsTips;
						itemInfo.IsLogs = list[i].IsLogs;
						bool flag3 = info == null;
						if (flag3)
						{
							info = new List<ItemInfo>();
						}
						info.Add(itemInfo);
					}
				}
				flag4 = true;
			}
			else
			{
				flag4 = false;
			}
			return flag4;
		}

		// Token: 0x06007262 RID: 29282 RVA: 0x00262A0C File Offset: 0x00260C0C
		public static List<ItemInfo> CopySystemDrop(int copyId, int OpenCount)
		{
			int num = Convert.ToInt32((double)OpenCount * 0.1);
			int num2 = Convert.ToInt32((double)OpenCount * 0.3);
			int num3 = OpenCount - num - num2;
			List<ItemInfo> list = new List<ItemInfo>();
			List<ItemInfo> list2 = null;
			int dropCondiction = DropInventory.GetDropCondiction(eDropType.Copy, copyId.ToString(), "2");
			bool flag = dropCondiction > 0;
			if (flag)
			{
				for (int i = 0; i < num; i++)
				{
					bool flag2 = DropInventory.GetDropItems(eDropType.Copy, dropCondiction, ref list2) && list2 != null;
					if (flag2)
					{
						list.Add(list2[0]);
						list2 = null;
					}
				}
			}
			int dropCondiction2 = DropInventory.GetDropCondiction(eDropType.Copy, copyId.ToString(), "3");
			bool flag3 = dropCondiction2 > 0;
			if (flag3)
			{
				for (int j = 0; j < num2; j++)
				{
					bool flag4 = DropInventory.GetDropItems(eDropType.Copy, dropCondiction2, ref list2) && list2 != null;
					if (flag4)
					{
						list.Add(list2[0]);
						list2 = null;
					}
				}
			}
			int dropCondiction3 = DropInventory.GetDropCondiction(eDropType.Copy, copyId.ToString(), "4");
			bool flag5 = dropCondiction3 > 0;
			if (flag5)
			{
				for (int k = 0; k < num3; k++)
				{
					bool flag6 = DropInventory.GetDropItems(eDropType.Copy, dropCondiction3, ref list2) && list2 != null;
					if (flag6)
					{
						list.Add(list2[0]);
						list2 = null;
					}
				}
			}
			return DropInventory.RandomSortList(list);
		}

		// Token: 0x06007263 RID: 29283 RVA: 0x00262B90 File Offset: 0x00260D90
		public static List<ItemInfo> RandomSortList(List<ItemInfo> list)
		{
			return list.OrderBy((ItemInfo key) => DropInventory.random.Next()).ToList<ItemInfo>();
		}

		// Token: 0x06007264 RID: 29284 RVA: 0x00262BCC File Offset: 0x00260DCC
		private static int GetDropCondiction(eDropType type, string para1, string para2)
		{
			try
			{
				return DropMgr.FindCondiction(type, para1, para2);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = DropInventory.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ILog log = DropInventory.log;
					string text = "Drop Error：";
					string text2 = type.ToString();
					string text3 = " @ ";
					Exception ex2 = ex;
					log.Error(text + text2 + text3 + ((ex2 != null) ? ex2.ToString() : null));
				}
			}
			return 0;
		}

		// Token: 0x06007265 RID: 29285 RVA: 0x00262C44 File Offset: 0x00260E44
		private static bool GetDropItems(eDropType type, int dropId, ref List<ItemInfo> itemInfos)
		{
			Console.WriteLine("dropId:{0}", dropId);
			bool flag = dropId == 0;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				try
				{
					int num = 1;
					List<DropItem> list = DropMgr.FindDropItem(dropId);
					int maxRound = ThreadSafeRandom.NextStatic(list.Select((DropItem s) => s.Random).Max());
					List<DropItem> list2 = list.Where((DropItem s) => s.Random >= maxRound).ToList<DropItem>();
					int num2 = list2.Count<DropItem>();
					bool flag3 = num2 == 0;
					if (flag3)
					{
						return false;
					}
					num = ((num > num2) ? num2 : num);
					int[] randomUnrepeatArray = DropInventory.GetRandomUnrepeatArray(0, num2 - 1, num);
					int[] array = randomUnrepeatArray;
					int[] array2 = array;
					int[] array3 = array2;
					foreach (int num3 in array3)
					{
						int num4 = ThreadSafeRandom.NextStatic(list2[num3].BeginData, list2[num3].EndData);
						ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(list2[num3].ItemId);
						ItemInfo itemInfo = ItemInfo.CreateFromTemplate(itemTemplateInfo, num4, 101);
						bool flag4 = itemInfo != null;
						if (flag4)
						{
							itemInfo.IsBinds = list2[num3].IsBind;
							itemInfo.ValidDate = list2[num3].ValueDate;
							itemInfo.IsTips = list2[num3].IsTips;
							bool flag5 = itemInfos == null;
							if (flag5)
							{
								itemInfos = new List<ItemInfo>();
							}
							bool flag6 = DropInfoMgr.CanDrop(itemTemplateInfo.TemplateID);
							if (flag6)
							{
								itemInfos.Add(itemInfo);
							}
						}
					}
					return true;
				}
				catch (Exception ex)
				{
					bool isErrorEnabled = DropInventory.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						ILog log = DropInventory.log;
						string text = "Drop Error：";
						string text2 = type.ToString();
						string text3 = " @ ";
						Exception ex2 = ex;
						log.Error(text + text2 + text3 + ((ex2 != null) ? ex2.ToString() : null));
					}
				}
				flag2 = false;
			}
			return flag2;
		}

		// Token: 0x06007266 RID: 29286 RVA: 0x00262E74 File Offset: 0x00261074
		private static bool GetDropPets(eDropType type, int dropId, ref List<PetTemplateInfo> petInfos)
		{
			bool flag = dropId == 0;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				try
				{
					int num = 1;
					List<DropItem> list = DropMgr.FindDropItem(dropId);
					int maxRound = ThreadSafeRandom.NextStatic(list.Select((DropItem s) => s.Random).Max());
					List<DropItem> list2 = list.Where((DropItem s) => s.Random >= maxRound).ToList<DropItem>();
					int num2 = list2.Count<DropItem>();
					bool flag3 = num2 == 0;
					if (flag3)
					{
						return false;
					}
					num = ((num > num2) ? num2 : num);
					int[] randomUnrepeatArray = DropInventory.GetRandomUnrepeatArray(0, num2 - 1, num);
					int[] array = randomUnrepeatArray;
					int[] array2 = array;
					int[] array3 = array2;
					foreach (int num3 in array3)
					{
						int num4 = ThreadSafeRandom.NextStatic(list2[num3].BeginData, list2[num3].EndData);
						PetTemplateInfo petTemplateInfo = PetMgr.FindPetTemplate(list2[num3].ItemId);
						bool flag4 = petTemplateInfo != null;
						if (flag4)
						{
							bool flag5 = petInfos == null;
							if (flag5)
							{
								petInfos = new List<PetTemplateInfo>();
							}
							bool flag6 = DropInfoMgr.CanDrop(petTemplateInfo.TemplateID);
							if (flag6)
							{
								petInfos.Add(petTemplateInfo);
							}
						}
					}
					return true;
				}
				catch (Exception ex)
				{
					bool isErrorEnabled = DropInventory.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						ILog log = DropInventory.log;
						string text = "Drop Error：";
						string text2 = type.ToString();
						string text3 = " @ ";
						Exception ex2 = ex;
						log.Error(text + text2 + text3 + ((ex2 != null) ? ex2.ToString() : null));
					}
				}
				flag2 = false;
			}
			return flag2;
		}

		// Token: 0x06007267 RID: 29287 RVA: 0x00263044 File Offset: 0x00261244
		public static int[] GetRandomUnrepeatArray(int minValue, int maxValue, int count)
		{
			int[] array = new int[count];
			for (int i = 0; i < count; i++)
			{
				int num = ThreadSafeRandom.NextStatic(minValue, maxValue + 1);
				int num2 = 0;
				for (int j = 0; j < i; j++)
				{
					bool flag = array[j] == num;
					if (flag)
					{
						num2++;
					}
				}
				bool flag2 = num2 == 0;
				if (flag2)
				{
					array[i] = num;
				}
				else
				{
					i--;
				}
			}
			return array;
		}

		// Token: 0x04003DE0 RID: 15840
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04003DE1 RID: 15841
		private static ThreadSafeRandom random = new ThreadSafeRandom();

		// Token: 0x04003DE2 RID: 15842
		public static int roundDate = 0;
	}
}
