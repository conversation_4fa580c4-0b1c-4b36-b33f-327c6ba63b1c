﻿using System;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E41 RID: 3649
	public class PE3182 : BasePetEffect
	{
		// Token: 0x06007F12 RID: 32530 RVA: 0x002A38DC File Offset: 0x002A1ADC
		public PE3182(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE3182, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F13 RID: 32531 RVA: 0x002A3958 File Offset: 0x002A1B58
		public override bool Start(Living living)
		{
			PE3182 pe = living.PetEffectList.GetOfType(ePetEffectType.PE3182) as PE3182;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F14 RID: 32532 RVA: 0x00031650 File Offset: 0x0002F850
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
		}

		// Token: 0x06007F15 RID: 32533 RVA: 0x00031679 File Offset: 0x0002F879
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x06007F16 RID: 32534 RVA: 0x000316A2 File Offset: 0x0002F8A2
		private void Player_BeginSelfTurn(Living player)
		{
			this.m_added = 0;
		}

		// Token: 0x06007F17 RID: 32535 RVA: 0x002A39B4 File Offset: 0x002A1BB4
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			this.m_added++;
			bool flag = this.m_added == 1 && this.rand.Next(100) < 50 && living.Game is PVPGame;
			if (flag)
			{
				damageAmount = 0;
				criticalAmount = 0;
				living.Game.AddAction(new LivingSayAction(living, "Miễn Dịch", 9, 0, 1000));
			}
		}

		// Token: 0x04004E02 RID: 19970
		private int m_type = 0;

		// Token: 0x04004E03 RID: 19971
		private int m_count = 0;

		// Token: 0x04004E04 RID: 19972
		private int m_probability = 0;

		// Token: 0x04004E05 RID: 19973
		private int m_delay = 0;

		// Token: 0x04004E06 RID: 19974
		private int m_coldDown = 0;

		// Token: 0x04004E07 RID: 19975
		private int m_currentId;

		// Token: 0x04004E08 RID: 19976
		private int m_added = 0;
	}
}
