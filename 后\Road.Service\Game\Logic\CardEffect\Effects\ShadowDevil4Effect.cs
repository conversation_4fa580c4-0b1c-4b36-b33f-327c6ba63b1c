﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F36 RID: 3894
	public class ShadowDevil4Effect : BaseCardEffect
	{
		// Token: 0x06008473 RID: 33907 RVA: 0x002B8358 File Offset: 0x002B6558
		public ShadowDevil4Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.ShadowDevil4, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x06008474 RID: 33908 RVA: 0x002B83C8 File Offset: 0x002B65C8
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.ShadowDevil4) is ShadowDevil4Effect;
			return flag || base.Start(living);
		}

		// Token: 0x06008475 RID: 33909 RVA: 0x00034EE2 File Offset: 0x000330E2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerAfterReset += this.ChangeProperty1;
		}

		// Token: 0x06008476 RID: 33910 RVA: 0x00034F0B File Offset: 0x0003310B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.PlayerAfterReset -= this.ChangeProperty1;
		}

		// Token: 0x06008477 RID: 33911 RVA: 0x002B8400 File Offset: 0x002B6600
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = living.Game is PVEGame && (living.Game as PVEGame).Info.ID == 4 && criticalAmount > 0;
			if (flag)
			{
				criticalAmount += this.m_value;
			}
		}

		// Token: 0x06008478 RID: 33912 RVA: 0x002B844C File Offset: 0x002B664C
		private void ChangeProperty1(Player player)
		{
			bool flag = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 4;
			if (flag)
			{
				player.Game.SendMessage(player.PlayerDetail, "您激活了黑暗堡垒4件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活黑暗堡垒4件套卡.", 3);
			}
		}

		// Token: 0x04005296 RID: 21142
		private int m_indexValue = 0;

		// Token: 0x04005297 RID: 21143
		private int m_value = 0;

		// Token: 0x04005298 RID: 21144
		private int m_added = 0;
	}
}
