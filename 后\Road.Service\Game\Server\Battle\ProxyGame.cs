﻿using System;
using Game.Base;
using Game.Base.Packets;
using Game.Logic;

namespace Game.Server.Battle
{
	// Token: 0x02000C45 RID: 3141
	public class ProxyGame : AbstractGame
	{
		// Token: 0x06007005 RID: 28677 RVA: 0x0002A217 File Offset: 0x00028417
		public ProxyGame(int id, FightServerConnector fightServer, eRoomType roomType, eGameType gameType, int timeType)
			: base(id, roomType, gameType, timeType)
		{
			this.m_fightingServer = fightServer;
			this.m_fightingServer.Disconnected += this.m_fightingServer_Disconnected;
		}

		// Token: 0x06007006 RID: 28678 RVA: 0x000255BF File Offset: 0x000237BF
		private void m_fightingServer_Disconnected(BaseClient client)
		{
			this.Stop();
		}

		// Token: 0x06007007 RID: 28679 RVA: 0x0002A246 File Offset: 0x00028446
		public override void ProcessData(GSPacketIn pkg)
		{
			this.m_fightingServer.SendToGame(base.Id, pkg);
		}

		// Token: 0x04003C44 RID: 15428
		private FightServerConnector m_fightingServer;
	}
}
