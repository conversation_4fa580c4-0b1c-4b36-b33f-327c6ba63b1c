﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DC1 RID: 3521
	public class AE1093 : BasePetEffect
	{
		// Token: 0x06007C68 RID: 31848 RVA: 0x0029758C File Offset: 0x0029578C
		public AE1093(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1093, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C69 RID: 31849 RVA: 0x0029760C File Offset: 0x0029580C
		public override bool Start(Living living)
		{
			AE1093 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1093) as AE1093;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C6A RID: 31850 RVA: 0x0002FA58 File Offset: 0x0002DC58
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C6B RID: 31851 RVA: 0x0002FA6E File Offset: 0x0002DC6E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C6C RID: 31852 RVA: 0x0029766C File Offset: 0x0029586C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddPetEffect(new CE1093(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004A81 RID: 19073
		private int m_type = 0;

		// Token: 0x04004A82 RID: 19074
		private int m_count = 0;

		// Token: 0x04004A83 RID: 19075
		private int m_probability = 0;

		// Token: 0x04004A84 RID: 19076
		private int m_delay = 0;

		// Token: 0x04004A85 RID: 19077
		private int m_coldDown = 0;

		// Token: 0x04004A86 RID: 19078
		private int m_currentId;

		// Token: 0x04004A87 RID: 19079
		private int m_added = 0;
	}
}
