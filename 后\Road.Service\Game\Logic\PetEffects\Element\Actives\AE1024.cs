﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D97 RID: 3479
	public class AE1024 : BasePetEffect
	{
		// Token: 0x06007B8F RID: 31631 RVA: 0x002937EC File Offset: 0x002919EC
		public AE1024(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1024, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B90 RID: 31632 RVA: 0x0029386C File Offset: 0x00291A6C
		public override bool Start(Living living)
		{
			AE1024 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1024) as AE1024;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B91 RID: 31633 RVA: 0x0002F2D3 File Offset: 0x0002D4D3
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.PlayerAnyShellThrow += this.Player_PlayerAnyShellThrow;
		}

		// Token: 0x06007B92 RID: 31634 RVA: 0x002938CC File Offset: 0x00291ACC
		private void Player_PlayerAnyShellThrow(Player player)
		{
			bool flag = !this.IsTrigger;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					bool flag2 = player2.PlayerDetail != player.PlayerDetail;
					if (flag2)
					{
						player2.Game.SendPetBuff(player2, base.ElementInfo, true);
						player2.Game.SendPlayerPicture(player2, 29, true);
						player2.AddPetEffect(new CE1024(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
					}
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007B93 RID: 31635 RVA: 0x0002F2FC File Offset: 0x0002D4FC
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.PlayerAnyShellThrow -= this.Player_PlayerAnyShellThrow;
		}

		// Token: 0x06007B94 RID: 31636 RVA: 0x002939BC File Offset: 0x00291BBC
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x0400495B RID: 18779
		private int m_type = 0;

		// Token: 0x0400495C RID: 18780
		private int m_count = 0;

		// Token: 0x0400495D RID: 18781
		private int m_probability = 0;

		// Token: 0x0400495E RID: 18782
		private int m_delay = 0;

		// Token: 0x0400495F RID: 18783
		private int m_coldDown = 0;

		// Token: 0x04004960 RID: 18784
		private int m_currentId;

		// Token: 0x04004961 RID: 18785
		private int m_added = 0;
	}
}
