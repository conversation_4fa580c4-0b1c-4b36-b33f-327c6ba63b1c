﻿using System;

namespace Game.Logic
{
	// Token: 0x02000CA1 RID: 3233
	public class LoadingFileInfo
	{
		// Token: 0x060073B4 RID: 29620 RVA: 0x0002B098 File Offset: 0x00029298
		public LoadingFileInfo(int type, string path, string className)
		{
			this.Type = type;
			this.Path = path;
			this.ClassName = className;
		}

		// Token: 0x040043C6 RID: 17350
		public int Type;

		// Token: 0x040043C7 RID: 17351
		public string Path;

		// Token: 0x040043C8 RID: 17352
		public string ClassName;
	}
}
