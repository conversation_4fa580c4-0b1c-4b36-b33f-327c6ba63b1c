﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000CFE RID: 3326
	public class PetAddAgilityEquip : AbstractPetEffect
	{
		// Token: 0x06007852 RID: 30802 RVA: 0x002851C0 File Offset: 0x002833C0
		public PetAddAgilityEquip(int count, string elementID)
			: base(ePetEffectType.PetAddAgilityEquip, elementID)
		{
			this.m_count = count;
			bool flag = !(elementID == "4395");
			if (flag)
			{
				bool flag2 = elementID == "4254";
				if (flag2)
				{
					this.m_value = 30.0;
					this.m_count = 3;
					this.m_percent = true;
				}
			}
			else
			{
				this.m_value = 30.0;
				this.m_percent = true;
			}
		}

		// Token: 0x06007853 RID: 30803 RVA: 0x00285240 File Offset: 0x00283440
		public override bool Start(Living living)
		{
			PetAddAgilityEquip petAddAgilityEquip = living.PetEffectList.GetOfType(ePetEffectType.PetAddAgilityEquip) as PetAddAgilityEquip;
			bool flag = petAddAgilityEquip != null;
			bool flag2;
			if (flag)
			{
				petAddAgilityEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007854 RID: 30804 RVA: 0x00285288 File Offset: 0x00283488
		public override void OnAttached(Living living)
		{
			bool flag = this.m_added == 0.0;
			if (flag)
			{
				bool percent = this.m_percent;
				if (percent)
				{
					this.m_added = living.Agility * this.m_value / 100.0;
				}
				else
				{
					this.m_added = this.m_value;
				}
				living.Agility += this.m_added;
				living.Game.UpdateEnergy((Player)living, (int)((0.0 - this.m_added) / 30.0));
			}
			living.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x06007855 RID: 30805 RVA: 0x0002C959 File Offset: 0x0002AB59
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x06007856 RID: 30806 RVA: 0x0028533C File Offset: 0x0028353C
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.Agility -= this.m_added;
				this.m_added = 0.0;
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
		}

		// Token: 0x04004685 RID: 18053
		private int m_count;

		// Token: 0x04004686 RID: 18054
		private double m_value;

		// Token: 0x04004687 RID: 18055
		private bool m_percent;

		// Token: 0x04004688 RID: 18056
		private double m_added;
	}
}
