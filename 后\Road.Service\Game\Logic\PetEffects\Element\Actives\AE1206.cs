﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DFF RID: 3583
	public class AE1206 : BasePetEffect
	{
		// Token: 0x06007DAE RID: 32174 RVA: 0x0029D0C0 File Offset: 0x0029B2C0
		public AE1206(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1206, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DAF RID: 32175 RVA: 0x0029D140 File Offset: 0x0029B340
		public override bool Start(Living living)
		{
			AE1206 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1206) as AE1206;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DB0 RID: 32176 RVA: 0x00030787 File Offset: 0x0002E987
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DB1 RID: 32177 RVA: 0x0003079D File Offset: 0x0002E99D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DB2 RID: 32178 RVA: 0x0029D1A0 File Offset: 0x0029B3A0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.AddPetEffect(new CE1206(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004C31 RID: 19505
		private int m_type = 0;

		// Token: 0x04004C32 RID: 19506
		private int m_count = 0;

		// Token: 0x04004C33 RID: 19507
		private int m_probability = 0;

		// Token: 0x04004C34 RID: 19508
		private int m_delay = 0;

		// Token: 0x04004C35 RID: 19509
		private int m_coldDown = 0;

		// Token: 0x04004C36 RID: 19510
		private int m_currentId;

		// Token: 0x04004C37 RID: 19511
		private int m_added = 0;
	}
}
