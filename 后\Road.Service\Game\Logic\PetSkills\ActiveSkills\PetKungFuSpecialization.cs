﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D3D RID: 3389
	public class PetKungFuSpecialization : BasePetEffect
	{
		// Token: 0x060079A3 RID: 31139 RVA: 0x0002DCA4 File Offset: 0x0002BEA4
		public PetKungFuSpecialization(int probability, int skillId, string elementID)
			: base(ePetEffectType.PetKungFuSpecialization, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x060079A4 RID: 31140 RVA: 0x0028BC54 File Offset: 0x00289E54
		public override bool Start(Living living)
		{
			PetKungFuSpecialization petKungFuSpecialization = living.PetEffectList.GetOfType(ePetEffectType.PetKungFuSpecialization) as PetKungFuSpecialization;
			bool flag = petKungFuSpecialization != null;
			bool flag2;
			if (flag)
			{
				petKungFuSpecialization.m_probability = ((this.m_probability > petKungFuSpecialization.m_probability) ? this.m_probability : petKungFuSpecialization.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079A5 RID: 31141 RVA: 0x0002DCCD File Offset: 0x0002BECD
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
		}

		// Token: 0x060079A6 RID: 31142 RVA: 0x0002DCE3 File Offset: 0x0002BEE3
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
		}

		// Token: 0x060079A7 RID: 31143 RVA: 0x0028BCB4 File Offset: 0x00289EB4
		private void player_PlayerBuffSkillPet(Player player)
		{
			player.Game.sendShowPicSkil(player, base.ElementInfo, true);
			int num = 1;
			int currentId = this.m_currentId;
			int num2 = currentId;
			if (num2 <= 455)
			{
				if (num2 - 447 > 1 && num2 != 453 && num2 != 455)
				{
					goto IL_006C;
				}
			}
			else if (num2 - 468 > 1 && num2 != 476 && num2 != 498)
			{
				goto IL_006C;
			}
			num = 2;
			IL_006C:
			new PetKungFuSpecializationEquip(num, base.ElementInfo.ID.ToString()).Start(player);
		}

		// Token: 0x04004746 RID: 18246
		private int m_probability;

		// Token: 0x04004747 RID: 18247
		private int m_currentId;
	}
}
