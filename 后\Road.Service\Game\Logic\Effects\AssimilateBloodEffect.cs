﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EDD RID: 3805
	public class AssimilateBloodEffect : BasePlayerEffect
	{
		// Token: 0x060082DC RID: 33500 RVA: 0x00033BFF File Offset: 0x00031DFF
		public AssimilateBloodEffect(int count, int probability)
			: base(eEffectType.AssimilateBloodEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x060082DD RID: 33501 RVA: 0x002B1F88 File Offset: 0x002B0188
		public override bool Start(Living living)
		{
			AssimilateBloodEffect assimilateBloodEffect = living.EffectList.GetOfType(eEffectType.AssimilateBloodEffect) as AssimilateBloodEffect;
			bool flag = assimilateBloodEffect != null;
			bool flag2;
			if (flag)
			{
				assimilateBloodEffect.m_probability = ((this.m_probability > assimilateBloodEffect.m_probability) ? this.m_probability : assimilateBloodEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082DE RID: 33502 RVA: 0x00033C27 File Offset: 0x00031E27
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.player_AfterKillingLiving;
			player.BeforePlayerShoot += this.player_PlayerShoot;
		}

		// Token: 0x060082DF RID: 33503 RVA: 0x002B1FE4 File Offset: 0x002B01E4
		private void player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = living.IsLiving && this.IsTrigger;
			if (flag)
			{
				living.AddBlood(damageAmount * this.m_count / 100);
				living.Game.SendGameUpdateHealth(living, 0, damageAmount * this.m_count / 100);
			}
		}

		// Token: 0x060082E0 RID: 33504 RVA: 0x00033C50 File Offset: 0x00031E50
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.player_AfterKillingLiving;
			player.BeforePlayerShoot -= this.player_PlayerShoot;
		}

		// Token: 0x060082E1 RID: 33505 RVA: 0x002B2034 File Offset: 0x002B0234
		private void player_PlayerShoot(Player player, int ball)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				this.IsTrigger = true;
				player.AttackEffectTrigger = true;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("AssimilateBloodEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051E4 RID: 20964
		private int m_count = 0;

		// Token: 0x040051E5 RID: 20965
		private int m_probability = 0;
	}
}
