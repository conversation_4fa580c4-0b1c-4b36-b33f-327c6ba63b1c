﻿using System;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C6E RID: 3182
	public class LevelUpgradeCondition : BaseCondition
	{
		// Token: 0x060070AF RID: 28847 RVA: 0x0002A421 File Offset: 0x00028621
		public LevelUpgradeCondition(BaseAchievement quest, AchievementConditionInfo info, int value)
			: base(quest, info, value)
		{
		}

		// Token: 0x060070B0 RID: 28848 RVA: 0x0002A68F File Offset: 0x0002888F
		public override void AddTrigger(GamePlayer player)
		{
			player.LevelUp += this.player_LevelUp;
		}

		// Token: 0x060070B1 RID: 28849 RVA: 0x00250444 File Offset: 0x0024E644
		private void player_LevelUp(GamePlayer player)
		{
			bool flag = base.Value < player.Level;
			if (flag)
			{
				base.Value = player.Level;
			}
		}

		// Token: 0x060070B2 RID: 28850 RVA: 0x0002A6A5 File Offset: 0x000288A5
		public override void RemoveTrigger(GamePlayer player)
		{
			player.LevelUp -= this.player_LevelUp;
		}

		// Token: 0x060070B3 RID: 28851 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}
	}
}
