﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E08 RID: 3592
	public class AE1221 : BasePetEffect
	{
		// Token: 0x06007DDB RID: 32219 RVA: 0x0029DE90 File Offset: 0x0029C090
		public AE1221(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1221, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007DDC RID: 32220 RVA: 0x0029DF10 File Offset: 0x0029C110
		public override bool Start(Living living)
		{
			AE1221 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1221) as AE1221;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007DDD RID: 32221 RVA: 0x00030913 File Offset: 0x0002EB13
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DDE RID: 32222 RVA: 0x00030929 File Offset: 0x0002EB29
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007DDF RID: 32223 RVA: 0x0029DF70 File Offset: 0x0029C170
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.Game.SendPetBuff(player2, base.ElementInfo, true);
					player2.AddPetEffect(new CE1221(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004C70 RID: 19568
		private int m_type = 0;

		// Token: 0x04004C71 RID: 19569
		private int m_count = 0;

		// Token: 0x04004C72 RID: 19570
		private int m_probability = 0;

		// Token: 0x04004C73 RID: 19571
		private int m_delay = 0;

		// Token: 0x04004C74 RID: 19572
		private int m_coldDown = 0;

		// Token: 0x04004C75 RID: 19573
		private int m_currentId;

		// Token: 0x04004C76 RID: 19574
		private int m_added = 0;
	}
}
