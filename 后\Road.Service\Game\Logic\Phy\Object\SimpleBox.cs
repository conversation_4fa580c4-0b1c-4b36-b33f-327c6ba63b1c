﻿using System;
using System.Drawing;
using SqlDataProvider.Data;

namespace Game.Logic.Phy.Object
{
	// Token: 0x02000CE0 RID: 3296
	public class SimpleBox : PhysicalObj
	{
		// Token: 0x1700147D RID: 5245
		// (get) Token: 0x0600776B RID: 30571 RVA: 0x00280974 File Offset: 0x0027EB74
		// (set) Token: 0x0600776C RID: 30572 RVA: 0x0002C107 File Offset: 0x0002A307
		public int UserID
		{
			get
			{
				return this._userID;
			}
			set
			{
				this._userID = value;
			}
		}

		// Token: 0x1700147E RID: 5246
		// (get) Token: 0x0600776D RID: 30573 RVA: 0x0028098C File Offset: 0x0027EB8C
		// (set) Token: 0x0600776E RID: 30574 RVA: 0x0002C111 File Offset: 0x0002A311
		public int LiveCount
		{
			get
			{
				return this._liveCount;
			}
			set
			{
				this._liveCount = value;
			}
		}

		// Token: 0x1700147F RID: 5247
		// (get) Token: 0x0600776F RID: 30575 RVA: 0x0002C11B File Offset: 0x0002A31B
		public ItemInfo Item
		{
			get
			{
				return this.m_item;
			}
		}

		// Token: 0x17001480 RID: 5248
		// (get) Token: 0x06007770 RID: 30576 RVA: 0x0002C123 File Offset: 0x0002A323
		public override int Type
		{
			get
			{
				return this.m_boxType;
			}
		}

		// Token: 0x06007771 RID: 30577 RVA: 0x002809A4 File Offset: 0x0027EBA4
		public SimpleBox(int id, string model, ItemInfo item, int type)
			: base(id, "", model, "", 1, 1, 0)
		{
			this._userID = 0;
			this.m_rect = new Rectangle(-15, -15, 30, 30);
			this.m_item = item;
			this.m_boxType = type;
		}

		// Token: 0x06007772 RID: 30578 RVA: 0x002809F4 File Offset: 0x0027EBF4
		public override void CollidedByObject(Physics phy)
		{
			bool flag = phy is SimpleBomb;
			if (flag)
			{
				SimpleBomb simpleBomb = phy as SimpleBomb;
				simpleBomb.Owner.PickBox(this);
			}
		}

		// Token: 0x04004600 RID: 17920
		private int _userID;

		// Token: 0x04004601 RID: 17921
		private int _liveCount;

		// Token: 0x04004602 RID: 17922
		private ItemInfo m_item;

		// Token: 0x04004603 RID: 17923
		private int m_boxType;
	}
}
