﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F08 RID: 3848
	[GameCommand(116, "关卡准备")]
	public class MissionPrepareCommand : ICommandHandler
	{
		// Token: 0x060083A8 RID: 33704 RVA: 0x002B5174 File Offset: 0x002B3374
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool flag = game.GameState == eGameState.SessionPrepared || game.GameState == eGameState.GameOver;
			if (flag)
			{
				bool flag2 = packet.ReadBoolean();
				bool flag3 = player.Ready != flag2;
				if (flag3)
				{
					player.Ready = flag2;
					game.SendToAll(packet);
				}
			}
		}
	}
}
