﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.NormalSpell
{
	// Token: 0x02000CBA RID: 3258
	[SpellAttibute(129)]
	public class GridSpell : ISpellHandler
	{
		// Token: 0x060074F6 RID: 29942 RVA: 0x0026E0E4 File Offset: 0x0026C2E4
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.SetBall(129);
			}
		}
	}
}
