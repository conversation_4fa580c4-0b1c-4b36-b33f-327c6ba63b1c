﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E21 RID: 3617
	public class AE1259 : BasePetEffect
	{
		// Token: 0x06007E5D RID: 32349 RVA: 0x002A017C File Offset: 0x0029E37C
		public AE1259(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1259, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E5E RID: 32350 RVA: 0x002A01FC File Offset: 0x0029E3FC
		public override bool Start(Living living)
		{
			AE1259 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1259) as AE1259;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E5F RID: 32351 RVA: 0x00030E27 File Offset: 0x0002F027
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E60 RID: 32352 RVA: 0x00030E3D File Offset: 0x0002F03D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E61 RID: 32353 RVA: 0x002A025C File Offset: 0x0029E45C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.AddPetEffect(new CE1259(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004D1F RID: 19743
		private int m_type = 0;

		// Token: 0x04004D20 RID: 19744
		private int m_count = 0;

		// Token: 0x04004D21 RID: 19745
		private int m_probability = 0;

		// Token: 0x04004D22 RID: 19746
		private int m_delay = 0;

		// Token: 0x04004D23 RID: 19747
		private int m_coldDown = 0;

		// Token: 0x04004D24 RID: 19748
		private int m_currentId;

		// Token: 0x04004D25 RID: 19749
		private int m_added = 0;
	}
}
