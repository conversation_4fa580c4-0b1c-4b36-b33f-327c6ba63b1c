﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E4E RID: 3662
	public class CE1028 : BasePetEffect
	{
		// Token: 0x06007F5F RID: 32607 RVA: 0x002A4E6C File Offset: 0x002A306C
		public CE1028(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1028, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F60 RID: 32608 RVA: 0x002A4EEC File Offset: 0x002A30EC
		public override bool Start(Living living)
		{
			CE1028 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1028) as CE1028;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F61 RID: 32609 RVA: 0x00031819 File Offset: 0x0002FA19
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F62 RID: 32610 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F63 RID: 32611 RVA: 0x002A4F4C File Offset: 0x002A314C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 800;
				living.SyncAtTime = true;
				living.AddBlood(this.m_added);
				living.SyncAtTime = false;
				List<Living> list = living.Game.Map.FindAllNearestSameTeam(living.X, living.Y, 250.0, living);
				foreach (Living living2 in list)
				{
					living2.SyncAtTime = true;
					living2.AddBlood(this.m_added);
					living2.SyncAtTime = false;
				}
			}
		}

		// Token: 0x06007F64 RID: 32612 RVA: 0x00031842 File Offset: 0x0002FA42
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E5D RID: 20061
		private int m_type = 0;

		// Token: 0x04004E5E RID: 20062
		private int m_count = 0;

		// Token: 0x04004E5F RID: 20063
		private int m_probability = 0;

		// Token: 0x04004E60 RID: 20064
		private int m_delay = 0;

		// Token: 0x04004E61 RID: 20065
		private int m_coldDown = 0;

		// Token: 0x04004E62 RID: 20066
		private int m_currentId;

		// Token: 0x04004E63 RID: 20067
		private int m_added = 0;
	}
}
