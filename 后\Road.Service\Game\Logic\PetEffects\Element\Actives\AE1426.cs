﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E2C RID: 3628
	public class AE1426 : BasePetEffect
	{
		// Token: 0x06007E9D RID: 32413 RVA: 0x002A12EC File Offset: 0x0029F4EC
		public AE1426(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1426, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E9E RID: 32414 RVA: 0x002A136C File Offset: 0x0029F56C
		public override bool Start(Living living)
		{
			AE1426 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1426) as AE1426;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E9F RID: 32415 RVA: 0x00031161 File Offset: 0x0002F361
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EA0 RID: 32416 RVA: 0x00031177 File Offset: 0x0002F377
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EA1 RID: 32417 RVA: 0x002A13CC File Offset: 0x0029F5CC
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.AddPetEffect(new CE1426(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004D6C RID: 19820
		private int m_type = 0;

		// Token: 0x04004D6D RID: 19821
		private int m_count = 0;

		// Token: 0x04004D6E RID: 19822
		private int m_probability = 0;

		// Token: 0x04004D6F RID: 19823
		private int m_delay = 0;

		// Token: 0x04004D70 RID: 19824
		private int m_coldDown = 0;

		// Token: 0x04004D71 RID: 19825
		private int m_currentId;

		// Token: 0x04004D72 RID: 19826
		private int m_added = 0;
	}
}
