﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E7C RID: 3708
	public class CE1170 : BasePetEffect
	{
		// Token: 0x06008079 RID: 32889 RVA: 0x002A931C File Offset: 0x002A751C
		public CE1170(int count, int probability, int type, int skillId, int delay, string elementID, Living source)
			: base(ePetEffectType.CE1170, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
			this.m_source = source;
		}

		// Token: 0x0600807A RID: 32890 RVA: 0x002A93A4 File Offset: 0x002A75A4
		public override bool Start(Living living)
		{
			CE1170 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1170) as CE1170;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600807B RID: 32891 RVA: 0x00032288 File Offset: 0x00030488
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600807C RID: 32892 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600807D RID: 32893 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginNextTurn(Living living)
		{
		}

		// Token: 0x0600807E RID: 32894 RVA: 0x002A9404 File Offset: 0x002A7604
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 800;
				living.AddBlood(-this.m_added, 1);
				bool flag2 = living.Blood <= 0;
				if (flag2)
				{
					living.Die();
					bool flag3 = this.m_source != null && this.m_source is Player;
					if (flag3)
					{
						(this.m_source as Player).PlayerDetail.OnKillingLiving(this.m_source.Game, 2, living.Id, living.IsLiving, this.m_added);
					}
				}
			}
		}

		// Token: 0x0600807F RID: 32895 RVA: 0x000322C4 File Offset: 0x000304C4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004F9D RID: 20381
		private int m_type = 0;

		// Token: 0x04004F9E RID: 20382
		private int m_count = 0;

		// Token: 0x04004F9F RID: 20383
		private int m_probability = 0;

		// Token: 0x04004FA0 RID: 20384
		private int m_delay = 0;

		// Token: 0x04004FA1 RID: 20385
		private int m_coldDown = 0;

		// Token: 0x04004FA2 RID: 20386
		private int m_currentId;

		// Token: 0x04004FA3 RID: 20387
		private int m_added = 0;

		// Token: 0x04004FA4 RID: 20388
		private Living m_source;
	}
}
