﻿using System;
using Game.Logic.Effects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CC9 RID: 3273
	[SpellAttibute(30)]
	internal class SealSpell : ISpellHandler
	{
		// Token: 0x06007514 RID: 29972 RVA: 0x0026E9B8 File Offset: 0x0026CBB8
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				new SealEffect(item.Property3, 1).Start(player);
			}
			else
			{
				bool flag = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
				if (flag)
				{
					new SealEffect(item.Property3, 1).Start(game.CurrentLiving);
				}
			}
		}
	}
}
