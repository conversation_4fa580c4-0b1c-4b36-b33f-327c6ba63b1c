﻿using System;
using System.Collections.Generic;
using System.Drawing;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E30 RID: 3632
	public class AE1437 : BasePetEffect
	{
		// Token: 0x06007EB1 RID: 32433 RVA: 0x002A1918 File Offset: 0x0029FB18
		public AE1437(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1437, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007EB2 RID: 32434 RVA: 0x002A19A0 File Offset: 0x0029FBA0
		public override bool Start(Living living)
		{
			AE1437 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1437) as AE1437;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007EB3 RID: 32435 RVA: 0x00031211 File Offset: 0x0002F411
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EB4 RID: 32436 RVA: 0x00031227 File Offset: 0x0002F427
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EB5 RID: 32437 RVA: 0x002A1A00 File Offset: 0x0029FC00
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				int num = this.m_living.X - this.BaseDistant;
				int num2 = this.m_living.X + this.BaseDistant;
				int backroundWidht = this.m_living.Game.Map.Info.BackroundWidht;
				this.m_fx = ((num > 0) ? num : 0);
				this.m_tx = ((num2 >= backroundWidht) ? backroundWidht : num2);
				bool flag2 = this.m_fx == 0;
				if (flag2)
				{
					this.m_tx += Math.Abs(num);
				}
				bool flag3 = this.m_tx == backroundWidht;
				if (flag3)
				{
					this.m_fx -= Math.Abs(num2 - backroundWidht);
				}
				List<Living> list = new List<Living>();
				bool flag4 = this.m_living.Game is PVPGame;
				if (flag4)
				{
					List<Player> allEnemyPlayers = this.m_living.Game.GetAllEnemyPlayers(this.m_living);
					list = this.m_living.Game.Map.FindRandomPlayer(this.m_fx, this.m_tx, allEnemyPlayers);
				}
				else
				{
					list = this.m_living.Game.Map.FindRandomLiving(this.m_fx, this.m_tx);
				}
				int count = list.Count;
				for (int i = 0; i < 5; i++)
				{
					GSPacketIn gspacketIn = new GSPacketIn(91, this.m_living.Id);
					gspacketIn.Parameter1 = this.m_living.Id;
					gspacketIn.WriteByte(61);
					gspacketIn.WriteInt(count);
					this.m_living.SyncAtTime = false;
					try
					{
						foreach (Living living in list)
						{
							int num3 = 0;
							living.SyncAtTime = false;
							living.IsFrost = false;
							living.IsHide = false;
							player.Game.SendGameUpdateFrozenState(living);
							player.Game.SendGameUpdateHideState(living);
							int num4 = this.MakeDamage(living);
							num4 += num4 * 50 / 100;
							int num5 = this.MakeCriticalDamage(living, num4);
							int num6 = 0;
							bool flag5 = living is Player;
							if (flag5)
							{
								living.OnTakedDamage(living, ref num4, ref num5);
							}
							bool flag6 = living.TakeDamage(this.m_living, ref num4, ref num5, "范围攻击");
							if (flag6)
							{
								num6 = num4 + num5;
								bool flag7 = living is Player;
								if (flag7)
								{
									num3 = (living as Player).Dander;
								}
							}
							gspacketIn.WriteInt(living.Id);
							gspacketIn.WriteInt(num6);
							gspacketIn.WriteInt(living.Blood);
							gspacketIn.WriteInt(num3);
							gspacketIn.WriteInt(1);
						}
						player.Game.SendToAll(gspacketIn);
					}
					finally
					{
						this.m_living.SyncAtTime = true;
						foreach (Living living2 in list)
						{
							living2.SyncAtTime = true;
						}
					}
				}
				foreach (Living living3 in list)
				{
					bool isLiving = living3.IsLiving;
					if (!isLiving)
					{
						List<Player> allTeamPlayers = this.m_living.Game.GetAllTeamPlayers(this.m_living);
						foreach (Player player2 in allTeamPlayers)
						{
							player2.SyncAtTime = true;
							player2.AddBlood(player2.MaxBlood * 5 / 100);
							player2.SyncAtTime = false;
						}
					}
				}
			}
		}

		// Token: 0x06007EB6 RID: 32438 RVA: 0x002A1E8C File Offset: 0x002A008C
		private int MakeDamage(Living p)
		{
			double baseDamage = this.m_living.BaseDamage;
			double num = p.BaseGuard;
			double num2 = p.Defence;
			double attack = this.m_living.Attack;
			bool flag = p.AddArmor && (p as Player).DeputyWeapon != null;
			if (flag)
			{
				int num3 = (p as Player).DeputyWeapon.Template.Property7 + (int)Math.Pow(1.1, (double)(p as Player).DeputyWeapon.StrengthenLevel);
				num += (double)num3;
				num2 += (double)num3;
			}
			bool ignoreArmor = this.m_living.IgnoreArmor;
			if (ignoreArmor)
			{
				num = 0.0;
				num2 = 0.0;
			}
			float currentDamagePlus = this.m_living.CurrentDamagePlus;
			float currentShootMinus = this.m_living.CurrentShootMinus;
			double num4 = 0.95 * (num - (double)(3 * this.m_living.Grade)) / (500.0 + num - (double)(3 * this.m_living.Grade));
			double num5 = ((num2 - this.m_living.Lucky >= 0.0) ? (0.95 * (num2 - this.m_living.Lucky) / (600.0 + num2 - this.m_living.Lucky)) : 0.0);
			double num6 = baseDamage * (1.0 + attack * 0.001) * (1.0 - (num4 + num5 - num4 * num5)) * (double)currentDamagePlus * (double)currentShootMinus;
			Rectangle directDemageRect = p.GetDirectDemageRect();
			double num7 = Math.Sqrt((double)((directDemageRect.X - this.m_living.X) * (directDemageRect.X - this.m_living.X) + (directDemageRect.Y - this.m_living.Y) * (directDemageRect.Y - this.m_living.Y)));
			num6 *= 1.0 - num7 / (double)Math.Abs(this.m_tx - this.m_fx) / 4.0;
			bool flag2 = num6 < 0.0;
			int num8;
			if (flag2)
			{
				num8 = 1;
			}
			else
			{
				num8 = (int)num6;
			}
			return num8;
		}

		// Token: 0x06007EB7 RID: 32439 RVA: 0x002A20E4 File Offset: 0x002A02E4
		private int MakeCriticalDamage(Living p, int baseDamage)
		{
			double lucky = this.m_living.Lucky;
			Random random = new Random();
			bool flag = 75000.0 * lucky / (lucky + 800.0) > (double)random.Next(100000);
			int num2;
			if (flag)
			{
				int num = (int)((0.5 + lucky * 0.0003) * (double)baseDamage);
				num2 = num * 50 / 100;
			}
			else
			{
				num2 = 0;
			}
			return num2;
		}

		// Token: 0x04004D88 RID: 19848
		private int m_type = 0;

		// Token: 0x04004D89 RID: 19849
		private int m_count = 0;

		// Token: 0x04004D8A RID: 19850
		private int m_probability = 0;

		// Token: 0x04004D8B RID: 19851
		private int m_delay = 0;

		// Token: 0x04004D8C RID: 19852
		private int m_coldDown = 0;

		// Token: 0x04004D8D RID: 19853
		private int m_currentId;

		// Token: 0x04004D8E RID: 19854
		private int m_added = 0;

		// Token: 0x04004D8F RID: 19855
		private int m_fx;

		// Token: 0x04004D90 RID: 19856
		private int m_tx;

		// Token: 0x04004D91 RID: 19857
		private int BaseDistant = 500;
	}
}
