﻿using System;

namespace Game.Base.Config
{
	// Token: 0x02000F96 RID: 3990
	[AttributeUsage(AttributeTargets.Field, AllowMultiple = false)]
	public class ConfigPropertyAttribute : Attribute
	{
		// Token: 0x170014D6 RID: 5334
		// (get) Token: 0x060087B9 RID: 34745 RVA: 0x00035FF0 File Offset: 0x000341F0
		public string Key
		{
			get
			{
				return this.m_key;
			}
		}

		// Token: 0x170014D7 RID: 5335
		// (get) Token: 0x060087BA RID: 34746 RVA: 0x00035FF8 File Offset: 0x000341F8
		public string Description
		{
			get
			{
				return this.m_description;
			}
		}

		// Token: 0x170014D8 RID: 5336
		// (get) Token: 0x060087BB RID: 34747 RVA: 0x00036000 File Offset: 0x00034200
		public object DefaultValue
		{
			get
			{
				return this.m_defaultValue;
			}
		}

		// Token: 0x060087BC RID: 34748 RVA: 0x00036008 File Offset: 0x00034208
		public ConfigPropertyAttribute(string key, string description, object defaultValue)
		{
			this.m_key = key;
			this.m_description = description;
			this.m_defaultValue = defaultValue;
		}

		// Token: 0x040053BF RID: 21439
		private string m_key;

		// Token: 0x040053C0 RID: 21440
		private string m_description;

		// Token: 0x040053C1 RID: 21441
		private object m_defaultValue;
	}
}
