﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E87 RID: 3719
	public class CE1181 : BasePetEffect
	{
		// Token: 0x1700149C RID: 5276
		// (get) Token: 0x060080C1 RID: 32961 RVA: 0x000325DC File Offset: 0x000307DC
		public int Count
		{
			get
			{
				return this.m_count;
			}
		}

		// Token: 0x060080C2 RID: 32962 RVA: 0x002AA3F8 File Offset: 0x002A85F8
		public CE1181(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1181, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060080C3 RID: 32963 RVA: 0x002AA478 File Offset: 0x002A8678
		public override bool Start(Living living)
		{
			CE1181 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1181) as CE1181;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080C4 RID: 32964 RVA: 0x002AA4D8 File Offset: 0x002A86D8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.BaseGuard += (double)this.m_added;
				player.Game.SendPetBuff(player, base.Info, true);
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x060080C5 RID: 32965 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x060080C6 RID: 32966 RVA: 0x002AA54C File Offset: 0x002A874C
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x060080C7 RID: 32967 RVA: 0x002AA580 File Offset: 0x002A8780
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BaseGuard -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004FEE RID: 20462
		private int m_type = 0;

		// Token: 0x04004FEF RID: 20463
		private int m_count = 0;

		// Token: 0x04004FF0 RID: 20464
		private int m_probability = 0;

		// Token: 0x04004FF1 RID: 20465
		private int m_delay = 0;

		// Token: 0x04004FF2 RID: 20466
		private int m_coldDown = 0;

		// Token: 0x04004FF3 RID: 20467
		private int m_currentId;

		// Token: 0x04004FF4 RID: 20468
		private int m_added = 0;
	}
}
