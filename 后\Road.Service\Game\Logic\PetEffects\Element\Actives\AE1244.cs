﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E16 RID: 3606
	public class AE1244 : BasePetEffect
	{
		// Token: 0x06007E23 RID: 32291 RVA: 0x0029F234 File Offset: 0x0029D434
		public AE1244(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1244, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E24 RID: 32292 RVA: 0x0029F2B4 File Offset: 0x0029D4B4
		public override bool Start(Living living)
		{
			AE1244 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1244) as AE1244;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E25 RID: 32293 RVA: 0x00030BD1 File Offset: 0x0002EDD1
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E26 RID: 32294 RVA: 0x00030BE7 File Offset: 0x0002EDE7
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E27 RID: 32295 RVA: 0x0029F314 File Offset: 0x0029D514
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && player.Game is PVPGame;
			if (flag)
			{
				player.AddPetEffect(new CE1244(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004CD2 RID: 19666
		private int m_type = 0;

		// Token: 0x04004CD3 RID: 19667
		private int m_count = 0;

		// Token: 0x04004CD4 RID: 19668
		private int m_probability = 0;

		// Token: 0x04004CD5 RID: 19669
		private int m_delay = 0;

		// Token: 0x04004CD6 RID: 19670
		private int m_coldDown = 0;

		// Token: 0x04004CD7 RID: 19671
		private int m_currentId;

		// Token: 0x04004CD8 RID: 19672
		private int m_added = 0;
	}
}
