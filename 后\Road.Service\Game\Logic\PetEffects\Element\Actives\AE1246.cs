﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E17 RID: 3607
	public class AE1246 : BasePetEffect
	{
		// Token: 0x06007E28 RID: 32296 RVA: 0x0029F388 File Offset: 0x0029D588
		public AE1246(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1246, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E29 RID: 32297 RVA: 0x0029F408 File Offset: 0x0029D608
		public override bool Start(Living living)
		{
			AE1246 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1246) as AE1246;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E2A RID: 32298 RVA: 0x00030BFD File Offset: 0x0002EDFD
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E2B RID: 32299 RVA: 0x00030C13 File Offset: 0x0002EE13
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E2C RID: 32300 RVA: 0x0029F468 File Offset: 0x0029D668
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1246(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004CD9 RID: 19673
		private int m_type = 0;

		// Token: 0x04004CDA RID: 19674
		private int m_count = 0;

		// Token: 0x04004CDB RID: 19675
		private int m_probability = 0;

		// Token: 0x04004CDC RID: 19676
		private int m_delay = 0;

		// Token: 0x04004CDD RID: 19677
		private int m_coldDown = 0;

		// Token: 0x04004CDE RID: 19678
		private int m_currentId;

		// Token: 0x04004CDF RID: 19679
		private int m_added = 0;
	}
}
