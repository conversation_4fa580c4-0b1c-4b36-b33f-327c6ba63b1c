﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DF1 RID: 3569
	public class AE1186 : BasePetEffect
	{
		// Token: 0x06007D64 RID: 32100 RVA: 0x0029BD08 File Offset: 0x00299F08
		public AE1186(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1186, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D65 RID: 32101 RVA: 0x0029BD88 File Offset: 0x00299F88
		public override bool Start(Living living)
		{
			AE1186 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1186) as AE1186;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D66 RID: 32102 RVA: 0x0003049A File Offset: 0x0002E69A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D67 RID: 32103 RVA: 0x000304B0 File Offset: 0x0002E6B0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D68 RID: 32104 RVA: 0x0029BDE8 File Offset: 0x00299FE8
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1186(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004BCF RID: 19407
		private int m_type = 0;

		// Token: 0x04004BD0 RID: 19408
		private int m_count = 0;

		// Token: 0x04004BD1 RID: 19409
		private int m_probability = 0;

		// Token: 0x04004BD2 RID: 19410
		private int m_delay = 0;

		// Token: 0x04004BD3 RID: 19411
		private int m_coldDown = 0;

		// Token: 0x04004BD4 RID: 19412
		private int m_currentId;

		// Token: 0x04004BD5 RID: 19413
		private int m_added = 0;
	}
}
