﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E1E RID: 3614
	public class AE1256 : BasePetEffect
	{
		// Token: 0x06007E4D RID: 32333 RVA: 0x0029FCA0 File Offset: 0x0029DEA0
		public AE1256(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1256, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E4E RID: 32334 RVA: 0x0029FD20 File Offset: 0x0029DF20
		public override bool Start(Living living)
		{
			AE1256 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1256) as AE1256;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E4F RID: 32335 RVA: 0x00030D7D File Offset: 0x0002EF7D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007E50 RID: 32336 RVA: 0x0029FD80 File Offset: 0x0029DF80
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007E51 RID: 32337 RVA: 0x0029FDB0 File Offset: 0x0029DFB0
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				this.IsTrigger = false;
				target.AddPetEffect(new CE1256(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007E52 RID: 32338 RVA: 0x00030DA6 File Offset: 0x0002EFA6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004D0A RID: 19722
		private int m_type = 0;

		// Token: 0x04004D0B RID: 19723
		private int m_count = 0;

		// Token: 0x04004D0C RID: 19724
		private int m_probability = 0;

		// Token: 0x04004D0D RID: 19725
		private int m_delay = 0;

		// Token: 0x04004D0E RID: 19726
		private int m_coldDown = 0;

		// Token: 0x04004D0F RID: 19727
		private int m_currentId;

		// Token: 0x04004D10 RID: 19728
		private int m_added = 0;
	}
}
