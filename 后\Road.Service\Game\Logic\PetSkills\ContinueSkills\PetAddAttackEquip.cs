﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000CFF RID: 3327
	public class PetAddAttackEquip : AbstractPetEffect
	{
		// Token: 0x06007857 RID: 30807 RVA: 0x002853A4 File Offset: 0x002835A4
		public PetAddAttackEquip(int count, string elementID)
			: base(ePetEffectType.PetAddAttackEquip, elementID)
		{
			this.m_count = count;
			bool flag = elementID == null;
			if (!flag)
			{
				int length = elementID.Length;
				bool flag2 = length != 4;
				if (!flag2)
				{
					switch (elementID[3])
					{
					case '0':
					{
						bool flag3 = !(elementID == "1180");
						if (flag3)
						{
							bool flag4 = elementID == "1400";
							if (flag4)
							{
								this.m_value = 8.0;
								this.m_count = 3;
								this.m_percent = true;
							}
						}
						else
						{
							this.m_value = 500.0;
						}
						return;
					}
					case '1':
					case '3':
					case '6':
					case '7':
						return;
					case '2':
					{
						bool flag5 = elementID == "1482";
						if (flag5)
						{
							this.m_value = 6.0;
							this.m_percent = true;
						}
						return;
					}
					case '4':
					{
						bool flag6 = elementID == "1194";
						if (flag6)
						{
							this.m_value = 200.0;
						}
						return;
					}
					case '5':
					{
						bool flag7 = !(elementID == "1195");
						if (flag7)
						{
							return;
						}
						break;
					}
					case '8':
					{
						bool flag8 = elementID == "1178";
						if (flag8)
						{
							this.m_value = 100.0;
						}
						return;
					}
					case '9':
						if (elementID == "1489")
						{
							this.m_value = 5.0;
							this.m_percent = true;
							return;
						}
						if (elementID == "1399")
						{
							this.m_value = 5.0;
							this.m_percent = true;
							return;
						}
						if (elementID == "4559")
						{
							this.m_value = 25.0;
							this.m_count = 3;
							this.m_percent = true;
							return;
						}
						if (!(elementID == "1179"))
						{
							return;
						}
						break;
					default:
						return;
					}
					this.m_value = 300.0;
				}
			}
		}

		// Token: 0x06007858 RID: 30808 RVA: 0x002855DC File Offset: 0x002837DC
		public override bool Start(Living living)
		{
			PetAddAttackEquip petAddAttackEquip = living.PetEffectList.GetOfType(ePetEffectType.PetAddAttackEquip) as PetAddAttackEquip;
			bool flag = petAddAttackEquip != null;
			bool flag2;
			if (flag)
			{
				petAddAttackEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007859 RID: 30809 RVA: 0x00285624 File Offset: 0x00283824
		public override void OnAttached(Living living)
		{
			bool flag = this.m_added == 0.0;
			if (flag)
			{
				bool percent = this.m_percent;
				if (percent)
				{
					this.m_added = living.Attack * this.m_value / 100.0;
				}
				else
				{
					this.m_added = this.m_value;
				}
				living.Attack += this.m_added;
			}
			living.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x0600785A RID: 30810 RVA: 0x0002C96F File Offset: 0x0002AB6F
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x0600785B RID: 30811 RVA: 0x002856A8 File Offset: 0x002838A8
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count <= 0;
			if (flag)
			{
				living.Attack -= this.m_added;
				this.m_added = 0.0;
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				living.Game.SendPlayerPicture(living, 29, false);
				this.Stop();
			}
		}

		// Token: 0x04004689 RID: 18057
		private int m_count;

		// Token: 0x0400468A RID: 18058
		private double m_value;

		// Token: 0x0400468B RID: 18059
		private bool m_percent;

		// Token: 0x0400468C RID: 18060
		private double m_added;
	}
}
