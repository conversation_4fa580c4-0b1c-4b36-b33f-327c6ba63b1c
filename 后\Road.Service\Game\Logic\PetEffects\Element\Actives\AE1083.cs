﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DB8 RID: 3512
	public class AE1083 : BasePetEffect
	{
		// Token: 0x06007C39 RID: 31801 RVA: 0x00296680 File Offset: 0x00294880
		public AE1083(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1083, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C3A RID: 31802 RVA: 0x00296700 File Offset: 0x00294900
		public override bool Start(Living living)
		{
			AE1083 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1083) as AE1083;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C3B RID: 31803 RVA: 0x0002F92A File Offset: 0x0002DB2A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C3C RID: 31804 RVA: 0x00296760 File Offset: 0x00294960
		protected override void OnPausedOnPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Attack -= (double)this.m_added;
			this.IsTrigger = false;
		}

		// Token: 0x06007C3D RID: 31805 RVA: 0x002967B0 File Offset: 0x002949B0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && !this.IsTrigger;
			if (flag)
			{
				this.IsTrigger = true;
				this.m_added = 500;
				player.Attack += (double)this.m_added;
				player.Game.SendPetBuff(player, base.ElementInfo, true);
			}
		}

		// Token: 0x04004A42 RID: 19010
		private int m_type = 0;

		// Token: 0x04004A43 RID: 19011
		private int m_count = 0;

		// Token: 0x04004A44 RID: 19012
		private int m_probability = 0;

		// Token: 0x04004A45 RID: 19013
		private int m_delay = 0;

		// Token: 0x04004A46 RID: 19014
		private int m_coldDown = 0;

		// Token: 0x04004A47 RID: 19015
		private int m_currentId;

		// Token: 0x04004A48 RID: 19016
		private int m_added = 0;
	}
}
