﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D5C RID: 3420
	public class PE1078 : BasePetEffect
	{
		// Token: 0x06007A62 RID: 31330 RVA: 0x0028EA8C File Offset: 0x0028CC8C
		public PE1078(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1078, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A63 RID: 31331 RVA: 0x0028EB0C File Offset: 0x0028CD0C
		public override bool Start(Living living)
		{
			PE1078 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1078) as PE1078;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A64 RID: 31332 RVA: 0x0002E6D2 File Offset: 0x0002C8D2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerUseSecondWeapon += this.Player_PlayerUseSecondWeapon;
		}

		// Token: 0x06007A65 RID: 31333 RVA: 0x0028EB6C File Offset: 0x0028CD6C
		private void Player_PlayerUseSecondWeapon(Player player, int value)
		{
			bool flag = value == 31;
			if (flag)
			{
				this.m_added = 500;
				player.SyncAtTime = true;
				player.AddBlood(this.m_added);
				player.SyncAtTime = false;
			}
		}

		// Token: 0x06007A66 RID: 31334 RVA: 0x0002E6E8 File Offset: 0x0002C8E8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerUseSecondWeapon -= this.Player_PlayerUseSecondWeapon;
		}

		// Token: 0x040047C4 RID: 18372
		private int m_type = 0;

		// Token: 0x040047C5 RID: 18373
		private int m_count = 0;

		// Token: 0x040047C6 RID: 18374
		private int m_probability = 0;

		// Token: 0x040047C7 RID: 18375
		private int m_delay = 0;

		// Token: 0x040047C8 RID: 18376
		private int m_coldDown = 0;

		// Token: 0x040047C9 RID: 18377
		private int m_currentId;

		// Token: 0x040047CA RID: 18378
		private int m_added = 0;
	}
}
