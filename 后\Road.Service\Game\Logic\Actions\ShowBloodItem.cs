﻿using System;

namespace Game.Logic.Actions
{
	// Token: 0x02000F69 RID: 3945
	public class ShowBloodItem : BaseAction
	{
		// Token: 0x0600852F RID: 34095 RVA: 0x000356CD File Offset: 0x000338CD
		public ShowBloodItem(int livingId, int delay, int finishTime)
			: base(delay, finishTime)
		{
			this.m_livingId = livingId;
		}

		// Token: 0x06008530 RID: 34096 RVA: 0x000356E0 File Offset: 0x000338E0
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			game.ShowBloodItem(this.m_livingId);
			base.Finish(tick);
		}

		// Token: 0x0400533C RID: 21308
		private int m_livingId;
	}
}
