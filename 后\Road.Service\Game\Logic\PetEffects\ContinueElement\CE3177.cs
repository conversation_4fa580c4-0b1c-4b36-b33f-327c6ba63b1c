﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EC4 RID: 3780
	public class CE3177 : BasePetEffect
	{
		// Token: 0x0600824C RID: 33356 RVA: 0x002B0404 File Offset: 0x002AE604
		public CE3177(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE3177, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600824D RID: 33357 RVA: 0x002B0480 File Offset: 0x002AE680
		public override bool Start(Living living)
		{
			CE3177 ce = living.PetEffectList.GetOfType(ePetEffectType.CE3177) as CE3177;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600824E RID: 33358 RVA: 0x000333C9 File Offset: 0x000315C9
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PetEffects.SkipCritical = 20;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x0600824F RID: 33359 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008250 RID: 33360 RVA: 0x002B04DC File Offset: 0x002AE6DC
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.PetEffects.SkipCritical = 0;
				living.Game.SendPetBuff(living, base.Info, false);
				this.Stop();
			}
		}

		// Token: 0x06008251 RID: 33361 RVA: 0x000333FF File Offset: 0x000315FF
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x0400519A RID: 20890
		private int m_type = 0;

		// Token: 0x0400519B RID: 20891
		private int m_count = 0;

		// Token: 0x0400519C RID: 20892
		private int m_probability = 0;

		// Token: 0x0400519D RID: 20893
		private int m_delay = 0;

		// Token: 0x0400519E RID: 20894
		private int m_coldDown = 0;

		// Token: 0x0400519F RID: 20895
		private int m_currentId;

		// Token: 0x040051A0 RID: 20896
		private int m_added = 0;
	}
}
