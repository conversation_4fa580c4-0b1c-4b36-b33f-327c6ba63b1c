﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DD7 RID: 3543
	public class AE1138 : BasePetEffect
	{
		// Token: 0x06007CDE RID: 31966 RVA: 0x0029947C File Offset: 0x0029767C
		public AE1138(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1138, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CDF RID: 31967 RVA: 0x002994FC File Offset: 0x002976FC
		public override bool Start(Living living)
		{
			AE1138 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1138) as AE1138;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CE0 RID: 31968 RVA: 0x0002FF8A File Offset: 0x0002E18A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007CE1 RID: 31969 RVA: 0x0002FFB3 File Offset: 0x0002E1B3
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007CE2 RID: 31970 RVA: 0x0029955C File Offset: 0x0029775C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.m_added = 100;
				player.YHM_UseSkillPetWithProp = true;
				player.PetEffects.CritRate += this.m_added;
			}
		}

		// Token: 0x06007CE3 RID: 31971 RVA: 0x002995AC File Offset: 0x002977AC
		private void player_AfterPlayerShooted(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.PetEffects.CritRate -= this.m_added;
				this.m_added = 0;
			}
		}

		// Token: 0x04004B19 RID: 19225
		private int m_type = 0;

		// Token: 0x04004B1A RID: 19226
		private int m_count = 0;

		// Token: 0x04004B1B RID: 19227
		private int m_probability = 0;

		// Token: 0x04004B1C RID: 19228
		private int m_delay = 0;

		// Token: 0x04004B1D RID: 19229
		private int m_coldDown = 0;

		// Token: 0x04004B1E RID: 19230
		private int m_currentId;

		// Token: 0x04004B1F RID: 19231
		private int m_added = 0;
	}
}
