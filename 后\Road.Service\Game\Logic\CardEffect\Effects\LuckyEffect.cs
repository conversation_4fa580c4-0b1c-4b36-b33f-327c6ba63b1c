﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F31 RID: 3889
	public class LuckyEffect : BaseCardEffect
	{
		// Token: 0x06008459 RID: 33881 RVA: 0x002B7BFC File Offset: 0x002B5DFC
		public LuckyEffect(int index, CardBuffInfo info)
			: base(eCardEffectType.LuckyDeck, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x0600845A RID: 33882 RVA: 0x002B7C6C File Offset: 0x002B5E6C
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.LuckyDeck) is LuckyEffect;
			return flag || base.Start(living);
		}

		// Token: 0x0600845B RID: 33883 RVA: 0x00034DFE File Offset: 0x00032FFE
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x0600845C RID: 33884 RVA: 0x00034E14 File Offset: 0x00033014
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x0600845D RID: 33885 RVA: 0x002B7CA4 File Offset: 0x002B5EA4
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.Attack -= (double)this.m_added;
				player.Agility -= (double)this.m_added;
				player.Lucky -= (double)this.m_added;
				player.Defence -= (double)this.m_added;
				this.m_added = 0;
			}
			bool flag2 = player.Game is PVPGame && (player.Game as PVPGame).IsMatchOrFreedom();
			if (flag2)
			{
				this.m_added = this.m_value;
				player.Attack += (double)this.m_added;
				player.Agility += (double)this.m_added;
				player.Lucky += (double)this.m_added;
				player.Defence += (double)this.m_added;
			}
		}

		// Token: 0x04005287 RID: 21127
		private int m_indexValue = 0;

		// Token: 0x04005288 RID: 21128
		private int m_value = 0;

		// Token: 0x04005289 RID: 21129
		private int m_added = 0;
	}
}
