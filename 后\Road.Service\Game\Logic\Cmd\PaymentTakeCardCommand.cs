﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F0D RID: 3853
	[GameCommand(114, "付费翻牌")]
	public class PaymentTakeCardCommand : ICommandHandler
	{
		// Token: 0x060083B3 RID: 33715 RVA: 0x002B5354 File Offset: 0x002B3554
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool flag = !player.FinishTakeCard || player.CanTakeOut != 0 || player.HasPaymentTakeCard;
			if (!flag)
			{
				bool flag2 = player.GetFightBuffByType(eBuffType.Card_Get) != null && !game.IsSpecialPVE() && player.PlayerDetail.UsePayBuff(eBuffType.Card_Get);
				bool flag3;
				if (flag2)
				{
					flag3 = true;
				}
				else
				{
					int num = ((player.PlayerDetail.PlayerCharacter.TypeVIP > 0) ? 437 : 486);
					flag3 = player.PlayerDetail.RemoveMoney(num) > 0;
				}
				bool flag4 = flag3;
				if (flag4)
				{
					int num2 = (int)packet.ReadByte();
					player.CanTakeOut++;
					player.FinishTakeCard = false;
					player.HasPaymentTakeCard = true;
					player.PlayerDetail.LogAddMoney(AddMoneyType.Game, AddMoneyType.Game_PaymentTakeCard, player.PlayerDetail.PlayerCharacter.ID, 100, player.PlayerDetail.PlayerCharacter.Money);
					bool flag5 = num2 < 0 || num2 > game.Cards.Length;
					if (flag5)
					{
						game.TakeCard(player, false);
					}
					else
					{
						game.TakeCard(player, num2, false);
					}
				}
				else
				{
					player.PlayerDetail.SendInsufficientMoney(1);
				}
			}
		}
	}
}
