﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E4F RID: 3663
	public class CE1032 : BasePetEffect
	{
		// Token: 0x06007F65 RID: 32613 RVA: 0x002A502C File Offset: 0x002A322C
		public CE1032(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1032, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F66 RID: 32614 RVA: 0x002A50A8 File Offset: 0x002A32A8
		public override bool Start(Living living)
		{
			CE1032 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1032) as CE1032;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F67 RID: 32615 RVA: 0x0003186C File Offset: 0x0002FA6C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.BeforeTakeDamage += this.Player_BeforeTakeDamage;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F68 RID: 32616 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F69 RID: 32617 RVA: 0x002A5104 File Offset: 0x002A3304
		private void Player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			this.m_added = 100;
			damageAmount -= this.m_added;
			bool flag = damageAmount < 0;
			if (flag)
			{
				damageAmount = 1;
			}
		}

		// Token: 0x06007F6A RID: 32618 RVA: 0x002A5134 File Offset: 0x002A3334
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F6B RID: 32619 RVA: 0x000318A8 File Offset: 0x0002FAA8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.BeforeTakeDamage -= this.Player_BeforeTakeDamage;
		}

		// Token: 0x04004E64 RID: 20068
		private int m_type = 0;

		// Token: 0x04004E65 RID: 20069
		private int m_count = 0;

		// Token: 0x04004E66 RID: 20070
		private int m_probability = 0;

		// Token: 0x04004E67 RID: 20071
		private int m_delay = 0;

		// Token: 0x04004E68 RID: 20072
		private int m_coldDown = 0;

		// Token: 0x04004E69 RID: 20073
		private int m_currentId;

		// Token: 0x04004E6A RID: 20074
		private int m_added = 0;
	}
}
