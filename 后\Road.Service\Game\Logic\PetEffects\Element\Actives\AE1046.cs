﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DA8 RID: 3496
	public class AE1046 : BasePetEffect
	{
		// Token: 0x06007BE8 RID: 31720 RVA: 0x00295014 File Offset: 0x00293214
		public AE1046(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1046, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BE9 RID: 31721 RVA: 0x00295094 File Offset: 0x00293294
		public override bool Start(Living living)
		{
			AE1046 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1046) as AE1046;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BEA RID: 31722 RVA: 0x0002F657 File Offset: 0x0002D857
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BEB RID: 31723 RVA: 0x0002F66D File Offset: 0x0002D86D
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007BEC RID: 31724 RVA: 0x002950F4 File Offset: 0x002932F4
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1046(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x040049D2 RID: 18898
		private int m_type = 0;

		// Token: 0x040049D3 RID: 18899
		private int m_count = 0;

		// Token: 0x040049D4 RID: 18900
		private int m_probability = 0;

		// Token: 0x040049D5 RID: 18901
		private int m_delay = 0;

		// Token: 0x040049D6 RID: 18902
		private int m_coldDown = 0;

		// Token: 0x040049D7 RID: 18903
		private int m_currentId;

		// Token: 0x040049D8 RID: 18904
		private int m_added = 0;
	}
}
