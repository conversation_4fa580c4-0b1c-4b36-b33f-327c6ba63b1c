﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FD4 RID: 4052
	public class FightSpiritTemplateMgr
	{
		// Token: 0x06008AC3 RID: 35523 RVA: 0x002F85F8 File Offset: 0x002F67F8
		public static bool ReLoad()
		{
			try
			{
				FightSpiritTemplateInfo[] array = FightSpiritTemplateMgr.LoadFightSpiritTemplateDb();
				Dictionary<int, List<FightSpiritTemplateInfo>> dictionary = FightSpiritTemplateMgr.LoadFightSpiritTemplates(array);
				bool flag = array.Length != 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, List<FightSpiritTemplateInfo>>>(ref FightSpiritTemplateMgr.m_fightSpiritTemplates, dictionary);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = FightSpiritTemplateMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					FightSpiritTemplateMgr.log.Error("ReLoad FightSpiritTemplate", ex);
				}
				return false;
			}
			return true;
		}

		// Token: 0x06008AC4 RID: 35524 RVA: 0x002F8670 File Offset: 0x002F6870
		public static bool Init()
		{
			return FightSpiritTemplateMgr.ReLoad();
		}

		// Token: 0x06008AC5 RID: 35525 RVA: 0x002F8688 File Offset: 0x002F6888
		public static FightSpiritTemplateInfo[] LoadFightSpiritTemplateDb()
		{
			FightSpiritTemplateInfo[] allFightSpiritTemplate;
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				allFightSpiritTemplate = produceBussiness.GetAllFightSpiritTemplate();
			}
			return allFightSpiritTemplate;
		}

		// Token: 0x06008AC6 RID: 35526 RVA: 0x002F86C4 File Offset: 0x002F68C4
		public static Dictionary<int, List<FightSpiritTemplateInfo>> LoadFightSpiritTemplates(FightSpiritTemplateInfo[] infos)
		{
			Dictionary<int, List<FightSpiritTemplateInfo>> dictionary = new Dictionary<int, List<FightSpiritTemplateInfo>>();
			for (int i = 0; i < infos.Length; i++)
			{
				FightSpiritTemplateInfo info = infos[i];
				bool flag = !dictionary.Keys.Contains(info.FightSpiritID);
				if (flag)
				{
					IEnumerable<FightSpiritTemplateInfo> enumerable = infos.Where((FightSpiritTemplateInfo s) => s.FightSpiritID == info.FightSpiritID);
					dictionary.Add(info.FightSpiritID, enumerable.ToList<FightSpiritTemplateInfo>());
				}
			}
			return dictionary;
		}

		// Token: 0x06008AC7 RID: 35527 RVA: 0x002F8750 File Offset: 0x002F6950
		public static List<FightSpiritTemplateInfo> FindFightSpiritTemplates(int id)
		{
			FightSpiritTemplateMgr.m_clientLocker.AcquireWriterLock(-1);
			try
			{
				bool flag = FightSpiritTemplateMgr.m_fightSpiritTemplates.ContainsKey(id);
				if (flag)
				{
					return FightSpiritTemplateMgr.m_fightSpiritTemplates[id];
				}
			}
			finally
			{
				FightSpiritTemplateMgr.m_clientLocker.ReleaseWriterLock();
			}
			return new List<FightSpiritTemplateInfo>();
		}

		// Token: 0x06008AC8 RID: 35528 RVA: 0x002F87B4 File Offset: 0x002F69B4
		public static FightSpiritTemplateInfo FindFightSpiritTemplateInfo(int FigSpiritId, int lv)
		{
			foreach (FightSpiritTemplateInfo fightSpiritTemplateInfo in FightSpiritTemplateMgr.FindFightSpiritTemplates(FigSpiritId))
			{
				bool flag = fightSpiritTemplateInfo.Level == lv;
				if (flag)
				{
					return fightSpiritTemplateInfo;
				}
			}
			return null;
		}

		// Token: 0x06008AC9 RID: 35529 RVA: 0x002F881C File Offset: 0x002F6A1C
		public static int GOLDEN_LEVEL(int lv)
		{
			try
			{
				string fightSpiritLevelAddDamage = GameProperties.FightSpiritLevelAddDamage;
				char[] array = new char[] { '|' };
				string[] array2 = fightSpiritLevelAddDamage.Split(array);
				string[] array3 = array2;
				foreach (string text in array3)
				{
					bool flag = text.Split(new char[] { ',' })[0] == lv.ToString();
					if (flag)
					{
						return int.Parse(text.Split(new char[] { ',' })[1]);
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = FightSpiritTemplateMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					FightSpiritTemplateMgr.log.Error("FightSpiritTemplate.GOLDEN_LEVEL: ", ex);
				}
			}
			return 0;
		}

		// Token: 0x06008ACA RID: 35530 RVA: 0x002F88F0 File Offset: 0x002F6AF0
		public static int[] Exps()
		{
			List<FightSpiritTemplateInfo> list = FightSpiritTemplateMgr.FindFightSpiritTemplates(100001);
			List<int> list2 = new List<int>();
			foreach (FightSpiritTemplateInfo fightSpiritTemplateInfo in list)
			{
				list2.Add(fightSpiritTemplateInfo.Exp);
			}
			return list2.ToArray();
		}

		// Token: 0x06008ACB RID: 35531 RVA: 0x002F8968 File Offset: 0x002F6B68
		public static int GetProp(int figSpiritId, int lv, int place, ref int addAtt, ref int rdcDama)
		{
			FightSpiritTemplateInfo fightSpiritTemplateInfo = FightSpiritTemplateMgr.FindFightSpiritTemplateInfo(figSpiritId, lv);
			bool flag = fightSpiritTemplateInfo == null;
			if (flag)
			{
				List<FightSpiritTemplateInfo> list = FightSpiritTemplateMgr.FindFightSpiritTemplates(figSpiritId);
				bool flag2 = list.Count <= 0;
				if (flag2)
				{
					FightSpiritTemplateMgr.log.ErrorFormat("FigSpiritId: {0} not found! Return 0", figSpiritId);
					return 0;
				}
				fightSpiritTemplateInfo = list[list.Count - 1];
				FightSpiritTemplateMgr.log.ErrorFormat("FigSpiritId: {0}, level: {1} not found! Return Max level in database is {2}", figSpiritId, lv, fightSpiritTemplateInfo.Level);
			}
			bool flag3 = figSpiritId == 100001 || figSpiritId == 100003;
			if (flag3)
			{
				addAtt += FightSpiritTemplateMgr.GOLDEN_LEVEL(lv);
			}
			else
			{
				rdcDama += FightSpiritTemplateMgr.GOLDEN_LEVEL(lv);
			}
			if (!true)
			{
			}
			int num;
			switch (place)
			{
			case 2:
				num = fightSpiritTemplateInfo.Attack;
				goto IL_0120;
			case 3:
				num = fightSpiritTemplateInfo.Lucky;
				goto IL_0120;
			case 4:
				break;
			case 5:
				num = fightSpiritTemplateInfo.Agility;
				goto IL_0120;
			default:
				if (place == 11)
				{
					num = fightSpiritTemplateInfo.Defence;
					goto IL_0120;
				}
				if (place == 13)
				{
					num = fightSpiritTemplateInfo.Lucky;
					goto IL_0120;
				}
				break;
			}
			num = 0;
			IL_0120:
			if (!true)
			{
			}
			return num;
		}

		// Token: 0x04005500 RID: 21760
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04005501 RID: 21761
		private static Dictionary<int, List<FightSpiritTemplateInfo>> m_fightSpiritTemplates = new Dictionary<int, List<FightSpiritTemplateInfo>>();

		// Token: 0x04005502 RID: 21762
		private static ReaderWriterLock m_clientLocker = new ReaderWriterLock();
	}
}
