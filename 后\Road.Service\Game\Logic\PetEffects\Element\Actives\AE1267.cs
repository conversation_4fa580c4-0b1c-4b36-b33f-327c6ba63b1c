﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E24 RID: 3620
	public class AE1267 : BasePetEffect
	{
		// Token: 0x06007E6D RID: 32365 RVA: 0x002A06A8 File Offset: 0x0029E8A8
		public AE1267(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1267, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E6E RID: 32366 RVA: 0x002A0728 File Offset: 0x0029E928
		public override bool Start(Living living)
		{
			AE1267 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1267) as AE1267;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E6F RID: 32367 RVA: 0x00030ED1 File Offset: 0x0002F0D1
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007E70 RID: 32368 RVA: 0x002A0788 File Offset: 0x0029E988
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && player.Game is PVPGame;
			if (flag)
			{
				this.m_added = 20;
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007E71 RID: 32369 RVA: 0x002A07D0 File Offset: 0x0029E9D0
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				target.Game.SendPetBuff(target, base.ElementInfo, true);
				bool flag = (target as Player).PetMP < this.m_added;
				if (flag)
				{
					this.m_added = (target as Player).PetMP;
				}
				(target as Player).PetMP -= this.m_added;
				this.IsTrigger = false;
				this.m_added = 0;
				target.AddPetEffect(new CE1266(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007E72 RID: 32370 RVA: 0x00030EFA File Offset: 0x0002F0FA
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004D34 RID: 19764
		private int m_type = 0;

		// Token: 0x04004D35 RID: 19765
		private int m_count = 0;

		// Token: 0x04004D36 RID: 19766
		private int m_probability = 0;

		// Token: 0x04004D37 RID: 19767
		private int m_delay = 0;

		// Token: 0x04004D38 RID: 19768
		private int m_coldDown = 0;

		// Token: 0x04004D39 RID: 19769
		private int m_currentId;

		// Token: 0x04004D3A RID: 19770
		private int m_added = 0;
	}
}
