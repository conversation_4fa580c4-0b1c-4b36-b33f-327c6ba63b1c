﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E1D RID: 3613
	public class AE1255 : BasePetEffect
	{
		// Token: 0x06007E47 RID: 32327 RVA: 0x0029FB34 File Offset: 0x0029DD34
		public AE1255(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1255, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E48 RID: 32328 RVA: 0x0029FBB4 File Offset: 0x0029DDB4
		public override bool Start(Living living)
		{
			AE1255 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1255) as AE1255;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E49 RID: 32329 RVA: 0x00030D2B File Offset: 0x0002EF2B
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007E4A RID: 32330 RVA: 0x0029FC14 File Offset: 0x0029DE14
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007E4B RID: 32331 RVA: 0x0029FC44 File Offset: 0x0029DE44
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				this.IsTrigger = false;
				target.AddPetEffect(new CE1255(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007E4C RID: 32332 RVA: 0x00030D54 File Offset: 0x0002EF54
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004D03 RID: 19715
		private int m_type = 0;

		// Token: 0x04004D04 RID: 19716
		private int m_count = 0;

		// Token: 0x04004D05 RID: 19717
		private int m_probability = 0;

		// Token: 0x04004D06 RID: 19718
		private int m_delay = 0;

		// Token: 0x04004D07 RID: 19719
		private int m_coldDown = 0;

		// Token: 0x04004D08 RID: 19720
		private int m_currentId;

		// Token: 0x04004D09 RID: 19721
		private int m_added = 0;
	}
}
