﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DEA RID: 3562
	public class AE1179 : BasePetEffect
	{
		// Token: 0x06007D41 RID: 32065 RVA: 0x0029B0F8 File Offset: 0x002992F8
		public AE1179(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1179, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D42 RID: 32066 RVA: 0x0029B178 File Offset: 0x00299378
		public override bool Start(Living living)
		{
			AE1179 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1179) as AE1179;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D43 RID: 32067 RVA: 0x00030366 File Offset: 0x0002E566
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D44 RID: 32068 RVA: 0x0003037C File Offset: 0x0002E57C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007D45 RID: 32069 RVA: 0x0029B1D8 File Offset: 0x002993D8
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1179(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004B9E RID: 19358
		private int m_type = 0;

		// Token: 0x04004B9F RID: 19359
		private int m_count = 0;

		// Token: 0x04004BA0 RID: 19360
		private int m_probability = 0;

		// Token: 0x04004BA1 RID: 19361
		private int m_delay = 0;

		// Token: 0x04004BA2 RID: 19362
		private int m_coldDown = 0;

		// Token: 0x04004BA3 RID: 19363
		private int m_currentId;

		// Token: 0x04004BA4 RID: 19364
		private int m_added = 0;
	}
}
