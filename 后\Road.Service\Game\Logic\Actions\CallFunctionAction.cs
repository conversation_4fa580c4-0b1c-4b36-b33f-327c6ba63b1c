﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F45 RID: 3909
	public class CallFunctionAction : BaseAction
	{
		// Token: 0x060084DD RID: 34013 RVA: 0x000350E3 File Offset: 0x000332E3
		public CallFunctionAction(LivingCallBack func, int delay)
			: base(delay)
		{
			this.m_func = func;
		}

		// Token: 0x060084DE RID: 34014 RVA: 0x002B8DB4 File Offset: 0x002B6FB4
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			try
			{
				this.m_func();
			}
			finally
			{
				base.Finish(tick);
			}
		}

		// Token: 0x040052BF RID: 21183
		private LivingCallBack m_func;
	}
}
