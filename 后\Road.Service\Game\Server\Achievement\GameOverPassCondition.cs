﻿using System;
using Game.Logic;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Achievement
{
	// Token: 0x02000C68 RID: 3176
	public class GameOverPassCondition : BaseCondition
	{
		// Token: 0x06007095 RID: 28821 RVA: 0x0002A589 File Offset: 0x00028789
		public GameOverPassCondition(BaseAchievement quest, AchievementConditionInfo info, int type, int value)
			: base(quest, info, value)
		{
			this.m_type = type;
		}

		// Token: 0x06007096 RID: 28822 RVA: 0x0002A59E File Offset: 0x0002879E
		public override void AddTrigger(GamePlayer player)
		{
			player.MissionFullOver += this.player_PlayerMissionFullOverEventHandle;
		}

		// Token: 0x06007097 RID: 28823 RVA: 0x00250194 File Offset: 0x0024E394
		private void player_PlayerMissionFullOverEventHandle(AbstractGame game, int missionId, bool isWin, int turnNum)
		{
			bool flag = !isWin;
			if (!flag)
			{
				switch (this.m_type)
				{
				case 1:
				{
					bool flag2 = missionId == 6104 || missionId == 6204 || missionId == 6304;
					if (flag2)
					{
						int num = base.Value;
						base.Value = num + 1;
					}
					break;
				}
				case 2:
				{
					bool flag3 = missionId == 7004 || missionId == 7104 || missionId == 7204;
					if (flag3)
					{
						int num = base.Value;
						base.Value = num + 1;
					}
					break;
				}
				case 3:
				{
					bool flag4 = missionId == 3106 || missionId == 3206 || missionId == 3306;
					if (flag4)
					{
						int num = base.Value;
						base.Value = num + 1;
					}
					break;
				}
				case 4:
				{
					bool flag5 = missionId == 1073 || missionId == 1176 || missionId == 1277 || missionId == 1378;
					if (flag5)
					{
						int num = base.Value;
						base.Value = num + 1;
					}
					break;
				}
				case 5:
				{
					bool flag6 = missionId == 2002 || missionId == 2102;
					if (flag6)
					{
						int num = base.Value;
						base.Value = num + 1;
					}
					break;
				}
				case 6:
				{
					bool flag7 = missionId == 4103 || missionId == 4203 || missionId == 4303;
					if (flag7)
					{
						int num = base.Value;
						base.Value = num + 1;
					}
					break;
				}
				case 7:
				{
					bool flag8 = missionId == 5104 || missionId == 5204 || missionId == 5304;
					if (flag8)
					{
						int num = base.Value;
						base.Value = num + 1;
					}
					break;
				}
				}
			}
		}

		// Token: 0x06007098 RID: 28824 RVA: 0x0002A5B4 File Offset: 0x000287B4
		public override void RemoveTrigger(GamePlayer player)
		{
			player.MissionFullOver -= this.player_PlayerMissionFullOverEventHandle;
		}

		// Token: 0x06007099 RID: 28825 RVA: 0x00250064 File Offset: 0x0024E264
		public override bool IsCompleted(GamePlayer player)
		{
			return base.Value >= this.m_info.Condiction_Para2;
		}

		// Token: 0x04003C61 RID: 15457
		private int m_type;
	}
}
