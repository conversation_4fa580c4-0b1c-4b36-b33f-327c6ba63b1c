﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E1A RID: 3610
	public class AE1249 : BasePetEffect
	{
		// Token: 0x06007E37 RID: 32311 RVA: 0x0029F748 File Offset: 0x0029D948
		public AE1249(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1249, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E38 RID: 32312 RVA: 0x0029F7C8 File Offset: 0x0029D9C8
		public override bool Start(Living living)
		{
			AE1249 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1249) as AE1249;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E39 RID: 32313 RVA: 0x00030C81 File Offset: 0x0002EE81
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E3A RID: 32314 RVA: 0x00030C97 File Offset: 0x0002EE97
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E3B RID: 32315 RVA: 0x0029F828 File Offset: 0x0029DA28
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1249(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004CEE RID: 19694
		private int m_type = 0;

		// Token: 0x04004CEF RID: 19695
		private int m_count = 0;

		// Token: 0x04004CF0 RID: 19696
		private int m_probability = 0;

		// Token: 0x04004CF1 RID: 19697
		private int m_delay = 0;

		// Token: 0x04004CF2 RID: 19698
		private int m_coldDown = 0;

		// Token: 0x04004CF3 RID: 19699
		private int m_currentId;

		// Token: 0x04004CF4 RID: 19700
		private int m_added = 0;
	}
}
