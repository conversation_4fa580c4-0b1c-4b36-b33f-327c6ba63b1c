﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.NormalSpell
{
	// Token: 0x02000CB5 RID: 3253
	[SpellAttibute(1)]
	public class AddLifeSpell : ISpellHandler
	{
		// Token: 0x060074EC RID: 29932 RVA: 0x0026DEA4 File Offset: 0x0026C0A4
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			int property = item.Property2;
			int num = property;
			if (num != 0)
			{
				if (num == 1)
				{
					List<Player> allFightPlayers = player.Game.GetAllFightPlayers();
					foreach (Player player2 in allFightPlayers)
					{
						bool flag = player2.IsLiving && player2.Team == player.Team;
						if (flag)
						{
							player2.AddBlood(item.Property3);
						}
					}
				}
			}
			else
			{
				int num2 = item.Property3;
				bool isLiving = player.IsLiving;
				if (isLiving)
				{
					bool flag2 = player.FightBuffers.ConsortionAddSpellCount > 0;
					if (flag2)
					{
						num2 += player.FightBuffers.ConsortionAddSpellCount;
					}
					player.AddBlood(num2);
				}
				else
				{
					bool flag3 = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
					if (flag3)
					{
						game.CurrentLiving.AddBlood(num2);
					}
				}
			}
		}
	}
}
