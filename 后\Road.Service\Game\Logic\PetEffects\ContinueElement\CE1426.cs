﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EBD RID: 3773
	public class CE1426 : BasePetEffect
	{
		// Token: 0x0600821E RID: 33310 RVA: 0x002AF6E8 File Offset: 0x002AD8E8
		public CE1426(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1426, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x0600821F RID: 33311 RVA: 0x002AF768 File Offset: 0x002AD968
		public override bool Start(Living living)
		{
			CE1426 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1426) as CE1426;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008220 RID: 33312 RVA: 0x002AF7C8 File Offset: 0x002AD9C8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginAttacking += this.ChangeProperty;
			player.BeginSelfTurn += this.player_beginSeftTurn;
			player.Game.SendPetBuff(player, base.ElementInfo, true);
			player.ShowImprisonment(true);
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008221 RID: 33313 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008222 RID: 33314 RVA: 0x002AF82C File Offset: 0x002ADA2C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginAttacking -= this.ChangeProperty;
			player.BeginSelfTurn -= this.player_beginSeftTurn;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.ShowImprisonment(false);
		}

		// Token: 0x06008223 RID: 33315 RVA: 0x002AF87C File Offset: 0x002ADA7C
		public void player_beginSeftTurn(Living living)
		{
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008224 RID: 33316 RVA: 0x002AF8A0 File Offset: 0x002ADAA0
		private void ChangeProperty(Living living)
		{
			bool isAttacking = living.IsAttacking;
			if (isAttacking)
			{
				bool flag = this.m_count >= 0;
				if (flag)
				{
					((Player)living).SkipAttack();
				}
				this.m_count--;
			}
		}

		// Token: 0x0400516A RID: 20842
		private int m_type = 0;

		// Token: 0x0400516B RID: 20843
		private int m_count = 0;

		// Token: 0x0400516C RID: 20844
		private int m_probability = 0;

		// Token: 0x0400516D RID: 20845
		private int m_delay = 0;

		// Token: 0x0400516E RID: 20846
		private int m_coldDown = 0;

		// Token: 0x0400516F RID: 20847
		private int m_currentId;

		// Token: 0x04005170 RID: 20848
		private int m_added = 0;
	}
}
