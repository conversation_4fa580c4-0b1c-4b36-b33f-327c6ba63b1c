using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using Bussiness.CenterService;
using Bussiness.Managers;
using SqlDataProvider.BaseClass;
using SqlDataProvider.Data;

namespace Bussiness
{
	// Token: 0x02000FB4 RID: 4020
	public class PlayerBussiness : BaseBussiness
	{
		// Token: 0x060088C5 RID: 35013 RVA: 0x002D3778 File Offset: 0x002D1978
		public PlayerBussiness()
		{
		}

		// Token: 0x060088C6 RID: 35014 RVA: 0x002D37E0 File Offset: 0x002D19E0
		public List<int> DeleteMailAll(int UserID, bool flag)
		{
			SqlDataReader sqlDataReader = null;
			List<int> list = new List<int>();
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", UserID),
					new SqlParameter("@flag", flag)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Mail_DeleteAll", array);
				while (sqlDataReader.Read())
				{
					list.Add((int)sqlDataReader["ID"]);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return list;
		}

		// Token: 0x060088C7 RID: 35015 RVA: 0x002D38A4 File Offset: 0x002D1AA4
		public PlayerBussiness(AreaConfigInfo config)
		{
			this.AreaName = config.AreaName;
			this.AreaID = config.AreaID;
			this.VersionFlash = config.Version;
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("Data Source=");
			stringBuilder.Append(config.DataSource);
			stringBuilder.Append("; Initial Catalog=");
			stringBuilder.Append(config.Catalog);
			stringBuilder.Append("; Persist Security Info=True;User ID=");
			stringBuilder.Append(config.UserID);
			stringBuilder.Append("; Password=");
			stringBuilder.Append(config.Password);
			stringBuilder.Append(";");
			string text = stringBuilder.ToString();
			this.db = new Sql_DbObject("AreaConfig", text);
		}

		// Token: 0x060088C8 RID: 35016 RVA: 0x002D39BC File Offset: 0x002D1BBC
		public bool ActivePlayer(ref PlayerInfo player, string userName, string passWord, bool sex, int gold, int money, int giftToken, string IP, string site)
		{
			bool flag = false;
			try
			{
				player = new PlayerInfo
				{
					Agility = 0,
					Attack = 0,
					Colors = ",,,,,,",
					Skin = "",
					ConsortiaID = 0,
					Defence = 0,
					Gold = gold,
					GP = 1,
					Grade = 1,
					ID = 0,
					Luck = 0,
					Money = money,
					GiftToken = giftToken,
					Score = 0,
					NickName = "",
					Sex = sex,
					State = 0,
					Style = ",,,,,,",
					Hide = 1111111111
				};
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int)
					{
						Direction = ParameterDirection.Output
					},
					new SqlParameter("@Attack", player.Attack),
					new SqlParameter("@Colors", player.Colors ?? ""),
					new SqlParameter("@ConsortiaID", player.ConsortiaID),
					new SqlParameter("@Defence", player.Defence),
					new SqlParameter("@Gold", player.Gold),
					new SqlParameter("@GP", player.GP),
					new SqlParameter("@Grade", player.Grade),
					new SqlParameter("@Luck", player.Luck),
					new SqlParameter("@Money", player.Money),
					new SqlParameter("@Style", player.Style ?? ""),
					new SqlParameter("@Agility", player.Agility),
					new SqlParameter("@State", player.State),
					new SqlParameter("@UserName", userName),
					new SqlParameter("@PassWord", passWord),
					new SqlParameter("@Sex", sex),
					new SqlParameter("@Hide", player.Hide),
					new SqlParameter("@ActiveIP", IP),
					new SqlParameter("@Skin", player.Skin ?? ""),
					new SqlParameter("@Result", SqlDbType.Int)
					{
						Direction = ParameterDirection.ReturnValue
					},
					new SqlParameter("@Site", site),
					new SqlParameter("@GiftToken", player.GiftToken),
					new SqlParameter("@Score", player.Score)
				};
				bool flag2 = this.db.RunProcedure("SP_Users_Active", array);
				if (flag2)
				{
					player.ID = (int)array[0].Value;
					flag = (int)array[19].Value == 0;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088C9 RID: 35017 RVA: 0x002D3D3C File Offset: 0x002D1F3C
		public bool RegisterPlayer(string userName, string passWord, string nickName, string bStyle, string gStyle, string armColor, string hairColor, string faceColor, string clothColor, int sex, ref string msg, int validDate)
		{
			bool flag = false;
			try
			{
				string[] array = bStyle.Split(new char[] { ',' });
				string[] array2 = gStyle.Split(new char[] { ',' });
				SqlParameter[] array3 = new SqlParameter[18];
				array3[0] = new SqlParameter("@UserName", userName);
				array3[1] = new SqlParameter("@PassWord", passWord);
				array3[2] = new SqlParameter("@NickName", nickName);
				array3[3] = new SqlParameter("@BArmID", array[0]);
				array3[4] = new SqlParameter("@BHairID", array[1]);
				array3[5] = new SqlParameter("@BFaceID", array[2]);
				array3[6] = new SqlParameter("@BClothID", array[3]);
				array3[7] = new SqlParameter("@GArmID", array2[0]);
				array3[8] = new SqlParameter("@GHairID", array2[1]);
				array3[9] = new SqlParameter("@GFaceID", array2[2]);
				array3[10] = new SqlParameter("@GClothID", array2[3]);
				array3[11] = new SqlParameter("@ArmColor", (armColor == null) ? "" : armColor);
				array3[12] = new SqlParameter("@HairColor", (hairColor == null) ? "" : hairColor);
				array3[13] = new SqlParameter("@FaceColor", (faceColor == null) ? "" : faceColor);
				array3[14] = new SqlParameter("@ClothColor", (clothColor == null) ? "" : clothColor);
				array3[15] = new SqlParameter("@Sex", sex);
				array3[16] = new SqlParameter("@Result", SqlDbType.Int);
				SqlParameter[] array4 = array3;
				array4[16].Direction = ParameterDirection.ReturnValue;
				array4[17] = new SqlParameter("@StyleDate", validDate);
				flag = this.db.RunProcedure("SP_Users_RegisterNotValidate", array4);
				int num = (int)array4[16].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				if (num3 != 2)
				{
					if (num3 == 3)
					{
						msg = LanguageMgr.GetTranslation("PlayerBussiness.RegisterPlayer.Msg3", Array.Empty<object>());
					}
				}
				else
				{
					msg = LanguageMgr.GetTranslation("PlayerBussiness.RegisterPlayer.Msg2", Array.Empty<object>());
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088CA RID: 35018 RVA: 0x002D3F90 File Offset: 0x002D2190
		public bool RenameNick(string userName, string nickName, string newNickName, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserName", userName),
					new SqlParameter("@NickName", nickName),
					new SqlParameter("@NewNickName", newNickName),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[3].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_Users_RenameNick", array);
				int num = (int)array[3].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				bool flag2 = num3 - 4 <= 1;
				if (flag2)
				{
					msg = LanguageMgr.GetTranslation("PlayerBussiness.RenameNick.Msg4", Array.Empty<object>());
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("RenameNick", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088CB RID: 35019 RVA: 0x002D407C File Offset: 0x002D227C
		public bool UpdateRenames()
		{
			bool flag = false;
			try
			{
				flag = this.db.RunProcedure("Sp_Renames_Batch");
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateRenames", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088CC RID: 35020 RVA: 0x002D40DC File Offset: 0x002D22DC
		public bool RenameConsortiaName(string userName, string nickName, string consortiaName, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserName", userName),
					new SqlParameter("@NickName", nickName),
					new SqlParameter("@ConsortiaName", consortiaName),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[3].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_Users_RenameConsortiaName", array);
				int num = (int)array[3].Value;
				flag = num == 0;
				int num2 = num;
				int num3 = num2;
				bool flag2 = num3 - 4 <= 1;
				if (flag2)
				{
					msg = LanguageMgr.GetTranslation("PlayerBussiness.SP_Users_RenameConsortiaName.Msg4", Array.Empty<object>());
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("RenameNick", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088CD RID: 35021 RVA: 0x002D41C8 File Offset: 0x002D23C8
		public bool UpdatePasswordInfo(int userID, string PasswordQuestion1, string PasswordAnswer1, string PasswordQuestion2, string PasswordAnswer2, int Count)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", userID),
					new SqlParameter("@PasswordQuestion1", PasswordQuestion1),
					new SqlParameter("@PasswordAnswer1", PasswordAnswer1),
					new SqlParameter("@PasswordQuestion2", PasswordQuestion2),
					new SqlParameter("@PasswordAnswer2", PasswordAnswer2),
					new SqlParameter("@FailedPasswordAttemptCount", Count)
				};
				flag = this.db.RunProcedure("SP_Users_Password_Add", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088CE RID: 35022 RVA: 0x002D4294 File Offset: 0x002D2494
		public void GetPasswordInfo(int userID, ref string PasswordQuestion1, ref string PasswordAnswer1, ref string PasswordQuestion2, ref string PasswordAnswer2, ref int Count)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", userID)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Users_Password_Info", array);
				while (sqlDataReader.Read())
				{
					PasswordQuestion1 = ((sqlDataReader["PasswordQuestion1"] == null) ? "" : sqlDataReader["PasswordQuestion1"].ToString());
					PasswordAnswer1 = ((sqlDataReader["PasswordAnswer1"] == null) ? "" : sqlDataReader["PasswordAnswer1"].ToString());
					PasswordQuestion2 = ((sqlDataReader["PasswordQuestion2"] == null) ? "" : sqlDataReader["PasswordQuestion2"].ToString());
					PasswordAnswer2 = ((sqlDataReader["PasswordAnswer2"] == null) ? "" : sqlDataReader["PasswordAnswer2"].ToString());
					DateTime dateTime = (DateTime)sqlDataReader["LastFindDate"];
					bool flag = dateTime == DateTime.Today;
					if (flag)
					{
						Count = (int)sqlDataReader["FailedPasswordAttemptCount"];
					}
					else
					{
						Count = 5;
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
		}

		// Token: 0x060088CF RID: 35023 RVA: 0x002D4444 File Offset: 0x002D2644
		public bool UpdatePasswordTwo(int userID, string passwordTwo)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", userID),
					new SqlParameter("@PasswordTwo", passwordTwo)
				};
				flag = this.db.RunProcedure("SP_Users_UpdatePasswordTwo", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088D0 RID: 35024 RVA: 0x002D44D0 File Offset: 0x002D26D0
		public PlayerInfo[] GetUserLoginList(string userName)
		{
			List<PlayerInfo> list = new List<PlayerInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserName", SqlDbType.VarChar, 200)
				};
				array[0].Value = userName;
				this.db.GetReader(ref sqlDataReader, "SP_Users_LoginList", array);
				while (sqlDataReader.Read())
				{
					list.Add(this.InitPlayerInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060088D1 RID: 35025 RVA: 0x002D45B4 File Offset: 0x002D27B4
		public PlayerInfo LoginGame(string username, ref int isFirst, ref bool isExist, ref bool isError, bool firstValidate, ref DateTime forbidDate, string nickname)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserName", username),
					new SqlParameter("@Password", ""),
					new SqlParameter("@FirstValidate", firstValidate),
					new SqlParameter("@Nickname", nickname)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Users_LoginWeb", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					isFirst = (int)sqlDataReader["IsFirst"];
					isExist = (bool)sqlDataReader["IsExist"];
					forbidDate = (DateTime)sqlDataReader["ForbidDate"];
					bool flag2 = isFirst > 1;
					if (flag2)
					{
						isFirst--;
					}
					return this.InitPlayerInfo(sqlDataReader);
				}
			}
			catch (Exception ex)
			{
				isError = true;
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag3 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag3)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x060088D2 RID: 35026 RVA: 0x002D46FC File Offset: 0x002D28FC
		public bool UpdatePlayer(PlayerInfo player)
		{
			bool flag = false;
			try
			{
				bool flag2 = player.Grade < 1;
				if (flag2)
				{
					return flag;
				}
				SqlParameter[] array = new SqlParameter[110];
				array[0] = new SqlParameter("@UserID", player.ID);
				array[1] = new SqlParameter("@Attack", player.Attack);
				array[2] = new SqlParameter("@Colors", (player.Colors == null) ? "" : player.Colors);
				array[3] = new SqlParameter("@ConsortiaID", player.ConsortiaID);
				array[4] = new SqlParameter("@Defence", player.Defence);
				array[5] = new SqlParameter("@Gold", player.Gold);
				array[6] = new SqlParameter("@GP", player.GP);
				array[7] = new SqlParameter("@Grade", player.Grade);
				array[8] = new SqlParameter("@Luck", player.Luck);
				array[9] = new SqlParameter("@Money", player.Money);
				array[10] = new SqlParameter("@Style", (player.Style == null) ? "" : player.Style);
				array[11] = new SqlParameter("@Agility", player.Agility);
				array[12] = new SqlParameter("@State", player.State);
				array[13] = new SqlParameter("@Hide", player.Hide);
				array[14] = new SqlParameter("@ExpendDate", (player.ExpendDate == null) ? "" : player.ExpendDate.ToString());
				array[15] = new SqlParameter("@Win", player.Win);
				array[16] = new SqlParameter("@Total", player.Total);
				array[17] = new SqlParameter("@Escape", player.Escape);
				array[18] = new SqlParameter("@Skin", (player.Skin == null) ? "" : player.Skin);
				array[19] = new SqlParameter("@Offer", player.Offer);
				array[20] = new SqlParameter("@AntiAddiction", player.AntiAddiction);
				array[20].Direction = ParameterDirection.InputOutput;
				array[21] = new SqlParameter("@Result", SqlDbType.Int);
				array[21].Direction = ParameterDirection.ReturnValue;
				array[22] = new SqlParameter("@RichesOffer", player.RichesOffer);
				array[23] = new SqlParameter("@RichesRob", player.RichesRob);
				array[24] = new SqlParameter("@CheckCount", player.CheckCount);
				array[24].Direction = ParameterDirection.InputOutput;
				array[25] = new SqlParameter("@MarryInfoID", player.MarryInfoID);
				array[26] = new SqlParameter("@DayLoginCount", player.DayLoginCount);
				array[27] = new SqlParameter("@Nimbus", player.Nimbus);
				array[28] = new SqlParameter("@LastAward", player.LastAward);
				array[29] = new SqlParameter("@GiftToken", player.GiftToken);
				array[30] = new SqlParameter("@QuestSite", player.QuestSite);
				array[31] = new SqlParameter("@PvePermission", player.PvePermission);
				array[32] = new SqlParameter("@FightPower", player.FightPower);
				array[33] = new SqlParameter("@AnswerSite", player.AnswerSite);
				array[34] = new SqlParameter("@LastAuncherAward", player.LastAuncherAward);
				array[35] = new SqlParameter("@ChatCount", player.ChatCount);
				array[36] = new SqlParameter("@TypeVIP", player.TypeVIP);
				array[37] = new SqlParameter("@VIPLevel", player.VIPLevel);
				array[38] = new SqlParameter("@VIPExp", player.VIPExp);
				array[39] = new SqlParameter("@VIPNextLevelDaysNeeded", player.GetVIPNextLevelDaysNeeded(player.VIPLevel, player.VIPExp));
				array[40] = new SqlParameter("@AchievementPoint", player.AchievementPoint);
				array[41] = new SqlParameter("@Blood", player.Blood);
				array[42] = new SqlParameter("@ShopFinallyGottenTime", player.ShopFinallyGottenTime);
				array[43] = new SqlParameter("@Honor", player.Honor);
				array[44] = new SqlParameter("@FightLibMission", player.FightLibMission);
				array[45] = new SqlParameter("@WeaklessGuildProgressStr", player.WeaklessGuildProgressStr);
				array[46] = new SqlParameter("@IsOldPlayer", player.IsOldPlayer);
				array[47] = new SqlParameter("@GiftGP", player.GiftGP);
				array[48] = new SqlParameter("@GiftLevel", player.GiftLevel);
				array[49] = new SqlParameter("@Score", player.Score);
				array[50] = new SqlParameter("@LotteryScore", player.LotteryScore);
				array[51] = new SqlParameter("@MysteriousSPAR", player.MysteriousSPAR);
				array[52] = new SqlParameter("@AchievementProcess", player.AchievementProcess);
				array[53] = new SqlParameter("@LastSpaDate", player.LastSpaDate);
				array[54] = new SqlParameter("@IsInSpaPubGoldToday", player.IsInSpaPubGoldToday);
				array[55] = new SqlParameter("@IsInSpaPubMoneyToday", player.IsInSpaPubMoneyToday);
				array[56] = new SqlParameter("@SpaPubGoldRoomLimit", player.SpaPubGoldRoomLimit);
				array[57] = new SqlParameter("@SpaPubMoneyRoomLimit", player.SpaPubMoneyRoomLimit);
				array[58] = new SqlParameter("@LastLoginDate", player.LastLoginDate);
				array[59] = new SqlParameter("@HonorID", player.HonorID);
				array[60] = new SqlParameter("@TotemId", player.TotemId);
				array[61] = new SqlParameter("@MyHornor", player.MyHornor);
				array[62] = new SqlParameter("@DamageScore", player.DamageScore);
				array[63] = new SqlParameter("@TotemUpgrade", player.TotemUpgrade);
				array[64] = new SqlParameter("@MaxBuyHonor", player.MaxBuyHonor);
				array[65] = new SqlParameter("@TreasureScore", player.TreasureScore);
				array[66] = new SqlParameter("@LuckStone", player.LuckStone);
				array[67] = new SqlParameter("@CaddyNormal", player.CaddyNormal);
				array[68] = new SqlParameter("@CaddySilver", player.CaddySilver);
				array[69] = new SqlParameter("@LastEggAward", player.LastEggAward);
				array[70] = new SqlParameter("@DailyScore", player.DailyScore);
				array[71] = new SqlParameter("@DailyWinCount", player.DailyWinCount);
				array[72] = new SqlParameter("@DailyGameCount", player.DailyGameCount);
				array[73] = new SqlParameter("@DailyLeagueFirst", player.DailyLeagueFirst);
				array[74] = new SqlParameter("@DailyLeagueLastScore", player.DailyLeagueLastScore);
				array[75] = new SqlParameter("@WeeklyScore", player.WeeklyScore);
				array[76] = new SqlParameter("@WeeklyGameCount", player.WeeklyGameCount);
				array[77] = new SqlParameter("@EliteScore", player.EliteScore);
				array[78] = new SqlParameter("@EliteRank", player.EliteRank);
				array[79] = new SqlParameter("@BlueDrillType", player.BlueDrillType);
				array[80] = new SqlParameter("@BlueDrillLevel", player.BlueDrillLevel);
				array[81] = new SqlParameter("@BlueDrillBeginDate", player.BlueDrillBeginDate);
				array[82] = new SqlParameter("@BlueDrillEndDate", player.BlueDrillEndDate);
				array[83] = new SqlParameter("@isGetQQHallRegisterGift", player.isGetQQHallRegisterGift);
				array[84] = new SqlParameter("@isGetQQHallActiveGift", player.isGetQQHallActiveGift);
				array[85] = new SqlParameter("@qqHallUpgradeGift", player.qqHallUpgradeGift);
				array[86] = new SqlParameter("@isGetBlueDrillRegisterGift", player.isGetBlueDrillRegisterGift);
				array[87] = new SqlParameter("@isGetBlueDrillDayGift", player.isGetBlueDrillDayGift);
				array[88] = new SqlParameter("@blueDrillUpgradeGift", player.blueDrillUpgradeGift);
				array[89] = new SqlParameter("@TreasureNormalTicket", player.TreasureNormalTicket);
				array[90] = new SqlParameter("@TreasureAdvancedTicket", player.TreasureAdvancedTicket);
				array[91] = new SqlParameter("@TreasureDrowCount", player.TreasureDrowCount);
				array[92] = new SqlParameter("@ElementPro", player.ElementPro);
				array[93] = new SqlParameter("@TakeCardNum", player.TakeCardNum);
				array[94] = new SqlParameter("@IsFistGetPet", player.IsFistGetPet);
				array[95] = new SqlParameter("@LastRefreshPet", player.LastRefreshPet);
				array[96] = new SqlParameter("@HomeTempleExp", player.HomeTempleExp);
				array[97] = new SqlParameter("@CurrentSelectIndex", player.CurrentSelectIndex);
				array[98] = new SqlParameter("@CurrentLevel", player.CurrentLevel);
				array[99] = new SqlParameter("@PromoteLevel", player.PromoteLevel);
				array[100] = new SqlParameter("@NecklaceExp", player.NecklaceExp);
				array[101] = new SqlParameter("@NecklaceCastLevel", player.NecklaceCastLevel);
				array[102] = new SqlParameter("@Stive", player.Stive);
				array[103] = new SqlParameter("@BuyStiveNum", player.BuyStiveNum);
				array[104] = new SqlParameter("@GhostEquipList", player.GhostEquipList);
				array[105] = new SqlParameter("@FineSuitExp", player.FineSuitExp);
				array[106] = new SqlParameter("@zhanling", player.zhanling);
				array[107] = new SqlParameter("@zhanlingExp", player.zhanlingExp);
				array[108] = new SqlParameter("@zhanlingLevel", player.zhanlingLevel);
				array[109] = new SqlParameter("@zhanlingVipType", player.zhanlingVipType);
				this.db.RunProcedure("SP_Users_Update", array);
				flag = (int)array[21].Value == 0;
				bool flag3 = flag;
				if (flag3)
				{
					player.AntiAddiction = (int)array[20].Value;
					player.CheckCount = (int)array[24].Value;
				}
				player.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088D3 RID: 35027 RVA: 0x002D52B8 File Offset: 0x002D34B8
		public bool UpdatePlayerLastAward(int id)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", id)
				};
				flag = this.db.RunProcedure("SP_Users_LastAward", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdatePlayerAward", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088D4 RID: 35028 RVA: 0x002D5338 File Offset: 0x002D3538
		public bool UpdatePlayerLastAuncherAward(int id)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", id)
				};
				flag = this.db.RunProcedure("SP_Users_LastAuncherAward", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdatePlayerLastAuncherAward", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088D5 RID: 35029 RVA: 0x002D53B8 File Offset: 0x002D35B8
		public bool UpdatePlayerLastEggAward(int id)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", id)
				};
				flag = this.db.RunProcedure("SP_Users_LastEggAward", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdatePlayerLastEggAward", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088D6 RID: 35030 RVA: 0x002D5438 File Offset: 0x002D3638
		public bool UpdateBoxProgression(int userid, int boxProgression, int getBoxLevel, DateTime addGPLastDate, DateTime BoxGetDate, int alreadyBox)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", userid),
					new SqlParameter("@BoxProgression", boxProgression),
					new SqlParameter("@GetBoxLevel", getBoxLevel),
					new SqlParameter("@AddGPLastDate", addGPLastDate),
					new SqlParameter("@BoxGetDate", BoxGetDate),
					new SqlParameter("@AlreadyGetBox", alreadyBox),
					new SqlParameter("@OutPut", SqlDbType.Int)
				};
				array[6].Direction = ParameterDirection.Output;
				this.db.RunProcedure("SP_Users_Update_BoxProgression", array);
				flag = (int)array[6].Value == 1;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("User_Update_BoxProgression", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088D7 RID: 35031 RVA: 0x002D5540 File Offset: 0x002D3740
		public PlayerInfo GetUserSingleByUserID(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_SingleByUserID", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return this.InitPlayerInfo(sqlDataReader);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x060088D8 RID: 35032 RVA: 0x002D5610 File Offset: 0x002D3810
		public PlayerInfo GetUserSingleByUserName(string userName)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserName", SqlDbType.VarChar, 200)
				};
				array[0].Value = userName;
				this.db.GetReader(ref sqlDataReader, "SP_Users_SingleByUserName", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return this.InitPlayerInfo(sqlDataReader);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x060088D9 RID: 35033 RVA: 0x002D56E0 File Offset: 0x002D38E0
		public PlayerInfo GetUserSingleByNickName(string nickName)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@NickName", SqlDbType.VarChar, 200)
				};
				array[0].Value = nickName;
				this.db.GetReader(ref sqlDataReader, "SP_Users_SingleByNickName", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return this.InitPlayerInfo(sqlDataReader);
				}
			}
			catch
			{
				throw new Exception();
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x060088DA RID: 35034 RVA: 0x002D5790 File Offset: 0x002D3990
		public bool GetUserCheckByNickName(string nickName)
		{
			bool flag = false;
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@NickName", SqlDbType.NVarChar, 200)
				};
				array[0].Value = nickName;
				bool reader = this.db.GetReader(ref sqlDataReader, "SP_Users_CheckByNickName", array);
				if (reader)
				{
					while (sqlDataReader.Read())
					{
						flag = true;
					}
				}
			}
			catch
			{
				flag = true;
				throw new Exception();
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return flag;
		}

		// Token: 0x060088DB RID: 35035 RVA: 0x002D5848 File Offset: 0x002D3A48
		public PlayerInfo InitPlayerInfo(SqlDataReader reader)
		{
			PlayerInfo playerInfo = new PlayerInfo();
			playerInfo.IsConsortia = (bool)reader["IsConsortia"];
			playerInfo.Agility = (int)reader["Agility"];
			playerInfo.Attack = (int)reader["Attack"];
			playerInfo.Colors = ((reader["Colors"] == null) ? "" : reader["Colors"].ToString());
			playerInfo.ConsortiaID = (int)reader["ConsortiaID"];
			playerInfo.Defence = (int)reader["Defence"];
			playerInfo.Gold = (int)reader["Gold"];
			playerInfo.GP = (int)reader["GP"];
			playerInfo.Grade = (int)reader["Grade"];
			playerInfo.ID = (int)reader["UserID"];
			playerInfo.Luck = (int)reader["Luck"];
			playerInfo.Blood = (int)reader["Blood"];
			playerInfo.Money = (int)reader["Money"];
			playerInfo.GiftToken = (int)reader["GiftToken"];
			playerInfo.NickName = ((reader["NickName"] == null) ? "" : reader["NickName"].ToString());
			playerInfo.Sex = (bool)reader["Sex"];
			playerInfo.State = (int)reader["State"];
			playerInfo.Style = ((reader["Style"] == null) ? "" : reader["Style"].ToString());
			playerInfo.Hide = (int)reader["Hide"];
			playerInfo.Honor = ((reader["Honor"] == null) ? "" : reader["Honor"].ToString());
			playerInfo.HonorID = (int)reader["HonorID"];
			playerInfo.Repute = (int)reader["Repute"];
			playerInfo.UserName = ((reader["UserName"] == null) ? "" : reader["UserName"].ToString());
			playerInfo.ConsortiaName = ((reader["ConsortiaName"] == null) ? "" : reader["ConsortiaName"].ToString());
			playerInfo.Offer = (int)reader["Offer"];
			playerInfo.Win = (int)reader["Win"];
			playerInfo.Total = (int)reader["Total"];
			playerInfo.Escape = (int)reader["Escape"];
			playerInfo.Skin = ((reader["Skin"] == null) ? "" : reader["Skin"].ToString());
			playerInfo.IsBanChat = (bool)reader["IsBanChat"];
			playerInfo.ReputeOffer = (int)reader["ReputeOffer"];
			playerInfo.ConsortiaRepute = (int)reader["ConsortiaRepute"];
			playerInfo.ConsortiaLevel = (int)reader["ConsortiaLevel"];
			playerInfo.StoreLevel = (int)reader["StoreLevel"];
			playerInfo.ShopLevel = (int)reader["ShopLevel"];
			playerInfo.SmithLevel = (int)reader["SmithLevel"];
			playerInfo.BufferLevel = (int)reader["BufferLevel"];
			playerInfo.ConsortiaHonor = (int)reader["ConsortiaHonor"];
			playerInfo.RichesOffer = (int)reader["RichesOffer"];
			playerInfo.RichesRob = (int)reader["RichesRob"];
			playerInfo.AntiAddiction = (int)reader["AntiAddiction"];
			playerInfo.DutyLevel = (int)reader["DutyLevel"];
			playerInfo.DutyName = ((reader["DutyName"] == null) ? "" : reader["DutyName"].ToString());
			playerInfo.Right = (int)reader["Right"];
			playerInfo.ChairmanName = ((reader["ChairmanName"] == null) ? "" : reader["ChairmanName"].ToString());
			playerInfo.AddDayGP = (int)reader["AddDayGP"];
			playerInfo.AddDayOffer = (int)reader["AddDayOffer"];
			playerInfo.AddDayGiftGP = (int)reader["AddDayGiftGP"];
			playerInfo.AddDayAchievementPoint = (int)reader["AddDayAchievementPoint"];
			playerInfo.AddWeekGP = (int)reader["AddWeekGP"];
			playerInfo.AddWeekOffer = (int)reader["AddWeekOffer"];
			playerInfo.AddWeekGiftGP = (int)reader["AddWeekGiftGP"];
			playerInfo.AddWeekAchievementPoint = (int)reader["AddWeekAchievementPoint"];
			playerInfo.ConsortiaRiches = (int)reader["ConsortiaRiches"];
			playerInfo.CheckCount = (int)reader["CheckCount"];
			playerInfo.IsMarried = (bool)reader["IsMarried"];
			playerInfo.SpouseID = (int)reader["SpouseID"];
			playerInfo.SpouseName = ((reader["SpouseName"] == null) ? "" : reader["SpouseName"].ToString());
			playerInfo.MarryInfoID = (int)reader["MarryInfoID"];
			playerInfo.IsCreatedMarryRoom = (bool)reader["IsCreatedMarryRoom"];
			playerInfo.DayLoginCount = (int)reader["DayLoginCount"];
			playerInfo.PasswordTwo = ((reader["PasswordTwo"] == null) ? "" : reader["PasswordTwo"].ToString());
			playerInfo.SelfMarryRoomID = (int)reader["SelfMarryRoomID"];
			playerInfo.IsGotRing = (bool)reader["IsGotRing"];
			playerInfo.Rename = (bool)reader["Rename"];
			playerInfo.ConsortiaRename = (bool)reader["ConsortiaRename"];
			playerInfo.IsFirst = (int)reader["IsFirst"];
			playerInfo.Nimbus = (int)reader["Nimbus"];
			playerInfo.LastAward = (DateTime)reader["LastAward"];
			playerInfo.QuestSite = ((reader["QuestSite"] == null) ? new byte[200] : ((byte[])reader["QuestSite"]));
			playerInfo.PvePermission = ((reader["PvePermission"] == null) ? "" : reader["PvePermission"].ToString());
			playerInfo.FightLibMission = ((reader["FightLibMission"] == DBNull.Value) ? "" : reader["FightLibMission"].ToString());
			playerInfo.FightPower = (int)reader["FightPower"];
			playerInfo.PasswordQuest1 = ((reader["PasswordQuestion1"] == null) ? "" : reader["PasswordQuestion1"].ToString());
			playerInfo.PasswordQuest2 = ((reader["PasswordQuestion2"] == null) ? "" : reader["PasswordQuestion2"].ToString());
			DateTime dateTime = (DateTime)reader["LastFindDate"];
			playerInfo.FailedPasswordAttemptCount = (int)reader["FailedPasswordAttemptCount"];
			playerInfo.AnswerSite = (int)reader["AnswerSite"];
			playerInfo.LastAuncherAward = (DateTime)reader["LastAuncherAward"];
			playerInfo.LastDate = ((reader["LastDate"] == DBNull.Value) ? DateTime.Now : ((DateTime)reader["LastDate"]));
			playerInfo.TypeVIP = Convert.ToByte(reader["TypeVIP"]);
			playerInfo.VIPLevel = (int)reader["VIPLevel"];
			playerInfo.VIPExp = (int)reader["VIPExp"];
			playerInfo.VIPExpireDay = (DateTime)reader["VIPExpireDay"];
			playerInfo.VIPNextLevelDaysNeeded = (int)reader["VIPNextLevelDaysNeeded"];
			playerInfo.LastVIPPackTime = (DateTime)reader["LastVIPPackTime"];
			playerInfo.CanTakeVipReward = (bool)reader["CanTakeVipReward"];
			playerInfo.AchievementPoint = (int)reader["AchievementPoint"];
			playerInfo.MasterID = (int)reader["MasterID"];
			playerInfo.ApprenticeshipState = (int)reader["ApprenticeshipState"];
			playerInfo.MasterOrApprentices = ((reader["MasterOrApprentices"] == DBNull.Value) ? "" : ((string)reader["MasterOrApprentices"]));
			playerInfo.GraduatesCount = (int)reader["GraduatesCount"];
			playerInfo.HonourOfMaster = ((reader["HonourOfMaster"] == DBNull.Value) ? "" : ((string)reader["HonourOfMaster"]));
			playerInfo.FreezesDate = ((reader["FreezesDate"] == DBNull.Value) ? DateTime.Now : ((DateTime)reader["FreezesDate"]));
			playerInfo.ShopFinallyGottenTime = ((reader["ShopFinallyGottenTime"] == DBNull.Value) ? DateTime.Now.AddDays(-1.0) : ((DateTime)reader["ShopFinallyGottenTime"]));
			playerInfo.WeaklessGuildProgressStr = (string)reader["WeaklessGuildProgressStr"];
			playerInfo.ChatCount = ((reader["ChatCount"] != null) ? ((int)reader["ChatCount"]) : 0);
			playerInfo.OnlineTime = (int)reader["OnlineTime"];
			playerInfo.GiftGP = ((reader["GiftGP"] != DBNull.Value) ? ((int)reader["GiftGP"]) : 0);
			playerInfo.GiftLevel = ((reader["GiftLevel"] != DBNull.Value) ? ((int)reader["GiftLevel"]) : 0);
			playerInfo.Score = (int)reader["Score"];
			playerInfo.MysteriousSPAR = (int)reader["MysteriousSPAR"];
			playerInfo.LotteryScore = (int)reader["LotteryScore"];
			playerInfo.TreasureScore = (int)reader["TreasureScore"];
			playerInfo.TreasureNormalTicket = (int)reader["TreasureNormalTicket"];
			playerInfo.TreasureAdvancedTicket = (int)reader["TreasureAdvancedTicket"];
			playerInfo.TreasureDrowCount = (int)reader["TreasureDrowCount"];
			playerInfo.LuckStone = (int)reader["LuckStone"];
			playerInfo.CaddyNormal = (int)reader["CaddyNormal"];
			playerInfo.CaddySilver = (int)reader["CaddySilver"];
			playerInfo.AchievementProcess = ((reader["AchievementProcess"] == null) ? "" : reader["AchievementProcess"].ToString());
			playerInfo.BoxGetDate = (DateTime)reader["BoxGetDate"];
			playerInfo.AlreadyGetBox = (int)reader["AlreadyGetBox"];
			playerInfo.BoxProgression = (int)reader["BoxProgression"];
			playerInfo.GetBoxLevel = (int)reader["GetBoxLevel"];
			playerInfo.AddGPLastDate = (DateTime)reader["AddGPLastDate"];
			playerInfo.LastSpaDate = (DateTime)reader["LastSpaDate"];
			playerInfo.IsInSpaPubGoldToday = (bool)reader["IsInSpaPubGoldToday"];
			playerInfo.IsInSpaPubMoneyToday = (bool)reader["IsInSpaPubMoneyToday"];
			playerInfo.SpaPubGoldRoomLimit = (int)reader["SpaPubGoldRoomLimit"];
			playerInfo.SpaPubMoneyRoomLimit = (int)reader["SpaPubMoneyRoomLimit"];
			playerInfo.LastLoginDate = (DateTime)reader["LastLoginDate"];
			playerInfo.TotemId = (int)reader["TotemId"];
			playerInfo.MyHornor = (int)reader["MyHornor"];
			playerInfo.DamageScore = (int)reader["DamageScore"];
			playerInfo.MaxBuyHonor = (int)reader["MaxBuyHonor"];
			playerInfo.TotemUpgrade = ((reader["TotemUpgrade"] == DBNull.Value) ? null : ((string)reader["TotemUpgrade"]));
			playerInfo.LastEggAward = ((reader["LastEggAward"] == DBNull.Value) ? DateTime.Now : ((DateTime)reader["LastEggAward"]));
			playerInfo.DailyScore = (int)reader["DailyScore"];
			playerInfo.DailyWinCount = (int)reader["DailyWinCount"];
			playerInfo.DailyGameCount = (int)reader["DailyGameCount"];
			playerInfo.DailyLeagueFirst = (bool)reader["DailyLeagueFirst"];
			playerInfo.DailyLeagueLastScore = (int)reader["DailyLeagueLastScore"];
			playerInfo.WeeklyScore = (int)reader["WeeklyScore"];
			playerInfo.WeeklyGameCount = (int)reader["WeeklyGameCount"];
			playerInfo.WeeklyRanking = (int)reader["WeeklyRanking"];
			playerInfo.EliteScore = (int)reader["EliteScore"];
			playerInfo.EliteRank = (int)reader["EliteRank"];
			playerInfo.BlueDrillType = (int)reader["BlueDrillType"];
			playerInfo.BlueDrillLevel = (int)reader["BlueDrillLevel"];
			playerInfo.BlueDrillBeginDate = (DateTime)reader["BlueDrillBeginDate"];
			playerInfo.BlueDrillEndDate = (DateTime)reader["BlueDrillEndDate"];
			playerInfo.isGetQQHallRegisterGift = (int)reader["isGetQQHallRegisterGift"];
			playerInfo.isGetQQHallActiveGift = (int)reader["isGetQQHallActiveGift"];
			playerInfo.qqHallUpgradeGift = ((reader["qqHallUpgradeGift"] == null) ? "" : reader["qqHallUpgradeGift"].ToString());
			playerInfo.isGetBlueDrillRegisterGift = (int)reader["isGetBlueDrillRegisterGift"];
			playerInfo.isGetBlueDrillDayGift = (int)reader["isGetBlueDrillDayGift"];
			playerInfo.blueDrillUpgradeGift = ((reader["blueDrillUpgradeGift"] == null) ? "" : reader["blueDrillUpgradeGift"].ToString());
			playerInfo.ElementPro = ((reader["ElementPro"] == DBNull.Value) ? null : ((string)reader["ElementPro"]));
			playerInfo.TakeCardNum = (int)reader["TakeCardNum"];
			playerInfo.IsFistGetPet = (bool)reader["IsFistGetPet"];
			playerInfo.LastRefreshPet = (DateTime)reader["LastRefreshPet"];
			playerInfo.HomeTempleExp = (int)reader["HomeTempleExp"];
			playerInfo.CurrentSelectIndex = (int)reader["CurrentSelectIndex"];
			playerInfo.CurrentLevel = (int)reader["CurrentLevel"];
			playerInfo.PromoteLevel = (int)reader["PromoteLevel"];
			playerInfo.NecklaceExp = (int)reader["NecklaceExp"];
			playerInfo.NecklaceCastLevel = (int)reader["NecklaceCastLevel"];
			playerInfo.Stive = (int)reader["Stive"];
			playerInfo.BuyStiveNum = (int)reader["BuyStiveNum"];
			playerInfo.GhostEquipList = ((reader["GhostEquipList"] == DBNull.Value) ? "" : ((string)reader["GhostEquipList"]));
			playerInfo.FineSuitExp = (int)reader["FineSuitExp"];
			ValueTuple<int, int, int, int> zhanlingValues = this.GetZhanlingValues(playerInfo.ID);
			playerInfo.zhanling = zhanlingValues.Item1;
			playerInfo.zhanlingExp = zhanlingValues.Item2;
			playerInfo.zhanlingLevel = zhanlingValues.Item3;
			playerInfo.zhanlingVipType = zhanlingValues.Item4;
			return playerInfo;
		}

		// Token: 0x060088DC RID: 35036 RVA: 0x002D69D4 File Offset: 0x002D4BD4
		[return: TupleElementNames(new string[] { "zhanling", "zhanlingExp", "zhanlingLevel", "zhanlingVipType" })]
		private ValueTuple<int, int, int, int> GetZhanlingValues(int userId)
		{
			string text = "Server=WIN-II1K7BSQ89Q;Database=Db_Tank_S1;Persist Security Info=True;User Id=sa; Password=********";
			ValueTuple<int, int, int, int> valueTuple;
			using (SqlConnection sqlConnection = new SqlConnection(text))
			{
				sqlConnection.Open();
				string text2 = "SELECT zhanling, zhanlingExp, zhanlingLevel, zhanlingVipType FROM Sys_User_Detail WHERE UserID = @UserID";
				using (SqlCommand sqlCommand = new SqlCommand(text2, sqlConnection))
				{
					sqlCommand.Parameters.Add(new SqlParameter("@UserID", userId));
					using (SqlDataReader sqlDataReader = sqlCommand.ExecuteReader())
					{
						bool flag = sqlDataReader.Read();
						if (!flag)
						{
							throw new Exception("User not found.");
						}
						int num = ((sqlDataReader["zhanling"] != DBNull.Value) ? ((int)sqlDataReader["zhanling"]) : 0);
						int num2 = ((sqlDataReader["zhanlingExp"] != DBNull.Value) ? ((int)sqlDataReader["zhanlingExp"]) : 0);
						int num3 = ((sqlDataReader["zhanlingLevel"] != DBNull.Value) ? ((int)sqlDataReader["zhanlingLevel"]) : 0);
						int num4 = ((sqlDataReader["zhanlingVipType"] != DBNull.Value) ? ((int)sqlDataReader["zhanlingVipType"]) : 0);
						Console.WriteLine("读取战令经验数据");
						valueTuple = new ValueTuple<int, int, int, int>(num, num2, num3, num4);
					}
				}
			}
			return valueTuple;
		}

		// Token: 0x060088DD RID: 35037 RVA: 0x002D6B7C File Offset: 0x002D4D7C
		public PlayerInfo[] GetPlayerPage(int page, int size, ref int total, int order, int userID, ref bool resultValue)
		{
			return this.GetPlayerPage(page, size, ref total, order, 0, userID, "UserID", ref resultValue);
		}

		// Token: 0x060088DE RID: 35038 RVA: 0x002D6BA4 File Offset: 0x002D4DA4
		public PlayerInfo[] GetPlayerPage(int page, int size, ref int total, int order, int where, int userID, string key, ref bool resultValue)
		{
			List<PlayerInfo> list = new List<PlayerInfo>();
			try
			{
				string text = " IsExist=1 and IsFirst<> 0 ";
				bool flag = userID != -1;
				if (flag)
				{
					text = text + " and UserID =" + userID.ToString() + " ";
				}
				string text2 = "GP desc";
				switch (order)
				{
				case 1:
					text2 = "Offer desc";
					break;
				case 2:
					text2 = "AddDayGP desc";
					break;
				case 3:
					text2 = "AddWeekGP desc";
					break;
				case 4:
					text2 = "AddDayOffer desc";
					break;
				case 5:
					text2 = "AddWeekOffer desc";
					break;
				case 6:
					text2 = "FightPower desc";
					break;
				case 7:
					text2 = "EliteScore desc";
					break;
				case 8:
					text2 = "State desc, GraduatesCount desc, FightPower desc";
					break;
				case 9:
					text2 = "NEWID()";
					break;
				case 10:
					text2 = "State desc, GP asc, FightPower desc";
					break;
				case 11:
					text2 = "AchievementPoint desc";
					break;
				case 12:
					text2 = "AddDayAchievementPoint desc";
					break;
				case 13:
					text2 = "AddWeekAchievementPoint desc";
					break;
				case 14:
					text2 = "GiftGP desc";
					break;
				case 15:
					text2 = "AddDayGiftGP desc";
					break;
				case 16:
					text2 = "AddWeekGiftGP desc";
					break;
				case 17:
					text2 = "DailyLeagueLastScore desc";
					break;
				case 18:
					text2 = "WeeklyScore desc";
					break;
				}
				switch (where)
				{
				case 0:
					text = text ?? "";
					break;
				case 1:
					text += " and Grade >= 20 ";
					break;
				case 2:
					text += " and Grade > 5 and Grade < 17 ";
					break;
				case 3:
					text += " and Grade >= 20 and ApprenticeshipState != 3 and State = 1 ";
					break;
				case 4:
					text += " and Grade > 5 and Grade < 17 and MasterID = 0 and State = 1 ";
					break;
				}
				text2 += ",UserID";
				DataTable page2 = base.GetPage("V_Sys_User_Detail", text, page, size, "*", text2, key, ref total);
				foreach (object obj in page2.Rows)
				{
					DataRow dataRow = (DataRow)obj;
					list.Add(new PlayerInfo
					{
						AreaID = this.AreaID,
						AreaName = this.AreaName,
						Agility = (int)dataRow["Agility"],
						Attack = (int)dataRow["Attack"],
						Colors = ((dataRow["Colors"] == null) ? "" : dataRow["Colors"].ToString()),
						ConsortiaID = (int)dataRow["ConsortiaID"],
						Defence = (int)dataRow["Defence"],
						Gold = (int)dataRow["Gold"],
						GP = (int)dataRow["GP"],
						Grade = (int)dataRow["Grade"],
						ID = (int)dataRow["UserID"],
						Luck = (int)dataRow["Luck"],
						Money = (int)dataRow["Money"],
						Score = (int)dataRow["Score"],
						NickName = ((dataRow["NickName"] == null) ? "" : dataRow["NickName"].ToString()),
						Sex = (bool)dataRow["Sex"],
						State = (int)dataRow["State"],
						Style = ((dataRow["Style"] == null) ? "" : dataRow["Style"].ToString()),
						Hide = (int)dataRow["Hide"],
						Repute = (int)dataRow["Repute"],
						UserName = ((dataRow["UserName"] == null) ? "" : dataRow["UserName"].ToString()),
						ConsortiaName = ((dataRow["ConsortiaName"] == null) ? "" : dataRow["ConsortiaName"].ToString()),
						Offer = (int)dataRow["Offer"],
						Skin = ((dataRow["Skin"] == null) ? "" : dataRow["Skin"].ToString()),
						IsBanChat = (bool)dataRow["IsBanChat"],
						ReputeOffer = (int)dataRow["ReputeOffer"],
						ConsortiaRepute = (int)dataRow["ConsortiaRepute"],
						ConsortiaLevel = (int)dataRow["ConsortiaLevel"],
						StoreLevel = (int)dataRow["StoreLevel"],
						ShopLevel = (int)dataRow["ShopLevel"],
						SmithLevel = (int)dataRow["SmithLevel"],
						BufferLevel = (int)dataRow["BufferLevel"],
						ConsortiaHonor = (int)dataRow["ConsortiaHonor"],
						RichesOffer = (int)dataRow["RichesOffer"],
						RichesRob = (int)dataRow["RichesRob"],
						DutyLevel = (int)dataRow["DutyLevel"],
						DutyName = ((dataRow["DutyName"] == null) ? "" : dataRow["DutyName"].ToString()),
						Right = (int)dataRow["Right"],
						ChairmanName = ((dataRow["ChairmanName"] == null) ? "" : dataRow["ChairmanName"].ToString()),
						Win = (int)dataRow["Win"],
						Total = (int)dataRow["Total"],
						Escape = (int)dataRow["Escape"],
						AddDayGP = (int)dataRow["AddDayGP"],
						AddDayOffer = (int)dataRow["AddDayOffer"],
						AddDayAchievementPoint = (int)dataRow["AddDayAchievementPoint"],
						AddWeekGP = (int)dataRow["AddWeekGP"],
						AddWeekOffer = (int)dataRow["AddWeekOffer"],
						AddWeekAchievementPoint = (int)dataRow["AddWeekAchievementPoint"],
						ConsortiaRiches = (int)dataRow["ConsortiaRiches"],
						CheckCount = (int)dataRow["CheckCount"],
						Nimbus = (int)dataRow["Nimbus"],
						GiftToken = (int)dataRow["GiftToken"],
						QuestSite = ((dataRow["QuestSite"] == null) ? new byte[8000] : ((byte[])dataRow["QuestSite"])),
						PvePermission = ((dataRow["PvePermission"] == null) ? "" : dataRow["PvePermission"].ToString()),
						FightLibMission = ((dataRow["FightLibMission"] == DBNull.Value) ? "" : dataRow["FightLibMission"].ToString()),
						FightPower = (int)dataRow["FightPower"],
						TypeVIP = byte.Parse(dataRow["TypeVIP"].ToString()),
						VIPLevel = (int)dataRow["VIPLevel"],
						AchievementPoint = (int)dataRow["AchievementPoint"],
						ApprenticeshipState = (int)dataRow["ApprenticeshipState"],
						MasterID = (int)dataRow["MasterID"],
						GraduatesCount = (int)dataRow["GraduatesCount"],
						MasterOrApprentices = ((dataRow["MasterOrApprentices"] == DBNull.Value) ? "" : dataRow["MasterOrApprentices"].ToString()),
						HonourOfMaster = ((dataRow["HonourOfMaster"] == DBNull.Value) ? "" : dataRow["HonourOfMaster"].ToString()),
						Honor = (string)dataRow["Honor"],
						HonorID = (int)dataRow["HonorID"],
						GiftGP = (int)dataRow["GiftGP"],
						GiftLevel = (int)dataRow["GiftLevel"],
						AddDayGiftGP = (int)dataRow["AddDayGiftGP"],
						AddWeekGiftGP = (int)dataRow["AddWeekGiftGP"],
						IsMarried = (bool)dataRow["IsMarried"],
						SpouseID = (int)dataRow["SpouseID"],
						SpouseName = ((dataRow["SpouseName"] == DBNull.Value) ? "" : dataRow["SpouseName"].ToString()),
						SpaPubGoldRoomLimit = (int)dataRow["SpaPubGoldRoomLimit"],
						SpaPubMoneyRoomLimit = (int)dataRow["SpaPubMoneyRoomLimit"],
						WeeklyScore = (int)dataRow["WeeklyScore"],
						DailyLeagueLastScore = (int)dataRow["DailyLeagueLastScore"],
						EliteScore = (int)dataRow["EliteScore"]
					});
				}
				resultValue = true;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return list.ToArray();
		}

		// Token: 0x060088DF RID: 35039 RVA: 0x002D7720 File Offset: 0x002D5920
		public bool UserChangeSex(int UserId, bool newSex)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserId", UserId),
					new SqlParameter("@Sex", newSex),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_Users_ChangSexByCard", array);
				int num = (int)array[2].Value;
				flag = num == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_Users_ChangSexByCard ", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088E0 RID: 35040 RVA: 0x002D77E0 File Offset: 0x002D59E0
		public ItemInfo[] GetUserItem(int UserID)
		{
			List<ItemInfo> list = new List<ItemInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Items_All", array);
				while (sqlDataReader.Read())
				{
					list.Add(this.InitItem(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060088E1 RID: 35041 RVA: 0x002D78C4 File Offset: 0x002D5AC4
		public ItemInfo[] GetUserBagByType(int UserID, int bagType)
		{
			List<ItemInfo> list = new List<ItemInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[2];
				array[0] = new SqlParameter("@UserID", SqlDbType.Int, 4);
				SqlParameter[] array2 = array;
				array2[0].Value = UserID;
				array2[1] = new SqlParameter("@BagType", bagType);
				this.db.GetReader(ref sqlDataReader, "SP_Users_BagByType", array2);
				while (sqlDataReader.Read())
				{
					list.Add(this.InitItem(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060088E2 RID: 35042 RVA: 0x002D79BC File Offset: 0x002D5BBC
		public List<ItemInfo> GetUserEuqip(int UserID)
		{
			List<ItemInfo> list = new List<ItemInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Items_Equip", array);
				while (sqlDataReader.Read())
				{
					list.Add(this.InitItem(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list;
		}

		// Token: 0x060088E3 RID: 35043 RVA: 0x002D7A9C File Offset: 0x002D5C9C
		public ItemInfo GetUserItemSingle(int itemID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", SqlDbType.Int, 4)
				};
				array[0].Value = itemID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Items_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return this.InitItem(sqlDataReader);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x060088E4 RID: 35044 RVA: 0x002D7B6C File Offset: 0x002D5D6C
		public ItemInfo InitItem(SqlDataReader reader)
		{
			ItemInfo itemInfo = new ItemInfo(ItemMgr.FindItemTemplate((int)reader["TemplateID"]));
			itemInfo.AgilityCompose = (int)reader["AgilityCompose"];
			itemInfo.AttackCompose = (int)reader["AttackCompose"];
			itemInfo.Color = reader["Color"].ToString();
			itemInfo.Count = (int)reader["Count"];
			itemInfo.DefendCompose = (int)reader["DefendCompose"];
			itemInfo.ItemID = (int)reader["ItemID"];
			itemInfo.LuckCompose = (int)reader["LuckCompose"];
			itemInfo.Place = (int)reader["Place"];
			itemInfo.StrengthenLevel = (int)reader["StrengthenLevel"];
			itemInfo.StrengthenExp = (int)reader["StrengthenExp"];
			itemInfo.TemplateID = (int)reader["TemplateID"];
			itemInfo.UserID = (int)reader["UserID"];
			itemInfo.ValidDate = (int)reader["ValidDate"];
			itemInfo.IsExist = (bool)reader["IsExist"];
			itemInfo.IsBinds = (bool)reader["IsBinds"];
			itemInfo.IsUsed = (bool)reader["IsUsed"];
			itemInfo.BeginDate = (DateTime)reader["BeginDate"];
			itemInfo.IsJudge = (bool)reader["IsJudge"];
			itemInfo.BagType = (int)reader["BagType"];
			itemInfo.Skin = reader["Skin"].ToString();
			itemInfo.RemoveDate = (DateTime)reader["RemoveDate"];
			itemInfo.RemoveType = (int)reader["RemoveType"];
			itemInfo.Hole1 = (int)reader["Hole1"];
			itemInfo.Hole2 = (int)reader["Hole2"];
			itemInfo.Hole3 = (int)reader["Hole3"];
			itemInfo.Hole4 = (int)reader["Hole4"];
			itemInfo.Hole5 = (int)reader["Hole5"];
			itemInfo.Hole6 = (int)reader["Hole6"];
			itemInfo.Hole5Level = (int)reader["Hole5Level"];
			itemInfo.Hole5Exp = (int)reader["Hole5Exp"];
			itemInfo.Hole6Level = (int)reader["Hole6Level"];
			itemInfo.Hole6Exp = (int)reader["Hole6Exp"];
			itemInfo.StrengthenTimes = (int)reader["StrengthenTimes"];
			itemInfo.goldBeginTime = (DateTime)reader["goldBeginTime"];
			itemInfo.goldValidDate = (int)reader["goldValidDate"];
			itemInfo.isGold = (bool)reader["isGold"];
			itemInfo.GoldEquip = ItemMgr.FindGoldItemTemplate(itemInfo.TemplateID, itemInfo.GoldValidDate());
			itemInfo.MagicExp = (int)reader["MagicExp"];
			itemInfo.MagicLevel = (int)reader["MagicLevel"];
			itemInfo.MagicSpiritLevel = (int)reader["MagicSpiritLevel"];
			itemInfo.LatentEnergyCurStr = (string)reader["LatentEnergyCurStr"];
			itemInfo.LatentEnergyNewStr = (string)reader["LatentEnergyNewStr"];
			itemInfo.LatentEnergyEndTime = (DateTime)reader["LatentEnergyEndTime"];
			itemInfo.IsDirty = false;
			return itemInfo;
		}

		// Token: 0x060088E5 RID: 35045 RVA: 0x002D7F80 File Offset: 0x002D6180
		public bool AddGoods(ItemInfo item)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[42];
				array[0] = new SqlParameter("@ItemID", item.ItemID);
				array[0].Direction = ParameterDirection.Output;
				array[1] = new SqlParameter("@UserID", item.UserID);
				array[2] = new SqlParameter("@TemplateID", item.TemplateID);
				array[3] = new SqlParameter("@Place", item.Place);
				array[4] = new SqlParameter("@AgilityCompose", item.AgilityCompose);
				array[5] = new SqlParameter("@AttackCompose", item.AttackCompose);
				array[6] = new SqlParameter("@BeginDate", item.BeginDate);
				array[7] = new SqlParameter("@Color", (item.Color == null) ? "" : item.Color);
				array[8] = new SqlParameter("@Count", item.Count);
				array[9] = new SqlParameter("@DefendCompose", item.DefendCompose);
				array[10] = new SqlParameter("@IsBinds", item.IsBinds);
				array[11] = new SqlParameter("@IsExist", item.IsExist);
				array[12] = new SqlParameter("@IsJudge", item.IsJudge);
				array[13] = new SqlParameter("@LuckCompose", item.LuckCompose);
				array[14] = new SqlParameter("@StrengthenLevel", item.StrengthenLevel);
				array[15] = new SqlParameter("@StrengthenExp", item.StrengthenExp);
				array[16] = new SqlParameter("@ValidDate", item.ValidDate);
				array[17] = new SqlParameter("@BagType", item.BagType);
				array[18] = new SqlParameter("@Skin", (item.Skin == null) ? "" : item.Skin);
				array[19] = new SqlParameter("@IsUsed", item.IsUsed);
				array[20] = new SqlParameter("@RemoveDate", item.RemoveDate);
				array[21] = new SqlParameter("@RemoveType", item.RemoveType);
				array[22] = new SqlParameter("@Hole1", item.Hole1);
				array[23] = new SqlParameter("@Hole2", item.Hole2);
				array[24] = new SqlParameter("@Hole3", item.Hole3);
				array[25] = new SqlParameter("@Hole4", item.Hole4);
				array[26] = new SqlParameter("@Hole5", item.Hole5);
				array[27] = new SqlParameter("@Hole6", item.Hole6);
				array[28] = new SqlParameter("@Hole5Level", item.Hole5Level);
				array[29] = new SqlParameter("@Hole5Exp", item.Hole5Exp);
				array[30] = new SqlParameter("@Hole6Level", item.Hole6Level);
				array[31] = new SqlParameter("@Hole6Exp", item.Hole6Exp);
				array[32] = new SqlParameter("@StrengthenTimes", item.StrengthenTimes);
				array[33] = new SqlParameter("@isGold", item.isGold);
				array[34] = new SqlParameter("@goldValidDate", item.goldValidDate);
				array[35] = new SqlParameter("@goldBeginTime", item.goldBeginTime);
				array[36] = new SqlParameter("@MagicExp", item.MagicExp);
				array[37] = new SqlParameter("@MagicLevel", item.MagicLevel);
				array[38] = new SqlParameter("@MagicSpiritLevel", item.MagicSpiritLevel);
				array[39] = new SqlParameter("@LatentEnergyCurStr", item.LatentEnergyCurStr);
				array[40] = new SqlParameter("@LatentEnergyNewStr", item.LatentEnergyNewStr);
				array[41] = new SqlParameter("@LatentEnergyEndTime", item.LatentEnergyEndTime);
				flag = this.db.RunProcedure("SP_Users_Items_Add", array);
				item.ItemID = (int)array[0].Value;
				item.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x060088E6 RID: 35046 RVA: 0x002D8458 File Offset: 0x002D6658
		public bool UpdateGoods(ItemInfo item)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ItemID", item.ItemID),
					new SqlParameter("@UserID", item.UserID),
					new SqlParameter("@TemplateID", item.TemplateID),
					new SqlParameter("@Place", item.Place),
					new SqlParameter("@AgilityCompose", item.AgilityCompose),
					new SqlParameter("@AttackCompose", item.AttackCompose),
					new SqlParameter("@BeginDate", item.BeginDate),
					new SqlParameter("@Color", (item.Color == null) ? "" : item.Color),
					new SqlParameter("@Count", item.Count),
					new SqlParameter("@DefendCompose", item.DefendCompose),
					new SqlParameter("@IsBinds", item.IsBinds),
					new SqlParameter("@IsExist", item.IsExist),
					new SqlParameter("@IsJudge", item.IsJudge),
					new SqlParameter("@LuckCompose", item.LuckCompose),
					new SqlParameter("@StrengthenLevel", item.StrengthenLevel),
					new SqlParameter("@StrengthenExp", item.StrengthenExp),
					new SqlParameter("@ValidDate", item.ValidDate),
					new SqlParameter("@BagType", item.BagType),
					new SqlParameter("@Skin", item.Skin),
					new SqlParameter("@IsUsed", item.IsUsed),
					new SqlParameter("@RemoveDate", item.RemoveDate),
					new SqlParameter("@RemoveType", item.RemoveType),
					new SqlParameter("@Hole1", item.Hole1),
					new SqlParameter("@Hole2", item.Hole2),
					new SqlParameter("@Hole3", item.Hole3),
					new SqlParameter("@Hole4", item.Hole4),
					new SqlParameter("@Hole5", item.Hole5),
					new SqlParameter("@Hole6", item.Hole6),
					new SqlParameter("@Hole5Level", item.Hole5Level),
					new SqlParameter("@Hole5Exp", item.Hole5Exp),
					new SqlParameter("@Hole6Level", item.Hole6Level),
					new SqlParameter("@Hole6Exp", item.Hole6Exp),
					new SqlParameter("@StrengthenTimes", item.StrengthenTimes),
					new SqlParameter("@isGold", item.isGold),
					new SqlParameter("@goldValidDate", item.goldValidDate),
					new SqlParameter("@goldBeginTime", item.goldBeginTime),
					new SqlParameter("@MagicExp", item.MagicExp),
					new SqlParameter("@MagicLevel", item.MagicLevel),
					new SqlParameter("@MagicSpiritLevel", item.MagicSpiritLevel),
					new SqlParameter("@LatentEnergyCurStr", item.LatentEnergyCurStr),
					new SqlParameter("@LatentEnergyNewStr", item.LatentEnergyNewStr),
					new SqlParameter("@LatentEnergyEndTime", item.LatentEnergyEndTime)
				};
				flag = this.db.RunProcedure("SP_Users_Items_Update", array);
				item.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088E7 RID: 35047 RVA: 0x002D88E4 File Offset: 0x002D6AE4
		public bool DeleteGoods(int itemID)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", itemID)
				};
				flag = this.db.RunProcedure("SP_Users_Items_Delete", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088E8 RID: 35048 RVA: 0x002D8964 File Offset: 0x002D6B64
		public BestEquipInfo[] GetCelebByDayBestEquip()
		{
			List<BestEquipInfo> list = new List<BestEquipInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Users_BestEquip");
				while (sqlDataReader.Read())
				{
					list.Add(new BestEquipInfo
					{
						Date = (DateTime)sqlDataReader["RemoveDate"],
						GP = (int)sqlDataReader["GP"],
						Grade = (int)sqlDataReader["Grade"],
						ItemName = ((sqlDataReader["Name"] == null) ? "" : sqlDataReader["Name"].ToString()),
						NickName = ((sqlDataReader["NickName"] == null) ? "" : sqlDataReader["NickName"].ToString()),
						Sex = (bool)sqlDataReader["Sex"],
						Strengthenlevel = (int)sqlDataReader["Strengthenlevel"],
						UserName = ((sqlDataReader["UserName"] == null) ? "" : sqlDataReader["UserName"].ToString())
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060088E9 RID: 35049 RVA: 0x002D8B34 File Offset: 0x002D6D34
		public UserMailInfo InitMail(SqlDataReader reader)
		{
			return new UserMailInfo
			{
				Annex1 = reader["Annex1"].ToString(),
				Annex2 = reader["Annex2"].ToString(),
				Content = reader["Content"].ToString(),
				Gold = (int)reader["Gold"],
				ID = (int)reader["ID"],
				IsExist = (bool)reader["IsExist"],
				Money = (int)reader["Money"],
				Receiver = reader["Receiver"].ToString(),
				ReceiverID = (int)reader["ReceiverID"],
				Sender = reader["Sender"].ToString(),
				SenderID = (int)reader["SenderID"],
				Title = reader["Title"].ToString(),
				Type = (int)reader["Type"],
				ValidDate = (int)reader["ValidDate"],
				IsRead = (bool)reader["IsRead"],
				SendTime = (DateTime)reader["SendTime"],
				Annex1Name = ((reader["Annex1Name"] == null) ? "" : reader["Annex1Name"].ToString()),
				Annex2Name = ((reader["Annex2Name"] == null) ? "" : reader["Annex2Name"].ToString()),
				Annex3 = reader["Annex3"].ToString(),
				Annex4 = reader["Annex4"].ToString(),
				Annex5 = reader["Annex5"].ToString(),
				Annex3Name = ((reader["Annex3Name"] == null) ? "" : reader["Annex3Name"].ToString()),
				Annex4Name = ((reader["Annex4Name"] == null) ? "" : reader["Annex4Name"].ToString()),
				Annex5Name = ((reader["Annex5Name"] == null) ? "" : reader["Annex5Name"].ToString()),
				AnnexRemark = ((reader["AnnexRemark"] == null) ? "" : reader["AnnexRemark"].ToString()),
				GiftToken = (int)reader["GiftToken"]
			};
		}

		// Token: 0x060088EA RID: 35050 RVA: 0x002D8E1C File Offset: 0x002D701C
		public UserMailInfo[] GetMailByUserID(int userID)
		{
			List<UserMailInfo> list = new List<UserMailInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = userID;
				this.db.GetReader(ref sqlDataReader, "SP_Mail_ByUserID", array);
				while (sqlDataReader.Read())
				{
					list.Add(this.InitMail(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060088EB RID: 35051 RVA: 0x002D8F00 File Offset: 0x002D7100
		public UserMailInfo[] GetMailBySenderID(int userID)
		{
			List<UserMailInfo> list = new List<UserMailInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = userID;
				this.db.GetReader(ref sqlDataReader, "SP_Mail_BySenderID", array);
				while (sqlDataReader.Read())
				{
					list.Add(this.InitMail(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x060088EC RID: 35052 RVA: 0x002D8FE4 File Offset: 0x002D71E4
		public UserMailInfo GetMailSingle(int UserID, int mailID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", mailID),
					new SqlParameter("@UserID", UserID)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Mail_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return this.InitMail(sqlDataReader);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x060088ED RID: 35053 RVA: 0x002D90BC File Offset: 0x002D72BC
		public bool SendMail(UserMailInfo mail)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[29];
				array[0] = new SqlParameter("@ID", mail.ID);
				array[0].Direction = ParameterDirection.Output;
				array[1] = new SqlParameter("@Annex1", (mail.Annex1 == null) ? "" : mail.Annex1);
				array[2] = new SqlParameter("@Annex2", (mail.Annex2 == null) ? "" : mail.Annex2);
				array[3] = new SqlParameter("@Content", (mail.Content == null) ? "" : mail.Content);
				array[4] = new SqlParameter("@Gold", mail.Gold);
				array[5] = new SqlParameter("@IsExist", true);
				array[6] = new SqlParameter("@Money", mail.Money);
				array[7] = new SqlParameter("@Receiver", (mail.Receiver == null) ? "" : mail.Receiver);
				array[8] = new SqlParameter("@ReceiverID", mail.ReceiverID);
				array[9] = new SqlParameter("@Sender", (mail.Sender == null) ? "" : mail.Sender);
				array[10] = new SqlParameter("@SenderID", mail.SenderID);
				array[11] = new SqlParameter("@Title", (mail.Title == null) ? "" : mail.Title);
				array[12] = new SqlParameter("@IfDelS", false);
				array[13] = new SqlParameter("@IsDelete", false);
				array[14] = new SqlParameter("@IsDelR", false);
				array[15] = new SqlParameter("@IsRead", false);
				array[16] = new SqlParameter("@SendTime", DateTime.Now);
				array[17] = new SqlParameter("@Type", mail.Type);
				array[18] = new SqlParameter("@Annex1Name", (mail.Annex1Name == null) ? "" : mail.Annex1Name);
				array[19] = new SqlParameter("@Annex2Name", (mail.Annex2Name == null) ? "" : mail.Annex2Name);
				array[20] = new SqlParameter("@Annex3", (mail.Annex3 == null) ? "" : mail.Annex3);
				array[21] = new SqlParameter("@Annex4", (mail.Annex4 == null) ? "" : mail.Annex4);
				array[22] = new SqlParameter("@Annex5", (mail.Annex5 == null) ? "" : mail.Annex5);
				array[23] = new SqlParameter("@Annex3Name", (mail.Annex3Name == null) ? "" : mail.Annex3Name);
				array[24] = new SqlParameter("@Annex4Name", (mail.Annex4Name == null) ? "" : mail.Annex4Name);
				array[25] = new SqlParameter("@Annex5Name", (mail.Annex5Name == null) ? "" : mail.Annex5Name);
				array[26] = new SqlParameter("@ValidDate", mail.ValidDate);
				array[27] = new SqlParameter("@AnnexRemark", (mail.AnnexRemark == null) ? "" : mail.AnnexRemark);
				array[28] = new SqlParameter("@GiftToken", mail.GiftToken);
				flag = this.db.RunProcedure("SP_Mail_Send", array);
				mail.ID = (int)array[0].Value;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x060088EE RID: 35054 RVA: 0x002D94B8 File Offset: 0x002D76B8
		public bool DeleteMail(int UserID, int mailID, out int senderID)
		{
			bool flag = false;
			senderID = 0;
			try
			{
				SqlParameter[] array = new SqlParameter[4];
				array[0] = new SqlParameter("@ID", mailID);
				array[1] = new SqlParameter("@UserID", UserID);
				array[2] = new SqlParameter("@SenderID", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[2].Value = senderID;
				array2[2].Direction = ParameterDirection.InputOutput;
				array2[3] = new SqlParameter("@Result", SqlDbType.Int);
				array2[3].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_Mail_Delete", array2);
				bool flag2 = (int)array2[3].Value == 0;
				if (flag2)
				{
					flag = true;
					senderID = (int)array2[2].Value;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088EF RID: 35055 RVA: 0x002D95B4 File Offset: 0x002D77B4
		public bool UpdateMail(UserMailInfo mail, int oldMoney)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[30];
				array[0] = new SqlParameter("@ID", mail.ID);
				array[1] = new SqlParameter("@Annex1", (mail.Annex1 == null) ? "" : mail.Annex1);
				array[2] = new SqlParameter("@Annex2", (mail.Annex2 == null) ? "" : mail.Annex2);
				array[3] = new SqlParameter("@Content", (mail.Content == null) ? "" : mail.Content);
				array[4] = new SqlParameter("@Gold", mail.Gold);
				array[5] = new SqlParameter("@IsExist", mail.IsExist);
				array[6] = new SqlParameter("@Money", mail.Money);
				array[7] = new SqlParameter("@Receiver", (mail.Receiver == null) ? "" : mail.Receiver);
				array[8] = new SqlParameter("@ReceiverID", mail.ReceiverID);
				array[9] = new SqlParameter("@Sender", (mail.Sender == null) ? "" : mail.Sender);
				array[10] = new SqlParameter("@SenderID", mail.SenderID);
				array[11] = new SqlParameter("@Title", (mail.Title == null) ? "" : mail.Title);
				array[12] = new SqlParameter("@IfDelS", false);
				array[13] = new SqlParameter("@IsDelete", false);
				array[14] = new SqlParameter("@IsDelR", false);
				array[15] = new SqlParameter("@IsRead", mail.IsRead);
				array[16] = new SqlParameter("@SendTime", mail.SendTime);
				array[17] = new SqlParameter("@Type", mail.Type);
				array[18] = new SqlParameter("@OldMoney", oldMoney);
				array[19] = new SqlParameter("@ValidDate", mail.ValidDate);
				array[20] = new SqlParameter("@Annex1Name", mail.Annex1Name);
				array[21] = new SqlParameter("@Annex2Name", mail.Annex2Name);
				array[22] = new SqlParameter("@Result", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[22].Direction = ParameterDirection.ReturnValue;
				array2[23] = new SqlParameter("@Annex3", (mail.Annex3 == null) ? "" : mail.Annex3);
				array2[24] = new SqlParameter("@Annex4", (mail.Annex4 == null) ? "" : mail.Annex4);
				array2[25] = new SqlParameter("@Annex5", (mail.Annex5 == null) ? "" : mail.Annex5);
				array2[26] = new SqlParameter("@Annex3Name", (mail.Annex3Name == null) ? "" : mail.Annex3Name);
				array2[27] = new SqlParameter("@Annex4Name", (mail.Annex4Name == null) ? "" : mail.Annex4Name);
				array2[28] = new SqlParameter("@Annex5Name", (mail.Annex5Name == null) ? "" : mail.Annex5Name);
				array2[29] = new SqlParameter("GiftToken", mail.GiftToken);
				this.db.RunProcedure("SP_Mail_Update", array2);
				int num = (int)array2[22].Value;
				flag = num == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x060088F0 RID: 35056 RVA: 0x002D99A0 File Offset: 0x002D7BA0
		public bool CancelPaymentMail(int userid, int mailID, ref int senderID)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[4];
				array[0] = new SqlParameter("@userid", userid);
				array[1] = new SqlParameter("@mailID", mailID);
				array[2] = new SqlParameter("@senderID", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[2].Value = senderID;
				array2[2].Direction = ParameterDirection.InputOutput;
				array2[3] = new SqlParameter("@Result", SqlDbType.Int);
				array2[3].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Mail_PaymentCancel", array2);
				int num = (int)array2[3].Value;
				flag = num == 0;
				bool flag2 = flag;
				if (flag2)
				{
					senderID = (int)array2[2].Value;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088F1 RID: 35057 RVA: 0x002D9AA0 File Offset: 0x002D7CA0
		public bool ScanMail(ref string noticeUserID)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@NoticeUserID", SqlDbType.NVarChar, 4000)
				};
				array[0].Direction = ParameterDirection.Output;
				this.db.RunProcedure("SP_Mail_Scan", array);
				noticeUserID = array[0].Value.ToString();
				flag = true;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x060088F2 RID: 35058 RVA: 0x002D9B4C File Offset: 0x002D7D4C
		public bool SendMailAndItem(UserMailInfo mail, ItemInfo item, ref int returnValue)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[34];
				array[0] = new SqlParameter("@ItemID", item.ItemID);
				array[1] = new SqlParameter("@UserID", item.UserID);
				array[2] = new SqlParameter("@TemplateID", item.TemplateID);
				array[3] = new SqlParameter("@Place", item.Place);
				array[4] = new SqlParameter("@AgilityCompose", item.AgilityCompose);
				array[5] = new SqlParameter("@AttackCompose", item.AttackCompose);
				array[6] = new SqlParameter("@BeginDate", item.BeginDate);
				array[7] = new SqlParameter("@Color", (item.Color == null) ? "" : item.Color);
				array[8] = new SqlParameter("@Count", item.Count);
				array[9] = new SqlParameter("@DefendCompose", item.DefendCompose);
				array[10] = new SqlParameter("@IsBinds", item.IsBinds);
				array[11] = new SqlParameter("@IsExist", item.IsExist);
				array[12] = new SqlParameter("@IsJudge", item.IsJudge);
				array[13] = new SqlParameter("@LuckCompose", item.LuckCompose);
				array[14] = new SqlParameter("@StrengthenLevel", item.StrengthenLevel);
				array[15] = new SqlParameter("@ValidDate", item.ValidDate);
				array[16] = new SqlParameter("@BagType", item.BagType);
				array[17] = new SqlParameter("@ID", mail.ID);
				SqlParameter[] array2 = array;
				array2[17].Direction = ParameterDirection.Output;
				array2[18] = new SqlParameter("@Annex1", (mail.Annex1 == null) ? "" : mail.Annex1);
				array2[19] = new SqlParameter("@Annex2", (mail.Annex2 == null) ? "" : mail.Annex2);
				array2[20] = new SqlParameter("@Content", (mail.Content == null) ? "" : mail.Content);
				array2[21] = new SqlParameter("@Gold", mail.Gold);
				array2[22] = new SqlParameter("@Money", mail.Money);
				array2[23] = new SqlParameter("@Receiver", (mail.Receiver == null) ? "" : mail.Receiver);
				array2[24] = new SqlParameter("@ReceiverID", mail.ReceiverID);
				array2[25] = new SqlParameter("@Sender", (mail.Sender == null) ? "" : mail.Sender);
				array2[26] = new SqlParameter("@SenderID", mail.SenderID);
				array2[27] = new SqlParameter("@Title", (mail.Title == null) ? "" : mail.Title);
				array2[28] = new SqlParameter("@IfDelS", false);
				array2[29] = new SqlParameter("@IsDelete", false);
				array2[30] = new SqlParameter("@IsDelR", false);
				array2[31] = new SqlParameter("@IsRead", false);
				array2[32] = new SqlParameter("@SendTime", DateTime.Now);
				array2[33] = new SqlParameter("@Result", SqlDbType.Int);
				array2[33].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_Admin_SendUserItem", array2);
				returnValue = (int)array2[33].Value;
				flag = returnValue == 0;
				bool flag2 = flag;
				if (flag2)
				{
					using (CenterServiceClient centerServiceClient = new CenterServiceClient())
					{
						centerServiceClient.MailNotice(mail.ReceiverID, this.AreaID);
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x060088F3 RID: 35059 RVA: 0x002D9FC8 File Offset: 0x002D81C8
		public bool SendMailAndMoney(UserMailInfo mail, ref int returnValue)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[18];
				array[0] = new SqlParameter("@ID", mail.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@Annex1", (mail.Annex1 == null) ? "" : mail.Annex1);
				array2[2] = new SqlParameter("@Annex2", (mail.Annex2 == null) ? "" : mail.Annex2);
				array2[3] = new SqlParameter("@Content", (mail.Content == null) ? "" : mail.Content);
				array2[4] = new SqlParameter("@Gold", mail.Gold);
				array2[5] = new SqlParameter("@IsExist", true);
				array2[6] = new SqlParameter("@Money", mail.Money);
				array2[7] = new SqlParameter("@Receiver", (mail.Receiver == null) ? "" : mail.Receiver);
				array2[8] = new SqlParameter("@ReceiverID", mail.ReceiverID);
				array2[9] = new SqlParameter("@Sender", (mail.Sender == null) ? "" : mail.Sender);
				array2[10] = new SqlParameter("@SenderID", mail.SenderID);
				array2[11] = new SqlParameter("@Title", (mail.Title == null) ? "" : mail.Title);
				array2[12] = new SqlParameter("@IfDelS", false);
				array2[13] = new SqlParameter("@IsDelete", false);
				array2[14] = new SqlParameter("@IsDelR", false);
				array2[15] = new SqlParameter("@IsRead", false);
				array2[16] = new SqlParameter("@SendTime", DateTime.Now);
				array2[17] = new SqlParameter("@Result", SqlDbType.Int);
				array2[17].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_Admin_SendUserMoney", array2);
				returnValue = (int)array2[17].Value;
				flag = returnValue == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x060088F4 RID: 35060 RVA: 0x002DA258 File Offset: 0x002D8458
		public int SendMailAndItem(string title, string content, int UserID, int templateID, int count, int validDate, int gold, int money, int StrengthenLevel, int AttackCompose, int DefendCompose, int AgilityCompose, int LuckCompose, bool isBinds)
		{
			UserMailInfo userMailInfo = new UserMailInfo();
			userMailInfo.Annex1 = "";
			userMailInfo.Content = title;
			userMailInfo.Gold = gold;
			userMailInfo.Money = money;
			userMailInfo.Receiver = "";
			userMailInfo.ReceiverID = UserID;
			userMailInfo.Sender = "疯狂DDTank管理员";
			userMailInfo.SenderID = 0;
			userMailInfo.Title = content;
			ItemInfo itemInfo = new ItemInfo(null);
			itemInfo.AgilityCompose = AgilityCompose;
			itemInfo.AttackCompose = AttackCompose;
			itemInfo.BeginDate = DateTime.Now;
			itemInfo.Color = "";
			itemInfo.DefendCompose = DefendCompose;
			itemInfo.IsDirty = false;
			itemInfo.IsExist = true;
			itemInfo.IsJudge = true;
			itemInfo.LuckCompose = LuckCompose;
			itemInfo.StrengthenLevel = StrengthenLevel;
			itemInfo.TemplateID = templateID;
			itemInfo.ValidDate = validDate;
			itemInfo.Count = count;
			itemInfo.IsBinds = isBinds;
			int num = 1;
			this.SendMailAndItem(userMailInfo, itemInfo, ref num);
			return num;
		}

		// Token: 0x060088F5 RID: 35061 RVA: 0x002DA35C File Offset: 0x002D855C
		public int SendMailAndItemByUserName(string title, string content, string userName, int templateID, int count, int validDate, int gold, int money, int StrengthenLevel, int AttackCompose, int DefendCompose, int AgilityCompose, int LuckCompose, bool isBinds)
		{
			PlayerInfo userSingleByUserName = this.GetUserSingleByUserName(userName);
			bool flag = userSingleByUserName != null;
			int num;
			if (flag)
			{
				num = this.SendMailAndItem(title, content, userSingleByUserName.ID, templateID, count, validDate, gold, money, StrengthenLevel, AttackCompose, DefendCompose, AgilityCompose, LuckCompose, isBinds);
			}
			else
			{
				num = 2;
			}
			return num;
		}

		// Token: 0x060088F6 RID: 35062 RVA: 0x002DA3A8 File Offset: 0x002D85A8
		public int SendMailAndItemByNickName(string title, string content, string NickName, int templateID, int count, int validDate, int gold, int money, int StrengthenLevel, int AttackCompose, int DefendCompose, int AgilityCompose, int LuckCompose, bool isBinds)
		{
			PlayerInfo userSingleByNickName = this.GetUserSingleByNickName(NickName);
			bool flag = userSingleByNickName != null;
			int num;
			if (flag)
			{
				num = this.SendMailAndItem(title, content, userSingleByNickName.ID, templateID, count, validDate, gold, money, StrengthenLevel, AttackCompose, DefendCompose, AgilityCompose, LuckCompose, isBinds);
			}
			else
			{
				num = 2;
			}
			return num;
		}

		// Token: 0x060088F7 RID: 35063 RVA: 0x002DA3F4 File Offset: 0x002D85F4
		public int SendMailAndItem(string title, string content, int userID, int gold, int money, int giftToken, string param)
		{
			int num = 1;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@Title", title),
					new SqlParameter("@Content", content),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Gold", gold),
					new SqlParameter("@Money", money),
					new SqlParameter("@GiftToken", giftToken),
					new SqlParameter("@Param", param),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[7].Direction = ParameterDirection.ReturnValue;
				bool flag = this.db.RunProcedure("SP_Admin_SendAllItem", array);
				num = (int)array[7].Value;
				bool flag2 = num == 0;
				if (flag2)
				{
					using (CenterServiceClient centerServiceClient = new CenterServiceClient())
					{
						centerServiceClient.MailNotice(userID, this.AreaID);
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return num;
		}

		// Token: 0x060088F8 RID: 35064 RVA: 0x002DA574 File Offset: 0x002D8774
		public int SendMailAndItemByUserName(string title, string content, string userName, int gold, int money, int giftToken, string param)
		{
			PlayerInfo userSingleByUserName = this.GetUserSingleByUserName(userName);
			bool flag = userSingleByUserName != null;
			int num;
			if (flag)
			{
				num = this.SendMailAndItem(title, content, userSingleByUserName.ID, gold, money, giftToken, param);
			}
			else
			{
				num = 2;
			}
			return num;
		}

		// Token: 0x060088F9 RID: 35065 RVA: 0x002DA5B4 File Offset: 0x002D87B4
		public int SendMailAndItemByNickName(string title, string content, string nickName, int gold, int money, int giftToken, string param)
		{
			PlayerInfo userSingleByNickName = this.GetUserSingleByNickName(nickName);
			bool flag = userSingleByNickName != null;
			int num;
			if (flag)
			{
				num = this.SendMailAndItem(title, content, userSingleByNickName.ID, gold, money, giftToken, param);
			}
			else
			{
				num = 2;
			}
			return num;
		}

		// Token: 0x060088FA RID: 35066 RVA: 0x002DA5F4 File Offset: 0x002D87F4
		public bool SendItemsToMail(List<ItemInfo> items, int playerId, string content, string title)
		{
			return this.SendItemsToMail(items, playerId, content, title, 8);
		}

		// Token: 0x060088FB RID: 35067 RVA: 0x002DA614 File Offset: 0x002D8814
		public bool SendItemsToMail(List<ItemInfo> items, int playerId, string content, string title, int type)
		{
			bool flag = false;
			List<ItemInfo> list = new List<ItemInfo>();
			foreach (ItemInfo itemInfo in items)
			{
				bool flag2 = itemInfo.Template.MaxCount == 1 && itemInfo.Count > 1;
				if (flag2)
				{
					for (int i = 0; i < itemInfo.Count; i++)
					{
						ItemInfo itemInfo2 = ItemInfo.CloneFromTemplate(itemInfo.Template, itemInfo);
						itemInfo2.Count = 1;
						list.Add(itemInfo2);
					}
				}
				else
				{
					list.Add(itemInfo);
				}
			}
			for (int j = 0; j < items.Count; j += 5)
			{
				UserMailInfo userMailInfo = new UserMailInfo
				{
					Title = ((title != null) ? title : LanguageMgr.GetTranslation("Game.Server.GameUtils.Title", Array.Empty<object>())),
					Gold = 0,
					IsExist = true,
					Money = 0,
					Receiver = "",
					ReceiverID = playerId,
					Sender = "疯狂DDTank管理员",
					SenderID = 0,
					Type = type,
					GiftToken = 0
				};
				List<ItemInfo> list2 = new List<ItemInfo>();
				StringBuilder stringBuilder = new StringBuilder();
				StringBuilder stringBuilder2 = new StringBuilder();
				stringBuilder.Append(LanguageMgr.GetTranslation("Game.Server.GameUtils.CommonBag.AnnexRemark", Array.Empty<object>()));
				content = ((content != null) ? LanguageMgr.GetTranslation(content, Array.Empty<object>()) : "");
				int num = j;
				bool flag3 = items.Count > num;
				if (flag3)
				{
					ItemInfo itemInfo3 = items[num];
					bool flag4 = itemInfo3.ItemID == 0;
					if (flag4)
					{
						this.AddGoods(itemInfo3);
					}
					else
					{
						list2.Add(itemInfo3);
					}
					bool flag5 = title == null;
					if (flag5)
					{
						userMailInfo.Title = itemInfo3.Template.Name;
					}
					userMailInfo.Annex1 = itemInfo3.ItemID.ToString();
					userMailInfo.Annex1Name = itemInfo3.Template.Name;
					stringBuilder.Append(string.Concat(new string[]
					{
						"1、",
						userMailInfo.Annex1Name,
						"x",
						itemInfo3.Count.ToString(),
						";"
					}));
					stringBuilder2.Append(string.Concat(new string[]
					{
						"1、",
						userMailInfo.Annex1Name,
						"x",
						itemInfo3.Count.ToString(),
						";"
					}));
				}
				num = j + 1;
				bool flag6 = items.Count > num;
				if (flag6)
				{
					ItemInfo itemInfo4 = items[num];
					bool flag7 = itemInfo4.ItemID == 0;
					if (flag7)
					{
						this.AddGoods(itemInfo4);
					}
					else
					{
						list2.Add(itemInfo4);
					}
					userMailInfo.Annex2 = itemInfo4.ItemID.ToString();
					userMailInfo.Annex2Name = itemInfo4.Template.Name;
					stringBuilder.Append(string.Concat(new string[]
					{
						"2、",
						userMailInfo.Annex2Name,
						"x",
						itemInfo4.Count.ToString(),
						";"
					}));
					stringBuilder2.Append(string.Concat(new string[]
					{
						"2、",
						userMailInfo.Annex2Name,
						"x",
						itemInfo4.Count.ToString(),
						";"
					}));
				}
				num = j + 2;
				bool flag8 = items.Count > num;
				if (flag8)
				{
					ItemInfo itemInfo5 = items[num];
					bool flag9 = itemInfo5.ItemID == 0;
					if (flag9)
					{
						this.AddGoods(itemInfo5);
					}
					else
					{
						list2.Add(itemInfo5);
					}
					userMailInfo.Annex3 = itemInfo5.ItemID.ToString();
					userMailInfo.Annex3Name = itemInfo5.Template.Name;
					stringBuilder.Append(string.Concat(new string[]
					{
						"3、",
						userMailInfo.Annex3Name,
						"x",
						itemInfo5.Count.ToString(),
						";"
					}));
					stringBuilder2.Append(string.Concat(new string[]
					{
						"3、",
						userMailInfo.Annex3Name,
						"x",
						itemInfo5.Count.ToString(),
						";"
					}));
				}
				num = j + 3;
				bool flag10 = items.Count > num;
				if (flag10)
				{
					ItemInfo itemInfo6 = items[num];
					bool flag11 = itemInfo6.ItemID == 0;
					if (flag11)
					{
						this.AddGoods(itemInfo6);
					}
					else
					{
						list2.Add(itemInfo6);
					}
					userMailInfo.Annex4 = itemInfo6.ItemID.ToString();
					userMailInfo.Annex4Name = itemInfo6.Template.Name;
					stringBuilder.Append(string.Concat(new string[]
					{
						"4、",
						userMailInfo.Annex4Name,
						"x",
						itemInfo6.Count.ToString(),
						";"
					}));
					stringBuilder2.Append(string.Concat(new string[]
					{
						"4、",
						userMailInfo.Annex4Name,
						"x",
						itemInfo6.Count.ToString(),
						";"
					}));
				}
				num = j + 4;
				bool flag12 = items.Count > num;
				if (flag12)
				{
					ItemInfo itemInfo7 = items[num];
					bool flag13 = itemInfo7.ItemID == 0;
					if (flag13)
					{
						this.AddGoods(itemInfo7);
					}
					else
					{
						list2.Add(itemInfo7);
					}
					userMailInfo.Annex5 = itemInfo7.ItemID.ToString();
					userMailInfo.Annex5Name = itemInfo7.Template.Name;
					stringBuilder.Append(string.Concat(new string[]
					{
						"5、",
						userMailInfo.Annex5Name,
						"x",
						itemInfo7.Count.ToString(),
						";"
					}));
					stringBuilder2.Append(string.Concat(new string[]
					{
						"5、",
						userMailInfo.Annex5Name,
						"x",
						itemInfo7.Count.ToString(),
						";"
					}));
				}
				userMailInfo.AnnexRemark = stringBuilder.ToString();
				bool flag14 = content == null && stringBuilder2.ToString() == null;
				if (flag14)
				{
					userMailInfo.Content = LanguageMgr.GetTranslation("Game.Server.GameUtils.Content", Array.Empty<object>());
				}
				else
				{
					bool flag15 = content != "";
					if (flag15)
					{
						userMailInfo.Content = content;
					}
					else
					{
						userMailInfo.Content = stringBuilder2.ToString();
					}
				}
				flag = this.SendMail(userMailInfo);
			}
			return flag;
		}

		// Token: 0x060088FC RID: 35068 RVA: 0x002DAD88 File Offset: 0x002D8F88
		public Dictionary<int, int> GetFriendsIDAll(int UserID)
		{
			Dictionary<int, int> dictionary = new Dictionary<int, int>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Friends_All", array);
				while (sqlDataReader.Read())
				{
					bool flag = !dictionary.ContainsKey((int)sqlDataReader["FriendID"]);
					if (flag)
					{
						dictionary.Add((int)sqlDataReader["FriendID"], (int)sqlDataReader["Relation"]);
					}
					else
					{
						dictionary[(int)sqlDataReader["FriendID"]] = (int)sqlDataReader["Relation"];
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return dictionary;
		}

		// Token: 0x060088FD RID: 35069 RVA: 0x002DAECC File Offset: 0x002D90CC
		public bool AddFriends(FriendInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@AddDate", DateTime.Now),
					new SqlParameter("@FriendID", info.FriendID),
					new SqlParameter("@IsExist", true),
					new SqlParameter("@Remark", (info.Remark == null) ? "" : info.Remark),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@Relation", info.Relation)
				};
				flag = this.db.RunProcedure("SP_Users_Friends_Add", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x060088FE RID: 35070 RVA: 0x002DAFF4 File Offset: 0x002D91F4
		public bool DeleteFriends(int UserID, int FriendID)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", FriendID),
					new SqlParameter("@UserID", UserID)
				};
				flag = this.db.RunProcedure("SP_Users_Friends_Delete", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x060088FF RID: 35071 RVA: 0x002DB084 File Offset: 0x002D9284
		public FriendInfo[] GetFriendsAll(int UserID)
		{
			List<FriendInfo> list = new List<FriendInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Friends", array);
				while (sqlDataReader.Read())
				{
					list.Add(new FriendInfo
					{
						AddDate = (DateTime)sqlDataReader["AddDate"],
						Colors = ((sqlDataReader["Colors"] == null) ? "" : sqlDataReader["Colors"].ToString()),
						FriendID = (int)sqlDataReader["FriendID"],
						Grade = (int)sqlDataReader["Grade"],
						Hide = (int)sqlDataReader["Hide"],
						ID = (int)sqlDataReader["ID"],
						IsExist = (bool)sqlDataReader["IsExist"],
						NickName = ((sqlDataReader["NickName"] == null) ? "" : sqlDataReader["NickName"].ToString()),
						Remark = ((sqlDataReader["Remark"] == null) ? "" : sqlDataReader["Remark"].ToString()),
						Sex = (((bool)sqlDataReader["Sex"]) ? 1 : 0),
						State = (int)sqlDataReader["State"],
						Style = ((sqlDataReader["Style"] == null) ? "" : sqlDataReader["Style"].ToString()),
						UserID = (int)sqlDataReader["UserID"],
						ConsortiaName = ((sqlDataReader["ConsortiaName"] == null) ? "" : sqlDataReader["ConsortiaName"].ToString()),
						Offer = (int)sqlDataReader["Offer"],
						Win = (int)sqlDataReader["Win"],
						Total = (int)sqlDataReader["Total"],
						Escape = (int)sqlDataReader["Escape"],
						Relation = (int)sqlDataReader["Relation"],
						Repute = (int)sqlDataReader["Repute"],
						UserName = ((sqlDataReader["UserName"] == null) ? "" : sqlDataReader["UserName"].ToString()),
						DutyName = ((sqlDataReader["DutyName"] == null) ? "" : sqlDataReader["DutyName"].ToString()),
						Nimbus = (int)sqlDataReader["Nimbus"],
						AchievementPoint = (int)sqlDataReader["AchievementPoint"],
						Honor = ((sqlDataReader["Rank"] == null) ? "" : sqlDataReader["Rank"].ToString()),
						FightPower = (int)sqlDataReader["FightPower"],
						ApprenticeshipState = (int)sqlDataReader["ApprenticeshipState"],
						TypeVIP = (int)sqlDataReader["TypeVIP"],
						VIPLevel = (int)sqlDataReader["VIPLevel"],
						IsMarried = (bool)sqlDataReader["IsMarried"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008900 RID: 35072 RVA: 0x002DB4E0 File Offset: 0x002D96E0
		public ArrayList GetFriendsGood(string UserName)
		{
			ArrayList arrayList = new ArrayList();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserName", SqlDbType.VarChar)
				};
				array[0].Value = UserName;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Friends_Good", array);
				while (sqlDataReader.Read())
				{
					arrayList.Add((sqlDataReader["UserName"] == null) ? "" : sqlDataReader["UserName"].ToString());
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return arrayList;
		}

		// Token: 0x06008901 RID: 35073 RVA: 0x002DB5D8 File Offset: 0x002D97D8
		public FriendInfo[] GetFriendsBbs(string condictArray)
		{
			List<FriendInfo> list = new List<FriendInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@SearchUserName", SqlDbType.NVarChar, 4000)
				};
				array[0].Value = condictArray;
				this.db.GetReader(ref sqlDataReader, "SP_Users_FriendsBbs", array);
				while (sqlDataReader.Read())
				{
					list.Add(new FriendInfo
					{
						NickName = ((sqlDataReader["NickName"] == null) ? "" : sqlDataReader["NickName"].ToString()),
						UserID = (int)sqlDataReader["UserID"],
						UserName = ((sqlDataReader["UserName"] == null) ? "" : sqlDataReader["UserName"].ToString()),
						IsExist = ((int)sqlDataReader["UserID"] > 0)
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008902 RID: 35074 RVA: 0x002DB764 File Offset: 0x002D9964
		public bool UpdateFriends(int UserID, int FriendID, string Remark)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", FriendID),
					new SqlParameter("@UserID", UserID),
					new SqlParameter("@Remark", Remark)
				};
				flag = this.db.RunProcedure("SP_Users_Friends_Remark", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008903 RID: 35075 RVA: 0x002DB804 File Offset: 0x002D9A04
		public QuestDataInfo[] GetUserQuest(int userID)
		{
			List<QuestDataInfo> list = new List<QuestDataInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = userID;
				this.db.GetReader(ref sqlDataReader, "SP_QuestData_All", array);
				while (sqlDataReader.Read())
				{
					list.Add(new QuestDataInfo
					{
						CompletedDate = (DateTime)sqlDataReader["CompletedDate"],
						IsComplete = (bool)sqlDataReader["IsComplete"],
						Condition1 = (int)sqlDataReader["Condition1"],
						Condition2 = (int)sqlDataReader["Condition2"],
						Condition3 = (int)sqlDataReader["Condition3"],
						Condition4 = (int)sqlDataReader["Condition4"],
						Condition5 = (int)sqlDataReader["Condition5"],
						Condition6 = (int)sqlDataReader["Condition5"],
						Condition7 = (int)sqlDataReader["Condition7"],
						Condition8 = (int)sqlDataReader["Condition8"],
						QuestID = (int)sqlDataReader["QuestID"],
						UserID = (int)sqlDataReader["UserId"],
						IsExist = (bool)sqlDataReader["IsExist"],
						RandDobule = (int)sqlDataReader["RandDobule"],
						RepeatFinish = (int)sqlDataReader["RepeatFinish"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008904 RID: 35076 RVA: 0x002DBA64 File Offset: 0x002D9C64
		public bool UpdateDbQuestDataInfo(QuestDataInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@QuestID", info.QuestID),
					new SqlParameter("@CompletedDate", info.CompletedDate),
					new SqlParameter("@IsComplete", info.IsComplete),
					new SqlParameter("@Condition1", info.Condition1),
					new SqlParameter("@Condition2", info.Condition2),
					new SqlParameter("@Condition3", info.Condition3),
					new SqlParameter("@Condition4", info.Condition4),
					new SqlParameter("@Condition5", info.Condition5),
					new SqlParameter("@Condition6", info.Condition6),
					new SqlParameter("@Condition7", info.Condition7),
					new SqlParameter("@Condition8", info.Condition8),
					new SqlParameter("@IsExist", info.IsExist),
					new SqlParameter("@RepeatFinish", info.RepeatFinish),
					new SqlParameter("@RandDobule", info.RandDobule)
				};
				flag = this.db.RunProcedure("SP_QuestData_Add", array);
				info.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008905 RID: 35077 RVA: 0x002DBC70 File Offset: 0x002D9E70
		public UserBufferInfo[] GetUserBuffer(int userID)
		{
			List<UserBufferInfo> list = new List<UserBufferInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = userID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Buff_All", array);
				while (sqlDataReader.Read())
				{
					list.Add(new UserBufferInfo
					{
						BeginDate = (DateTime)sqlDataReader["BeginDate"],
						Data = ((sqlDataReader["Data"] == null) ? "" : sqlDataReader["Data"].ToString()),
						Type = (int)sqlDataReader["Type"],
						UserID = (int)sqlDataReader["UserID"],
						ValidDate = (int)sqlDataReader["ValidDate"],
						ValidCount = (int)sqlDataReader["ValidCount"],
						Value = (int)sqlDataReader["Value"],
						IsExist = (bool)sqlDataReader["IsExist"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						IsDirty = false
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008906 RID: 35078 RVA: 0x002DBE60 File Offset: 0x002DA060
		public ConsortiaBufferInfo[] GetUserConsortiaBuffer(int ConsortiaID)
		{
			List<ConsortiaBufferInfo> list = new List<ConsortiaBufferInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ConsortiaID", SqlDbType.Int, 4)
				};
				array[0].Value = ConsortiaID;
				this.db.GetReader(ref sqlDataReader, "SP_User_Consortia_Buff_All", array);
				while (sqlDataReader.Read())
				{
					list.Add(new ConsortiaBufferInfo
					{
						ConsortiaID = (int)sqlDataReader["ConsortiaID"],
						BufferID = (int)sqlDataReader["BufferID"],
						IsOpen = (bool)sqlDataReader["IsOpen"],
						BeginDate = (DateTime)sqlDataReader["BeginDate"],
						ValidDate = (int)sqlDataReader["ValidDate"],
						Type = (int)sqlDataReader["Type"],
						Value = (int)sqlDataReader["Value"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init SP_User_Consortia_Buff_All", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008907 RID: 35079 RVA: 0x002DC008 File Offset: 0x002DA208
		public bool SaveBuffer(UserBufferInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@Type", info.Type),
					new SqlParameter("@BeginDate", info.BeginDate),
					new SqlParameter("@Data", (info.Data == null) ? "" : info.Data),
					new SqlParameter("@IsExist", info.IsExist),
					new SqlParameter("@ValidDate", info.ValidDate),
					new SqlParameter("@ValidCount", info.ValidCount),
					new SqlParameter("@Value", info.Value),
					new SqlParameter("@TemplateID", info.TemplateID)
				};
				flag = this.db.RunProcedure("SP_Users_Buff_Add", array);
				info.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008908 RID: 35080 RVA: 0x002DC188 File Offset: 0x002DA388
		public bool AddChargeMoney(string chargeID, string userName, int money, string payWay, decimal needMoney, out int userID, ref int isResult, DateTime date, string IP, string nickName)
		{
			bool flag = false;
			userID = 0;
			try
			{
				SqlParameter[] array = new SqlParameter[10];
				array[0] = new SqlParameter("@ChargeID", chargeID);
				array[1] = new SqlParameter("@UserName", userName);
				array[2] = new SqlParameter("@Money", money);
				array[3] = new SqlParameter("@Date", date.ToString("yyyy-MM-dd HH:mm:ss"));
				array[4] = new SqlParameter("@PayWay", payWay);
				array[5] = new SqlParameter("@NeedMoney", needMoney);
				array[6] = new SqlParameter("@UserID", userID);
				SqlParameter[] array2 = array;
				array2[6].Direction = ParameterDirection.InputOutput;
				array2[7] = new SqlParameter("@Result", SqlDbType.Int);
				array2[7].Direction = ParameterDirection.ReturnValue;
				array2[8] = new SqlParameter("@IP", IP);
				array2[9] = new SqlParameter("@NickName", nickName);
				flag = this.db.RunProcedure("SP_Charge_Money_Add", array2);
				userID = (int)array2[6].Value;
				isResult = (int)array2[7].Value;
				flag = isResult == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008909 RID: 35081 RVA: 0x002DC2EC File Offset: 0x002DA4EC
		public bool ChargeToUser(string userName, ref int money, string nickName)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[3];
				array[0] = new SqlParameter("@UserName", userName);
				array[1] = new SqlParameter("@money", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[1].Direction = ParameterDirection.Output;
				array2[2] = new SqlParameter("@NickName", nickName);
				flag = this.db.RunProcedure("SP_Charge_To_User", array2);
				money = (int)array2[1].Value;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600890A RID: 35082 RVA: 0x002DC39C File Offset: 0x002DA59C
		public AuctionInfo GetAuctionSingle(int auctionID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@AuctionID", auctionID)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Auction_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return this.InitAuctionInfo(sqlDataReader);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x0600890B RID: 35083 RVA: 0x002DC464 File Offset: 0x002DA664
		public bool AddAuction(AuctionInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[17];
				array[0] = new SqlParameter("@AuctionID", info.AuctionID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@AuctioneerID", info.AuctioneerID);
				array2[2] = new SqlParameter("@AuctioneerName", (info.AuctioneerName == null) ? "" : info.AuctioneerName);
				array2[3] = new SqlParameter("@BeginDate", info.BeginDate);
				array2[4] = new SqlParameter("@BuyerID", info.BuyerID);
				array2[5] = new SqlParameter("@BuyerName", (info.BuyerName == null) ? "" : info.BuyerName);
				array2[6] = new SqlParameter("@IsExist", info.IsExist);
				array2[7] = new SqlParameter("@ItemID", info.ItemID);
				array2[8] = new SqlParameter("@Mouthful", info.Mouthful);
				array2[9] = new SqlParameter("@PayType", info.PayType);
				array2[10] = new SqlParameter("@Price", info.Price);
				array2[11] = new SqlParameter("@Rise", info.Rise);
				array2[12] = new SqlParameter("@ValidDate", info.ValidDate);
				array2[13] = new SqlParameter("@TemplateID", info.TemplateID);
				array2[14] = new SqlParameter("Name", info.Name);
				array2[15] = new SqlParameter("Category", info.Category);
				array2[16] = new SqlParameter("Random", info.Random);
				flag = this.db.RunProcedure("SP_Auction_Add", array2);
				info.AuctionID = (int)array2[0].Value;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600890C RID: 35084 RVA: 0x002DC6C8 File Offset: 0x002DA8C8
		public bool UpdateAuction(AuctionInfo info, double Cess)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[17];
				array[0] = new SqlParameter("@AuctionID", info.AuctionID);
				array[1] = new SqlParameter("@AuctioneerID", info.AuctioneerID);
				array[2] = new SqlParameter("@AuctioneerName", (info.AuctioneerName == null) ? "" : info.AuctioneerName);
				array[3] = new SqlParameter("@BeginDate", info.BeginDate);
				array[4] = new SqlParameter("@BuyerID", info.BuyerID);
				array[5] = new SqlParameter("@BuyerName", (info.BuyerName == null) ? "" : info.BuyerName);
				array[6] = new SqlParameter("@IsExist", info.IsExist);
				array[7] = new SqlParameter("@ItemID", info.ItemID);
				array[8] = new SqlParameter("@Mouthful", info.Mouthful);
				array[9] = new SqlParameter("@PayType", info.PayType);
				array[10] = new SqlParameter("@Price", info.Price);
				array[11] = new SqlParameter("@Rise", info.Rise);
				array[12] = new SqlParameter("@ValidDate", info.ValidDate);
				array[13] = new SqlParameter("Name", info.Name);
				array[14] = new SqlParameter("Category", info.Category);
				array[15] = new SqlParameter("@Result", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[15].Direction = ParameterDirection.ReturnValue;
				array2[16] = new SqlParameter("@Cess", Cess);
				this.db.RunProcedure("SP_Auction_Update", array2);
				int num = (int)array2[15].Value;
				flag = num == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600890D RID: 35085 RVA: 0x002DC920 File Offset: 0x002DAB20
		public bool DeleteAuction(int auctionID, int userID, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@AuctionID", auctionID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Auction_Delete", array);
				int num = (int)array[2].Value;
				flag = num == 0;
				switch (num)
				{
				case 0:
					msg = LanguageMgr.GetTranslation("PlayerBussiness.DeleteAuction.Msg1", Array.Empty<object>());
					break;
				case 1:
					msg = LanguageMgr.GetTranslation("PlayerBussiness.DeleteAuction.Msg2", Array.Empty<object>());
					break;
				case 2:
					msg = LanguageMgr.GetTranslation("PlayerBussiness.DeleteAuction.Msg3", Array.Empty<object>());
					break;
				default:
					msg = LanguageMgr.GetTranslation("PlayerBussiness.DeleteAuction.Msg4", Array.Empty<object>());
					break;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600890E RID: 35086 RVA: 0x002DCA48 File Offset: 0x002DAC48
		public AuctionInfo[] GetAuctionPage(int page, string name, int type, int pay, ref int total, int userID, int buyID, int order, bool sort, int size, string AuctionIDs)
		{
			List<AuctionInfo> list = new List<AuctionInfo>();
			try
			{
				string text = " IsExist=1 ";
				bool flag = !string.IsNullOrEmpty(name);
				if (flag)
				{
					text = text + " and Name like '%" + name + "%' ";
				}
				bool flag2 = type != -1;
				if (flag2)
				{
					int num = type;
					int num2 = num;
					switch (num2)
					{
					case 0:
					case 1:
					case 2:
					case 3:
					case 4:
					case 5:
					case 6:
					case 7:
					case 8:
					case 9:
					case 10:
					case 11:
					case 12:
					case 13:
					case 14:
					case 15:
					case 16:
					case 17:
						text = text + " and Category =" + type.ToString() + " ";
						break;
					case 18:
					case 19:
					case 20:
					case 30:
					case 31:
					case 32:
					case 33:
					case 34:
						break;
					case 21:
						text += " and Category in(1,2,5,8,9) ";
						break;
					case 22:
						text += " and Category in(13,15,6,4,3) ";
						break;
					case 23:
						text += " and Category in(16,11,10) ";
						break;
					case 24:
						text += " and Category in(8,9) ";
						break;
					case 25:
						text += " and Category in (7,17) ";
						break;
					case 26:
						text += " and TemplateId>=311000 and TemplateId<=313999";
						break;
					case 27:
						text += " and TemplateId>=311000 and TemplateId<=311999 ";
						break;
					case 28:
						text += " and TemplateId>=312000 and TemplateId<=312999 ";
						break;
					case 29:
						text += " and TemplateId>=313000 and TempLateId<=313999";
						break;
					case 35:
						text += " and TemplateID in (11560,11561,11562)";
						break;
					default:
						switch (num2)
						{
						case 1100:
							text += " and TemplateID in (11019,11021,11022,11023) ";
							break;
						case 1101:
							text += " and TemplateID='11019' ";
							break;
						case 1102:
							text += " and TemplateID='11021' ";
							break;
						case 1103:
							text += " and TemplateID='11022' ";
							break;
						case 1104:
							text += " and TemplateID='11023' ";
							break;
						case 1105:
							text += " and TemplateID in (11001,11002,11003,11004,11005,11006,11007,11008,11009,11010,11011,11012,11013,11014,11015,11016) ";
							break;
						case 1106:
							text += " and TemplateID in (11001,11002,11003,11004) ";
							break;
						case 1107:
							text += " and TemplateID in (11005,11006,11007,11008) ";
							break;
						case 1108:
							text += " and TemplateID in (11009,11010,11011,11012) ";
							break;
						case 1109:
							text += " and TemplateID in (11013,11014,11015,11016) ";
							break;
						case 1110:
							text += " and TemplateID='11024' ";
							break;
						case 1111:
						case 1112:
							text += " and Category in (11) and Property1 = 10";
							break;
						case 1113:
							text += " and TemplateID in (314101,314102,314103,314104,314105,314106,314107,314108,314109,314110,314111,314112,314113,314114,314115,314116,314121,314122,314123,314124,314125,314126,314127,314128,314129,314130,314131,314132,314133,314134) ";
							break;
						case 1114:
							text += " and TemplateID in (314117,314118,314119,314120,314135,314136,314137,314138,314139) ";
							break;
						case 1116:
							text += " and TemplateID='11035' ";
							break;
						case 1117:
							text += " and TemplateID='11036' ";
							break;
						case 1118:
							text += " and TemplateID='11026' ";
							break;
						case 1119:
							text += " and TemplateID='11027' ";
							break;
						}
						break;
					}
				}
				bool flag3 = pay != -1;
				if (flag3)
				{
					text = text + " and PayType =" + pay.ToString() + " ";
				}
				bool flag4 = userID != -1;
				if (flag4)
				{
					text = text + " and AuctioneerID =" + userID.ToString() + " ";
				}
				bool flag5 = buyID != -1;
				if (flag5)
				{
					text = string.Concat(new string[]
					{
						text,
						" and (BuyerID =",
						buyID.ToString(),
						" or AuctionID in (",
						AuctionIDs,
						")) "
					});
				}
				string text2 = "Category,Name,Price,dd,AuctioneerID";
				switch (order)
				{
				case 0:
					text2 = "Name";
					break;
				case 2:
					text2 = "dd";
					break;
				case 3:
					text2 = "AuctioneerName";
					break;
				case 4:
					text2 = "Price";
					break;
				case 5:
					text2 = "BuyerName";
					break;
				}
				text2 += (sort ? " desc" : "");
				text2 += ",AuctionID ";
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@QueryStr", "V_Auction_Scan"),
					new SqlParameter("@QueryWhere", text),
					new SqlParameter("@PageSize", size),
					new SqlParameter("@PageCurrent", page),
					new SqlParameter("@FdShow", "*"),
					new SqlParameter("@FdOrder", text2),
					new SqlParameter("@FdKey", "AuctionID"),
					new SqlParameter("@TotalRow", total)
				};
				array[7].Direction = ParameterDirection.Output;
				DataTable dataTable = this.db.GetDataTable("Auction", "SP_CustomPage", array);
				total = (int)array[7].Value;
				foreach (object obj in dataTable.Rows)
				{
					DataRow dataRow = (DataRow)obj;
					list.Add(new AuctionInfo
					{
						AuctioneerID = (int)dataRow["AuctioneerID"],
						AuctioneerName = dataRow["AuctioneerName"].ToString(),
						AuctionID = (int)dataRow["AuctionID"],
						BeginDate = (DateTime)dataRow["BeginDate"],
						BuyerID = (int)dataRow["BuyerID"],
						BuyerName = dataRow["BuyerName"].ToString(),
						Category = (int)dataRow["Category"],
						IsExist = (bool)dataRow["IsExist"],
						ItemID = (int)dataRow["ItemID"],
						Name = dataRow["Name"].ToString(),
						Mouthful = (int)dataRow["Mouthful"],
						PayType = (int)dataRow["PayType"],
						Price = (int)dataRow["Price"],
						Rise = (int)dataRow["Rise"],
						ValidDate = (int)dataRow["ValidDate"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return list.ToArray();
		}

		// Token: 0x0600890F RID: 35087 RVA: 0x002DD18C File Offset: 0x002DB38C
		public AuctionInfo InitAuctionInfo(SqlDataReader reader)
		{
			return new AuctionInfo
			{
				AuctioneerID = (int)reader["AuctioneerID"],
				AuctioneerName = ((reader["AuctioneerName"] == null) ? "" : reader["AuctioneerName"].ToString()),
				AuctionID = (int)reader["AuctionID"],
				BeginDate = (DateTime)reader["BeginDate"],
				BuyerID = (int)reader["BuyerID"],
				BuyerName = ((reader["BuyerName"] == null) ? "" : reader["BuyerName"].ToString()),
				IsExist = (bool)reader["IsExist"],
				ItemID = (int)reader["ItemID"],
				Mouthful = (int)reader["Mouthful"],
				PayType = (int)reader["PayType"],
				Price = (int)reader["Price"],
				Rise = (int)reader["Rise"],
				ValidDate = (int)reader["ValidDate"],
				Name = reader["Name"].ToString(),
				Category = (int)reader["Category"]
			};
		}

		// Token: 0x06008910 RID: 35088 RVA: 0x002DD328 File Offset: 0x002DB528
		public bool ScanAuction(ref string noticeUserID, double cess)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[2];
				array[0] = new SqlParameter("@NoticeUserID", SqlDbType.NVarChar, 4000);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@Cess", cess);
				this.db.RunProcedure("SP_Auction_Scan", array2);
				noticeUserID = (array2[0].Value != null) ? array2[0].Value.ToString() : "";
				flag = true;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008911 RID: 35089 RVA: 0x002DD3E8 File Offset: 0x002DB5E8
		public bool AddMarryInfo(MarryInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[5];
				array[0] = new SqlParameter("@ID", info.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@UserID", info.UserID);
				array2[2] = new SqlParameter("@IsPublishEquip", info.IsPublishEquip);
				array2[3] = new SqlParameter("@Introduction", info.Introduction);
				array2[4] = new SqlParameter("@RegistTime", info.RegistTime);
				flag = this.db.RunProcedure("SP_MarryInfo_Add", array2);
				info.ID = (int)array2[0].Value;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddMarryInfo", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008912 RID: 35090 RVA: 0x002DD4E4 File Offset: 0x002DB6E4
		public bool DeleteMarryInfo(int ID, int userID, ref string msg)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", ID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_MarryInfo_Delete", array);
				int num = (int)array[2].Value;
				flag = num == 0;
				bool flag2 = num == 0;
				if (flag2)
				{
					msg = LanguageMgr.GetTranslation("PlayerBussiness.DeleteAuction.Succeed", Array.Empty<object>());
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("DeleteAuction", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008913 RID: 35091 RVA: 0x002DD5C0 File Offset: 0x002DB7C0
		public MarryInfo GetMarryInfoSingle(int ID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", ID)
				};
				this.db.GetReader(ref sqlDataReader, "SP_MarryInfo_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return new MarryInfo
					{
						ID = (int)sqlDataReader["ID"],
						UserID = (int)sqlDataReader["UserID"],
						IsPublishEquip = (bool)sqlDataReader["IsPublishEquip"],
						Introduction = sqlDataReader["Introduction"].ToString(),
						RegistTime = (DateTime)sqlDataReader["RegistTime"]
					};
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetMarryInfoSingle", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x06008914 RID: 35092 RVA: 0x002DD6FC File Offset: 0x002DB8FC
		public bool UpdateMarryInfo(MarryInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@IsPublishEquip", info.IsPublishEquip),
					new SqlParameter("@Introduction", info.Introduction),
					new SqlParameter("@RegistTime", info.RegistTime),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[5].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_MarryInfo_Update", array);
				int num = (int)array[5].Value;
				flag = num == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008915 RID: 35093 RVA: 0x002DD808 File Offset: 0x002DBA08
		public MarryInfo[] GetMarryInfoPage(int page, string name, bool sex, int size, ref int total)
		{
			List<MarryInfo> list = new List<MarryInfo>();
			try
			{
				string text = ((!sex) ? " IsExist=1 and Sex=0 and UserExist=1" : " IsExist=1 and Sex=1 and UserExist=1");
				bool flag = !string.IsNullOrEmpty(name);
				if (flag)
				{
					text = string.Concat(new string[] { text, text, " and NickName like '%", name, "%' " });
				}
				string text2 = "State desc,IsMarried";
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@QueryStr", "V_Sys_Marry_Info"),
					new SqlParameter("@QueryWhere", text),
					new SqlParameter("@PageSize", size),
					new SqlParameter("@PageCurrent", page),
					new SqlParameter("@FdShow", "*"),
					new SqlParameter("@FdOrder", text2),
					new SqlParameter("@FdKey", "ID"),
					new SqlParameter("@TotalRow", total)
				};
				array[7].Direction = ParameterDirection.Output;
				DataTable dataTable = this.db.GetDataTable("V_Sys_Marry_Info", "SP_CustomPage", array);
				total = (int)array[7].Value;
				foreach (object obj in dataTable.Rows)
				{
					DataRow dataRow = (DataRow)obj;
					list.Add(new MarryInfo
					{
						ID = (int)dataRow["ID"],
						UserID = (int)dataRow["UserID"],
						IsPublishEquip = (bool)dataRow["IsPublishEquip"],
						Introduction = dataRow["Introduction"].ToString(),
						NickName = dataRow["NickName"].ToString(),
						TypeVIP = (int)dataRow["TypeVIP"],
						VIPLevel = (int)dataRow["VIPLevel"],
						NickName = dataRow["NickName"].ToString(),
						IsConsortia = (bool)dataRow["IsConsortia"],
						ConsortiaID = (int)dataRow["ConsortiaID"],
						Sex = (bool)dataRow["Sex"],
						Win = (int)dataRow["Win"],
						Total = (int)dataRow["Total"],
						Escape = (int)dataRow["Escape"],
						GP = (int)dataRow["GP"],
						Honor = dataRow["Honor"].ToString(),
						Style = dataRow["Style"].ToString(),
						Colors = dataRow["Colors"].ToString(),
						Hide = (int)dataRow["Hide"],
						Grade = (int)dataRow["Grade"],
						State = (int)dataRow["State"],
						Repute = (int)dataRow["Repute"],
						Skin = dataRow["Skin"].ToString(),
						Offer = (int)dataRow["Offer"],
						IsMarried = (bool)dataRow["IsMarried"],
						ConsortiaName = dataRow["ConsortiaName"].ToString(),
						DutyName = dataRow["DutyName"].ToString(),
						Nimbus = (int)dataRow["Nimbus"],
						AchievementPoint = (int)dataRow["AchievementPoint"],
						FightPower = (int)dataRow["FightPower"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008916 RID: 35094 RVA: 0x002DDCE8 File Offset: 0x002DBEE8
		public MarryApplyInfo[] GetPlayerMarryApply(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			List<MarryApplyInfo> list = new List<MarryApplyInfo>();
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", UserID)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Get_Marry_Apply", array);
				while (sqlDataReader.Read())
				{
					list.Add(new MarryApplyInfo
					{
						UserID = (int)sqlDataReader["UserID"],
						ApplyUserID = (int)sqlDataReader["ApplyUserID"],
						ApplyUserName = sqlDataReader["ApplyUserName"].ToString(),
						ApplyType = (int)sqlDataReader["ApplyType"],
						ApplyResult = (bool)sqlDataReader["ApplyResult"],
						LoveProclamation = sqlDataReader["LoveProclamation"].ToString(),
						ID = (int)sqlDataReader["Id"]
					});
				}
				return list.ToArray();
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetPlayerMarryApply", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x06008917 RID: 35095 RVA: 0x002DDE84 File Offset: 0x002DC084
		public bool InsertMarryRoomInfo(MarryRoomInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[20];
				array[0] = new SqlParameter("@ID", info.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.InputOutput;
				array2[1] = new SqlParameter("@Name", info.Name);
				array2[2] = new SqlParameter("@PlayerID", info.PlayerID);
				array2[3] = new SqlParameter("@PlayerName", info.PlayerName);
				array2[4] = new SqlParameter("@GroomID", info.GroomID);
				array2[5] = new SqlParameter("@GroomName", info.GroomName);
				array2[6] = new SqlParameter("@BrideID", info.BrideID);
				array2[7] = new SqlParameter("@BrideName", info.BrideName);
				array2[8] = new SqlParameter("@Pwd", info.Pwd);
				array2[9] = new SqlParameter("@AvailTime", info.AvailTime);
				array2[10] = new SqlParameter("@MaxCount", info.MaxCount);
				array2[11] = new SqlParameter("@GuestInvite", info.GuestInvite);
				array2[12] = new SqlParameter("@MapIndex", info.MapIndex);
				array2[13] = new SqlParameter("@BeginTime", info.BeginTime);
				array2[14] = new SqlParameter("@BreakTime", info.BreakTime);
				array2[15] = new SqlParameter("@RoomIntroduction", info.RoomIntroduction);
				array2[16] = new SqlParameter("@ServerID", info.ServerID);
				array2[17] = new SqlParameter("@IsHymeneal", info.IsHymeneal);
				array2[18] = new SqlParameter("@IsGunsaluteUsed", info.IsGunsaluteUsed);
				array2[19] = new SqlParameter("@Result", SqlDbType.Int);
				array2[19].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Insert_Marry_Room_Info", array2);
				flag = (int)array2[19].Value == 0;
				bool flag2 = flag;
				if (flag2)
				{
					info.ID = (int)array2[0].Value;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("InsertMarryRoomInfo", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008918 RID: 35096 RVA: 0x002DE104 File Offset: 0x002DC304
		public bool UpdateMarryRoomInfo(MarryRoomInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@AvailTime", info.AvailTime),
					new SqlParameter("@BreakTime", info.BreakTime),
					new SqlParameter("@roomIntroduction", info.RoomIntroduction),
					new SqlParameter("@isHymeneal", info.IsHymeneal),
					new SqlParameter("@Name", info.Name),
					new SqlParameter("@Pwd", info.Pwd),
					new SqlParameter("@IsGunsaluteUsed", info.IsGunsaluteUsed),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[8].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Update_Marry_Room_Info", array);
				flag = (int)array[8].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateMarryRoomInfo", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008919 RID: 35097 RVA: 0x002DE24C File Offset: 0x002DC44C
		public bool DisposeMarryRoomInfo(int ID)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", ID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[1].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Dispose_Marry_Room_Info", array);
				flag = (int)array[1].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("DisposeMarryRoomInfo", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600891A RID: 35098 RVA: 0x002DE2F4 File Offset: 0x002DC4F4
		public MarryRoomInfo[] GetMarryRoomInfo()
		{
			SqlDataReader sqlDataReader = null;
			List<MarryRoomInfo> list = new List<MarryRoomInfo>();
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Get_Marry_Room_Info");
				while (sqlDataReader.Read())
				{
					list.Add(new MarryRoomInfo
					{
						ID = (int)sqlDataReader["ID"],
						Name = sqlDataReader["Name"].ToString(),
						PlayerID = (int)sqlDataReader["PlayerID"],
						PlayerName = sqlDataReader["PlayerName"].ToString(),
						GroomID = (int)sqlDataReader["GroomID"],
						GroomName = sqlDataReader["GroomName"].ToString(),
						BrideID = (int)sqlDataReader["BrideID"],
						BrideName = sqlDataReader["BrideName"].ToString(),
						Pwd = sqlDataReader["Pwd"].ToString(),
						AvailTime = (int)sqlDataReader["AvailTime"],
						MaxCount = (int)sqlDataReader["MaxCount"],
						GuestInvite = (bool)sqlDataReader["GuestInvite"],
						MapIndex = (int)sqlDataReader["MapIndex"],
						BeginTime = (DateTime)sqlDataReader["BeginTime"],
						BreakTime = (DateTime)sqlDataReader["BreakTime"],
						RoomIntroduction = sqlDataReader["RoomIntroduction"].ToString(),
						ServerID = (int)sqlDataReader["ServerID"],
						IsHymeneal = (bool)sqlDataReader["IsHymeneal"],
						IsGunsaluteUsed = (bool)sqlDataReader["IsGunsaluteUsed"]
					});
				}
				return list.ToArray();
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetMarryRoomInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x0600891B RID: 35099 RVA: 0x002DE588 File Offset: 0x002DC788
		public MarryRoomInfo GetMarryRoomInfoSingle(int id)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", id)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Get_Marry_Room_Info_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return new MarryRoomInfo
					{
						ID = (int)sqlDataReader["ID"],
						Name = sqlDataReader["Name"].ToString(),
						PlayerID = (int)sqlDataReader["PlayerID"],
						PlayerName = sqlDataReader["PlayerName"].ToString(),
						GroomID = (int)sqlDataReader["GroomID"],
						GroomName = sqlDataReader["GroomName"].ToString(),
						BrideID = (int)sqlDataReader["BrideID"],
						BrideName = sqlDataReader["BrideName"].ToString(),
						Pwd = sqlDataReader["Pwd"].ToString(),
						AvailTime = (int)sqlDataReader["AvailTime"],
						MaxCount = (int)sqlDataReader["MaxCount"],
						GuestInvite = (bool)sqlDataReader["GuestInvite"],
						MapIndex = (int)sqlDataReader["MapIndex"],
						BeginTime = (DateTime)sqlDataReader["BeginTime"],
						BreakTime = (DateTime)sqlDataReader["BreakTime"],
						RoomIntroduction = sqlDataReader["RoomIntroduction"].ToString(),
						ServerID = (int)sqlDataReader["ServerID"],
						IsHymeneal = (bool)sqlDataReader["IsHymeneal"],
						IsGunsaluteUsed = (bool)sqlDataReader["IsGunsaluteUsed"]
					};
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetMarryRoomInfo", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x0600891C RID: 35100 RVA: 0x002DE820 File Offset: 0x002DCA20
		public bool UpdateBreakTimeWhereServerStop()
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[0].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Update_Marry_Room_Info_Sever_Stop", array);
				flag = (int)array[0].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateBreakTimeWhereServerStop", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600891D RID: 35101 RVA: 0x002DE8B4 File Offset: 0x002DCAB4
		public MarryProp GetMarryProp(int id)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", id)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Select_Marry_Prop", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return new MarryProp
					{
						IsMarried = (bool)sqlDataReader["IsMarried"],
						SpouseID = (int)sqlDataReader["SpouseID"],
						SpouseName = sqlDataReader["SpouseName"].ToString(),
						IsCreatedMarryRoom = (bool)sqlDataReader["IsCreatedMarryRoom"],
						SelfMarryRoomID = (int)sqlDataReader["SelfMarryRoomID"],
						IsGotRing = (bool)sqlDataReader["IsGotRing"]
					};
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetMarryProp", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x0600891E RID: 35102 RVA: 0x002DEA24 File Offset: 0x002DCC24
		public bool SavePlayerMarryNotice(MarryApplyInfo info, int answerId, ref int id)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[9];
				array[0] = new SqlParameter("@UserID", info.UserID);
				array[1] = new SqlParameter("@ApplyUserID", info.ApplyUserID);
				array[2] = new SqlParameter("@ApplyUserName", info.ApplyUserName);
				array[3] = new SqlParameter("@ApplyType", info.ApplyType);
				array[4] = new SqlParameter("@ApplyResult", info.ApplyResult);
				array[5] = new SqlParameter("@LoveProclamation", info.LoveProclamation);
				array[6] = new SqlParameter("@AnswerId", answerId);
				array[7] = new SqlParameter("@ouototal", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[7].Direction = ParameterDirection.Output;
				array2[8] = new SqlParameter("@Result", SqlDbType.Int);
				array2[8].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Insert_Marry_Notice", array2);
				id = (int)array2[7].Value;
				flag = (int)array2[8].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SavePlayerMarryNotice", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600891F RID: 35103 RVA: 0x002DEB84 File Offset: 0x002DCD84
		public bool UpdatePlayerGotRingProp(int groomID, int brideID)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@GroomID", groomID),
					new SqlParameter("@BrideID", brideID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Update_GotRing_Prop", array);
				flag = (int)array[2].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdatePlayerGotRingProp", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008920 RID: 35104 RVA: 0x002DEC40 File Offset: 0x002DCE40
		public int VIPRenewal(string nickName, int renewalDays, int TypeVIP, ref DateTime ExpireDayOut)
		{
			int num = 0;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@NickName", nickName),
					new SqlParameter("@RenewalDays", renewalDays),
					new SqlParameter("@ExpireDayOut", DateTime.Now),
					new SqlParameter("@TypeVIP", TypeVIP),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.Output;
				array[4].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Update_Users_VIP", array);
				ExpireDayOut = (DateTime)array[2].Value;
				num = (int)array[4].Value;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_Update_Users_VIP", ex);
				}
			}
			return num;
		}

		// Token: 0x06008921 RID: 35105 RVA: 0x002DED3C File Offset: 0x002DCF3C
		public bool UpdateLastVIPPackTime(int ID)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", ID),
					new SqlParameter("@LastVIPPackTime", DateTime.Now),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_UpdateUserLastVIPPackTime", array);
				flag = true;
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_UpdateUserLastVIPPackTime", ex);
					flag2 = flag;
				}
				else
				{
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x06008922 RID: 35106 RVA: 0x002DEDEC File Offset: 0x002DCFEC
		public AchievementDataInfo[] GetUserAchievement(int userID)
		{
			List<AchievementDataInfo> list = new List<AchievementDataInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = userID;
				this.db.GetReader(ref sqlDataReader, "SP_Get_User_AchievementData", array);
				while (sqlDataReader.Read())
				{
					list.Add(new AchievementDataInfo
					{
						UserID = (int)sqlDataReader["UserID"],
						AchievementID = (int)sqlDataReader["AchievementID"],
						IsComplete = (bool)sqlDataReader["IsComplete"],
						CompletedDate = (DateTime)sqlDataReader["CompletedDate"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008923 RID: 35107 RVA: 0x002DEF30 File Offset: 0x002DD130
		public bool UpdateDbAchievementDataInfo(AchievementDataInfo info)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@AchievementID", info.AchievementID),
					new SqlParameter("@IsComplete", info.IsComplete),
					new SqlParameter("@CompletedDate", info.CompletedDate)
				};
				flag = this.db.RunProcedure("SP_Update_AchievementData", array);
				info.IsDirty = false;
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
				flag2 = flag;
			}
			return flag2;
		}

		// Token: 0x06008924 RID: 35108 RVA: 0x002DF000 File Offset: 0x002DD200
		public UserExtraInfo GetUserExtra(int userID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = userID;
				this.db.GetReader(ref sqlDataReader, "SP_Get_User_Extra", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return new UserExtraInfo
					{
						UserID = (int)sqlDataReader["UserID"],
						LeftRoutteCount = ((sqlDataReader["LeftRoutteCount"] == DBNull.Value) ? GameProperties.LeftRoutteMaxDay : ((int)sqlDataReader["LeftRoutteCount"])),
						LeftRoutteRate = ((sqlDataReader["LeftRoutteRate"] == DBNull.Value) ? 0f : float.Parse(sqlDataReader["LeftRoutteRate"].ToString()))
					};
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x06008925 RID: 35109 RVA: 0x002DF150 File Offset: 0x002DD350
		public bool UpdateUserExtra(UserExtraInfo info)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@LeftRoutteCount", info.LeftRoutteCount),
					new SqlParameter("@LeftRoutteRate", info.LeftRoutteRate)
				};
				flag = this.db.RunProcedure("SP_Update_User_Extra", array);
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("UpdateUserExtra", ex);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x06008926 RID: 35110 RVA: 0x002DF208 File Offset: 0x002DD408
		public PlayerInfo[] GetRankCaddy()
		{
			List<PlayerInfo> list = new List<PlayerInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Get_Rank_Caddy");
				while (sqlDataReader.Read())
				{
					list.Add(new PlayerInfo
					{
						ID = (int)sqlDataReader["UserID"],
						NickName = (string)sqlDataReader["NickName"],
						LuckStone = (int)sqlDataReader["LuckStone"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_Get_Rank_Caddy", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008927 RID: 35111 RVA: 0x002DF30C File Offset: 0x002DD50C
		public UserBoguAdventureInfo[] GetRankBogu()
		{
			List<UserBoguAdventureInfo> list = new List<UserBoguAdventureInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Get_Rank_Bogu");
				while (sqlDataReader.Read())
				{
					list.Add(new UserBoguAdventureInfo
					{
						UserID = (int)sqlDataReader["UserID"],
						NickName = (string)sqlDataReader["NickName"],
						Score = (int)sqlDataReader["Score"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_Get_Rank_Bogu", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008928 RID: 35112 RVA: 0x002DF410 File Offset: 0x002DD610
		public UserActiveInfo[] GetRankMoon()
		{
			List<UserActiveInfo> list = new List<UserActiveInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Get_Rank_Moon");
				while (sqlDataReader.Read())
				{
					UserActiveInfo userActiveInfo = new UserActiveInfo
					{
						UserID = (int)sqlDataReader["UserID"],
						NickName = (string)sqlDataReader["NickName"],
						MoonScore = (int)sqlDataReader["MoonScore"]
					};
					list.Add(userActiveInfo);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_Get_Rank_Moon", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008929 RID: 35113 RVA: 0x002DF514 File Offset: 0x002DD714
		public UserBoguTurnInfo[] GetRankTurn()
		{
			List<UserBoguTurnInfo> list = new List<UserBoguTurnInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Get_Rank_Turn");
				while (sqlDataReader.Read())
				{
					UserBoguTurnInfo userBoguTurnInfo = new UserBoguTurnInfo
					{
						UserID = (int)sqlDataReader["UserID"],
						NickName = (string)sqlDataReader["NickName"],
						Score = (int)sqlDataReader["Score"]
					};
					list.Add(userBoguTurnInfo);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_Get_Rank_Turn", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x0600892A RID: 35114 RVA: 0x002DF618 File Offset: 0x002DD818
		public PlayerInfo[] GetRankLeague()
		{
			List<PlayerInfo> list = new List<PlayerInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Get_Rank_League");
				while (sqlDataReader.Read())
				{
					list.Add(new PlayerInfo
					{
						ID = (int)sqlDataReader["UserID"],
						NickName = (string)sqlDataReader["NickName"],
						Grade = (int)sqlDataReader["Grade"],
						WeeklyScore = (int)sqlDataReader["WeeklyScore"],
						WeeklyRanking = (int)sqlDataReader["WeeklyRanking"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetRankLeague", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x0600892B RID: 35115 RVA: 0x002DF750 File Offset: 0x002DD950
		public bool UpdateAcademyPlayer(PlayerInfo player)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", player.ID),
					new SqlParameter("@ApprenticeshipState", player.ApprenticeshipState),
					new SqlParameter("@MasterID", player.MasterID),
					new SqlParameter("@MasterOrApprentices", player.MasterOrApprentices),
					new SqlParameter("@GraduatesCount", player.GraduatesCount),
					new SqlParameter("@HonourOfMaster", player.HonourOfMaster),
					null,
					new SqlParameter("@FreezesDate", player.FreezesDate)
				};
				array[6] = new SqlParameter("@Result", SqlDbType.Int);
				array[6].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_Academy_Update", array);
				flag = (int)array[6].Value == 0;
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("Init", ex);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x0600892C RID: 35116 RVA: 0x002DF888 File Offset: 0x002DDA88
		public TexpInfo GetUserTexpInfoSingle(int ID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", ID)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Get_User_Texp", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return new TexpInfo
					{
						UserID = (int)sqlDataReader["UserID"],
						attTexpExp = (int)sqlDataReader["attTexpExp"],
						defTexpExp = (int)sqlDataReader["defTexpExp"],
						hpTexpExp = (int)sqlDataReader["hpTexpExp"],
						lukTexpExp = (int)sqlDataReader["lukTexpExp"],
						spdTexpExp = (int)sqlDataReader["spdTexpExp"],
						texpCount = (int)sqlDataReader["texpCount"],
						texpTaskCount = (int)sqlDataReader["texpTaskCount"],
						texpTaskDate = (DateTime)sqlDataReader["texpTaskDate"]
					};
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetTexpInfoSingle", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x0600892D RID: 35117 RVA: 0x002DFA38 File Offset: 0x002DDC38
		public bool UpdateUserTexpInfo(TexpInfo info)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@attTexpExp", info.attTexpExp),
					new SqlParameter("@defTexpExp", info.defTexpExp),
					new SqlParameter("@hpTexpExp", info.hpTexpExp),
					new SqlParameter("@lukTexpExp", info.lukTexpExp),
					new SqlParameter("@spdTexpExp", info.spdTexpExp),
					new SqlParameter("@texpCount", info.texpCount),
					new SqlParameter("@texpTaskCount", info.texpTaskCount),
					new SqlParameter("@texpTaskDate", info.texpTaskDate)
				};
				this.db.RunProcedure("SP_Update_UserTexp", array);
				info.IsDirty = false;
				flag = true;
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("Init", ex);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x0600892E RID: 35118 RVA: 0x002DFB98 File Offset: 0x002DDD98
		public UserDailyInfo GetDailyLogListSingle(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", UserID)
				};
				this.db.GetReader(ref sqlDataReader, "SP_DailyLogList_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return new UserDailyInfo
					{
						ID = (int)sqlDataReader["ID"],
						UserID = (int)sqlDataReader["UserID"],
						UserAwardLog = (int)sqlDataReader["UserAwardLog"],
						DayLog = (string)sqlDataReader["DayLog"],
						LastDate = (DateTime)sqlDataReader["LastDate"]
					};
				}
			}
			catch (Exception ex)
			{
				bool flag2 = !BaseBussiness.log.IsErrorEnabled;
				if (flag2)
				{
					BaseBussiness.log.Error("DailyLogList", ex);
				}
			}
			finally
			{
				bool flag3 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag3)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x0600892F RID: 35119 RVA: 0x002DFCD4 File Offset: 0x002DDED4
		public bool AddDailyLogList(UserDailyInfo info)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@UserAwardLog", info.UserAwardLog),
					new SqlParameter("@DayLog", info.DayLog),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[3].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_DailyLogList_Add", array);
				flag = (int)array[3].Value == 0;
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("UpdateAASInfo", ex);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x06008930 RID: 35120 RVA: 0x002DFDB0 File Offset: 0x002DDFB0
		public bool UpdateDailyLogList(UserDailyInfo info)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@UserAwardLog", info.UserAwardLog),
					new SqlParameter("@DayLog", info.DayLog),
					new SqlParameter("@LastDate", info.LastDate.ToString()),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[4].Direction = ParameterDirection.ReturnValue;
				flag = this.db.RunProcedure("SP_DailyLogList_Update", array);
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("User_Update_BoxProgression", ex);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x06008931 RID: 35121 RVA: 0x002DFE98 File Offset: 0x002DE098
		public List<UserRankInfo> GetSingleUserRank(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			List<UserRankInfo> list = new List<UserRankInfo>();
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_GetSingleUserRank", array);
				while (sqlDataReader.Read())
				{
					list.Add(new UserRankInfo
					{
						ID = (int)sqlDataReader["ID"],
						UserID = (int)sqlDataReader["UserID"],
						Name = (string)sqlDataReader["Name"],
						Attack = (int)sqlDataReader["Attack"],
						Defence = (int)sqlDataReader["Defence"],
						Luck = (int)sqlDataReader["Luck"],
						Agility = (int)sqlDataReader["Agility"],
						BeginDate = (DateTime)sqlDataReader["BeginDate"],
						Validate = (int)sqlDataReader["Validate"],
						IsExit = (bool)sqlDataReader["IsExit"],
						NewTitleID = (int)sqlDataReader["NewTitleID"],
						EndDate = (DateTime)sqlDataReader["EndDate"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_GetSingleUserRankInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list;
		}

		// Token: 0x06008932 RID: 35122 RVA: 0x002E00AC File Offset: 0x002DE2AC
		public bool AddUserRank(UserRankInfo item)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[13];
				array[0] = new SqlParameter("@ID", item.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@UserID", item.UserID);
				array2[2] = new SqlParameter("@Name", item.Name);
				array2[3] = new SqlParameter("@Attack", item.Attack);
				array2[4] = new SqlParameter("@Defence", item.Defence);
				array2[5] = new SqlParameter("@Luck", item.Luck);
				array2[6] = new SqlParameter("@Agility", item.Agility);
				array2[7] = new SqlParameter("@BeginDate", item.BeginDate);
				array2[8] = new SqlParameter("@Validate", item.Validate);
				array2[9] = new SqlParameter("@IsExit", item.IsExit);
				array2[10] = new SqlParameter("@Result", SqlDbType.Int);
				array2[10].Direction = ParameterDirection.ReturnValue;
				array2[11] = new SqlParameter("@NewTitleID", item.NewTitleID);
				array2[12] = new SqlParameter("@EndDate", item.EndDate);
				this.db.RunProcedure("SP_UserRank_Add", array2);
				flag = (int)array2[10].Value == 0;
				item.ID = (int)array2[0].Value;
				item.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008933 RID: 35123 RVA: 0x002E0294 File Offset: 0x002DE494
		public bool UpdateUserRank(UserRankInfo item)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[13];
				array[0] = new SqlParameter("@ID", item.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@UserID", item.UserID);
				array2[2] = new SqlParameter("@Name", item.Name);
				array2[3] = new SqlParameter("@Attack", item.Attack);
				array2[4] = new SqlParameter("@Defence", item.Defence);
				array2[5] = new SqlParameter("@Luck", item.Luck);
				array2[6] = new SqlParameter("@Agility", item.Agility);
				array2[7] = new SqlParameter("@BeginDate", item.BeginDate);
				array2[8] = new SqlParameter("@Validate", item.Validate);
				array2[9] = new SqlParameter("@IsExit", item.IsExit);
				array2[10] = new SqlParameter("@Result", SqlDbType.Int);
				array2[10].Direction = ParameterDirection.ReturnValue;
				array2[11] = new SqlParameter("@NewTitleID", item.NewTitleID);
				array2[12] = new SqlParameter("@EndDate", item.EndDate);
				this.db.RunProcedure("SP_UpdateUserRank", array2);
				flag = (int)array2[10].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_UpdateUserRank", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008934 RID: 35124 RVA: 0x002E0460 File Offset: 0x002DE660
		public bool AddUserGift(UserGiftInfo info)
		{
			bool flag = false;
			bool flag2;
			try
			{
				this.db.RunProcedure("SP_Users_Gift_Add", new SqlParameter[]
				{
					new SqlParameter("@SenderID", info.SenderID),
					new SqlParameter("@SenderName", info.SenderName),
					new SqlParameter("@ReceiverID", info.ReceiverID),
					new SqlParameter("@ReceiverName", info.ReceiverName),
					new SqlParameter("@TemplateID", info.TemplateID),
					new SqlParameter("@Count", info.Count)
				});
				flag = true;
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("AddUserGift", ex);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x06008935 RID: 35125 RVA: 0x002E0554 File Offset: 0x002DE754
		public bool UpdateUserGiftGP(int userId, int giftgp)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", userId),
					new SqlParameter("@GiftGP", giftgp),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[2].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_UpdateGiftGP", array);
				flag = (int)array[2].Value == 0;
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("AddUserGift", ex);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x06008936 RID: 35126 RVA: 0x002E0614 File Offset: 0x002DE814
		public UserGiftInfo InitGiftInfo(SqlDataReader read)
		{
			return new UserGiftInfo
			{
				ID = (int)read["ID"],
				ReceiverID = (int)read["ReceiverID"],
				ReceiverName = (string)read["ReceiverName"],
				SenderID = (int)read["SenderID"],
				SenderName = (string)read["SenderName"],
				TemplateID = (int)read["TemplateID"],
				Count = (int)read["Count"],
				CreateDate = (DateTime)read["CreateDate"],
				LastUpdate = (DateTime)read["LastUpdate"]
			};
		}

		// Token: 0x06008937 RID: 35127 RVA: 0x002E06FC File Offset: 0x002DE8FC
		public UserGiftInfo GetGiftSingle(int userid)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", userid)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Gift_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return this.InitGiftInfo(sqlDataReader);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x06008938 RID: 35128 RVA: 0x002E07C4 File Offset: 0x002DE9C4
		public UserGiftInfo[] GetAllUserGifts(int userid, bool isReceive)
		{
			List<UserGiftInfo> list = new List<UserGiftInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", userid),
					new SqlParameter("@IsReceive", isReceive)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Users_Gift_Single", array);
				while (sqlDataReader.Read())
				{
					list.Add(new UserGiftInfo
					{
						ID = (int)sqlDataReader["ID"],
						ReceiverID = (int)sqlDataReader["ReceiverID"],
						ReceiverName = (string)sqlDataReader["ReceiverName"],
						SenderID = (int)sqlDataReader["SenderID"],
						SenderName = (string)sqlDataReader["SenderName"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						Count = (int)sqlDataReader["Count"],
						CreateDate = (DateTime)sqlDataReader["CreateDate"],
						LastUpdate = (DateTime)sqlDataReader["LastUpdate"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllUserGifts", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008939 RID: 35129 RVA: 0x002E099C File Offset: 0x002DEB9C
		public UserGiftInfo[] GetAllUserReceivedGifts(int userid)
		{
			Dictionary<int, UserGiftInfo> dictionary = new Dictionary<int, UserGiftInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				UserGiftInfo[] allUserGifts = this.GetAllUserGifts(userid, true);
				bool flag = allUserGifts != null;
				if (flag)
				{
					UserGiftInfo[] array = allUserGifts;
					UserGiftInfo[] array2 = array;
					UserGiftInfo[] array3 = array2;
					UserGiftInfo[] array4 = array3;
					foreach (UserGiftInfo userGiftInfo in array4)
					{
						bool flag2 = dictionary.ContainsKey(userGiftInfo.TemplateID);
						if (flag2)
						{
							dictionary[userGiftInfo.TemplateID].Count += userGiftInfo.Count;
						}
						else
						{
							dictionary.Add(userGiftInfo.TemplateID, userGiftInfo);
						}
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllUserReceivedGifts", ex);
				}
			}
			finally
			{
				bool flag3 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag3)
				{
					sqlDataReader.Close();
				}
			}
			return dictionary.Values.ToArray<UserGiftInfo>();
		}

		// Token: 0x0600893A RID: 35130 RVA: 0x002E0AC0 File Offset: 0x002DECC0
		public bool UpdateGift(UserGiftInfo gift, int count)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", gift.ID),
					new SqlParameter("@SenderID", gift.SenderID),
					new SqlParameter("@SenderName", gift.SenderName),
					new SqlParameter("@ReceiverID", gift.ReceiverID),
					new SqlParameter("@ReceiverName", gift.ReceiverName),
					new SqlParameter("@TemplateID", gift.TemplateID),
					new SqlParameter("@Count", count),
					new SqlParameter("@CreateDate", gift.CreateDate),
					new SqlParameter("@LastUpdate", gift.LastUpdate),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[7].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_Gift_Update", array);
				flag = (int)array[7].Value == 0;
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("Init", ex);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x0600893B RID: 35131 RVA: 0x002E0C30 File Offset: 0x002DEE30
		public UserRankDateInfo InitUserRankDateInfo(SqlDataReader dr)
		{
			return new UserRankDateInfo
			{
				UserID = (int)dr["UserID"],
				ConsortiaID = (int)dr["ConsortiaID"],
				FightPower = (int)dr["FightPower"],
				PrevFightPower = (int)dr["PrevFightPower"],
				GP = (int)dr["GP"],
				PrevGP = (int)dr["PrevGP"],
				AchievementPoint = (int)dr["AchievementPoint"],
				PrevAchievementPoint = (int)dr["PrevAchievementPoint"],
				GiftGp = (int)dr["GiftGp"],
				PrevGiftGp = (int)dr["PrevGiftGp"],
				LeagueAddWeek = (int)dr["LeagueAddWeek"],
				PrevLeagueAddWeek = (int)dr["PrevLeagueAddWeek"],
				ConsortiaFightPower = (int)dr["ConsortiaFightPower"],
				ConsortiaPrevFightPower = (int)dr["ConsortiaPrevFightPower"],
				ConsortiaLevel = (int)dr["ConsortiaLevel"],
				ConsortiaPrevLevel = (int)dr["ConsortiaPrevLevel"],
				ConsortiaRiches = (int)dr["ConsortiaRiches"],
				ConsortiaPrevRiches = (int)dr["ConsortiaPrevRiches"],
				ConsortiaGiftGp = (int)dr["ConsortiaGiftGp"],
				ConsortiaPrevGiftGp = (int)dr["ConsortiaPrevGiftGp"]
			};
		}

		// Token: 0x0600893C RID: 35132 RVA: 0x002E0E14 File Offset: 0x002DF014
		public bool UpdateRank()
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[0];
				flag = this.db.RunProcedure("SP_Sys_Update_Consortia_DayList", array);
				flag = this.db.RunProcedure("SP_Sys_Update_Consortia_FightPower", array);
				flag = this.db.RunProcedure("SP_Sys_Update_Consortia_Honor", array);
				flag = this.db.RunProcedure("SP_Sys_Update_Consortia_List", array);
				flag = this.db.RunProcedure("SP_Sys_Update_Consortia_WeekList", array);
				flag = this.db.RunProcedure("SP_Sys_Update_OfferList", array);
				flag = this.db.RunProcedure("SP_Sys_Update_Users_DayList", array);
				flag = this.db.RunProcedure("SP_Sys_Update_Users_List", array);
				flag = this.db.RunProcedure("SP_Sys_Update_Users_WeekList", array);
				flag = this.db.RunProcedure("SP_Sys_Update_Users_Rank_Date", array);
				flag = this.db.RunProcedure("SP_Sys_Update_Users_Rank_League", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init UpdatePersonalRank", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600893D RID: 35133 RVA: 0x002E0F34 File Offset: 0x002DF134
		public UserRankDateInfo GetUserRankDateByID(int userID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = userID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Rank_Date", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return this.InitUserRankDateInfo(sqlDataReader);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("InitUserRankDateInfo", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x0600893E RID: 35134 RVA: 0x002E1004 File Offset: 0x002DF204
		public UserRankDateInfo[] GetAllUserRankDate()
		{
			List<UserRankDateInfo> list = new List<UserRankDateInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[0];
				this.db.GetReader(ref sqlDataReader, "SP_Users_Rank_Date_All", array);
				while (sqlDataReader.Read())
				{
					list.Add(this.InitUserRankDateInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("InitUserRankDateInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x0600893F RID: 35135 RVA: 0x002E10CC File Offset: 0x002DF2CC
		private UserCardInfo InitUserCard(SqlDataReader dr)
		{
			return new UserCardInfo
			{
				CardID = (int)dr["CardID"],
				UserID = (int)dr["UserID"],
				TemplateID = (int)dr["TemplateID"],
				Place = (int)dr["Place"],
				Count = (int)dr["Count"],
				Attack = (int)dr["Attack"],
				Defence = (int)dr["Defence"],
				Agility = (int)dr["Agility"],
				Luck = (int)dr["Luck"],
				AttackReset = (int)dr["AttackReset"],
				DefenceReset = (int)dr["DefenceReset"],
				AgilityReset = (int)dr["AgilityReset"],
				LuckReset = (int)dr["LuckReset"],
				Guard = (int)dr["Guard"],
				Damage = (int)dr["Damage"],
				Level = (int)dr["Level"],
				CardGP = (int)dr["CardGP"],
				isFirstGet = (bool)dr["isFirstGet"]
			};
		}

		// Token: 0x06008940 RID: 35136 RVA: 0x002E1284 File Offset: 0x002DF484
		public UserCardInfo[] GetSingleUserCard(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			List<UserCardInfo> list = new List<UserCardInfo>();
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_GetSingleUserCard", array);
				while (sqlDataReader.Read())
				{
					UserCardInfo userCardInfo = this.InitUserCard(sqlDataReader);
					list.Add(userCardInfo);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSingleUserCard", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008941 RID: 35137 RVA: 0x002E136C File Offset: 0x002DF56C
		public bool AddUserCard(UserCardInfo item)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[19];
				array[0] = new SqlParameter("@CardID", item.CardID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@UserID", item.UserID);
				array2[2] = new SqlParameter("@TemplateID", item.TemplateID);
				array2[3] = new SqlParameter("@Place", item.Place);
				array2[4] = new SqlParameter("@Count", item.Count);
				array2[5] = new SqlParameter("@Attack", item.Attack);
				array2[6] = new SqlParameter("@Defence", item.Defence);
				array2[7] = new SqlParameter("@Agility", item.Agility);
				array2[8] = new SqlParameter("@Luck", item.Luck);
				array2[9] = new SqlParameter("@Guard", item.Guard);
				array2[10] = new SqlParameter("@Damage", item.Damage);
				array2[11] = new SqlParameter("@Level", item.Level);
				array2[12] = new SqlParameter("@CardGP", item.CardGP);
				array2[13] = new SqlParameter("@isFirstGet", item.isFirstGet);
				array2[14] = new SqlParameter("@AttackReset", item.AttackReset);
				array2[15] = new SqlParameter("@DefenceReset", item.DefenceReset);
				array2[16] = new SqlParameter("@AgilityReset", item.AgilityReset);
				array2[17] = new SqlParameter("@LuckReset", item.LuckReset);
				array2[18] = new SqlParameter("@Result", SqlDbType.Int);
				array2[18].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_UserCard_Add", array2);
				flag = (int)array2[18].Value == 0;
				item.CardID = (int)array2[0].Value;
				item.IsDirty = false;
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("Init", ex);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x06008942 RID: 35138 RVA: 0x002E15F4 File Offset: 0x002DF7F4
		public bool UpdateUserCard(UserCardInfo item)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@CardID", item.CardID),
					new SqlParameter("@UserID", item.UserID),
					new SqlParameter("@TemplateID", item.TemplateID),
					new SqlParameter("@Place", item.Place),
					new SqlParameter("@Count", item.Count),
					new SqlParameter("@Attack", item.Attack),
					new SqlParameter("@Defence", item.Defence),
					new SqlParameter("@Agility", item.Agility),
					new SqlParameter("@Luck", item.Luck),
					new SqlParameter("@Guard", item.Guard),
					new SqlParameter("@Damage", item.Damage),
					new SqlParameter("@Level", item.Level),
					new SqlParameter("@CardGP", item.CardGP),
					null,
					new SqlParameter("@isFirstGet", item.isFirstGet),
					new SqlParameter("@AttackReset", item.AttackReset),
					new SqlParameter("@DefenceReset", item.DefenceReset),
					new SqlParameter("@AgilityReset", item.AgilityReset),
					new SqlParameter("@LuckReset", item.LuckReset)
				};
				array[13] = new SqlParameter("@Result", SqlDbType.Int);
				array[13].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_UpdateUserCard", array);
				flag = (int)array[13].Value == 0;
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("SP_UpdateUserCard", ex);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x06008943 RID: 35139 RVA: 0x002E1854 File Offset: 0x002DFA54
		public List<UserCardInfo> GetUserCardEuqip(int UserID)
		{
			List<UserCardInfo> list = new List<UserCardInfo>();
			SqlDataReader sqlDataReader = null;
			List<UserCardInfo> list2;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Items_Card_Equip", array);
				while (sqlDataReader.Read())
				{
					UserCardInfo userCardInfo = this.InitUserCard(sqlDataReader);
					list.Add(userCardInfo);
				}
				list2 = list;
			}
			catch (Exception ex)
			{
				bool flag = !BaseBussiness.log.IsErrorEnabled;
				if (flag)
				{
					list2 = list;
				}
				else
				{
					BaseBussiness.log.Error("Init", ex);
					list2 = list;
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return list2;
		}

		// Token: 0x06008944 RID: 35140 RVA: 0x002E193C File Offset: 0x002DFB3C
		public UserPetInfo InitPet(SqlDataReader reader)
		{
			return new UserPetInfo
			{
				ID = (int)reader["ID"],
				TemplateID = (int)reader["TemplateID"],
				Name = reader["Name"].ToString(),
				UserID = (int)reader["UserID"],
				Attack = (int)reader["Attack"],
				AttackGrow = (int)reader["AttackGrow"],
				Agility = (int)reader["Agility"],
				AgilityGrow = (int)reader["AgilityGrow"],
				Defence = (int)reader["Defence"],
				DefenceGrow = (int)reader["DefenceGrow"],
				Luck = (int)reader["Luck"],
				LuckGrow = (int)reader["LuckGrow"],
				Blood = (int)reader["Blood"],
				BloodGrow = (int)reader["BloodGrow"],
				Damage = (int)reader["Damage"],
				DamageGrow = (int)reader["DamageGrow"],
				Guard = (int)reader["Guard"],
				GuardGrow = (int)reader["GuardGrow"],
				Level = (int)reader["Level"],
				GP = (int)reader["GP"],
				MaxGP = (int)reader["MaxGP"],
				Hunger = (int)reader["Hunger"],
				MP = (int)reader["MP"],
				Place = (int)reader["Place"],
				IsEquip = (bool)reader["IsEquip"],
				IsExit = (bool)reader["IsExit"],
				Skill = reader["Skill"].ToString(),
				SkillEquip = reader["SkillEquip"].ToString(),
				currentStarExp = (int)reader["currentStarExp"],
				breakGrade = (int)reader["breakGrade"],
				breakAttack = (int)reader["breakAttack"],
				breakDefence = (int)reader["breakDefence"],
				breakAgility = (int)reader["breakAgility"],
				breakLuck = (int)reader["breakLuck"],
				breakBlood = (int)reader["breakBlood"],
				eQPets = ((reader["eQPets"] == null) ? "" : reader["eQPets"].ToString()),
				BaseProp = ((reader["BaseProp"] == null) ? "" : reader["BaseProp"].ToString())
			};
		}

		// Token: 0x06008945 RID: 35141 RVA: 0x002E1CD0 File Offset: 0x002DFED0
		public bool AddUserPet(UserPetInfo item)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[39];
				array[0] = new SqlParameter("@TemplateID", item.TemplateID);
				array[1] = new SqlParameter("@Name", (item.Name == null) ? "Error!" : item.Name);
				array[2] = new SqlParameter("@UserID", item.UserID);
				array[3] = new SqlParameter("@Attack", item.Attack);
				array[4] = new SqlParameter("@Defence", item.Defence);
				array[5] = new SqlParameter("@Luck", item.Luck);
				array[6] = new SqlParameter("@Agility", item.Agility);
				array[7] = new SqlParameter("@Blood", item.Blood);
				array[8] = new SqlParameter("@Damage", item.Damage);
				array[9] = new SqlParameter("@Guard", item.Guard);
				array[10] = new SqlParameter("@AttackGrow", item.AttackGrow);
				array[11] = new SqlParameter("@DefenceGrow", item.DefenceGrow);
				array[12] = new SqlParameter("@LuckGrow", item.LuckGrow);
				array[13] = new SqlParameter("@AgilityGrow", item.AgilityGrow);
				array[14] = new SqlParameter("@BloodGrow", item.BloodGrow);
				array[15] = new SqlParameter("@DamageGrow", item.DamageGrow);
				array[16] = new SqlParameter("@GuardGrow", item.GuardGrow);
				array[17] = new SqlParameter("@Level", item.Level);
				array[18] = new SqlParameter("@GP", item.GP);
				array[19] = new SqlParameter("@MaxGP", item.MaxGP);
				array[20] = new SqlParameter("@Hunger", item.Hunger);
				array[21] = new SqlParameter("@PetHappyStar", item.PetHappyStar);
				array[22] = new SqlParameter("@MP", item.MP);
				array[23] = new SqlParameter("@IsEquip", item.IsEquip);
				array[24] = new SqlParameter("@Skill", item.Skill);
				array[25] = new SqlParameter("@SkillEquip", item.SkillEquip);
				array[26] = new SqlParameter("@Place", item.Place);
				array[27] = new SqlParameter("@IsExit", item.IsExit);
				array[28] = new SqlParameter("@ID", item.ID);
				SqlParameter[] array2 = array;
				array2[28].Direction = ParameterDirection.Output;
				array2[29] = new SqlParameter("@currentStarExp", item.currentStarExp);
				array2[30] = new SqlParameter("@Result", SqlDbType.Int);
				array2[30].Direction = ParameterDirection.ReturnValue;
				array2[31] = new SqlParameter("@breakGrade", item.breakGrade);
				array2[32] = new SqlParameter("@breakAttack", item.breakAttack);
				array2[33] = new SqlParameter("@breakDefence", item.breakDefence);
				array2[34] = new SqlParameter("@breakAgility", item.breakAgility);
				array2[35] = new SqlParameter("@breakLuck", item.breakLuck);
				array2[36] = new SqlParameter("@breakBlood", item.breakBlood);
				array2[37] = new SqlParameter("@eQPets", item.eQPets);
				array2[38] = new SqlParameter("@BaseProp", item.BaseProp);
				flag = this.db.RunProcedure("SP_User_Add_Pet_Lacrar", array2);
				flag = (int)array2[30].Value == 0;
				item.ID = (int)array2[28].Value;
				item.IsDirty = false;
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("Init", ex);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x06008946 RID: 35142 RVA: 0x002E2144 File Offset: 0x002E0344
		public bool UpdateUserPet(UserPetInfo item)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[39];
				array[0] = new SqlParameter("@TemplateID", item.TemplateID);
				array[1] = new SqlParameter("@Name", (item.Name == null) ? "XiaoJian" : item.Name);
				array[2] = new SqlParameter("@UserID", item.UserID);
				array[3] = new SqlParameter("@Attack", item.Attack);
				array[4] = new SqlParameter("@Defence", item.Defence);
				array[5] = new SqlParameter("@Luck", item.Luck);
				array[6] = new SqlParameter("@Agility", item.Agility);
				array[7] = new SqlParameter("@Blood", item.Blood);
				array[8] = new SqlParameter("@Damage", item.Damage);
				array[9] = new SqlParameter("@Guard", item.Guard);
				array[10] = new SqlParameter("@AttackGrow", item.AttackGrow);
				array[11] = new SqlParameter("@DefenceGrow", item.DefenceGrow);
				array[12] = new SqlParameter("@LuckGrow", item.LuckGrow);
				array[13] = new SqlParameter("@AgilityGrow", item.AgilityGrow);
				array[14] = new SqlParameter("@BloodGrow", item.BloodGrow);
				array[15] = new SqlParameter("@DamageGrow", item.DamageGrow);
				array[16] = new SqlParameter("@GuardGrow", item.GuardGrow);
				array[17] = new SqlParameter("@Level", item.Level);
				array[18] = new SqlParameter("@GP", item.GP);
				array[19] = new SqlParameter("@MaxGP", item.MaxGP);
				array[20] = new SqlParameter("@Hunger", item.Hunger);
				array[21] = new SqlParameter("@PetHappyStar", item.PetHappyStar);
				array[22] = new SqlParameter("@MP", item.MP);
				array[23] = new SqlParameter("@IsEquip", item.IsEquip);
				array[24] = new SqlParameter("@Place", item.Place);
				array[25] = new SqlParameter("@IsExit", item.IsExit);
				array[26] = new SqlParameter("@ID", item.ID);
				array[27] = new SqlParameter("@Skill", item.Skill);
				array[28] = new SqlParameter("@SkillEquip", item.SkillEquip);
				array[29] = new SqlParameter("@currentStarExp", item.currentStarExp);
				array[30] = new SqlParameter("@Result", SqlDbType.Int);
				SqlParameter[] array2 = array;
				array2[30].Direction = ParameterDirection.ReturnValue;
				array2[31] = new SqlParameter("@breakGrade", item.breakGrade);
				array2[32] = new SqlParameter("@breakAttack", item.breakAttack);
				array2[33] = new SqlParameter("@breakDefence", item.breakDefence);
				array2[34] = new SqlParameter("@breakAgility", item.breakAgility);
				array2[35] = new SqlParameter("@breakLuck", item.breakLuck);
				array2[36] = new SqlParameter("@breakBlood", item.breakBlood);
				array2[37] = new SqlParameter("@eQPets", item.eQPets);
				array2[38] = new SqlParameter("@BaseProp", item.BaseProp);
				this.db.RunProcedure("SP_UserPet_Update_Lacrar", array2);
				int num = (int)array2[30].Value;
				flag = num == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008947 RID: 35143 RVA: 0x002E2590 File Offset: 0x002E0790
		public UserPetInfo[] GetUserAdoptPetSingles(int UserID)
		{
			List<UserPetInfo> list = new List<UserPetInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_Get_User_AdoptPetList", array);
				while (sqlDataReader.Read())
				{
					list.Add(new UserPetInfo
					{
						ID = (int)sqlDataReader["ID"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						Name = sqlDataReader["Name"].ToString(),
						UserID = (int)sqlDataReader["UserID"],
						Attack = (int)sqlDataReader["Attack"],
						AttackGrow = (int)sqlDataReader["AttackGrow"],
						Agility = (int)sqlDataReader["Agility"],
						AgilityGrow = (int)sqlDataReader["AgilityGrow"],
						Defence = (int)sqlDataReader["Defence"],
						DefenceGrow = (int)sqlDataReader["DefenceGrow"],
						Luck = (int)sqlDataReader["Luck"],
						LuckGrow = (int)sqlDataReader["LuckGrow"],
						Blood = (int)sqlDataReader["Blood"],
						BloodGrow = (int)sqlDataReader["BloodGrow"],
						Damage = (int)sqlDataReader["Damage"],
						DamageGrow = (int)sqlDataReader["DamageGrow"],
						Guard = (int)sqlDataReader["Guard"],
						GuardGrow = (int)sqlDataReader["GuardGrow"],
						Level = (int)sqlDataReader["Level"],
						GP = (int)sqlDataReader["GP"],
						MaxGP = (int)sqlDataReader["MaxGP"],
						Hunger = (int)sqlDataReader["Hunger"],
						MP = (int)sqlDataReader["MP"],
						Place = (int)sqlDataReader["Place"],
						IsEquip = (bool)sqlDataReader["IsEquip"],
						IsExit = (bool)sqlDataReader["IsExit"],
						Skill = sqlDataReader["Skill"].ToString(),
						SkillEquip = sqlDataReader["SkillEquip"].ToString()
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008948 RID: 35144 RVA: 0x002E2918 File Offset: 0x002E0B18
		public bool RemoveUserAdoptPet(int ID)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", ID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[1].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Remove_User_AdoptPet", array);
				int num = (int)array[1].Value;
				flag = num == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008949 RID: 35145 RVA: 0x002E29C4 File Offset: 0x002E0BC4
		public bool ClearAdoptPet(int ID)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", ID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[1].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Clear_AdoptPet", array);
				int num = (int)array[1].Value;
				flag = num == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600894A RID: 35146 RVA: 0x002E2A70 File Offset: 0x002E0C70
		public bool UpdateUserAdoptPet(int ID)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", ID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[1].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Update_User_AdoptPet", array);
				int num = (int)array[1].Value;
				flag = num == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600894B RID: 35147 RVA: 0x002E2B1C File Offset: 0x002E0D1C
		public bool AddUserAdoptPet(UserPetInfo info, bool isUse)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@TemplateID", info.TemplateID),
					new SqlParameter("@Name", (info.Name == null) ? "XiaoJian" : info.Name),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@Attack", info.Attack),
					new SqlParameter("@Defence", info.Defence),
					new SqlParameter("@Luck", info.Luck),
					new SqlParameter("@Agility", info.Agility),
					new SqlParameter("@Blood", info.Blood),
					new SqlParameter("@Damage", info.Damage),
					new SqlParameter("@Guard", info.Guard),
					new SqlParameter("@AttackGrow", info.AttackGrow),
					new SqlParameter("@DefenceGrow", info.DefenceGrow),
					new SqlParameter("@LuckGrow", info.LuckGrow),
					new SqlParameter("@AgilityGrow", info.AgilityGrow),
					new SqlParameter("@BloodGrow", info.BloodGrow),
					new SqlParameter("@DamageGrow", info.DamageGrow),
					new SqlParameter("@GuardGrow", info.GuardGrow),
					new SqlParameter("@Skill", info.Skill),
					new SqlParameter("@SkillEquip", info.SkillEquip),
					new SqlParameter("@Place", info.Place),
					new SqlParameter("@IsExit", info.IsExit),
					new SqlParameter("@IsUse", isUse),
					new SqlParameter("@ID", info.ID)
				};
				array[22].Direction = ParameterDirection.Output;
				flag = this.db.RunProcedure("SP_User_AdoptPet", array);
				info.ID = (int)array[22].Value;
				info.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600894C RID: 35148 RVA: 0x002E2DEC File Offset: 0x002E0FEC
		public UserPetInfo[] GetUserPetSingles(int UserID, int vipLv)
		{
			List<UserPetInfo> list = new List<UserPetInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_Get_UserPet_By_ID", array);
				while (sqlDataReader.Read())
				{
					UserPetInfo userPetInfo = this.InitPet(sqlDataReader);
					userPetInfo.VIPLevel = vipLv;
					list.Add(userPetInfo);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x0600894D RID: 35149 RVA: 0x002E2EDC File Offset: 0x002E10DC
		public SpaRoomInfo[] GetSpaRoomInfo()
		{
			SqlDataReader sqlDataReader = null;
			List<SpaRoomInfo> list = new List<SpaRoomInfo>();
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Get_Spa_Room_Info");
				while (sqlDataReader.Read())
				{
					list.Add(new SpaRoomInfo
					{
						RoomID = (int)sqlDataReader["RoomID"],
						RoomName = ((sqlDataReader["RoomName"] == null) ? "" : sqlDataReader["RoomName"].ToString()),
						PlayerID = (int)sqlDataReader["PlayerID"],
						PlayerName = ((sqlDataReader["PlayerName"] == null) ? "" : sqlDataReader["PlayerName"].ToString()),
						Pwd = ((sqlDataReader["Pwd"].ToString() == null) ? "" : sqlDataReader["Pwd"].ToString()),
						AvailTime = (int)sqlDataReader["AvailTime"],
						MaxCount = (int)sqlDataReader["MaxCount"],
						BeginTime = (DateTime)sqlDataReader["BeginTime"],
						BreakTime = (DateTime)sqlDataReader["BreakTime"],
						RoomIntroduction = ((sqlDataReader["RoomIntroduction"] == null) ? "" : sqlDataReader["RoomIntroduction"].ToString()),
						RoomType = (int)sqlDataReader["RoomType"],
						ServerID = (int)sqlDataReader["ServerID"],
						RoomNumber = (int)sqlDataReader["RoomNumber"]
					});
				}
				return list.ToArray();
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSpaRoomInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x0600894E RID: 35150 RVA: 0x002E313C File Offset: 0x002E133C
		public bool DisposeSpaRoomInfo(int RoomID)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@RoomID", RoomID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[1].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Dispose_Spa_Room_Info", array);
				flag = (int)array[1].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("DisposeSpaRoomInfo", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600894F RID: 35151 RVA: 0x002E31E4 File Offset: 0x002E13E4
		public bool InsertSpaPubRoomInfo(SpaRoomInfo info)
		{
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@RoomName", info.RoomName),
					new SqlParameter("@AvailTime", info.AvailTime),
					new SqlParameter("@MaxCount", info.MaxCount),
					new SqlParameter("@MapIndex", info.MapIndex),
					new SqlParameter("@BeginTime", info.BeginTime),
					new SqlParameter("@BreakTime", info.BreakTime),
					new SqlParameter("@RoomIntroduction", info.RoomIntroduction),
					new SqlParameter("@RoomType", info.RoomType),
					new SqlParameter("@ServerID", info.ServerID),
					new SqlParameter("@RoomNumber", info.RoomNumber),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[10].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Insert_Spa_PubRoom_Info", array);
				int num = (int)array[10].Value;
				bool flag = num > 0;
				if (flag)
				{
					info.RoomID = (int)array[10].Value;
					return true;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("InsertSpaRoomInfo", ex);
				}
			}
			return false;
		}

		// Token: 0x06008950 RID: 35152 RVA: 0x002E3390 File Offset: 0x002E1590
		public bool UpdateBreakTimeWhereSpaServerStop()
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[0].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Update_Spa_Room_Info_Sever_Stop", array);
				flag = (int)array[0].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Spa_UpdateBreakTimeWhereServerStop", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008951 RID: 35153 RVA: 0x002E3424 File Offset: 0x002E1624
		public bool InsertSpaRoomInfo(SpaRoomInfo info)
		{
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@RoomName", info.RoomName),
					new SqlParameter("@PlayerID", info.PlayerID),
					new SqlParameter("@PlayerName", info.PlayerName),
					new SqlParameter("@Pwd", info.Pwd),
					new SqlParameter("@AvailTime", info.AvailTime),
					new SqlParameter("@MaxCount", info.MaxCount),
					new SqlParameter("@MapIndex", info.MapIndex),
					new SqlParameter("@BeginTime", info.BeginTime),
					new SqlParameter("@BreakTime", info.BreakTime),
					new SqlParameter("@RoomIntroduction", info.RoomIntroduction),
					new SqlParameter("@RoomType", info.RoomType),
					new SqlParameter("@ServerID", info.ServerID),
					new SqlParameter("@RoomNumber", info.RoomNumber),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[13].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Insert_Spa_Room_Info", array);
				int num = (int)array[13].Value;
				bool flag = num > 0;
				if (flag)
				{
					info.RoomID = (int)array[13].Value;
					return true;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("InsertSpaRoomInfo", ex);
				}
			}
			return false;
		}

		// Token: 0x06008952 RID: 35154 RVA: 0x002E3610 File Offset: 0x002E1810
		public bool UpdateSpaRoomInfo(SpaRoomInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@RoomID", info.RoomID),
					new SqlParameter("@RoomName", info.RoomName),
					new SqlParameter("@Pwd", info.Pwd),
					new SqlParameter("@AvailTime", info.AvailTime),
					new SqlParameter("@BreakTime", info.BreakTime),
					new SqlParameter("@roomIntroduction", info.RoomIntroduction),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[6].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Update_Spa_Room_Info", array);
				flag = (int)array[6].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateSpaRoomInfo", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008953 RID: 35155 RVA: 0x002E3724 File Offset: 0x002E1924
		public bool AddUserCaddy(UserCaddyInfo info)
		{
			bool flag = false;
			bool flag2;
			try
			{
				this.db.RunProcedure("SP_Users_Caddy_Add", new SqlParameter[]
				{
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@Type", info.Type),
					new SqlParameter("@TemplateID", info.TemplateID)
				});
				flag = true;
				flag2 = flag;
			}
			catch (Exception ex)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("AddUserCaddy", ex);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x06008954 RID: 35156 RVA: 0x002E37D8 File Offset: 0x002E19D8
		public UserCaddyInfo[] GetUserCaddy(int type)
		{
			List<UserCaddyInfo> list = new List<UserCaddyInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@Type", SqlDbType.Int, 5)
				};
				array[0].Value = type;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Caddy_All", array);
				while (sqlDataReader.Read())
				{
					list.Add(new UserCaddyInfo
					{
						ID = (int)sqlDataReader["ID"],
						UserID = (int)sqlDataReader["UserID"],
						Type = (int)sqlDataReader["Type"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						AddTime = (DateTime)sqlDataReader["AddTime"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008955 RID: 35157 RVA: 0x002E3950 File Offset: 0x002E1B50
		public PlayerEliteGameInfo[] GetAllPlayerEliteGame()
		{
			List<PlayerEliteGameInfo> list = new List<PlayerEliteGameInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Elite_Game_Data_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitPlayerEliteGame(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllPlayerEliteGame", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008956 RID: 35158 RVA: 0x002E3A0C File Offset: 0x002E1C0C
		public PlayerEliteGameInfo InitPlayerEliteGame(SqlDataReader reader)
		{
			return new PlayerEliteGameInfo
			{
				UserID = (int)reader["UserID"],
				NickName = ((reader["NickName"] == null) ? "" : reader["NickName"].ToString()),
				GroupType = (int)reader["GroupType"],
				Rank = (int)reader["Rank"],
				CurrentPoint = (int)reader["CurrentPoint"],
				Status = (int)reader["Status"],
				MatchOrderNumber = (int)reader["MatchOrderNumber"],
				Winer = (int)reader["Winer"],
				ReadyStatus = (bool)reader["ReadyStatus"],
				RoundType = (int)reader["RoundType"],
				Grade = (int)reader["Grade"],
				Blood = (int)reader["Blood"],
				FightPower = (int)reader["FightPower"],
				TotalMatch = (int)reader["TotalMatch"]
			};
		}

		// Token: 0x06008957 RID: 35159 RVA: 0x002E3B7C File Offset: 0x002E1D7C
		public bool AddPlayerEliteGame(PlayerEliteGameInfo item)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", item.UserID),
					new SqlParameter("@NickName", item.NickName),
					new SqlParameter("@GroupType", item.GroupType),
					new SqlParameter("@Rank", item.Rank),
					new SqlParameter("@CurrentPoint", item.CurrentPoint),
					new SqlParameter("@Status", item.Status),
					new SqlParameter("@MatchOrderNumber", item.MatchOrderNumber),
					new SqlParameter("@Winer", item.Winer),
					new SqlParameter("@ReadyStatus", item.ReadyStatus),
					new SqlParameter("@RoundType", item.RoundType),
					new SqlParameter("@Grade", item.Grade),
					new SqlParameter("@Blood", item.Blood),
					new SqlParameter("@FightPower", item.FightPower),
					new SqlParameter("@UserMoneyPay", item.UserMoneyPay),
					new SqlParameter("@TotalMatch", item.TotalMatch),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[15].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Elite_Game_Data_Add", array);
				flag = (int)array[14].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddPlayerEliteGame", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008958 RID: 35160 RVA: 0x002E3D88 File Offset: 0x002E1F88
		public bool ClearEliteGameData()
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[0];
				flag = this.db.RunProcedure("SP_Elite_Game_Data_Delete", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_Elite_Game_Data_Delete", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008959 RID: 35161 RVA: 0x002E3DF4 File Offset: 0x002E1FF4
		public bool ResetEliteGame(int point)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@EliteScore", point)
				};
				flag = this.db.RunProcedure("SP_Elite_Game_Reset", array);
				return flag;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600895A RID: 35162 RVA: 0x002E3E74 File Offset: 0x002E2074
		public bool ClearLeagueGame()
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[0];
				flag = this.db.RunProcedure("SP_LeagueGame_Clear", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("ClearTreasure", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600895B RID: 35163 RVA: 0x002E3EE0 File Offset: 0x002E20E0
		public bool ClearMoonLight()
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[0];
				flag = this.db.RunProcedure("SP_MoonLight_Clear", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("ClearTreasure", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600895C RID: 35164 RVA: 0x002E3F4C File Offset: 0x002E214C
		public bool ClearBoguAdventure()
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[0];
				flag = this.db.RunProcedure("SP_BoguAdventure_Clear", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("ClearTreasure", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600895D RID: 35165 RVA: 0x002E3FB8 File Offset: 0x002E21B8
		public bool ClearBoGuTurn()
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[0];
				flag = this.db.RunProcedure("SP_BoGuTur_Clear", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("ClearTreasure", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600895E RID: 35166 RVA: 0x002E4024 File Offset: 0x002E2224
		public bool ClearTreasure()
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[0];
				flag = this.db.RunProcedure("SP_TreasureHunting_Clear", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("ClearTreasure", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600895F RID: 35167 RVA: 0x002E4090 File Offset: 0x002E2290
		public bool AddTreasureTicket()
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[0];
				flag = this.db.RunProcedure("SP_TreasureHunting_Ticket_Add", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddTreasureTicket", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008960 RID: 35168 RVA: 0x002E40FC File Offset: 0x002E22FC
		public bool AddTreasureAdvTicket()
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[0];
				flag = this.db.RunProcedure("SP_TreasureHunting_AdvTicket_Add", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddTreasureAdvTicket", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008961 RID: 35169 RVA: 0x002E4168 File Offset: 0x002E2368
		public UserEmblemInfo InitUserEmblemInfo(SqlDataReader reader)
		{
			return new UserEmblemInfo
			{
				ID = (int)reader["ID"],
				UserID = (int)reader["UserID"],
				TemplateID = (int)reader["TemplateID"],
				EquipPos = (int)reader["EquipPos"],
				EquipIndex = (int)reader["EquipIndex"],
				MainType = (int)reader["MainType"],
				MainValue = (int)reader["MainValue"],
				StrValue = (string)reader["StrValue"],
				IsLock = (bool)reader["IsLock"],
				IsExist = (bool)reader["IsExist"]
			};
		}

		// Token: 0x06008962 RID: 35170 RVA: 0x002E4268 File Offset: 0x002E2468
		public UserScrollInfo InitUserScrollInfo(SqlDataReader reader)
		{
			return new UserScrollInfo
			{
				ID = (int)reader["ID"],
				UserID = (int)reader["UserID"],
				TemplateID = (int)reader["TemplateID"],
				Count = (int)reader["Count"],
				IsBind = (bool)reader["IsBind"],
				IsExist = (bool)reader["IsExist"]
			};
		}

		// Token: 0x06008963 RID: 35171 RVA: 0x002E430C File Offset: 0x002E250C
		public List<UserEmblemInfo> GetSingleUserEmblem(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			List<UserEmblemInfo> list = new List<UserEmblemInfo>();
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", UserID)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Get_User_Emblem", array);
				while (sqlDataReader.Read())
				{
					list.Add(this.InitUserEmblemInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSingleUserEmblem", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list;
		}

		// Token: 0x06008964 RID: 35172 RVA: 0x002E43E0 File Offset: 0x002E25E0
		public List<UserScrollInfo> GetSingleUserScroll(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			List<UserScrollInfo> list = new List<UserScrollInfo>();
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", UserID)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Get_User_Scroll", array);
				while (sqlDataReader.Read())
				{
					list.Add(this.InitUserScrollInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSingleUserScroll", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list;
		}

		// Token: 0x06008965 RID: 35173 RVA: 0x002E44B4 File Offset: 0x002E26B4
		public bool AddOrUpdateUserEmblem(UserEmblemInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[11];
				array[0] = new SqlParameter("@ID", info.ID);
				SqlParameter[] array2 = array;
				bool flag2 = info.ID == 0;
				if (flag2)
				{
					array2[0].Direction = ParameterDirection.Output;
				}
				array2[1] = new SqlParameter("@UserID", info.UserID);
				array2[2] = new SqlParameter("@TemplateID", info.TemplateID);
				array2[3] = new SqlParameter("@EquipPos", info.EquipPos);
				array2[4] = new SqlParameter("@EquipIndex", info.EquipIndex);
				array2[5] = new SqlParameter("@MainType", info.MainType);
				array2[6] = new SqlParameter("@MainValue", info.MainValue);
				array2[7] = new SqlParameter("@StrValue", info.StrValue);
				array2[8] = new SqlParameter("@IsLock", info.IsLock);
				array2[9] = new SqlParameter("@IsExist", info.IsExist);
				array2[10] = new SqlParameter("@Result", SqlDbType.Int);
				array2[10].Direction = ParameterDirection.ReturnValue;
				flag = ((info.ID != 0) ? this.db.RunProcedure("SP_Users_Emblem_Update", array2) : this.db.RunProcedure("SP_Users_Emblem_Add", array2));
				bool flag3 = info.ID == 0;
				if (flag3)
				{
					info.ID = (int)array2[0].Value;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddOrUpdateUserEmblem", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008966 RID: 35174 RVA: 0x002E468C File Offset: 0x002E288C
		public bool AddOrUpdateUserScroll(UserScrollInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[7];
				array[0] = new SqlParameter("@ID", info.ID);
				SqlParameter[] array2 = array;
				bool flag2 = info.ID == 0;
				if (flag2)
				{
					array2[0].Direction = ParameterDirection.Output;
				}
				array2[1] = new SqlParameter("@UserID", info.UserID);
				array2[2] = new SqlParameter("@TemplateID", info.TemplateID);
				array2[3] = new SqlParameter("@Count", info.Count);
				array2[4] = new SqlParameter("@IsBind", info.IsBind);
				array2[5] = new SqlParameter("@IsExist", info.IsExist);
				array2[6] = new SqlParameter("@Result", SqlDbType.Int);
				array2[6].Direction = ParameterDirection.ReturnValue;
				flag = ((info.ID != 0) ? this.db.RunProcedure("SP_Users_Scroll_Update", array2) : this.db.RunProcedure("SP_Users_Scroll_Add", array2));
				bool flag3 = info.ID == 0;
				if (flag3)
				{
					info.ID = (int)array2[0].Value;
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddOrUpdateUserEmblem", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008967 RID: 35175 RVA: 0x002E4808 File Offset: 0x002E2A08
		public bool AddBoguAdventureData(UserBoguAdventureInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@NickName", info.NickName),
					new SqlParameter("@CurrentIndex", info.CurrentIndex),
					new SqlParameter("@Blood", info.Blood),
					new SqlParameter("@Score", info.Score),
					new SqlParameter("@IsAcquireAward1", info.IsAcquireAward1),
					new SqlParameter("@IsAcquireAward2", info.IsAcquireAward2),
					new SqlParameter("@IsAcquireAward3", info.IsAcquireAward3),
					new SqlParameter("@OpenCount", info.OpenCount),
					new SqlParameter("@FindMineTimes", info.FindMineTimes),
					new SqlParameter("@FreeResetCount", info.FreeResetCount),
					new SqlParameter("@ResetCount", info.ResetCount),
					new SqlParameter("@CellInfo", info.CellInfo),
					new SqlParameter("@AwardCount", info.AwardCount),
					new SqlParameter("@LastEnterGame", info.LastEnterGame),
					new SqlParameter("@ID", info.ID)
				};
				array[15].Direction = ParameterDirection.Output;
				flag = this.db.RunProcedure("SP_BoguAdventure_Add", array);
				info.ID = (int)array[15].Value;
				info.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddBoguAdventureData", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008968 RID: 35176 RVA: 0x002E4A20 File Offset: 0x002E2C20
		public bool UpdateBoguAdventureData(UserBoguAdventureInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@NickName", info.NickName),
					new SqlParameter("@CurrentIndex", info.CurrentIndex),
					new SqlParameter("@Blood", info.Blood),
					new SqlParameter("@Score", info.Score),
					new SqlParameter("@IsAcquireAward1", info.IsAcquireAward1),
					new SqlParameter("@IsAcquireAward2", info.IsAcquireAward2),
					new SqlParameter("@IsAcquireAward3", info.IsAcquireAward3),
					new SqlParameter("@OpenCount", info.OpenCount),
					new SqlParameter("@FindMineTimes", info.FindMineTimes),
					new SqlParameter("@FreeResetCount", info.FreeResetCount),
					new SqlParameter("@ResetCount", info.ResetCount),
					new SqlParameter("@CellInfo", info.CellInfo),
					new SqlParameter("@AwardCount", info.AwardCount),
					new SqlParameter("@LastEnterGame", info.LastEnterGame)
				};
				flag = this.db.RunProcedure("SP_BoguAdventure_Update", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateBoguAdventureData", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008969 RID: 35177 RVA: 0x002E4C10 File Offset: 0x002E2E10
		public UserBoguAdventureInfo GetSingleBoguAdventure(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_BoguAdventure_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return this.InitBoguAdventureDataInfo(sqlDataReader);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSingleBoguAdventure", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x0600896A RID: 35178 RVA: 0x002E4CE0 File Offset: 0x002E2EE0
		public UserBoguAdventureInfo InitBoguAdventureDataInfo(SqlDataReader dr)
		{
			return new UserBoguAdventureInfo
			{
				ID = (int)dr["ID"],
				UserID = (int)dr["UserID"],
				NickName = ((dr["NickName"] == null) ? "" : dr["NickName"].ToString()),
				CurrentIndex = (int)dr["CurrentIndex"],
				Blood = (int)dr["Blood"],
				Score = (int)dr["Score"],
				IsAcquireAward1 = (int)dr["IsAcquireAward1"],
				IsAcquireAward2 = (int)dr["IsAcquireAward2"],
				IsAcquireAward3 = (int)dr["IsAcquireAward3"],
				OpenCount = (int)dr["OpenCount"],
				FindMineTimes = (int)dr["FindMineTimes"],
				FreeResetCount = (int)dr["FreeResetCount"],
				ResetCount = (int)dr["ResetCount"],
				CellInfo = ((dr["CellInfo"] == null) ? "" : dr["CellInfo"].ToString()),
				AwardCount = ((dr["AwardCount"] == null) ? "" : dr["AwardCount"].ToString()),
				LastEnterGame = (DateTime)dr["LastEnterGame"]
			};
		}

		// Token: 0x0600896B RID: 35179 RVA: 0x002E4EA8 File Offset: 0x002E30A8
		public UserPairBoxInfo GetSinglePairBox(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_PairBox_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return this.InitPairBoxDataInfo(sqlDataReader);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSinglePairBox", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x0600896C RID: 35180 RVA: 0x002E4F78 File Offset: 0x002E3178
		public bool AddPairBoxData(UserPairBoxInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@ReelCount", info.ReelCount),
					new SqlParameter("@FreeReelCount", info.FreeReelCount),
					new SqlParameter("@MarkCount", info.MarkCount),
					new SqlParameter("@TotalBuyCount", info.TotalBuyCount),
					new SqlParameter("@FreeResetCount", info.FreeResetCount),
					new SqlParameter("@AwardCount", info.AwardCount),
					new SqlParameter("@LastEnterGame", info.LastEnterGame),
					new SqlParameter("@ID", info.ID)
				};
				array[8].Direction = ParameterDirection.Output;
				flag = this.db.RunProcedure("SP_PairBox_Add", array);
				info.ID = (int)array[8].Value;
				info.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddPairBoxData", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600896D RID: 35181 RVA: 0x002E50E8 File Offset: 0x002E32E8
		public bool UpdatePairBoxData(UserPairBoxInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@ReelCount", info.ReelCount),
					new SqlParameter("@FreeReelCount", info.FreeReelCount),
					new SqlParameter("@MarkCount", info.MarkCount),
					new SqlParameter("@TotalBuyCount", info.TotalBuyCount),
					new SqlParameter("@FreeResetCount", info.FreeResetCount),
					new SqlParameter("@AwardCount", info.AwardCount),
					new SqlParameter("@LastEnterGame", info.LastEnterGame)
				};
				flag = this.db.RunProcedure("SP_PairBox_Update", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdatePairBoxData", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600896E RID: 35182 RVA: 0x002E5228 File Offset: 0x002E3428
		public UserPairBoxInfo InitPairBoxDataInfo(SqlDataReader dr)
		{
			return new UserPairBoxInfo
			{
				ID = (int)dr["ID"],
				UserID = (int)dr["UserID"],
				ReelCount = (int)dr["ReelCount"],
				FreeReelCount = (int)dr["FreeReelCount"],
				MarkCount = (int)dr["MarkCount"],
				TotalBuyCount = (int)dr["TotalBuyCount"],
				FreeResetCount = (int)dr["FreeResetCount"],
				AwardCount = (string)dr["AwardCount"],
				LastEnterGame = (DateTime)dr["LastEnterGame"]
			};
		}

		// Token: 0x0600896F RID: 35183 RVA: 0x002E5310 File Offset: 0x002E3510
		public UserAvatarInfo[] GetSingleUserAvatarColectionInfo(int UserID)
		{
			List<UserAvatarInfo> list = new List<UserAvatarInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Avatar_Single", array);
				while (sqlDataReader.Read())
				{
					UserAvatarInfo userAvatarInfo = new UserAvatarInfo
					{
						ID = (int)sqlDataReader["ID"],
						UserID = (int)sqlDataReader["UserID"],
						endTime = (DateTime)sqlDataReader["endTime"],
						dataId = (int)sqlDataReader["dataId"],
						ActiveCount = (int)sqlDataReader["ActiveCount"],
						Sex = (int)sqlDataReader["Sex"],
						ActiveDress = ((sqlDataReader["ActiveDress"] == null) ? "" : sqlDataReader["ActiveDress"].ToString())
					};
					list.Add(userAvatarInfo);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSingleUserAvatarColectionInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008970 RID: 35184 RVA: 0x002E54CC File Offset: 0x002E36CC
		public bool AddUserAvatarColectionInfo(UserAvatarInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[8];
				array[0] = new SqlParameter("@ID", info.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@UserID", info.UserID);
				array2[2] = new SqlParameter("@endTime", info.endTime);
				array2[3] = new SqlParameter("@dataId", info.dataId);
				array2[4] = new SqlParameter("@ActiveCount", info.ActiveCount);
				array2[5] = new SqlParameter("@Sex", info.Sex);
				array2[6] = new SqlParameter("@ActiveDress", info.ActiveDress);
				array2[7] = new SqlParameter("@Result", SqlDbType.Int);
				array2[7].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_Avatar_Add", array2);
				flag = (int)array2[7].Value == 0;
				info.ID = (int)array2[0].Value;
				info.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddUserAvatarColectionInfo", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008971 RID: 35185 RVA: 0x002E5654 File Offset: 0x002E3854
		public bool UpdateUserAvatarColectionInfo(UserAvatarInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@endTime", info.endTime),
					new SqlParameter("@dataId", info.dataId),
					new SqlParameter("@ActiveCount", info.ActiveCount),
					new SqlParameter("@Sex", info.Sex),
					new SqlParameter("@ActiveDress", info.ActiveDress),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[7].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_Avatar_Update", array);
				flag = (int)array[7].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateUserAvatarColectionInfo", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008972 RID: 35186 RVA: 0x002E578C File Offset: 0x002E398C
		public UserActiveInfo InitActiveSystem(SqlDataReader reader)
		{
			return new UserActiveInfo
			{
				ID = (int)reader["ID"],
				UserID = (int)reader["UserID"],
				NickName = ((reader["NickName"] == null) ? "" : reader["NickName"].ToString()),
				LastRefresh = (DateTime)reader["LastRefresh"],
				CurRefreshedTimes = (int)reader["CurRefreshedTimes"],
				KeyCount = (int)reader["KeyCount"],
				ChipCount = (int)reader["ChipCount"],
				LimitCount = (int)reader["LimitCount"],
				OpenCount = (int)reader["OpenCount"],
				MoonScore = (int)reader["MoonScore"],
				RewardState = ((reader["RewardState"] == null) ? "" : reader["RewardState"].ToString()),
				IsRecharged = (bool)reader["IsRecharged"],
				IsGetAwardRecharged = (bool)reader["IsGetAwardRecharged"],
				SignInCurrentID = (int)reader["SignInCurrentID"],
				SignInLastDate = (DateTime)reader["SignInLastDate"],
				DailyChargeNum = (int)reader["DailyChargeNum"],
				DailyChargeAward = ((reader["DailyChargeAward"] == null) ? "" : reader["DailyChargeAward"].ToString()),
				TotalChargeNum = (int)reader["TotalChargeNum"],
				TotalChargeAward = ((reader["TotalChargeAward"] == null) ? "" : reader["TotalChargeAward"].ToString()),
				TotalXFMoney = (int)reader["TotalXFMoney"],
				TotalXFAward = ((reader["TotalXFAward"] == null) ? "" : reader["TotalXFAward"].ToString()),
				ChargeChangeAward = ((reader["ChargeChangeAward"] == null) ? "" : reader["ChargeChangeAward"].ToString()),
				lastEnterYearMonter = (DateTime)reader["lastEnterYearMonter"],
				BoxState = ((reader["BoxState"] == null) ? "" : reader["BoxState"].ToString()),
				ChallengeNum = (int)reader["ChallengeNum"],
				BuyBuffNum = (int)reader["BuyBuffNum"],
				DamageNum = (int)reader["DamageNum"]
			};
		}

		// Token: 0x06008973 RID: 35187 RVA: 0x002E5AA0 File Offset: 0x002E3CA0
		public UserActiveInfo GetSingleActiveSystem(int userId)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", userId)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Users_ActiveSystem_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return this.InitActiveSystem(sqlDataReader);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSigleActiveSystem", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x06008974 RID: 35188 RVA: 0x002E5B68 File Offset: 0x002E3D68
		public bool AddActiveSystem(UserActiveInfo item)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[28];
				array[0] = new SqlParameter("@ID", item.ID);
				array[0].Direction = ParameterDirection.Output;
				array[1] = new SqlParameter("@UserID", item.UserID);
				array[2] = new SqlParameter("@NickName", item.NickName);
				array[3] = new SqlParameter("@LastRefresh", item.LastRefresh);
				array[4] = new SqlParameter("@CurRefreshedTimes", item.CurRefreshedTimes);
				array[5] = new SqlParameter("@KeyCount", item.KeyCount);
				array[6] = new SqlParameter("@ChipCount", item.ChipCount);
				array[7] = new SqlParameter("@LimitCount", item.LimitCount);
				array[8] = new SqlParameter("@OpenCount", item.OpenCount);
				array[9] = new SqlParameter("@MoonScore", item.MoonScore);
				array[10] = new SqlParameter("@RewardState", item.RewardState);
				array[11] = new SqlParameter("@IsRecharged", item.IsRecharged);
				array[12] = new SqlParameter("@IsGetAwardRecharged", item.IsGetAwardRecharged);
				array[13] = new SqlParameter("@SignInCurrentID", item.SignInCurrentID);
				array[14] = new SqlParameter("@SignInLastDate", item.SignInLastDate);
				array[15] = new SqlParameter("@DailyChargeNum", item.DailyChargeNum);
				array[16] = new SqlParameter("@DailyChargeAward", item.DailyChargeAward);
				array[17] = new SqlParameter("@TotalChargeNum", item.TotalChargeNum);
				array[18] = new SqlParameter("@TotalChargeAward", item.TotalChargeAward);
				array[19] = new SqlParameter("@TotalXFMoney", item.TotalXFMoney);
				array[20] = new SqlParameter("@TotalXFAward", item.TotalXFAward);
				array[21] = new SqlParameter("@ChargeChangeAward", item.ChargeChangeAward);
				array[23] = new SqlParameter("@lastEnterYearMonter", item.lastEnterYearMonter);
				array[24] = new SqlParameter("@BoxState", item.BoxState);
				array[25] = new SqlParameter("@ChallengeNum", item.ChallengeNum);
				array[26] = new SqlParameter("@BuyBuffNum", item.BuyBuffNum);
				array[27] = new SqlParameter("@DamageNum", item.DamageNum);
				array[22] = new SqlParameter("@Result", SqlDbType.Int);
				array[22].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_ActiveSystem_Add", array);
				flag = (int)array[22].Value == 0;
				item.ID = (int)array[0].Value;
				item.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddActiveSystem", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008975 RID: 35189 RVA: 0x002E5EA8 File Offset: 0x002E40A8
		public bool UpdateActiveSystem(UserActiveInfo item)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", item.ID),
					new SqlParameter("@UserID", item.UserID),
					new SqlParameter("@NickName", item.NickName),
					new SqlParameter("@LastRefresh", item.LastRefresh),
					new SqlParameter("@CurRefreshedTimes", item.CurRefreshedTimes),
					new SqlParameter("@KeyCount", item.KeyCount),
					new SqlParameter("@ChipCount", item.ChipCount),
					new SqlParameter("@LimitCount", item.LimitCount),
					new SqlParameter("@OpenCount", item.OpenCount),
					new SqlParameter("@MoonScore", item.MoonScore),
					new SqlParameter("@RewardState", item.RewardState),
					new SqlParameter("@IsRecharged", item.IsRecharged),
					new SqlParameter("@IsGetAwardRecharged", item.IsGetAwardRecharged),
					new SqlParameter("@SignInCurrentID", item.SignInCurrentID),
					new SqlParameter("@SignInLastDate", item.SignInLastDate),
					new SqlParameter("@DailyChargeNum", item.DailyChargeNum),
					new SqlParameter("@DailyChargeAward", item.DailyChargeAward),
					new SqlParameter("@TotalChargeNum", item.TotalChargeNum),
					new SqlParameter("@TotalChargeAward", item.TotalChargeAward),
					new SqlParameter("@TotalXFMoney", item.TotalXFMoney),
					new SqlParameter("@TotalXFAward", item.TotalXFAward),
					new SqlParameter("@ChargeChangeAward", item.ChargeChangeAward),
					new SqlParameter("@Result", SqlDbType.Int),
					new SqlParameter("@lastEnterYearMonter", item.lastEnterYearMonter),
					new SqlParameter("@BoxState", item.BoxState),
					new SqlParameter("@ChallengeNum", item.ChallengeNum),
					new SqlParameter("@BuyBuffNum", item.BuyBuffNum),
					new SqlParameter("@DamageNum", item.DamageNum)
				};
				array[22].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_ActiveSystem_Update", array);
				flag = (int)array[22].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateActiveSystem", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008976 RID: 35190 RVA: 0x002E61C4 File Offset: 0x002E43C4
		public UserGypsyInfo InitGypsyItemDataInfo(SqlDataReader dr)
		{
			return new UserGypsyInfo
			{
				ID = (int)dr["ID"],
				UserID = (int)dr["UserID"],
				GypsyID = (int)dr["GypsyID"],
				InfoID = (int)dr["InfoID"],
				Unit = (int)dr["Unit"],
				Num = (int)dr["Num"],
				Price = (int)dr["Price"],
				CanBuy = (int)dr["CanBuy"],
				Quality = (int)dr["Quality"]
			};
		}

		// Token: 0x06008977 RID: 35191 RVA: 0x002E62AC File Offset: 0x002E44AC
		public bool UpdateGypsyItemData(UserGypsyInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@GypsyID", info.GypsyID),
					new SqlParameter("@InfoID", info.InfoID),
					new SqlParameter("@Unit", info.Unit),
					new SqlParameter("@Num", info.Num),
					new SqlParameter("@Price", info.Price),
					new SqlParameter("@CanBuy", info.CanBuy),
					new SqlParameter("@Quality", info.Quality)
				};
				flag = this.db.RunProcedure("SP_Users_Gypsy_Update", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateGypsyItemData", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008978 RID: 35192 RVA: 0x002E63F0 File Offset: 0x002E45F0
		public bool AddGypsyItemData(UserGypsyInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@GypsyID", info.GypsyID),
					new SqlParameter("@InfoID", info.InfoID),
					new SqlParameter("@Unit", info.Unit),
					new SqlParameter("@Num", info.Num),
					new SqlParameter("@Price", info.Price),
					new SqlParameter("@CanBuy", info.CanBuy),
					new SqlParameter("@Quality", info.Quality)
				};
				array[0].Direction = ParameterDirection.Output;
				flag = this.db.RunProcedure("SP_Users_Gypsy_Add", array);
				info.ID = (int)array[0].Value;
				info.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddGypsyItemData", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008979 RID: 35193 RVA: 0x002E6568 File Offset: 0x002E4768
		public UserGypsyInfo[] GetSingleGypsyItemData(int ID)
		{
			List<UserGypsyInfo> list = new List<UserGypsyInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", SqlDbType.Int, 4)
				};
				array[0].Value = ID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Gypsy_Single", array);
				while (sqlDataReader.Read())
				{
					list.Add(this.InitGypsyItemDataInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllGypsyItemDataByID", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x0600897A RID: 35194 RVA: 0x002E664C File Offset: 0x002E484C
		public List<UserMoonLightBoxInfo> GetUserMoonItemSingles(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			List<UserMoonLightBoxInfo> list = new List<UserMoonLightBoxInfo>();
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", UserID)
				};
				this.db.GetReader(ref sqlDataReader, "SP_Users_MoonItems_Single", array);
				while (sqlDataReader.Read())
				{
					list.Add(this.InitMoonItem(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetUserMoonItemSingles", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list;
		}

		// Token: 0x0600897B RID: 35195 RVA: 0x002E6720 File Offset: 0x002E4920
		public UserMoonLightBoxInfo InitMoonItem(SqlDataReader reader)
		{
			return new UserMoonLightBoxInfo
			{
				ID = (int)reader["ID"],
				UserID = (int)reader["UserID"],
				TemplateID = (int)reader["TemplateID"],
				Count = (int)reader["Count"],
				IsBinds = (bool)reader["IsBinds"],
				IsExist = (bool)reader["IsExist"],
				IsDirty = false
			};
		}

		// Token: 0x0600897C RID: 35196 RVA: 0x002E67CC File Offset: 0x002E49CC
		public bool AddMoonGoods(UserMoonLightBoxInfo item)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[6];
				array[0] = new SqlParameter("@ID", item.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@UserID", item.UserID);
				array2[2] = new SqlParameter("@TemplateID", item.TemplateID);
				array2[3] = new SqlParameter("@Count", item.Count);
				array2[4] = new SqlParameter("@IsBinds", item.IsBinds);
				array2[5] = new SqlParameter("@IsExist", item.IsExist);
				flag = this.db.RunProcedure("SP_Users_MoonItems_Add", array2);
				item.ID = (int)array2[0].Value;
				item.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddMoonGoods", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600897D RID: 35197 RVA: 0x002E6900 File Offset: 0x002E4B00
		public bool UpdateMoonGoods(UserMoonLightBoxInfo item)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", item.ID),
					new SqlParameter("@UserID", item.UserID),
					new SqlParameter("@TemplateID", item.TemplateID),
					new SqlParameter("@Count", item.Count),
					new SqlParameter("@IsBinds", item.IsBinds),
					new SqlParameter("@IsExist", item.IsExist)
				};
				flag = this.db.RunProcedure("SP_Users_MoonItems_Update", array);
				item.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600897E RID: 35198 RVA: 0x002E6A04 File Offset: 0x002E4C04
		public UserEventInfo[] GetUserEventProcess(int userID)
		{
			SqlDataReader sqlDataReader = null;
			List<UserEventInfo> list = new List<UserEventInfo>();
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = userID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Event_Single", array);
				while (sqlDataReader.Read())
				{
					list.Add(new UserEventInfo
					{
						UserID = (int)sqlDataReader["UserID"],
						ActiveType = (int)sqlDataReader["ActiveType"],
						Conditions = (int)sqlDataReader["Conditions"],
						AwardGot = (int)sqlDataReader["AwardGot"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetUserEventProcess", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x0600897F RID: 35199 RVA: 0x002E6B48 File Offset: 0x002E4D48
		public bool UpdateUserEventProcess(UserEventInfo ex)
		{
			bool flag = false;
			bool flag2;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", ex.UserID),
					new SqlParameter("@ActiveType", ex.ActiveType),
					new SqlParameter("@Conditions", ex.Conditions),
					new SqlParameter("@AwardGot", ex.AwardGot)
				};
				flag = this.db.RunProcedure("SP_Users_Event_Update", array);
				ex.IsDirty = false;
				flag2 = flag;
			}
			catch (Exception ex2)
			{
				bool flag3 = !BaseBussiness.log.IsErrorEnabled;
				if (flag3)
				{
					flag2 = flag;
				}
				else
				{
					BaseBussiness.log.Error("UpdateUserEventProcess", ex2);
					flag2 = flag;
				}
			}
			return flag2;
		}

		// Token: 0x06008980 RID: 35200 RVA: 0x002E6C20 File Offset: 0x002E4E20
		public UserFarmInfo GetSingleFarm(int Id)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", SqlDbType.Int, 4)
				};
				array[0].Value = Id;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Farm_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return new UserFarmInfo
					{
						ID = (int)sqlDataReader["ID"],
						FarmID = (int)sqlDataReader["FarmID"],
						PayFieldMoney = (string)sqlDataReader["PayFieldMoney"],
						PayAutoMoney = (string)sqlDataReader["PayAutoMoney"],
						AutoPayTime = (DateTime)sqlDataReader["AutoPayTime"],
						AutoValidDate = (int)sqlDataReader["AutoValidDate"],
						VipLimitLevel = (int)sqlDataReader["VipLimitLevel"],
						FarmerName = (string)sqlDataReader["FarmerName"],
						GainFieldId = (int)sqlDataReader["GainFieldId"],
						MatureId = (int)sqlDataReader["MatureId"],
						KillCropId = (int)sqlDataReader["KillCropId"],
						isAutoId = (int)sqlDataReader["isAutoId"],
						isFarmHelper = (bool)sqlDataReader["isFarmHelper"],
						buyExpRemainNum = (int)sqlDataReader["buyExpRemainNum"],
						isArrange = (bool)sqlDataReader["isArrange"]
					};
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSingleFarm", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x06008981 RID: 35201 RVA: 0x002E6E64 File Offset: 0x002E5064
		public bool AddFarm(UserFarmInfo item)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@FarmID", item.FarmID),
					new SqlParameter("@PayFieldMoney", item.PayFieldMoney),
					new SqlParameter("@PayAutoMoney", item.PayAutoMoney),
					new SqlParameter("@AutoPayTime", item.AutoPayTime.ToString("MM/dd/yyyy HH:mm:ss")),
					new SqlParameter("@AutoValidDate", item.AutoValidDate),
					new SqlParameter("@VipLimitLevel", item.VipLimitLevel),
					new SqlParameter("@FarmerName", item.FarmerName),
					new SqlParameter("@GainFieldId", item.GainFieldId),
					new SqlParameter("@MatureId", item.MatureId),
					new SqlParameter("@KillCropId", item.KillCropId),
					new SqlParameter("@isAutoId", item.isAutoId),
					new SqlParameter("@isFarmHelper", item.isFarmHelper),
					new SqlParameter("@buyExpRemainNum", item.buyExpRemainNum),
					new SqlParameter("@isArrange", item.isArrange),
					new SqlParameter("@ID", item.ID)
				};
				array[14].Direction = ParameterDirection.Output;
				flag = this.db.RunProcedure("SP_Users_Farm_Add", array);
				item.ID = (int)array[14].Value;
				item.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddFarm", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008982 RID: 35202 RVA: 0x002E706C File Offset: 0x002E526C
		public bool UpdateFarm(UserFarmInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@FarmID", info.FarmID),
					new SqlParameter("@PayFieldMoney", info.PayFieldMoney),
					new SqlParameter("@PayAutoMoney", info.PayAutoMoney),
					new SqlParameter("@AutoPayTime", info.AutoPayTime.ToString("MM/dd/yyyy HH:mm:ss")),
					new SqlParameter("@AutoValidDate", info.AutoValidDate),
					new SqlParameter("@VipLimitLevel", info.VipLimitLevel),
					new SqlParameter("@FarmerName", info.FarmerName),
					new SqlParameter("@GainFieldId", info.GainFieldId),
					new SqlParameter("@MatureId", info.MatureId),
					new SqlParameter("@KillCropId", info.KillCropId),
					new SqlParameter("@isAutoId", info.isAutoId),
					new SqlParameter("@isFarmHelper", info.isFarmHelper),
					new SqlParameter("@buyExpRemainNum", info.buyExpRemainNum),
					new SqlParameter("@isArrange", info.isArrange)
				};
				flag = this.db.RunProcedure("SP_Users_Farm_Update", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateFarm", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008983 RID: 35203 RVA: 0x002E724C File Offset: 0x002E544C
		public UserFieldInfo[] GetSingleFields(int ID)
		{
			List<UserFieldInfo> list = new List<UserFieldInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", SqlDbType.Int, 4)
				};
				array[0].Value = ID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Fields_Single", array);
				while (sqlDataReader.Read())
				{
					list.Add(new UserFieldInfo
					{
						ID = (int)sqlDataReader["ID"],
						FarmID = (int)sqlDataReader["FarmID"],
						FieldID = (int)sqlDataReader["FieldID"],
						SeedID = (int)sqlDataReader["SeedID"],
						PlantTime = (DateTime)sqlDataReader["PlantTime"],
						AccelerateTime = (int)sqlDataReader["AccelerateTime"],
						FieldValidDate = (int)sqlDataReader["FieldValidDate"],
						PayTime = (DateTime)sqlDataReader["PayTime"],
						GainCount = (int)sqlDataReader["GainCount"],
						AutoSeedID = (int)sqlDataReader["AutoSeedID"],
						AutoFertilizerID = (int)sqlDataReader["AutoFertilizerID"],
						AutoSeedIDCount = (int)sqlDataReader["AutoSeedIDCount"],
						AutoFertilizerCount = (int)sqlDataReader["AutoFertilizerCount"],
						isAutomatic = (bool)sqlDataReader["isAutomatic"],
						AutomaticTime = (DateTime)sqlDataReader["AutomaticTime"],
						IsExit = (bool)sqlDataReader["IsExit"],
						payFieldTime = (int)sqlDataReader["payFieldTime"],
						FastForward = (int)sqlDataReader["FastForward"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_GetSingleFields", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008984 RID: 35204 RVA: 0x002E74EC File Offset: 0x002E56EC
		public bool AddFields(UserFieldInfo item)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@FarmID", item.FarmID),
					new SqlParameter("@FieldID", item.FieldID),
					new SqlParameter("@SeedID", item.SeedID),
					new SqlParameter("@PlantTime", item.PlantTime.ToString("MM/dd/yyyy HH:mm:ss")),
					new SqlParameter("@AccelerateTime", item.AccelerateTime),
					new SqlParameter("@FieldValidDate", item.FieldValidDate),
					new SqlParameter("@PayTime", item.PayTime.ToString("MM/dd/yyyy HH:mm:ss")),
					new SqlParameter("@GainCount", item.GainCount),
					new SqlParameter("@AutoSeedID", item.AutoSeedID),
					new SqlParameter("@AutoFertilizerID", item.AutoFertilizerID),
					new SqlParameter("@AutoSeedIDCount", item.AutoSeedIDCount),
					new SqlParameter("@AutoFertilizerCount", item.AutoFertilizerCount),
					new SqlParameter("@isAutomatic", item.isAutomatic),
					new SqlParameter("@AutomaticTime", item.AutomaticTime.ToString("MM/dd/yyyy HH:mm:ss")),
					new SqlParameter("@IsExit", item.IsExit),
					new SqlParameter("@payFieldTime", item.payFieldTime),
					new SqlParameter("@ID", item.ID),
					new SqlParameter("@FastForward", item.FastForward)
				};
				array[16].Direction = ParameterDirection.Output;
				flag = this.db.RunProcedure("SP_Users_Fields_Add", array);
				item.ID = (int)array[16].Value;
				item.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008985 RID: 35205 RVA: 0x002E7760 File Offset: 0x002E5960
		public bool UpdateFields(UserFieldInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@FarmID", info.FarmID),
					new SqlParameter("@FieldID", info.FieldID),
					new SqlParameter("@SeedID", info.SeedID),
					new SqlParameter("@PlantTime", info.PlantTime.ToString("yyyy/MM/dd HH:mm:ss")),
					new SqlParameter("@AccelerateTime", info.AccelerateTime),
					new SqlParameter("@FieldValidDate", info.FieldValidDate),
					new SqlParameter("@PayTime", info.PayTime.ToString("yyyy/MM/dd HH:mm:ss")),
					new SqlParameter("@GainCount", info.GainCount),
					new SqlParameter("@AutoSeedID", info.AutoSeedID),
					new SqlParameter("@AutoFertilizerID", info.AutoFertilizerID),
					new SqlParameter("@AutoSeedIDCount", info.AutoSeedIDCount),
					new SqlParameter("@AutoFertilizerCount", info.AutoFertilizerCount),
					new SqlParameter("@isAutomatic", info.isAutomatic),
					new SqlParameter("@AutomaticTime", info.AutomaticTime.ToString("yyyy/MM/dd HH:mm:ss")),
					new SqlParameter("@IsExit", info.IsExit),
					new SqlParameter("@payFieldTime", info.payFieldTime),
					new SqlParameter("@FastForward", info.FastForward)
				};
				flag = this.db.RunProcedure("SP_Users_Fields_Update", array);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008986 RID: 35206 RVA: 0x002E79AC File Offset: 0x002E5BAC
		public List<UserMarkInfo> GetSingleUserMarkInfo(int UserID)
		{
			List<UserMarkInfo> list = new List<UserMarkInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_Mark_Single", array);
				while (sqlDataReader.Read())
				{
					UserMarkInfo userMarkInfo = new UserMarkInfo
					{
						ID = (int)sqlDataReader["ID"],
						UserID = (int)sqlDataReader["UserID"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						BornTime = (int)sqlDataReader["BornTime"],
						BornLv = (int)sqlDataReader["BornLv"],
						HammerLv = (int)sqlDataReader["HammerLv"],
						HLv = (int)sqlDataReader["HLv"],
						Equip = (int)sqlDataReader["Equip"],
						MainPro = ((sqlDataReader["MainPro"] == null) ? "" : sqlDataReader["MainPro"].ToString()),
						Props = ((sqlDataReader["Props"] == null) ? "" : sqlDataReader["Props"].ToString()),
						SealProListStr = ((sqlDataReader["SealProListStr"] == null) ? "" : sqlDataReader["SealProListStr"].ToString()),
						SealSkillID = (int)sqlDataReader["SealSkillID"],
						IsBind = (bool)sqlDataReader["IsBind"],
						IsLock = (bool)sqlDataReader["IsLock"],
						IsExist = (bool)sqlDataReader["IsExist"]
					};
					list.Add(userMarkInfo);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSingleUserMarkInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list;
		}

		// Token: 0x06008987 RID: 35207 RVA: 0x002E7C40 File Offset: 0x002E5E40
		public bool AddUserMarkInfo(UserMarkInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[16];
				array[0] = new SqlParameter("@ID", info.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@UserID", info.UserID);
				array2[2] = new SqlParameter("@TemplateID", info.TemplateID);
				array2[3] = new SqlParameter("@BornTime", info.BornTime);
				array2[4] = new SqlParameter("@BornLv", info.BornLv);
				array2[5] = new SqlParameter("@HammerLv", info.HammerLv);
				array2[6] = new SqlParameter("@HLv", info.HLv);
				array2[7] = new SqlParameter("@Equip", info.Equip);
				array2[8] = new SqlParameter("@MainPro", info.MainPro);
				array2[9] = new SqlParameter("@Props", info.Props);
				array2[10] = new SqlParameter("@SealProListStr", info.SealProListStr);
				array2[11] = new SqlParameter("@SealSkillID", info.SealSkillID);
				array2[12] = new SqlParameter("@IsBind", info.IsBind);
				array2[13] = new SqlParameter("@IsLock", info.IsLock);
				array2[14] = new SqlParameter("@IsExist", info.IsExist);
				array2[15] = new SqlParameter("@Result", SqlDbType.Int);
				array2[15].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_Mark_Add", array2);
				flag = (int)array2[15].Value == 0;
				info.ID = (int)array2[0].Value;
				info.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddUserMarkInfo", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x06008988 RID: 35208 RVA: 0x002E7E88 File Offset: 0x002E6088
		public bool UpdateUserMarkInfo(UserMarkInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@TemplateID", info.TemplateID),
					new SqlParameter("@BornTime", info.BornTime),
					new SqlParameter("@BornLv", info.BornLv),
					new SqlParameter("@HammerLv", info.HammerLv),
					new SqlParameter("@HLv", info.HLv),
					new SqlParameter("@Equip", info.Equip),
					new SqlParameter("@MainPro", info.MainPro),
					new SqlParameter("@Props", info.Props),
					new SqlParameter("@SealProListStr", info.SealProListStr),
					new SqlParameter("@SealSkillID", info.SealSkillID),
					new SqlParameter("@IsBind", info.IsBind),
					new SqlParameter("@IsLock", info.IsLock),
					new SqlParameter("@IsExist", info.IsExist),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[15].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_Mark_Update", array);
				flag = (int)array[15].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateUserMarkInfo", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008989 RID: 35209 RVA: 0x002E808C File Offset: 0x002E628C
		public List<UserMarkSchemeInfo> GetSingleUserMarkSchemeInfo(int UserID)
		{
			List<UserMarkSchemeInfo> list = new List<UserMarkSchemeInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_MarkScheme_Single", array);
				while (sqlDataReader.Read())
				{
					UserMarkSchemeInfo userMarkSchemeInfo = new UserMarkSchemeInfo
					{
						ID = (int)sqlDataReader["ID"],
						UserID = (int)sqlDataReader["UserID"],
						SchemeID = (int)sqlDataReader["SchemeID"],
						SchemeData = ((sqlDataReader["SchemeData"] == null) ? "" : sqlDataReader["SchemeData"].ToString()),
						NickName = ((sqlDataReader["NickName"] == null) ? "" : sqlDataReader["NickName"].ToString())
					};
					list.Add(userMarkSchemeInfo);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSingleUserMarkSchemeInfo", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list;
		}

		// Token: 0x0600898A RID: 35210 RVA: 0x002E8228 File Offset: 0x002E6428
		public bool AddUserMarkSchemeInfo(UserMarkSchemeInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[6];
				array[0] = new SqlParameter("@ID", info.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@UserID", info.UserID);
				array2[2] = new SqlParameter("@SchemeID", info.SchemeID);
				array2[3] = new SqlParameter("@SchemeData", info.SchemeData);
				array2[4] = new SqlParameter("@NickName", info.NickName);
				array2[5] = new SqlParameter("@Result", SqlDbType.Int);
				array2[5].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_MarkScheme_Add", array2);
				flag = (int)array2[5].Value == 0;
				info.ID = (int)array2[0].Value;
				info.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddUserMarkSchemeInfo", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600898B RID: 35211 RVA: 0x002E8378 File Offset: 0x002E6578
		public bool UpdateUserMarkSchemeInfo(UserMarkSchemeInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@SchemeID", info.SchemeID),
					new SqlParameter("@SchemeData", info.SchemeData),
					new SqlParameter("@NickName", info.NickName),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[5].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_MarkScheme_Update", array);
				flag = (int)array[5].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateUserMarkSchemeInfo", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600898C RID: 35212 RVA: 0x002E847C File Offset: 0x002E667C
		public UserBoguTurnInfo GetSingleBoguTurn(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_BoguTurn_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return new UserBoguTurnInfo
					{
						ID = (int)sqlDataReader["ID"],
						UserID = (int)sqlDataReader["UserID"],
						NickName = ((sqlDataReader["NickName"] == null) ? "" : sqlDataReader["NickName"].ToString()),
						Type = (int)sqlDataReader["Type"],
						Level = (int)sqlDataReader["Level"],
						SubScript = (int)sqlDataReader["SubScript"],
						PurrMoneyCount = (int)sqlDataReader["PurrMoneyCount"],
						FreeCoin = (int)sqlDataReader["FreeCoin"],
						ChipCount = (int)sqlDataReader["ChipCount"],
						Score = (int)sqlDataReader["Score"],
						Claimed = ((sqlDataReader["Claimed"] == null) ? "" : sqlDataReader["Claimed"].ToString()),
						NiuniumoneyRestrict = (int)sqlDataReader["NiuniumoneyRestrict"],
						AddState = (int)sqlDataReader["AddState"]
					};
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSingleBoguTurn", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x0600898D RID: 35213 RVA: 0x002E86B8 File Offset: 0x002E68B8
		public bool AddBoguTurn(UserBoguTurnInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[14];
				array[0] = new SqlParameter("@ID", info.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@UserID", info.UserID);
				array2[2] = new SqlParameter("@NickName", info.NickName);
				array2[3] = new SqlParameter("@Type", info.Type);
				array2[4] = new SqlParameter("@Level", info.Level);
				array2[5] = new SqlParameter("@SubScript", info.SubScript);
				array2[6] = new SqlParameter("@PurrMoneyCount", info.PurrMoneyCount);
				array2[7] = new SqlParameter("@FreeCoin", info.FreeCoin);
				array2[8] = new SqlParameter("@ChipCount", info.ChipCount);
				array2[9] = new SqlParameter("@Score", info.Score);
				array2[10] = new SqlParameter("@Claimed", info.Claimed);
				array2[11] = new SqlParameter("@NiuniumoneyRestrict", info.NiuniumoneyRestrict);
				array2[12] = new SqlParameter("@AddState", info.AddState);
				array2[13] = new SqlParameter("@Result", SqlDbType.Int);
				array2[13].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_BoguTurn_Add", array2);
				flag = (int)array2[13].Value == 0;
				info.ID = (int)array2[0].Value;
				info.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddBoguTurn", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600898E RID: 35214 RVA: 0x002E88B4 File Offset: 0x002E6AB4
		public bool UpdateBoguTurn(UserBoguTurnInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@NickName", info.NickName),
					new SqlParameter("@Type", info.Type),
					new SqlParameter("@Level", info.Level),
					new SqlParameter("@SubScript", info.SubScript),
					new SqlParameter("@PurrMoneyCount", info.PurrMoneyCount),
					new SqlParameter("@FreeCoin", info.FreeCoin),
					new SqlParameter("@ChipCount", info.ChipCount),
					new SqlParameter("@Score", info.Score),
					new SqlParameter("@Claimed", info.Claimed),
					new SqlParameter("@NiuniumoneyRestrict", info.NiuniumoneyRestrict),
					new SqlParameter("@AddState", info.AddState),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[13].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_BoguTurn_Update", array);
				flag = (int)array[13].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateBoguTurn", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600898F RID: 35215 RVA: 0x002E8A8C File Offset: 0x002E6C8C
		public UserExpBottleInfo GetSingleExpBottle(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_Users_ExpBottle_Single", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return new UserExpBottleInfo
					{
						ID = (int)sqlDataReader["ID"],
						UserID = (int)sqlDataReader["UserID"],
						TemplateID = (int)sqlDataReader["TemplateID"],
						Exp = (int)sqlDataReader["Exp"],
						Stage = (int)sqlDataReader["Stage"]
					};
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSingleExpBottle", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x06008990 RID: 35216 RVA: 0x002E8BD0 File Offset: 0x002E6DD0
		public bool AddExpBottle(UserExpBottleInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[6];
				array[0] = new SqlParameter("@ID", info.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@UserID", info.UserID);
				array2[2] = new SqlParameter("@TemplateID", info.TemplateID);
				array2[3] = new SqlParameter("@Exp", info.Exp);
				array2[4] = new SqlParameter("@Stage", info.Stage);
				array2[5] = new SqlParameter("@Result", SqlDbType.Int);
				array2[5].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_ExpBottle_Add", array2);
				flag = (int)array2[5].Value == 0;
				info.ID = (int)array2[0].Value;
				info.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddExpBottle", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008991 RID: 35217 RVA: 0x002E8D04 File Offset: 0x002E6F04
		public bool UpdateExpBottle(UserExpBottleInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@TemplateID", info.TemplateID),
					new SqlParameter("@Exp", info.Exp),
					new SqlParameter("@Stage", info.Stage),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[5].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_ExpBottle_Update", array);
				flag = (int)array[5].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateExpBottle", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008992 RID: 35218 RVA: 0x002E8E10 File Offset: 0x002E7010
		public bool UpdateEditable(string table, string column, string valueStr, string condition)
		{
			bool flag = false;
			try
			{
				string text = string.Concat(new string[] { "UPDATE [dbo].[", table, "] SET [", column, "] = @", column, " WHERE ", condition });
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@" + column, valueStr)
				};
				flag = this.db.Exesqlcomm(text, array);
			}
			catch (Exception ex)
			{
				BaseBussiness.log.Error(ex.Message);
			}
			return flag;
		}

		// Token: 0x06008993 RID: 35219 RVA: 0x002E8EB8 File Offset: 0x002E70B8
		public bool UpdateWoCao(string table, string column, int valueStr, int condition)
		{
			bool flag = false;
			try
			{
				string text = string.Concat(new string[]
				{
					"UPDATE [dbo].[",
					table,
					"] SET [",
					column,
					"] = ",
					valueStr.ToString(),
					" WHERE UserID = ",
					condition.ToString()
				});
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter(column, valueStr)
				};
				flag = this.db.Exesqlcomm(text, array);
				Console.WriteLine(text);
			}
			catch (Exception ex)
			{
				BaseBussiness.log.Error(ex.Message);
			}
			return flag;
		}

		// Token: 0x06008994 RID: 35220 RVA: 0x002E8F6C File Offset: 0x002E716C
		public bool UpdateWoCao1(string table, string column, string valueStr, int condition)
		{
			bool flag = false;
			try
			{
				string text = string.Concat(new string[]
				{
					"UPDATE [dbo].[",
					table,
					"] SET [",
					column,
					"] = '",
					valueStr,
					"'WHERE UserID = ",
					condition.ToString()
				});
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter(column, valueStr)
				};
				flag = this.db.Exesqlcomm(text, array);
				Console.WriteLine(text);
			}
			catch (Exception ex)
			{
				BaseBussiness.log.Error(ex.Message);
			}
			return flag;
		}

		// Token: 0x06008995 RID: 35221 RVA: 0x002E9018 File Offset: 0x002E7218
		public bool UpdateGemStoneInfo(UserGemStone info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@FigSpiritId", info.FigSpiritId),
					new SqlParameter("@FigSpiritIdValue", info.FigSpiritIdValue),
					new SqlParameter("@EquipPlace", info.EquipPlace),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[5].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_UpdateGemStoneInfo", array);
				return true;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_UpdateGemStoneInfo", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008996 RID: 35222 RVA: 0x002E9110 File Offset: 0x002E7310
		public List<UserGemStone> GetSingleGemStones(int ID)
		{
			List<UserGemStone> list = new List<UserGemStone>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", SqlDbType.Int, 4)
				};
				array[0].Value = ID;
				this.db.GetReader(ref sqlDataReader, "SP_GetSingleGemStone", array);
				while (sqlDataReader.Read())
				{
					list.Add(new UserGemStone
					{
						ID = (int)sqlDataReader["ID"],
						UserID = (int)sqlDataReader["UserID"],
						FigSpiritId = (int)sqlDataReader["FigSpiritId"],
						FigSpiritIdValue = (string)sqlDataReader["FigSpiritIdValue"],
						EquipPlace = (int)sqlDataReader["EquipPlace"]
					});
				}
				return list;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_GetSingleUserGemStones", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list;
		}

		// Token: 0x06008997 RID: 35223 RVA: 0x002E9268 File Offset: 0x002E7468
		public bool AddUserGemStone(UserGemStone item)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[6];
				array[0] = new SqlParameter("@ID", item.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@UserID", item.UserID);
				array2[2] = new SqlParameter("@FigSpiritId", item.FigSpiritId);
				array2[3] = new SqlParameter("@FigSpiritIdValue", item.FigSpiritIdValue);
				array2[4] = new SqlParameter("@EquipPlace", item.EquipPlace);
				array2[5] = new SqlParameter("@Result", SqlDbType.Int);
				array2[5].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_Users_GemStones_Add", array2);
				flag = (int)array2[5].Value == 0;
				item.ID = (int)array2[0].Value;
				item.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return flag;
		}

		// Token: 0x06008998 RID: 35224 RVA: 0x002E9394 File Offset: 0x002E7594
		public UserChristmasInfo GetSingleUserChristmas(int UserID)
		{
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_GetSingleUserChristmas", array);
				bool flag = sqlDataReader.Read();
				if (flag)
				{
					return new UserChristmasInfo
					{
						ID = (int)sqlDataReader["ID"],
						UserID = (int)sqlDataReader["UserID"],
						Exp = (int)sqlDataReader["Exp"],
						AwardState = (int)sqlDataReader["AwardState"],
						Count = (int)sqlDataReader["Count"],
						PacksNumber = (int)sqlDataReader["PacksNumber"],
						LastPacks = (int)sqlDataReader["LastPacks"],
						GameBeginTime = (DateTime)sqlDataReader["GameBeginTime"],
						GameEndTime = (DateTime)sqlDataReader["GameEndTime"],
						IsEnter = (bool)sqlDataReader["IsEnter"],
						DayPacks = (int)sqlDataReader["DayPacks"],
						AvailTime = (int)sqlDataReader["AvailTime"]
					};
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetSingleUserChristmas", ex);
				}
			}
			finally
			{
				bool flag2 = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag2)
				{
					sqlDataReader.Close();
				}
			}
			return null;
		}

		// Token: 0x06008999 RID: 35225 RVA: 0x002E9598 File Offset: 0x002E7798
		public bool AddUserChristmas(UserChristmasInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[13];
				array[0] = new SqlParameter("@ID", info.ID);
				SqlParameter[] array2 = array;
				array2[0].Direction = ParameterDirection.Output;
				array2[1] = new SqlParameter("@UserID", info.UserID);
				array2[2] = new SqlParameter("@Exp", info.Exp);
				array2[3] = new SqlParameter("@AwardState", info.AwardState);
				array2[4] = new SqlParameter("@Count", info.Count);
				array2[5] = new SqlParameter("@PacksNumber", info.PacksNumber);
				array2[6] = new SqlParameter("@LastPacks", info.LastPacks);
				array2[7] = new SqlParameter("@GameBeginTime", info.GameBeginTime);
				array2[8] = new SqlParameter("@GameEndTime", info.GameEndTime);
				array2[9] = new SqlParameter("@IsEnter", info.IsEnter);
				array2[10] = new SqlParameter("@DayPacks", info.DayPacks);
				array2[11] = new SqlParameter("@AvailTime", info.AvailTime);
				array2[12] = new SqlParameter("@Result", SqlDbType.Int);
				array2[12].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_UserChristmas_Add", array2);
				flag = (int)array2[12].Value == 0;
				info.ID = (int)array2[0].Value;
				info.IsDirty = false;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("AddUserChristmas", ex);
				}
			}
			finally
			{
			}
			return flag;
		}

		// Token: 0x0600899A RID: 35226 RVA: 0x002E97A4 File Offset: 0x002E79A4
		public bool UpdateUserChristmas(UserChristmasInfo info)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ID", info.ID),
					new SqlParameter("@UserID", info.UserID),
					new SqlParameter("@Exp", info.Exp),
					new SqlParameter("@AwardState", info.AwardState),
					new SqlParameter("@Count", info.Count),
					new SqlParameter("@PacksNumber", info.PacksNumber),
					new SqlParameter("@LastPacks", info.LastPacks),
					new SqlParameter("@GameBeginTime", info.GameBeginTime),
					new SqlParameter("@GameEndTime", info.GameEndTime),
					new SqlParameter("@IsEnter", info.IsEnter),
					new SqlParameter("@DayPacks", info.DayPacks),
					new SqlParameter("@AvailTime", info.AvailTime),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[12].Direction = ParameterDirection.ReturnValue;
				this.db.RunProcedure("SP_UpdateUserChristmas", array);
				flag = (int)array[12].Value == 0;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("UpdateUserChristmas", ex);
				}
			}
			return flag;
		}

		// Token: 0x0600899B RID: 35227 RVA: 0x002E996C File Offset: 0x002E7B6C
		public UserZhanlingInfo InitUserZhanlingInfo(SqlDataReader reader)
		{
			return new UserZhanlingInfo
			{
				UserID = (int)reader["UserID"],
				zhanlingID = (int)reader["zhanlingID"],
				IsGet = (int)reader["IsGet"]
			};
		}

		// Token: 0x0600899C RID: 35228 RVA: 0x002E99C8 File Offset: 0x002E7BC8
		public bool UpdateZhanlingData(int UserID, int zhanlingID, int IsGet)
		{
			bool flag = false;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", UserID),
					new SqlParameter("@zhanlingID", zhanlingID),
					new SqlParameter("@IsGet", IsGet)
				};
				Console.WriteLine(UserID);
				Console.WriteLine(zhanlingID);
				Console.WriteLine(IsGet);
				flag = this.db.RunProcedure("SP_Insert_Update_Userzhanling", array);
				Console.WriteLine("执行过程" + flag.ToString());
			}
			catch (Exception ex)
			{
				Exception ex2 = ex;
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_Insert_Update_Userzhanling", ex2);
				}
			}
			return flag;
		}

		// Token: 0x0600899D RID: 35229 RVA: 0x002E9A9C File Offset: 0x002E7C9C
		public UserZhanlingInfo[] GetUserzhanlingInfo(int UserID)
		{
			List<UserZhanlingInfo> list = new List<UserZhanlingInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@UserID", SqlDbType.Int, 4)
				};
				array[0].Value = UserID;
				this.db.GetReader(ref sqlDataReader, "SP_GetUserzhanling", array);
				while (sqlDataReader.Read())
				{
					UserZhanlingInfo userZhanlingInfo = this.InitUserZhanlingInfo(sqlDataReader);
					list.Add(userZhanlingInfo);
				}
				return list.ToArray();
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("SP_GetUserzhanling", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x0600899E RID: 35230 RVA: 0x002E9B90 File Offset: 0x002E7D90
		public zhanling[] GetAllZhaning()
		{
			List<zhanling> list = new List<zhanling>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_zhanling_all");
				while (sqlDataReader.Read())
				{
					list.Add(new zhanling
					{
						TemplateID = (int)sqlDataReader["TemplateID"],
						ID = (int)sqlDataReader["ID"],
						Type = (int)sqlDataReader["Type"],
						Count = (int)sqlDataReader["Count"],
						Level = (int)sqlDataReader["Level"]
					});
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("GetAllClothProperty", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x0400549A RID: 21658
		private string AreaName = "欢庆元旦";

		// Token: 0x0400549B RID: 21659
		private int AreaID = 1;

		// Token: 0x0400549C RID: 21660
		private string VersionFlash = "3.6";

		// Token: 0x0400549D RID: 21661
		private string connectionStringg = ConfigurationManager.ConnectionStrings["PlayerData"].ConnectionString;

		// Token: 0x0400549E RID: 21662
		private string ServerData = ConfigurationManager.ConnectionStrings["PlayerData"].ConnectionString;
	}
}
