﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DDA RID: 3546
	public class AE1152 : BasePetEffect
	{
		// Token: 0x06007CF0 RID: 31984 RVA: 0x00299914 File Offset: 0x00297B14
		public AE1152(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1152, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CF1 RID: 31985 RVA: 0x00299994 File Offset: 0x00297B94
		public override bool Start(Living living)
		{
			AE1152 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1152) as AE1152;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CF2 RID: 31986 RVA: 0x00030080 File Offset: 0x0002E280
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007CF3 RID: 31987 RVA: 0x002999F4 File Offset: 0x00297BF4
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && player.Game is PVPGame;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007CF4 RID: 31988 RVA: 0x00299A34 File Offset: 0x00297C34
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				this.IsTrigger = false;
				target.Game.SendPetBuff(target, base.ElementInfo, true);
				target.AddPetEffect(new CE1152(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString(), living), 0);
			}
		}

		// Token: 0x06007CF5 RID: 31989 RVA: 0x000300A9 File Offset: 0x0002E2A9
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004B2E RID: 19246
		private int m_type = 0;

		// Token: 0x04004B2F RID: 19247
		private int m_count = 0;

		// Token: 0x04004B30 RID: 19248
		private int m_probability = 0;

		// Token: 0x04004B31 RID: 19249
		private int m_delay = 0;

		// Token: 0x04004B32 RID: 19250
		private int m_coldDown = 0;

		// Token: 0x04004B33 RID: 19251
		private int m_currentId;

		// Token: 0x04004B34 RID: 19252
		private int m_added = 0;
	}
}
