﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EF1 RID: 3825
	public class NoHoleEffect : AbstractEffect
	{
		// Token: 0x06008353 RID: 33619 RVA: 0x0003438B File Offset: 0x0003258B
		public NoHoleEffect(int count)
			: base(eEffectType.NoHoleEffect)
		{
			this.m_count = count;
		}

		// Token: 0x06008354 RID: 33620 RVA: 0x002B3738 File Offset: 0x002B1938
		public override bool Start(Living living)
		{
			NoHoleEffect noHoleEffect = living.EffectList.GetOfType(eEffectType.NoHoleEffect) as NoHoleEffect;
			bool flag = noHoleEffect != null;
			bool flag2;
			if (flag)
			{
				noHoleEffect.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008355 RID: 33621 RVA: 0x0003439E File Offset: 0x0003259E
		public override void OnAttached(Living living)
		{
			living.IsNoHole = true;
			living.BeginSelfTurn += this.player_BeginFitting;
		}

		// Token: 0x06008356 RID: 33622 RVA: 0x000343BC File Offset: 0x000325BC
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginFitting;
			living.IsNoHole = false;
		}

		// Token: 0x06008357 RID: 33623 RVA: 0x002B3780 File Offset: 0x002B1980
		private void player_BeginFitting(Living player)
		{
			this.m_count--;
			bool flag = this.m_count <= 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x0400520D RID: 21005
		private int m_count;
	}
}
