﻿using System;

namespace Game.Logic.Actions
{
	// Token: 0x02000F66 RID: 3942
	public class PlaySoundAction : BaseAction
	{
		// Token: 0x06008526 RID: 34086 RVA: 0x00035630 File Offset: 0x00033830
		public PlaySoundAction(string action, int delay)
			: base(delay, 1000)
		{
			this.m_action = action;
		}

		// Token: 0x06008527 RID: 34087 RVA: 0x00035647 File Offset: 0x00033847
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			((PVEGame)game).SendPlaySound(this.m_action);
			base.Finish(tick);
		}

		// Token: 0x04005336 RID: 21302
		private string m_action;
	}
}
