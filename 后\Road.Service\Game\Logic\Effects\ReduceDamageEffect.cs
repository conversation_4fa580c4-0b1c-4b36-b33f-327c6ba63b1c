﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EF5 RID: 3829
	public class ReduceDamageEffect : BasePlayerEffect
	{
		// Token: 0x06008367 RID: 33639 RVA: 0x000344D0 File Offset: 0x000326D0
		public ReduceDamageEffect(int count, int probability)
			: base(eEffectType.ReduceDamageEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x06008368 RID: 33640 RVA: 0x002B3A28 File Offset: 0x002B1C28
		public override bool Start(Living living)
		{
			ReduceDamageEffect reduceDamageEffect = living.EffectList.GetOfType(eEffectType.ReduceDamageEffect) as ReduceDamageEffect;
			bool flag = reduceDamageEffect != null;
			bool flag2;
			if (flag)
			{
				this.m_probability = ((this.m_probability > reduceDamageEffect.m_probability) ? this.m_probability : reduceDamageEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008369 RID: 33641 RVA: 0x000344F8 File Offset: 0x000326F8
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforeTakeDamage += this.player_BeforeTakeDamage;
		}

		// Token: 0x0600836A RID: 33642 RVA: 0x0003450E File Offset: 0x0003270E
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforeTakeDamage -= this.player_BeforeTakeDamage;
		}

		// Token: 0x0600836B RID: 33643 RVA: 0x002B3A84 File Offset: 0x002B1C84
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			this.IsTrigger = false;
			bool flag = damageAmount > 0 && AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				this.IsTrigger = true;
				bool flag2 = (damageAmount -= this.m_count) <= 0;
				if (flag2)
				{
					damageAmount = 1;
				}
				living.DefenceEffectTrigger = true;
				living.Game.AddAction(new LivingSayAction(living, LanguageMgr.GetTranslation("ReduceDamageEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x04005214 RID: 21012
		private int m_count = 0;

		// Token: 0x04005215 RID: 21013
		private int m_probability = 0;
	}
}
