﻿using System;

namespace Game.Base.Commands
{
	// Token: 0x02000F99 RID: 3993
	[Cmd("&cmd", ePrivLevel.Admin, "Config the command system.", new string[] { "/cmd [option] <para1> <para2>      ", "eg: /cmd -reload           :Reload the command system.", "    /cmd -list             :Display all commands." })]
	public class CommandMgrSetupCommand : AbstractCommandHandler, ICommandHandler
	{
		// Token: 0x060087C5 RID: 34757 RVA: 0x002C8628 File Offset: 0x002C6828
		public bool OnCommand(BaseClient client, string[] args)
		{
			bool flag = args.Length > 1;
			if (flag)
			{
				string text = args[1];
				string text2 = text;
				bool flag2 = !(text2 == "-reload");
				if (flag2)
				{
					bool flag3 = text2 == "-list";
					if (flag3)
					{
						CommandMgr.DisplaySyntax(client);
					}
					else
					{
						this.DisplaySyntax(client);
					}
				}
				else
				{
					CommandMgr.LoadCommands();
				}
			}
			else
			{
				this.DisplaySyntax(client);
			}
			return true;
		}
	}
}
