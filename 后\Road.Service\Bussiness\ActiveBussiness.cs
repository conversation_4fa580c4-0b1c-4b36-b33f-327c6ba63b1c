﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using SqlDataProvider.Data;

namespace Bussiness
{
	// Token: 0x02000F9F RID: 3999
	public class ActiveBussiness : BaseBussiness
	{
		// Token: 0x06008808 RID: 34824 RVA: 0x002C96D4 File Offset: 0x002C78D4
		public ActiveInfo[] GetAllActives()
		{
			List<ActiveInfo> list = new List<ActiveInfo>();
			SqlDataReader sqlDataReader = null;
			try
			{
				this.db.GetReader(ref sqlDataReader, "SP_Active_All");
				while (sqlDataReader.Read())
				{
					list.Add(this.InitActiveInfo(sqlDataReader));
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			finally
			{
				bool flag = sqlDataReader != null && !sqlDataReader.IsClosed;
				if (flag)
				{
					sqlDataReader.Close();
				}
			}
			return list.ToArray();
		}

		// Token: 0x06008809 RID: 34825 RVA: 0x002C9790 File Offset: 0x002C7990
		public ActiveInfo InitActiveInfo(SqlDataReader reader)
		{
			ActiveInfo activeInfo = new ActiveInfo();
			activeInfo.ActiveID = (int)reader["ActiveID"];
			activeInfo.Description = ((reader["Description"] == null) ? "" : reader["Description"].ToString());
			activeInfo.Content = ((reader["Content"] == null) ? "" : reader["Content"].ToString());
			activeInfo.AwardContent = ((reader["AwardContent"] == null) ? "" : reader["AwardContent"].ToString());
			activeInfo.HasKey = (int)reader["HasKey"];
			bool flag = !string.IsNullOrEmpty(reader["EndDate"].ToString());
			if (flag)
			{
				activeInfo.EndDate = (DateTime)reader["EndDate"];
			}
			activeInfo.IsOnly = (bool)reader["IsOnly"];
			activeInfo.StartDate = (DateTime)reader["StartDate"];
			activeInfo.Title = reader["Title"].ToString();
			activeInfo.Type = (int)reader["Type"];
			activeInfo.ActionTimeContent = ((reader["ActionTimeContent"] == null) ? "" : reader["ActionTimeContent"].ToString());
			activeInfo.IsAdvance = (bool)reader["IsAdvance"];
			activeInfo.GoodsExchangeTypes = ((reader["GoodsExchangeTypes"] == null) ? "" : reader["GoodsExchangeTypes"].ToString());
			activeInfo.GoodsExchangeNum = ((reader["GoodsExchangeNum"] == null) ? "" : reader["GoodsExchangeNum"].ToString());
			activeInfo.limitType = ((reader["limitType"] == null) ? "" : reader["limitType"].ToString());
			activeInfo.limitValue = ((reader["limitValue"] == null) ? "" : reader["limitValue"].ToString());
			activeInfo.IsShow = (bool)reader["IsShow"];
			return activeInfo;
		}

		// Token: 0x0600880A RID: 34826 RVA: 0x002C99F0 File Offset: 0x002C7BF0
		public int PullDown(int activeID, string awardID, int userID, ref string msg)
		{
			int num = 1;
			try
			{
				SqlParameter[] array = new SqlParameter[]
				{
					new SqlParameter("@ActiveID", activeID),
					new SqlParameter("@AwardID", awardID),
					new SqlParameter("@UserID", userID),
					new SqlParameter("@Result", SqlDbType.Int)
				};
				array[3].Direction = ParameterDirection.ReturnValue;
				bool flag = this.db.RunProcedure("SP_Active_PullDown", array);
				if (flag)
				{
					num = (int)array[3].Value;
					switch (num)
					{
					case 0:
						msg = "ActiveBussiness.Msg0";
						break;
					case 1:
						msg = "ActiveBussiness.Msg1";
						break;
					case 2:
						msg = "ActiveBussiness.Msg2";
						break;
					case 3:
						msg = "ActiveBussiness.Msg3";
						break;
					case 4:
						msg = "ActiveBussiness.Msg4";
						break;
					case 5:
						msg = "ActiveBussiness.Msg5";
						break;
					case 6:
						msg = "ActiveBussiness.Msg6";
						break;
					case 7:
						msg = "ActiveBussiness.Msg7";
						break;
					case 8:
						msg = "ActiveBussiness.Msg8";
						break;
					default:
						msg = "ActiveBussiness.Msg9";
						break;
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = BaseBussiness.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					BaseBussiness.log.Error("Init", ex);
				}
			}
			return num;
		}
	}
}
