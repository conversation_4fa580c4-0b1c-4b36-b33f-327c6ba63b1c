﻿using System;
using System.Collections.Specialized;
using System.Reflection;
using System.Threading;
using log4net;

namespace Game.Base.Events
{
	// Token: 0x02000F8A RID: 3978
	public sealed class GameEventMgr
	{
		// Token: 0x06008778 RID: 34680 RVA: 0x002C7360 File Offset: 0x002C5560
		public static void RegisterGlobalEvents(Assembly asm, Type attribute, RoadEvent e)
		{
			bool flag = asm == null;
			if (flag)
			{
				throw new ArgumentNullException("asm", "No assembly given to search for global event handlers!");
			}
			bool flag2 = attribute == null;
			if (flag2)
			{
				throw new ArgumentNullException("attribute", "No attribute given!");
			}
			bool flag3 = e == null;
			if (flag3)
			{
				throw new ArgumentNullException("e", "No event type given!");
			}
			Type[] types = asm.GetTypes();
			Type[] array = types;
			Type[] array2 = array;
			foreach (Type type in array2)
			{
				bool flag4 = !type.IsClass;
				if (!flag4)
				{
					MethodInfo[] methods = type.GetMethods(BindingFlags.Static | BindingFlags.Public);
					MethodInfo[] array4 = methods;
					MethodInfo[] array5 = array4;
					foreach (MethodInfo methodInfo in array5)
					{
						object[] customAttributes = methodInfo.GetCustomAttributes(attribute, false);
						bool flag5 = customAttributes.Length == 0;
						if (!flag5)
						{
							try
							{
								GameEventMgr.m_GlobalHandlerCollection.AddHandler(e, (RoadEventHandler)Delegate.CreateDelegate(typeof(RoadEventHandler), methodInfo));
							}
							catch (Exception ex)
							{
								bool isErrorEnabled = GameEventMgr.log.IsErrorEnabled;
								if (isErrorEnabled)
								{
									GameEventMgr.log.Error("Error registering global event. Method: " + type.FullName + "." + methodInfo.Name, ex);
								}
							}
						}
					}
				}
			}
		}

		// Token: 0x06008779 RID: 34681 RVA: 0x00035E19 File Offset: 0x00034019
		public static void AddHandler(RoadEvent e, RoadEventHandler del)
		{
			GameEventMgr.AddHandler(e, del, false);
		}

		// Token: 0x0600877A RID: 34682 RVA: 0x00035E25 File Offset: 0x00034025
		public static void AddHandlerUnique(RoadEvent e, RoadEventHandler del)
		{
			GameEventMgr.AddHandler(e, del, true);
		}

		// Token: 0x0600877B RID: 34683 RVA: 0x002C74D8 File Offset: 0x002C56D8
		private static void AddHandler(RoadEvent e, RoadEventHandler del, bool unique)
		{
			bool flag = e == null;
			if (flag)
			{
				throw new ArgumentNullException("e", "No event type given!");
			}
			bool flag2 = del == null;
			if (flag2)
			{
				throw new ArgumentNullException("del", "No event handler given!");
			}
			if (unique)
			{
				GameEventMgr.m_GlobalHandlerCollection.AddHandlerUnique(e, del);
			}
			else
			{
				GameEventMgr.m_GlobalHandlerCollection.AddHandler(e, del);
			}
		}

		// Token: 0x0600877C RID: 34684 RVA: 0x00035E31 File Offset: 0x00034031
		public static void AddHandler(object obj, RoadEvent e, RoadEventHandler del)
		{
			GameEventMgr.AddHandler(obj, e, del, false);
		}

		// Token: 0x0600877D RID: 34685 RVA: 0x00035E3E File Offset: 0x0003403E
		public static void AddHandlerUnique(object obj, RoadEvent e, RoadEventHandler del)
		{
			GameEventMgr.AddHandler(obj, e, del, true);
		}

		// Token: 0x0600877E RID: 34686 RVA: 0x002C7540 File Offset: 0x002C5740
		private static void AddHandler(object obj, RoadEvent e, RoadEventHandler del, bool unique)
		{
			bool flag = obj == null;
			if (flag)
			{
				throw new ArgumentNullException("obj", "No object given!");
			}
			bool flag2 = e == null;
			if (flag2)
			{
				throw new ArgumentNullException("e", "No event type given!");
			}
			bool flag3 = del == null;
			if (flag3)
			{
				throw new ArgumentNullException("del", "No event handler given!");
			}
			bool flag4 = !e.IsValidFor(obj);
			if (flag4)
			{
				throw new ArgumentException("Object is not valid for this event type", "obj");
			}
			try
			{
				GameEventMgr.m_lock.AcquireReaderLock(3000);
				try
				{
					RoadEventHandlerCollection roadEventHandlerCollection = (RoadEventHandlerCollection)GameEventMgr.m_GameObjectEventCollections[obj];
					bool flag5 = roadEventHandlerCollection == null;
					if (flag5)
					{
						roadEventHandlerCollection = new RoadEventHandlerCollection();
						LockCookie lockCookie = GameEventMgr.m_lock.UpgradeToWriterLock(3000);
						try
						{
							GameEventMgr.m_GameObjectEventCollections[obj] = roadEventHandlerCollection;
						}
						finally
						{
							GameEventMgr.m_lock.DowngradeFromWriterLock(ref lockCookie);
						}
					}
					if (unique)
					{
						roadEventHandlerCollection.AddHandlerUnique(e, del);
					}
					else
					{
						roadEventHandlerCollection.AddHandler(e, del);
					}
				}
				finally
				{
					GameEventMgr.m_lock.ReleaseReaderLock();
				}
			}
			catch (ApplicationException ex)
			{
				bool isErrorEnabled = GameEventMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					GameEventMgr.log.Error("Failed to add local event handler!", ex);
				}
			}
		}

		// Token: 0x0600877F RID: 34687 RVA: 0x002C76AC File Offset: 0x002C58AC
		public static void RemoveHandler(RoadEvent e, RoadEventHandler del)
		{
			bool flag = e == null;
			if (flag)
			{
				throw new ArgumentNullException("e", "No event type given!");
			}
			bool flag2 = del == null;
			if (flag2)
			{
				throw new ArgumentNullException("del", "No event handler given!");
			}
			GameEventMgr.m_GlobalHandlerCollection.RemoveHandler(e, del);
		}

		// Token: 0x06008780 RID: 34688 RVA: 0x002C76FC File Offset: 0x002C58FC
		public static void RemoveHandler(object obj, RoadEvent e, RoadEventHandler del)
		{
			bool flag = obj == null;
			if (flag)
			{
				throw new ArgumentNullException("obj", "No object given!");
			}
			bool flag2 = e == null;
			if (flag2)
			{
				throw new ArgumentNullException("e", "No event type given!");
			}
			bool flag3 = del == null;
			if (flag3)
			{
				throw new ArgumentNullException("del", "No event handler given!");
			}
			try
			{
				GameEventMgr.m_lock.AcquireReaderLock(3000);
				try
				{
					RoadEventHandlerCollection roadEventHandlerCollection = (RoadEventHandlerCollection)GameEventMgr.m_GameObjectEventCollections[obj];
					if (roadEventHandlerCollection != null)
					{
						roadEventHandlerCollection.RemoveHandler(e, del);
					}
				}
				finally
				{
					GameEventMgr.m_lock.ReleaseReaderLock();
				}
			}
			catch (ApplicationException ex)
			{
				bool isErrorEnabled = GameEventMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					GameEventMgr.log.Error("Failed to remove local event handler!", ex);
				}
			}
		}

		// Token: 0x06008781 RID: 34689 RVA: 0x002C77E0 File Offset: 0x002C59E0
		public static void RemoveAllHandlers(RoadEvent e, bool deep)
		{
			bool flag = e == null;
			if (flag)
			{
				throw new ArgumentNullException("e", "No event type given!");
			}
			if (deep)
			{
				try
				{
					GameEventMgr.m_lock.AcquireReaderLock(3000);
					try
					{
						foreach (object obj in GameEventMgr.m_GameObjectEventCollections.Values)
						{
							RoadEventHandlerCollection roadEventHandlerCollection = (RoadEventHandlerCollection)obj;
							roadEventHandlerCollection.RemoveAllHandlers(e);
						}
					}
					finally
					{
						GameEventMgr.m_lock.ReleaseReaderLock();
					}
				}
				catch (ApplicationException ex)
				{
					bool isErrorEnabled = GameEventMgr.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						GameEventMgr.log.Error("Failed to add local event handlers!", ex);
					}
				}
			}
			GameEventMgr.m_GlobalHandlerCollection.RemoveAllHandlers(e);
		}

		// Token: 0x06008782 RID: 34690 RVA: 0x002C78E0 File Offset: 0x002C5AE0
		public static void RemoveAllHandlers(object obj, RoadEvent e)
		{
			bool flag = obj == null;
			if (flag)
			{
				throw new ArgumentNullException("obj", "No object given!");
			}
			bool flag2 = e == null;
			if (flag2)
			{
				throw new ArgumentNullException("e", "No event type given!");
			}
			try
			{
				GameEventMgr.m_lock.AcquireReaderLock(3000);
				try
				{
					RoadEventHandlerCollection roadEventHandlerCollection = (RoadEventHandlerCollection)GameEventMgr.m_GameObjectEventCollections[obj];
					if (roadEventHandlerCollection != null)
					{
						roadEventHandlerCollection.RemoveAllHandlers(e);
					}
				}
				finally
				{
					GameEventMgr.m_lock.ReleaseReaderLock();
				}
			}
			catch (ApplicationException ex)
			{
				bool isErrorEnabled = GameEventMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					GameEventMgr.log.Error("Failed to remove local event handlers!", ex);
				}
			}
		}

		// Token: 0x06008783 RID: 34691 RVA: 0x002C79A8 File Offset: 0x002C5BA8
		public static void RemoveAllHandlers(bool deep)
		{
			if (deep)
			{
				try
				{
					GameEventMgr.m_lock.AcquireWriterLock(3000);
					try
					{
						GameEventMgr.m_GameObjectEventCollections.Clear();
					}
					finally
					{
						GameEventMgr.m_lock.ReleaseWriterLock();
					}
				}
				catch (ApplicationException ex)
				{
					bool isErrorEnabled = GameEventMgr.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						GameEventMgr.log.Error("Failed to remove all local event handlers!", ex);
					}
				}
			}
			GameEventMgr.m_GlobalHandlerCollection.RemoveAllHandlers();
		}

		// Token: 0x06008784 RID: 34692 RVA: 0x00035E4B File Offset: 0x0003404B
		public static void Notify(RoadEvent e)
		{
			GameEventMgr.Notify(e, null, null);
		}

		// Token: 0x06008785 RID: 34693 RVA: 0x00035E57 File Offset: 0x00034057
		public static void Notify(RoadEvent e, object sender)
		{
			GameEventMgr.Notify(e, sender, null);
		}

		// Token: 0x06008786 RID: 34694 RVA: 0x00035E63 File Offset: 0x00034063
		public static void Notify(RoadEvent e, EventArgs args)
		{
			GameEventMgr.Notify(e, null, args);
		}

		// Token: 0x06008787 RID: 34695 RVA: 0x002C7A3C File Offset: 0x002C5C3C
		public static void Notify(RoadEvent e, object sender, EventArgs eArgs)
		{
			bool flag = e == null;
			if (flag)
			{
				throw new ArgumentNullException("e", "No event type given!");
			}
			bool flag2 = sender != null;
			if (flag2)
			{
				try
				{
					RoadEventHandlerCollection roadEventHandlerCollection = null;
					GameEventMgr.m_lock.AcquireReaderLock(3000);
					try
					{
						roadEventHandlerCollection = (RoadEventHandlerCollection)GameEventMgr.m_GameObjectEventCollections[sender];
					}
					finally
					{
						GameEventMgr.m_lock.ReleaseReaderLock();
					}
					if (roadEventHandlerCollection != null)
					{
						roadEventHandlerCollection.Notify(e, sender, eArgs);
					}
				}
				catch (ApplicationException ex)
				{
					bool isErrorEnabled = GameEventMgr.log.IsErrorEnabled;
					if (isErrorEnabled)
					{
						GameEventMgr.log.Error("Failed to notify local event handler!", ex);
					}
				}
			}
			GameEventMgr.m_GlobalHandlerCollection.Notify(e, sender, eArgs);
		}

		// Token: 0x040053AC RID: 21420
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040053AD RID: 21421
		private static readonly HybridDictionary m_GameObjectEventCollections = new HybridDictionary();

		// Token: 0x040053AE RID: 21422
		private static readonly ReaderWriterLock m_lock = new ReaderWriterLock();

		// Token: 0x040053AF RID: 21423
		private const int TIMEOUT = 3000;

		// Token: 0x040053B0 RID: 21424
		private static RoadEventHandlerCollection m_GlobalHandlerCollection = new RoadEventHandlerCollection();
	}
}
