﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D23 RID: 3363
	public class GongfuSpecial : AbstractPetEffect
	{
		// Token: 0x06007914 RID: 30996 RVA: 0x002891D8 File Offset: 0x002873D8
		public GongfuSpecial(int skillId, string elementID)
			: base(ePetEffectType.GongfuSpecial, elementID)
		{
			this.m_skillID = skillId;
			bool flag = skillId == 459;
			if (flag)
			{
				this.m_percent = 80;
				this.xvalue = 1;
			}
		}

		// Token: 0x06007915 RID: 30997 RVA: 0x00288CEC File Offset: 0x00286EEC
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.AddAllDamage) is AddAllDamage;
			return flag || base.Start(living);
		}

		// Token: 0x06007916 RID: 30998 RVA: 0x0002D357 File Offset: 0x0002B557
		private void sendImgSkill(Living living)
		{
			living.Game.sendShowPicSkil(living, base.ElementInfo, true);
			living.BeginNextTurn -= this.sendImgSkill;
		}

		// Token: 0x06007917 RID: 30999 RVA: 0x0002D381 File Offset: 0x0002B581
		public override void OnAttached(Living living)
		{
			living.BeginNextTurn += this.sendImgSkill;
			living.OnMakeDamageEvent += this.player_OnMakeDamageEvent;
		}

		// Token: 0x06007918 RID: 31000 RVA: 0x0028922C File Offset: 0x0028742C
		private void player_OnMakeDamageEvent(Living living, Living target, ref int damageAmount, ref int criticalAmount)
		{
			this.m_added = (target.MaxBlood - target.Blood) * 100 / target.MaxBlood;
			bool flag = this.m_added >= this.m_percent && this.m_percent > 0;
			if (flag)
			{
				this.m_added = this.m_percent;
			}
			damageAmount += this.xvalue * damageAmount * this.m_added / 100;
		}

		// Token: 0x06007919 RID: 31001 RVA: 0x0002D3AA File Offset: 0x0002B5AA
		public override void OnRemoved(Living living)
		{
			living.OnMakeDamageEvent -= this.player_OnMakeDamageEvent;
		}

		// Token: 0x040046F1 RID: 18161
		private int m_skillID;

		// Token: 0x040046F2 RID: 18162
		private int m_added;

		// Token: 0x040046F3 RID: 18163
		private int m_percent = 0;

		// Token: 0x040046F4 RID: 18164
		private int xvalue = 0;

		// Token: 0x040046F5 RID: 18165
		private bool canadd = false;
	}
}
