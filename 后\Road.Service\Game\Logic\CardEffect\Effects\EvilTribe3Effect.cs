﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F21 RID: 3873
	public class EvilTribe3Effect : BaseCardEffect
	{
		// Token: 0x060083FC RID: 33788 RVA: 0x002B63E0 File Offset: 0x002B45E0
		public EvilTribe3Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.EvilTribe3, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x060083FD RID: 33789 RVA: 0x002B6450 File Offset: 0x002B4650
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.EvilTribe3) is EvilTribe3Effect;
			return flag || base.Start(living);
		}

		// Token: 0x060083FE RID: 33790 RVA: 0x00034922 File Offset: 0x00032B22
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty1;
			player.BeforeTakeDamage += this.ChangeProperty;
		}

		// Token: 0x060083FF RID: 33791 RVA: 0x0003494B File Offset: 0x00032B4B
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty1;
			player.BeforeTakeDamage -= this.ChangeProperty;
		}

		// Token: 0x06008400 RID: 33792 RVA: 0x002B6488 File Offset: 0x002B4688
		private void ChangeProperty(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = living.Game is PVEGame && (living.Game as PVEGame).Info.ID == 3;
			if (flag)
			{
				damageAmount -= this.m_value;
			}
		}

		// Token: 0x06008401 RID: 33793 RVA: 0x002B64D0 File Offset: 0x002B46D0
		private void ChangeProperty1(Player player)
		{
			bool flag = player.Game is PVEGame && (player.Game as PVEGame).Info.ID == 3;
			if (flag)
			{
				player.Game.SendMessage(player.PlayerDetail, "您激活了邪神部落3件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活邪神部落3件套卡.", 3);
			}
		}

		// Token: 0x04005259 RID: 21081
		private int m_indexValue = 0;

		// Token: 0x0400525A RID: 21082
		private int m_value = 0;

		// Token: 0x0400525B RID: 21083
		private int m_added = 0;
	}
}
