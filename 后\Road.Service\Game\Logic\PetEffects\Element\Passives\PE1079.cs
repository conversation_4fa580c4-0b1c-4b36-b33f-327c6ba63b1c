﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D5D RID: 3421
	public class PE1079 : BasePetEffect
	{
		// Token: 0x06007A67 RID: 31335 RVA: 0x0028EBB0 File Offset: 0x0028CDB0
		public PE1079(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1079, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007A68 RID: 31336 RVA: 0x0028EC30 File Offset: 0x0028CE30
		public override bool Start(Living living)
		{
			PE1079 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1079) as PE1079;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007A69 RID: 31337 RVA: 0x0002E6FE File Offset: 0x0002C8FE
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerUseSecondWeapon += this.Player_PlayerUseSecondWeapon;
		}

		// Token: 0x06007A6A RID: 31338 RVA: 0x0028EC90 File Offset: 0x0028CE90
		private void Player_PlayerUseSecondWeapon(Player player, int value)
		{
			bool flag = value == 31;
			if (flag)
			{
				this.m_added = 1000;
				player.SyncAtTime = true;
				player.AddBlood(this.m_added);
				player.SyncAtTime = false;
			}
		}

		// Token: 0x06007A6B RID: 31339 RVA: 0x0002E714 File Offset: 0x0002C914
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerUseSecondWeapon -= this.Player_PlayerUseSecondWeapon;
		}

		// Token: 0x040047CB RID: 18379
		private int m_type = 0;

		// Token: 0x040047CC RID: 18380
		private int m_count = 0;

		// Token: 0x040047CD RID: 18381
		private int m_probability = 0;

		// Token: 0x040047CE RID: 18382
		private int m_delay = 0;

		// Token: 0x040047CF RID: 18383
		private int m_coldDown = 0;

		// Token: 0x040047D0 RID: 18384
		private int m_currentId;

		// Token: 0x040047D1 RID: 18385
		private int m_added = 0;
	}
}
