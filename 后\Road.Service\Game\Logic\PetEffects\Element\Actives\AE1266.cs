﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E23 RID: 3619
	public class AE1266 : BasePetEffect
	{
		// Token: 0x06007E67 RID: 32359 RVA: 0x002A04C4 File Offset: 0x0029E6C4
		public AE1266(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1266, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E68 RID: 32360 RVA: 0x002A0544 File Offset: 0x0029E744
		public override bool Start(Living living)
		{
			AE1266 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1266) as AE1266;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E69 RID: 32361 RVA: 0x00030E7F File Offset: 0x0002F07F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007E6A RID: 32362 RVA: 0x002A05A4 File Offset: 0x0029E7A4
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && player.Game is PVPGame;
			if (flag)
			{
				this.m_added = 10;
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007E6B RID: 32363 RVA: 0x002A05EC File Offset: 0x0029E7EC
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				target.Game.SendPetBuff(target, base.ElementInfo, true);
				bool flag = (target as Player).PetMP < this.m_added;
				if (flag)
				{
					this.m_added = (target as Player).PetMP;
				}
				(target as Player).PetMP -= this.m_added;
				this.IsTrigger = false;
				this.m_added = 0;
				target.AddPetEffect(new CE1266(0, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007E6C RID: 32364 RVA: 0x00030EA8 File Offset: 0x0002F0A8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004D2D RID: 19757
		private int m_type = 0;

		// Token: 0x04004D2E RID: 19758
		private int m_count = 0;

		// Token: 0x04004D2F RID: 19759
		private int m_probability = 0;

		// Token: 0x04004D30 RID: 19760
		private int m_delay = 0;

		// Token: 0x04004D31 RID: 19761
		private int m_coldDown = 0;

		// Token: 0x04004D32 RID: 19762
		private int m_currentId;

		// Token: 0x04004D33 RID: 19763
		private int m_added = 0;
	}
}
