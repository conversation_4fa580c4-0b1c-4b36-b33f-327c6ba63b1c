﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D4D RID: 3405
	public class PetViolentPosture_ReduceBlood : BasePetEffect
	{
		// Token: 0x060079F9 RID: 31225 RVA: 0x0028D1D4 File Offset: 0x0028B3D4
		public PetViolentPosture_ReduceBlood(int probability, int skillId, string elementID)
			: base(ePetEffectType.PetViolentPosture_ReduceBlood, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
			if (!(elementID == "1572") && !(elementID == "1573") && !(elementID == "1608") && !(elementID == "1609"))
			{
				if (elementID == "1631")
				{
					this.m_value = 40;
				}
			}
			else
			{
				this.m_value = 80;
			}
		}

		// Token: 0x060079FA RID: 31226 RVA: 0x0028D264 File Offset: 0x0028B464
		public override bool Start(Living living)
		{
			PetViolentPosture_ReduceBlood petViolentPosture_ReduceBlood = living.PetEffectList.GetOfType(ePetEffectType.PetViolentPosture_ReduceBlood) as PetViolentPosture_ReduceBlood;
			bool flag = petViolentPosture_ReduceBlood != null;
			bool flag2;
			if (flag)
			{
				petViolentPosture_ReduceBlood.m_probability = ((this.m_probability > petViolentPosture_ReduceBlood.m_probability) ? this.m_probability : petViolentPosture_ReduceBlood.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060079FB RID: 31227 RVA: 0x0002E223 File Offset: 0x0002C423
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
		}

		// Token: 0x060079FC RID: 31228 RVA: 0x0002E239 File Offset: 0x0002C439
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
		}

		// Token: 0x060079FD RID: 31229 RVA: 0x0028D2C4 File Offset: 0x0028B4C4
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.SyncAtTime = true;
				player.AddBlood(-(player.Blood * this.m_value / 100), 1);
				player.SyncAtTime = false;
				player.Game.sendShowPicSkil(player, base.ElementInfo, true);
			}
		}

		// Token: 0x04004774 RID: 18292
		private int m_probability;

		// Token: 0x04004775 RID: 18293
		private int m_currentId;

		// Token: 0x04004776 RID: 18294
		private int m_value;
	}
}
