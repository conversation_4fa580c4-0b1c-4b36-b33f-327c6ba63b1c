﻿using System;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F04 RID: 3844
	public class GameCommandAttribute : Attribute
	{
		// Token: 0x170014A2 RID: 5282
		// (get) Token: 0x060083A0 RID: 33696 RVA: 0x000347AF File Offset: 0x000329AF
		public int Code
		{
			get
			{
				return this.m_code;
			}
		}

		// Token: 0x170014A3 RID: 5283
		// (get) Token: 0x060083A1 RID: 33697 RVA: 0x000347B7 File Offset: 0x000329B7
		public string Description
		{
			get
			{
				return this.m_description;
			}
		}

		// Token: 0x060083A2 RID: 33698 RVA: 0x000347BF File Offset: 0x000329BF
		public GameCommandAttribute(int code, string description)
		{
			this.m_code = code;
			this.m_description = description;
		}

		// Token: 0x04005223 RID: 21027
		private int m_code;

		// Token: 0x04005224 RID: 21028
		private string m_description;
	}
}
