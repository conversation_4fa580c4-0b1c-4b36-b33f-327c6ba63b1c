﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EA0 RID: 3744
	public class CE1213 : BasePetEffect
	{
		// Token: 0x06008163 RID: 33123 RVA: 0x002ACB38 File Offset: 0x002AAD38
		public CE1213(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1213, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008164 RID: 33124 RVA: 0x002ACBB8 File Offset: 0x002AADB8
		public override bool Start(Living living)
		{
			CE1213 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1213) as CE1213;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008165 RID: 33125 RVA: 0x00032B4A File Offset: 0x00030D4A
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008166 RID: 33126 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008167 RID: 33127 RVA: 0x002ACC18 File Offset: 0x002AAE18
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06008168 RID: 33128 RVA: 0x00032B73 File Offset: 0x00030D73
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PetEffects.CritRate = 0;
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x0400509D RID: 20637
		private int m_type = 0;

		// Token: 0x0400509E RID: 20638
		private int m_count = 0;

		// Token: 0x0400509F RID: 20639
		private int m_probability = 0;

		// Token: 0x040050A0 RID: 20640
		private int m_delay = 0;

		// Token: 0x040050A1 RID: 20641
		private int m_coldDown = 0;

		// Token: 0x040050A2 RID: 20642
		private int m_currentId;

		// Token: 0x040050A3 RID: 20643
		private int m_added = 0;
	}
}
