﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E43 RID: 3651
	public class CE1017 : BasePetEffect
	{
		// Token: 0x06007F1D RID: 32541 RVA: 0x002A3BD0 File Offset: 0x002A1DD0
		public CE1017(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1017, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F1E RID: 32542 RVA: 0x002A3C50 File Offset: 0x002A1E50
		public override bool Start(Living living)
		{
			CE1017 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1017) as CE1017;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F1F RID: 32543 RVA: 0x002A3CB0 File Offset: 0x002A1EB0
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.SpeedMultX(0);
			player.Game.SendPlayerPicture(player, 23, true);
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F20 RID: 32544 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F21 RID: 32545 RVA: 0x000316D8 File Offset: 0x0002F8D8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.SpeedMultX(3);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.Game.SendPlayerPicture(player, 23, false);
		}

		// Token: 0x06007F22 RID: 32546 RVA: 0x002A3CFC File Offset: 0x002A1EFC
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x04004E10 RID: 19984
		private int m_type = 0;

		// Token: 0x04004E11 RID: 19985
		private int m_count = 0;

		// Token: 0x04004E12 RID: 19986
		private int m_probability = 0;

		// Token: 0x04004E13 RID: 19987
		private int m_delay = 0;

		// Token: 0x04004E14 RID: 19988
		private int m_coldDown = 0;

		// Token: 0x04004E15 RID: 19989
		private int m_currentId;

		// Token: 0x04004E16 RID: 19990
		private int m_added = 0;
	}
}
