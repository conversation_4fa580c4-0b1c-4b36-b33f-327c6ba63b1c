﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EB9 RID: 3769
	public class CE1260 : BasePetEffect
	{
		// Token: 0x06008206 RID: 33286 RVA: 0x002AF170 File Offset: 0x002AD370
		public CE1260(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1260, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06008207 RID: 33287 RVA: 0x002AF1F0 File Offset: 0x002AD3F0
		public override bool Start(Living living)
		{
			CE1260 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1260) as CE1260;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008208 RID: 33288 RVA: 0x000331AD File Offset: 0x000313AD
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008209 RID: 33289 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x0600820A RID: 33290 RVA: 0x002AF250 File Offset: 0x002AD450
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				living.SyncAtTime = true;
				living.AddBlood(1000);
				living.SyncAtTime = false;
			}
		}

		// Token: 0x0600820B RID: 33291 RVA: 0x000331D6 File Offset: 0x000313D6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x0400514C RID: 20812
		private int m_type = 0;

		// Token: 0x0400514D RID: 20813
		private int m_count = 0;

		// Token: 0x0400514E RID: 20814
		private int m_probability = 0;

		// Token: 0x0400514F RID: 20815
		private int m_delay = 0;

		// Token: 0x04005150 RID: 20816
		private int m_coldDown = 0;

		// Token: 0x04005151 RID: 20817
		private int m_currentId;

		// Token: 0x04005152 RID: 20818
		private int m_added = 0;
	}
}
