﻿using System;
using Game.Logic.CardEffects;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.CardEffect.Effects
{
	// Token: 0x02000F26 RID: 3878
	public class FourArtifacts4Effect : BaseCardEffect
	{
		// Token: 0x0600841C RID: 33820 RVA: 0x002B6AE4 File Offset: 0x002B4CE4
		public FourArtifacts4Effect(int index, CardBuffInfo info)
			: base(eCardEffectType.FourArtifacts4, info)
		{
			this.m_indexValue = index;
			string[] array = info.Value.Split(new char[] { '|' });
			bool flag = this.m_indexValue < array.Length;
			if (flag)
			{
				this.m_value = int.Parse(array[this.m_indexValue]);
			}
		}

		// Token: 0x0600841D RID: 33821 RVA: 0x002B6B54 File Offset: 0x002B4D54
		public override bool Start(Living living)
		{
			bool flag = living.CardEffectList.GetOfType(eCardEffectType.FourArtifacts4) is FourArtifacts4Effect;
			return flag || base.Start(living);
		}

		// Token: 0x0600841E RID: 33822 RVA: 0x00034B72 File Offset: 0x00032D72
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerAfterReset += this.ChangeProperty;
		}

		// Token: 0x0600841F RID: 33823 RVA: 0x00034B88 File Offset: 0x00032D88
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerAfterReset -= this.ChangeProperty;
		}

		// Token: 0x06008420 RID: 33824 RVA: 0x002B6B8C File Offset: 0x002B4D8C
		private void ChangeProperty(Player player)
		{
			bool flag = this.m_added != 0;
			if (flag)
			{
				player.Attack -= (double)this.m_added;
				player.Agility -= (double)this.m_added;
				player.Lucky -= (double)this.m_added;
				player.Defence -= (double)this.m_added;
				this.m_added = 0;
			}
			this.m_added = this.m_value;
			player.Attack += (double)this.m_added;
			player.Agility += (double)this.m_added;
			player.Lucky += (double)this.m_added;
			player.Defence += (double)this.m_added;
			player.Game.SendMessage(player.PlayerDetail, "您激活了四神器4件套卡.", player.PlayerDetail.PlayerCharacter.NickName + "激活四神器4件套卡.", 3);
		}

		// Token: 0x04005267 RID: 21095
		private int m_indexValue = 0;

		// Token: 0x04005268 RID: 21096
		private int m_value = 0;

		// Token: 0x04005269 RID: 21097
		private int m_added = 0;
	}
}
