﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E13 RID: 3603
	public class AE1239 : BasePetEffect
	{
		// Token: 0x06007E14 RID: 32276 RVA: 0x0029EE38 File Offset: 0x0029D038
		public AE1239(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1239, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E15 RID: 32277 RVA: 0x0029EEB8 File Offset: 0x0029D0B8
		public override bool Start(Living living)
		{
			AE1239 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1239) as AE1239;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E16 RID: 32278 RVA: 0x00030B4D File Offset: 0x0002ED4D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E17 RID: 32279 RVA: 0x00030B63 File Offset: 0x0002ED63
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E18 RID: 32280 RVA: 0x0029EF18 File Offset: 0x0029D118
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1239(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004CBD RID: 19645
		private int m_type = 0;

		// Token: 0x04004CBE RID: 19646
		private int m_count = 0;

		// Token: 0x04004CBF RID: 19647
		private int m_probability = 0;

		// Token: 0x04004CC0 RID: 19648
		private int m_delay = 0;

		// Token: 0x04004CC1 RID: 19649
		private int m_coldDown = 0;

		// Token: 0x04004CC2 RID: 19650
		private int m_currentId;

		// Token: 0x04004CC3 RID: 19651
		private int m_added = 0;
	}
}
