﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DAF RID: 3503
	public class AE1057 : BasePetEffect
	{
		// Token: 0x06007C0C RID: 31756 RVA: 0x002959A0 File Offset: 0x00293BA0
		public AE1057(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1057, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C0D RID: 31757 RVA: 0x00295A20 File Offset: 0x00293C20
		public override bool Start(Living living)
		{
			AE1057 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1057) as AE1057;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C0E RID: 31758 RVA: 0x0002F79E File Offset: 0x0002D99E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007C0F RID: 31759 RVA: 0x00295A80 File Offset: 0x00293C80
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId;
			if (!flag)
			{
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					this.m_added = 3000;
					player2.SyncAtTime = true;
					player2.AddBlood(this.m_added);
					player2.SyncAtTime = false;
				}
			}
		}

		// Token: 0x06007C10 RID: 31760 RVA: 0x0002F7B4 File Offset: 0x0002D9B4
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004A03 RID: 18947
		private int m_type = 0;

		// Token: 0x04004A04 RID: 18948
		private int m_count = 0;

		// Token: 0x04004A05 RID: 18949
		private int m_probability = 0;

		// Token: 0x04004A06 RID: 18950
		private int m_delay = 0;

		// Token: 0x04004A07 RID: 18951
		private int m_coldDown = 0;

		// Token: 0x04004A08 RID: 18952
		private int m_currentId;

		// Token: 0x04004A09 RID: 18953
		private int m_added = 0;
	}
}
