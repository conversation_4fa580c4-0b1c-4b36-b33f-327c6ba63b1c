﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DD9 RID: 3545
	public class AE1151 : BasePetEffect
	{
		// Token: 0x06007CEA RID: 31978 RVA: 0x00299784 File Offset: 0x00297984
		public AE1151(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1151, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007CEB RID: 31979 RVA: 0x00299804 File Offset: 0x00297A04
		public override bool Start(Living living)
		{
			AE1151 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1151) as AE1151;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007CEC RID: 31980 RVA: 0x0003002E File Offset: 0x0002E22E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKillingLiving += this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007CED RID: 31981 RVA: 0x00299864 File Offset: 0x00297A64
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && player.Game is PVPGame;
			if (flag)
			{
				this.IsTrigger = true;
			}
		}

		// Token: 0x06007CEE RID: 31982 RVA: 0x002998A4 File Offset: 0x00297AA4
		private void Player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				this.IsTrigger = false;
				target.Game.SendPetBuff(target, base.ElementInfo, true);
				target.AddPetEffect(new CE1151(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString(), living), 0);
			}
		}

		// Token: 0x06007CEF RID: 31983 RVA: 0x00030057 File Offset: 0x0002E257
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKillingLiving -= this.Player_AfterKillingLiving;
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004B27 RID: 19239
		private int m_type = 0;

		// Token: 0x04004B28 RID: 19240
		private int m_count = 0;

		// Token: 0x04004B29 RID: 19241
		private int m_probability = 0;

		// Token: 0x04004B2A RID: 19242
		private int m_delay = 0;

		// Token: 0x04004B2B RID: 19243
		private int m_coldDown = 0;

		// Token: 0x04004B2C RID: 19244
		private int m_currentId;

		// Token: 0x04004B2D RID: 19245
		private int m_added = 0;
	}
}
