﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F5C RID: 3932
	public class LivingPlayeMovieAction : BaseAction
	{
		// Token: 0x06008510 RID: 34064 RVA: 0x000354A6 File Offset: 0x000336A6
		public LivingPlayeMovieAction(Living living, string action, int delay, int movieTime, LivingCallBack callBack)
			: base(delay, movieTime)
		{
			this.m_living = living;
			this.m_action = action;
			this.m_callBack = callBack;
			this.m_movieTime = movieTime;
		}

		// Token: 0x06008511 RID: 34065 RVA: 0x002B9C0C File Offset: 0x002B7E0C
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			game.SendLivingPlayMovie(this.m_living, this.m_action);
			bool flag = this.m_callBack != null;
			if (flag)
			{
				this.m_living.CallFuction(this.m_callBack, this.m_movieTime);
			}
			base.Finish(tick);
		}

		// Token: 0x0400530E RID: 21262
		private Living m_living;

		// Token: 0x0400530F RID: 21263
		private string m_action;

		// Token: 0x04005310 RID: 21264
		private LivingCallBack m_callBack;

		// Token: 0x04005311 RID: 21265
		private int m_movieTime;
	}
}
