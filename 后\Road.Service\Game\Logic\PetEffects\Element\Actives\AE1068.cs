﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DB5 RID: 3509
	public class AE1068 : BasePetEffect
	{
		// Token: 0x06007C2A RID: 31786 RVA: 0x002962A0 File Offset: 0x002944A0
		public AE1068(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1068, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C2B RID: 31787 RVA: 0x00296320 File Offset: 0x00294520
		public override bool Start(Living living)
		{
			AE1068 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1068) as AE1068;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C2C RID: 31788 RVA: 0x0002F8A6 File Offset: 0x0002DAA6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C2D RID: 31789 RVA: 0x0002F8BC File Offset: 0x0002DABC
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007C2E RID: 31790 RVA: 0x00296380 File Offset: 0x00294580
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId && player.Game is PVPGame;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1068(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004A2D RID: 18989
		private int m_type = 0;

		// Token: 0x04004A2E RID: 18990
		private int m_count = 0;

		// Token: 0x04004A2F RID: 18991
		private int m_probability = 0;

		// Token: 0x04004A30 RID: 18992
		private int m_delay = 0;

		// Token: 0x04004A31 RID: 18993
		private int m_coldDown = 0;

		// Token: 0x04004A32 RID: 18994
		private int m_currentId;

		// Token: 0x04004A33 RID: 18995
		private int m_added = 0;
	}
}
