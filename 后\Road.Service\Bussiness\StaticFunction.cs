﻿using System;
using System.Configuration;
using System.Reflection;
using log4net;

namespace Bussiness
{
	// Token: 0x02000FB9 RID: 4025
	public class StaticFunction
	{
		// Token: 0x06008A3E RID: 35390 RVA: 0x002F64B8 File Offset: 0x002F46B8
		public static bool UpdateConfig(string fileName, string name, string value)
		{
			try
			{
				Configuration configuration = ConfigurationManager.OpenMappedExeConfiguration(new ExeConfigurationFileMap
				{
					ExeConfigFilename = fileName
				}, ConfigurationUserLevel.None);
				configuration.AppSettings.Settings[name].Value = value;
				configuration.Save();
				ConfigurationManager.RefreshSection("appSettings");
				return true;
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = StaticFunction.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					StaticFunction.log.Error("UpdateConfig", ex);
				}
			}
			return false;
		}

		// Token: 0x0400549F RID: 21663
		protected static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
	}
}
