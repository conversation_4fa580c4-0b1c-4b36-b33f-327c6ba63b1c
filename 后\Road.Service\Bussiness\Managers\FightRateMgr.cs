﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FD3 RID: 4051
	public class FightRateMgr
	{
		// Token: 0x06008ABD RID: 35517 RVA: 0x002F83E0 File Offset: 0x002F65E0
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, FightRateInfo> dictionary = new Dictionary<int, FightRateInfo>();
				bool flag = FightRateMgr.LoadFightRate(dictionary);
				if (flag)
				{
					FightRateMgr.m_lock.AcquireWriterLock(-1);
					try
					{
						FightRateMgr._fightRate = dictionary;
						return true;
					}
					catch
					{
					}
					finally
					{
						FightRateMgr.m_lock.ReleaseWriterLock();
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = FightRateMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					FightRateMgr.log.Error("AwardMgr", ex);
				}
			}
			return false;
		}

		// Token: 0x06008ABE RID: 35518 RVA: 0x002F8488 File Offset: 0x002F6688
		public static bool Init()
		{
			bool flag;
			try
			{
				FightRateMgr.m_lock = new ReaderWriterLock();
				FightRateMgr._fightRate = new Dictionary<int, FightRateInfo>();
				flag = FightRateMgr.LoadFightRate(FightRateMgr._fightRate);
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = FightRateMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					FightRateMgr.log.Error("AwardMgr", ex);
				}
				flag = false;
			}
			return flag;
		}

		// Token: 0x06008ABF RID: 35519 RVA: 0x002F84F0 File Offset: 0x002F66F0
		private static bool LoadFightRate(Dictionary<int, FightRateInfo> fighRate)
		{
			using (ServiceBussiness serviceBussiness = new ServiceBussiness())
			{
				FightRateInfo[] fightRate = serviceBussiness.GetFightRate(1);
				FightRateInfo[] array = fightRate;
				FightRateInfo[] array2 = array;
				FightRateInfo[] array3 = array2;
				foreach (FightRateInfo fightRateInfo in array3)
				{
					bool flag = !fighRate.ContainsKey(fightRateInfo.ID);
					if (flag)
					{
						fighRate.Add(fightRateInfo.ID, fightRateInfo);
					}
				}
			}
			return true;
		}

		// Token: 0x06008AC0 RID: 35520 RVA: 0x002F8584 File Offset: 0x002F6784
		public static FightRateInfo[] GetAllFightRateInfo()
		{
			FightRateInfo[] array = null;
			FightRateMgr.m_lock.AcquireReaderLock(-1);
			try
			{
				array = FightRateMgr._fightRate.Values.ToArray<FightRateInfo>();
			}
			catch
			{
			}
			finally
			{
				FightRateMgr.m_lock.ReleaseReaderLock();
			}
			return (array == null) ? new FightRateInfo[0] : array;
		}

		// Token: 0x040054FC RID: 21756
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040054FD RID: 21757
		private static ReaderWriterLock m_lock;

		// Token: 0x040054FE RID: 21758
		protected static Dictionary<int, FightRateInfo> _fightRate;

		// Token: 0x040054FF RID: 21759
		private static ThreadSafeRandom random = new ThreadSafeRandom();
	}
}
