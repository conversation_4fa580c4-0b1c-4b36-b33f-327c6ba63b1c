﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000EBB RID: 3771
	public class CE1270 : BasePetEffect
	{
		// Token: 0x06008212 RID: 33298 RVA: 0x002AF380 File Offset: 0x002AD580
		public CE1270(int count, int probability, int type, int skillId, int delay, string elementID, Living liv)
			: base(ePetEffectType.CE1270, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
			this.m_liv = liv;
		}

		// Token: 0x06008213 RID: 33299 RVA: 0x002AF408 File Offset: 0x002AD608
		public override bool Start(Living living)
		{
			CE1270 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1270) as CE1270;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008214 RID: 33300 RVA: 0x0003323F File Offset: 0x0003143F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06008215 RID: 33301 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06008216 RID: 33302 RVA: 0x00033268 File Offset: 0x00031468
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x06008217 RID: 33303 RVA: 0x002AF468 File Offset: 0x002AD668
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
			else
			{
				this.m_added = 800;
				living.SyncAtTime = true;
				living.AddBlood(-this.m_added, 1);
				living.SyncAtTime = false;
				bool flag2 = living.Blood <= 0;
				if (flag2)
				{
					living.Die();
					bool flag3 = this.m_liv != null && this.m_liv is Player;
					if (flag3)
					{
						(this.m_liv as Player).PlayerDetail.OnKillingLiving(this.m_liv.Game, 2, living.Id, living.IsLiving, this.m_added);
					}
				}
			}
		}

		// Token: 0x0400515A RID: 20826
		private int m_type = 0;

		// Token: 0x0400515B RID: 20827
		private int m_count = 0;

		// Token: 0x0400515C RID: 20828
		private int m_probability = 0;

		// Token: 0x0400515D RID: 20829
		private int m_delay = 0;

		// Token: 0x0400515E RID: 20830
		private int m_coldDown = 0;

		// Token: 0x0400515F RID: 20831
		private int m_currentId;

		// Token: 0x04005160 RID: 20832
		private int m_added = 0;

		// Token: 0x04005161 RID: 20833
		private Living m_liv;
	}
}
