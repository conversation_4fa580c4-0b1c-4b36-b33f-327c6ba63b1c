﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000CFC RID: 3324
	public class CE4844 : BasePetEffect
	{
		// Token: 0x06007845 RID: 30789 RVA: 0x00284FAC File Offset: 0x002831AC
		public CE4844(int count, string elementID)
			: base(ePetEffectType.CE4844, elementID)
		{
			this.m_count = count;
			bool flag = elementID == "4844";
			if (flag)
			{
				this.m_value = 100;
			}
		}

		// Token: 0x06007846 RID: 30790 RVA: 0x00284FE8 File Offset: 0x002831E8
		public override bool Start(Living living)
		{
			CE4844 ce = living.PetEffectList.GetOfType(ePetEffectType.CE4844) as CE4844;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007847 RID: 30791 RVA: 0x0002C88F File Offset: 0x0002AA8F
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.player_BeginSelfTurn;
			player.AfterKilledByLiving += this.player_AfterKilledByLiving;
			this.IsTrigger = true;
		}

		// Token: 0x06007848 RID: 30792 RVA: 0x0002C8BF File Offset: 0x0002AABF
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginSelfTurn -= this.player_BeginSelfTurn;
			player.AfterKilledByLiving -= this.player_AfterKilledByLiving;
		}

		// Token: 0x06007849 RID: 30793 RVA: 0x00285030 File Offset: 0x00283230
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			this.IsTrigger = true;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.m_added = 0;
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
		}

		// Token: 0x0600784A RID: 30794 RVA: 0x0002C8E8 File Offset: 0x0002AAE8
		public void SetValue(int value)
		{
			this.m_value = value;
		}

		// Token: 0x0600784B RID: 30795 RVA: 0x00285084 File Offset: 0x00283284
		private void player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = !this.IsTrigger;
			if (!flag)
			{
				this.m_added = damageAmount * this.m_value / 100;
				target.SyncAtTime = true;
				target.AddBlood(-this.m_added, 1);
				target.SyncAtTime = false;
				bool flag2 = target.Blood <= 0;
				if (flag2)
				{
					target.Die();
					bool flag3 = living != null && living is Player;
					if (flag3)
					{
						(living as Player).OnAfterKillingLiving(target, this.m_added, 0);
					}
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x04004681 RID: 18049
		private int m_count;

		// Token: 0x04004682 RID: 18050
		private int m_value;

		// Token: 0x04004683 RID: 18051
		private int m_added;
	}
}
