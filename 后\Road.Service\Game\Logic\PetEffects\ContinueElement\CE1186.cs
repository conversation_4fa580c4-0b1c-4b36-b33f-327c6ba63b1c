﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E8C RID: 3724
	public class CE1186 : BasePetEffect
	{
		// Token: 0x060080E2 RID: 32994 RVA: 0x002AAC10 File Offset: 0x002A8E10
		public CE1186(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1186, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x060080E3 RID: 32995 RVA: 0x002AAC90 File Offset: 0x002A8E90
		public override bool Start(Living living)
		{
			CE1186 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1186) as CE1186;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060080E4 RID: 32996 RVA: 0x0003266E File Offset: 0x0003086E
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginNextTurn += this.Player_BeginNextTurn;
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
		}

		// Token: 0x060080E5 RID: 32997 RVA: 0x00005683 File Offset: 0x00003883
		private void Player_BeginNextTurn(Living living)
		{
		}

		// Token: 0x060080E6 RID: 32998 RVA: 0x002AACF0 File Offset: 0x002A8EF0
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_added = 500;
			living.AddBlood(-this.m_added, 1);
			bool flag = living.Blood <= 0;
			if (flag)
			{
				living.Die();
				bool flag2 = living.Game.CurrentLiving != null && living.Game.CurrentLiving is Player;
				if (flag2)
				{
					(living.Game.CurrentLiving as Player).PlayerDetail.OnKillingLiving(living.Game, 2, living.Id, living.IsLiving, this.m_added);
				}
			}
		}

		// Token: 0x060080E7 RID: 32999 RVA: 0x00032697 File Offset: 0x00030897
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeginNextTurn -= this.Player_BeginNextTurn;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04005011 RID: 20497
		private int m_type = 0;

		// Token: 0x04005012 RID: 20498
		private int m_count = 0;

		// Token: 0x04005013 RID: 20499
		private int m_probability = 0;

		// Token: 0x04005014 RID: 20500
		private int m_delay = 0;

		// Token: 0x04005015 RID: 20501
		private int m_coldDown = 0;

		// Token: 0x04005016 RID: 20502
		private int m_currentId;

		// Token: 0x04005017 RID: 20503
		private int m_added = 0;
	}
}
