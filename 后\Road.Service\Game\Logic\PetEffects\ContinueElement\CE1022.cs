﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E48 RID: 3656
	public class CE1022 : BasePetEffect
	{
		// Token: 0x06007F3B RID: 32571 RVA: 0x002A4394 File Offset: 0x002A2594
		public CE1022(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1022, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F3C RID: 32572 RVA: 0x002A4414 File Offset: 0x002A2614
		public override bool Start(Living living)
		{
			CE1022 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1022) as CE1022;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F3D RID: 32573 RVA: 0x002A4474 File Offset: 0x002A2674
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 100;
				player.BaseGuard += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F3E RID: 32574 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F3F RID: 32575 RVA: 0x002A44D4 File Offset: 0x002A26D4
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F40 RID: 32576 RVA: 0x002A4508 File Offset: 0x002A2708
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Game.SendPlayerPicture(player, 30, false);
			player.BaseGuard -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E33 RID: 20019
		private int m_type = 0;

		// Token: 0x04004E34 RID: 20020
		private int m_count = 0;

		// Token: 0x04004E35 RID: 20021
		private int m_probability = 0;

		// Token: 0x04004E36 RID: 20022
		private int m_delay = 0;

		// Token: 0x04004E37 RID: 20023
		private int m_coldDown = 0;

		// Token: 0x04004E38 RID: 20024
		private int m_currentId;

		// Token: 0x04004E39 RID: 20025
		private int m_added = 0;
	}
}
