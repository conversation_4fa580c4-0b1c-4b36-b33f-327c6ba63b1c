﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ContinueSkills
{
	// Token: 0x02000D02 RID: 3330
	public class PetAddMagicAttackEquip : AbstractPetEffect
	{
		// Token: 0x06007866 RID: 30822 RVA: 0x00285D18 File Offset: 0x00283F18
		public PetAddMagicAttackEquip(int count, string elementID)
			: base(ePetEffectType.PetAddMagicAttackEquip, elementID)
		{
			this.m_count = count;
			if (!(elementID == "1407"))
			{
				if (!(elementID == "1408"))
				{
					if (!(elementID == "1743"))
					{
						if (!(elementID == "1813"))
						{
							if (elementID == "43950")
							{
								this.m_value = 30.0;
								this.m_percent = true;
							}
						}
						else
						{
							this.m_value = 20.0;
							this.m_percent = true;
						}
					}
					else
					{
						this.m_value = 35.0;
						this.m_percent = true;
					}
				}
				else
				{
					this.m_value = 10.0;
					this.m_percent = true;
				}
			}
			else
			{
				this.m_value = 5.0;
				this.m_percent = true;
			}
		}

		// Token: 0x06007867 RID: 30823 RVA: 0x00285DFC File Offset: 0x00283FFC
		public override bool Start(Living living)
		{
			PetAddMagicAttackEquip petAddMagicAttackEquip = living.PetEffectList.GetOfType(ePetEffectType.PetAddMagicAttackEquip) as PetAddMagicAttackEquip;
			bool flag = petAddMagicAttackEquip != null;
			bool flag2;
			if (flag)
			{
				petAddMagicAttackEquip.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007868 RID: 30824 RVA: 0x00285E44 File Offset: 0x00284044
		public override void OnAttached(Living living)
		{
			bool flag = this.m_added == 0.0;
			if (flag)
			{
				bool percent = this.m_percent;
				if (percent)
				{
					this.m_added = living.MagicAttack * this.m_value / 100.0;
				}
				else
				{
					this.m_added = this.m_value;
				}
				living.MagicAttack += this.m_added;
			}
			living.BeginSelfTurn += this.player_BeginSelfTurn;
		}

		// Token: 0x06007869 RID: 30825 RVA: 0x0002CA07 File Offset: 0x0002AC07
		public override void OnRemoved(Living living)
		{
			living.BeginSelfTurn -= this.player_BeginSelfTurn;
		}

		// Token: 0x0600786A RID: 30826 RVA: 0x00285EC8 File Offset: 0x002840C8
		private void player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				living.MagicAttack -= this.m_added;
				this.m_added = 0.0;
				living.Game.sendShowPicSkil(living, base.ElementInfo, false);
				this.Stop();
			}
		}

		// Token: 0x04004693 RID: 18067
		private int m_count;

		// Token: 0x04004694 RID: 18068
		private double m_value;

		// Token: 0x04004695 RID: 18069
		private bool m_percent;

		// Token: 0x04004696 RID: 18070
		private double m_added;
	}
}
