﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CBE RID: 3262
	[SpellAttibute(10)]
	public class ABombSpell : ISpellHandler
	{
		// Token: 0x060074FE RID: 29950 RVA: 0x0026E248 File Offset: 0x0026C448
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isLiving = player.IsLiving;
			if (isLiving)
			{
				player.SetBall(4);
			}
			else
			{
				bool flag = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
				if (flag)
				{
					(game.CurrentLiving as Player).SetBall(4);
				}
			}
		}
	}
}
