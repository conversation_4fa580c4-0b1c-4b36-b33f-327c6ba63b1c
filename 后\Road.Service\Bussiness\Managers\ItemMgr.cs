﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Xml;
using Game.Base;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness.Managers
{
	// Token: 0x02000FDE RID: 4062
	public class ItemMgr
	{
		// Token: 0x06008B05 RID: 35589 RVA: 0x002F9E84 File Offset: 0x002F8084
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, ItemTemplateInfo> dictionary = new Dictionary<int, ItemTemplateInfo>();
				List<TemplateData> list = new List<TemplateData>();
				bool flag = ItemMgr.LoadItem(dictionary) && ItemMgr.LoadTSUpgradeTemplate(list);
				if (flag)
				{
					ItemMgr.m_lock.AcquireWriterLock(-1);
					try
					{
						ItemMgr.m_items = dictionary;
						ItemMgr._tsUpgrades = list;
						ItemMgr.SyncTSUpgradeTemplates();
						return true;
					}
					catch
					{
					}
					finally
					{
						ItemMgr.m_lock.ReleaseWriterLock();
					}
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = ItemMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ItemMgr.log.Error("ReLoad", ex);
				}
			}
			return false;
		}

		// Token: 0x06008B06 RID: 35590 RVA: 0x002F9F48 File Offset: 0x002F8148
		private static void SyncTSUpgradeTemplates()
		{
			ItemMgr._warOrderItem = new Dictionary<int, TemplateData>();
			foreach (TemplateData templateData in ItemMgr._tsUpgrades)
			{
				int types = templateData.Types;
				int num = types;
				bool flag = num == 120 && !ItemMgr._warOrderItem.ContainsKey(templateData.Grades);
				if (flag)
				{
					ItemMgr._warOrderItem.Add(templateData.Grades, templateData);
				}
			}
		}

		// Token: 0x06008B07 RID: 35591 RVA: 0x002F9FE0 File Offset: 0x002F81E0
		private static bool LoadTSUpgradeTemplate(List<TemplateData> datas)
		{
			try
			{
				XmlDocument xmlDocument = Marshal.LoadXMLData("TS_UpgradeTemplate", true);
				bool flag = xmlDocument != null;
				if (flag)
				{
					foreach (object obj in xmlDocument.SelectNodes("Result/child::node()"))
					{
						XmlNode xmlNode = (XmlNode)obj;
						datas.Add(new TemplateData
						{
							Types = int.Parse(xmlNode.Attributes["Types"].Value),
							TemplateId = int.Parse(xmlNode.Attributes["TemplateId"].Value),
							Grades = int.Parse(xmlNode.Attributes["Grades"].Value),
							Data = int.Parse(xmlNode.Attributes["Data"].Value),
							TemplateName = xmlNode.Attributes["TemplateName"].Value,
							ActiveObj = xmlNode.Attributes["ActiveObj"].Value,
							ItemTempId1 = int.Parse(xmlNode.Attributes["ItemTempId1"].Value),
							Param1 = int.Parse(xmlNode.Attributes["Param1"].Value),
							ItemTempId2 = int.Parse(xmlNode.Attributes["ItemTempId2"].Value),
							Param2 = int.Parse(xmlNode.Attributes["Param2"].Value),
							ItemTempId3 = int.Parse(xmlNode.Attributes["ItemTempId3"].Value),
							Param3 = int.Parse(xmlNode.Attributes["Param3"].Value),
							ItemTempId4 = int.Parse(xmlNode.Attributes["ItemTempId4"].Value),
							Param4 = int.Parse(xmlNode.Attributes["Param4"].Value)
						});
					}
				}
				else
				{
					ItemMgr.log.Error("TS_UpgradeTemplate not found");
				}
				return true;
			}
			catch (Exception ex)
			{
				ItemMgr.log.Error(ex);
			}
			return false;
		}

		// Token: 0x06008B08 RID: 35592 RVA: 0x002FA288 File Offset: 0x002F8488
		public static bool Init()
		{
			bool flag2;
			try
			{
				ItemMgr.m_lock = new ReaderWriterLock();
				ItemMgr.m_items = new Dictionary<int, ItemTemplateInfo>();
				ItemMgr._tsUpgrades = new List<TemplateData>();
				bool flag = !ItemMgr.LoadItem(ItemMgr.m_items) || !ItemMgr.LoadTSUpgradeTemplate(ItemMgr._tsUpgrades);
				if (flag)
				{
					flag2 = false;
				}
				else
				{
					ItemMgr.SyncTSUpgradeTemplates();
					flag2 = ItemMgr.LoadItem(ItemMgr.m_items);
				}
			}
			catch (Exception ex)
			{
				bool isErrorEnabled = ItemMgr.log.IsErrorEnabled;
				if (isErrorEnabled)
				{
					ItemMgr.log.Error("Init", ex);
				}
				flag2 = false;
			}
			return flag2;
		}

		// Token: 0x06008B09 RID: 35593 RVA: 0x002FA328 File Offset: 0x002F8528
		public static bool LoadItem(Dictionary<int, ItemTemplateInfo> infos)
		{
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				ItemTemplateInfo[] allGoods = produceBussiness.GetAllGoods();
				ItemTemplateInfo[] array = allGoods;
				ItemTemplateInfo[] array2 = array;
				ItemTemplateInfo[] array3 = array2;
				foreach (ItemTemplateInfo itemTemplateInfo in array3)
				{
					bool flag = !infos.Keys.Contains(itemTemplateInfo.TemplateID);
					if (flag)
					{
						infos.Add(itemTemplateInfo.TemplateID, itemTemplateInfo);
					}
				}
			}
			return true;
		}

		// Token: 0x06008B0A RID: 35594 RVA: 0x002FA3C0 File Offset: 0x002F85C0
		public static ItemTemplateInfo FindItemTemplate(int templateId)
		{
			bool flag = ItemMgr.m_items == null;
			if (flag)
			{
				ItemMgr.Init();
			}
			ItemMgr.m_lock.AcquireReaderLock(-1);
			try
			{
				bool flag2 = ItemMgr.m_items.Keys.Contains(templateId);
				if (flag2)
				{
					return ItemMgr.m_items[templateId];
				}
			}
			finally
			{
				ItemMgr.m_lock.ReleaseReaderLock();
			}
			return null;
		}

		// Token: 0x06008B0B RID: 35595 RVA: 0x002FA438 File Offset: 0x002F8638
		public static ItemTemplateInfo GetGoodsbyFusionTypeandQuality(int fusionType, int quality)
		{
			bool flag = ItemMgr.m_items == null;
			if (flag)
			{
				ItemMgr.Init();
			}
			ItemMgr.m_lock.AcquireReaderLock(-1);
			try
			{
				foreach (ItemTemplateInfo itemTemplateInfo in ItemMgr.m_items.Values)
				{
					bool flag2 = itemTemplateInfo.FusionType == fusionType && itemTemplateInfo.Quality == quality;
					if (flag2)
					{
						return itemTemplateInfo;
					}
				}
			}
			finally
			{
				ItemMgr.m_lock.ReleaseReaderLock();
			}
			return null;
		}

		// Token: 0x06008B0C RID: 35596 RVA: 0x002FA4F4 File Offset: 0x002F86F4
		public static ItemTemplateInfo GetGoodsbyFusionTypeandLevel(int fusionType, int level)
		{
			bool flag = ItemMgr.m_items == null;
			if (flag)
			{
				ItemMgr.Init();
			}
			ItemMgr.m_lock.AcquireReaderLock(-1);
			try
			{
				foreach (ItemTemplateInfo itemTemplateInfo in ItemMgr.m_items.Values)
				{
					bool flag2 = itemTemplateInfo.FusionType == fusionType && itemTemplateInfo.Level == level;
					if (flag2)
					{
						return itemTemplateInfo;
					}
				}
			}
			finally
			{
				ItemMgr.m_lock.ReleaseReaderLock();
			}
			return null;
		}

		// Token: 0x06008B0D RID: 35597 RVA: 0x002FA5B0 File Offset: 0x002F87B0
		public static ItemTemplateInfo FindGoldItemTemplate(int templateId, bool IsGold)
		{
			if (IsGold)
			{
				GoldEquipTemplateInfo goldEquipTemplateInfo = GoldEquipMgr.FindGoldEquipByTemplate(templateId);
				bool flag = goldEquipTemplateInfo != null && ItemMgr.m_items.Keys.Contains(goldEquipTemplateInfo.NewTemplateId);
				if (flag)
				{
					return ItemMgr.m_items[goldEquipTemplateInfo.NewTemplateId];
				}
			}
			return null;
		}

		// Token: 0x06008B0E RID: 35598 RVA: 0x002FA604 File Offset: 0x002F8804
		public static TemplateData GetUpgradeWarOrderItem(int grade)
		{
			return (!ItemMgr._warOrderItem.ContainsKey(grade)) ? null : ItemMgr._warOrderItem[grade];
		}

		// Token: 0x04005519 RID: 21785
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x0400551A RID: 21786
		private static Dictionary<int, ItemTemplateInfo> m_items;

		// Token: 0x0400551B RID: 21787
		private static ReaderWriterLock m_lock;

		// Token: 0x0400551C RID: 21788
		private static Dictionary<int, TemplateData> _warOrderItem;

		// Token: 0x0400551D RID: 21789
		private static List<TemplateData> _tsUpgrades;
	}
}
