﻿using System;
using Game.Logic.Effects;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F54 RID: 3924
	public class LivingDelayEffectAction : BaseAction
	{
		// Token: 0x06008500 RID: 34048 RVA: 0x0003537C File Offset: 0x0003357C
		public LivingDelayEffectAction(Living living, AbstractEffect effect, int delay)
			: base(delay)
		{
			this.m_effect = effect;
			this.m_living = living;
		}

		// Token: 0x06008501 RID: 34049 RVA: 0x00035395 File Offset: 0x00033595
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			this.m_effect.Start(this.m_living);
			base.Finish(tick);
		}

		// Token: 0x040052EB RID: 21227
		private AbstractEffect m_effect;

		// Token: 0x040052EC RID: 21228
		private Living m_living;
	}
}
