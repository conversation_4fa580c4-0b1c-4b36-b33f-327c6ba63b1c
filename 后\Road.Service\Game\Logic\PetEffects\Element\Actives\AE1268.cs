﻿using System;
using System.Collections.Generic;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E25 RID: 3621
	public class AE1268 : BasePetEffect
	{
		// Token: 0x06007E73 RID: 32371 RVA: 0x002A088C File Offset: 0x0029EA8C
		public AE1268(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1268, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E74 RID: 32372 RVA: 0x002A090C File Offset: 0x0029EB0C
		public override bool Start(Living living)
		{
			AE1268 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1268) as AE1268;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E75 RID: 32373 RVA: 0x00030F23 File Offset: 0x0002F123
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E76 RID: 32374 RVA: 0x00030F39 File Offset: 0x0002F139
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E77 RID: 32375 RVA: 0x002A096C File Offset: 0x0029EB6C
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				this.m_added = 5;
				List<Player> allTeamPlayers = player.Game.GetAllTeamPlayers(player);
				foreach (Player player2 in allTeamPlayers)
				{
					player2.AddPetMP(this.m_added);
				}
			}
		}

		// Token: 0x04004D3B RID: 19771
		private int m_type = 0;

		// Token: 0x04004D3C RID: 19772
		private int m_count = 0;

		// Token: 0x04004D3D RID: 19773
		private int m_probability = 0;

		// Token: 0x04004D3E RID: 19774
		private int m_delay = 0;

		// Token: 0x04004D3F RID: 19775
		private int m_coldDown = 0;

		// Token: 0x04004D40 RID: 19776
		private int m_currentId;

		// Token: 0x04004D41 RID: 19777
		private int m_added = 0;
	}
}
