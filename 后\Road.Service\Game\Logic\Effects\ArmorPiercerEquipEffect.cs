﻿using System;
using Bussiness;
using Bussiness.Managers;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;
using Game.Logic.Spells;

namespace Game.Logic.Effects
{
	// Token: 0x02000EDC RID: 3804
	public class ArmorPiercerEquipEffect : BasePlayerEffect
	{
		// Token: 0x060082D6 RID: 33494 RVA: 0x00033B7E File Offset: 0x00031D7E
		public ArmorPiercerEquipEffect(int count, int probability)
			: base(eEffectType.ArmorPiercer)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x060082D7 RID: 33495 RVA: 0x002B1E68 File Offset: 0x002B0068
		public override bool Start(Living living)
		{
			ArmorPiercerEquipEffect armorPiercerEquipEffect = living.EffectList.GetOfType(eEffectType.ArmorPiercer) as ArmorPiercerEquipEffect;
			bool flag = armorPiercerEquipEffect != null;
			bool flag2;
			if (flag)
			{
				armorPiercerEquipEffect.m_probability = ((this.m_probability > armorPiercerEquipEffect.m_probability) ? this.m_probability : armorPiercerEquipEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x060082D8 RID: 33496 RVA: 0x00033BAD File Offset: 0x00031DAD
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.ChangeProperty;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x060082D9 RID: 33497 RVA: 0x002B1EC4 File Offset: 0x002B00C4
		private void player_AfterPlayerShooted(Player player)
		{
			bool effectTrigger = this.EffectTrigger;
			if (effectTrigger)
			{
				player.FlyingPartical = 0;
				player.IgnoreArmor = false;
				this.EffectTrigger = false;
			}
		}

		// Token: 0x060082DA RID: 33498 RVA: 0x00033BD6 File Offset: 0x00031DD6
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.ChangeProperty;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x060082DB RID: 33499 RVA: 0x002B1EF4 File Offset: 0x002B00F4
		private void ChangeProperty(Player player, int ball)
		{
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000 && !player.AttackEffectTrigger;
			if (flag)
			{
				this.EffectTrigger = true;
				player.FlyingPartical = 65;
				SpellMgr.ExecuteSpell(player.Game, player, ItemMgr.FindItemTemplate(10020));
				player.AttackEffectTrigger = true;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("ArmorPiercerEquipEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051E1 RID: 20961
		private int m_count = 0;

		// Token: 0x040051E2 RID: 20962
		private int m_probability = 0;

		// Token: 0x040051E3 RID: 20963
		private bool EffectTrigger = false;
	}
}
