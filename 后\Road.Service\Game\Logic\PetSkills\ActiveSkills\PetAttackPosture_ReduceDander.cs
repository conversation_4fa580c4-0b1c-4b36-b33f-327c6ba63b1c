﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D2D RID: 3373
	public class PetAttackPosture_ReduceDander : BasePetEffect
	{
		// Token: 0x0600794B RID: 31051 RVA: 0x0002D703 File Offset: 0x0002B903
		public PetAttackPosture_ReduceDander(int probability, int skillId, string elementID)
			: base(ePetEffectType.PetAttackPosture_ReduceDander, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x0600794C RID: 31052 RVA: 0x00289BC8 File Offset: 0x00287DC8
		public override bool Start(Living living)
		{
			PetAttackPosture_ReduceDander petAttackPosture_ReduceDander = living.PetEffectList.GetOfType(ePetEffectType.PetAttackPosture_ReduceDander) as PetAttackPosture_ReduceDander;
			bool flag = petAttackPosture_ReduceDander != null;
			bool flag2;
			if (flag)
			{
				petAttackPosture_ReduceDander.m_probability = ((this.m_probability > petAttackPosture_ReduceDander.m_probability) ? this.m_probability : petAttackPosture_ReduceDander.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x0600794D RID: 31053 RVA: 0x0002D72C File Offset: 0x0002B92C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
			player.BeginNextTurn += this.player_BeginNextTurn;
		}

		// Token: 0x0600794E RID: 31054 RVA: 0x0002D755 File Offset: 0x0002B955
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
			player.BeginNextTurn -= this.player_BeginNextTurn;
		}

		// Token: 0x0600794F RID: 31055 RVA: 0x00289C28 File Offset: 0x00287E28
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				this.IsTrigger = true;
				player.PetEffectTrigger = true;
				player.Game.sendShowPicSkil(player, base.ElementInfo, true);
				bool flag2 = this.m_currentId != 499;
				if (flag2)
				{
					player.SetDander(player.Dander - 50);
				}
				else
				{
					player.AddDander(40);
				}
			}
		}

		// Token: 0x06007950 RID: 31056 RVA: 0x00289CA4 File Offset: 0x00287EA4
		private void player_BeginNextTurn(Living player)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				this.IsTrigger = false;
				player.Game.sendShowPicSkil(player, base.ElementInfo, false);
			}
		}

		// Token: 0x04004710 RID: 18192
		private int m_probability;

		// Token: 0x04004711 RID: 18193
		private int m_currentId;
	}
}
