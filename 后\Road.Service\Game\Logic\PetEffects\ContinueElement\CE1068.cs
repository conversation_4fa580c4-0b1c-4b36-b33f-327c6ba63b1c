﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E61 RID: 3681
	public class CE1068 : BasePetEffect
	{
		// Token: 0x06007FD1 RID: 32721 RVA: 0x002A6B34 File Offset: 0x002A4D34
		public CE1068(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1068, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007FD2 RID: 32722 RVA: 0x002A6BB4 File Offset: 0x002A4DB4
		public override bool Start(Living living)
		{
			CE1068 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1068) as CE1068;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007FD3 RID: 32723 RVA: 0x002A6C14 File Offset: 0x002A4E14
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
			this.IsTrigger = true;
		}

		// Token: 0x06007FD4 RID: 32724 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007FD5 RID: 32725 RVA: 0x002A6C64 File Offset: 0x002A4E64
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = !this.IsTrigger;
			if (!flag)
			{
				this.m_added = 800;
				target.SyncAtTime = true;
				target.AddBlood(-this.m_added, 1);
				target.SyncAtTime = false;
				bool flag2 = target.Blood <= 0;
				if (flag2)
				{
					target.Die();
					bool flag3 = living != null && living is Player;
					if (flag3)
					{
						(living as Player).PlayerDetail.OnKillingLiving(living.Game, 2, target.Id, target.IsLiving, this.m_added);
					}
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007FD6 RID: 32726 RVA: 0x002A6D10 File Offset: 0x002A4F10
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			this.IsTrigger = true;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.m_added = 0;
				this.Stop();
			}
		}

		// Token: 0x06007FD7 RID: 32727 RVA: 0x00031C56 File Offset: 0x0002FE56
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x04004EE2 RID: 20194
		private int m_type = 0;

		// Token: 0x04004EE3 RID: 20195
		private int m_count = 0;

		// Token: 0x04004EE4 RID: 20196
		private int m_probability = 0;

		// Token: 0x04004EE5 RID: 20197
		private int m_delay = 0;

		// Token: 0x04004EE6 RID: 20198
		private int m_coldDown = 0;

		// Token: 0x04004EE7 RID: 20199
		private int m_currentId;

		// Token: 0x04004EE8 RID: 20200
		private int m_added = 0;
	}
}
