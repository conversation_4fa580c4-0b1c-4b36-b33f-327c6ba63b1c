﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000D9A RID: 3482
	public class AE1027 : BasePetEffect
	{
		// Token: 0x06007BA0 RID: 31648 RVA: 0x00293D40 File Offset: 0x00291F40
		public AE1027(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1027, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007BA1 RID: 31649 RVA: 0x00293DC0 File Offset: 0x00291FC0
		public override bool Start(Living living)
		{
			AE1027 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1027) as AE1027;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007BA2 RID: 31650 RVA: 0x0002F3A3 File Offset: 0x0002D5A3
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007BA3 RID: 31651 RVA: 0x00293E20 File Offset: 0x00292020
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1027(1, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x06007BA4 RID: 31652 RVA: 0x0002F3B9 File Offset: 0x0002D5B9
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004970 RID: 18800
		private int m_type = 0;

		// Token: 0x04004971 RID: 18801
		private int m_count = 0;

		// Token: 0x04004972 RID: 18802
		private int m_probability = 0;

		// Token: 0x04004973 RID: 18803
		private int m_delay = 0;

		// Token: 0x04004974 RID: 18804
		private int m_coldDown = 0;

		// Token: 0x04004975 RID: 18805
		private int m_currentId;

		// Token: 0x04004976 RID: 18806
		private int m_added = 0;
	}
}
