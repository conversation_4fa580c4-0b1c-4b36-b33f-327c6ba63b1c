﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D2C RID: 3372
	public class PetAttackPosture_AddAgilityAndSpeed : BasePetEffect
	{
		// Token: 0x06007946 RID: 31046 RVA: 0x0002D6AE File Offset: 0x0002B8AE
		public PetAttackPosture_AddAgilityAndSpeed(int probability, int skillId, string elementID)
			: base(ePetEffectType.PetAttackPosture_AddAgilityAndSpeed, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
		}

		// Token: 0x06007947 RID: 31047 RVA: 0x00289B04 File Offset: 0x00287D04
		public override bool Start(Living living)
		{
			PetAttackPosture_AddAgilityAndSpeed petAttackPosture_AddAgilityAndSpeed = living.PetEffectList.GetOfType(ePetEffectType.PetAttackPosture_AddAgilityAndSpeed) as PetAttackPosture_AddAgilityAndSpeed;
			bool flag = petAttackPosture_AddAgilityAndSpeed != null;
			bool flag2;
			if (flag)
			{
				petAttackPosture_AddAgilityAndSpeed.m_probability = ((this.m_probability > petAttackPosture_AddAgilityAndSpeed.m_probability) ? this.m_probability : petAttackPosture_AddAgilityAndSpeed.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007948 RID: 31048 RVA: 0x0002D6D7 File Offset: 0x0002B8D7
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_PlayerBuffSkillPet;
		}

		// Token: 0x06007949 RID: 31049 RVA: 0x0002D6ED File Offset: 0x0002B8ED
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_PlayerBuffSkillPet;
		}

		// Token: 0x0600794A RID: 31050 RVA: 0x00289B64 File Offset: 0x00287D64
		private void player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.PetEffectTrigger = true;
				player.Game.sendShowPicSkil(player, base.Info, true);
				new PetAddSpeedEquip(1, base.Info.ID.ToString()).Start(player);
			}
		}

		// Token: 0x0400470E RID: 18190
		private int m_probability;

		// Token: 0x0400470F RID: 18191
		private int m_currentId;
	}
}
