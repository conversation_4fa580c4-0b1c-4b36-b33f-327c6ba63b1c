﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D2B RID: 3371
	public class PetAddTurn : BasePetEffect
	{
		// Token: 0x06007940 RID: 31040 RVA: 0x002898E0 File Offset: 0x00287AE0
		public PetAddTurn(int value, int probability, int skillId, string elementID)
			: base(ePetEffectType.PetAddTurn, elementID)
		{
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_currentId = skillId;
			this.m_value = value;
			int currentId = this.m_currentId;
			int num = currentId;
			if (num <= 470)
			{
				if (num != 449)
				{
					if (num == 470)
					{
						this.m_count = 15;
						this.m_removed = 40;
					}
				}
				else
				{
					this.m_count = 10;
					this.m_removed = 70;
				}
			}
			else if (num != 499)
			{
				if (num - 1160 > 2)
				{
					if (num == 1212)
					{
						this.m_value = 150;
					}
				}
				else
				{
					this.m_value = 120;
				}
			}
			else
			{
				this.m_count = 10;
				this.m_removed = 80;
			}
		}

		// Token: 0x06007941 RID: 31041 RVA: 0x002899B8 File Offset: 0x00287BB8
		public override bool Start(Living living)
		{
			PetAddTurn petAddTurn = living.PetEffectList.GetOfType(ePetEffectType.PetAddTurn) as PetAddTurn;
			bool flag = petAddTurn != null;
			bool flag2;
			if (flag)
			{
				petAddTurn.m_probability = ((this.m_probability > petAddTurn.m_probability) ? this.m_probability : petAddTurn.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007942 RID: 31042 RVA: 0x00289A18 File Offset: 0x00287C18
		private void player_BeginNextTurn(Living living)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				((Player)living).IsAddTurn = true;
				((Player)living).Energy = this.m_value;
				bool flag = !living.Game.IsPVE();
				if (flag)
				{
					(living as Player).ReduceDelay30();
				}
				this.IsTrigger = false;
			}
		}

		// Token: 0x06007943 RID: 31043 RVA: 0x00289A78 File Offset: 0x00287C78
		public void player_PlayerShoot(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AttackGemLimit = 2;
				this.IsTrigger = true;
				player.EffectTrigger = true;
				player.Game.SendEquipEffect(player, LanguageMgr.GetTranslation("PetAttackEffect.Success", Array.Empty<object>()));
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("AddTurnEquipEffect.msg", Array.Empty<object>()), 9, 0, 1000));
				player.Delay = 0;
			}
		}

		// Token: 0x06007944 RID: 31044 RVA: 0x0002D65C File Offset: 0x0002B85C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerShoot += this.player_PlayerShoot;
			player.BeginNextTurn += this.player_BeginNextTurn;
		}

		// Token: 0x06007945 RID: 31045 RVA: 0x0002D685 File Offset: 0x0002B885
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerShoot -= this.player_PlayerShoot;
			player.BeginNextTurn -= this.player_BeginNextTurn;
		}

		// Token: 0x04004709 RID: 18185
		private int m_probability = 0;

		// Token: 0x0400470A RID: 18186
		private int m_currentId;

		// Token: 0x0400470B RID: 18187
		private int m_value;

		// Token: 0x0400470C RID: 18188
		private int m_count;

		// Token: 0x0400470D RID: 18189
		private int m_removed;
	}
}
