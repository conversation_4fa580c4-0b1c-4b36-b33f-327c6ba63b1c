﻿using System;
using Game.Base.Packets;
using Game.Logic.Phy.Object;

namespace Game.Logic.Cmd
{
	// Token: 0x02000F03 RID: 3843
	[GameCommand(40, "使用飞行技能")]
	public class FlyCommand : ICommandHandler
	{
		// Token: 0x0600839E RID: 33694 RVA: 0x002B5080 File Offset: 0x002B3280
		public void HandleCommand(BaseGame game, Player player, GSPacketIn packet)
		{
			bool isAttacking = player.IsAttacking;
			if (isAttacking)
			{
				player.ShootCount = 1;
				player.BallCount = 1;
				player.UseFlySkill();
			}
		}
	}
}
