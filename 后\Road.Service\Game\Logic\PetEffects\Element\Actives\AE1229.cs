﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E0F RID: 3599
	public class AE1229 : BasePetEffect
	{
		// Token: 0x06007E00 RID: 32256 RVA: 0x0029E910 File Offset: 0x0029CB10
		public AE1229(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1229, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007E01 RID: 32257 RVA: 0x0029E990 File Offset: 0x0029CB90
		public override bool Start(Living living)
		{
			AE1229 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1229) as AE1229;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007E02 RID: 32258 RVA: 0x00030A9D File Offset: 0x0002EC9D
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E03 RID: 32259 RVA: 0x00030AB3 File Offset: 0x0002ECB3
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007E04 RID: 32260 RVA: 0x0029E9F0 File Offset: 0x0029CBF0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.AddPetEffect(new CE1229(2, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004CA1 RID: 19617
		private int m_type = 0;

		// Token: 0x04004CA2 RID: 19618
		private int m_count = 0;

		// Token: 0x04004CA3 RID: 19619
		private int m_probability = 0;

		// Token: 0x04004CA4 RID: 19620
		private int m_delay = 0;

		// Token: 0x04004CA5 RID: 19621
		private int m_coldDown = 0;

		// Token: 0x04004CA6 RID: 19622
		private int m_currentId;

		// Token: 0x04004CA7 RID: 19623
		private int m_added = 0;
	}
}
