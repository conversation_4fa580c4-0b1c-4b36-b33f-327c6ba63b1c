﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EEF RID: 3823
	public class LockDirectionEquipEffect : BasePlayerEffect
	{
		// Token: 0x06008346 RID: 33606 RVA: 0x00034271 File Offset: 0x00032471
		public LockDirectionEquipEffect(int count, int probability)
			: base(eEffectType.LockDirectionEquipEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x06008347 RID: 33607 RVA: 0x002B3520 File Offset: 0x002B1720
		public override bool Start(Living living)
		{
			LockDirectionEquipEffect lockDirectionEquipEffect = living.EffectList.GetOfType(eEffectType.LockDirectionEquipEffect) as LockDirectionEquipEffect;
			bool flag = lockDirectionEquipEffect != null;
			bool flag2;
			if (flag)
			{
				lockDirectionEquipEffect.m_probability = ((this.m_probability > lockDirectionEquipEffect.m_probability) ? this.m_probability : lockDirectionEquipEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008348 RID: 33608 RVA: 0x00034299 File Offset: 0x00032499
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.ChangeProperty;
			player.AfterKillingLiving += this.player_AfterKillingLiving;
		}

		// Token: 0x06008349 RID: 33609 RVA: 0x002B357C File Offset: 0x002B177C
		private void player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = this.IsTrigger && target is Player;
			if (flag)
			{
				(target as Player).AddEffect(new LockDirectionEffect(2), 0);
			}
		}

		// Token: 0x0600834A RID: 33610 RVA: 0x000342C2 File Offset: 0x000324C2
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.ChangeProperty;
			player.AfterKillingLiving -= this.player_AfterKillingLiving;
		}

		// Token: 0x0600834B RID: 33611 RVA: 0x002B35B8 File Offset: 0x002B17B8
		private void ChangeProperty(Player player, int ball)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				this.IsTrigger = true;
				player.AttackEffectTrigger = true;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("LockDirectionEquipEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x04005209 RID: 21001
		private int m_count = 0;

		// Token: 0x0400520A RID: 21002
		private int m_probability = 0;
	}
}
