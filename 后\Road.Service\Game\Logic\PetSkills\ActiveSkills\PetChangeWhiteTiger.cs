﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D32 RID: 3378
	public class PetChangeWhiteTiger : BasePetEffect
	{
		// Token: 0x06007967 RID: 31079 RVA: 0x0002D8F3 File Offset: 0x0002BAF3
		public PetChangeWhiteTiger(int count, int skillId, string elementID)
			: base(ePetEffectType.PetChangeWhiteTiger, elementID)
		{
			this.m_skillId = skillId;
			this.m_count = count;
		}

		// Token: 0x06007968 RID: 31080 RVA: 0x0028A320 File Offset: 0x00288520
		public override bool Start(Living living)
		{
			PetChangeWhiteTiger petChangeWhiteTiger = living.PetEffectList.GetOfType(ePetEffectType.PetChangeWhiteTiger) as PetChangeWhiteTiger;
			bool flag = petChangeWhiteTiger != null;
			bool flag2;
			if (flag)
			{
				petChangeWhiteTiger.m_count = this.m_count;
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007969 RID: 31081 RVA: 0x0002D911 File Offset: 0x0002BB11
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x0600796A RID: 31082 RVA: 0x0002D927 File Offset: 0x0002BB27
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x0600796B RID: 31083 RVA: 0x0028A368 File Offset: 0x00288568
		private void player_PlayerBuffSkillPet(Living living)
		{
			bool flag = living.PetEffects.CurrentUseSkill == this.m_skillId;
			if (flag)
			{
				living.WhiteTiger = true;
				living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				living.AddPetEffect(new PetChangeSuitEquip(4, "13932|suits727", base.ElementInfo.ID.ToString()), 0);
				living.AddPetEffect(new PetAddAgilityEquip(3, base.ElementInfo.ID.ToString()), 0);
				living.AddPetEffect(new CE4283(3, base.ElementInfo.ID.ToString()), 0);
				bool flag2 = this.m_skillId == 2363;
				if (flag2)
				{
					living.AddPetEffect(new PetAddMagicAttackEquip(3, base.ElementInfo.ID.ToString()), 0);
				}
			}
		}

		// Token: 0x04004721 RID: 18209
		private int m_count;

		// Token: 0x04004722 RID: 18210
		private int m_skillId;
	}
}
