﻿using System;
using System.Collections;

namespace Game.Base.Config
{
	// Token: 0x02000F95 RID: 3989
	public class ConfigElement
	{
		// Token: 0x170014D2 RID: 5330
		public ConfigElement this[string key]
		{
			get
			{
				Hashtable children = this.m_children;
				lock (children)
				{
					bool flag2 = !this.m_children.Contains(key);
					if (flag2)
					{
						this.m_children.Add(key, this.GetNewConfigElement(this));
					}
				}
				return (ConfigElement)this.m_children[key];
			}
			set
			{
				Hashtable children = this.m_children;
				lock (children)
				{
					this.m_children[key] = value;
				}
			}
		}

		// Token: 0x170014D3 RID: 5331
		// (get) Token: 0x060087AB RID: 34731 RVA: 0x00035F97 File Offset: 0x00034197
		public ConfigElement Parent
		{
			get
			{
				return this.m_parent;
			}
		}

		// Token: 0x170014D4 RID: 5332
		// (get) Token: 0x060087AC RID: 34732 RVA: 0x00035F9F File Offset: 0x0003419F
		public bool HasChildren
		{
			get
			{
				return this.m_children.Count > 0;
			}
		}

		// Token: 0x170014D5 RID: 5333
		// (get) Token: 0x060087AD RID: 34733 RVA: 0x00035FAF File Offset: 0x000341AF
		public Hashtable Children
		{
			get
			{
				return this.m_children;
			}
		}

		// Token: 0x060087AE RID: 34734 RVA: 0x00035FB7 File Offset: 0x000341B7
		public ConfigElement(ConfigElement parent)
		{
			this.m_parent = parent;
		}

		// Token: 0x060087AF RID: 34735 RVA: 0x002C8130 File Offset: 0x002C6330
		protected virtual ConfigElement GetNewConfigElement(ConfigElement parent)
		{
			return new ConfigElement(parent);
		}

		// Token: 0x060087B0 RID: 34736 RVA: 0x002C8148 File Offset: 0x002C6348
		public string GetString()
		{
			return this.m_value;
		}

		// Token: 0x060087B1 RID: 34737 RVA: 0x002C8160 File Offset: 0x002C6360
		public string GetString(string defaultValue)
		{
			return (this.m_value != null) ? this.m_value : defaultValue;
		}

		// Token: 0x060087B2 RID: 34738 RVA: 0x002C8184 File Offset: 0x002C6384
		public int GetInt()
		{
			return int.Parse(this.m_value);
		}

		// Token: 0x060087B3 RID: 34739 RVA: 0x002C81A4 File Offset: 0x002C63A4
		public int GetInt(int defaultValue)
		{
			return (this.m_value != null) ? int.Parse(this.m_value) : defaultValue;
		}

		// Token: 0x060087B4 RID: 34740 RVA: 0x002C81CC File Offset: 0x002C63CC
		public long GetLong()
		{
			return long.Parse(this.m_value);
		}

		// Token: 0x060087B5 RID: 34741 RVA: 0x002C81EC File Offset: 0x002C63EC
		public long GetLong(long defaultValue)
		{
			return (this.m_value != null) ? long.Parse(this.m_value) : defaultValue;
		}

		// Token: 0x060087B6 RID: 34742 RVA: 0x002C8214 File Offset: 0x002C6414
		public bool GetBoolean()
		{
			return bool.Parse(this.m_value);
		}

		// Token: 0x060087B7 RID: 34743 RVA: 0x002C8234 File Offset: 0x002C6434
		public bool GetBoolean(bool defaultValue)
		{
			return (this.m_value != null) ? bool.Parse(this.m_value) : defaultValue;
		}

		// Token: 0x060087B8 RID: 34744 RVA: 0x00035FE1 File Offset: 0x000341E1
		public void Set(object value)
		{
			this.m_value = value.ToString();
		}

		// Token: 0x040053BC RID: 21436
		protected ConfigElement m_parent = null;

		// Token: 0x040053BD RID: 21437
		protected Hashtable m_children = new Hashtable();

		// Token: 0x040053BE RID: 21438
		protected string m_value = null;
	}
}
