﻿using System;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000E2E RID: 3630
	public class AE1432 : BasePetEffect
	{
		// Token: 0x06007EA7 RID: 32423 RVA: 0x002A1600 File Offset: 0x0029F800
		public AE1432(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1432, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007EA8 RID: 32424 RVA: 0x002A1680 File Offset: 0x0029F880
		public override bool Start(Living living)
		{
			AE1432 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1432) as AE1432;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007EA9 RID: 32425 RVA: 0x000311B9 File Offset: 0x0002F3B9
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EAA RID: 32426 RVA: 0x000311CF File Offset: 0x0002F3CF
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.player_AfterBuffSkillPetByLiving;
		}

		// Token: 0x06007EAB RID: 32427 RVA: 0x002A16E0 File Offset: 0x0029F8E0
		private void player_AfterBuffSkillPetByLiving(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill == this.m_currentId;
			if (flag)
			{
				player.PetEffects.CritRate = 12;
				player.BaseDamage += player.BaseDamage * 10.0 / 100.0;
				player.Game.SendPetBuff(player, base.ElementInfo, true);
				player.AddPetEffect(new CE1432(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x04004D7A RID: 19834
		private int m_type = 0;

		// Token: 0x04004D7B RID: 19835
		private int m_count = 0;

		// Token: 0x04004D7C RID: 19836
		private int m_probability = 0;

		// Token: 0x04004D7D RID: 19837
		private int m_delay = 0;

		// Token: 0x04004D7E RID: 19838
		private int m_coldDown = 0;

		// Token: 0x04004D7F RID: 19839
		private int m_currentId;

		// Token: 0x04004D80 RID: 19840
		private int m_added = 0;
	}
}
