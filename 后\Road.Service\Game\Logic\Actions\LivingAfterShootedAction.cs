﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F4D RID: 3917
	public class LivingAfterShootedAction : BaseAction
	{
		// Token: 0x060084F1 RID: 34033 RVA: 0x00035264 File Offset: 0x00033464
		public LivingAfterShootedAction(Living owner, Living living, int delay)
			: base(delay, 0)
		{
			this.m_liv = living;
			this.m_own = owner;
		}

		// Token: 0x060084F2 RID: 34034 RVA: 0x0003527E File Offset: 0x0003347E
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			this.m_liv.OnAfterTakedBomb();
			this.m_liv.OnAfterTakeDamage(this.m_own);
			base.Finish(tick);
		}

		// Token: 0x040052D4 RID: 21204
		private Living m_liv;

		// Token: 0x040052D5 RID: 21205
		private Living m_own;
	}
}
