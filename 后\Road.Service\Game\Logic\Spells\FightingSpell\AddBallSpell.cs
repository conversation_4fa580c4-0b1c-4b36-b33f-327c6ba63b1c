﻿using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.FightingSpell
{
	// Token: 0x02000CC0 RID: 3264
	[SpellAttibute(15)]
	public class AddBallSpell : ISpellHandler
	{
		// Token: 0x06007502 RID: 29954 RVA: 0x0026E47C File Offset: 0x0026C67C
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			bool isSpecialSkill = player.IsSpecialSkill;
			if (!isSpecialSkill)
			{
				bool isLiving = player.IsLiving;
				if (isLiving)
				{
					bool flag = (player.CurrentBall.ID == 3 || player.CurrentBall.ID == 5 || player.CurrentBall.ID == 1) && item.TemplateID == 10003;
					if (flag)
					{
						player.BallCount = 1;
					}
					else
					{
						player.CurrentDamagePlus *= 0.5f;
						player.BallCount = item.Property2;
					}
				}
				else
				{
					bool flag2 = game.CurrentLiving != null && game.CurrentLiving is Player && game.CurrentLiving.Team == player.Team;
					if (flag2)
					{
						bool flag3 = ((game.CurrentLiving as Player).CurrentBall.ID == 3 || (game.CurrentLiving as Player).CurrentBall.ID == 5 || (game.CurrentLiving as Player).CurrentBall.ID == 1) && item.TemplateID == 10003;
						if (flag3)
						{
							(game.CurrentLiving as Player).BallCount = 1;
						}
						else
						{
							game.CurrentLiving.CurrentDamagePlus *= 0.5f;
							(game.CurrentLiving as Player).BallCount = item.Property2;
						}
					}
				}
			}
		}
	}
}
