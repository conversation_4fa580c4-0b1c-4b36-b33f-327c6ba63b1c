﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D8E RID: 3470
	public class PE3168 : BasePetEffect
	{
		// Token: 0x06007B5E RID: 31582 RVA: 0x00292B1C File Offset: 0x00290D1C
		public PE3168(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE3168, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B5F RID: 31583 RVA: 0x00292B98 File Offset: 0x00290D98
		public override bool Start(Living living)
		{
			PE3168 pe = living.PetEffectList.GetOfType(ePetEffectType.PE3168) as PE3168;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B60 RID: 31584 RVA: 0x0002F068 File Offset: 0x0002D268
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerShoot += this.ChangeProperty;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06007B61 RID: 31585 RVA: 0x0002F091 File Offset: 0x0002D291
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerShoot -= this.ChangeProperty;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x06007B62 RID: 31586 RVA: 0x0002F0BA File Offset: 0x0002D2BA
		private void ChangeProperty(Player player)
		{
			this.m_added = 36;
			player.PetEffects.PetBaseAtt += this.m_added;
		}

		// Token: 0x06007B63 RID: 31587 RVA: 0x0002F0DE File Offset: 0x0002D2DE
		private void player_AfterPlayerShooted(Player player)
		{
			player.PetEffects.PetBaseAtt -= this.m_added;
			this.m_added = 0;
		}

		// Token: 0x0400491C RID: 18716
		private int m_type = 0;

		// Token: 0x0400491D RID: 18717
		private int m_count = 0;

		// Token: 0x0400491E RID: 18718
		private int m_probability = 0;

		// Token: 0x0400491F RID: 18719
		private int m_delay = 0;

		// Token: 0x04004920 RID: 18720
		private int m_coldDown = 0;

		// Token: 0x04004921 RID: 18721
		private int m_currentId;

		// Token: 0x04004922 RID: 18722
		private int m_added = 0;
	}
}
