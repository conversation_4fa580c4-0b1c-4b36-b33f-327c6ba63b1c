﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000EFA RID: 3834
	public class SealEquipEffect : BasePlayerEffect
	{
		// Token: 0x06008382 RID: 33666 RVA: 0x000346AE File Offset: 0x000328AE
		public SealEquipEffect(int count, int probability)
			: base(eEffectType.SealEquipEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x06008383 RID: 33667 RVA: 0x002B3E5C File Offset: 0x002B205C
		public override bool Start(Living living)
		{
			SealEquipEffect sealEquipEffect = living.EffectList.GetOfType(eEffectType.SealEquipEffect) as SealEquipEffect;
			bool flag = sealEquipEffect != null;
			bool flag2;
			if (flag)
			{
				sealEquipEffect.m_probability = ((this.m_probability > sealEquipEffect.m_probability) ? this.m_probability : sealEquipEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008384 RID: 33668 RVA: 0x000346D6 File Offset: 0x000328D6
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.ChangeProperty;
			player.AfterKillingLiving += this.player_AfterKillingLiving;
		}

		// Token: 0x06008385 RID: 33669 RVA: 0x002B3EB8 File Offset: 0x002B20B8
		private void player_AfterKillingLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool isTrigger = this.IsTrigger;
			if (isTrigger)
			{
				target.AddEffect(new SealEffect(2, 1), 0);
			}
		}

		// Token: 0x06008386 RID: 33670 RVA: 0x000346FF File Offset: 0x000328FF
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.ChangeProperty;
			player.AfterKillingLiving -= this.player_AfterKillingLiving;
		}

		// Token: 0x06008387 RID: 33671 RVA: 0x002B3EE4 File Offset: 0x002B20E4
		private void ChangeProperty(Player player, int ball)
		{
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				this.IsTrigger = true;
				player.AttackEffectTrigger = true;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("SealEquipEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x0400521E RID: 21022
		private int m_count = 0;

		// Token: 0x0400521F RID: 21023
		private int m_probability = 0;
	}
}
