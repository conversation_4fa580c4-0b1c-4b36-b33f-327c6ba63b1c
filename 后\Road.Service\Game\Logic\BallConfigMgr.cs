﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using Bussiness;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000C83 RID: 3203
	public class BallConfigMgr
	{
		// Token: 0x06007158 RID: 29016 RVA: 0x0025B5B8 File Offset: 0x002597B8
		public static bool Init()
		{
			return BallConfigMgr.ReLoad();
		}

		// Token: 0x06007159 RID: 29017 RVA: 0x0025B5D0 File Offset: 0x002597D0
		public static bool ReLoad()
		{
			try
			{
				Dictionary<int, BombConfigInfo> dictionary = BallConfigMgr.LoadFromDatabase();
				bool flag = dictionary.Values.Count > 0;
				if (flag)
				{
					Interlocked.Exchange<Dictionary<int, BombConfigInfo>>(ref BallConfigMgr.m_infos, dictionary);
					return true;
				}
			}
			catch (Exception ex)
			{
				BallConfigMgr.log.Error("BallConfig Mgr init error:", ex);
			}
			return false;
		}

		// Token: 0x0600715A RID: 29018 RVA: 0x0025B638 File Offset: 0x00259838
		private static Dictionary<int, BombConfigInfo> LoadFromDatabase()
		{
			Dictionary<int, BombConfigInfo> dictionary = new Dictionary<int, BombConfigInfo>();
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				BombConfigInfo[] bombConfigInfo = produceBussiness.GetBombConfigInfo();
				BombConfigInfo[] array = bombConfigInfo;
				BombConfigInfo[] array2 = array;
				BombConfigInfo[] array3 = array2;
				foreach (BombConfigInfo bombConfigInfo2 in array3)
				{
					bool flag = !dictionary.ContainsKey(bombConfigInfo2.TemplateID);
					if (flag)
					{
						dictionary.Add(bombConfigInfo2.TemplateID, bombConfigInfo2);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x0600715B RID: 29019 RVA: 0x0025B6D4 File Offset: 0x002598D4
		public static BombConfigInfo FindBall(int id)
		{
			bool flag = BallConfigMgr.m_infos.ContainsKey(id);
			BombConfigInfo bombConfigInfo;
			if (flag)
			{
				bombConfigInfo = BallConfigMgr.m_infos[id];
			}
			else
			{
				bombConfigInfo = null;
			}
			return bombConfigInfo;
		}

		// Token: 0x04003DAB RID: 15787
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04003DAC RID: 15788
		private static Dictionary<int, BombConfigInfo> m_infos;
	}
}
