﻿using System;
using Bussiness;
using Game.Logic.Actions;
using Game.Logic.Phy.Object;

namespace Game.Logic.Effects
{
	// Token: 0x02000ECE RID: 3790
	public class AddAgilityEffect : BasePlayerEffect
	{
		// Token: 0x06008285 RID: 33413 RVA: 0x00033584 File Offset: 0x00031784
		public AddAgilityEffect(int count, int probability)
			: base(eEffectType.AddAgilityEffect)
		{
			this.m_count = count;
			this.m_probability = probability;
		}

		// Token: 0x06008286 RID: 33414 RVA: 0x002B0F70 File Offset: 0x002AF170
		public override bool Start(Living living)
		{
			AddAgilityEffect addAgilityEffect = living.EffectList.GetOfType(eEffectType.AddAgilityEffect) as AddAgilityEffect;
			bool flag = addAgilityEffect != null;
			bool flag2;
			if (flag)
			{
				this.m_probability = ((this.m_probability > addAgilityEffect.m_probability) ? this.m_probability : addAgilityEffect.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06008287 RID: 33415 RVA: 0x000335B2 File Offset: 0x000317B2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeforePlayerShoot += this.ChangeProperty;
			player.AfterPlayerShooted += this.player_AfterPlayerShooted;
		}

		// Token: 0x06008288 RID: 33416 RVA: 0x000335DB File Offset: 0x000317DB
		private void player_AfterPlayerShooted(Player player)
		{
			player.FlyingPartical = 0;
		}

		// Token: 0x06008289 RID: 33417 RVA: 0x000335E5 File Offset: 0x000317E5
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.BeforePlayerShoot -= this.ChangeProperty;
			player.AfterPlayerShooted -= this.player_AfterPlayerShooted;
		}

		// Token: 0x0600828A RID: 33418 RVA: 0x002B0FCC File Offset: 0x002AF1CC
		private void ChangeProperty(Player player, int ball)
		{
			player.Agility -= (double)this.m_added;
			this.m_added = 0;
			this.IsTrigger = false;
			bool flag = AbstractEffect.random.Next(1000000) < this.m_probability * 10000;
			if (flag)
			{
				player.FlyingPartical = 65;
				player.AttackEffectTrigger = true;
				this.IsTrigger = true;
				player.Agility += (double)this.m_count;
				this.m_added = this.m_count;
				player.Game.AddAction(new LivingSayAction(player, LanguageMgr.GetTranslation("AddAgilityEffect.msg", Array.Empty<object>()), 9, 0, 1000));
			}
		}

		// Token: 0x040051BF RID: 20927
		private int m_count = 0;

		// Token: 0x040051C0 RID: 20928
		private int m_probability = 0;

		// Token: 0x040051C1 RID: 20929
		private int m_added = 0;
	}
}
