﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Passives
{
	// Token: 0x02000D89 RID: 3465
	public class PE1431 : BasePetEffect
	{
		// Token: 0x06007B42 RID: 31554 RVA: 0x002925B0 File Offset: 0x002907B0
		public PE1431(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.PE1431, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007B43 RID: 31555 RVA: 0x00292630 File Offset: 0x00290830
		public override bool Start(Living living)
		{
			PE1431 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1431) as PE1431;
			bool flag = pe != null;
			bool flag2;
			if (flag)
			{
				pe.m_probability = ((this.m_probability > pe.m_probability) ? this.m_probability : pe.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007B44 RID: 31556 RVA: 0x0002EE7C File Offset: 0x0002D07C
		protected override void OnAttachedToPlayer(Player player)
		{
			player.TakePlayerDamage += this.player_BeforeTakeDamage;
		}

		// Token: 0x06007B45 RID: 31557 RVA: 0x0002EE92 File Offset: 0x0002D092
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.TakePlayerDamage -= this.player_BeforeTakeDamage;
		}

		// Token: 0x06007B46 RID: 31558 RVA: 0x00292690 File Offset: 0x00290890
		private void player_BeforeTakeDamage(Living living, Living source, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.rand.Next(100) < 30;
			if (flag)
			{
				criticalAmount = 0;
			}
		}

		// Token: 0x040048FF RID: 18687
		private int m_type = 0;

		// Token: 0x04004900 RID: 18688
		private int m_count = 0;

		// Token: 0x04004901 RID: 18689
		private int m_probability = 0;

		// Token: 0x04004902 RID: 18690
		private int m_delay = 0;

		// Token: 0x04004903 RID: 18691
		private int m_coldDown = 0;

		// Token: 0x04004904 RID: 18692
		private int m_currentId;

		// Token: 0x04004905 RID: 18693
		private int m_added = 0;
	}
}
