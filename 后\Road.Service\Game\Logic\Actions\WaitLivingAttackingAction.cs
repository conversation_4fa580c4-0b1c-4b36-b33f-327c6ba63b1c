﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.Actions
{
	// Token: 0x02000F6A RID: 3946
	public class WaitLivingAttackingAction : BaseAction
	{
		// Token: 0x06008531 RID: 34097 RVA: 0x000356F8 File Offset: 0x000338F8
		public WaitLivingAttackingAction(TurnedLiving living, int turnIndex, int delay)
			: base(delay)
		{
			this.m_living = living;
			this.m_turnIndex = turnIndex;
			living.EndAttacking += this.player_EndAttacking;
		}

		// Token: 0x06008532 RID: 34098 RVA: 0x00035724 File Offset: 0x00033924
		private void player_EndAttacking(Living player)
		{
			player.EndAttacking -= this.player_EndAttacking;
			base.Finish(TickHelper.GetTickCount());
		}

		// Token: 0x06008533 RID: 34099 RVA: 0x002BA668 File Offset: 0x002B8868
		protected override void ExecuteImp(BaseGame game, long tick)
		{
			base.Finish(tick);
			bool flag = game.TurnIndex == this.m_turnIndex && this.m_living.IsAttacking;
			if (flag)
			{
				this.m_living.StopAttacking();
				game.CheckState(0);
			}
		}

		// Token: 0x0400533D RID: 21309
		private TurnedLiving m_living;

		// Token: 0x0400533E RID: 21310
		private int m_turnIndex;
	}
}
