<?xml version="1.0" encoding="utf-8"?>
<project version="2">
  <!-- Generated with JPEXS Free Flash Decompiler v.24.0.1 -->
  <!-- Output SWF options -->
  <output>
    <movie outputType="Application" />
    <movie input="" />
    <movie path="2_v3_6_1_u9 .swf" />
    <movie fps="24" />
    <movie width="500" />
    <movie height="375" />
    <movie version="27" />
    <movie minorVersion="0" />
    <movie platform="Flash Player" />
    <movie background="#ffffff" />
  </output>
  <!-- Other classes to be compiled into your SWF -->
  <classpaths>
    <class path="src" />
  </classpaths>
  <!-- Build options -->
  <build>
    <option accessible="False" />
    <option allowSourcePathOverlap="False" />
    <option benchmark="False" />
    <option es="False" />
    <option loadConfig="" />
    <option optimize="True" />
    <option showActionScriptWarnings="True" />
    <option showBindingWarnings="True" />
    <option showDeprecationWarnings="True" />
    <option showUnusedTypeSelectorWarnings="True" />
    <option strict="True" />
    <option useNetwork="True" />
    <option useResourceBundleMetadata="True" />
    <option warnings="True" />
    <option verboseStackTraces="False" />
    <option additional="-swf-version=38" />
    <option customSDK="" />
  </build>
  <!-- Class files to compile (other referenced classes will automatically be included) -->
  <compileTargets>
<compile path="src/_c825cacf866db1dea92bc8874deb52ce6a5d32346b03dea7aefddc4284f6fdc1_flash_display_Sprite.as" />
  </compileTargets>
  <!-- Paths to exclude from the Project Explorer tree -->
  <hiddenPaths>
    <!-- example: <hidden path="..." /> -->
  </hiddenPaths>
  <!-- Executed before build -->
  <preBuildCommand />
  <!-- Executed after build -->
  <postBuildCommand alwaysRun="False" />
  <!-- Other project options -->
  <options>
    <option showHiddenPaths="False" />
    <option testMovie="Default" />
  </options>
</project>