﻿using System;
using System.Runtime.InteropServices;
using System.Text;

namespace Bussiness
{
	// Token: 0x02000FAE RID: 4014
	public class IniReader
	{
		// Token: 0x06008898 RID: 34968
		[DllImport("kernel32")]
		private static extern int GetPrivateProfileString(string section, string key, string def, StringBuilder retVal, int size, string filePath);

		// Token: 0x06008899 RID: 34969 RVA: 0x0003630A File Offset: 0x0003450A
		public IniReader(string _FilePath)
		{
			this.FilePath = _FilePath;
		}

		// Token: 0x0600889A RID: 34970 RVA: 0x002D1F78 File Offset: 0x002D0178
		public string GetIniString(string Section, string Key)
		{
			StringBuilder stringBuilder = new StringBuilder(2550);
			IniReader.GetPrivateProfileString(Section, Key, "", stringBuilder, 2550, this.FilePath);
			return stringBuilder.ToString();
		}

		// Token: 0x04005496 RID: 21654
		private string FilePath;
	}
}
