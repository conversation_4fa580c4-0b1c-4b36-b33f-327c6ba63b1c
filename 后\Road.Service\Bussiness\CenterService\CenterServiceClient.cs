﻿using System;
using System.CodeDom.Compiler;
using System.Diagnostics;
using System.ServiceModel;
using System.ServiceModel.Channels;

namespace Bussiness.CenterService
{
	// Token: 0x02001004 RID: 4100
	[DebuggerStepThrough]
	[GeneratedCode("System.ServiceModel", "4.0.0.0")]
	public class CenterServiceClient : ClientBase<ICenterService>, ICenterService
	{
		// Token: 0x06008BE7 RID: 35815 RVA: 0x00036C61 File Offset: 0x00034E61
		public CenterServiceClient()
		{
		}

		// Token: 0x06008BE8 RID: 35816 RVA: 0x00036C6B File Offset: 0x00034E6B
		public CenterServiceClient(string endpointConfigurationName)
			: base(endpointConfigurationName)
		{
		}

		// Token: 0x06008BE9 RID: 35817 RVA: 0x00036C76 File Offset: 0x00034E76
		public CenterServiceClient(string endpointConfigurationName, string remoteAddress)
			: base(endpointConfigurationName, remoteAddress)
		{
		}

		// Token: 0x06008BEA RID: 35818 RVA: 0x00036C82 File Offset: 0x00034E82
		public CenterServiceClient(string endpointConfigurationName, EndpointAddress remoteAddress)
			: base(endpointConfigurationName, remoteAddress)
		{
		}

		// Token: 0x06008BEB RID: 35819 RVA: 0x00036C8E File Offset: 0x00034E8E
		public CenterServiceClient(Binding binding, EndpointAddress remoteAddress)
			: base(binding, remoteAddress)
		{
		}

		// Token: 0x06008BEC RID: 35820 RVA: 0x002FED48 File Offset: 0x002FCF48
		public ServerData[] GetServerList(int areaid)
		{
			return base.Channel.GetServerList(areaid);
		}

		// Token: 0x06008BED RID: 35821 RVA: 0x002FED68 File Offset: 0x002FCF68
		public bool ChargeMoney(int userID, int areaid, string chargeID)
		{
			return base.Channel.ChargeMoney(userID, areaid, chargeID);
		}

		// Token: 0x06008BEE RID: 35822 RVA: 0x002FED88 File Offset: 0x002FCF88
		public bool SystemNotice(string msg)
		{
			return base.Channel.SystemNotice(msg);
		}

		// Token: 0x06008BEF RID: 35823 RVA: 0x002FEDA8 File Offset: 0x002FCFA8
		public bool KitoffUser(int playerID, int areaid, string msg)
		{
			return base.Channel.KitoffUser(playerID, areaid, msg);
		}

		// Token: 0x06008BF0 RID: 35824 RVA: 0x002FEDC8 File Offset: 0x002FCFC8
		public bool ReLoadServerList()
		{
			return base.Channel.ReLoadServerList();
		}

		// Token: 0x06008BF1 RID: 35825 RVA: 0x002FEDE8 File Offset: 0x002FCFE8
		public bool MailNotice(int playerID, int areaid)
		{
			return base.Channel.MailNotice(playerID, areaid);
		}

		// Token: 0x06008BF2 RID: 35826 RVA: 0x002FEE08 File Offset: 0x002FD008
		public bool ActivePlayer(bool isActive)
		{
			return base.Channel.ActivePlayer(isActive);
		}

		// Token: 0x06008BF3 RID: 35827 RVA: 0x002FEE28 File Offset: 0x002FD028
		public bool CreatePlayer(int id, string name, string password, int areaid, bool isFirst)
		{
			return base.Channel.CreatePlayer(id, name, password, areaid, isFirst);
		}

		// Token: 0x06008BF4 RID: 35828 RVA: 0x002FEE4C File Offset: 0x002FD04C
		public bool ValidateLoginAndGetID(string name, string password, int areaid, ref int userID, ref bool isFirst)
		{
			return base.Channel.ValidateLoginAndGetID(name, password, areaid, ref userID, ref isFirst);
		}

		// Token: 0x06008BF5 RID: 35829 RVA: 0x002FEE70 File Offset: 0x002FD070
		public bool AASUpdateState(bool state)
		{
			return base.Channel.AASUpdateState(state);
		}

		// Token: 0x06008BF6 RID: 35830 RVA: 0x002FEE90 File Offset: 0x002FD090
		public int AASGetState()
		{
			return base.Channel.AASGetState();
		}

		// Token: 0x06008BF7 RID: 35831 RVA: 0x002FEEB0 File Offset: 0x002FD0B0
		public int ExperienceRateUpdate(int serverId)
		{
			return base.Channel.ExperienceRateUpdate(serverId);
		}

		// Token: 0x06008BF8 RID: 35832 RVA: 0x002FEED0 File Offset: 0x002FD0D0
		public int NoticeServerUpdate(int serverId, int type)
		{
			return base.Channel.NoticeServerUpdate(serverId, type);
		}

		// Token: 0x06008BF9 RID: 35833 RVA: 0x002FEEF0 File Offset: 0x002FD0F0
		public bool UpdateConfigState(int type, bool state)
		{
			return base.Channel.UpdateConfigState(type, state);
		}

		// Token: 0x06008BFA RID: 35834 RVA: 0x002FEF10 File Offset: 0x002FD110
		public int GetConfigState(int type)
		{
			return base.Channel.GetConfigState(type);
		}

		// Token: 0x06008BFB RID: 35835 RVA: 0x002FEF30 File Offset: 0x002FD130
		public bool Reload(string type)
		{
			return base.Channel.Reload(type);
		}

		// Token: 0x06008BFC RID: 35836 RVA: 0x002FEF50 File Offset: 0x002FD150
		public bool CheckUserValidate(int playerID, string keyString)
		{
			return base.Channel.CheckUserValidate(playerID, keyString);
		}
	}
}
