﻿using System;
using Game.Logic.PetEffects.Element.Passives;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DB6 RID: 3510
	public class AE1071 : BasePetEffect
	{
		// Token: 0x06007C2F RID: 31791 RVA: 0x00296408 File Offset: 0x00294608
		public AE1071(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1071, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007C30 RID: 31792 RVA: 0x00296484 File Offset: 0x00294684
		public override bool Start(Living living)
		{
			AE1071 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1071) as AE1071;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007C31 RID: 31793 RVA: 0x0002F8D2 File Offset: 0x0002DAD2
		protected override void OnAttachedToPlayer(Player player)
		{
			player.AfterKilledByLiving += this.Player_AfterKilledByLiving;
		}

		// Token: 0x06007C32 RID: 31794 RVA: 0x0002F8E8 File Offset: 0x0002DAE8
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.AfterKilledByLiving -= this.Player_AfterKilledByLiving;
		}

		// Token: 0x06007C33 RID: 31795 RVA: 0x002964E0 File Offset: 0x002946E0
		private void Player_AfterKilledByLiving(Living living, Living target, int damageAmount, int criticalAmount)
		{
			bool flag = damageAmount > 0 || criticalAmount > 0;
			if (flag)
			{
				PE1072 pe = living.PetEffectList.GetOfType(ePetEffectType.PE1072) as PE1072;
				bool flag2 = pe != null;
				if (flag2)
				{
					pe.Stop();
				}
				PE1073 pe2 = living.PetEffectList.GetOfType(ePetEffectType.PE1073) as PE1073;
				bool flag3 = pe2 != null;
				if (flag3)
				{
					pe2.Stop();
				}
				living.Game.SendPetBuff(living, base.ElementInfo, false);
			}
		}

		// Token: 0x04004A34 RID: 18996
		private int m_type = 0;

		// Token: 0x04004A35 RID: 18997
		private int m_count = 0;

		// Token: 0x04004A36 RID: 18998
		private int m_probability = 0;

		// Token: 0x04004A37 RID: 18999
		private int m_delay = 0;

		// Token: 0x04004A38 RID: 19000
		private int m_coldDown = 0;

		// Token: 0x04004A39 RID: 19001
		private int m_currentId;

		// Token: 0x04004A3A RID: 19002
		private int m_added = 0;
	}
}
