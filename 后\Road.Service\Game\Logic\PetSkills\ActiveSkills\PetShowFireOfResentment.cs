﻿using System;
using Game.Logic.Event;
using Game.Logic.PetEffects;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetSkills.ActiveSkills
{
	// Token: 0x02000D4A RID: 3402
	public class PetShowFireOfResentment : BasePetEffect
	{
		// Token: 0x060079EA RID: 31210 RVA: 0x0002E158 File Offset: 0x0002C358
		public PetShowFireOfResentment(int count, int skillId, string elementID)
			: base(ePetEffectType.PetShowFireofResentment, elementID)
		{
			this.m_count = count;
			this.m_skillId = skillId;
		}

		// Token: 0x060079EB RID: 31211 RVA: 0x0028CEB8 File Offset: 0x0028B0B8
		private void player_PlayerBuffSkillPet(Living living)
		{
			Console.WriteLine("{0},1我进来执行了", this.m_count);
			bool flag = living.PetEffects.CurrentUseSkill == this.m_skillId;
			if (flag)
			{
				for (int i = 0; i < this.m_count; i++)
				{
					living.Game.sendShowPicSkil(living, base.ElementInfo, true);
				}
				living.AddPetEffect(new PetShowFireofResentmentEquip(this.m_count, base.ElementInfo.ID.ToString()), 0);
			}
		}

		// Token: 0x060079EC RID: 31212 RVA: 0x0002E176 File Offset: 0x0002C376
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079ED RID: 31213 RVA: 0x0002E18C File Offset: 0x0002C38C
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= new PlayerEventHandle(this.player_PlayerBuffSkillPet);
		}

		// Token: 0x060079EE RID: 31214 RVA: 0x0028CF48 File Offset: 0x0028B148
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.PetShowFireofResentment) is PetShowFireOfResentment;
			return flag || base.Start(living);
		}

		// Token: 0x0400476D RID: 18285
		private int m_skillId;

		// Token: 0x0400476E RID: 18286
		private int m_count;
	}
}
