﻿using System;
using Game.Logic.PetEffects;
using Game.Logic.Phy.Object;

namespace Game.Logic.GhostEffect
{
	// Token: 0x02000EC8 RID: 3784
	public class AddDanderDamage : BasePetEffect
	{
		// Token: 0x06008264 RID: 33380 RVA: 0x002B0A40 File Offset: 0x002AEC40
		public AddDanderDamage(string element)
			: base(ePetEffectType.EquipGhostSkill_AddDanderDamage, element)
		{
			bool flag = element == null;
			if (!flag)
			{
				int length = element.Length;
				bool flag2 = length != 4;
				if (!flag2)
				{
					switch (element[3])
					{
					case '0':
					{
						bool flag3 = element == "2110";
						if (flag3)
						{
							this.m_value = 50;
						}
						break;
					}
					case '1':
					{
						bool flag4 = element == "2101";
						if (flag4)
						{
							this.m_value = 5;
						}
						break;
					}
					case '2':
					{
						bool flag5 = element == "2102";
						if (flag5)
						{
							this.m_value = 10;
						}
						break;
					}
					case '3':
					{
						bool flag6 = element == "2103";
						if (flag6)
						{
							this.m_value = 15;
						}
						break;
					}
					case '4':
					{
						bool flag7 = element == "2104";
						if (flag7)
						{
							this.m_value = 20;
						}
						break;
					}
					case '5':
					{
						bool flag8 = element == "2105";
						if (flag8)
						{
							this.m_value = 25;
						}
						break;
					}
					case '6':
					{
						bool flag9 = element == "2106";
						if (flag9)
						{
							this.m_value = 30;
						}
						break;
					}
					case '7':
					{
						bool flag10 = element == "2107";
						if (flag10)
						{
							this.m_value = 35;
						}
						break;
					}
					case '8':
					{
						bool flag11 = element == "2108";
						if (flag11)
						{
							this.m_value = 40;
						}
						break;
					}
					case '9':
					{
						bool flag12 = element == "2109";
						if (flag12)
						{
							this.m_value = 45;
						}
						break;
					}
					}
				}
			}
		}

		// Token: 0x06008265 RID: 33381 RVA: 0x002B0C04 File Offset: 0x002AEE04
		public override bool Start(Living living)
		{
			bool flag = living.PetEffectList.GetOfType(ePetEffectType.EquipGhostSkill_AddDanderDamage) is AddDanderDamage;
			return flag || base.Start(living);
		}

		// Token: 0x06008266 RID: 33382 RVA: 0x00033485 File Offset: 0x00031685
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerUseDander += this.player_UseDander;
			player.TakePlayerDamage += this.player_TakeDamage;
		}

		// Token: 0x06008267 RID: 33383 RVA: 0x000334AE File Offset: 0x000316AE
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerUseDander -= this.player_UseDander;
			player.TakePlayerDamage -= this.player_TakeDamage;
		}

		// Token: 0x06008268 RID: 33384 RVA: 0x002B0C40 File Offset: 0x002AEE40
		private void player_TakeDamage(Living living, Living target, ref int damageAmount, ref int criticalAmount)
		{
			bool flag = this.isDander;
			if (flag)
			{
				damageAmount += damageAmount * this.m_value / 100;
				this.isDander = false;
			}
		}

		// Token: 0x06008269 RID: 33385 RVA: 0x000334D7 File Offset: 0x000316D7
		private void player_UseDander(Player player)
		{
			this.isDander = true;
		}

		// Token: 0x040051B6 RID: 20918
		private int m_value = 0;

		// Token: 0x040051B7 RID: 20919
		private bool isDander = false;
	}
}
