﻿using System;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.ContinueElement
{
	// Token: 0x02000E49 RID: 3657
	public class CE1023 : BasePetEffect
	{
		// Token: 0x06007F41 RID: 32577 RVA: 0x002A4568 File Offset: 0x002A2768
		public CE1023(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.CE1023, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007F42 RID: 32578 RVA: 0x002A45E8 File Offset: 0x002A27E8
		public override bool Start(Living living)
		{
			CE1023 ce = living.PetEffectList.GetOfType(ePetEffectType.CE1023) as CE1023;
			bool flag = ce != null;
			bool flag2;
			if (flag)
			{
				ce.m_probability = ((this.m_probability > ce.m_probability) ? this.m_probability : ce.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007F43 RID: 32579 RVA: 0x002A4648 File Offset: 0x002A2848
		protected override void OnAttachedToPlayer(Player player)
		{
			player.BeginSelfTurn += this.Player_BeginSelfTurn;
			bool flag = this.m_added == 0;
			if (flag)
			{
				this.m_added = 200;
				player.BaseGuard += (double)this.m_added;
			}
			player.PlayerClearBuffSkillPet += this.Player_PlayerClearBuffSkillPet;
		}

		// Token: 0x06007F44 RID: 32580 RVA: 0x0002C94F File Offset: 0x0002AB4F
		private void Player_PlayerClearBuffSkillPet(Player player)
		{
			this.Stop();
		}

		// Token: 0x06007F45 RID: 32581 RVA: 0x002A46AC File Offset: 0x002A28AC
		private void Player_BeginSelfTurn(Living living)
		{
			this.m_count--;
			bool flag = this.m_count < 0;
			if (flag)
			{
				this.Stop();
			}
		}

		// Token: 0x06007F46 RID: 32582 RVA: 0x002A46E0 File Offset: 0x002A28E0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.Game.SendPetBuff(player, base.ElementInfo, false);
			player.Game.SendPlayerPicture(player, 30, false);
			player.BaseGuard -= (double)this.m_added;
			this.m_added = 0;
			player.BeginSelfTurn -= this.Player_BeginSelfTurn;
		}

		// Token: 0x04004E3A RID: 20026
		private int m_type = 0;

		// Token: 0x04004E3B RID: 20027
		private int m_count = 0;

		// Token: 0x04004E3C RID: 20028
		private int m_probability = 0;

		// Token: 0x04004E3D RID: 20029
		private int m_delay = 0;

		// Token: 0x04004E3E RID: 20030
		private int m_coldDown = 0;

		// Token: 0x04004E3F RID: 20031
		private int m_currentId;

		// Token: 0x04004E40 RID: 20032
		private int m_added = 0;
	}
}
