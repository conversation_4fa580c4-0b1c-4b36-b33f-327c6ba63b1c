﻿using System;
using System.Collections.Generic;
using Game.Logic.PetEffects.ContinueElement;
using Game.Logic.Phy.Object;

namespace Game.Logic.PetEffects.Element.Actives
{
	// Token: 0x02000DE1 RID: 3553
	public class AE1170 : BasePetEffect
	{
		// Token: 0x06007D14 RID: 32020 RVA: 0x0029A3E4 File Offset: 0x002985E4
		public AE1170(int count, int probability, int type, int skillId, int delay, string elementID)
			: base(ePetEffectType.AE1170, elementID)
		{
			this.m_count = count;
			this.m_coldDown = count;
			this.m_probability = ((probability == -1) ? 10000 : probability);
			this.m_type = type;
			this.m_delay = delay;
			this.m_currentId = skillId;
		}

		// Token: 0x06007D15 RID: 32021 RVA: 0x0029A464 File Offset: 0x00298664
		public override bool Start(Living living)
		{
			AE1170 ae = living.PetEffectList.GetOfType(ePetEffectType.AE1170) as AE1170;
			bool flag = ae != null;
			bool flag2;
			if (flag)
			{
				ae.m_probability = ((this.m_probability > ae.m_probability) ? this.m_probability : ae.m_probability);
				flag2 = true;
			}
			else
			{
				flag2 = base.Start(living);
			}
			return flag2;
		}

		// Token: 0x06007D16 RID: 32022 RVA: 0x000301DA File Offset: 0x0002E3DA
		protected override void OnAttachedToPlayer(Player player)
		{
			player.PlayerBuffSkillPet += this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x06007D17 RID: 32023 RVA: 0x0029A4C4 File Offset: 0x002986C4
		private void Player_PlayerBuffSkillPet(Player player)
		{
			bool flag = player.PetEffects.CurrentUseSkill != this.m_currentId || !(player.Game is PVPGame);
			if (!flag)
			{
				List<Player> allEnemyPlayers = player.Game.GetAllEnemyPlayers(player);
				foreach (Player player2 in allEnemyPlayers)
				{
					player2.AddPetEffect(new CE1170(3, this.m_probability, this.m_type, this.m_currentId, this.m_delay, base.ElementInfo.ID.ToString(), player), 0);
				}
			}
		}

		// Token: 0x06007D18 RID: 32024 RVA: 0x000301F0 File Offset: 0x0002E3F0
		protected override void OnRemovedFromPlayer(Player player)
		{
			player.PlayerBuffSkillPet -= this.Player_PlayerBuffSkillPet;
		}

		// Token: 0x04004B5F RID: 19295
		private int m_type = 0;

		// Token: 0x04004B60 RID: 19296
		private int m_count = 0;

		// Token: 0x04004B61 RID: 19297
		private int m_probability = 0;

		// Token: 0x04004B62 RID: 19298
		private int m_delay = 0;

		// Token: 0x04004B63 RID: 19299
		private int m_coldDown = 0;

		// Token: 0x04004B64 RID: 19300
		private int m_currentId;

		// Token: 0x04004B65 RID: 19301
		private int m_added = 0;
	}
}
